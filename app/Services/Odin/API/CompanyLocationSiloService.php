<?php

namespace App\Services\Odin\API;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Http\Requests\Odin\API\CompanyLocationSiloDataRequest;
use App\Http\Requests\Odin\API\CompanyLocationSiloEntryRequest;
use App\Models\Legacy\Location;
use App\Models\Odin\LocationSiloPage;
use App\Models\Odin\Silo;
use App\Models\Odin\Website;
use App\Repositories\Legacy\NonPurchasingCompanyLocationRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\CompanyListRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\LocationSiloPageRepository;
use App\Repositories\Odin\SolarIncentivesRepository;
use App\Repositories\Odin\SolarShortCodesRepository;
use App\Repositories\Odin\WebsiteRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CompanyLocationSiloService
{
    const DEFAULT_WEBSITE_KEY = 'sr';

    const REPOSITORY_SOLAR_SHORT_CODES = 'solar_short_codes';
    const REPOSITORY_COMPANY_LIST      = 'company_list';
    const REPOSITORY_SOLAR_INCENTIVES  = 'solar_incentives';

    const RESPONSE_CODE = 'response_code';
    const REDIRECT_PATH = 'redirect_path';

    const PARENT_LOCATION_NAME         = 'parent_location_name';
    const PARENT_LOCATION_SLUG         = 'parent_location_slug';
    const PARENT_LOCATION_ABBREVIATION = 'parent_location_abbreviation';
    const LOCATION_NAME                = 'location_name';
    const LOCATION_SLUG                = 'location_slug';
    const LOCATION_ABBREVIATION        = 'location_abbreviation';
    const LOCATION_TYPE                = 'location_type';
    const COMPANY_COUNT                = 'company_count';
    const WEBSITE_KEY                  = 'website_key';
    const INDUSTRY                     = 'industry';

    public function __construct(
        protected CompanyListRepository                  $companyListRepository,
        protected LocationRepository                     $locationRepository,
        protected NonPurchasingCompanyLocationRepository $nonPurchasingCompanyLocationRepository,
        protected CompanyRepository                      $companyRepository,
        protected LocationSiloPageRepository             $siloPageRepository,
        protected SolarIncentivesRepository              $solarIncentivesRepository,
        protected SolarShortCodesRepository              $solarShortCodesRepository,
        protected WebsiteRepository                      $websiteRepository
    ) {}

    /**
     * Returns an entry by its path & website.
     *
     * @param string $requestFullPath
     * @param string $websiteKey
     * @return array
     */
    public function getEntryData(string $requestFullPath, string $websiteKey = self::DEFAULT_WEBSITE_KEY): array
    {
        $website = $this->websiteRepository->getWebsiteByAbbreviation($websiteKey);

        if($website === null)
            return $this->getInvalidPageResponse($requestFullPath);

        $locationSiloPage = $this->siloPageRepository->getLocationSiloPageByUrlAndWebsiteId($requestFullPath, $website->{Website::FIELD_ID});

        if ($locationSiloPage) {
            return $this->getValidPageResponse($locationSiloPage);
        }

        return $this->getInvalidPageResponse($requestFullPath);
    }

    /**
     * @param int $locationSiloPageId
     * @param array $componentDataRequests
     * @param bool $requiresActiveCampaigns
     * @return array
     */
    public function getDataFromRepositories(int $locationSiloPageId, array $componentDataRequests, bool $requiresActiveCampaigns): array
    {
        $location = null;
        $companyIdsByNonPurchasingLocation = null;
        $companyIdsByActiveCampaignLocation = null;
        $usedCompanyIds = [];
        $shortCodes = [];

        /** @var LocationSiloPage|null $locationSiloPage */
        $locationSiloPage = LocationSiloPage::query()->find($locationSiloPageId);

        if (!$locationSiloPage) {
            return [
                self::RESPONSE_CODE => 404
            ];
        }

        $primaryIndustryId = $locationSiloPage->silo->industry_id;

        $locationId = match ($locationSiloPage->{LocationSiloPage::FIELD_LOCATION_TYPE}->value) {
            LocationSiloPageLocationType::STATE->value, LocationSiloPageLocationType::CITY->value => $locationSiloPage->{LocationSiloPage::FIELD_LOCATION_ID},
            default => null,
        };

        $companyIdsByIndustry = $this->companyRepository->getCompanyIdsByIndustryId($primaryIndustryId);

        if ($locationId) {
            $companyIdsByNonPurchasingLocation = array_values(array_intersect($this->companyListRepository->getCompanyIdsByNonPurchasingLocation($locationId), $companyIdsByIndustry));

            if ($requiresActiveCampaigns) {
                $companyIdsByActiveCampaignLocation = array_values(array_intersect($this->companyListRepository->getCompanyIdsByActiveCampaignLocation($locationId), $companyIdsByIndustry));
            }

            /** @var Location|null $location */
            $location = Location::query()->find($locationId);
        }

        $components = collect();

        foreach ($componentDataRequests as $dataRequest) {
            $componentData = null;

            switch ($dataRequest['repository_type']) {
                case self::REPOSITORY_COMPANY_LIST:
                    $componentData  = $this->companyListRepository->getCompanyList($dataRequest, $usedCompanyIds, $companyIdsByNonPurchasingLocation, $companyIdsByActiveCampaignLocation, $location, $primaryIndustryId);
                    $usedCompanyIds = array_merge($usedCompanyIds, Arr::pluck($componentData, 'id'));
                    break;
                case self::REPOSITORY_SOLAR_INCENTIVES:
                    $componentData = $this->solarIncentivesRepository->getDataByLocation($location);
                    break;
                case self::REPOSITORY_SOLAR_SHORT_CODES:
                    $shortCodes = $this->solarShortCodesRepository->getSolarShortCodesByLocation($location);
                    break;
            }

            $components[$dataRequest[CompanyLocationSiloDataRequest::FIELD_ID]] = $componentData;
        }

        $data = [];
        $data['components'] = $components;
        $data['child_pages'] = $this->getChildPages($locationSiloPage);
        $data['short_codes'] = array_merge($this->getPageShortCodes($locationSiloPage, $companyIdsByIndustry, $companyIdsByNonPurchasingLocation), $shortCodes);

        return array_merge(Arr::whereNotNull($data), [
            self::RESPONSE_CODE => 200
        ]);
    }

    /**
     * @param LocationSiloPage $locationSiloPage
     * @param array $companyIdsByIndustry
     * @param array|null $companyIdsByNonPurchasingLocation
     * @return array|string[]
     */
    private function getPageShortCodes(LocationSiloPage $locationSiloPage, array $companyIdsByIndustry, ?array $companyIdsByNonPurchasingLocation): array
    {
        return match ($locationSiloPage->{LocationSiloPage::FIELD_LOCATION_TYPE}->value) {
            LocationSiloPageLocationType::CITY->value => [
                self::PARENT_LOCATION_NAME         => $locationSiloPage->parentLocation->{Location::STATE},
                self::PARENT_LOCATION_ABBREVIATION => $locationSiloPage->parentLocation->{Location::STATE_ABBREVIATION},
                self::PARENT_LOCATION_SLUG         => $locationSiloPage->parentLocation->{Location::STATE_KEY},
                self::LOCATION_NAME                => $locationSiloPage->location->{Location::CITY},
                self::LOCATION_ABBREVIATION        => null,
                self::LOCATION_SLUG                => $locationSiloPage->location->{Location::CITY_KEY},
                self::LOCATION_TYPE                => $locationSiloPage->location_type,
                self::COMPANY_COUNT                => number_format(count($companyIdsByNonPurchasingLocation)),
                self::INDUSTRY                     => $locationSiloPage->silo->industry->name
            ],
            LocationSiloPageLocationType::STATE->value => [
                self::PARENT_LOCATION_NAME         => 'United States',
                self::PARENT_LOCATION_ABBREVIATION => 'US',
                self::PARENT_LOCATION_SLUG         => null,
                self::LOCATION_NAME                => $locationSiloPage->location->{Location::STATE},
                self::LOCATION_ABBREVIATION        => $locationSiloPage->location->{Location::STATE_ABBREVIATION},
                self::LOCATION_SLUG                => $locationSiloPage->location->{Location::STATE_KEY},
                self::LOCATION_TYPE                => $locationSiloPage->location_type,
                self::COMPANY_COUNT                => number_format(count($companyIdsByNonPurchasingLocation)),
                self::INDUSTRY                     => $locationSiloPage->silo->industry->name
            ],
            default => [
                self::LOCATION_NAME         => 'United States',
                self::LOCATION_ABBREVIATION => 'US',
                self::LOCATION_TYPE         => $locationSiloPage->location_type,
                self::COMPANY_COUNT         => number_format(count($companyIdsByIndustry)),
                self::INDUSTRY              => $locationSiloPage->silo->industry->name
            ],
        };
    }

    /**
     * @param LocationSiloPage $locationSiloPage
     * @return Collection
     */
    private function getChildPages(LocationSiloPage $locationSiloPage): Collection
    {
        switch ($locationSiloPage->{LocationSiloPage::FIELD_LOCATION_TYPE}->value) {
            case LocationSiloPageLocationType::CITY->value:
                return collect();
            case LocationSiloPageLocationType::STATE->value:
                $locationPages = $this->siloPageRepository->getCityPagesByStateSiloPage($locationSiloPage);

                return $locationPages->map(function ($locationPage) {
                    return [
                        'name' => $locationPage->location->{Location::CITY},
                        'path' => $locationPage->silo->{Silo::FIELD_ROOT_PATH} . $locationPage->{LocationSiloPage::FIELD_RELATIVE_PATH}
                    ];
                })->sortBy('name')->values();
            case LocationSiloPageLocationType::NATIONAL->value:
            default:
                $locationPages = $this->siloPageRepository->getStatePagesByNationalSiloPage($locationSiloPage);

                return $locationPages->map(function ($locationPage) {
                    return [
                        'name' => $locationPage->location->{Location::STATE},
                        'path' => $locationPage->silo->{Silo::FIELD_ROOT_PATH} . $locationPage->{LocationSiloPage::FIELD_RELATIVE_PATH}
                    ];
                })->sortBy('name')->values();
        }
    }

    /**
     * @param LocationSiloPage $locationSiloPage
     * @return array
     */
    private function getValidPageResponse(LocationSiloPage $locationSiloPage): array
    {
        return [
            self::RESPONSE_CODE                                          => 200,
            CompanyLocationSiloEntryRequest::FIELD_LOCATION_SILO_PAGE_ID => $locationSiloPage->{LocationSiloPage::FIELD_ID},
            CompanyLocationSiloEntryRequest::FIELD_COLLECTION_HANDLE     => $locationSiloPage->silo->{Silo::FIELD_COLLECTION_HANDLE},
            CompanyLocationSiloEntryRequest::FIELD_ENTRY_SLUG            => $locationSiloPage->{LocationSiloPage::FIELD_ENTRY_SLUG},
            CompanyLocationSiloDataRequest::FIELD_INDUSTRY_SLUG          => $locationSiloPage->silo->industry->slug,
            CompanyLocationSiloDataRequest::FIELD_FLOW_BUILDER_FLOW      => $locationSiloPage->silo->flow_id,
            CompanyLocationSiloDataRequest::FIELD_FLOW_BUILDER_REVISION  => $locationSiloPage->silo->revision_id
        ];
    }

    /**
     * @param string $requestFullPath
     * @return array|int[]
     */
    private function getInvalidPageResponse(string $requestFullPath): array
    {
        $sections = explode('/', ltrim($requestFullPath, '/'));
        $rootPath = "/" . $sections[0];

        $pages = $this->siloPageRepository->getFullPathsBySiloRootPath($rootPath);

        if (count($pages) > 0) {
            while ($sections) {
                array_pop($sections);

                $path = "/" . join("/", $sections);

                if (in_array($path, $pages)) {
                    return [
                        self::RESPONSE_CODE => 302,
                        self::REDIRECT_PATH => $path
                    ];
                }
            }
        }

        return [
            self::RESPONSE_CODE => 404
        ];
    }
}
