<?php

namespace App\Services\Odin\API;

use App\Contracts\Odin\API\OdinResourceServiceContract;
use App\Enums\Odin\API\FieldClassification;
use App\Enums\Odin\API\FieldType;
use Illuminate\Support\Collection;

class OdinAPIService
{
    /**
     * @param OdinAPIParsingService $parsingService
     * @param array $resourceServices
     */
    public function __construct(
        protected OdinAPIParsingService $parsingService,
        protected array $resourceServices = []
    ) {}

    /**
     * Returns the resource service for a given classification.
     *
     * @param FieldClassification $classification
     * @return OdinResourceServiceContract|null
     */
    protected function getResourceService(FieldClassification $classification): ?OdinResourceServiceContract
    {
        return $this->resourceServices[$classification->value] ?? null;
    }

    /**
     * Handles parsing the raw data, and then grouping by the classification.
     *
     * @param array $data
     * @return Collection
     */
    protected function parseAndGroupData(array $data): Collection
    {
        return $this->parsingService->parseMany($data)
                                    ->groupBy(fn(APIFieldModel $item) => $item->classification->value);
    }

    public function create(array $data): bool
    {
        $data = $this->parseAndGroupData($data);
        $result = true;

        foreach($data as $classification => $values)
            if($result)
                $result = $this->getResourceService(FieldClassification::from($classification))?->create($values) ?? false;

        return $result;
    }

    public function update(mixed $primaryKey, array $data): bool
    {
        $data = $this->parseAndGroupData($data);
        $result = true;

        foreach($data as $classification => $values)
            if($result)
                $result = $this->getResourceService(FieldClassification::from($classification))?->update($primaryKey, $values) ?? false;

        return $result;
    }

    /**
     * Handles deleting a resource.
     *
     * @param mixed $primaryKey
     * @param FieldClassification $classification
     * @return bool|null
     */
    public function delete(mixed $primaryKey, FieldClassification $classification): ?bool
    {
        return $this->getResourceService($classification)?->delete($primaryKey);
    }

    /**
     * Update a value by key on an array of APIFieldModel data. Mutates array.
     * @param array $fieldModelData
     * @param string $key
     * @param mixed $value
     * @return bool
     */
    public function updateFieldModelByKey(array &$fieldModelData, string $key, mixed $value): bool
    {
        $existingKey = collect($fieldModelData)->search(fn(array $fieldArray) => $fieldArray['key'] === $key);
        if ($existingKey) {
            $fieldModelData[$existingKey]['value'] = $value;
            return true;
        }
        return false;
    }

    /**
     * Add an entry to an array of APIFieldModel data. Mutates array.
     * @param array $fieldModelData
     * @param string $fieldName
     * @param string $key
     * @param mixed $value
     * @param FieldClassification $fieldClassification
     * @param FieldType $fieldType
     * @return bool
     */
    public function createFieldModelEntry(array &$fieldModelData, string $fieldName, string $key, mixed $value, FieldClassification $fieldClassification, FieldType $fieldType): bool
    {
        $existingKey = collect($fieldModelData)->search(fn(array $fieldArray) => $fieldArray['key'] === $key);
        if ($existingKey) {
            return false;
        }
        else {
            $fieldModelData[$fieldName] = [
                'key'               => $key,
                'value'             => $value,
                'type'              => $fieldType,
                'classification'    => $fieldClassification,
            ];
            return true;
        }
    }
}
