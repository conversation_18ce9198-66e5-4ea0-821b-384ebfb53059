<?php

namespace App\Services\Odin\Pricing;

use App\Repositories\FloorPricingHistoryRepository;
use Illuminate\Support\Collection;

class FloorPricingHistoryService
{
    public function __construct(
        protected FloorPricingHistoryRepository $floorPricingHistoryRepository
    )
    {

    }

    /**
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param int|null $stateLocationId
     * @return Collection
     */
    public function getFloorPriceHistoryLogs(
        int $serviceProductId,
        int $qualityTierId,
        int $propertyTypeId,
        ?int $stateLocationId = null,
    ): Collection
    {
        return $this->floorPricingHistoryRepository->getFloorPriceHistoryLogs(
            serviceProductId: $serviceProductId,
            qualityTierId   : $qualityTierId,
            propertyTypeId  : $propertyTypeId,
            stateLocationId : $stateLocationId,
        );
    }
}
