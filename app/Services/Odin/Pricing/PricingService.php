<?php

namespace App\Services\Odin\Pricing;

use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\PropertyType;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductProcessing\FloorPricingRepository;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Enums\Odin\Region;
use App\Repositories\Odin\ServiceProductRepository;
use Carbon\Carbon;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Enums\Odin\Product as ProductEnum;

class PricingService
{

    protected FloorPricingRepository $pricingRepository;
    protected LocationRepository $locationRepository;

    protected ServiceProductRepository $serviceProductRepository;

    public function __construct(ServiceProductRepository $serviceProductRepository)
    {
        $this->serviceProductRepository = $serviceProductRepository;
    }

    /**
     * @param string $regionType
     * @param Location $location
     * @param array $salesTypes
     * @return bool
     */
    public function updatePricing(
        string $productName,
        string $regionType,
        int $industryId,
        int $industryServiceId,
        string $qualityTierName,
        string $propertyTypeName,
        Location $location,
        array $salesTypes): bool
    {
        try {
            $pricingModel = match($regionType) {
                Region::STATE->value => new ProductStateFloorPrice,
                Region::COUNTY->value => new ProductCountyFloorPrice,
            };
            $locationIdField = match($regionType) {
                Region::STATE->value => ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
                Region::COUNTY->value => ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID,
            };

            $leadSalesLegTypes = SaleType::all();
            $qualityTiers  = QualityTier::all();
            $propertyTypes = PropertyType::all();

            foreach($salesTypes as $key => $value)
            {
                if ($value['explicit_price'] === $value['price']) {
                    continue;
                }
                $serviceProduct = $this->serviceProductRepository->getServiceProductByIndustry($industryServiceId, $productName);
                /** @var ProductStateFloorPrice|ProductCountyFloorPrice $updatablePrice */
                $updatablePrice = $pricingModel::query()
                    ->where($locationIdField, $location->{Location::ID})
                    ->where($pricingModel::FIELD_SERVICE_PRODUCT_ID, '=', $serviceProduct->id)
                    ->where($pricingModel::FIELD_SALE_TYPE_ID, $leadSalesLegTypes->where('key', '=', $key)->first()->id)
                    ->where($pricingModel::FIELD_QUALITY_TIER_ID, $qualityTiers->where('name', '=', $qualityTierName)->first()->id)
                    ->where($pricingModel::FIELD_PROPERTY_TYPE_ID, $propertyTypes->where('name', '=', $propertyTypeName)->first()->id)
                    ->firstOrFail();
                $updatablePrice->{$pricingModel::FIELD_PRICE} = $value['price'];
                $updatablePrice->save();

                // Cascade pricing for state level updates
                if ($regionType === Region::STATE->value) {
                    $this->cascadeStatePricing($updatablePrice);
                }
            }
            return true;
        }
            catch(\Exception $exception)
        {
            return false;
        }
    }

    /**
     * Initialise an IndustryService with default prices for all Locations
     * @param int $serviceProductId
     * @return bool
     */
    public function initialisePricingForService(int $serviceProductId): bool
    {
        $defaultPricing = [
            QualityTierEnum::PREMIUM->value    => 150,
            QualityTierEnum::STANDARD->value   => 100,
            QualityTierEnum::IN_HOME->value    => 150,
            QualityTierEnum::ONLINE->value     => 100,
        ];

        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()->findOrFail($serviceProductId);

        $stateLocations = Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->select(Location::STATE_KEY, Location::ID)
            ->get()
            ->keyBy(Location::STATE_KEY);

        $stateLocationIds = $stateLocations->pluck(Location::ID);
        $stateLocationMap = $stateLocations->toArray();

        $countyLocations = Location::query()
            ->select([Location::ID, Location::STATE_KEY])
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->get();

        $saleTypeIds = SaleType::query()->pluck(SaleType::FIELD_ID)->toArray();

        $propertyTypeIds = PropertyType::query()
            ->whereIn(PropertyType::FIELD_NAME, [PropertyTypeEnum::RESIDENTIAL, PropertyTypeEnum::COMMERCIAL])
            ->pluck(PropertyType::FIELD_ID)
            ->toArray();

        $qualityTierNames = $serviceProduct->product->name === 'Lead'
            ? [ QualityTierEnum::STANDARD->value, QualityTierEnum::PREMIUM->value]
            : [ QualityTierEnum::ONLINE->value, QualityTierEnum::IN_HOME->value];

        $qualityTiers = QualityTier::query()
            ->whereIn(QualityTier::FIELD_NAME, $qualityTierNames)
            ->get()
            ->keyBy(QualityTier::FIELD_NAME);

        $this->deleteAllPricesForServiceId($serviceProductId);

        $insertStateRows = [];
        foreach($stateLocationIds as $stateLocationId) {
            foreach($qualityTiers as $qualityTierName => $qualityTier) {
                $price = $defaultPricing[$qualityTierName];
                $qualityTierId = $qualityTier->id;
                foreach($propertyTypeIds as $propertyTypeId) {
                    foreach($saleTypeIds as $saleTypeId) {
                        $insertStateRows[] = [
                            ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $stateLocationId,
                            ProductStateFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                            ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
                            ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                            ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                            ProductStateFloorPrice::FIELD_PRICE              => $price
                        ];

                        if(count($insertStateRows) >= 500) {
                            DB::table(ProductStateFloorPrice::TABLE)->insert($insertStateRows);

                            $insertStateRows = [];
                        }
                    }
                }
            }
        }

        if(!empty($insertStateRows)) {
            DB::table(ProductStateFloorPrice::TABLE)->insert($insertStateRows);
        }

        $insertCountyRows = [];
        foreach($countyLocations as $countyLocation) {
            $countyLocationId = $countyLocation->{Location::ID};
            $countyStateLocationId = $stateLocationMap[$countyLocation->{Location::STATE_KEY}]['id'];
            foreach($qualityTiers as $qualityTierName => $qualityTier) {
                $price = $defaultPricing[$qualityTierName];
                $qualityTierId = $qualityTier->id;
                foreach($propertyTypeIds as $propertyTypeId) {
                    foreach($saleTypeIds as $saleTypeId) {
                        $insertCountyRows[] = [
                            ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID => $countyLocationId,
                            ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID  => $countyStateLocationId,
                            ProductCountyFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                            ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
                            ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                            ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                            ProductCountyFloorPrice::FIELD_PRICE              => $price
                        ];

                        if(count($insertCountyRows) >= 500) {
                            DB::table(ProductCountyFloorPrice::TABLE)->insert($insertCountyRows);

                            $insertCountyRows = [];
                        }
                    }
                }
            }
        }

        if(!empty($insertCountyRows)) {
            DB::table(ProductCountyFloorPrice::TABLE)->insert($insertCountyRows);
        }

        return true;
    }

    /**
     * @param int $fromServiceProductId
     * @param int $toServiceProductId
     * @return bool
     */
    public function importAllPricesFromIndustryService(int $fromServiceProductId, int $toServiceProductId): bool
    {
        $this->deleteAllPricesForServiceId($toServiceProductId);

        ProductStateFloorPrice::query()
            ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $fromServiceProductId)
            ->chunk(2000, function ($prices) use ($toServiceProductId) {
                $prices = $prices->map(function($price) use ($toServiceProductId) {
                    $price->service_product_id = $toServiceProductId;
                    $price->created_at = Carbon::now();
                    $price->updated_at = Carbon::now();
                    unset($price->id, $price->created_at, $price->updated_at);
                    return $price;
                })->toArray();

                ProductStateFloorPrice::query()
                    ->insert($prices);
            });

        ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $fromServiceProductId)
            ->chunk(2000, function ($prices) use ($toServiceProductId) {
                $prices = $prices->map(function($price) use ($toServiceProductId) {
                    $price->service_product_id = $toServiceProductId;
                    $price->created_at = Carbon::now();
                    $price->updated_at = Carbon::now();
                    unset($price->id, $price->created_at, $price->updated_at);
                    return $price;
                })->toArray();

                ProductCountyFloorPrice::query()
                    ->insert($prices);
            });

        return true;
    }

    /**
     * @param int $serviceProductId
     * @return void
     */
    public function deleteAllPricesForServiceId(int $serviceProductId): void
    {
        ProductStateFloorPrice::query()
            ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->delete();

        ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->delete();
    }

    /**
     * @param int $productId
     * @return array
     */
    public function getExportableServices(int $productId): array
    {
        $validServiceProducts = ProductStateFloorPrice::query()
            ->pluck(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID)
            ->unique();

        $idsFilteredByProduct = ServiceProduct::query()
            ->whereIn(ServiceProduct::FIELD_ID, $validServiceProducts)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $productId)
            ->pluck(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID);

        $services = IndustryService::query()
            ->with(IndustryService::RELATION_INDUSTRY)
            ->whereIn(IndustryService::FIELD_ID, $idsFilteredByProduct)
            ->get();

        $industries = $services->reduce(function ($output, IndustryService $industryService) {
            if (!($output->search(fn(Industry $industry) => $industry->id === $industryService->industry_id) > -1)) {
                $output->push($industryService->industry);
            }
            return $output;
        }, collect());

        return [ $industries, $services ];
    }

    /**
     * @param ProductStateFloorPrice $updatablePrice
     * @return void
     */
    public function cascadeStatePricing(ProductStateFloorPrice $updatablePrice): void
    {
       $count = ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID, '=', $updatablePrice->{ProductStateFloorPrice::FIELD_STATE_LOCATION_ID})
            ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, '=', $updatablePrice->{ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID})
            ->where(ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, '=', $updatablePrice->{ProductStateFloorPrice::FIELD_SALE_TYPE_ID})
            ->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID,  '=', $updatablePrice->{ProductStateFloorPrice::FIELD_QUALITY_TIER_ID})
            ->where(ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', $updatablePrice->{ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID})
            ->where(ProductCountyFloorPrice::FIELD_PRICE, '<', $updatablePrice->{ProductStateFloorPrice::FIELD_PRICE})->update([
               ProductCountyFloorPrice::FIELD_PRICE => $updatablePrice->{ProductStateFloorPrice::FIELD_PRICE}
            ]);
    }

    /**
     * @param ServiceProduct $serviceProduct
     * @param LeadCampaign $campaign
     * @return array
     */
    public function getPriceRangeForCampaign(ServiceProduct $serviceProduct, LeadCampaign $campaign): array
    {
        $countyIds = $campaign->leadCampaignCountyLocations()
            ->pluck(LeadCampaignLocation::LOCATION_ID)
            ->unique();

        $stateIds = $campaign->leadCampaignStateLocations()
            ->pluck(LeadCampaignLocation::LOCATION_ID)
            ->unique();

        return $this->getPriceRangeForLocations($serviceProduct, $countyIds, $stateIds);
    }

    /**
     * @param ServiceProduct $serviceProduct
     * @param Collection $countyIds
     * @param Collection $stateIds
     * @return array
     */
    public function getPriceRangeForLocations(ServiceProduct $serviceProduct, Collection $countyIds, Collection $stateIds): array
    {
        $output = [];

        //todo: confirm appointment price (for solar and roofing we are using formula to calculate price)
        if ($serviceProduct->product->name === ProductEnum::APPOINTMENT->value) {
            foreach ([QualityTierEnum::IN_HOME->value, QualityTierEnum::ONLINE->value] as $qualityTier) {
                /** @var QualityTier $tier */
                $tier = QualityTier::query()->where(QualityTier::FIELD_NAME, $qualityTier)->firstOrFail();
                $output[$tier->name] = $this->getPriceForSaleTypes($serviceProduct, $countyIds, $stateIds, $tier);
            }
        }
        else
            $output = $this->getPriceForSaleTypes($serviceProduct, $countyIds, $stateIds);

        return $output;

    }

    /**
     * @param ServiceProduct $serviceProduct
     * @param Collection $countyIds
     * @param Collection $stateIds
     * @param QualityTier|null $qualityTier
     *
     * @return array
     */
    protected function getPriceForSaleTypes(ServiceProduct $serviceProduct, Collection $countyIds, Collection $stateIds, ?QualityTier $qualityTier = null): array
    {
        $prices = [];

        $leadSalesTypes = SaleType::all();

        /** @var SaleType $salesType */
        foreach($leadSalesTypes as $salesType) {
            $statePriceQuery = DB::table(ProductStateFloorPrice::TABLE)
                ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProduct->id)
                ->where(ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $salesType->id)
                ->whereIn(ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateIds);

            if ($qualityTier) {
                $statePriceQuery->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTier->id);
            }

            $minPrice = $statePriceQuery->min(ProductStateFloorPrice::FIELD_PRICE);

            $countyPriceQuery = DB::table(ProductCountyFloorPrice::TABLE)
                ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProduct->id)
                ->where(ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, $salesType->id)
                ->whereIn(ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyIds);

            if ($qualityTier) {
                $statePriceQuery->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTier->id);
            }

            $maxPrice = $countyPriceQuery->max(ProductCountyFloorPrice::FIELD_PRICE);

            $prices[$salesType->key] = [
                'min'   => $minPrice,
                'max'   => $maxPrice,
            ];
        }

        return $prices;
    }

    public function setPriceRangeNationally(
        int $serviceProductId,
        int $propertyTypeId,
        int $qualityTierId,
        array $pricingBySaleType,
    ): int
    {
        $rowsUpdated = 0;

        foreach($pricingBySaleType as $saleType => $pricing) {
            $saleTypeId = SaleType::query()
                ->where(SaleType::FIELD_NAME, $saleType)
                ->first()
                ?->id;
            $newPrice = $pricing['price'];

            if (!$saleTypeId || !$newPrice) continue;

            $rowsUpdated += ProductStateFloorPrice::query()
                ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
                ->where(ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
                ->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
                ->where(ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
                ->update([ProductStateFloorPrice::FIELD_PRICE => $newPrice]);

            $rowsUpdated += ProductCountyFloorPrice::query()
                ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
                ->where(ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
                ->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
                ->where(ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
                ->update([ProductCountyFloorPrice::FIELD_PRICE => $newPrice]);
        }

        return $rowsUpdated;
    }

}
