<?php

namespace App\Services\Odin\Pricing;

use App\Enums\LoweredFloorPricePolicy;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class LoweredFloorPricePolicyService
{
    protected array $loweredPrices;
    protected int $serviceProductId;
    protected int $qualityTierId;
    protected int $propertyTypeId;
    protected int $stateLocationId;
    protected ?int $countyLocationId;

    public function handlePolicySideEffects(
        LoweredFloorPricePolicy $policy,
        array $policyFields,
        int $serviceProductId,
        int $qualityTierId,
        int $propertyTypeId,
        int $stateLocationId,
        array $currentPriceCollection,
        ?int $countyLocationId = null,
    ): bool
    {
        $this->serviceProductId = $serviceProductId;
        $this->qualityTierId = $qualityTierId;
        $this->propertyTypeId = $propertyTypeId;
        $this->stateLocationId = $stateLocationId;
        $this->countyLocationId = $countyLocationId;
        $this->loweredPrices = $currentPriceCollection;

        return match ($policy) {
            LoweredFloorPricePolicy::CREATE_CAMPAIGN_BIDS => $this->createBidsForActiveCampaigns($policyFields),
            LoweredFloorPricePolicy::NO_ACTION            => $this->noAction(),
        };
    }

    /**
     * When lowering a floor price in a state or county, this creates bids at the current price
     * for any campaign which:
     *  - has received a lead in the last X days (default 30)
     *  - is not using custom floor pricing
     *  - has active zip codes in the state or county where the prices are being lowered
     * For each valid campaign, a bid at the pre-modified price will be created for each sale type which:
     *  - is having its price lowered
     *  - the campaigns has no current bid in place for
     *
     * @param array $policyFields
     * @return bool
     */
    protected function createBidsForActiveCampaigns(array $policyFields): bool
    {
        $recentPurchaseThreshold = $policyFields[LoweredFloorPricePolicy::MODIFY_CAMPAIGNS_RECENCY_THRESHOLD];
        $recentPurchaseTimestamp = now()->subDays($recentPurchaseThreshold);
        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()->find($this->serviceProductId);

        $affectedCampaignBidModules = CompanyCampaign::query()
            ->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(Budget::TABLE, Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID)
            ->join(CompanyCampaignBidPriceModule::TABLE, CompanyCampaignBidPriceModule::TABLE .'.'. CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(ProductAssignment::TABLE, fn(JoinClause $join) =>
                $join->on(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_BUDGET_ID, '=', Budget::TABLE .'.'. Budget::FIELD_ID)
                    ->where(ProductAssignment::FIELD_DELIVERED, true)
                    ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', $recentPurchaseTimestamp)
                    ->where(ProductAssignment::FIELD_QUALITY_TIER_ID, '=', $this->qualityTierId)
            )->join(CompanyCampaignLocationModule::TABLE, CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID, '=', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID)
            ->join(CompanyCampaignLocationModuleLocation::TABLE, CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID, '=', CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_ID)
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ID, '=', CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->when($this->countyLocationId, fn(Builder $query) =>
                $query->join(Location::TABLE . ' as county_locations', fn(JoinClause $join) =>
                    $join->on('county_locations.' . Location::COUNTY_KEY, '=', Location::TABLE .'.'. Location::COUNTY_KEY)
                        ->where('county_locations.' . Location::TYPE, Location::TYPE_COUNTY)
                        ->where('county_locations.' . Location::ID, $this->countyLocationId)
            ))->when(!$this->countyLocationId, fn(Builder $query) =>
                $query->join(Location::TABLE . ' as state_locations', fn(JoinClause $join) =>
                $join->on('state_locations.' . Location::STATE_KEY, '=', Location::TABLE .'.'. Location::STATE_KEY)
                    ->where('state_locations.' . Location::TYPE, Location::TYPE_STATE)
                    ->where('state_locations.' . Location::ID, $this->stateLocationId)
            ))->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID, $serviceProduct->industry_service_id)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_PRODUCT_ID, $serviceProduct->product_id)
            ->where(CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES, false)
            ->distinct(CompanyCampaignBidPriceModule::TABLE .'.'. CompanyCampaignBidPriceModule::FIELD_ID)
            ->select([CompanyCampaignBidPriceModule::TABLE .'.'. CompanyCampaignBidPriceModule::FIELD_ID])
            ->pluck(CompanyCampaignBidPriceModule::FIELD_ID)
            ->toArray();

        if ($affectedCampaignBidModules) {
            if ($this->countyLocationId) {
                $upsertData = $this->createBidDataForCountyLocation($affectedCampaignBidModules);
                if ($upsertData) {
                    if (!ProductCountyBidPrice::query()->upsert($upsertData, [], [ProductCountyBidPrice::FIELD_PRICE])) {
                        return false;
                    }
                }
            }
            else {
                $upsertData = $this->createBidDataForStateLocation($affectedCampaignBidModules);
                if ($upsertData) {
                    if (!ProductStateBidPrice::query()->upsert($upsertData, [], [ProductStateBidPrice::FIELD_PRICE])) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    /**
     * @param array $affectedCampaignBidModuleIds
     * @return array
     */
    protected function createBidDataForCountyLocation(array $affectedCampaignBidModuleIds): array
    {
        $upsertData = [];
        $existingBids = ProductCountyBidPrice::query()
            ->whereIn(ProductCountyBidPrice::FIELD_MODULE_ID, $affectedCampaignBidModuleIds)
            ->where([
                ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID => $this->serviceProductId,
                ProductCountyBidPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierId,
                ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeId,
                ProductCountyBidPrice::FIELD_STATE_LOCATION_ID  => $this->stateLocationId,
                ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID  => $this->countyLocationId
            ])->select([ProductCountyBidPrice::FIELD_SALE_TYPE_ID, ProductCountyBidPrice::FIELD_MODULE_ID])
            ->get()
            ->reduce(function($output, $moduleBid) {
                $output[$moduleBid->module_id] = $output[$moduleBid->module_id] ?? [];
                $output[$moduleBid->module_id][] = $moduleBid->sale_type_id;
                return $output;
            }, []);

        foreach($affectedCampaignBidModuleIds as $moduleId) {
            $saleTypesWithBids = $existingBids[$moduleId] ?? [];
            foreach($this->loweredPrices as $saleTypeId => $price) {
                if (!in_array($saleTypeId, $saleTypesWithBids)) {
                    $upsertData[] = [
                        ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID => $this->serviceProductId,
                        ProductCountyBidPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierId,
                        ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeId,
                        ProductCountyBidPrice::FIELD_STATE_LOCATION_ID  => $this->stateLocationId,
                        ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID => $this->countyLocationId,
                        ProductCountyBidPrice::FIELD_MODULE_ID          => $moduleId,
                        ProductCountyBidPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                        ProductCountyBidPrice::FIELD_PRICE              => $price,
                    ];
                }
            }
        }

        return $upsertData;
    }

    /**
     * @param array $affectedCampaignBidModuleIds
     * @return array
     */
    protected function createBidDataForStateLocation(array $affectedCampaignBidModuleIds): array
    {
        $upsertData = [];
        $existingBids = ProductStateBidPrice::query()
            ->whereIn(ProductStateBidPrice::FIELD_MODULE_ID, $affectedCampaignBidModuleIds)
            ->where([
                ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID => $this->serviceProductId,
                ProductStateBidPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierId,
                ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeId,
                ProductStateBidPrice::FIELD_STATE_LOCATION_ID  => $this->stateLocationId
            ])->select([ProductStateBidPrice::FIELD_SALE_TYPE_ID, ProductStateBidPrice::FIELD_MODULE_ID])
            ->get()
            ->reduce(function($output, $moduleBid) {
                $output[$moduleBid->module_id] = $output[$moduleBid->module_id] ?? [];
                $output[$moduleBid->module_id][] = $moduleBid->sale_type_id;
                return $output;
            }, []);

        foreach($affectedCampaignBidModuleIds as $moduleId) {
            $saleTypesWithBids = $existingBids[$moduleId] ?? [];
            foreach($this->loweredPrices as $saleTypeId => $price) {
                if (!in_array($saleTypeId, $saleTypesWithBids)) {
                    $upsertData[] = [
                        ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID => $this->serviceProductId,
                        ProductStateBidPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierId,
                        ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeId,
                        ProductStateBidPrice::FIELD_STATE_LOCATION_ID  => $this->stateLocationId,
                        ProductStateBidPrice::FIELD_MODULE_ID          => $moduleId,
                        ProductStateBidPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                        ProductStateBidPrice::FIELD_PRICE              => $price,
                    ];
                }
            }
        }

        return $upsertData;
    }

    /**
     * @return bool
     */
    protected function noAction(): bool
    {
        return true;
    }
}
