<?php

namespace App\Services\Odin\Pricing;

use App\Enums\Odin\Region;
use App\Events\FloorPricingUpdatedEvent;
use App\Models\Odin\ProductStateFloorPrice;
use Illuminate\Support\Collection;

class FloorPriceChangesService
{
    const string SALE_TYPE_KEY = 'sale_type_key';

    /**
     * @param array $statePrices
     * @param array $countyPrices
     * @param array $priceUpdates
     * @param int $serviceProductId
     * @param int $countyLocationId
     * @return void
     */
    public function dispatchCountyPriceUpdateNotification(
        array $statePrices,
        array $countyPrices,
        array $priceUpdates,
        int $serviceProductId,
        int $countyLocationId,
    ): void
    {
        // initialise to state price
        $initialPrices = $statePrices;

        // update initial prices with any county prices, as this is an update county price function
        foreach ($countyPrices as $saleTypeId => $countyPrice) {
            $initialPrices[$saleTypeId]['price'] = $countyPrice;
        }

        // map initial price to sale type => price, in same manner as the price updates.
        $initialPrices = collect($initialPrices)->mapWithKeys(function ($item) {
            return [$item[self::SALE_TYPE_KEY] => $item[ProductStateFloorPrice::FIELD_PRICE]];
        })->toArray();

        $this->formatAndDispatchFloorPriceUpdate(
            initialPrices: $initialPrices,
            newPrices: $priceUpdates,
            locationId: $countyLocationId,
            serviceProductId: $serviceProductId,
            region: Region::COUNTY
        );
    }

    /**
     * @param array $initialPrices
     * @param array $newPrices
     * @param int $locationId
     * @param int $serviceProductId
     * @param Region $region
     * @return void
     */
    public function formatAndDispatchFloorPriceUpdate(
        array $initialPrices,
        array $newPrices,
        int $locationId,
        int $serviceProductId,
        Region $region
    ): void
    {
        $formattedPriceUpdates = $this->formatFloorPricingChanges(
            oldPricing: $initialPrices,
            newPricing: $newPrices
        )->toArray();

        if (!empty($formattedPriceUpdates)) {
            FloorPricingUpdatedEvent::dispatch(
                $region->value,
                $locationId,
                $serviceProductId,
                $formattedPriceUpdates,
            );
        }
    }

    /**
     * @param array $oldPricing
     * @param array $newPricing
     * @return Collection
     */
    public function formatFloorPricingChanges(array $oldPricing, array $newPricing): Collection
    {
        return collect($newPricing)->filter(function ($value, $key) use ($oldPricing) {
            return (float)$value !== (float)$oldPricing[$key]; //remove matching prices
        })->mapWithKeys(function ($value, $saleType) use ($oldPricing) {
            return [$saleType => [
                'old' => (float)$oldPricing[$saleType],
                'new' => (float)$value
            ]];
        });
    }
}