<?php

namespace App\Services\Odin;

use App\Enums\CompanyMediaAssetType;
use App\Models\Legacy\EloquentCompanyYoutubeLink;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyMediaAsset;
use App\Services\YoutubeHelperService;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateLegacyCompanyYoutubeLinksService
{
    /**
     * @param Collection $links
     * @return void
     */
    public function handle(Collection $links): void
    {
        $companyYoutubeLinks = [];
        $now                 = Carbon::now();

        /** @var EloquentCompanyYoutubeLink $link */
        foreach ($links as $link) {
            if(!empty($link[EloquentCompanyYoutubeLink::LINK])) {

                /** @var Company|null $company */
                $company = Company::query()
                    ->where(Company::FIELD_LEGACY_ID, $link->{EloquentCompanyYoutubeLink::COMPANY_ID})
                    ->first();

                if(!empty($company)) {

                    $baseCriteria = [
                        CompanyMediaAsset::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                        CompanyMediaAsset::FIELD_URL        => YoutubeHelperService::generateYoutubeLinkFromVideoId($link[EloquentCompanyYoutubeLink::LINK]),
                    ];

                    /** @var CompanyMediaAsset|null $alreadyAdded */
                    $alreadyAdded = CompanyMediaAsset::query()->where($baseCriteria)->first();
                    if(!$alreadyAdded) {
                        $companyYoutubeLinks[] = [
                            ...$baseCriteria,
                            CompanyMediaAsset::FIELD_TYPE       => CompanyMediaAssetType::LINK->value,
                            CompanyMediaAsset::FIELD_CREATED_AT => $now,
                            CompanyMediaAsset::FIELD_UPDATED_AT => $now,
                        ];
                    }
                } else {
                    logger()->error("Company doesn't exist for the link: `{$link->{EloquentCompanyYoutubeLink::ID}}`");
                }
            } else {
                logger()->error("The Youtube link/ID is missing for the link: `{$link->{EloquentCompanyYoutubeLink::ID}}`");
            }
        }

        if(!empty($companyYoutubeLinks)) {
            DB::table(CompanyMediaAsset::TABLE)->insert($companyYoutubeLinks);
        }
    }
}
