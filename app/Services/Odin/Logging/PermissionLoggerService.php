<?php

namespace App\Services\Odin\Logging;

use App\Models\Permission;
use App\Models\Role;
use App\Repositories\ActivityLog\ActivityLogRepository;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\User;
use Illuminate\Support\Facades\Auth;

/**
 *
 */
class PermissionLoggerService extends LoggerService
{
    /**
     *
     */
    const CHANNEL_PERMISSIONS = 'permissions';

    /**
     *
     */
    public function __construct()
    {
        parent::__construct(self::CHANNEL_PERMISSIONS);
    }

    /**
     * Optional Prefix for Every Log Statement
     *
     * @return string
     */
    protected function getLogPrefix(): string
    {
        return 'User: ' . Auth::user()->name .' [ID: '. Auth::user()->id .'] ';
    }

    /**
     * Returns arrays of what has been removed and added
     *
     * @param array $oldElements
     * @param array $newElements
     * @return array
     */
    protected function getRemovedAndAddedElements(array $oldElements, array $newElements): array
    {
        $removedElements = array_diff($oldElements, $newElements);
        $addedElements = array_diff($newElements, $oldElements);
        return array($removedElements, $addedElements);
    }

    /**
     * Logs the change in roles associated with a given user
     *
     * @param User $user
     * @param array $oldRoles
     * @param array $newRoles
     * @return void
     */
    public function logUserRoleChange(User $user, array $oldRoles, array $newRoles): void
    {
        $logString = ' modified user: '. $user->name . ' [ID: ' .$user->id . ']' . ' by ';

        $this->logDifferenceOfArrays($oldRoles, $newRoles, $logString, 'roles:', Role::class);

        if (!empty(array_diff($newRoles, $oldRoles)) || !empty(array_diff($oldRoles, $newRoles))) {
            app(ActivityLogRepository::class)
                ->createActivityLog(
                    logName: 'user_roles',
                    description: 'User roles updated',
                    subjectType: User::class,
                    subjectId: $user->id,
                    properties: [
                        'old_roles' => $oldRoles,
                        'new_roles' => $newRoles
                    ]
                );
        }
    }

    public function logUserDirectPermissionChange(User $user, array $oldPermissions, array $newPermissions): void
    {
        if (!empty(array_diff($oldPermissions, $newPermissions)) || !empty(array_diff($newPermissions, $oldPermissions))) {
            app(ActivityLogRepository::class)
                ->createActivityLog(
                    logName: 'user_direct_permissions',
                    description: 'User direct permissions updated',
                    subjectType: User::class,
                    subjectId: $user->id,
                    properties: [
                        'old_permissions' => $oldPermissions,
                        'new_permissions' => $newPermissions
                    ]
                );
        }
    }

    /**
     * Logs the changes in two different arrays, the first being old and the second being the updated arrays.
     * $prefix is a string added before appending what fields have been added and what have been removed.
     *
     *
     * @param array $oldElements
     * @param array $newElements
     * @param string $prefix
     * @param string $removedAndAdded
     * @param $model
     * @return void
     */
    protected function logDifferenceOfArrays(array $oldElements, array $newElements, string $prefix, string $removedAndAdded, $model): void
    {
        list($removedElements, $addedElements) = $this->getRemovedAndAddedElements($oldElements, $newElements);

        if (count($removedElements) > 0) {
            $prefix .= 'removing ' .$removedAndAdded .' ' . $this->getNameListImploded($model, $removedElements);
        }

        if (count($addedElements) > 0) {
            if (count($removedElements) > 0) {
                $prefix .= ', and ';
            }
            $prefix .= 'adding ' .$removedAndAdded .' '. $this->getNameListImploded($model, $addedElements);
        }

        $this->writeInfoLog($prefix);
    }

    /**
     * Takes a modelType, like Permission or Role and grabs all the names based on the ids given
     *
     * @param $modelType
     * @param array $elementIds
     * @return string
     */
    protected function getNameListImploded($modelType, array $elementIds): string
    {
        $modelInstance = new $modelType;
        $listed = $modelInstance->query()->whereIn('id', $elementIds)->pluck('name')->toArray();
        return implode(', ', $listed);
    }

    /**
     * Log a user changing the permissions linked to a role
     *
     * @param string $roleName
     * @param array $oldPermissions
     * @param array $newPermissions
     * @return void
     */
    public function logRolePermissionChange(string $roleName, array $oldPermissions, array $newPermissions):void
    {
        $logString = ' modified role: '. $roleName . ' by ';
        $this->logDifferenceOfArrays($oldPermissions, $newPermissions, $logString, 'permissions:', Permission::class);
    }

    /**
     * Log a user creating a permission
     *
     * @param Model $permission
     * @return void
     */
    public function logCreatePermission(Model $permission)
    {
        $logString = ' added permission: ' . $permission->name . ' [ID: ' . $permission->id .']';

        $this->writeInfoLog($logString);
    }

    /**
     * Log a user creating a role
     *
     * @param Model $role
     * @return void
     */
    public function logCreateRole(Model $role)
    {
        $logString = ' added role: ' . $role->name . ' [ID: ' . $role->id .']';

        $this->writeInfoLog($logString);
    }

    /**
     * Log a user updating a permission title
     *
     * @param string $oldPermissionName
     * @param string $newPermissionName
     * @return void
     */
    public function logUpdatePermission(string $oldPermissionName, string $newPermissionName): void
    {
        $logString = ' changed permission title from: '. $oldPermissionName . ' to: ' . $newPermissionName .'.';

        $this->writeInfoLog($logString);
    }

    /**
     * Log a user updating a role name
     *
     * @param string $oldRoleName
     * @param string $newRoleName
     * @return void
     */
    public function logUpdateRoleName(string $oldRoleName, string $newRoleName): void
    {
        $logString = ' changed role title from: '. $oldRoleName . ' to: ' . $newRoleName .'.';

        $this->writeInfoLog($logString);
    }
}
