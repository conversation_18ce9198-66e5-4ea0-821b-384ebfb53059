<?php

namespace App\Services\Odin\Logging;

use Illuminate\Support\Facades\Log;

abstract class LoggerService
{
    public function __construct(protected string $channel)
    {

    }

    protected function getLogPrefix(): string
    {
        return '';
    }

    protected function writeInfoLog(string $logString): void
    {
        Log::channel($this->channel)->info($this->getLogPrefix() . $logString);
    }
}
