<?php

namespace App\Services\Odin;

use App\Enums\Odin\LocationSiloPageLocationType;
use App\Models\Odin\LocationSiloPage;
use Illuminate\Support\Collection;

class SiloShortcodeService
{
    const SILO_CODE_COUNTRY     = 'country';
    const SILO_CODE_STATE       = 'state';
    const SILO_CODE_CITY        = 'city';
    const SILO_CODE_INDUSTRY    = 'industry';

    /**
     * @return Collection<string>
     */
    public function getShortcodes(): Collection
    {
        return collect([
            'City'      => self::SILO_CODE_CITY,
            'Country'   => self::SILO_CODE_COUNTRY,
            'State'     => self::SILO_CODE_STATE,
            'Industry'  => self::SILO_CODE_INDUSTRY,
        ]);
    }

    /**
     * @param string $relativePath
     * @param array $locationData
     * @param array $commonShortcodes
     * @return string
     */
    public function processShortcodes(string $relativePath, array $locationData, array $commonShortcodes): string
    {
        return preg_replace_callback("/\{[0-z-]+}/", function($match) use ($locationData, $commonShortcodes) {
            $key = preg_replace("/[{}]/", "", $match[0]);
            return $locationData[$key] ?? $commonShortcodes[$key] ?? $key;
        }, $relativePath);
    }

    /**
     * Generate single relative path with shortcodes
     * Use only for single updates
     * @param int $locationSiloPageId
     * @param string $relativePath
     * @return string
     */
    public function handleLocationSiloShortcode(int $locationSiloPageId, string $relativePath): string
    {
        if (!preg_match("/\{[0-z-]+}/", $relativePath)) {
            return $relativePath;
        }

        $locationSiloPage = LocationSiloPage::query()
            ->findOrFail($locationSiloPageId);

        $commonShortcodes = [
            SiloShortcodeService::SILO_CODE_COUNTRY     => 'US',
            SiloShortcodeService::SILO_CODE_INDUSTRY    => $locationSiloPage->silo?->industry?->slug ?? 'missing-industry',
        ];
        $locationShortcodes = [
            self::SILO_CODE_STATE   =>  "",
            self::SILO_CODE_CITY    => "",
        ];

        if ($locationSiloPage->location_type === LocationSiloPageLocationType::STATE) {
            $locationShortcodes[self::SILO_CODE_STATE] = $locationSiloPage->location->state_key;
        }
        if ($locationSiloPage->location_type === LocationSiloPageLocationType::CITY) {
            $locationShortcodes[self::SILO_CODE_STATE] = $locationSiloPage->parentLocation->state_key;
            $locationShortcodes[self::SILO_CODE_CITY]  = $locationSiloPage->location->city_key;
        }

        return $this->processShortcodes($relativePath, $locationShortcodes, $commonShortcodes);
    }
}
