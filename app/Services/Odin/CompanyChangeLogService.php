<?php

namespace App\Services\Odin;

use App\Enums\Odin\CompanyChangeLogType;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyChangeLog;
use Carbon\Carbon;

class CompanyChangeLogService
{
    /**
     * @param Company $company
     * @param CompanyChangeLogType $log
     * @param array $payload
     * @return CompanyChangeLog
     */
    public function log(
        Company $company,
        CompanyChangeLogType $log,
        array $payload
    ): CompanyChangeLog
    {
        $ccl = new CompanyChangeLog();

        $ccl->{CompanyChangeLog::FIELD_COMPANY_ID} = $company->{Company::FIELD_ID};
        $ccl->{CompanyChangeLog::FIELD_LOG} = $log;
        $ccl->{CompanyChangeLog::FIELD_PAYLOAD} = $payload;

        $ccl->{CompanyChangeLog::CREATED_AT} = Carbon::now('UTC');

        $ccl->save();

        return $ccl;
    }
}
