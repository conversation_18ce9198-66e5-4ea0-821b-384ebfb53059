<?php

namespace App\Services\Odin;

use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Enums\Odin\Product as ProductEnum;
use App\DTO\Billing\InvoiceItemDTO;
use App\Events\ProductAssignment\ProductRejectedEvent;
use App\Events\ProductAssignment\ProductUnrejectedEvent;
use App\Jobs\Appointments\AttemptAppointmentDeliveryJob;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\ProductRejectionCalculationService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class ProductAssignmentService
{
    protected ProductAssignmentRepository $assignmentRepository;

    public function __construct(ProductAssignmentRepository $productAssignmentRepository)
    {
        $this->assignmentRepository = $productAssignmentRepository;
    }

    /**
     * @param Company $company
     * @param ProductAssignment $productAssignment
     * @param array $rejectionPayload
     * @return bool
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function validateAndRejectProduct(Company $company, ProductAssignment $productAssignment, array $rejectionPayload): bool
    {
        $rejectionExpiryIsValid = $productAssignment->rejection_expiry > now();
        if (!$rejectionExpiryIsValid)
            throw new Exception("This product's rejection window has expired.");

        /** @var ProductRejectionCalculationService $rejectionCalculationService */
        $rejectionCalculationService = app()->make(ProductRejectionCalculationService::class);
        $companyCanRejectProduct = $rejectionCalculationService->companyCanManuallyRejectProduct($company, $productAssignment->consumerProduct->serviceProduct->product);
        if (!$companyCanRejectProduct)
            throw new Exception("Rejection quota has been exceeded for this product.");

        if ($productAssignment->productRejections()->whereNotNull(ProductRejection::FIELD_DELETED_AT)->count() > 0)
            throw new Exception("This product has already been rejected.");

        $this->rejectProductAssignment(
            productAssignment: $productAssignment,
            companyUserId    : $rejectionPayload[ProductRejection::FIELD_COMPANY_USER_ID],
            rejectReason     : $rejectionPayload[ProductRejection::FIELD_REASON]
        );

        return true;
    }


    /**
     * @param ProductAssignment $productAssignment
     * @param int $companyUserId
     * @param string $rejectReason
     * @return bool
     * @throws BindingResolutionException
     */
    public function rejectProductAssignment(
        ProductAssignment $productAssignment,
        int $companyUserId,
        string $rejectReason,
    ): bool
    {
        $productAssignment->productRejections()->create([
            ProductRejection::FIELD_COMPANY_USER_ID => $companyUserId,
            ProductRejection::FIELD_REASON          => $rejectReason,
        ]);

        if ($productAssignment->consumerProduct->serviceProduct->product->name === ProductEnum::APPOINTMENT->value)
            $this->notifyConsumerOfCancelledAppointment($productAssignment);

        /** @var LegacyModule $legacyModule */
        $legacyModule = app()->make(LegacyModule::class);
        $legacyModule->updateLegacyQuoteCompany($productAssignment);

        ProductRejectedEvent::dispatch($productAssignment->id);

        return true;
    }

    /**
     * @param Company $company
     * @param ProductAssignment $productAssignment
     * @return bool
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function unrejectProductAssignment(Company $company, ProductAssignment $productAssignment): bool
    {
        $productAssignmentIsRejected = $productAssignment->productRejections()->whereNotNull(ProductRejection::FIELD_DELETED_AT)->first();
        if ($productAssignmentIsRejected)
            throw new Exception("This product cannot be unrejected.");

        $productAssignment->productRejections()
            ->delete();

        $productAssignment->update([
            ProductAssignment::FIELD_CHARGEABLE => true,
        ]);

        /** @var LegacyModule $legacyModule */
        $legacyModule = app()->make(LegacyModule::class);
        $legacyModule->updateLegacyQuoteCompany($productAssignment);

        ProductUnrejectedEvent::dispatch($productAssignment->id);

        return true;
    }

    /**
     * @param Company $company
     * @param ProductAssignment $productAssignment
     * @return bool
     * @throws BindingResolutionException
     */
    public function rejectionWillExceedThreshold(Company $company, ProductAssignment $productAssignment): bool
    {
        /** @var ProductRejectionCalculationService $rejectionCalculationService */
        $rejectionCalculationService = app()->make(ProductRejectionCalculationService::class);

        return $rejectionCalculationService->potentialRejectionExceedsThreshold($company, $productAssignment);
    }

    /**
     * @param Company $company
     * @param Carbon|null $deliveredBefore
     * @param array $campaignIds
     * @param array $consumerProductIds
     * @param array $relations
     * @return Collection
     */
    public function getUninvoicedProductAssignments(
        Company $company,
        ?Carbon $deliveredBefore = null,
        array $campaignIds = [],
        array $consumerProductIds = [],
        array $relations = [],
    ): Collection
    {
        if ($deliveredBefore === null) {
            $deliveredBefore = Carbon::now();
        }
        return $this->assignmentRepository->getChargeableUninvoicedProductAssignments(
            company           : $company,
            deliveredBefore   : $deliveredBefore,
            campaignIds       : $campaignIds,
            consumerProductIds: $consumerProductIds,
            relations         : $relations
        );
    }

    /**
     * @param Collection<ProductAssignment> $productAssignments
     * @return Collection<InvoiceItemDTO>
     */
    public function transformToInvoiceItems(Collection $productAssignments): Collection
    {
        $invoiceItems = collect();

        foreach ($productAssignments as $productAssignment) {
            $productType = $productAssignment
                ->{ProductAssignment::RELATION_CONSUMER_PRODUCT}
                ->{ConsumerProduct::RELATION_SERVICE_PRODUCT}
                ->{ServiceProduct::RELATION_PRODUCT}
                ->{Product::FIELD_NAME};

            $consumerProductId = $productAssignment
                ->{ProductAssignment::RELATION_CONSUMER_PRODUCT}
                ->{ConsumerProduct::FIELD_ID};

            $consumerName = $productAssignment->{ProductAssignment::RELATION_CONSUMER}->getInitial(false);

            $description = $productType . " " . $consumerProductId . ": " . $consumerName;

            $invoiceItems->push(new InvoiceItemDTO(
                billable_id  : $productAssignment->{ProductAssignment::FIELD_ID},
                billable_type: ProductAssignment::class,
                unit_price   : $productAssignment->{ProductAssignment::FIELD_COST} * 100,
                quantity     : 1,
                description  : $description,
            ));
        }

        return $invoiceItems;
    }

    /**
     * @param string $proxyPhone
     *
     * @return ProductAssignment|null
     */
    public function findProductAssignmentFromProxyPhone(string $proxyPhone): ?ProductAssignment
    {
        return ProductAssignment::query()
            ->whereLike(ProductAssignment::FIELD_PROXY_PHONE, "%$proxyPhone%")
            ->where(ProductAssignment::FIELD_PROXY_PHONE_ACTIVE, true)
            ->first();
    }

    /**
     * Notify the consumer that the company cancelled the appointment when an Appointment product is rejected
     * @param ProductAssignment $productAssignment
     * @return void
     */
    private function notifyConsumerOfCancelledAppointment(ProductAssignment $productAssignment): void
    {
        $appointmentTime = $productAssignment->consumerProduct->appointment?->appointment;
        if (!$appointmentTime || $appointmentTime <= now())
            return;

        $cancellationPayload = [
            AttemptAppointmentDeliveryJob::CANCELLATION_DELIVERED_COMPANY  => true,
            AttemptAppointmentDeliveryJob::CANCELLATION_DELIVERED_CONSUMER => false,
        ];

        AttemptAppointmentDeliveryJob::dispatch(
            $productAssignment->id,
            null,
            $cancellationPayload,
        );
    }
}
