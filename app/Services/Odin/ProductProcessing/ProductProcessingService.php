<?php

namespace App\Services\Odin\ProductProcessing;

use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Enums\Odin\Product as ProductEnum;
use App\Events\ConsumerProcessing\ConsumerProductApprovedEvent;
use App\Jobs\ConsumerProcessing\CheckProductHeartbeatJob;
use App\Jobs\RecordMonitoringLog;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\Odin\Campaigns\FutureCampaignAvailabilityService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Throwable;

class ProductProcessingService
{
    const int LEAD_LOCKED_BY_SYSTEM = -1;
    const int FALLBACK_UTC_OFFSET   = -5;

    const bool ALLOW_UNVERIFIED_ALLOCATION = true;
    const int  PREMIUM_ELECTRICAL_USAGE    = 300;

    /**
     * Whether we should log the progress of this job.
     *
     * @var bool
     */
    protected bool $shouldLog;

    /**
     * @param ProductProcessingRepository $productProcessingRepository
     * @param Dispatcher $dispatcher
     * @param ProductProcessingQueueRepository $queueRepository
     */
    public function __construct(
        protected ProductProcessingRepository              $productProcessingRepository,
        protected Dispatcher                               $dispatcher,
        protected ProductProcessingQueueRepository         $queueRepository,
    ) {
        $this->shouldLog = true;
    }

    /**
     * We can test removing legacy_id syncing through much of the updated processing logic
     *  by just returning null here
     *
     * @param ConsumerProduct|int $consumerProduct
     * @return int|null
     */
    public static function getLegacyId(ConsumerProduct|int $consumerProduct): ?int
    {
        if (gettype($consumerProduct) === 'integer')
            $consumerProduct = ConsumerProduct::query()->find($consumerProduct);

        return $consumerProduct?->consumer?->legacy_id ?? null;
    }

    /**
     * Verifies that a given lead is locked by a given lead processor.
     *
     * @param LeadProcessor $processor
     * @param ConsumerProduct $product
     *
     * @return bool
     */
    public function isProductReservedByProcessor(ConsumerProduct $product, LeadProcessor $processor): bool
    {
        return LeadProcessingReservedLead::query()
            ->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $product->id)
            ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, $processor->id)
            ->exists();
    }

    /**
     * Checks to see if lead is locked by "system"
     * @param ConsumerProduct $product
     * @return bool
     */
    public function isLeadReservedBySystem(ConsumerProduct $product): bool
    {
        return LeadProcessingReservedLead::query()
                ->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $product->id)
                ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, LeadProcessingReservedLead::SYSTEM_ID)
                ->exists();
    }

    /**
     * Reserves a lead for a given lead processor.
     *
     * @param ConsumerProduct|null $product
     * @param LeadProcessor $processor
     *
     * @return bool
     * @throws Exception
     */
    public function reserveProduct(?ConsumerProduct $product, LeadProcessor $processor): bool
    {
        if (!$product) return false;

        /** @var LeadProcessingReservedLead $reserved */
        $reserved = LeadProcessingReservedLead::query()->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $product->id)->first();

        if($reserved !== null && $reserved->processor_id !== $processor->id) return false;

        return $this->productProcessingRepository->reserveProduct($product->id, $processor->id);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @return bool
     */
    public function releaseProduct(ConsumerProduct $consumerProduct, LeadProcessor $processor): bool
    {
        if ($this->isProductReservedByProcessor($consumerProduct, $processor) || $this->isLeadReservedBySystem($consumerProduct)) {
            $this->productProcessingRepository->releaseProduct($consumerProduct->id);
            $this->removeHeartbeat($consumerProduct, $processor);

            return true;
        }

        return false;
    }

    /**
     * Handles getting and reserving the next product in the queue for a given processor.
     *
     * @param LeadProcessor $processor
     * @param int|null $specificProductId
     * @param int|null $previousProductId
     *
     * @return ConsumerProduct|null
     * @throws Exception
     */
    public function getNextProduct(LeadProcessor $processor, ?int $specificProductId = null, ?int $previousProductId = null): ?ConsumerProduct
    {
        if ($specificProductId !== null) {
            return $this->getAndReserveByProductId($specificProductId, $processor);
        }

        $product = $this->queueRepository->getNextProduct($processor, $previousProductId);

        $this->queueRepository->flipQueueState($processor->team->primaryQueue);

        return $product;
    }

    /**
     * Handles the retrieval and reservation of a product by a specific product id.
     *
     * @param int $productId
     * @param LeadProcessor $processor
     *
     * @return ConsumerProduct|null
     * @throws Exception
     */
    protected function getAndReserveByProductId(int $productId, LeadProcessor $processor): ?ConsumerProduct
    {
        /** @var ConsumerProduct|null $product */
        $product = ConsumerProduct::query()->find($productId);

        if (!$product) return null;

        if (!$this->isProductReservedByProcessor($product, $processor) && !in_array($product->status, [ConsumerProduct::STATUS_ALLOCATED, ConsumerProduct::STATUS_CANCELLED])) {
            if (!$this->reserveProduct($product, $processor)) {
                return null;
            }
        }

        return $product;
    }

    /**
     * Handles approving a product based on the requested params.
     *
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $bestTimeToContact
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param int[]|null $excludedCompanies
     *
     * @return bool
     *
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function approveProduct(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $bestTimeToContact = null,
        ?string         $comment = null,
        ?bool           $publicComment = null,
        ?array          $excludedCompanies = null
    ): bool
    {
        $product = ProductEnum::from($consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{Product::FIELD_NAME});

        if(!in_array($product, ProductEnum::cases(), true)) {
            throw new Exception("Invalid product");
        }

        $this->logStatus(
            "Approving lead: {$consumerProduct->{ConsumerProduct::FIELD_ID}}",
            [
                "consumer_product_id" => $consumerProduct->{ConsumerProduct::FIELD_ID}
            ]
        );

        return $this->approveLead(
            consumerProduct: $consumerProduct,
            processor: $processor,
            reason: $reason,
            comment: $comment,
            publicComment: $publicComment,
            bestTimeToContact: $bestTimeToContact,
            excludedCompanies: $excludedCompanies
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @return bool
     * @throws Exception
     */
    public function cancelLead(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $comment = null,
        ?bool           $publicComment = null,
    ): bool
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $processor))
            return false;

        $this->queueRepository->changeQueues(
            consumerProduct: $consumerProduct,
            processor: $processor,
            newProcessingStatus: ProductProcessingQueueRepository::STATUS_CANCELLED,
            reason: $reason,
            comment: $comment,
            publicComment: $publicComment,
        );

        return $this->releaseProduct($consumerProduct, $processor);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @return bool
     * @throws Exception
     */
    public function moveToUnderReview(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $comment = null,
        ?bool           $publicComment = null,
    ): bool
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $processor))
            return false;

        $alreadySoldOffHours = $this->productProcessingRepository->productHadOffHourSales($consumerProduct);
        if ($alreadySoldOffHours) {
            $this->queueRepository->changeQueues(
                consumerProduct: $consumerProduct,
                processor: LeadProcessor::systemProcessor(),
                newProcessingStatus: ProductProcessingQueueRepository::STATUS_ALLOCATED,
                reason: '',
                comment: $comment,
                publicComment: $publicComment,
            );
        }
        else {
            $this->queueRepository->changeQueues(
                consumerProduct: $consumerProduct,
                processor: $processor,
                newProcessingStatus:  ProductProcessingQueueRepository::STATUS_UNDER_REVIEW,
                reason:  $reason,
                comment:  $comment,
                publicComment:  $publicComment,
            );
        }

        return $this->releaseProduct($consumerProduct, $processor);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @return bool
     * @throws BindingResolutionException
     */
    public function moveToPendingReview(
        ConsumerProduct $consumerProduct,
        LeadProcessor $processor,
        string $reason,
        ?string $comment = null,
        ?bool $publicComment = false,
    ): bool
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $processor))
            return false;

        /** @var FutureCampaignAvailabilityService $service */
        $service = app()->make(FutureCampaignAvailabilityService::class);

        $companiesAvailable = $service->companiesWithAvailableBudgetCount($consumerProduct);

        if ($companiesAvailable > 0) {
            // 'Verified' budget is available, move to 'Pending Review'
            $this->queueRepository->changeQueues(
                consumerProduct: $consumerProduct,
                processor: $processor,
                newProcessingStatus: ProductProcessingQueueRepository::STATUS_PENDING_REVIEW,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment,
            );
        } else{
            // No 'Verified' or 'Unverified' budget available, mark as 'Under Review'
            $this->queueRepository->changeQueues(
                consumerProduct: $consumerProduct,
                processor: $processor,
                newProcessingStatus: ProductProcessingQueueRepository::STATUS_UNDER_REVIEW,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment,
            );
        }

        return $this->releaseProduct($consumerProduct, $processor);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param string|null $bestTimeToContact
     * @param int[]|null $excludedCompanies
     *
     * @return bool
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function approveLead(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $comment = null,
        ?bool           $publicComment = null,
        ?string         $bestTimeToContact = null,
        ?array          $excludedCompanies = null
    ): bool
    {
        ConsumerProductLifecycleTrackingService::approvedToSell($consumerProduct);

        try {
            DB::beginTransaction();

            // This will have a value if QA qualifies via conversation.
            if($bestTimeToContact) {
                $this->productProcessingRepository->updateBestTimeToCall($consumerProduct, $bestTimeToContact);
            }

            if(!$this->markProductAsAllocated($consumerProduct, $processor, $reason, $comment, $publicComment)) {
                DB::rollback();
                return false;
            }

            $consumerReference = $consumerProduct->consumer->reference ?? null;
            if(!$consumerReference) {
                DB::rollback();
                return false;
            }

            $this->dispatcher->dispatch(new ConsumerProductApprovedEvent(
                consumerProduct: $consumerProduct,
                processor: $processor,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment,
            ));

            $this->productProcessingRepository->reserveProductToSystem($consumerProduct->id);

            $this->queueRepository->addProductToAllocationQueue(
                consumerProductId: $consumerProduct->{ConsumerProduct::FIELD_ID},
                processor: $processor,
                reason: $reason,
                deliverAt: now(), // new allocation doesn't use this
            );

            /** @var ConsumerProjectProcessingService $service */
            $service = app()->make(ConsumerProjectProcessingService::class);
            $service->allocate(
                consumer: $consumerProduct->consumer,
                address: $consumerProduct->address,
                excludedCompanies: $excludedCompanies
            );

            DB::commit();

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            $this->logStatus(
                $errLocation."Lead approval err: ".substr($e->getMessage(), 0, 255),
                [
                    "consumer_product_id" => $consumerProduct->{ConsumerProduct::FIELD_ID},
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * Handles marking an Initial lead as Allocated.
     *
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @return bool
     *
     * @throws Exception
     */
    public function markProductAsAllocated(
        ConsumerProduct $consumerProduct,
        LeadProcessor   $processor,
        string          $reason,
        ?string         $comment = null,
        ?bool           $publicComment = null,
    ): bool
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $processor))
            return false;

        $this->queueRepository->changeQueues($consumerProduct, $processor, ProductProcessingQueueRepository::STATUS_ALLOCATED, $reason, $comment, $publicComment);

        return $this->reserveProduct($consumerProduct, $processor);
    }

    /**
     * @param int $consumerProductId
     * @return bool
     */
    public function createInitialProduct(int $consumerProductId): bool
    {
        $initialQueue = LeadProcessingInitial::query()->firstOrCreate(
            [LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId],
            [LeadProcessingInitial::FIELD_LEAD_ID => self::getLegacyId($consumerProductId)]
        );

        return !empty($initialQueue);
    }

    /**
     * @param int $consumerProductId
     * @param string $reason
     * @param LeadProcessor $processor
     * @return bool
     */
    public function createPendingReviewLead(
        int $consumerProductId,
        string $reason,
        LeadProcessor $processor,
    ): bool
    {
        LeadProcessingPendingReview::query()->updateOrCreate([
            LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            LeadProcessingPendingReview::FIELD_USER_ID             => $processor->id,
            LeadProcessingPendingReview::FIELD_REASON              => $reason,
        ], [
            LeadProcessingPendingReview::FIELD_LEAD_ID => self::getLegacyId($consumerProductId),
        ]);

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @return bool
     */
    public function heartbeat(ConsumerProduct $consumerProduct, LeadProcessor $processor): bool
    {
        if (!$this->isProductReservedByProcessor($consumerProduct, $processor))
            return false;

        if (!$this->productProcessingRepository->hasHeartbeat($consumerProduct, $processor))
            dispatch(new CheckProductHeartbeatJob($consumerProduct->id, $processor->id));

        $this->productProcessingRepository->processHeartbeat($consumerProduct, $processor);

        return true;
    }

    /**
     *
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @return void
     */
    protected function removeHeartbeat(ConsumerProduct $consumerProduct, LeadProcessor $processor): void
    {
        $this->productProcessingRepository->removeHeartbeat($consumerProduct, $processor);
    }

    /**
     * Logs a status message of the job.
     *
     * This only runs if the shouldLog value is set to true.
     *
     * @param string $message
     * @param array $payload
     * @return void
     */
    public function logStatus(string $message, array $payload = []): void
    {
        try {
            if($this->shouldLog) {
                $payload['env'] = App::environment();

                RecordMonitoringLog::dispatch($message, $payload);
            }
        } catch (Exception $e) {
            logger()->error($e);
        }
    }
}
