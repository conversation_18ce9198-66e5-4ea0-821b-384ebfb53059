<?php

namespace App\Services\Odin\ProductProcessing;

use App\DataModels\Odin\Prices\BestRevenueScenarioDataModel;
use App\DataModels\Odin\Prices\BRSCampaignPrices;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\LeadProcessing\LeadAllocatedEvent;
use App\Events\LeadProcessing\LeadUndersoldEvent;
use App\Jobs\AllocateLeadJob;
use App\Jobs\DispatchPubSubEvent;
use App\Jobs\RecordMonitoringLog;
use App\Models\AppointmentProcessingAllocation;
use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingConfiguration;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\Industry;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\BestRevenueRecordingService;
use App\Services\LeadProcessing\LeadProcessingService;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\Odin\Campaigns\OdinCampaignAvailabilityService;
use App\Services\ProductPricing\BestRevenueScenario\BestRevenueScenarioServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Throwable;

class OdinProcessingService
{
    /**
     * Whether we should log the progress of this job.
     *
     * @var bool
     */
    protected bool $shouldLog = true;

    public function __construct(
        protected OdinCampaignAvailabilityService $campaignAvailabilityService,
        protected LeadProcessingService           $processingService,
        protected BestRevenueRecordingService     $bestRevenueRecordingService,
        protected LeadProcessingRepository        $repository,
        protected LeadDeliveryService             $leadDeliveryService,
        protected ConsumerProductRepository       $consumerProductRepository,
        protected Dispatcher                      $dispatcher,
    ) {}

    /**
     * @param LeadProcessingAllocation $allocation
     * @param array $companyIds
     * @param array $forceIncludeCampaignIds
     * @param int|null $remainingLegs
     * @return bool
     * @throws Throwable
     */
    public function allocateLead(
        LeadProcessingAllocation $allocation,
        array $companyIds = [],
        array $forceIncludeCampaignIds = [],
        ?int $remainingLegs = null
    ): bool
    {
        /** @var ConsumerProduct $product */
        $product = $allocation->{LeadProcessingAllocation::RELATION_CONSUMER_PRODUCT};

        if ($this->campaignAvailabilityService->areCompaniesAvailable($product, $companyIds)) {
            $companiesWithBudgetCount = $this->campaignAvailabilityService->companiesWithAvailableBudgetCount($product);

            if ($companiesWithBudgetCount === 0) {
                $this->logStatus("No companies with budget for lead: {$allocation->lead_id}", ["lead_id" => $allocation->lead_id, "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);

                Cache::remember("ps-lead-unsold-no-budget-{$allocation->lead_id}", LeadProcessingService::EXPIRY_1_DAY, function () use (&$allocation) {
                    DispatchPubSubEvent::dispatch(
                        EventCategory::LEADS,
                        EventName::LEAD_UNSOLD_NO_BUDGET,
                        ["lead_id" => $allocation->lead_id, "initiator_id" => $allocation->leadProcessor?->id ?? 0]
                    );
                });

                return $this->processCompaniesNotAvailable($allocation, EloquentQuote::VALUE_STATUS_UNDER_REVIEW, $companyIds, $remainingLegs);
            } else if ($companiesWithBudgetCount < $product->contact_requests) {
                Cache::remember("unsold-or-undersold-lead-{$allocation->lead_id}", LeadProcessingService::EXPIRY_1_DAY,
                    function () use ($allocation) {
                        LeadUndersoldEvent::dispatch($allocation->lead->uuid, $allocation->processing_scenario);

                        DispatchPubSubEvent::dispatch(
                            EventCategory::LEADS,
                            EventName::LEAD_UNDERSOLD,
                            ["lead_id" => $allocation->lead_id, "initiator_id" => $allocation->leadProcessor?->id ?? 0]
                        );

                        return true;
                    });

                $this->logStatus("Companies available are less than requested for lead: {$allocation->lead_id}", ["lead_id" => $allocation->lead_id, "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);
            }

            if ($this->processAvailableCompanies($allocation, $companyIds, $remainingLegs)) {
                $this->logStatus("Successfully processed lead: {$allocation->lead_id}", ["lead_id" => $allocation->lead_id, "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);
                return true;
            }
        }

        $this->logStatus("No companies available for lead: {$allocation->lead_id}", ["lead_id" => $allocation->lead_id, "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);

        return $this->processCompaniesNotAvailable($allocation, EloquentQuote::VALUE_STATUS_NO_COMPANIES, $companyIds, $remainingLegs);
    }

    /**
     * Handles processing that companies aren't available.
     *
     * @param LeadProcessingAllocation $allocation
     * @param string $status
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @return bool
     *
     * @TODO: Decide how we want to handle MI leads in the future. Reattempt to sell? Deliver to aggregators? Right now
     *          for speed, we do nothing.
     */
    protected function processCompaniesNotAvailable(LeadProcessingAllocation $allocation, string $status, array $ignoreCompanyIds = [], ?int $remainingLegs = null): bool
    {
        $this->logStatus(
            "Processing companies not available: {$allocation->lead_id}",
            [
                "lead_id" => $allocation->lead_id,
                "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
            ]
        );

        Cache::remember("unsold-or-undersold-lead-{$allocation->lead_id}", LeadProcessingService::EXPIRY_1_DAY, function () use ($allocation) {
            LeadUndersoldEvent::dispatch($allocation->lead->uuid, $allocation->processing_scenario);
            return true;
        });
        $this->bestRevenueRecordingService->updateRevenueScenario($allocation->lead, null);

        Cache::remember("ps-lead-unsold-no-companies-{$allocation->lead_id}", LeadProcessingService::EXPIRY_1_DAY, function () use (&$allocation) {
            DispatchPubSubEvent::dispatch(
                EventCategory::LEADS,
                EventName::LEAD_UNSOLD_NO_COMPANIES,
                ["lead_id" => $allocation->lead_id, "initiator_id" => $allocation->leadProcessor?->id ?? 0]
            );

            return true;
        });

        $this->repository->updateLeadStatus($allocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE}, EloquentQuote::VALUE_STATUS_NO_COMPANIES);
        $allocation->consumerProduct->status = ConsumerProduct::STATUS_UNSOLD;
        $allocation->consumerProduct->save();

        return $this->processingService->releaseLead($allocation->lead, $allocation->leadProcessor);
    }

    /**
     * Handles processing available companies and attempting delivery
     *
     * @param LeadProcessingAllocation $allocation
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @return bool
     */
    protected function processAvailableCompanies(
        LeadProcessingAllocation $allocation,
        array $ignoreCompanyIds = [],
        ?int $remainingLegs = null
    ): bool
    {
        try {
            $brsService = BestRevenueScenarioServiceFactory::make("odin");

            $this->logStatus("Obtaining ODIN BRS: {$allocation->consumer_product_id}", ["consumer_product_id" => $allocation->consumer_product_id]);
            $brs = $brsService->getBestRevenueScenario($allocation->consumer_product_id, $ignoreCompanyIds, $remainingLegs);
            $this->logStatus("Obtained ODIN BRS: {$allocation->consumer_product_id}", ["consumer_product_id" => $allocation->consumer_product_id]);

            if (empty($brs)) {
                $this->logStatus("No BRS campaigns found for {$allocation->consumer_product_id}", ["consumer_product_id" => $allocation->consumer_product_id]);

                return false;
            }

            $this->logStatus("Creating ODIN deliveries: {$allocation->consumer_product_id}", ["consumer_product_id" => $allocation->consumer_product_id]);

            $companiesAllEligible = $this->areCompaniesAllEligible(
                $allocation,
                $brs,
                $this->processingService->determineLeadAgeInDays($allocation->{LeadProcessingAllocation::RELATION_LEAD}) > 0
            );

            if ($companiesAllEligible) {
                $this->deliverLeadToCampaigns($allocation, $brs);
            } else {
                $this->logStatus("Companies are currently not eligible for lead: {$allocation->lead_id}", ["lead_id" => $allocation->lead_id, "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);
                $this->processNotAllCompaniesEligible($allocation, $brs, $ignoreCompanyIds, $remainingLegs);
            }

            return true;
        } catch (Throwable $e) {
            $errLocation = $e->getFile() . '. Line: ' . $e->getLine() . '. ';

            $this->logStatus(
                $errLocation . substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            $this->logStatus("Failed creating ODIN deliveries for {$allocation->consumer_product_id}", ["consumer_product_id" => $allocation->consumer_product_id]);

            return false;
        }
    }

    protected function deliverLeadToCampaigns(LeadProcessingAllocation $allocation, BestRevenueScenarioDataModel $brs): bool
    {
        if ($allocation->lead->status !== EloquentQuote::VALUE_STATUS_ALLOCATED) {
            $this->repository->updateLeadStatus($allocation->lead->uuid, EloquentQuote::VALUE_STATUS_ALLOCATED);
        }

        $processingUser = $allocation->leadProcessor()->withTrashed()->first()?->user;

        $results = $this->leadDeliveryService->saveAndDeliverQuoteCompany(
            $allocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
            $this->getSelectedQuoteCompanies($brs),
            $processingUser?->legacy_user_id, // job will go back and add admin user for logging purposes
            false,
            $allocation->processing_scenario
        );

        $this->logStatus(
            "Successfully delivered quote companies for lead: {$allocation->lead_id}",
            [
                "lead_id"               => $allocation->lead_id,
                "legacy_admin_response" => $results,
                "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
            ]
        );

        // set to delivered and add scenario
        $allocation->update([
            LeadProcessingAllocation::FIELD_DELIVERED           => LeadProcessingAllocation::DELIVERED,
            LeadProcessingAllocation::FIELD_DELIVER_AT          => Carbon::now(),
            LeadProcessingAllocation::FIELD_PROCESSING_SCENARIO => $allocation->processing_scenario
        ]);

        $this->consumerProductRepository->updateConsumerProductStatusByLegacyLeadIdIfExists($allocation->lead_id, ConsumerProduct::STATUS_ALLOCATED);

        $this->dispatcher->dispatch(new LeadAllocatedEvent(
            $allocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
            $allocation->lead_processor_id,
            $allocation->processing_scenario
        ));

        if (count($results["errors"]) > 0) {
            $this->logStatus(
                "Errors in delivery for lead: {$allocation->lead_id}",
                [
                    "lead_id" => $allocation->lead_id,
                    "consumer_product_id" => $allocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                ]
            );
        }

        // unlock lead
        // get processor regardless of deletion status
        $processor = $allocation->leadProcessor()->withTrashed()->first();
        return $this->processingService->releaseLead($allocation->lead, $processor);
    }

    /**
     * This function mimics what is done in the handleDeliveryButton function in AvailableLeadCampaignsForSale.vue
     *
     * @param BestRevenueScenarioDataModel $brs
     * @return array
     */
    protected function getSelectedQuoteCompanies(BestRevenueScenarioDataModel $brs): array
    {
        $collection = collect($brs->toArray()["campaigns"])->map(function ($item) use ($brs) {
            /** @var ProductCampaign $productCampaign */
            $productCampaign = ProductCampaign::query()->findOrFail($item["campaign_id"]);

            /** @var LeadCampaignSalesTypeConfiguration $configuration */
            $configuration = $productCampaign->legacyCampaign->leadSalesTypeConfigurations()
                ->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, $brs->toArray()["saleTypeId"])
                ->firstOrFail();

            return [
                'companyid'                        => $item["company_id"],
                'lead_sales_type_configuration_id' => $configuration->id,
                'chargeable'                       => 1, // we always want to charge for leads that are auto allocated to best revenue scenario
                'non_budget_premium_sale'          => $item['non_budget_premium_sale'] ?? false,
                'price'                            => $item[BRSCampaignPrices::UNRECTIFIED_PRICE] ?? null
            ];
        });

        return $collection->toArray();
    }

    protected function processNotAllCompaniesEligible(LeadProcessingAllocation $allocation, BestRevenueScenarioDataModel $brs, array $ignoreCompanyIds = [], ?int $remainingLegs = null): void
    {
        $lead = $allocation->{LeadProcessingAllocation::RELATION_LEAD};

        $isApptLead = $this->isAppointmentLead($allocation);

        if ($this->processingService->determineLeadAgeInDays($lead) < 2) {
            $delay = $this->getReattemptDelay($lead, $brs);

            $allocation->{LeadProcessingAllocation::FIELD_DELIVER_AT} = Carbon::now('UTC')->addSeconds($delay);
            $allocation->save();

            dispatch((new AllocateLeadJob($allocation, false, $ignoreCompanyIds, $remainingLegs))->delay($delay));

            $this->logStatus(
                "Scheduled another lead allocation attempt: {$allocation->lead_id}",
                [
                    "lead_id" => $allocation->lead_id,
                    "delay"   => $delay
                ]
            );
        }
        else if($isApptLead) {
            LeadUndersoldEvent::dispatch($allocation->lead->uuid, $allocation->processing_scenario);

            DispatchPubSubEvent::dispatch(
                EventCategory::LEADS,
                EventName::LEAD_UNDERSOLD,
                ["lead_id" => $allocation->lead_id, "initiator_id" => $allocation->leadProcessor?->id ?? 0]
            );

            $this->logStatus(
                "Appointment lead undersold: {$allocation->lead_id}",
                [
                    "lead_id" => $allocation->lead_id
                ]
            );

            $allocation->{LeadProcessingAllocation::FIELD_DELIVER_AT} = LeadProcessingAllocation::BLANK_DELIVERY;
            $allocation->save();
        }
    }

    protected function areCompaniesAllEligible(LeadProcessingAllocation $allocation, BestRevenueScenarioDataModel $brs, int $age): bool
    {
        if (!$this->processingService->timezoneOpenForLead($allocation->lead)) {
            return false;
        }

        if (($this->processingService->withinTimezoneOpeningThreshold($allocation)) && $this->withinRecencyThreshold($brs)) {
            return false;
        }

        return true;
    }

    /**
     * @param BestRevenueScenarioDataModel $brs
     * @return bool
     */
    protected function withinRecencyThreshold(BestRevenueScenarioDataModel $brs): bool
    {
        return $this->getArtificialDelay($brs) > 0;
    }

    /**
     * @param BestRevenueScenarioDataModel $brs
     * @return int
     */
    protected function getArtificialDelay(BestRevenueScenarioDataModel $brs): int
    {
        /** @var LeadProcessingConfiguration|null $leadConfig */
        $leadConfig = LeadProcessingConfiguration::query()->first();
        $recencyThreshold = $leadConfig ? $leadConfig->{LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS} : LeadProcessingConfiguration::LEAD_RECENCY_THRESHOLD_DEFAULT;

        $mostRecentTimestamp = $this->getMostRecentTimestampDelivered($brs);

        if ($mostRecentTimestamp) {
            $mostRecentTimestamp = Carbon::createFromTimestamp($mostRecentTimestamp);

            $delay = $recencyThreshold - (int) Carbon::now()->diffInSeconds($mostRecentTimestamp, true);

            if ($delay > 0) {
                return $delay;
            }
        }

        return 0;
    }

    /**
     * @param BestRevenueScenarioDataModel $brs
     * @return int|null
     * @throws BindingResolutionException
     */
    protected function getMostRecentTimestampDelivered(BestRevenueScenarioDataModel $brs): ?int
    {
        $quoteCompanyRepository = app()->make(QuoteCompanyRepository::class);
        $quoteCompanies = collect($brs->toArray()["campaigns"])->map(function ($item) use ($quoteCompanyRepository) {
            return $quoteCompanyRepository->getMostRecentQuoteCompanyByCompanyId($item["company_id"]);
        });

        return $quoteCompanies
            ->sortByDesc(EloquentQuoteCompany::TIMESTAMP_DELIVERED)
            ->pluck(EloquentQuoteCompany::TIMESTAMP_DELIVERED)
            ->first();
    }

    /**
     * Logs a status message of the job.
     *
     * This only runs if the shouldLog value is set to true.
     * @param string $message
     * @param array $payload
     * @return void
     * @see LeadProcessingService::$shouldLog
     *
     */
    protected function logStatus(string $message, array $payload = []): void
    {
        try {
            if ($this->shouldLog) {
                                RecordMonitoringLog::dispatch('{ODIN}: ' . $message, $payload);
            }
        } catch (Exception $e) {
        }
    }

    /**
     * @param EloquentQuote $lead
     * @param BestRevenueScenarioDataModel|null $brs
     * @return int
     */
    protected function getReattemptDelay(EloquentQuote $lead, ?BestRevenueScenarioDataModel $brs): int
    {
        if (!$this->processingService->timezoneOpenForLead($lead)) {
            //delay until tomorrow 2 PM
            ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->processingService->getTimezoneOffsetFromZipcode(
                $lead->{EloquentQuote::RELATION_ADDRESS}->{EloquentAddress::ZIP_CODE}
            );

            $dstAdjustment = $this->processingService->getDSTAdjustment($observingDST);

            $delay = Carbon::tomorrow($standardUTCOffset + $dstAdjustment)->addHours(14)->timestamp - Carbon::now($standardUTCOffset + $dstAdjustment)->timestamp;
        } else if ($brs) {
            $delay = $this->getArtificialDelay($brs);
        } else {
            $delay = LeadProcessingService::DELAY_2_HOURS;
        }

        return $delay;
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @return bool
     */
    public function isAppointmentLead(LeadProcessingAllocation $leadProcessingAllocation): bool
    {
        return $leadProcessingAllocation
                ->{LeadProcessingAllocation::RELATION_CONSUMER_PRODUCT}
                ->{ConsumerProduct::RELATION_APPOINTMENT_PROCESSING_ALLOCATIONS}
                ->where(AppointmentProcessingAllocation::FIELD_ALLOCATED, true)
                ->count() > 0;
    }
}
