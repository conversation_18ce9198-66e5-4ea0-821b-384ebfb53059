<?php

namespace App\Services\Odin;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\OriginDomain;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;

class WebsitesService
{
    const DOMAIN_LOGO_SRC = 'domain_logo_src';
    const DOMAIN_NAME = 'domain_name';
    const DOMAIN_URL = 'domain_url';

    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return Website::all();
    }

    /**
     * @param int $consumerProductId
     * @return array
     */
    #[ArrayShape([self::DOMAIN_NAME => "string", self::DOMAIN_URL => "string", self::DOMAIN_LOGO_SRC => "string"])]
    public static function getDomainInfo(int $consumerProductId): array
    {
        $consumerProduct = ConsumerProduct::query()
            ->where(ConsumerProduct::FIELD_ID, $consumerProductId)
            ->with([
                ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY,
                ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING.'.'.ConsumerProductTracking::RELATION_WEBSITE
            ])
            ->firstOrFail();

        $industry = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME};

        if($industry === IndustryEnum::ROOFING->value) {
            $website = Website::query()->where(Website::FIELD_URL, OriginDomain::ROOFING_CALCULATOR->value)->firstOrFail();

            $domainInfo = [
                self::DOMAIN_NAME => $website->{Website::FIELD_NAME},
                self::DOMAIN_URL => OriginDomain::ROOFING_CALCULATOR->value,
                self::DOMAIN_LOGO_SRC => '/images/RC-Logo.png'
            ];
        }
        else if($industry === IndustryEnum::SOLAR->value) {
            $website = OriginDomain::tryFrom($consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING}?->{ConsumerProductTracking::RELATION_WEBSITE}->{Website::FIELD_URL} ?? '');

            if(!empty($website)
            && $website === OriginDomain::SOLAR_ESTIMATE) {
                $domainInfo = [
                    self::DOMAIN_NAME => $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING}?->{ConsumerProductTracking::RELATION_WEBSITE}->{Website::FIELD_NAME} ?? "SolarEstimate",
                    self::DOMAIN_URL => OriginDomain::SOLAR_ESTIMATE->value,
                    self::DOMAIN_LOGO_SRC => '/images/SE-Logo.png'
                ];
            }
            else {
                $domainInfo = [
                    self::DOMAIN_NAME => "SolarReviews",
                    self::DOMAIN_URL => OriginDomain::SOLAR_REVIEWS->value,
                    self::DOMAIN_LOGO_SRC => '/images/SR-Logo.png'
                ];
            }
        }
        else {
            $domainInfo = [
                self::DOMAIN_NAME => "FIXR",
                self::DOMAIN_URL => OriginDomain::FIXR->value,
                self::DOMAIN_LOGO_SRC => '/images/FIXR_Logo.png'
            ];
        }

        return $domainInfo;
    }
}
