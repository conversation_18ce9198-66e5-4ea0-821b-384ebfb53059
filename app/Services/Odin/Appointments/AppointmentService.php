<?php

namespace App\Services\Odin\Appointments;

use App\Abstracts\ProductPricing\BestRevenueScenarioServiceAbstract;
use App\Builders\Appointments\AppointmentSearchBuilder;
use App\DataModels\Odin\Prices\BestRevenueScenarioDataModel;
use App\DataModels\Odin\Prices\BidPriceTable;
use App\DataModels\Odin\Prices\FloorPriceTable;
use App\Enums\Odin\AppointmentCancellationReason;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\ProductAssignmentAffectRejectionPercentage;
use App\Enums\Odin\ProductAssignmentStatus;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes as SaleTypesEnum;
use App\Models\SaleType as SaleTypeModel;
use App\Enums\Odin\StateAbbreviation;
use App\Enums\Odin\TcpaService;
use App\Enums\RejectionReasons;
use App\Enums\Timezone;
use App\Events\ProductAssignment\ProductRejectedEvent;
use App\Exceptions\CustomMessageException;
use App\Jobs\Appointments\SendCancellationNoticeToCompanyJob;
use App\Jobs\Appointments\SendCancellationNoticeToConsumerJob;
use App\Jobs\RecordMonitoringLog;
use App\Mail\Appointments\NotifyAppointmentCompany;
use App\Mail\Appointments\NotifyAppointmentConsumer;
use App\Mail\Appointments\NotifyCancellationCompany;
use App\Mail\Appointments\NotifyCancellationConsumer;
use App\Models\AppointmentDelivery;
use App\Models\AppointmentProcessingAllocation;
use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingReservedLead;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignDeliveryMethod;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\LeadDeliveryMethod;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Models\User;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Legacy\DatabaseLocationRepository;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\LogMonitoringRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Repositories\Odin\ProductRejectionRepository;
use App\Repositories\Odin\ProductRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\DatabaseHelperService;
use App\Services\Delivery\Email;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\ProductPricing\BestRevenueScenario\BestRevenueScenarioServiceFactory;
use App\Services\ProductPricing\ProductPricingServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Mail\Mailable;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use Throwable;

class AppointmentService
{
    const APPOINTMENT_KEY = 'k';
    const APPOINTMENT_CODE = 'c';
    const APPOINTMENT_INDUSTRY = 'i';

    const ALIAS_WITHIN_REJECTION_WINDOW = 'within_rejection_window';

    const FUNC_RET_APPT_DELIVERY = 'apptDelivery';
    const FUNC_RET_PRODUCT_ASSIGNMENT = 'productAssignment';
    const FUNC_RET_CONSUMER_PRODUCT = 'consumerProduct';
    const FUNC_RET_INDUSTRY = 'industry';
    const FUNC_RET_ADDRESS = 'address';
    const FUNC_RET_COUNTY_KEY = 'countyKey';
    const FUNC_RET_PROPERTY_TYPE = 'propertyType';
    const FUNC_RET_LEGACY_LEAD = 'legacyLead';
    const FUNC_RET_QUALITY_TIER = 'qualityTier';
    const FUNC_RET_LEAD_PROCESSOR_USER = 'leadProcessorUser';
    const FUNC_RET_LEAD_PROCESSING_SCENARIO = 'leadProcessingScenario';
    const FUNC_RET_COMPANY_USER = 'companyUser';
    const FUNC_RET_APPOINTMENT_DATETIME = 'appointmentDateTime';
    const FUNC_RET_COMPANY_NAME = 'companyName';
    const FUNC_RET_LEAD_CONSUMER_PRODUCT = 'leadConsumerProduct';
    const FUNC_RET_LEAD_SALE_TYPE = 'leadSaleType';

    protected bool $isProd;
    protected ?string $twilioFromNumber;

    public function __construct(
        protected readonly TwilioCommunicationService $twilioCommunicationService,
        protected readonly ProductRejectionRepository $productRejectionRepository,
        protected readonly DatabaseLocationRepository $databaseLocationRepository,
        protected readonly LeadDeliveryService $leadDeliveryService,
        protected readonly ProductProcessingRepository $productProcessingRepository,
        protected readonly ProductAssignmentRepository $productAssignmentRepository,
        protected readonly ProductRepository $productRepository,
        protected readonly AppointmentCalendarIntegrationService $appointmentCalendarIntegrationService,
        protected readonly LeadProcessingRepository $leadProcessingRepository,
        protected readonly ConsumerProductRepository $consumerProductRepository
    ) {
        $this->isProd = App::environment('production');
        $this->twilioFromNumber = config('services.twilio.from_phone_number');
    }

    /**
     * @param int $companyId
     * @param int $appointmentId
     * @param int $campaignId
     * @param string $status
     * @param int $invoiceId
     * @param string $contactSearch
     * @param string $addressSearch
     * @param string $stateAbbr
     * @param string $city
     * @param string $zip
     * @param int $startTimestamp
     * @param int $endTimestamp
     * @param QualityTier|null $appointmentCategory
     *
     * @return Builder
     */
    public function getBaseQuery(
        int          $companyId,
        int          $appointmentId = 0,
        int          $campaignId = 0,
        string       $status = '',
        int          $invoiceId = 0,
        string       $contactSearch = '',
        string       $addressSearch = '',
        string       $stateAbbr = '',
        string       $city = '',
        string       $zip = '',
        int          $startTimestamp = 0,
        int          $endTimestamp = 0,
        ?QualityTier $appointmentCategory = null
    ): Builder
    {
        $queryBuilder = AppointmentSearchBuilder::newQuery()
            ->forCompany($companyId)
            ->forDeliveryStatus(true);

        if (!empty($appointmentId))
            $queryBuilder->forAppointment($appointmentId);
        if (!empty($campaignId))
            $queryBuilder->forCampaign($campaignId);
        if (!empty($status))
            $queryBuilder->forStatus($status);
        if (!empty($invoiceId))
            $queryBuilder->forInvoice($invoiceId);
        if (!empty($contactSearch))
            $queryBuilder->forContact($contactSearch);
        if (!empty($addressSearch))
            $queryBuilder->forAddress($addressSearch);
        if (!empty($stateAbbr))
            $queryBuilder->forStateAbbr($stateAbbr);
        if (!empty($city))
            $queryBuilder->forCity($city);
        if (!empty($zip))
            $queryBuilder->forZipCode($zip);
        if (!empty($startTimestamp))
            $queryBuilder->forStartTimestamp($startTimestamp);
        if (!empty($endTimestamp))
            $queryBuilder->forEndTimestamp($endTimestamp);
        if (!empty($appointmentCategory))
            $queryBuilder->forAppointmentCategory($appointmentCategory);

        return $queryBuilder->getQuery();
    }

    /**
     * @param int $companyId
     * @param int $appointmentId
     * @param int $campaignId
     * @param string $status
     * @param int $invoiceId
     * @param string $contactSearch
     * @param string $addressSearch
     * @param string $stateAbbr
     * @param string $city
     * @param string $zip
     * @param int $startTimestamp
     * @param int $endTimestamp
     * @param QualityTier|null $appointmentCategory
     * @param int $perPage
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function getCompanyAppointmentsPaginated(
        int $companyId,
        int $appointmentId = 0,
        int $campaignId = 0,
        string $status = '',
        int $invoiceId = 0,
        string $contactSearch = '',
        string $addressSearch = '',
        string $stateAbbr = '',
        string $city = '',
        string $zip = '',
        int $startTimestamp = 0,
        int $endTimestamp = 0,
        ?QualityTier $appointmentCategory = null,
        int $perPage = 10
    ): LengthAwarePaginator
    {
        return $this->getBaseQuery(
            $companyId,
            $appointmentId,
            $campaignId,
            $status,
            $invoiceId,
            $contactSearch,
            $addressSearch,
            $stateAbbr,
            $city,
            $zip,
            $startTimestamp,
            $endTimestamp,
            $appointmentCategory,
        )->selectRaw(sprintf(
            "%s, %s IS TRUE AND %s IS TRUE AND UNIX_TIMESTAMP(%s) > UNIX_TIMESTAMP() AS %s",
            ProductAssignment::TABLE . ".*",
            ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE,
            ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED,
            ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_REJECTION_EXPIRY,
            self::ALIAS_WITHIN_REJECTION_WINDOW
        ))
            ->with([
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER,
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_ADDRESS,
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_APPOINTMENT,
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_SERVICE_PRODUCT . '.' . ServiceProduct::RELATION_SERVICE,
                ProductAssignment::RELATION_PRODUCT_REJECTIONS,
                ProductAssignment::RELATION_PRODUCT_CANCELLATIONS,
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER_PRODUCT_TCPA_RECORD => function ($with) {
                    $with->where(ConsumerProductTcpaRecord::FIELD_TCPA_SERVICE_TYPE, TcpaService::WATCHDOG->value);
                }
            ])
            ->orderBy(ProductAssignment::FIELD_ID, 'desc')
            ->paginate($perPage);
    }

    /**
     * @param int $companyId
     * @param int $appointmentId
     * @param int $campaignId
     * @param string $status
     * @param int $invoiceId
     * @param string $contactSearch
     * @param string $addressSearch
     * @param string $stateAbbr
     * @param string $city
     * @param string $zip
     * @param int $startTimestamp
     * @param int $endTimestamp
     * @param QualityTier|null $appointmentCategory
     *
     * @return int|mixed
     */
    public function getTotalSpend(
        int $companyId,
        int $appointmentId = 0,
        int $campaignId = 0,
        string $status = '',
        int $invoiceId = 0,
        string $contactSearch = '',
        string $addressSearch = '',
        string $stateAbbr = '',
        string $city = '',
        string $zip = '',
        int $startTimestamp = 0,
        int $endTimestamp = 0,
        ?QualityTier $appointmentCategory = null
    ): mixed
    {
        return $this->getBaseQuery(
            $companyId,
            $appointmentId,
            $campaignId,
            $status,
            $invoiceId,
            $contactSearch,
            $addressSearch,
            $stateAbbr,
            $city,
            $zip,
            $startTimestamp,
            $endTimestamp,
            $appointmentCategory,
        )->sum(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST);
    }

    /**
     * @param string $linkKey
     * @param string $linkCode
     * @param string $industry
     * @return string
     */
    protected function getConsumerAppointmentUrl(string $linkKey, string $linkCode, string $industry): string
    {
        $params = http_build_query([
            self::APPOINTMENT_KEY => $linkKey,
            self::APPOINTMENT_CODE => $linkCode,
            self::APPOINTMENT_INDUSTRY => $industry
        ]);

        return config('app.solarreviews_domain.appointments')."/ap?{$params}";
    }

    /**
     * @param int $apptProductAssignmentId
     * @param string $industry
     * @return string
     */
    protected function getDashboardAppointmentUrl(int $apptProductAssignmentId, string $industry): string
    {
        if($industry === IndustryEnum::ROOFING->value) {
            $detailsUrl = config('app.dashboard.roofing_url');
        }
        else if($industry === IndustryEnum::SOLAR->value) {
            $detailsUrl = config('app.dashboard.url');
        }
        else {
            $detailsUrl = config('app.dashboard.fixr_url');
        }

        $detailsUrl .= "/appointments?appointment_id={$apptProductAssignmentId}";

        return $detailsUrl;
    }

    /**
     * @return string
     * @throws Exception
     */
    protected function generateCode(): string
    {
        $code = '';
        for($i = 0; $i < 6; $i++) {
            $code .= random_int(0, 9);
        }

        return $code;
    }

    /**
     * @return string
     * @throws Exception
     */
    protected function generateKey(): string
    {
        return Uuid::uuid4();
    }

    /**
     * @param int $leadConsumerProductId
     * @return bool
     */
    public function deleteAppointmentsByLeadConsumerProductId(int $leadConsumerProductId): bool
    {
        $productAppointments = ProductAppointment::query()->where(ProductAppointment::LEAD_CONSUMER_PRODUCT_ID, $leadConsumerProductId)->get();

        //Delete one by one so that the observer deleting method can pick it up
        //Laravel does not fire deleting event for mass deletes
        foreach($productAppointments as $productAppointment) {
            $productAppointment->delete();
        }

        return true;
    }

    /**
     * @param int $productCampaignId
     * @param int $productAssignmentId
     * @return AppointmentDelivery
     * @throws Exception
     */
    public function createAppointmentDelivery(int $productCampaignId, int $productAssignmentId): AppointmentDelivery
    {
        return AppointmentDelivery::create([
            AppointmentDelivery::FIELD_PRODUCT_CAMPAIGN_ID => $productCampaignId,
            AppointmentDelivery::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignmentId,
            AppointmentDelivery::FIELD_CONSUMER_TOKEN => $this->generateKey(),
            AppointmentDelivery::FIELD_CONSUMER_CODE => $this->generateCode(),
            AppointmentDelivery::FIELD_ATTEMPTS => 0,
            AppointmentDelivery::FIELD_NEXT_ATTEMPT_TIMESTAMP => Carbon::now()->timestamp
        ]);
    }

    /**
     * @param int $chunk
     * @return bool
     * @throws Throwable
     */
    public function deliverAppointments(int $chunk = 250): bool
    {
        try {
            $nextDeliveryAttemptBufferSecs = ((int) config('sales.appointments.next_delivery_attempt_buffer_minutes')) * Carbon::SECONDS_PER_MINUTE;
            $maxDeliveryAttempts = (int) config('sales.appointments.max_delivery_attempts');

            $handleDeliveryFailure = function(&$apptDelivery) use ($maxDeliveryAttempts, $nextDeliveryAttemptBufferSecs) {
                $apptDelivery->{AppointmentDelivery::FIELD_ATTEMPTS} += 1;

                if($apptDelivery->{AppointmentDelivery::FIELD_ATTEMPTS} < $maxDeliveryAttempts) {
                    $apptDelivery->{AppointmentDelivery::FIELD_NEXT_ATTEMPT_TIMESTAMP} = Carbon::now()->timestamp + $nextDeliveryAttemptBufferSecs;
                }
                else {
                    $apptDelivery->{AppointmentDelivery::FIELD_NEXT_ATTEMPT_TIMESTAMP} = 0;
                }

                $apptDelivery->save();
            };

            AppointmentDelivery::query()
                ->with([
                    AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT,
                    AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_LEGACY_CAMPAIGN => function($with) {
                        $with->select([
                            LeadCampaign::ID
                        ]);
                    },
                    AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_LEGACY_CAMPAIGN.'.'.LeadCampaign::RELATION_LEAD_CAMPAIGN_DELIVERY_METHODS.'.'.LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD.'.'.LeadDeliveryMethod::RELATION_CONTACT => function($with) {
                        $with
                            ->where(EloquentCompanyContact::FIELD_STATUS, EloquentCompanyContact::STATUS_ACTIVE)
                            ->select([
                                EloquentCompanyContact::FIELD_CONTACT_ID,
                                EloquentCompanyContact::FIELD_COMPANY_ID,
                                EloquentCompanyContact::FIELD_PHONE,
                                EloquentCompanyContact::FIELD_MOBILE,
                                EloquentCompanyContact::FIELD_EMAIL,
                                EloquentCompanyContact::FIELD_FIRST_NAME,
                                EloquentCompanyContact::FIELD_LAST_NAME
                            ]);
                    }
                ])
                ->whereNull(AppointmentDelivery::FIELD_COMPANY_CAMPAIGN_ID)
                ->where(function($where) {
                    $where
                        ->where(AppointmentDelivery::FIELD_CONSUMER_DELIVERED, false)
                        ->orWhere(AppointmentDelivery::FIELD_COMPANY_DELIVERED, false);
                })
                ->where(AppointmentDelivery::FIELD_ATTEMPTS, '<', $maxDeliveryAttempts)
                ->where(AppointmentDelivery::FIELD_NEXT_ATTEMPT_TIMESTAMP, '<=', Carbon::now()->timestamp)
                ->orderBy(AppointmentDelivery::CREATED_AT, 'asc')
                ->chunk($chunk, function($apptDeliveries) use ($handleDeliveryFailure) {
                    $companiesData = Company::query()
                        ->whereIn(Company::FIELD_ID, $apptDeliveries->map(fn($ad) => $ad->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::FIELD_COMPANY_ID}))
                        ->select([
                            Company::FIELD_ID,
                            Company::FIELD_NAME
                        ])
                        ->get()
                        ->keyBy(Company::FIELD_ID);

                    $consumerProductsData = ConsumerProduct::query()
                        ->whereIn(ConsumerProduct::FIELD_ID, $apptDeliveries->map(fn($ad) => $ad->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID}))
                        ->with([
                            ConsumerProduct::RELATION_CONSUMER,
                            ConsumerProduct::RELATION_ADDRESS,
                            ConsumerProduct::RELATION_APPOINTMENT,
                            ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY
                        ])
                        ->get()
                        ->keyBy(ConsumerProduct::FIELD_ID);

                    foreach($apptDeliveries as $apptDelivery) {
                        try {
                            DB::beginTransaction();

                            $consumerProduct = $consumerProductsData->get($apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID});
                            $campaign = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN};
                            $productAppointment = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT};

                            $apptDateTime = Carbon::createFromFormat(
                                "Y-m-d H:i:s",
                                $productAppointment->{ProductAppointment::APPOINTMENT},
                                (int) $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_UTC}
                            );

                            $deliveryRes = $this->deliverAppointmentToConsumerAndCompany(
                                $apptDelivery,
                                $consumerProduct,
                                $campaign,
                                $apptDateTime,
                                $companiesData->get($apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::FIELD_COMPANY_ID})->{Company::FIELD_NAME}
                            );

                            if($deliveryRes) {
                                $apptDelivery->{AppointmentDelivery::FIELD_ATTEMPTS} += 1;
                                $apptDelivery->save();

                                $this->markAppointmentDelivered(
                                    $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT},
                                    $productAppointment,
                                    $consumerProduct,
                                    $campaign,
                                    $apptDateTime
                                );
                            }
                            else {
                                $handleDeliveryFailure($apptDelivery);
                            }

                            DB::commit();
                        }
                        catch(Throwable $e) {
                            DB::rollBack();

                            $handleDeliveryFailure($apptDelivery);

                            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

                            self::writeAppointmentLog(
                                "Delivery err: ".$errLocation.substr($e->getMessage(), 0, 255),
                                [
                                    "appointment_delivery_id" => $apptDelivery?->{AppointmentDelivery::FIELD_ID} ?? 0,
                                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                                ]
                            );

                            logger()->error($e);
                        }
                    }
                });

            return true;
        }
        catch(Throwable $e) {
            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                __METHOD__.': '.$errLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($e);

            throw $e;
        }
    }

    /**
     * @param AppointmentDelivery $apptDelivery
     * @param ConsumerProduct $consumerProduct
     * @param ProductCampaign $campaign
     * @param Carbon $apptDateTime
     * @param string $companyName
     * @return bool
     * @throws Exception
     */
    private function deliverAppointmentToConsumerAndCompany(
        AppointmentDelivery &$apptDelivery,
        ConsumerProduct $consumerProduct,
        ProductCampaign $campaign,
        Carbon $apptDateTime,
        string $companyName
    ): bool
    {
        $industry = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME};

        $apptType = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT_TYPE};

        $companyNotified = (bool) $apptDelivery->{AppointmentDelivery::FIELD_COMPANY_DELIVERED};
        $consumerNotified = (bool) $apptDelivery->{AppointmentDelivery::FIELD_CONSUMER_DELIVERED};

        if(!$companyNotified) {
            $companyNotified = $this->deliverConsumerInformationToCompany(
                $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT},
                $consumerProduct,
                $consumerProduct->{ConsumerProduct::RELATION_ADDRESS},
                $consumerProduct->{ConsumerProduct::RELATION_CONSUMER},
                $industry,
                $companyName,
                $campaign,
                $apptDateTime,
                $apptType
            );

            $apptDelivery->{AppointmentDelivery::FIELD_COMPANY_DELIVERED} = $companyNotified;

            $apptDelivery->save();
        }

        if(!$consumerNotified) {
            $consumerNotified = $this->notifyConsumerOfAppointment(
                $consumerProduct,
                $consumerProduct->{ConsumerProduct::RELATION_ADDRESS},
                $consumerProduct->{ConsumerProduct::RELATION_CONSUMER},
                $industry,
                $companyName,
                $apptDelivery->{AppointmentDelivery::FIELD_CONSUMER_TOKEN},
                $apptDelivery->{AppointmentDelivery::FIELD_CONSUMER_CODE},
                $apptDateTime,
                $apptType
            );

            $apptDelivery->{AppointmentDelivery::FIELD_CONSUMER_DELIVERED} = $consumerNotified;

            $apptDelivery->save();
        }

        return $companyNotified && $consumerNotified;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param ProductAppointment $productAppointment
     * @param ConsumerProduct $consumerProduct
     * @param ProductCampaign $campaign
     * @param Carbon $apptDateTime
     * @return bool
     * @throws Exception
     */
    private function markAppointmentDelivered(
        ProductAssignment &$productAssignment,
        ProductAppointment $productAppointment,
        ConsumerProduct $consumerProduct,
        ProductCampaign $campaign,
        Carbon $apptDateTime
    ): bool
    {
        $productAssignment->{ProductAssignment::FIELD_DELIVERED} = true;
        $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT} = Carbon::now();
        $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY} = Carbon::createFromTimestamp($this->productRejectionRepository->getRejectionExpiry(ProductEnum::APPOINTMENT, $apptDateTime->timestamp));

        $productAssignment->save();

        $processingAllocation = AppointmentProcessingAllocation::query()
            ->where(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->with(AppointmentProcessingAllocation::RELATION_LEAD_PROCESSOR.'.'.LeadProcessor::RELATION_USER)
            ->firstOrFail();

        $processingAllocation->{AppointmentProcessingAllocation::FIELD_DELIVERED} = true;

        $processingAllocation->save();

        LeadProcessingReservedLead::query()
            ->where(LeadProcessingReservedLead::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::FIELD_ID})
            ->delete();

        $leadReference = $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::RELATION_LEGACY_LEAD}->{EloquentQuote::REFERENCE};

        if(!empty($productAppointment->{ProductAppointment::ORIGINAL_APPOINTMENT_ID})) {
            $originalProductAssignment = $productAppointment
                ->{ProductAppointment::RELATION_ORIGINAL_APPOINTMENT}
                ->{ProductAppointment::RELATION_CONSUMER_PRODUCT}
                ->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}
                ->first();

            $productCancellation = $originalProductAssignment->{ProductAssignment::RELATION_PRODUCT_CANCELLATIONS}->first();

            $this->leadDeliveryService->saveRescheduledAppointmentQuoteCompany(
                $originalProductAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                $processingAllocation->{AppointmentProcessingAllocation::RELATION_LEAD_PROCESSOR}->{LeadProcessor::RELATION_USER}->{User::FIELD_LEGACY_USER_ID},
                $productCancellation->{ProductCancellation::FIELD_REASON}->value,
                $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT}->timestamp,
                $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}->timestamp,
                true
            );
        }
        else {
            $this->leadDeliveryService->saveQuoteCompanyForAppointment(
                $leadReference,
                $productAssignment->{ProductAssignment::RELATION_COMPANY}->{Company::RELATION_LEGACY_COMPANY}->{EloquentCompany::REFERENCE},
                $campaign->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID},
                $productAssignment->{ProductAssignment::FIELD_COST},
                EloquentQuoteCompany::VALUE_SOLD_STATUS_SOLD,
                EloquentQuoteCompany::VALUE_CHARGE_STATUS_INITIAL,
                $productAssignment->{ProductAssignment::FIELD_CHARGEABLE},
                $productAssignment->{ProductAssignment::FIELD_DELIVERED},
                $productAssignment->{ProductAssignment::FIELD_EXCLUDE_BUDGET} === 0,
                $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}->timestamp,
                $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT}->timestamp,
                0,
                $processingAllocation->{AppointmentProcessingAllocation::RELATION_LEAD_PROCESSOR}?->{LeadProcessor::RELATION_USER}?->{User::FIELD_LEGACY_USER_ID} ?? 0,
                SaleTypesEnum::mapSaleTypeToLegacyId(SaleTypesEnum::mapAllocationsToVerifiedType($productAssignment->{ProductAssignment::FIELD_SALE_TYPE_ID})),
                $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                true
            );
        }

        return true;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param ConsumerProduct $consumerProduct
     * @param Address $address
     * @param Consumer $consumer
     * @param string $industry
     * @param string $companyName
     * @param ProductCampaign $campaign
     * @param Carbon $appointmentDateTime
     * @param QualityTier $appointmentType
     * @return bool
     * @throws Exception
     */
    public function deliverConsumerInformationToCompany(
        ProductAssignment $productAssignment,
        ConsumerProduct $consumerProduct,
        Address $address,
        Consumer $consumer,
        string $industry,
        string $companyName,
        ProductCampaign $campaign,
        Carbon $appointmentDateTime,
        QualityTier $appointmentType
    ): bool
    {
        if($consumerProduct->{ConsumerProduct::FIELD_ADDRESS_ID} !== $address->{Address::FIELD_ID}
        || $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_ID} !== $consumer->{Consumer::FIELD_ID}) {
            throw new Exception(__METHOD__.": Consumer and/or Address doesn't belong to the consumer product");
        }

        $dateTimeFormatted = $appointmentDateTime->format('m/d/Y g:i A');

        $companyName = $this->trimCompanyName($companyName);

        $detailsUrl = $this->getDashboardAppointmentUrl($productAssignment->{ProductAssignment::FIELD_ID}, $industry);

        $textMsg = sprintf(
            "%s is scheduled for an %s %s appointment on %s with:\n%s\n%s\n%s\n%s",
            $companyName,
            strtolower($appointmentType->value),
            $industry,
            $dateTimeFormatted,
            $consumer->getFullName(),
            $consumer->{Consumer::FIELD_PHONE},
            $consumer->{Consumer::FIELD_EMAIL},
            $address->getFullAddress()
        );

        return  $this->deliverCommunicationsToCampaign(
            $campaign,
            $textMsg,
            function($campaignDeliveryMethod) use ($consumerProduct, $address, $consumer, $industry, $companyName, $dateTimeFormatted, $detailsUrl, $appointmentType) {
                return new NotifyAppointmentCompany(
                    $consumerProduct,
                    $address,
                    $consumer,
                    $industry,
                    $companyName,
                    $campaignDeliveryMethod->{LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD}?->{LeadDeliveryMethod::RELATION_CONTACT}?->name ?? "",
                    $dateTimeFormatted,
                    $appointmentType,
                    $detailsUrl
                );
            }
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Address $address
     * @param Consumer $consumer
     * @param string $industry
     * @param string $companyName
     * @param string $consumerToken
     * @param string $consumerCode
     * @param Carbon $appointmentDateTime
     * @param QualityTier $appointmentType
     * @return bool
     * @throws Exception
     */
    public function notifyConsumerOfAppointment(
        ConsumerProduct $consumerProduct,
        Address $address,
        Consumer $consumer,
        string $industry,
        string $companyName,
        string $consumerToken,
        string $consumerCode,
        Carbon $appointmentDateTime,
        QualityTier $appointmentType
    ): bool
    {
        if($consumerProduct->{ConsumerProduct::FIELD_ADDRESS_ID} !== $address->{Address::FIELD_ID}
        || $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_ID} !== $consumer->{Consumer::FIELD_ID}) {
            throw new Exception(__METHOD__.": Consumer and/or Address doesn't belong to the consumer product");
        }

        $companyName = $this->trimCompanyName($companyName);

        $dateTimeFormatted = $appointmentDateTime->format('m/d/Y g:i A');

        $detailsUrl = $this->getConsumerAppointmentUrl($consumerToken, $consumerCode, $industry);

        if(!empty($consumer->{Consumer::FIELD_PHONE}) && $this->isProd) {
            $toPhone = $consumer->{Consumer::FIELD_PHONE};

            $senderName = match($industry) {
                IndustryEnum::ROOFING->value=> "RoofingCalc",
                IndustryEnum::SOLAR->value => "SolarReviews",
                default => 'FIXR'
            };

            $message = sprintf(
                "%s: A %s representative will %s %s to discuss %s options\nView/Cancel Appointment: %s",
                $senderName,
                $companyName,
                $appointmentType === QualityTier::ONLINE ? "schedule an online meeting for": "visit your property on",
                $dateTimeFormatted,
                strtolower($industry),
                $detailsUrl
            );

            $this->twilioCommunicationService->sendSMS(
                $this->twilioFromNumber,
                $toPhone,
                $message
            );
        }

        if(!empty($consumer->{Consumer::FIELD_EMAIL})) {
            Email::send(
                $this->isProd ? $consumer->{Consumer::FIELD_EMAIL} : config('app.outgoing_communication.test_email'),
                new NotifyAppointmentConsumer(
                    $consumerProduct,
                    $address,
                    $consumer,
                    $companyName,
                    $dateTimeFormatted,
                    $appointmentType,
                    $industry,
                    $detailsUrl
                )
            );
        }

        return true;
    }

    /**
     * @param int $apptConsumerProductId
     * @param int $leadConsumerProductId
     * @return bool
     */
    public function markAppointmentConsumerProductAndLeadAllocated(int $apptConsumerProductId, int $leadConsumerProductId): bool
    {
        ConsumerProduct::query()
            ->whereIn(ConsumerProduct::FIELD_ID, [$apptConsumerProductId, $leadConsumerProductId])
            ->update([
                ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_ALLOCATED
            ]);

        $consumer = ConsumerProduct::query()
            ->where(ConsumerProduct::FIELD_ID, $apptConsumerProductId)
            ->with(ConsumerProduct::RELATION_CONSUMER)
            ->firstOrFail()
            ->{ConsumerProduct::RELATION_CONSUMER};

        $legacyLeadReference = '';
        if($consumer->{Consumer::FIELD_LEGACY_ID}) {
            $legacyLeadReference = $consumer->{Consumer::RELATION_LEGACY_LEAD}?->{EloquentQuote::REFERENCE} ?? '';
        }

        app(LeadProcessingRepository::class)->updateLeadStatus($legacyLeadReference, EloquentQuote::VALUE_STATUS_ALLOCATED);

        return true;
    }

    /**
     * @param string $apptKey
     * @param string $apptCode
     * @param AppointmentCancellationReason $cancellationReason
     * @param string $cancellationNote
     * @param int $rescheduledDateTimestamp
     * @return bool
     * @throws CustomMessageException
     * @throws Exception|Throwable
     */
    public function cancelOrRescheduleConsumerAppointment(
        string $apptKey,
        string $apptCode,
        AppointmentCancellationReason $cancellationReason,
        string $cancellationNote,
        int $rescheduledDateTimestamp
    ): bool
    {
        try {
            if($cancellationReason === AppointmentCancellationReason::OTHER
            || $cancellationReason === AppointmentCancellationReason::NOT_INTERESTED) {
                $success = $this->cancelConsumerAppointment(
                    $apptKey,
                    $apptCode,
                    $cancellationReason,
                    $cancellationNote
                );
            }
            else if($cancellationReason === AppointmentCancellationReason::RESCHEDULED) {
                $success = $this->rescheduleAppointment(
                    $apptKey,
                    $apptCode,
                    $cancellationReason,
                    $cancellationNote,
                    $rescheduledDateTimestamp
                );
            }
            else {
                throw new CustomMessageException("Invalid cancellation reason");
            }
        }
        catch(Throwable $e) {
            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }

        return $success;
    }

    /**
     * @param string $apptKey
     * @param string $apptCode
     * @param AppointmentCancellationReason $cancellationReason
     * @param string $cancellationNote
     * @return bool
     * @throws CustomMessageException
     * @throws Throwable
     */
    public function cancelConsumerAppointment(
        string $apptKey,
        string $apptCode,
        AppointmentCancellationReason $cancellationReason,
        string $cancellationNote
    ): bool
    {
        try {
            extract($this->getAppointmentDeliveryInfoForCancellation($apptKey, $apptCode));

            $leadPrice = $this->getAppointmentLeadFloorPriceForCampaign(
                $leadConsumerProduct->{ConsumerProduct::FIELD_SERVICE_PRODUCT_ID},
                $industry,
                $propertyType,
                $qualityTier,
                StateAbbreviation::from(strtoupper($address->{Address::FIELD_STATE})),
                $leadSaleType,
                $countyKey
            );

            $rejectionExpiry = $this->productRejectionRepository->getRejectionExpiry();

            $leadQuoteCompanyId = $this->leadDeliveryService->demoteAppointmentQuoteCompanyToLead(
                $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                $leadPrice,
                SaleTypesEnum::mapSaleTypeToLegacyId($leadSaleType),
                $productAssignment->{ProductAssignment::RELATION_CAMPAIGN}->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID},
                $leadProcessorUser->{User::FIELD_LEGACY_USER_ID},
                $leadProcessingScenario,
                true,
                false,
                $rejectionExpiry,
                ''
            );

            DB::beginTransaction();

            $leadProductAssignment = $this->demoteAppointmentAssignmentToLead(
                $productAssignment,
                $leadPrice,
                true,
                $rejectionExpiry,
                $leadQuoteCompanyId,
                $leadSaleType
            );

            if(empty($leadProductAssignment)) {
                throw new CustomMessageException('Error cancelling appointment');
            }

            $this->createAppointmentProductCancellation(
                $productAssignment->{ProductAssignment::FIELD_ID},
                $cancellationReason,
                $cancellationNote
            );

            $this->markAppointmentProcessingAllocationRejectedCancelled($consumerProduct);

            $companyName = $productAssignment->{ProductAssignment::RELATION_COMPANY}->{Company::FIELD_NAME};

            $appointment = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT};

            $tzOffset = (string) $address->{Address::FIELD_UTC};

            $appointmentDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $appointment, $tzOffset)->format("m/d/Y g:i A");

            SendCancellationNoticeToConsumerJob::dispatch(
                $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT},
                $companyName,
                $industry,
                $appointmentDateTime
            )->afterCommit();

            SendCancellationNoticeToCompanyJob::dispatch(
                $consumerProduct,
                $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN},
                $productAssignment,
                $companyName,
                $industry,
                $appointmentDateTime
            )->afterCommit();

            DB::commit();

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param string $apptKey
     * @param string $apptCode
     * @param AppointmentCancellationReason $cancellationReason
     * @param string $cancellationNote
     * @param int $rescheduledDateTimestamp
     * @return bool
     * @throws Exception|Throwable
     */
    public function rescheduleAppointment(
        string $apptKey,
        string $apptCode,
        AppointmentCancellationReason $cancellationReason,
        string $cancellationNote,
        int $rescheduledDateTimestamp
    ): bool
    {
        try {
            extract($this->getAppointmentDeliveryInfoForCancellation($apptKey, $apptCode));

            $priorAppt = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT};

            if(!empty($priorAppt->{ProductAppointment::ORIGINAL_APPOINTMENT_ID})) {
                throw new CustomMessageException("Appointment was already rescheduled");
            }

            $rescheduledQuoteCompanyId = $this->leadDeliveryService->saveRescheduledAppointmentQuoteCompany(
                $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                0,
                $leadProcessorUser->{User::FIELD_LEGACY_USER_ID},
                $cancellationReason->value,
                0,
                0
            );

            DB::beginTransaction();

            $rescheduledAppt = $this->createRescheduledAppointmentAndAssociatedConsumerProduct(
                $priorAppt->{ProductAppointment::ID},
                $priorAppt->{ProductAppointment::APPOINTMENT_TYPE},
                Carbon::createFromTimestamp($rescheduledDateTimestamp)->setTimezone((string) ($address->{Address::FIELD_UTC} + (int) Timezone::isDST())),
                $priorAppt->{ProductAppointment::LEGACY_ID},
                $priorAppt->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID}
            );

            $rescheduledProductAssignment = $this->createRescheduledAppointmentProductAssignment($productAssignment, $rescheduledAppt, $rescheduledQuoteCompanyId);

            $this->createRescheduledAppointmentProcessingAllocation($consumerProduct, $rescheduledAppt->{ProductAppointment::RELATION_CONSUMER_PRODUCT});

            $this->createAppointmentProductCancellation(
                $productAssignment->{ProductAssignment::FIELD_ID},
                $cancellationReason,
                $cancellationNote
            );

            $this->createAppointmentDelivery($rescheduledProductAssignment->{ProductAssignment::FIELD_CAMPAIGN_ID}, $rescheduledProductAssignment->{ProductAssignment::FIELD_ID});

            $this->markAppointmentProcessingAllocationRejectedCancelled($consumerProduct);

            //Send rescheduled notification to company TODO

            DB::commit();

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param string $apptKey
     * @param string $apptCode
     * @return array
     */
    public function getAppointmentDeliveryInfoForCancellation(string $apptKey, string $apptCode): array
    {
        $apptDelivery = $this->getAppointmentDeliveryByKeyAndCode(
            $apptKey,
            $apptCode,
            [
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT => function($with) {
                    $with
                        ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                        ->where(ProductAssignment::FIELD_DELIVERED, true);
                },
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_SALE_TYPE,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_APPOINTMENT,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER.'.'.Consumer::RELATION_LEGACY_LEAD,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY,
                AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_LEGACY_CAMPAIGN => function($with) {
                    $with->select([
                        LeadCampaign::ID
                    ]);
                },
                AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_LEGACY_CAMPAIGN.'.'.LeadCampaign::RELATION_LEAD_CAMPAIGN_DELIVERY_METHODS.'.'.LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD.'.'.LeadDeliveryMethod::RELATION_CONTACT => function($with) {
                    $with
                        ->where(EloquentCompanyContact::FIELD_STATUS, EloquentCompanyContact::STATUS_ACTIVE)
                        ->select([
                            EloquentCompanyContact::FIELD_CONTACT_ID,
                            EloquentCompanyContact::FIELD_COMPANY_ID,
                            EloquentCompanyContact::FIELD_PHONE,
                            EloquentCompanyContact::FIELD_MOBILE,
                            EloquentCompanyContact::FIELD_EMAIL,
                            EloquentCompanyContact::FIELD_FIRST_NAME,
                            EloquentCompanyContact::FIELD_LAST_NAME
                        ]);
                }
            ]
        );

        $productAssignment = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT};

        $consumerProduct = $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT};

        $legacyLead = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::RELATION_LEGACY_LEAD};

        $leadConsumerProduct = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT};

        $leadSaleType = SaleTypesEnum::mapAllocationsToVerifiedType(
            $leadConsumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS} - $this->leadProcessingRepository->getRemainingLegsForAppointmentLead($legacyLead->{EloquentQuote::ID})
        );

        $industry = IndustryEnum::from($consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME});

        /** @var Address $address */
        $address = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS};

        $countyKey = $this->databaseLocationRepository->getZipCode($address->{Address::FIELD_ZIP_CODE})->{Location::COUNTY_KEY};

        $propertyType = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD}[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value;
        $propertyType = ucwords(strtolower($propertyType));
        $propertyType = PropertyType::from($propertyType);

        $qualityTier = $industry === IndustryEnum::SOLAR ? QualityTier::PREMIUM : QualityTier::STANDARD;

        $leadProcessorUser = $this->productProcessingRepository
            ->getProcessorsWhoAllocatedProducts(collect([$consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID}]))
            ->firstOrFail()
            ->{LeadProcessingHistory::RELATION_LEAD_PROCESSOR}
            ->{LeadProcessor::RELATION_USER};

        $leadProcessingScenario = $consumerProduct
            ->{ConsumerProduct::RELATION_APPOINTMENT}
            ->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT}
            ->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}
            ->{ConsumerProductData::FIELD_PAYLOAD}[ConsumerProductRepository::PRODUCT_DATA_KEY_STATUS_REASON] ?? '';

        return [
            self::FUNC_RET_APPT_DELIVERY => $apptDelivery,
            self::FUNC_RET_PRODUCT_ASSIGNMENT => $productAssignment,
            self::FUNC_RET_CONSUMER_PRODUCT => $consumerProduct,
            self::FUNC_RET_INDUSTRY => $industry,
            self::FUNC_RET_ADDRESS => $address,
            self::FUNC_RET_COUNTY_KEY => $countyKey,
            self::FUNC_RET_PROPERTY_TYPE => $propertyType,
            self::FUNC_RET_LEGACY_LEAD => $legacyLead,
            self::FUNC_RET_QUALITY_TIER => $qualityTier,
            self::FUNC_RET_LEAD_PROCESSOR_USER => $leadProcessorUser,
            self::FUNC_RET_LEAD_PROCESSING_SCENARIO => $leadProcessingScenario,
            self::FUNC_RET_LEAD_CONSUMER_PRODUCT => $leadConsumerProduct,
            self::FUNC_RET_LEAD_SALE_TYPE => $leadSaleType
        ];
    }

    /**
     * @param string $apptKey
     * @param string $apptCode
     * @param array $relations
     * @return AppointmentDelivery
     */
    public function getAppointmentDeliveryByKeyAndCode(string $apptKey, string $apptCode, array $relations = []): AppointmentDelivery
    {
        /** @type AppointmentDelivery */
        return AppointmentDelivery::query()
                ->where(AppointmentDelivery::FIELD_CONSUMER_TOKEN, $apptKey)
                ->where(AppointmentDelivery::FIELD_CONSUMER_CODE, $apptCode)
                ->with($relations)
                ->firstOrFail();
    }

    /**
     * @param int $productAssignmentId
     * @param AppointmentCancellationReason $reason
     * @param string $note
     * @return ProductCancellation
     */
    public function createAppointmentProductCancellation(int $productAssignmentId, AppointmentCancellationReason $reason, string $note = ''): ProductCancellation
    {
        /** @type ProductCancellation */
        return ProductCancellation::updateOrCreate(
            [
                ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignmentId
            ],
            [
                ProductCancellation::FIELD_REASON => $reason->value,
                ProductCancellation::FIELD_NOTE => $note
            ]
        );
    }

    /**
     * @param int $productAssignmentId
     * @param int $companyId
     * @param RejectionReasons $rejectionReason
     * @param string $rejectionNotes
     * @return bool
     * @throws Throwable
     */
    public function rejectAppointment(int $productAssignmentId, int $companyId, RejectionReasons $rejectionReason, string $rejectionNotes): bool
    {
        try {
            extract($this->getProductAssignmentInfoForRejection($productAssignmentId, $companyId));

            $leadPrice = $this->getAppointmentLeadFloorPriceForCampaign(
                $leadConsumerProduct->{ConsumerProduct::FIELD_SERVICE_PRODUCT_ID},
                $industry,
                $propertyType,
                $qualityTier,
                StateAbbreviation::from(strtoupper($address->{Address::FIELD_STATE})),
                $leadSaleType,
                $countyKey
            );

            $leadQuoteCompanyId = app(LeadDeliveryService::class)->demoteAppointmentQuoteCompanyToLead(
                $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                $leadPrice,
                SaleTypesEnum::mapSaleTypeToLegacyId($leadSaleType),
                $productAssignment->{ProductAssignment::RELATION_CAMPAIGN}->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID},
                $leadProcessorUser->{User::FIELD_LEGACY_USER_ID},
                $leadProcessingScenario,
                false,
                true,
                0,
                "{$rejectionReason->value}|{$rejectionNotes}"
            );

            DB::beginTransaction();

            $productRejection = app(ProductRejectionRepository::class)->createProductRejection(
                $productAssignment->{ProductAssignment::FIELD_ID},
                $companyUser->{CompanyUser::FIELD_ID},
                "{$rejectionReason->value}|{$rejectionNotes}"
            );

            if(empty($productRejection)) {
                $errMsg = "Error rejecting appointment";

                throw new CustomMessageException($errMsg);
            }

            $leadProductAssignment = $this->demoteAppointmentAssignmentToLead(
                $productAssignment,
                $leadPrice,
                false,
                0,
                $leadQuoteCompanyId,
                $leadSaleType
            );

            if(empty($leadProductAssignment)) {
                $errMsg = "Error demoting appointment to lead";

                throw new CustomMessageException($errMsg);
            }

            $this->markAppointmentProcessingAllocationRejectedCancelled($consumerProduct);

            SendCancellationNoticeToConsumerJob::dispatch(
                $consumerProduct,
                $companyName,
                $industry,
                $appointmentDateTime
            )->afterCommit();

            DB::commit();

            ProductRejectedEvent::dispatch($productAssignment->{ProductAssignment::FIELD_ID});

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                "Rejection err: ".$errLocation.substr($e->getMessage(), 0, 255),
                [
                    "product_assignment_id" => $productAssignmentId,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param int $productAssignmentId
     * @param int $companyId
     * @return array
     * @throws Exception
     */
    private function getProductAssignmentInfoForRejection(int $productAssignmentId, int $companyId): array
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_ID, $productAssignmentId)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->with([
                ProductAssignment::RELATION_SALE_TYPE,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER.'.'.Consumer::RELATION_LEGACY_LEAD,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT,
                ProductAssignment::RELATION_COMPANY
            ])
            ->firstOrFail();

        if(Carbon::now() >= $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}) {
            $errMsg = "Rejection period has passed";

            throw new CustomMessageException($errMsg);
        }

        $consumerProduct = $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT};

        $legacyLead = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::RELATION_LEGACY_LEAD};

        $leadConsumerProduct = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT};

        $leadSaleType = SaleTypesEnum::mapAllocationsToVerifiedType(
            $leadConsumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS} - $this->leadProcessingRepository->getRemainingLegsForAppointmentLead($legacyLead->{EloquentQuote::ID})
        );

        $rejectionPercentage = (float) app(ComputedRejectionStatisticRepository::class)->getCompanyAppointmentRejectionPercentage(
            $productAssignment->{ProductAssignment::RELATION_COMPANY}
        );

        if($rejectionPercentage >= (float) config('sales.appointments.overall_rejection_percentage_threshold')) {
            $errMsg = "Rejection limit exceeded";

            throw new CustomMessageException($errMsg);
        }

        if(empty($consumerProduct)) {
            $errMsg = "Invalid appointment";

            throw new CustomMessageException($errMsg);
        }

        $industry = IndustryEnum::from($consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME});

        /** @var Address $address */
        $address = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS};

        $countyKey = app(DatabaseLocationRepository::class)->getZipCode($address->{Address::FIELD_ZIP_CODE})->{Location::COUNTY_KEY};

        $propertyType = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD}[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value;
        $propertyType = ucwords(strtolower($propertyType));
        $propertyType = PropertyType::from($propertyType);

        $qualityTier = $industry === IndustryEnum::SOLAR ? QualityTier::PREMIUM : QualityTier::STANDARD;

        $companyName = $productAssignment->{ProductAssignment::RELATION_COMPANY}->{Company::FIELD_NAME};

        $appointmentDateTime = Carbon::createFromFormat(
            'Y-m-d H:i:s',
            $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT},
            (string) $address->{Address::FIELD_UTC}
        )->format("m/d/Y g:i A");

        $companyUser = app(CompanyUserRepository::class)->getCompanyUserByLegacyIdAndCompanyIdOrFail(app(EloquentUser::class)->{EloquentUser::ID}, $companyId);

        $leadProcessorUser = app(ProductProcessingRepository::class)
            ->getProcessorsWhoAllocatedProducts(collect([$consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID}]))
            ->firstOrFail()
            ->{LeadProcessingHistory::RELATION_LEAD_PROCESSOR}
            ->{LeadProcessor::RELATION_USER};

        $leadProcessingScenario = $consumerProduct
            ->{ConsumerProduct::RELATION_APPOINTMENT}
            ->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT}
            ->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}
            ->{ConsumerProductData::FIELD_PAYLOAD}[ConsumerProductRepository::PRODUCT_DATA_KEY_STATUS_REASON] ?? '';

        return [
            self::FUNC_RET_PRODUCT_ASSIGNMENT => $productAssignment,
            self::FUNC_RET_CONSUMER_PRODUCT => $consumerProduct,
            self::FUNC_RET_INDUSTRY => $industry,
            self::FUNC_RET_ADDRESS => $address,
            self::FUNC_RET_COUNTY_KEY => $countyKey,
            self::FUNC_RET_PROPERTY_TYPE => $propertyType,
            self::FUNC_RET_LEGACY_LEAD => $legacyLead,
            self::FUNC_RET_QUALITY_TIER => $qualityTier,
            self::FUNC_RET_LEAD_PROCESSOR_USER => $leadProcessorUser,
            self::FUNC_RET_LEAD_PROCESSING_SCENARIO => $leadProcessingScenario,
            self::FUNC_RET_COMPANY_USER => $companyUser,
            self::FUNC_RET_APPOINTMENT_DATETIME => $appointmentDateTime,
            self::FUNC_RET_COMPANY_NAME => $companyName,
            self::FUNC_RET_LEAD_CONSUMER_PRODUCT => $leadConsumerProduct,
            self::FUNC_RET_LEAD_SALE_TYPE => $leadSaleType
        ];
    }

    /**
     * @param ProductAssignment $apptProductAssignment
     * @param float $leadPrice
     * @param bool|null $canReject
     * @param int $rejectionExpiryTimestamp
     * @param int $leadQuoteCompanyId
     * @param SaleTypesEnum $leadSaleType
     * @return ProductAssignment|null The lead product assignment
     * @throws Throwable
     */
    protected function demoteAppointmentAssignmentToLead(
        ProductAssignment $apptProductAssignment,
        float $leadPrice,
        bool $canReject,
        int $rejectionExpiryTimestamp,
        int $leadQuoteCompanyId,
        SaleTypesEnum $leadSaleType
    ): ?ProductAssignment
    {
        try {
            DB::beginTransaction();

            // If the appointment is cancelled by consumer then appointment  should not be chargeable and should not count
            // towards rejection percentage. Resulting lead should be rejectable
            if($canReject) {
                $apptProductAssignment->{ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE} = ProductAssignmentAffectRejectionPercentage::NO_AFFECT->value;
                $apptProductAssignment->{ProductAssignment::FIELD_CHARGEABLE}                  = false;
            }

            $apptProductAssignment->save();

            $now = Carbon::now();

            $leadProductAssignment = $apptProductAssignment->replicate($apptProductAssignment->getGuarded());

            $leadConsumerProductId = $apptProductAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID};

            $leadProductAssignment->{ProductAssignment::FIELD_SALE_TYPE_ID} = SaleTypeModel::query()->where(SaleTypeModel::FIELD_NAME, $leadSaleType->value)->firstOrFail()->{SaleTypeModel::FIELD_ID};
            $leadProductAssignment->{ProductAssignment::FIELD_COST} = $leadPrice;
            $leadProductAssignment->{ProductAssignment::FIELD_LEGACY_ID} = $leadQuoteCompanyId;
            $leadProductAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID} = $leadConsumerProductId;
            $leadProductAssignment->{ProductAssignment::FIELD_CHARGEABLE} = true;
            $leadProductAssignment->{ProductAssignment::FIELD_DELIVERED} = false;
            $leadProductAssignment->{ProductAssignment::FIELD_DELIVERED_AT} = 0;
            $leadProductAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY} = 0;
            $leadProductAssignment->{ProductAssignment::CREATED_AT} = $now;
            $leadProductAssignment->{ProductAssignment::UPDATED_AT} = $now;
            $leadProductAssignment->{ProductAssignment::FIELD_PARENT_PRODUCT_ID} = $this->productRepository->getAppointmentProductId();

            // If the resulting lead is rejectable we want that lead to count towards the appointment rejection percentage.
            // If the resulting lead is not rejectable then we don't want it to count towards any rejection percentage
            if($canReject) {
                $leadProductAssignment->{ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE} = ProductAssignmentAffectRejectionPercentage::AFFECT_PARENT_PRODUCT->value;
            } else {
                $leadProductAssignment->{ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE} = ProductAssignmentAffectRejectionPercentage::NO_AFFECT->value;
            }

            if(!empty($canReject)
            && !empty($rejectionExpiryTimestamp)) {
                $leadProductAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY} = Carbon::createFromTimestamp($rejectionExpiryTimestamp);
                $leadProductAssignment->{ProductAssignment::FIELD_PARENT_PRODUCT_ID} = $this->productRepository->getAppointmentProductId();
            }

            $leadProductAssignment->save();

            DB::commit();

            return $leadProductAssignment;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                __METHOD__.': '.$errLocation.substr($e->getMessage(), 0, 255),
                [
                    "product_assignment_id" => $apptProductAssignment->{ProductAssignment::FIELD_ID},
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($e);

            throw $e;
        }
    }

    /**
     * @param int $originalApptId
     * @param QualityTier $appointmentType
     * @param Carbon $appointmentDateTime
     * @param int $legacyId
     * @param int $leadConsumerProductId
     * @return ProductAppointment
     * @throws Exception
     */
    public function createRescheduledAppointmentAndAssociatedConsumerProduct(
        int $originalApptId,
        QualityTier $appointmentType,
        Carbon $appointmentDateTime,
        ?int $legacyId,
        int $leadConsumerProductId
    ): ProductAppointment
    {
        DB::beginTransaction();

        $productAppointment = ProductAppointment::create([
            ProductAppointment::APPOINTMENT_TYPE => $appointmentType->value,
            ProductAppointment::APPOINTMENT_DATE => $appointmentDateTime->format('Y-m-d'),
            ProductAppointment::APPOINTMENT_TIME => $appointmentDateTime->format('H:i:s'),
            ProductAppointment::LEGACY_ID => $legacyId,
            ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => $leadConsumerProductId,
            ProductAppointment::CONSUMER_PRODUCT_ID => 0,
            ProductAppointment::ORIGINAL_APPOINTMENT_ID => $originalApptId
        ]);

        /** @var ConsumerProduct $leadConsumerProduct */
        $leadConsumerProduct = ConsumerProduct::findOrFail($leadConsumerProductId);

        $apptServiceProductId = app(ServiceProductRepository::class)->getServiceProductByProductAndIndustryService(
            $leadConsumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::FIELD_INDUSTRY_SERVICE_ID},
            ProductEnum::APPOINTMENT
        )?->{ServiceProduct::FIELD_ID};

        if (!$apptServiceProductId) {
            throw new Exception("Failed to create reschedule appointment. The service for appointment product is not available. Consumer Product: {$leadConsumerProduct->id}");
        }

        $consumerProductData = collect($leadConsumerProduct->attributesToArray())
            ->except([
                ConsumerProduct::FIELD_ID,
                ConsumerProduct::FIELD_SERVICE_PRODUCT_ID,
                ConsumerProduct::FIELD_CREATED_AT,
                ConsumerProduct::FIELD_UPDATED_AT
            ]);

        $consumerProductData->put(ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_ALLOCATED);
        $consumerProductData->put(ConsumerProduct::FIELD_GOOD_TO_SELL, true);
        $consumerProductData->put(ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, $apptServiceProductId);

        $consumerProduct = ConsumerProduct::create($consumerProductData->toArray());

        $productAppointment->{ProductAppointment::CONSUMER_PRODUCT_ID} = $consumerProduct->{ConsumerProduct::FIELD_ID};

        $productAppointment->save();

        DB::commit();

        return $productAppointment;
    }

    /**
     * @param ProductAssignment $priorApptAssignment
     * @param ProductAppointment $rescheduledAppt
     * @param int $rescheduledQuoteCompanyId
     * @return ProductAssignment
     */
    public function createRescheduledAppointmentProductAssignment(
        ProductAssignment $priorApptAssignment,
        ProductAppointment $rescheduledAppt,
        int $rescheduledQuoteCompanyId
    ): ProductAssignment
    {
        DB::beginTransaction();

        $priorApptAssignment->{ProductAssignment::FIELD_AFFECT_REJECTION_PERCENTAGE} = ProductAssignmentAffectRejectionPercentage::NO_AFFECT->value;
        $priorApptAssignment->{ProductAssignment::FIELD_CHARGEABLE} = false;

        $priorApptAssignment->save();

        $now = Carbon::now();

        $rescheduledApptAssignment = $priorApptAssignment->replicate($priorApptAssignment->getGuarded());

        $rescheduledApptAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID} = $rescheduledAppt->{ProductAppointment::CONSUMER_PRODUCT_ID};
        $rescheduledApptAssignment->{ProductAssignment::FIELD_LEGACY_ID} = $rescheduledQuoteCompanyId;
        $rescheduledApptAssignment->{ProductAssignment::FIELD_CHARGEABLE} = true;
        $rescheduledApptAssignment->{ProductAssignment::FIELD_DELIVERED} = false;
        $rescheduledApptAssignment->{ProductAssignment::FIELD_DELIVERED_AT} = 0;
        $rescheduledApptAssignment->{ProductAssignment::CREATED_AT} = $now;
        $rescheduledApptAssignment->{ProductAssignment::UPDATED_AT} = $now;

        $rescheduledApptAssignment->save();

        DB::commit();

        return $rescheduledApptAssignment;
    }

    /**
     * @param ConsumerProduct $priorConsumerProduct
     * @param ConsumerProduct $rescheduledConsumerProduct
     * @return AppointmentProcessingAllocation
     */
    public function createRescheduledAppointmentProcessingAllocation(ConsumerProduct $priorConsumerProduct, ConsumerProduct $rescheduledConsumerProduct): AppointmentProcessingAllocation
    {
        DB::beginTransaction();

        $priorAllocation = AppointmentProcessingAllocation::query()
            ->where(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $priorConsumerProduct->{ConsumerProduct::FIELD_ID})
            ->firstOrFail();

        /** @var AppointmentProcessingAllocation $rescheduledAllocation */
        $rescheduledAllocation = $priorAllocation->replicate($priorAllocation->getGuarded());

        $rescheduledAllocation->{AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID} = $rescheduledConsumerProduct->{ConsumerProduct::FIELD_ID};
        $rescheduledAllocation->{AppointmentProcessingAllocation::FIELD_DELIVERED} = false;
        $rescheduledAllocation->{AppointmentProcessingAllocation::FIELD_ALLOCATE_AT} = Carbon::now();

        $rescheduledAllocation->save();

        DB::commit();

        return $rescheduledAllocation;
    }

    /**
     * Get the lead floor price for the geographical region of the appointment campaign
     *
     * @param int $serviceProductId
     * @param IndustryEnum $industry
     * @param PropertyType $propertyType
     * @param QualityTier $qualityTier
     * @param StateAbbreviation $stateAbbreviation
     * @param SaleTypesEnum $appointmentSaleType
     * @param string $countyKey
     * @return float
     */
    public function getAppointmentLeadFloorPriceForCampaign(
        int $serviceProductId,
        IndustryEnum $industry,
        PropertyType $propertyType,
        QualityTier $qualityTier,
        StateAbbreviation $stateAbbreviation,
        SaleTypesEnum $appointmentSaleType,
        string $countyKey
    ): float
    {
        $productPricingService = ProductPricingServiceFactory::make(config('sales.product_pricing_driver'));

        $floorTable = $productPricingService->getFloorTable(
            $serviceProductId,
            ProductEnum::LEAD,
            $industry->value,
            $propertyType,
            $stateAbbreviation,
            [$countyKey]
        )->toArray();

        $stateAbbr = $stateAbbreviation->value;
        $saleType = $appointmentSaleType->value;

        $stateFloorPrice = $floorTable[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_SALES_TYPES][$saleType][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;
        $countyFloorPrice = $floorTable[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_COUNTIES][$countyKey][FloorPriceTable::ARRAY_KEY_SALES_TYPES][$saleType][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;

        return round($countyFloorPrice ?? $stateFloorPrice ?? 0.00, 2);
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param ProductCampaign $campaign
     * @param ConsumerProduct $consumerProduct
     * @param Consumer $consumer
     * @param Address $address
     * @param string $companyName
     * @param IndustryEnum $industry
     * @param string $appointmentDateTime
     * @return bool
     */
    public function sendCancellationNoticeToCompany(
        ProductAssignment $productAssignment,
        ProductCampaign $campaign,
        ConsumerProduct $consumerProduct,
        Consumer $consumer,
        Address $address,
        string $companyName,
        IndustryEnum $industry,
        string $appointmentDateTime
    ): bool
    {
        $detailsUrl = $this->getDashboardAppointmentUrl($productAssignment->{ProductAssignment::FIELD_ID}, $industry->value);

        $textMessage = sprintf(
            "Appointment for %s on %s was cancelled by the client\nDetails: %s",
            $consumer->getFullName(),
            $appointmentDateTime,
            $detailsUrl
        );

        $this->deliverCommunicationsToCampaign(
            $campaign,
            $textMessage,
            function($campaignDeliveryMethod) use ($consumerProduct, $address, $consumer, $industry, $companyName, $appointmentDateTime) {
                return new NotifyCancellationCompany(
                    $consumerProduct,
                    $address,
                    $consumer,
                    $industry,
                    $companyName,
                    $campaignDeliveryMethod->{LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD}?->{LeadDeliveryMethod::RELATION_CONTACT}?->name ?? "",
                    $appointmentDateTime
                );
            }
        );

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Consumer $consumer
     * @param string $companyName
     * @param IndustryEnum $industry
     * @param string $appointmentDateTime
     * @param bool $isRejection
     * @return bool
     * @throws Exception
     */
    public function sendCancellationConfirmationToConsumer(
        ConsumerProduct $consumerProduct,
        Consumer $consumer,
        string $companyName,
        IndustryEnum $industry,
        string $appointmentDateTime,
        bool $isRejection
    ): bool
    {
        $senderName = match($industry) {
            IndustryEnum::ROOFING => "RoofingCalc",
            IndustryEnum::SOLAR => "SolarReviews"
        };

        $companyName = $this->trimCompanyName($companyName);

        $textMessage = sprintf(
            "%s: Your appointment with %s was %s",
            $senderName,
            $companyName,
            $isRejection ? 'cancelled by the company' : 'cancelled'
        );

        if(!empty($consumer->{Consumer::FIELD_PHONE}) && $this->isProd) {
            $this->twilioCommunicationService->sendSMS($this->twilioFromNumber, $consumer->{Consumer::FIELD_PHONE}, $textMessage);
        }

        if(!empty($consumer->{Consumer::FIELD_EMAIL})) {
            Email::send(
                $this->isProd ? $consumer->{Consumer::FIELD_EMAIL} : config('app.outgoing_communication.test_email'),
                new NotifyCancellationConsumer(
                    $consumerProduct,
                    $consumerProduct->{ConsumerProduct::RELATION_CONSUMER},
                    $industry,
                    $companyName,
                    $appointmentDateTime,
                    $isRejection
                )
            );
        }

        return true;
    }

    /**
     * @param ProductCampaign $campaign
     * @param string $textMessage
     * @param Mailable|callable $email Either a Mailable or a function that returns a Mailable. The function receives a LeadCampaignDeliveryMethod as input.
     * @return bool
     */
    protected function deliverCommunicationsToCampaign(
        ProductCampaign $campaign,
        string $textMessage,
        Mailable|callable $email
    ): bool
    {
        $communicationSent = false;

        //TODO: use product campaign contacts once migrated
        $campaign->{ProductCampaign::RELATION_LEGACY_CAMPAIGN}->{LeadCampaign::RELATION_LEAD_CAMPAIGN_DELIVERY_METHODS}->each(function($legacyCampaignDeliveryMethod) use ($textMessage, $email, &$communicationSent) {
            $deliveryTypes = $legacyCampaignDeliveryMethod->getTypeArray();
            $deliveryMethod = $legacyCampaignDeliveryMethod->{LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD};

            $toNumber = in_array(LeadCampaignDeliveryMethod::TYPE_VALUE_APPT_SMS, $deliveryTypes, true) && !empty($deliveryMethod->{LeadDeliveryMethod::RELATION_CONTACT}->{EloquentCompanyContact::FIELD_MOBILE}) ? $deliveryMethod->{LeadDeliveryMethod::RELATION_CONTACT}->{EloquentCompanyContact::FIELD_MOBILE} : '';
            $toEmail = in_array(LeadCampaignDeliveryMethod::TYPE_VALUE_APPT_EMAIL, $deliveryTypes, true) && !empty($deliveryMethod->{LeadDeliveryMethod::RELATION_CONTACT}->{EloquentCompanyContact::FIELD_EMAIL}) ? $deliveryMethod->{LeadDeliveryMethod::RELATION_CONTACT}->{EloquentCompanyContact::FIELD_EMAIL} : '';

            if(!empty($toNumber) && $this->isProd) {
                $this->twilioCommunicationService->sendSMS($this->twilioFromNumber, $toNumber, $textMessage);

                $communicationSent = true;
            }

            if(!empty($toEmail)) {
                Email::send(
                    $this->isProd ? $toEmail : config('app.outgoing_communication.test_email'),
                    is_callable($email) ? call_user_func($email, $legacyCampaignDeliveryMethod) : $email
                );

                $communicationSent = true;
            }
        });

        return $communicationSent;
    }

    /**
     * @param string $companyName
     * @return string
     */
    protected function trimCompanyName(string $companyName): string
    {
        return trim(preg_replace('/llc|inc|,|\./', '',  $companyName));
    }

    /**
     * @return bool
     * @throws Throwable
     */
    public function reactivateCampaigns(): bool
    {
        self::writeAppointmentLog("Reactivating appointment campaigns");

        try {
            $productCampaigns = ProductCampaign::query()
                ->whereHas(ProductCampaign::RELATION_PRODUCT, function($has) {
                    $has->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT);
                })
                ->where(ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP, '<=', Carbon::now()->timestamp)
                ->where(ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP, '>', 0)
                ->where(ProductCampaign::FIELD_STATUS, false)
                ->get();

            $now = Carbon::now('UTC');

            foreach($productCampaigns as $productCampaign) {
                $productCampaign->{ProductCampaign::FIELD_STATUS} = true;
                $productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP} = 0;

                $productCampaign->save();

                foreach($productCampaign->{ProductCampaign::RELATION_BUDGETS} as $pcb) {
                    $pcb->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP} = $now;

                    $pcb->save();
                }
            }

            return true;
        }
        catch(Throwable $e) {
            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                __METHOD__.': '.$errLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($e);

            throw $e;
        }
    }

    /**
     * @param ProductCampaign $appointmentCampaign
     * @param array $leadCategoryIds
     * @return array
     */
    public function getAppointmentCampaignPriceRanges(ProductCampaign $appointmentCampaign, array $leadCategoryIds): array
    {
        $appointmentCampaign->loadMissing([
            ProductCampaign::RELATION_LEGACY_CAMPAIGN.'.'.LeadCampaign::RELATION_LEAD_CAMPAIGN_COUNTY_LOCATIONS.'.'.LeadCampaignLocation::RELATION_LOCATION,
            ProductCampaign::RELATION_COMPANY.'.'.Company::RELATION_LEGACY_COMPANY
        ]);

        $campaignCountyLocations = $appointmentCampaign->{ProductCampaign::RELATION_LEGACY_CAMPAIGN}->{LeadCampaign::RELATION_LEAD_CAMPAIGN_COUNTY_LOCATIONS};

        $industry = $appointmentCampaign->{ProductCampaign::RELATION_COMPANY}->{Company::RELATION_LEGACY_COMPANY}->isTypeRoofer() ? IndustryEnum::ROOFING : IndustryEnum::SOLAR;

        $leadCategories = LeadCategory::query()->whereIn(LeadCategory::ID, $leadCategoryIds)->pluck(LeadCategory::NAME)->toArray();

        return $this->fetchPriceRanges($leadCategories, $campaignCountyLocations, $industry);
    }

    /**
     * @param LeadCampaign $campaign
     * @param array $leadCategoryIds
     * @return array
     */
    public function getAppointmentCampaignPricesFromLegacyCampaign(LeadCampaign $campaign, array $leadCategoryIds): array
    {
        $campaignCountyLocations = $campaign->{LeadCampaign::RELATION_LEAD_CAMPAIGN_COUNTY_LOCATIONS};

        $industry = $campaign->company->isTypeRoofer() ? IndustryEnum::ROOFING : IndustryEnum::SOLAR;

        $leadCategories = LeadCategory::query()->whereIn(LeadCategory::ID, $leadCategoryIds)->pluck(LeadCategory::NAME)->toArray();

        return $this->fetchPriceRanges($leadCategories, $campaignCountyLocations, $industry);
    }

    /**
     * @param array $leadCategories
     * @param Collection $campaignCountyLocations
     * @param IndustryEnum $industry
     * @return array
     */
    protected function fetchPriceRanges(array $leadCategories, Collection $campaignCountyLocations, IndustryEnum $industry): array
    {
        $campaignLocations = [];
        foreach ($campaignCountyLocations as $leadCampaignCountyLocation) {
            $location = $leadCampaignCountyLocation->{LeadCampaignLocation::RELATION_LOCATION};

            if (empty($campaignLocations[$location->{Location::STATE_ABBREVIATION}])) {
                $campaignLocations[$location->{Location::STATE_ABBREVIATION}] = [];
            }

            $campaignLocations[$location->{Location::STATE_ABBREVIATION}][] = $location->{Location::COUNTY_KEY};
        }

        $productPricingService = ProductPricingServiceFactory::make(config('sales.product_pricing_driver'));

        $campaignPrices = [
            QualityTier::ONLINE->value => [
                SaleTypesEnum::EXCLUSIVE->value => [
                    "min" => 0,
                    "max" => 0
                ],
                SaleTypesEnum::DUO->value => [
                    "min" => 0,
                    "max" => 0
                ],
                SaleTypesEnum::TRIO->value => [
                    "min" => 0,
                    "max" => 0
                ]
            ],
            QualityTier::IN_HOME->value => [
                SaleTypesEnum::EXCLUSIVE->value => [
                    "min" => 0,
                    "max" => 0
                ],
                SaleTypesEnum::DUO->value => [
                    "min" => 0,
                    "max" => 0
                ]
            ]
        ];
        foreach($leadCategories as $leadCategory) {
            foreach($campaignLocations as $state => $countyKeys) {
                $propertyType = PropertyType::from($leadCategory);
                $stateAbbr = StateAbbreviation::from($state);

                $floorTable = $productPricingService->getFloorTable(
                    0,
                    ProductEnum::APPOINTMENT,
                    $industry->value,
                    $propertyType,
                    $stateAbbr,
                    $countyKeys
                )->toArray();

                foreach($floorTable[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS] as $qualityTier => $locations) {
                    $stateFloorPrices = $locations[FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr->value][FloorPriceTable::ARRAY_KEY_SALES_TYPES];

                    foreach($locations[FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr->value][FloorPriceTable::ARRAY_KEY_COUNTIES] as $countyPrices) {
                        foreach($countyPrices[FloorPriceTable::ARRAY_KEY_SALES_TYPES] as $saleTypeKey => $saleTypePrices) {
                            if(empty($campaignPrices[$qualityTier][$saleTypeKey])) {
                                continue;
                            }

                            $countySaleTypePrice = $saleTypePrices[FloorPriceTable::ARRAY_KEY_PRICE];
                            $stateSaleTypePrice = $stateFloorPrices[$saleTypeKey][FloorPriceTable::ARRAY_KEY_PRICE];

                            $campaignPrices[$qualityTier][$saleTypeKey]["min"] =
                                $campaignPrices[$qualityTier][$saleTypeKey]["min"] > 0
                                    ? min($campaignPrices[$qualityTier][$saleTypeKey]["min"], $countySaleTypePrice, $stateSaleTypePrice)
                                    : min($countySaleTypePrice, $stateSaleTypePrice);

                            $campaignPrices[$qualityTier][$saleTypeKey]["max"] =
                                $campaignPrices[$qualityTier][$saleTypeKey]["max"] > 0
                                    ? max($campaignPrices[$qualityTier][$saleTypeKey]["max"], $countySaleTypePrice, $stateSaleTypePrice)
                                    : max($countySaleTypePrice, $stateSaleTypePrice);
                        }
                    }
                }
            }
        }

        return $campaignPrices;
    }

    /**
     * @param int $consumerProductId
     * @param array $campaignIds
     * @return array
     * @throws Throwable
     */
    public function getProductCampaignsAvailability(int $consumerProductId, array $campaignIds): array
    {
        $consumerProduct = app(ConsumerProductRepository::class)->findOrFail($consumerProductId, [ConsumerProduct::RELATION_APPOINTMENT, ConsumerProduct::RELATION_ADDRESS]);

        $appointment = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT};

        $appointmentDateTime = Carbon::createFromFormat(
            'Y-m-d H:i:s',
            $appointment->{ProductAppointment::APPOINTMENT},
            (string) $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_UTC}
        );

        return $this->appointmentCalendarIntegrationService->getAvailabilityForProductCampaigns(collect($campaignIds), $appointmentDateTime, $appointment->{ProductAppointment::APPOINTMENT_TYPE}, $consumerProduct->{ConsumerProduct::CREATED_AT});
    }

    /**
     * @param int $apptConsumerProductId
     * @return array
     */
    public function getCompaniesThatReceivedRelatedAppointments(int $apptConsumerProductId): array
    {
        $leadConsumerProductId = ProductAppointment::query()->where(ProductAppointment::CONSUMER_PRODUCT_ID, $apptConsumerProductId)->firstOrFail()->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID};

        return ProductAppointment::query()
            ->join(ProductAssignment::TABLE, function($join) {
                $join->on(
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                    '=',
                    ProductAppointment::TABLE.'.'.ProductAppointment::CONSUMER_PRODUCT_ID
                );
            })
            ->where(ProductAppointment::TABLE.'.'.ProductAppointment::LEAD_CONSUMER_PRODUCT_ID, $leadConsumerProductId)
            ->where(ProductAppointment::TABLE.'.'.ProductAppointment::CONSUMER_PRODUCT_ID, '!=', $apptConsumerProductId)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
            ->select([
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID
            ])
            ->distinct()
            ->pluck(ProductAssignment::FIELD_COMPANY_ID)
            ->toArray();
    }

    /**
     * @param int $consumerProductId
     * @return bool
     */
    public function wasAppointmentOrLeadAlreadySold(int $consumerProductId): bool
    {
        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->exists();
    }

    /**
     * @param Collection $brsCampaign
     * @param int $consumerProductId
     * @param int $saleTypeId
     * @return int
     * @throws Throwable
     */
    public function pushAppointmentToCompanyWithCalendar(
        Collection $brsCampaign,
        int $consumerProductId,
        int $saleTypeId
    ): int
    {
        try {
            $productCampaign = ProductCampaign::findOrFail($brsCampaign->get(BestRevenueScenarioDataModel::CAMPAIGN_ID));
            $companyId = $productCampaign->{ProductCampaign::FIELD_COMPANY_ID};

            self::writeAppointmentLog(
                "Pushing appointment ({$consumerProductId}) to company ({$companyId})",
                [
                    "company_id" => $companyId,
                    "consumer_product_id" => $consumerProductId
                ]
            );

            if($this->wasAppointmentOrLeadAlreadySold($consumerProductId)) {
                self::writeAppointmentLog(
                    "Appointment ({$consumerProductId}) was already sold",
                    [
                        "consumer_product_id" => $consumerProductId
                    ]
                );

                throw new Exception("Appointment ({$consumerProductId}) already sold");
            }

            $apptConsumerProduct = ConsumerProduct::findOrFail($consumerProductId);
            $price = $brsCampaign->get(BestRevenueScenarioDataModel::PRICE);

            $leadReference = '';
            if($apptConsumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_LEGACY_ID}) {
              $leadReference = $apptConsumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::RELATION_LEGACY_LEAD}?->{EloquentQuote::REFERENCE} ?? '';
            }

            $companyReference = $productCampaign->{ProductCampaign::RELATION_COMPANY}->{Company::FIELD_REFERENCE};

            $processingAllocation = AppointmentProcessingAllocation::where(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $apptConsumerProduct->{ConsumerProduct::FIELD_ID})->firstOrFail();

            $legacyUserId = $processingAllocation->leadProcessor?->user?->legacy_user_id ?? 0;

            /** @var LeadDeliveryService $leadDeliveryService */
            $leadDeliveryService = app(LeadDeliveryService::class);

            $quoteCompanyId = $leadDeliveryService->saveQuoteCompanyForAppointment(
                $leadReference,
                $companyReference,
                $productCampaign->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID},
                $price,
                EloquentQuoteCompany::VALUE_SOLD_STATUS_SOLD,
                EloquentQuoteCompany::VALUE_CHARGE_STATUS_INITIAL,
                true,
                false,
                true,
                0,
                0,
                0,
                $legacyUserId,
                SaleTypesEnum::mapSaleTypeToLegacyId(SaleTypesEnum::mapAllocationsToVerifiedType($saleTypeId))
            );

            $leadDeliveryService->saveQuoteLogForAppointment(
                $leadReference,
                $legacyUserId,
                $processingAllocation->processing_scenario
            );

            DB::beginTransaction();

            $productAssignment = app(ProductAssignmentRepository::class)->updateOrCreateProductAssignment(
                [
                    ProductAssignment::FIELD_LEGACY_ID => $quoteCompanyId
                ],
                [
                    ProductAssignment::FIELD_COMPANY_ID => $companyId,
                    ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $apptConsumerProduct->{ConsumerProduct::FIELD_ID},
                    ProductAssignment::FIELD_COST => $price,
                    ProductAssignment::FIELD_CHARGEABLE => true,
                    ProductAssignment::FIELD_DELIVERED => false,
                    ProductAssignment::FIELD_EXCLUDE_BUDGET => false,
                    ProductAssignment::FIELD_SALE_TYPE_ID => $saleTypeId,
                    ProductAssignment::FIELD_CAMPAIGN_ID => $brsCampaign->get(BestRevenueScenarioDataModel::CAMPAIGN_ID),
                    ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID => $brsCampaign->get(BestRevenueScenarioDataModel::CAMPAIGN_BUDGET_ID)
                ]
            );

            $productAssignmentId = $productAssignment->{ProductAssignment::FIELD_ID};

            $appointment = $apptConsumerProduct->{ConsumerProduct::RELATION_APPOINTMENT};

            $this->createAppointmentDelivery(
                $productCampaign->{ProductCampaign::FIELD_ID},
                $productAssignmentId
            );

            $this->markAppointmentConsumerProductAndLeadAllocated(
                $appointment->{ProductAppointment::CONSUMER_PRODUCT_ID},
                $appointment->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID}
            );

            $consumer = $apptConsumerProduct->{ConsumerProduct::RELATION_CONSUMER};

            $this->appointmentCalendarIntegrationService->addEventToCalendar(
                $brsCampaign->get(BestRevenueScenarioDataModel::SCHEDULE_ID),
                $appointment->{ProductAppointment::APPOINTMENT_TYPE},
                "Appointment $productAssignmentId",
                Carbon::parse($appointment->{ProductAppointment::APPOINTMENT}, (string) $apptConsumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_UTC}),
                $consumer->getFullName(),
                $consumer->{Consumer::FIELD_EMAIL},
                $productAssignmentId
            );

            DB::commit();

            self::writeAppointmentLog(
                "Pushed appointment ({$consumerProductId}) to company ({$companyId})",
                [
                    "company_id" => $companyId,
                    "consumer_product_id" => $consumerProductId
                ]
            );

            return $companyId;
        }
        catch(Throwable $e) {
            if(DB::transactionLevel() > 0) {
                DB::rollBack(0);
            }

            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "consumer_product_id" => $consumerProductId,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param ConsumerProduct $apptConsumerProduct
     * @return bool
     */
    public function markAppointmentProcessingAllocationRejectedCancelled(ConsumerProduct $apptConsumerProduct): bool
    {
        AppointmentProcessingAllocation::query()
            ->where(AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $apptConsumerProduct->{ConsumerProduct::FIELD_ID})
            ->update([
                AppointmentProcessingAllocation::FIELD_CANCELLED_REJECTED => true
            ]);

        return true;
    }

    /**
     * @param int $chunk
     * @return bool
     */
    public function updateAppointmentLeadPricesToMatchSaleType(int $chunk = 50): bool
    {
        AppointmentProcessingAllocation::query()
            ->whereHas(AppointmentProcessingAllocation::RELATION_LEAD_PRODUCT_ASSIGNMENTS, function($has) {
                $has
                    ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::FIELD_DELIVERED, true)
                    ->groupBy(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
                    ->havingRaw("COUNT(DISTINCT ".ProductAssignment::FIELD_SALE_TYPE_ID.") > 1");
            })
            ->where(AppointmentProcessingAllocation::FIELD_ALLOCATED, true)
            ->with(AppointmentProcessingAllocation::RELATION_LEAD_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER)
            ->chunk($chunk, function($allocations) {
                foreach($allocations as $allocation) {
                    $this->updateLeadPriceToAppropriateSaleType($allocation->{AppointmentProcessingAllocation::RELATION_LEAD_CONSUMER_PRODUCT});
                }
            });

        return true;
    }

    /**
     * @param ConsumerProduct $leadConsumerProduct
     * @return bool
     * @throws Exception
     */
    protected function updateLeadPriceToAppropriateSaleType(ConsumerProduct $leadConsumerProduct): bool
    {
        self::writeAppointmentLog(
            "Updating lead price for consumer product {$leadConsumerProduct->{ConsumerProduct::FIELD_ID}} to appropriate sale type"
        );

        try {
            DB::beginTransaction();

            $leadId = $leadConsumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_LEGACY_ID};

            $leadSaleType = SaleTypesEnum::mapAllocationsToVerifiedType(
                $leadConsumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS} - $this->leadProcessingRepository->getRemainingLegsForAppointmentLead($leadId)
            );

            if(empty($leadSaleType)) {
                DB::rollBack();

                return false;
            }

            self::writeAppointmentLog(
                "Setting lead sale type {$leadSaleType->value} for consumer product {$leadConsumerProduct->{ConsumerProduct::FIELD_ID}}"
            );

            $appointmentProductId = $this->productRepository->getAppointmentProductId();

            $leadProductAssignments = ProductAssignment::query()
                ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $leadConsumerProduct->{ConsumerProduct::FIELD_ID})
                ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
                ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
                ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_PARENT_PRODUCT_ID, $appointmentProductId)
                ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_REJECTION_EXPIRY, '>', Carbon::now('UTC')->format(Carbon::DEFAULT_TO_STRING_FORMAT))
                ->get();

            $industry = IndustryEnum::from($leadConsumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME});

            /** @var Address $address */
            $address = $leadConsumerProduct->{ConsumerProduct::RELATION_ADDRESS};

            $countyKey = $this->databaseLocationRepository->getZipCode($address->{Address::FIELD_ZIP_CODE})->{Location::COUNTY_KEY};

            $propertyType = $this->leadConsumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD}[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value;
            $propertyType = ucwords(strtolower($propertyType));
            $propertyType = PropertyType::from($propertyType);

            $qualityTier = $industry === IndustryEnum::SOLAR ? QualityTier::PREMIUM : QualityTier::STANDARD;

            $leadSaleTypeId = SaleTypeModel::query()
                ->where(SaleTypeModel::FIELD_NAME, $leadSaleType->value)
                ->firstOrFail()
                ->{SaleTypeModel::FIELD_ID};

            $stateAbbr = StateAbbreviation::from(strtoupper($address->{Address::FIELD_STATE}));

            $updatedQuoteCompanies = [];
            foreach($leadProductAssignments as $leadProductAssignment) {
                $leadPrice = $this->getAppointmentLeadFloorPriceForCampaign(
                    $leadProductAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::FIELD_SERVICE_PRODUCT_ID},
                    $industry,
                    $propertyType,
                    $qualityTier,
                    $stateAbbr,
                    $leadSaleType,
                    $countyKey
                );

                if(empty($leadPrice)) {
                    logger()->error(__METHOD__.": No lead price");

                    continue;
                }

                if($leadProductAssignment->{ProductAssignment::FIELD_SALE_TYPE_ID} !== $leadSaleTypeId) {
                    $leadProductAssignment->{ProductAssignment::FIELD_COST} = $leadPrice;
                    $leadProductAssignment->{ProductAssignment::FIELD_SALE_TYPE_ID} = $leadSaleTypeId;

                    $leadProductAssignment->save();

                    $updatedQuoteCompanies[$leadProductAssignment->{ProductAssignment::FIELD_LEGACY_ID}] = $leadPrice;
                }
            }

            if(!empty($updatedQuoteCompanies)) {
                self::writeAppointmentLog(
                    "Updated lead prices for quote companies",
                    [
                        "quote_companies" => $updatedQuoteCompanies
                    ]
                );

                $this->leadDeliveryService->updateAppointmentLeadQuoteCompanyPrices($updatedQuoteCompanies, SaleTypesEnum::mapSaleTypeToLegacyId($leadSaleType));
            }

            DB::commit();

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "consumer_product_id" => $leadConsumerProduct->{ConsumerProduct::FIELD_ID},
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );
        }

        return false;
    }

    /**
     * @param ConsumerProduct $leadConsumerProduct
     * @param array $soldConsumerProducts
     * @param bool $multiIndustryLead
     * @return array
     * @throws BindingResolutionException
     * @throws Throwable
     */
    public function allocateAppointments(
        ConsumerProduct $leadConsumerProduct,
        array &$soldConsumerProducts,
        bool $multiIndustryLead = false
    ): array
    {
        $soldConsumerProducts = [];

        $dupeCheck = function() use ($leadConsumerProduct) {
            $potentialDuplicates = $this->consumerProductRepository
                ->getDuplicateConsumerProductsQuery($leadConsumerProduct)
                ->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function($has) {
                    $has
                        ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
                        ->orWhere(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true);
                })
                ->exists();

            if($potentialDuplicates) {
                throw new Exception("Duplicate appointment sale attempt: {$leadConsumerProduct->{ConsumerProduct::FIELD_ID}}");
            }
        };

        try {
            $dupeCheck();

            if (!$multiIndustryLead) {
                $brsService = BestRevenueScenarioServiceFactory::make(config('sales.brs_driver'));
            }
            else {
                $brsService = BestRevenueScenarioServiceFactory::make('odin');
            }

            $appointmentProcessingAllocations = $leadConsumerProduct
                ->{ConsumerProduct::RELATION_APPOINTMENT_PROCESSING_ALLOCATIONS}
                ->where(AppointmentProcessingAllocation::FIELD_ALLOCATE_AT, '<=', Carbon::now())
                ->where(AppointmentProcessingAllocation::FIELD_ALLOCATED, false)
                ->where(AppointmentProcessingAllocation::FIELD_DELIVERED, false)
                ->where(AppointmentProcessingAllocation::FIELD_ALLOCATED_AS_LEAD, false)
                ->where(AppointmentProcessingAllocation::FIELD_FAILED_APPT_ALLOCATION, false);

            $appointmentsRequested = $leadConsumerProduct->{ConsumerProduct::RELATION_APPOINTMENT_PROCESSING_ALLOCATIONS}->count();

            $finalSaleTypeId = null;
            $potentialCompanies = collect();
            $brsResults = collect();
            for($i = $appointmentsRequested; $i > 0; $i--) {
                $brsResults = $this->getBestRevenueScenarioResultsForAppointments($appointmentProcessingAllocations, $brsService, $i);

                if($brsResults->count() >= $i) {
                    $potentialCompanies = $this->permuteBRSResults($brsResults, $i);

                    if($potentialCompanies->count() >= $i) {
                        $finalSaleTypeId = $i;

                        break;
                    }
                }
            }

            if($finalSaleTypeId
            && $potentialCompanies->count() >= $finalSaleTypeId
            && count($brsResults) >= $finalSaleTypeId) {
                $dupeCheck();

                self::writeAppointmentLog(
                    "Found satisfactory sale type configuration for allocation: {$leadConsumerProduct->{ConsumerProduct::FIELD_ID}}",
                    [
                        "lead_consumer_product_id" => $leadConsumerProduct->{ConsumerProduct::FIELD_ID},
                        "final_sale_type" => $finalSaleTypeId,
                        "configuration" => $potentialCompanies->toArray()
                    ]
                );

                foreach($potentialCompanies as $apptConsumerProductId => $saleInfo) {
                    $companyId = $saleInfo->keys()->first();

                    $brsCampaign = null;

                    foreach($brsResults->get($apptConsumerProductId) as $campaign) {
                        if($campaign->get(BestRevenueScenarioDataModel::COMPANY_ID) === $companyId) {
                            $brsCampaign = $campaign;
                        }
                    }

                    $this->pushAppointmentToCompanyWithCalendar(
                        $brsCampaign,
                        $apptConsumerProductId,
                        $finalSaleTypeId
                    );

                    $soldConsumerProducts[] = $apptConsumerProductId;
                }
            }
            else {
                self::writeAppointmentLog(
                    "Couldn't find satisfactory sale type configuration for allocation: {$leadConsumerProduct->{ConsumerProduct::FIELD_ID}}",
                    [
                        "lead_consumer_product_id" => $leadConsumerProduct->{ConsumerProduct::FIELD_ID}
                    ]
                );
            }

            return $soldConsumerProducts;
        }
        catch(Throwable $e) {
            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "lead_consumer_product_id" => $leadConsumerProduct->{ConsumerProduct::FIELD_ID},
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            self::writeAppointmentLog(
                "Failed allocating appointments for {$leadConsumerProduct->{ConsumerProduct::FIELD_ID}}",
                ["lead_consumer_product_id" => $leadConsumerProduct->{ConsumerProduct::FIELD_ID}]
            );

            throw $e;
        }
    }

    /**
     * @param Collection $appointmentProcessingAllocations
     * @param BestRevenueScenarioServiceAbstract $brsService
     * @param int $saleTypeId
     * @return Collection
     * @throws Throwable
     */
    public function getBestRevenueScenarioResultsForAppointments(
        Collection $appointmentProcessingAllocations,
        BestRevenueScenarioServiceAbstract $brsService,
        int $saleTypeId
    )
    {
        $brsResults = collect();

        foreach($appointmentProcessingAllocations as $apa) {
            $apptConsumerProductId = $apa->{AppointmentProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID};

            self::writeAppointmentLog(
                "Obtaining appointment BRS: {$apptConsumerProductId}",
                [
                    "consumer_product_id" => $apptConsumerProductId,
                    "sale_type_id" => $saleTypeId
                ]
            );

            $brs = $brsService->getBestRevenueScenario($apptConsumerProductId, [], null, $saleTypeId);

            if(empty($brs)
            || $brs->isEmpty()) {
                self::writeAppointmentLog(
                    "No BRS campaigns found for {$apptConsumerProductId} for sale type {$saleTypeId}",
                    [
                        "consumer_product_id" => $apptConsumerProductId,
                        "sale_type_id" => $saleTypeId
                    ]
                );
            }
            else {
                $brsResults->put($apptConsumerProductId, $brs);

                self::writeAppointmentLog(
                    "Obtained appointment BRS: {$apptConsumerProductId}",
                    [
                        "consumer_product_id" => $apptConsumerProductId,
                        "sale_type_id" => $saleTypeId
                    ]
                );
            }
        }

        return $brsResults;
    }

    /**
     * @param Collection $brsResults
     * @param int $saleTypeId
     * @return Collection
     */
    public function permuteBRSResults(
        Collection $brsResults,
        int $saleTypeId
    ): Collection
    {
        $permutations = collect();
        $firstBrs = $brsResults->first();
        foreach($firstBrs->{BestRevenueScenarioDataModel::CAMPAIGNS} as $firstCampaign) {
            $potentialCompanies = collect([
                $firstBrs->{BestRevenueScenarioDataModel::CONSUMER_PRODUCT_ID} => collect([$firstCampaign->get(BestRevenueScenarioDataModel::COMPANY_ID) => $firstCampaign->get(BestRevenueScenarioDataModel::PRICE)])
            ]);

            foreach($brsResults as $brs) {
                if($brs->{BestRevenueScenarioDataModel::CONSUMER_PRODUCT_ID} !== $firstBrs->{BestRevenueScenarioDataModel::CONSUMER_PRODUCT_ID}) {
                    foreach($brs->{BestRevenueScenarioDataModel::CAMPAIGNS} as $brsCampaign) {
                        if($potentialCompanies->count() < $saleTypeId
                        && !$potentialCompanies->values()->contains(fn($e) => isset($e[$brsCampaign->get(BestRevenueScenarioDataModel::COMPANY_ID)]))) {
                            $potentialCompanies->put(
                                $brs->{BestRevenueScenarioDataModel::CONSUMER_PRODUCT_ID},
                                collect([$brsCampaign->get(BestRevenueScenarioDataModel::COMPANY_ID) => $brsCampaign->get(BestRevenueScenarioDataModel::PRICE)])
                            );

                            continue 2;
                        }
                    }
                }
            }

            if($potentialCompanies->count() >= $saleTypeId) {
                $permutations->push($potentialCompanies);
            }
        }

        $bestPotentialCompanies = collect();
        foreach($permutations as $permutation) {
            if($permutation->collapse()->sum() > $bestPotentialCompanies->collapse()->sum()) {
                $bestPotentialCompanies = $permutation;
            }
        }

        return $bestPotentialCompanies;
    }

    /**
     * @param Company $company
     * @return bool
     */
    public function hasSoldAppointments(Company $company): bool
    {
        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT, function($has) {
                $has->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value);
            })
            ->exists();
    }

    /**
     * @param string $message
     * @param array|null $payload
     * @param bool $sync
     * @return void
     */
    public static function writeAppointmentLog(string $message, ?array $payload = [], bool $sync = false): void
    {
        try {
            $payload['env'] = App::environment();

            if($sync) {
                RecordMonitoringLog::dispatchSync($message, $payload, LogMonitoringRepository::LOGGER_APPOINTMENTS);
            }
            else {
                RecordMonitoringLog::dispatch($message, $payload, LogMonitoringRepository::LOGGER_APPOINTMENTS);
            }
        }
        catch(Exception $e) {
            logger()->error($e->getMessage());
        }
    }
}
