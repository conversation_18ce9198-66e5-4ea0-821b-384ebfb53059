<?php

namespace App\Services\Odin\Appointments;

use App\Abstracts\ProductPricing\PriceTableDataModelAbstract;
use App\DataModels\Odin\Prices\FloorPriceTable;
use App\Enums\Odin\AppointmentCancellationReason;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes as SaleTypesEnum;
use App\Enums\Odin\StateAbbreviation;
use App\Enums\RejectionReasons;
use App\Events\ProductAssignment\ProductRejectedEvent;
use App\Exceptions\CustomMessageException;
use App\Jobs\Appointments\MultiIndustry\SendCancellationNoticeToCompanyJob;
use App\Jobs\Appointments\MultiIndustry\SendCancellationNoticeToConsumerJob;
use App\Mail\Appointments\MultiIndustry\NotifyCancellationCompany;
use App\Mail\Appointments\MultiIndustry\NotifyCancellationConsumer;
use App\Models\AppointmentDelivery;
use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignDeliveryMethod;
use App\Models\Legacy\LeadDeliveryMethod;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Models\User;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Legacy\DatabaseLocationRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Repositories\Odin\ProductRejectionRepository;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Delivery\Email;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\ProductPricing\ProductPricingServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Throwable;

class MultiIndustryAppointmentService extends AppointmentService
{
    public function cancelConsumerAppointment(string $apptKey, string $apptCode, AppointmentCancellationReason $cancellationReason, string $cancellationNote): bool
    {
        try {
            extract($this->getAppointmentDeliveryInfoForCancellation($apptKey, $apptCode));

            $leadPrice = $this->getPriceForTheCanceledAppointment(
                $this->findLeadServiceProductOrFail($consumerProduct)->id,
                $industry,
                $propertyType,
                $qualityTier,
                StateAbbreviation::from(strtoupper($address->{Address::FIELD_STATE})),
                $leadSaleType,
                $countyKey
            );

            $rejectionExpiry = $this->productRejectionRepository->getRejectionExpiry();

            $leadQuoteCompanyId = $this->leadDeliveryService->demoteAppointmentQuoteCompanyToLead(
                $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                $leadPrice,
                SaleTypesEnum::mapSaleTypeToLegacyId($leadSaleType),
                $productAssignment->{ProductAssignment::RELATION_CAMPAIGN}->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID},
                $leadProcessorUser->{User::FIELD_LEGACY_USER_ID},
                $leadProcessingScenario,
                true,
                false,
                $rejectionExpiry,
                ''
            );

            DB::beginTransaction();

            $leadProductAssignment = $this->demoteAppointmentAssignmentToLead(
                $productAssignment,
                $leadPrice,
                true,
                $rejectionExpiry,
                $leadQuoteCompanyId,
                $leadSaleType
            );

            if(empty($leadProductAssignment)) {
                throw new CustomMessageException('Error cancelling appointment');
            }

            $this->createAppointmentProductCancellation(
                $productAssignment->{ProductAssignment::FIELD_ID},
                $cancellationReason,
                $cancellationNote
            );

            $this->markAppointmentProcessingAllocationRejectedCancelled($consumerProduct);

            $companyName = $productAssignment->{ProductAssignment::RELATION_COMPANY}->{Company::FIELD_NAME};

            $appointment = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT};

            $tzOffset = (string) $address->{Address::FIELD_UTC};

            $appointmentDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $appointment, $tzOffset)->format("m/d/Y g:i A");

            SendCancellationNoticeToConsumerJob::dispatch(
                $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT},
                $companyName,
                $industry,
                $appointmentDateTime
            )->afterCommit();

            SendCancellationNoticeToCompanyJob::dispatch(
                $consumerProduct,
                $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN},
                $productAssignment,
                $companyName,
                $industry,
                $appointmentDateTime
            )->afterCommit();

            DB::commit();

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param int $productAssignmentId
     * @param int $companyId
     * @param RejectionReasons $rejectionReason
     * @param string $rejectionNotes
     *
     * @return bool
     * @throws CustomMessageException
     * @throws Throwable
     */
    public function rejectAppointment(int $productAssignmentId, int $companyId, RejectionReasons $rejectionReason, string $rejectionNotes): bool
    {
        try {
            extract($this->getProductAssignmentInfoForRejection($productAssignmentId, $companyId));

            $leadPrice = $this->getPriceForTheCanceledAppointment(
                $this->findLeadServiceProductOrFail($consumerProduct)->id,
                $industry,
                $propertyType,
                $qualityTier,
                StateAbbreviation::from(strtoupper($address->{Address::FIELD_STATE})),
                $leadSaleType,
                $countyKey
            );

            $leadQuoteCompanyId = app(LeadDeliveryService::class)->demoteAppointmentQuoteCompanyToLead(
                $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                $leadPrice,
                SaleTypesEnum::mapSaleTypeToLegacyId($leadSaleType),
                $productAssignment->{ProductAssignment::RELATION_CAMPAIGN}->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID},
                $leadProcessorUser->{User::FIELD_LEGACY_USER_ID},
                $leadProcessingScenario,
                false,
                true,
                0,
                "{$rejectionReason->value}|{$rejectionNotes}"
            );

            DB::beginTransaction();

            $productRejection = app(ProductRejectionRepository::class)->createProductRejection(
                $productAssignment->{ProductAssignment::FIELD_ID},
                $companyUser->{CompanyUser::FIELD_ID},
                "{$rejectionReason->value}|{$rejectionNotes}"
            );

            if(empty($productRejection)) {
                $errMsg = "Error rejecting appointment";

                throw new CustomMessageException($errMsg);
            }

            $leadProductAssignment = $this->demoteAppointmentAssignmentToLead(
                $productAssignment,
                $leadPrice,
                false,
                0,
                $leadQuoteCompanyId,
                $leadSaleType
            );

            if(empty($leadProductAssignment)) {
                $errMsg = "Error demoting appointment to lead";

                throw new CustomMessageException($errMsg);
            }

            $this->markAppointmentProcessingAllocationRejectedCancelled($consumerProduct);

            SendCancellationNoticeToConsumerJob::dispatch(
                $consumerProduct,
                $companyName,
                $industry,
                $appointmentDateTime
            )->afterCommit();

            DB::commit();

            ProductRejectedEvent::dispatch($productAssignment->{ProductAssignment::FIELD_ID});

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = __METHOD__.". ".$e->getFile().'. Line: '.$e->getLine().'. ';

            self::writeAppointmentLog(
                "Rejection err: ".$errLocation.substr($e->getMessage(), 0, 255),
                [
                    "product_assignment_id" => $productAssignmentId,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    public function getAppointmentDeliveryInfoForCancellation(string $apptKey, string $apptCode): array
    {
        $apptDelivery = $this->getAppointmentDeliveryByKeyAndCode(
            $apptKey,
            $apptCode,
            [
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT => function($with) {
                    $with
                        ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                        ->where(ProductAssignment::FIELD_DELIVERED, true);
                },
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_SALE_TYPE,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_APPOINTMENT,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER.'.'.Consumer::RELATION_LEGACY_LEAD,
                AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT.'.'.ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY,
                AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_LEGACY_CAMPAIGN => function($with) {
                    $with->select([
                        LeadCampaign::ID
                    ]);
                },
                AppointmentDelivery::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_LEGACY_CAMPAIGN.'.'.LeadCampaign::RELATION_LEAD_CAMPAIGN_DELIVERY_METHODS.'.'.LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD.'.'.LeadDeliveryMethod::RELATION_CONTACT => function($with) {
                    $with
                        ->where(EloquentCompanyContact::FIELD_STATUS, EloquentCompanyContact::STATUS_ACTIVE)
                        ->select([
                            EloquentCompanyContact::FIELD_CONTACT_ID,
                            EloquentCompanyContact::FIELD_COMPANY_ID,
                            EloquentCompanyContact::FIELD_PHONE,
                            EloquentCompanyContact::FIELD_MOBILE,
                            EloquentCompanyContact::FIELD_EMAIL,
                            EloquentCompanyContact::FIELD_FIRST_NAME,
                            EloquentCompanyContact::FIELD_LAST_NAME
                        ]);
                }
            ]
        );

        $productAssignment = $apptDelivery->{AppointmentDelivery::RELATION_PRODUCT_ASSIGNMENT};

        $consumerProduct = $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT};

        $industry = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME};

        /** @var Address $address */
        $address = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS};

        $countyKey = $this->databaseLocationRepository->getZipCode($address->{Address::FIELD_ZIP_CODE})->{Location::COUNTY_KEY};

        $propertyType = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD}[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value;
        $propertyType = ucwords(strtolower($propertyType));
        $propertyType = PropertyType::from($propertyType);

        $legacyLead = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::RELATION_LEGACY_LEAD};

        $qualityTier = $industry === IndustryEnum::SOLAR->value ? QualityTier::PREMIUM : QualityTier::STANDARD;

        $leadConsumerProduct = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT};

        $leadSaleType = SaleTypesEnum::mapAllocationsToVerifiedType(
            $leadConsumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS} - $this->leadProcessingRepository->getRemainingLegsForAppointmentLead($legacyLead->{EloquentQuote::ID})
        );

        $leadProcessorUser = $this->productProcessingRepository
            ->getProcessorsWhoAllocatedProducts(collect([$consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID}]))
            ->firstOrFail()
            ->{LeadProcessingHistory::RELATION_LEAD_PROCESSOR}
            ->{LeadProcessor::RELATION_USER};

        $leadProcessingScenario = $consumerProduct
            ->{ConsumerProduct::RELATION_APPOINTMENT}
            ->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT}
            ->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}
            ->{ConsumerProductData::FIELD_PAYLOAD}[ConsumerProductRepository::PRODUCT_DATA_KEY_STATUS_REASON] ?? '';

        return [
            self::FUNC_RET_APPT_DELIVERY => $apptDelivery,
            self::FUNC_RET_PRODUCT_ASSIGNMENT => $productAssignment,
            self::FUNC_RET_CONSUMER_PRODUCT => $consumerProduct,
            self::FUNC_RET_INDUSTRY => $industry,
            self::FUNC_RET_ADDRESS => $address,
            self::FUNC_RET_COUNTY_KEY => $countyKey,
            self::FUNC_RET_PROPERTY_TYPE => $propertyType,
            self::FUNC_RET_LEGACY_LEAD => $legacyLead,
            self::FUNC_RET_QUALITY_TIER => $qualityTier,
            self::FUNC_RET_LEAD_PROCESSOR_USER => $leadProcessorUser,
            self::FUNC_RET_LEAD_PROCESSING_SCENARIO => $leadProcessingScenario,
            self::FUNC_RET_LEAD_SALE_TYPE => $leadSaleType,
            self::FUNC_RET_LEAD_CONSUMER_PRODUCT => $leadConsumerProduct
        ];
    }

    /**
     * @param int $productAssignmentId
     * @param int $companyId
     * @return array
     * @throws Exception
     */
    private function getProductAssignmentInfoForRejection(int $productAssignmentId, int $companyId): array
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_ID, $productAssignmentId)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->with([
                ProductAssignment::RELATION_SALE_TYPE,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER.'.'.Consumer::RELATION_LEGACY_LEAD,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY,
                ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT,
                ProductAssignment::RELATION_COMPANY
            ])
            ->firstOrFail();

        if(Carbon::now() >= $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}) {
            $errMsg = "Rejection period has passed";

            throw new CustomMessageException($errMsg);
        }

        $consumerProduct = $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT};

        $rejectionPercentage = (float) app(ComputedRejectionStatisticRepository::class)->getCompanyAppointmentRejectionPercentage(
            $productAssignment->{ProductAssignment::RELATION_COMPANY}
        );

        if($rejectionPercentage >= (float) config('sales.appointments.overall_rejection_percentage_threshold')) {
            $errMsg = "Rejection limit exceeded";

            throw new CustomMessageException($errMsg);
        }

        if(empty($consumerProduct)) {
            $errMsg = "Invalid appointment";

            throw new CustomMessageException($errMsg);
        }

        $industry = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME};

        /** @var Address $address */
        $address = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS};

        $countyKey = app(DatabaseLocationRepository::class)->getZipCode($address->{Address::FIELD_ZIP_CODE})->{Location::COUNTY_KEY};

        $propertyType = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD}[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value;
        $propertyType = ucwords(strtolower($propertyType));
        $propertyType = PropertyType::from($propertyType);

        $legacyLead = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::RELATION_LEGACY_LEAD};

        $qualityTier = $industry === IndustryEnum::SOLAR->value ? QualityTier::PREMIUM : QualityTier::STANDARD;

        $companyName = $productAssignment->{ProductAssignment::RELATION_COMPANY}->{Company::FIELD_NAME};

        $leadConsumerProduct = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT};

        $leadSaleType = SaleTypesEnum::mapAllocationsToVerifiedType(
            $leadConsumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS} - $this->leadProcessingRepository->getRemainingLegsForAppointmentLead($legacyLead->{EloquentQuote::ID})
        );

        $appointmentDateTime = Carbon::createFromFormat(
            'Y-m-d H:i:s',
            $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT},
            (string) $address->{Address::FIELD_UTC}
        )->format("m/d/Y g:i A");

        /** @var DashboardAuthService $dashboardAuthService */
        $dashboardAuthService = app()->make(DashboardAuthService::class);

        $companyUser = CompanyUser::query()->findOrFail($dashboardAuthService->getUserId());

        $leadProcessorUser = app(ProductProcessingRepository::class)
            ->getProcessorsWhoAllocatedProducts(collect([$consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID}]))
            ->firstOrFail()
            ->{LeadProcessingHistory::RELATION_LEAD_PROCESSOR}
            ->{LeadProcessor::RELATION_USER};

        $leadProcessingScenario = $consumerProduct
            ->{ConsumerProduct::RELATION_APPOINTMENT}
            ->{ProductAppointment::RELATION_LEAD_CONSUMER_PRODUCT}
            ->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}
            ->{ConsumerProductData::FIELD_PAYLOAD}[ConsumerProductRepository::PRODUCT_DATA_KEY_STATUS_REASON] ?? '';

        return [
            self::FUNC_RET_PRODUCT_ASSIGNMENT => $productAssignment,
            self::FUNC_RET_CONSUMER_PRODUCT => $consumerProduct,
            self::FUNC_RET_INDUSTRY => $industry,
            self::FUNC_RET_ADDRESS => $address,
            self::FUNC_RET_COUNTY_KEY => $countyKey,
            self::FUNC_RET_PROPERTY_TYPE => $propertyType,
            self::FUNC_RET_LEGACY_LEAD => $legacyLead,
            self::FUNC_RET_QUALITY_TIER => $qualityTier,
            self::FUNC_RET_LEAD_PROCESSOR_USER => $leadProcessorUser,
            self::FUNC_RET_LEAD_PROCESSING_SCENARIO => $leadProcessingScenario,
            self::FUNC_RET_COMPANY_USER => $companyUser,
            self::FUNC_RET_APPOINTMENT_DATETIME => $appointmentDateTime,
            self::FUNC_RET_COMPANY_NAME => $companyName,
            self::FUNC_RET_LEAD_SALE_TYPE => $leadSaleType,
            self::FUNC_RET_LEAD_CONSUMER_PRODUCT => $leadConsumerProduct
        ];
    }

    public function sendConsumerCancellationConfirmation(ConsumerProduct $consumerProduct, Consumer $consumer, string $companyName, string $industry, string $appointmentDateTime, bool $isRejection): bool
    {
        $senderName = "Fixr";

        $companyName = $this->trimCompanyName($companyName);

        $textMessage = sprintf(
            "%s: Your appointment with %s was %s",
            $senderName,
            $companyName,
            $isRejection ? 'cancelled by the company' : 'cancelled'
        );

        if(!empty($consumer->{Consumer::FIELD_PHONE}) && $this->isProd) {
            $this->twilioCommunicationService->sendSMS($this->twilioFromNumber, $consumer->{Consumer::FIELD_PHONE}, $textMessage);
        }

        if(!empty($consumer->{Consumer::FIELD_EMAIL})) {
            Email::send(
                $this->isProd ? $consumer->{Consumer::FIELD_EMAIL} : config('app.outgoing_communication.test_email'),
                new NotifyCancellationConsumer(
                    $consumerProduct,
                    $consumerProduct->{ConsumerProduct::RELATION_CONSUMER},
                    $industry,
                    $companyName,
                    $appointmentDateTime,
                    $isRejection
                )
            );
        }

        return true;
    }

    public function sendCompanyCancellationNotice(ProductAssignment $productAssignment, ProductCampaign $campaign, ConsumerProduct $consumerProduct, Consumer $consumer, Address $address, string $companyName, string $industry, string $appointmentDateTime): bool
    {
        $detailsUrl = $this->getDashboardAppointmentUrl($productAssignment->{ProductAssignment::FIELD_ID}, $industry);

        $textMessage = sprintf(
            "Appointment for %s on %s was cancelled by the client\nDetails: %s",
            $consumer->getFullName(),
            $appointmentDateTime,
            $detailsUrl
        );

        $this->deliverCommunicationsToCampaign(
            $campaign,
            $textMessage,
            function($campaignDeliveryMethod) use ($consumerProduct, $address, $consumer, $industry, $companyName, $appointmentDateTime) {
                return new NotifyCancellationCompany(
                    $consumerProduct,
                    $address,
                    $consumer,
                    $industry,
                    $companyName,
                    $campaignDeliveryMethod->{LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD}?->{LeadDeliveryMethod::RELATION_CONTACT}?->name ?? "",
                    $appointmentDateTime
                );
            }
        );

        return true;
    }

    public function getPriceForTheCanceledAppointment(int $serviceProductId, string $industry, PropertyType $propertyType, QualityTier $qualityTier, StateAbbreviation $stateAbbreviation, SaleTypesEnum $appointmentSaleType, string $countyKey): float
    {
        $productPricingService = ProductPricingServiceFactory::make();

        $floorTable = $productPricingService->getFloorTable(
            $serviceProductId,
            ProductEnum::LEAD,
            $industry,
            $propertyType,
            $stateAbbreviation,
            [$countyKey],
            $qualityTier,
            PriceTableDataModelAbstract::APPOINTMENT_SALES_TYPES
        )->toArray();

        $stateAbbr = $stateAbbreviation->value;
        $saleType = $appointmentSaleType->value;

        $stateFloorPrice = $floorTable[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_SALES_TYPES][$saleType][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;
        $countyFloorPrice = $floorTable[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_COUNTIES][$countyKey][FloorPriceTable::ARRAY_KEY_SALES_TYPES][$saleType][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;

        return round($countyFloorPrice ?? $stateFloorPrice ?? 0.00, 2);
    }

    protected function findLeadServiceProductOrFail(ConsumerProduct $consumerProduct): ServiceProduct
    {
        if ($consumerProduct->serviceProduct->product->name !== ProductEnum::LEAD->value) {
            /** @var ServiceProduct */
            return ServiceProduct::query()
                ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $consumerProduct->serviceProduct->industry_service_id)
                ->whereHas(ServiceProduct::RELATION_PRODUCT, function (Builder $query) {
                    $query->where(Product::FIELD_NAME, ProductEnum::LEAD);
                })->firstOrFail();
        }

        return $consumerProduct->serviceProduct;
    }
}
