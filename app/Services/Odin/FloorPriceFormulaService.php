<?php

namespace App\Services\Odin;

use App\DataModels\Odin\Prices\FloorPriceTable;
use App\Enums\Odin\Industry;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Models\Odin\FloorPriceFormula;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductProcessing\FloorPriceFormulaRepository;

class FloorPriceFormulaService
{
    const CASCADE_FORMULAS_TO_COUNTIES_KEY = 'apply_to_all_counties';
    const STATE_KEY                        = 'state_key';
    const REGION_TYPE_STATE                = 'state';
    const REGION_TYPE_COUNTY               = 'county';
    const REGION_TYPE_KEY                  = 'region_type';
    const COUNTY_KEY                       = 'county_key';

    /**
     * @param FloorPriceTable $basePriceTable
     */
    public function __construct(public FloorPriceTable $basePriceTable) {}

    /**
     * @param FloorPriceFormula $formula
     * @param string $stateAbbr
     * @param string|null $countyKey
     * @return float
     */
    public function resolveFormula(FloorPriceFormula $formula, string $stateAbbr, ?string $countyKey = null): float
    {
        return $formula->multiplier_value * $this->resolveBasePrice($formula, $stateAbbr, $countyKey);
    }

    /**
     * @param FloorPriceFormula $formula
     * @param float $basePrice
     *
     * @return float
     */
    public static function resolveFormulaForBasePrice(FloorPriceFormula $formula, float $basePrice): float
    {
        if($formula->multiplier_type === FloorPriceFormula::MULTIPLIER_TYPE_MAX_REVENUE){
            $basePrice = $basePrice * self::staticGetMaxAllocationsBySalesType($formula->multiplier_sales_type);
        }

        return $formula->multiplier_value * $basePrice;
    }

    /**
     * @param string $salesType
     *
     * @return int
     */
    private static function staticGetMaxAllocationsBySalesType (string $salesType): int
    {
        return match ($salesType) {
            SaleTypes::EXCLUSIVE->value => 1,
            SaleTypes::DUO->value => 2,
            SaleTypes::TRIO->value => 3,
            default => 4
        };
    }

    /**
     * @param FloorPriceFormula $formula
     * @param string $stateAbbr
     * @param string|null $countyKey
     * @return float
     */
    private function resolveBasePrice(FloorPriceFormula $formula, string $stateAbbr, ?string $countyKey = null): float
    {
        // for multiplier type 'Rate', this is the base price
        $basePrice =  $this->basePriceTable->getPrice($formula->multiplier_quality_tier, $formula->multiplier_sales_type, $stateAbbr, $countyKey);

        // for multiplier type 'Max Revenue', we multiply by the sales type max allocation count
        if($formula->multiplier_type === FloorPriceFormula::MULTIPLIER_TYPE_MAX_REVENUE){
            $basePrice = $basePrice * $this->getMaxAllocationsBySalesType($formula->multiplier_sales_type);
        }
        return $basePrice;
    }

    /**
     * @param string $salesType
     * @return int
     */
    private function getMaxAllocationsBySalesType(string $salesType): int
    {
        return match ($salesType) {
            SaleTypes::EXCLUSIVE->value => 1,
            SaleTypes::DUO->value => 2,
            SaleTypes::TRIO->value => 3,
            default => 4
        };
    }

    /**
     * @param Product $product
     * @param Industry $industry
     * @param PropertyType $propertyType
     * @param QualityTier $qualityTier
     * @param array $data
     * @return void
     */
    public function updateOrCreateFormulas(Product $product, Industry $industry, PropertyType $propertyType, QualityTier $qualityTier, array $data): void
    {
        /** @var FloorPriceFormulaRepository $repository */
        $repository = app(FloorPriceFormulaRepository::class);

        /** @var LocationRepository $locationRepository */
        $locationRepository = app(LocationRepository::class);

        foreach ($data as $region){
            $regionType = $region[self::REGION_TYPE_KEY];

            // This argument only applies to state formulas, if set to true, we will cascade the state formulas to all child counties
            $cascadeFormulasToCounties =
                $regionType === self::REGION_TYPE_STATE &&
                key_exists(self::CASCADE_FORMULAS_TO_COUNTIES_KEY, $region) &&
                $region[self::CASCADE_FORMULAS_TO_COUNTIES_KEY] == true;

            // This is the location (state or county) where the original formula update was targeted
            $primaryLocation = match ($regionType){
                self::REGION_TYPE_STATE => $locationRepository->getState($region['key']),
                self::REGION_TYPE_COUNTY => $locationRepository->getCounty($region[self::STATE_KEY], $region[self::COUNTY_KEY])
            };

            $allLocations = collect();
            // Add supplemental locations i.e. Add cascaded counties for state
            if($cascadeFormulasToCounties){$allLocations = $locationRepository->getCountiesInState($region['key']);}
            $allLocations->push($primaryLocation);

            foreach ($region['sales_types'] as $salesType => $salesTypeData){
                $salesType = SaleTypes::from($salesType);
                $formula = $salesTypeData['formula'];
                foreach ($allLocations as $location){
                    $repository->updateOrCreateFormula([
                        FloorPriceFormula::FIELD_PRODUCT => $product->value,
                        FloorPriceFormula::FIELD_INDUSTRY => $industry->value,
                        FloorPriceFormula::FIELD_PROPERTY_TYPE => $propertyType->value,
                        FloorPriceFormula::FIELD_QUALITY_TIER => $qualityTier->value,
                        FloorPriceFormula::FIELD_SALES_TYPE => $salesType->value,
                        FloorPriceFormula::FIELD_LOCATION_ID => $location->id
                    ],[
                        FloorPriceFormula::FIELD_MULTIPLIER_VALUE => $formula[FloorPriceFormula::FIELD_MULTIPLIER_VALUE],
                        FloorPriceFormula::FIELD_MULTIPLIER_TYPE => $formula[FloorPriceFormula::FIELD_MULTIPLIER_TYPE],
                        FloorPriceFormula::FIELD_MULTIPLIER_QUALITY_TIER => $formula[FloorPriceFormula::FIELD_MULTIPLIER_QUALITY_TIER],
                        FloorPriceFormula::FIELD_MULTIPLIER_SALES_TYPE => $formula[FloorPriceFormula::FIELD_MULTIPLIER_SALES_TYPE]
                    ]);
                }
            }
        }
    }
}
