<?php

namespace App\Services\Odin;

use App\Enums\CompanyMediaAssetType;
use App\Models\Legacy\EloquentMedia;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyMediaAsset;
use App\Repositories\Legacy\MediaRepository;
use App\Services\Companies\CompanyProfileService;
use App\Services\FileUploadHelperService;
use Carbon\Carbon;
use Exception;
use Google\Cloud\Core\Exception\BadRequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class MigrateLegacyCompanyMediaService
{
    /**
     * @param MediaRepository       $legacyMediaRepository
     * @param CompanyProfileService $profileService
     */
    public function __construct(
        protected MediaRepository       $legacyMediaRepository,
        protected CompanyProfileService $profileService,
    ) {}

    /**
     * @param Collection $legacyMediaItems
     * @return void
     */
    public function handle(Collection $legacyMediaItems): void
    {
        $companyMediaAssets = [];

        /** @var EloquentMedia $legacyMediaItem */
        foreach ($legacyMediaItems as $legacyMediaItem) {

            /** @var Company|null $company */
            $company = Company::query()
                ->where(Company::FIELD_LEGACY_ID, $legacyMediaItem->{EloquentMedia::REL_ID})
                ->first();

            if (!empty($company)) {
                $fileNameToUpload = FileUploadHelperService::encryptFileName($legacyMediaItem->{EloquentMedia::REAL_NAME}, true);

                $companyMediaAssetBaseCriteria = [
                    CompanyMediaAsset::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                    CompanyMediaAsset::FIELD_URL        => $this->prepareURLForLegacyMediaItem($company, $fileNameToUpload),
                ];

                /** @var CompanyMediaAsset|null $alreadyAdded */
                $alreadyAdded = CompanyMediaAsset::query()->where($companyMediaAssetBaseCriteria)->first();
                if(!$alreadyAdded) {
                    try {
                        $url = $this->uploadLegacyMediaItemToCloud($company, $legacyMediaItem, $fileNameToUpload);
                        if (!is_null($url) && strlen(trim($url)) > 0) {
                            $companyMediaAssets[] = [
                                ...$companyMediaAssetBaseCriteria,
                                CompanyMediaAsset::FIELD_NAME       => $legacyMediaItem->{EloquentMedia::REAL_NAME},
                                CompanyMediaAsset::FIELD_TYPE       => $this->determineTypeForLegacyMediaItem($legacyMediaItem->{EloquentMedia::TYPE})->value,
                                CompanyMediaAsset::FIELD_CREATED_AT => Carbon::createFromTimestamp($legacyMediaItem->{EloquentMedia::TIMESTAMP_ADDED}),
                                CompanyMediaAsset::FIELD_UPDATED_AT => Carbon::createFromTimestamp($legacyMediaItem->{EloquentMedia::TIMESTAMP_UPDATED}),
                            ];
                        } else {
                            logger()->error("Skipping the legacy Media: `{$legacyMediaItem->{EloquentMedia::ID}}` since it couldn't be uploaded to the cloud.");
                        }
                    } catch (Throwable $e) {
                        logger()->error("Something went wrong while uploading the legacy Media: `{$legacyMediaItem->{EloquentMedia::ID}}`. Exception: " . $e->getMessage()."\n\n");
                    }
                }
            } else {
                logger()->error("Company doesn't exist for the legacy Media: `{$legacyMediaItem->{EloquentMedia::ID}}`");
            }
        }

        if(!empty($companyMediaAssets)) {
            DB::table(CompanyMediaAsset::TABLE)->insert($companyMediaAssets);
        }
    }

    /**
     * Handles determining the type of the legacy media item as per the protocol defined in A2.
     *
     * @param string $type
     * @return CompanyMediaAssetType
     */
    protected function determineTypeForLegacyMediaItem(string $type): CompanyMediaAssetType
    {
        return str_contains($type, 'image/') || str_contains($type, 'video/')
            ? CompanyMediaAssetType::MEDIA
            : CompanyMediaAssetType::ATTACHMENT;
    }

    /**
     * Handles preparing the cloud storage URL for the given file name against the requested company.
     *
     * @param Company $company
     * @param string  $fileName
     * @return string
     */
    protected function prepareURLForLegacyMediaItem(Company $company, string $fileName): string
    {
        $path   = $this->profileService->getMediaUploadPath($company);
        $url    = config('services.google.storage.urls.company_logos_file_url');
        $bucket = config('services.google.storage.buckets.company_logos');

        return rtrim($url, '/') . "/{$bucket}/{$path}/{$fileName}";
    }

    /**
     * Handles uploading the given legacy file to cloud.
     *
     * @param Company       $company
     * @param EloquentMedia $legacyMediaItem
     * @param string        $fileNameToUpload
     * @return string|null
     * @throws BadRequestException
     * @throws Exception
     */
    protected function uploadLegacyMediaItemToCloud(Company $company, EloquentMedia $legacyMediaItem, string $fileNameToUpload): ?string
    {
        $legacyMediaFilePath = $this->legacyMediaRepository->getLegacyMediaURLForCompany(
            legacyCompany : $company->{Company::FIELD_LEGACY_ID},
            fileName      : $legacyMediaItem->{EloquentMedia::REAL_NAME}
        );

        $path = $this->profileService->getMediaUploadPath($company);
        $file = FileUploadHelperService::createFromUrl(
            url            : $legacyMediaFilePath,
            originalName   : $legacyMediaItem->{EloquentMedia::REAL_NAME},
            mimeType       : $legacyMediaItem->{EloquentMedia::TYPE},
            throwException : false,
        );

        return $this->profileService->uploadInCloud($path, $file, $fileNameToUpload);
    }
}
