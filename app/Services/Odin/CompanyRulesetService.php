<?php

namespace App\Services\Odin;

use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\CompanyUser;
use App\Models\Ruleset;
use App\Services\Odin\Ruleset\Factories\RulesFactory;
use App\Services\Odin\Ruleset\RulesetService;
use App\Services\Odin\Ruleset\SourceFilters\CompanyRulesetSourceFilterBuilder;
use Illuminate\Contracts\Database\Eloquent\Builder;

class CompanyRulesetService
{

    public function generateRulesetService(Ruleset $ruleset): RulesetService
    {
        $rulesetService = new RulesetService($ruleset, RulesFactory::getCompanyRules($ruleset));

        $rulesetService->validate();

        return $rulesetService;
    }

    /**
     * @param Ruleset $ruleset
     * @param string[] $selectFields
     * @return Builder
     */
    public function getCompanyRulesetQuery(Ruleset $ruleset, array $selectFields = [Company::TABLE . '.*']): \Illuminate\Contracts\Database\Eloquent\Builder
    {
        $rulesetService = $this->generateRulesetService($ruleset);

        $companyQuery = CompanyRulesetSourceFilterBuilder::query()->getQuery($ruleset->{Ruleset::FIELD_FILTER}, Company::query()->select($selectFields)->distinct());

        return $rulesetService->addRuleQueryConditions($companyQuery);
    }

    /**
     * @param Ruleset $ruleset
     * @return Builder
     */
    public function getOpportunityNotificationRulesetQuery(Ruleset $ruleset): Builder
    {
        return $this->getCompanyRulesetQuery($ruleset)
            ->whereHas(Company::RELATION_ACCOUNT_MANAGER_CLIENTS)
            ->whereHas(Company::RELATION_CONFIGURATION, function ($query) {
                $query->where(CompanyConfiguration::FIELD_MISSED_PRODUCTS_ACTIVE, true);
            })
            ->where(function ($query) {
                $query->selectRaw('COUNT(' . CompanyUser::TABLE . '.' . CompanyUser::FIELD_ID .')')
                    ->from(CompanyUser::TABLE)
                    ->whereRaw(CompanyUser::TABLE.'.'.CompanyUser::FIELD_COMPANY_ID . ' = ' . Company::TABLE . '.' . Company::FIELD_ID)
                    ->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_IS_CONTACT, 1)
                    ->whereNotNull(CompanyUser::TABLE . '.' . CompanyUser::FIELD_EMAIL)
                    ->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_STATUS, EloquentUser::FIELD_STATUS_VALUE_ACTIVE)
                    ->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_CAN_RECEIVE_PROMOTIONS, 1)
                    ->groupBy(CompanyUser::TABLE . '.' . CompanyUser::FIELD_COMPANY_ID);
        }, '>', 0);
    }
}
