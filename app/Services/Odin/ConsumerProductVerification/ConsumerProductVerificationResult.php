<?php

namespace App\Services\Odin\ConsumerProductVerification;

use Illuminate\Contracts\Support\Arrayable;

class ConsumerProductVerificationResult implements Arrayable
{
    /**
     * The driver that was used to verify this consumer product
     *
     * @var string
     */
    public string $driver;

    /**
     * The confidence of this verification result
     * Usually a result between 0-500, where 500 is the least confident
     *
     * @var int
     */
    public int $confidence;

    /**
     * Deals with whether the phone number provided is valid
     *
     * @var bool
     */
    public bool $phone_is_valid;

    /**
     * Deals with whether the phone number provided matches the name provided
     * in the consumer product
     *
     * @var bool|null
     */
    public ?bool $phone_name_match;

    /**
     * The name of the subscriber of this phone
     *
     * @var string|null
     */
    public ?string $phone_subscriber_name;

    /**
     * The address match result of this consumer product verification
     *
     * @var string|null
     */
    public ?string $phone_address_match;

    /**
     * The line type of the phone
     *
     * @var string|null
     */
    public ?string $phone_line_type;

    /**
     * The country code of the phone
     *
     * @var string|null
     */
    public ?string $phone_country_code;

    /**
     * The name of the phone carrier
     *
     * @var string|null
     */
    public ?string $phone_carrier;

    /**
     * Deals with whether this phone has been registered for business use
     *
     * @var bool|null
     */
    public ?bool $phone_is_commercial;

    /**
     * Deals with whether this phone is a prepaid phone
     *
     * @var bool|null
     */
    public ?bool $phone_is_prepaid;

    /**
     * An array of warnings provided about this phone
     *
     * @var string[]|null
     */
    public ?array $phone_warnings;

    /**
     * Deals with whether the address provided is valid
     *
     * @var bool
     */
    public bool $address_is_valid;

    /**
     * Deals with whether the address provided matches the name provided in the consumer product
     *
     * @var bool|null
     */
    public ?bool $address_name_match;

    /**
     * The name of the registered owner of this address
     *
     * @var string|null
     */
    public ?string $address_resident_name;

    /**
     * The type of building that exists on this address
     *
     * @var string|null
     */
    public ?string $address_type;

    /**
     * Deals with whether this address has been registered for business use
     *
     * @var bool|null
     */
    public ?bool $address_is_commercial;

    /**
     * Deals with whether the address is performing freight forwarding or reshipping services
     *
     * @var bool|null
     */
    public ?bool $address_is_forwarder;

    /**
     * An array of warnings provided for this address
     *
     * @var string[]|null
     */
    public ?array $address_warnings;

    /**
     * Deals with whether the email address provided is valid
     *
     * @var bool
     */
    public bool $email_is_valid;

    /**
     * Deals with whether the email provided matches the name provided in the consumer product
     *
     * @var bool|null
     */
    public ?bool $email_name_match;

    /**
     * The name that is registered with this email
     *
     * @var string|null
     */
    public ?string $email_registered_name;

    /**
     * The days this email has been registered for
     *
     * @var int|null
     */
    public ?int $email_age;

    /**
     * Deals with whether the email provided is auto generated or not
     *
     * @var bool|null
     */
    public ?bool $email_is_autogenerated;

    /**
     * Deals with whether the email provided is a disposable email
     *
     * @var bool|null
     */
    public ?bool $email_is_disposable;

    /**
     * An array of warnings that were provided for this email
     *
     * @var string[]|null
     */
    public ?array $email_warnings;

    /**
     * Deals with whether the ip address provided is a valid ip address
     *
     * @var bool
     */
    public bool $ip_is_valid;

    /**
     * Deals with whether the ip provided has matched the name provided in the consumer product
     *
     * @var bool|null
     */
    public ?bool $ip_name_match;

    /**
     * The rough location of this ip address
     *
     * @var string|null
     */
    public ?string $ip_geolocation;

    /**
     * The estimated distance in miles from the address provided in the consumer product, to the location
     * of this ip address
     *
     * @var string|null
     */
    public ?string $ip_address_distance;

    /**
     * The estimated distance in miles from the phone provided in the consumer product, to the location
     * of this ip address
     *
     * @var string|null
     */
    public ?string $ip_phone_distance;

    /**
     * Deals with whether the ip provided has a risk of being behind a proxy
     *
     * @var bool|null
     */
    public ?bool $ip_is_proxy;

    /**
     * An array of warnings provided for this ip address
     *
     * @var string[]|null
     */
    public ?array $ip_warnings;

    /**
     * ConsumerProductVerificationResult constructor.
     *
     * @param array $properties
     */
    public function __construct(array $properties = [])
    {
        foreach ($properties as $key => $property) {
            if (property_exists($this, $key)) {
                $this->{$key} = $property;
            }
        }
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return (array) $this;
    }
}
