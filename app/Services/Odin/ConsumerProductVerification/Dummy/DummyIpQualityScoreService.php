<?php

namespace App\Services\Odin\ConsumerProductVerification\Dummy;

use App\Enums\Odin\VerificationServiceTypes;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationServiceInterface;
use App\Services\Odin\ConsumerProductVerification\IpQuality\IpQualityScoreService;
use App\Services\Odin\ConsumerProductVerification\IPQualityScoreResult;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class DummyIpQualityScoreService implements ConsumerProductVerificationServiceInterface
{
    /**
     * @param ConsumerProduct $consumerProduct
     * @return IPQualityScoreResult
     */
    public function verifyConsumerProduct(ConsumerProduct $consumerProduct): IPQualityScoreResult
    {
        $result              = new IPQualityScoreResult();
        $result->driver      = VerificationServiceTypes::DUMMY->value;
        $result->email       = $this->prepareEmailData();
        $result->phone       = $this->preparePhoneData();
        $result->transaction = $this->prepareTransactionData();

        return $result;
    }

    /**
     * @return array
     */
    protected function prepareEmailData(): array
    {
        return [
            IpQualityScoreService::EMAIL_DELIVERABILITY      => Arr::random(['high', 'medium', 'low']),
            IpQualityScoreService::EMAIL_FRAUD_SCORE         => rand(1, 100),
            IpQualityScoreService::EMAIL_FREQUENT_COMPLAINER => rand(0, 100) < 50,
            IpQualityScoreService::EMAIL_USER_ACTIVITY       => 'Enterprise L4+ required',
            IpQualityScoreService::EMAIL_ASSOCIATED_NAMES    => ['names' => [Str::random(10)]],
            IpQualityScoreService::EMAIL_ASSOCIATED_NUMBERS  => ['phone_numbers' => ['1999999999']],
            IpQualityScoreService::EMAIL_FIRST_SEEN          => ['human' => 'just now']
        ];
    }

    /**
     * @return array
     */
    protected function preparePhoneData(): array
    {
        return [
            IpQualityScoreService::PHONE_VALID             => rand(0, 100) < 50,
            IpQualityScoreService::PHONE_NAME              => Str::random(10),
            IpQualityScoreService::PHONE_LINE_TYPE         => 'Unknown',
            IpQualityScoreService::PHONE_USER_ACTIVITY     => 'Enterprise L4+ required',
            IpQualityScoreService::PHONE_ASSOCIATED_EMAILS => ['emails' => ['<EMAIL>']]
        ];
    }

    /**
     * @return array
     */
    protected function prepareTransactionData(): array
    {
        return [
            IpQualityScoreService::TRANSACTION_DETAILS       => [
                IpQualityScoreService::TRANSACTION_RISK_SCORE              => rand(1, 100),
                IpQualityScoreService::TRANSACTION_RISK_FACTOR             => ["Phone number is invalid."],
                IpQualityScoreService::TRANSACTION_EMAIL_NAME_IDENTITY     => Arr::random(['No match', 'Unknown']),
                IpQualityScoreService::TRANSACTION_NAME_ADDRESS_IDENTITY   => Arr::random(['No match', 'Unknown']),
                IpQualityScoreService::TRANSACTION_PHONE_NAME_IDENTITY     => Arr::random(['No match', 'Unknown']),
                IpQualityScoreService::TRANSACTION_PHONE_ADDRESS_IDENTITY  => Arr::random(['No match', 'Unknown']),
                IpQualityScoreService::TRANSACTION_PHONE_EMAIL_IDENTITY    => Arr::random(['No match', 'Unknown'])
            ],
            IpQualityScoreService::TRANSACTION_FRAUD_SCORE   => rand(1, 100),
            IpQualityScoreService::TRANSACTION_RECENT_ABUSE  => rand(0, 100) < 50
        ];
    }
}
