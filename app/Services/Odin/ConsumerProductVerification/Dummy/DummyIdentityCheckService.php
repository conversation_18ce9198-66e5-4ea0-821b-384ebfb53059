<?php

namespace App\Services\Odin\ConsumerProductVerification\Dummy;

use App\Enums\Odin\VerificationServiceTypes;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationServiceInterface;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationResult;

class DummyIdentityCheckService implements ConsumerProductVerificationServiceInterface
{
    /**
     * Verifies a given consumer product.
     *
     * @param ConsumerProduct $consumerProduct
     * @return ConsumerProductVerificationResult
     */
    public function verifyConsumerProduct(ConsumerProduct $consumerProduct): ConsumerProductVerificationResult
    {
        $result                   = new ConsumerProductVerificationResult();
        $result->driver           = VerificationServiceTypes::DUMMY->value;
        $result->confidence       = rand(1, 500);
        $result->phone_is_valid   = rand(0, 100) < 50;
        $result->phone_name_match = rand(0, 100) < 50;
        $result->address_is_valid = rand(0, 100) < 50;
        $result->email_is_valid   = rand(0, 100) < 50;
        $result->ip_is_valid      = rand(0, 100) < 50;

        return $result;
    }
}
