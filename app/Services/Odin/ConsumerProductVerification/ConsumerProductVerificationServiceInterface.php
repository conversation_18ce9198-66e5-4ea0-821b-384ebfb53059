<?php

namespace App\Services\Odin\ConsumerProductVerification;

use App\Models\Odin\ConsumerProduct;

interface ConsumerProductVerificationServiceInterface
{
    /**
     * Verifies a given consumer product.
     *
     * @param ConsumerProduct $consumerProduct
     * @return ConsumerProductVerificationResult|IPQualityScoreResult
     */
    public function verifyConsumerProduct(ConsumerProduct $consumerProduct): ConsumerProductVerificationResult|IPQualityScoreResult;
}
