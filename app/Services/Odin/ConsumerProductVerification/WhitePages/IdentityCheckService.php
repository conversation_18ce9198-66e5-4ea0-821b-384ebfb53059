<?php

namespace App\Services\Odin\ConsumerProductVerification\WhitePages;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\VerificationServiceTypes;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationResult;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationServiceInterface;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;

class IdentityCheckService implements ConsumerProductVerificationServiceInterface
{
    const WHITEPAGES_API_KEY    = 'api_key';
    const WHITEPAGES_STREET_1   = 'primary.address.street_line_1';
    const WHITEPAGES_STREET_2   = 'primary.address.street_line_2';
    const WHITEPAGES_COUNTRY    = 'primary.address.country_code';
    const WHITEPAGES_CITY       = 'primary.address.city';
    const WHITEPAGES_STATE      = 'primary.address.state_code';
    const WHITEPAGES_ZIP        = 'primary.address.postal_code';
    const WHITEPAGES_PHONE      = 'primary.phone';
    const WHITEPAGES_EMAIL      = 'primary.email_address';
    const WHITEPAGES_FIRST_NAME = 'primary.firstname';
    const WHITEPAGES_LAST_NAME  = 'primary.lastname';
    const WHITEPAGES_IP_ADDRESS = 'ip_address';

    const RESPONSE_IS_VALID         = 'is_valid';
    const RESPONSE_IS_COMMERCIAL    = 'is_commercial';
    const RESPONSE_WARNINGS         = 'warnings';
    const RESPONSE_MATCH_TO_NAME    = 'match_to_name';
    const RESPONSE_MATCH_TO_ADDRESS = 'match_to_address';
    const RESPONSE_TYPE             = 'type';
    const RESPONSE_CONFIDENCE       = 'identity_check_score';
    const RESPONSE_NAME             = 'name';

    const RESPONSE_PHONE              = 'primary_phone_checks';
    const RESPONSE_PHONE_COUNTRY_CODE = 'country_code';
    const RESPONSE_PHONE_LINE_TYPE    = 'line_type';
    const RESPONSE_PHONE_CARRIER      = 'carrier';
    const RESPONSE_PHONE_IS_PREPAID   = 'is_prepaid';
    const RESPONSE_PHONE_SUBSCRIBER   = 'subscriber';

    const RESPONSE_ADDRESS              = 'primary_address_checks';
    const RESPONSE_ADDRESS_RESIDENT     = 'resident';
    const RESPONSE_ADDRESS_IS_FORWARDER = 'is_forwarder';

    const RESPONSE_EMAIL                  = 'primary_email_address_checks';
    const RESPONSE_EMAIL_IS_AUTOGENERATED = 'is_autogenerated';
    const RESPONSE_EMAIL_IS_DISPOSABLE    = 'is_disposable';
    const RESPONSE_EMAIL_FIRST_SEEN       = 'email_first_seen_days';
    const RESPONSE_EMAIL_OWNER            = 'registered_owner';

    const RESPONSE_IP                  = 'ip_address_checks';
    const RESPONSE_IP_PROXY            = 'proxy_risk';
    const RESPONSE_IP_GEOLOCATION      = 'geolocation';
    const RESPONSE_IP_ADDRESS_DISTANCE = 'distance_from_primary_address';
    const RESPONSE_IP_PHONE_DISTANCE   = 'distance_from_primary_phone';
    const RESPONSE_IP_NAME_MATCH       = 'match_to_primary_name';

    const RESPONSE_GEOLOCATION_CITY     = 'city_name';
    const RESPONSE_GEOLOCATION_STATE    = 'subdivision';
    const RESPONSE_GEOLOCATION_ZIP_CODE = 'postal_code';

    const RESPONSE_FOUND     = 'Match';
    const RESPONSE_NOT_FOUND = 'No name found';

    /** @var string */
    protected string $apiKey;

    /** @var string */
    protected string $apiUrl;

    public function __construct()
    {
        $this->apiKey  = config('services.whitepages.identity_verification.api_key');
        $this->apiUrl  = config('services.whitepages.identity_verification.api_url');
    }

    /**
     * @inheritDoc
     */
    public function verifyConsumerProduct(ConsumerProduct $consumerProduct): ConsumerProductVerificationResult
    {
        $response = $this->sendRequest($consumerProduct);

        return $this->createConsumerProductVerificationResult($response->collect());
    }

    /**
     * Makes request to the 3rd party service to gather verification data for a given product.
     *
     * @param ConsumerProduct $consumerProduct
     * @return Response
     */
    protected function sendRequest(ConsumerProduct $consumerProduct): Response
    {
        return Http::get($this->apiUrl, $this->gatherParameters($consumerProduct));
    }

    /**
     * Gathers the parameters needed for the identity check service
     *
     * @param ConsumerProduct $consumerProduct
     * @return array
     */
    protected function gatherParameters(ConsumerProduct $consumerProduct): array
    {
        return [
            self::WHITEPAGES_API_KEY    => $this->apiKey,
            self::WHITEPAGES_STREET_1   => $consumerProduct->address?->address_1,
            self::WHITEPAGES_STREET_2   => $consumerProduct->address?->address_2,
            self::WHITEPAGES_COUNTRY    => $consumerProduct->address?->country,
            self::WHITEPAGES_CITY       => $consumerProduct->address?->city,
            self::WHITEPAGES_STATE      => $consumerProduct->address?->state,
            self::WHITEPAGES_ZIP        => $consumerProduct->address?->zip_code,
            self::WHITEPAGES_PHONE      => $consumerProduct->consumer->phone,
            self::WHITEPAGES_EMAIL      => $consumerProduct->consumer->email,
            self::WHITEPAGES_FIRST_NAME => $consumerProduct->consumer->first_name,
            self::WHITEPAGES_LAST_NAME  => $consumerProduct->consumer->last_name,
            self::WHITEPAGES_IP_ADDRESS => $consumerProduct->consumerProductData?->payload ? ($consumerProduct->consumerProductData?->payload[GlobalConfigurableFields::IP_ADDRESS->value] ?? null) : null
        ];
    }

    /**
     * Handles preparing a collection of ConsumerProductVerificationResult from the given collection.
     *
     * @param Collection $results
     * @return ConsumerProductVerificationResult
     */
    protected function createConsumerProductVerificationResult(Collection $results): ConsumerProductVerificationResult
    {
        $result          = new ConsumerProductVerificationResult();
        $phone_results   = collect($results->get(self::RESPONSE_PHONE));
        $address_results = collect($results->get(self::RESPONSE_ADDRESS));
        $email_results   = collect($results->get(self::RESPONSE_EMAIL));
        $ip_results      = collect($results->get(self::RESPONSE_IP));

        $result->driver     = VerificationServiceTypes::IDENTITY_CHECK->value;
        $result->confidence = $results->get(self::RESPONSE_CONFIDENCE);

        $result->phone_is_valid        = !! $phone_results->get(self::RESPONSE_IS_VALID);
        $result->phone_name_match      = $phone_results->get(self::RESPONSE_MATCH_TO_NAME) === self::RESPONSE_NOT_FOUND ? null : ($phone_results->get(self::RESPONSE_MATCH_TO_NAME) === self::RESPONSE_FOUND);
        $result->phone_subscriber_name = collect($phone_results->get(self::RESPONSE_PHONE_SUBSCRIBER))->get(self::RESPONSE_NAME);
        $result->phone_address_match   = $phone_results->get(self::RESPONSE_MATCH_TO_ADDRESS);
        $result->phone_line_type       = $phone_results->get(self::RESPONSE_PHONE_LINE_TYPE);
        $result->phone_country_code    = $phone_results->get(self::RESPONSE_PHONE_COUNTRY_CODE);
        $result->phone_carrier         = $phone_results->get(self::RESPONSE_PHONE_CARRIER);
        $result->phone_is_commercial   = $phone_results->get(self::RESPONSE_IS_COMMERCIAL);
        $result->phone_is_prepaid      = $phone_results->get(self::RESPONSE_PHONE_IS_PREPAID);
        $result->phone_warnings        = $phone_results->get(self::RESPONSE_WARNINGS);

        $result->address_is_valid      = !! $address_results->get(self::RESPONSE_IS_VALID);
        $result->address_name_match    = $address_results->get(self::RESPONSE_MATCH_TO_NAME) === self::RESPONSE_NOT_FOUND ? null : ($address_results->get(self::RESPONSE_MATCH_TO_NAME) === self::RESPONSE_FOUND);
        $result->address_resident_name = collect($address_results->get(self::RESPONSE_ADDRESS_RESIDENT))->get(self::RESPONSE_NAME);
        $result->address_type          = $address_results->get(self::RESPONSE_TYPE);
        $result->address_is_commercial = $address_results->get(self::RESPONSE_IS_COMMERCIAL);
        $result->address_is_forwarder  = $address_results->get(self::RESPONSE_ADDRESS_IS_FORWARDER);
        $result->address_warnings      = $address_results->get(self::RESPONSE_WARNINGS);

        $result->email_is_valid         = !! $email_results->get(self::RESPONSE_IS_VALID);
        $result->email_name_match       = $email_results->get(self::RESPONSE_MATCH_TO_NAME) === self::RESPONSE_NOT_FOUND ? null : ($email_results->get(self::RESPONSE_MATCH_TO_NAME) === self::RESPONSE_FOUND);
        $result->email_registered_name  = collect($email_results->get(self::RESPONSE_EMAIL_OWNER))->get(self::RESPONSE_NAME);
        $result->email_age              = $email_results->get(self::RESPONSE_EMAIL_FIRST_SEEN);
        $result->email_is_autogenerated = $email_results->get(self::RESPONSE_EMAIL_IS_AUTOGENERATED);
        $result->email_is_disposable    = $email_results->get(self::RESPONSE_EMAIL_IS_DISPOSABLE);
        $result->email_warnings         = $email_results->get(self::RESPONSE_WARNINGS);

        $result->ip_is_valid         = !! $ip_results->get(self::RESPONSE_IS_VALID);
        $result->ip_name_match       = $ip_results->get(self::RESPONSE_IP_NAME_MATCH) === self::RESPONSE_NOT_FOUND ? null : ($ip_results->get(self::RESPONSE_IP_NAME_MATCH) === self::RESPONSE_FOUND);
        $result->ip_address_distance = $ip_results->get(self::RESPONSE_IP_ADDRESS_DISTANCE);
        $result->ip_phone_distance   = $ip_results->get(self::RESPONSE_IP_PHONE_DISTANCE);
        $result->ip_is_proxy         = $ip_results->get(self::RESPONSE_IP_PROXY);
        $result->ip_warnings         = $ip_results->get(self::RESPONSE_WARNINGS);
        $ip_location                 = collect($ip_results->get(self::RESPONSE_IP_GEOLOCATION));
        $result->ip_geolocation      = "{$ip_location?->get(self::RESPONSE_GEOLOCATION_CITY)}, {$ip_location?->get(self::RESPONSE_GEOLOCATION_STATE)}, {$ip_location?->get(self::RESPONSE_GEOLOCATION_ZIP_CODE)}";

        return $result;
    }
}
