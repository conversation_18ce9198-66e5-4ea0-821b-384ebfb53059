<?php

namespace App\Services\Odin\ConsumerProductVerification;

use Illuminate\Contracts\Support\Arrayable;

class IPQualityScoreResult implements Arrayable
{
    /**
     * The driver that was used to verify this IP quality score
     *
     * @var string
     */
    public string $driver;

    /**
     * An array of email data.
     *
     * @var array
     */
    public array $email;

    /**
     * An array of phone data.
     *
     * @var array
     */
    public array $phone;

    /**
     * An array of transactions data.
     *
     * @var array
     */
    public array $transaction;

    /**
     * IPQualityScoreResult constructor.
     *
     * @param array $properties
     */
    public function __construct(array $properties = [])
    {
        foreach ($properties as $key => $property) {
            if (property_exists($this, $key)) {
                $this->{$key} = $property;
            }
        }
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return (array) $this;
    }
}
