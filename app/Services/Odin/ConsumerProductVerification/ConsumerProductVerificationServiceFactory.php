<?php

namespace App\Services\Odin\ConsumerProductVerification;

use App\Enums\Odin\VerificationServiceTypes;
use App\Services\Odin\ConsumerProductVerification\Dummy\DummyIpQualityScoreService;
use App\Services\Odin\ConsumerProductVerification\IpQuality\IpQualityScoreService;
use App\Services\Odin\ConsumerProductVerification\Dummy\DummyIdentityCheckService;
use App\Services\Odin\ConsumerProductVerification\WhitePages\IdentityCheckService;
use Exception;

class ConsumerProductVerificationServiceFactory
{
    /**
     * Makes the consumer product verification service.
     * @param string $service
     * @return ConsumerProductVerificationServiceInterface
     * @throws Exception
     */
    public static function makeService(string $service): ConsumerProductVerificationServiceInterface
    {
        $productVerificationDriver = config('services.consumer_product_verification.driver');
        $ipScoreDriver             = config('services.ip_quality_score.driver');

        return match ($service) {
            VerificationServiceTypes::IDENTITY_CHECK->value   => $productVerificationDriver === VerificationServiceTypes::DUMMY->value
                ? app()->make(DummyIdentityCheckService::class)
                : app()->make(IdentityCheckService::class),
            VerificationServiceTypes::IP_QUALITY_SCORE->value => $ipScoreDriver === VerificationServiceTypes::DUMMY->value
                ? app()->make(DummyIpQualityScoreService::class)
                : app()->make(IpQualityScoreService::class),
            default                                           => throw new Exception(__METHOD__.": Invalid service - $service")
        };
    }
}
