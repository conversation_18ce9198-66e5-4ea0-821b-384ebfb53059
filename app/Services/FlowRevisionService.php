<?php

namespace App\Services;

use App\Services\CloudStorage\GoogleCloudStorageService;
use Illuminate\Http\UploadedFile;
use Exception;

class FlowRevisionService
{
    public function __construct(protected GoogleCloudStorageService $googleCloudStorageService)
    {}

    /**
     * @param UploadedFile $file
     * @param string $storagePath
     * @return string
     * @throws Exception
     */
    public function uploadImage(UploadedFile $file, string $storagePath): string
    {
        $this->googleCloudStorageService->setCurrentBucket(config('services.google.storage.urls.flow_builder_image_bucket'));

        $path   = $file->store($storagePath, 'local');
        $response = $this->googleCloudStorageService->upload($path, fopen(storage_path("app/{$path}"), 'r'));

        return config('services.google.storage.urls.flow_builder_cdn_url') ? (config('services.google.storage.urls.flow_builder_cdn_url') . $response->info()['name']) : $response->info()['mediaLink'];
    }
}
