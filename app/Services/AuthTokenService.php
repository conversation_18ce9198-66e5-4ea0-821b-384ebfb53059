<?php

namespace App\Services;

use App\Models\User;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class AuthTokenService
{
    const USER_ID         = 'uid';
    const EXPIRATION_TIME = 'exp';
    const TOKEN           = 'token';
    const AUTH_TOKEN      = 'auth_token';

    public function __construct(protected string|null $key = null, protected string $algorithm = 'HS256') {}

    /**
     * @param string $key
     *
     * @return $this
     */
    public function setSigningKey(string $key): static
    {
        $this->key = $key;

        return $this;
    }

    /**
     * @param string $algorithm
     *
     * @return $this
     */
    public function setAlgorithm(string $algorithm): static
    {
        $this->algorithm = $algorithm;

        return $this;
    }

    /**
     * @param array $payload
     * @return string
     */
    public function generateToken(array $payload = []): string
    {
        if(empty($payload)) {
            /** @var User $user */
            $user = Auth::user();

            $payload = [
                self::USER_ID => $user->id,
                self::TOKEN => $user->createToken(self::AUTH_TOKEN)->accessToken->token
            ];
        }

        if(empty($payload[self::EXPIRATION_TIME])) {
            $payload[self::EXPIRATION_TIME] = now()->addHour()->timestamp;
        }

        return JWT::encode($payload, $this->key, $this->algorithm);
    }

    /**
     * @param string $token
     *
     * @return User
     */
    public function getUserFromToken(string $token): User
    {
        $payload        = (array)JWT::decode($token, new Key($this->key, $this->algorithm));
        $expirationTime = $payload[self::EXPIRATION_TIME];

        if (now()->timestamp > $expirationTime) throw new BadRequestException('Token expired');

        /** @var User $user */
        $user = User::query()->findOrFail($payload[self::USER_ID]);

        if (!$user->tokens()->where('token', $payload[self::TOKEN])->first()) throw new BadRequestException('Token does not exist');

        return $user;
    }

    /**
     * @param string $token
     * @return array
     */
    public function getPayloadFromToken(string $token): array
    {
        return (array) JWT::decode($token, new Key($this->key, $this->algorithm));
    }
}
