<?php

namespace App\Services\MissedProducts;

use App\Contracts\MissedProducts\MissedProductReasonContract;
use App\Enums\Odin\SystemModule;
use App\Enums\Odin\SytemModules\MissedLeadsFeatures;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MissedProducts\MissedProduct;
use App\Models\MissedProducts\MissedProductReasonEvent;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\MissedProductRepository;
use App\Services\MissedProducts\Reasons\BaseMissedProductReason;
use App\Services\MissedProducts\Reasons\CampaignsPausedReason;
use App\Services\MissedProducts\Reasons\EmailReason;
use App\Services\MissedProducts\Reasons\OutbidReason;
use App\Services\MissedProducts\Reasons\OverBudgetReason;
use App\Services\MissedProducts\Reasons\RejectionPercentageReason;
use App\Services\MissedProducts\Reasons\UnverifiedReason;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\ConsumerCommonGetter\ConsumerAddressStateCountyZipGetter;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerDataGetter\Field;
use App\Services\Odin\ConsumerFieldModuleVisibilityService\ConsumerFieldModuleVisibilityService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class MissedProductService
{
    const int CAMPAIGN_SUMMARY_CAMPAIGNS_TO_SHOW = 2;
    const int CAMPAIGN_SUMMARY_PREVIEWS_TO_SHOW = 1;
    const int NEW_COMPANY_PREVIEWS_TO_SHOW = 1;

    /** @var array<MissedProductReasonContract> */
    const array MISSED_PRODUCT_REASONS = [
        OverBudgetReason::class,
        RejectionPercentageReason::class,
        UnverifiedReason::class,
        EmailReason::class,
        CampaignsPausedReason::class,
    ];

    public function __construct(
        protected MissedProductRepository              $missedProductRepository,
        protected ConsumerFieldModuleVisibilityService $consumerFieldModuleVisibilityService
    ) {}

    /**
     * Gets the reasons why a company has missed products.
     *
     * @param Company $company
     * @param IndustryService $industryService
     * @return array
     * @throws BindingResolutionException
     */
    public function getReasonsForMissedProducts(Company $company, IndustryService $industryService): array
    {
        $reasons = [];

        foreach (self::MISSED_PRODUCT_REASONS as $reason) {
            /** @var MissedProductReasonContract $service */
            $service = app()->make($reason);

            $service->setCompany($company)->setService($industryService);

            $reasons[$service->getKey()] = [
                'impact'   => $service->doesImpact(),
                'title'    => $service->getTitle(),
                'message'  => $service->getMessage(),
                'category' => $service->getCategory()->value
            ];
        }

        return $reasons;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return ?MissedProduct
     * @throws Exception
     */
    public function createNewMissedProduct(ConsumerProduct $consumerProduct): ?MissedProduct
    {
        $consumerProductId = $consumerProduct->{ConsumerProduct::FIELD_ID};
        $serviceId         = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::FIELD_ID};
        $legsRemaining     = $this->getConsumerProductsLegsRemaining($consumerProduct);

        return $this->missedProductRepository->create($consumerProductId, $serviceId, $legsRemaining);
    }

    /**
     * Returns the number of sellable legs by checking the product ID against existing Product Assignments.
     *
     * @param ConsumerProduct $consumerProduct
     * @return int
     */
    public function getConsumerProductsLegsRemaining(ConsumerProduct $consumerProduct): int
    {
        $contactRequests    = $consumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS};
        $currentAllocations = ProductAssignment::query()
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, '=', ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->where([
                ConsumerProduct::FIELD_CONSUMER_ID => $consumerProduct->consumer_id,
                ProductAssignment::FIELD_DELIVERED => true,
            ])->count();

        return $contactRequests - $currentAllocations;
    }

    /**
     * @param Company $company
     * @param OpportunityNotificationConfig $config
     * @param int|null $numberOfDays
     * @return array
     */
    public function getExistingCompanyMissedProductsSummary(Company $company, OpportunityNotificationConfig $config, ?int $numberOfDays = 7): array
    {
        $from = $config->getStartDate();
        $productData = [
            'campaigns' => [],
        ];

        $campaignThreshold = $config->campaign_threshold ?? 5;
        $campaignLimit = self::CAMPAIGN_SUMMARY_CAMPAIGNS_TO_SHOW;
        $previewLimit = self::CAMPAIGN_SUMMARY_PREVIEWS_TO_SHOW;
        $finalTotal = 0;

        $campaignTotals = $company->futureCampaigns()
            ->pluck(CompanyCampaign::FIELD_ID)
            ->mapWithKeys(fn(int $id) => [
                $id => $this->missedProductRepository->getMissedProductsForCompanyCampaignQuery($id, $from)->count()
                    + $this->missedProductRepository->getOutbidEventCountForCampaign($id, $from),
            ])->filter(fn($count) => $count >= $campaignThreshold)
            ->sortDesc()
            ->take($campaignLimit);

        foreach($campaignTotals as $campaignId => $campaignTotal) {
            $campaign = CompanyCampaign::query()->findOrFail($campaignId);
            $campaignMissedProducts = $this->missedProductRepository->getMissedProductsForCompanyCampaignQuery($campaignId, $from)
                ->get();

            $consumerProductSummary = $campaignMissedProducts->reduce(function(array $output, $missedProduct) {
                $output[$missedProduct->cp_id] = $missedProduct->cp_created;
                return $output;
            }, []);

            $campaignSummary = $this->getCampaignMissedProductSummary($campaign, $consumerProductSummary, $from);
            $productPreviews = array_map(function($preview) use ($campaignSummary) {
                foreach($campaignSummary as $reasonData) {
                    if (in_array($preview[ConsumerProduct::FIELD_ID], $reasonData['products'])) {
                        $preview['reason'] = $reasonData['title'];
                    }
                    else {
                        $preview['reason'] = OutbidReason::SUMMARY;
                        $legsSold = [...array_filter($preview['fields'], fn($v) => $v['field'] === 'quotes_sold')][0] ?? null;
                        if ($legsSold)
                            $legsSold['value'] = max(1, $legsSold['value']);
                    }
                    return $preview;
                }
                return null;
            }, $this->getProductPreviews($campaignMissedProducts->take($previewLimit), $campaign->service));

            //Remove consumer product ID arrays and ensure totals match up
            $calculatedTotal = $this->cleanAndTotalCampaignSummary($campaignSummary);
            $finalTotal += $calculatedTotal;

            $campaignBlock = [
                CompanyCampaign::FIELD_NAME        => $campaign->name,
                IndustryService::RELATION_INDUSTRY => $campaign->service->industry->name,
                'product'                          => $campaign->product->name,
                'campaign_total'                   => $calculatedTotal,
                'summary'                          => $campaignSummary,
                'preview'                          => array_filter($productPreviews, fn($v) => $v),
            ];

            $productData['campaigns'][$campaign->reference] = $campaignBlock;
        }

        $productData['total_available'] = $finalTotal;

        return $productData;
    }

    /**
     * @param Company $company
     * @return array|array[]
     */
    public function getNewCompanyMissedProductsPreview(Company $company): array
    {
        $productBlockData = [];
        $config = OpportunityNotificationConfig::getBDMQueueConfig();
        $from = $config?->getStartDate() ?? now()->subWeek();

        $products = $this->missedProductRepository->getMissedProductsForNewCompany($company, $from)
            ->get()
            ->groupBy(Industry::FIELD_SLUG);

        /** @var $industryGroup Collection<MissedProduct> */
        foreach ($products as $industryGroup) {
            $totalAvailable = $industryGroup->count();

            if ($totalAvailable === 0)
                continue;

            /** @var Collection<MissedProduct> $missedProductsPreview */
            $missedProductsPreview = $industryGroup->take(self::NEW_COMPANY_PREVIEWS_TO_SHOW);
            /** @var ConsumerProduct $consumerProduct */
            $consumerProduct = $missedProductsPreview->first()->consumerProduct;

            $products = $this->getProductPreviews($missedProductsPreview, $consumerProduct->industryService);

            $productBlockData[] = [
                'id'                     => $consumerProduct->id,
                'product'                => $consumerProduct->serviceProduct->product?->name,
                'service'                => $consumerProduct->serviceProduct->service?->name,
                'industry'               => $consumerProduct->serviceProduct->service?->industry->name,
                'products'               => $products,
                'total_available'        => $totalAvailable
            ];

        }
        return $productBlockData;
    }

    /**
     * @param Collection $missedProducts
     * @param IndustryService $industryService
     * @return array
     */
    protected function getProductPreviews(Collection $missedProducts, IndustryService $industryService): array
    {
        $products = [];

        $visibleFields = $this->consumerFieldModuleVisibilityService
            ->getVisibleFieldsByIndustryServiceModuleFeature(
                $industryService->industry_id,
                $industryService->id,
                SystemModule::MISSED_LEADS,
                MissedLeadsFeatures::CLIENT_DASHBOARD->value
            );

        foreach ($missedProducts as $missedProduct) {
            $products[] = $this->getProductFields($missedProduct, $visibleFields);
        }

        return $products;
    }

    /**
     * @param MissedProduct $missedProduct
     * @param Collection $consumerFields
     * @return array
     */
    protected function getProductFields(MissedProduct $missedProduct, Collection $consumerFields): array
    {
        $product      = $missedProduct->{MissedProduct::RELATION_CONSUMER_PRODUCT};
        $serviceName  = $missedProduct->{MissedProduct::RELATION_INDUSTRY_SERVICE}?->{IndustryService::FIELD_NAME};
        $sellableLegs = $missedProduct->{MissedProduct::FIELD_SELLABLE_LEGS};

        $consumerAddressStateCountyZipGetter = new ConsumerAddressStateCountyZipGetter();

        $fields = [
            [
                'title'    => 'State / County',
                'field'    => $consumerAddressStateCountyZipGetter::ID,
                'value'    => $consumerAddressStateCountyZipGetter->getValue($missedProduct->{MissedProduct::RELATION_CONSUMER_PRODUCT}),
            ],
            [
                'title'    => 'Requested Service',
                'field'    => 'requested_service',
                'value'    => $serviceName,
            ],
            [
                'title'    => 'Bids requested by consumer',
                'field'    => 'quotes_requested',
                'value'    => $product->{ConsumerProduct::FIELD_CONTACT_REQUESTS},
            ],
            [
                'title'    => 'Quotes Sold',
                'field'    => 'quotes_sold',
                'value'    => (int)$product->{ConsumerProduct::FIELD_CONTACT_REQUESTS} - (int)$sellableLegs,
            ],
        ];

        $filteredData = $this->consumerFieldModuleVisibilityService->filterOne($missedProduct->consumerProduct, $consumerFields, true);

        foreach ($filteredData as $data) {
            $fields[] = [
                'title'    => $data[Field::FIELD_NAME],
                'field'    => $data[Field::FIELD_KEY],
                'value'    => $data[Field::FIELD_VALUE],
            ];
        }

        return [
            'id'     => $product->{ConsumerProduct::FIELD_ID},
            'fields' => collect($fields)->unique('field')->toArray(),
        ];
    }

    /**
     * Sort missed products according to missed product reason events
     *  -> check if CP was issued while the campaign was paused
     *  -> if not, check if CP was issued while campaign was over budget
     *  -> otherwise assume outbid
     *
     * @param CompanyCampaign $campaign
     * @param array $consumerProductSummary
     * @param Carbon $fromDate
     * @return array
     */
    protected function getCampaignMissedProductSummary(CompanyCampaign $campaign, array $consumerProductSummary, Carbon $fromDate): array
    {
        $missedProductReasons = [
            CampaignsPausedReason::class,
            OverBudgetReason::class,
        ];
        $output = [];

        foreach($missedProductReasons as $missedProductReason) {
            /** @var BaseMissedProductReason $reason */
            $reason = app($missedProductReason);
            $reason->setCampaign($campaign);
            $reason->setFromDate($fromDate);

            $output[$reason->getKey()] = [
                'title'    => $reason->getSummaryText(),
                'products' => [],
            ];
            $timeline = $reason->getTimeline();

            if ($timeline) {
                foreach ($timeline as $timelineItem) {
                    foreach ($consumerProductSummary as $consumerProductId => $createdAt) {
                        if (!$createdAt)
                            continue;

                        $startedAt = $timelineItem[MissedProductReasonEvent::FIELD_STARTED_AT] ?? null;
                        $endedAt = $timelineItem[MissedProductReasonEvent::FIELD_ENDED_AT] ?? null;

                        if (($startedAt === null || $createdAt >= $startedAt) && ($endedAt === null || $createdAt <= $endedAt)) {
                            $output[$reason->getKey()]['products'][] = $consumerProductId;
                            $consumerProductSummary[$consumerProductId] = null;
                        }
                    }
                }
            }
        }

        /** @var OutbidReason $outbidReason */
        $outbidReason = app(OutbidReason::class);
        $output[$outbidReason->getKey()] = [
            'title'    => $outbidReason->getSummaryText(),
            'products' => $this->getOutbidProductArray($campaign, $fromDate),
        ];

        return $output;
    }

    /**
     * Outbid events are not tracked per-product so there are no real cp_ids to attach here
     *
     * @param CompanyCampaign $campaign
     * @param Carbon $fromDate
     * @return array
     */
    private function getOutbidProductArray(CompanyCampaign $campaign, Carbon $fromDate): array
    {
        $count = $this->missedProductRepository->getOutbidEventCountForCampaign($campaign->id, $fromDate);

        return array_fill(0, $count, -1);
    }

    /**
     * Strip primary key arrays and leave totals before returning for email/frontend use
     * Also returns the actual campaign total after processing, in case there is a variance
     *
     * @param array $campaignSummary
     * @return int
     */
    private function cleanAndTotalCampaignSummary(array &$campaignSummary): int
    {
        $calculatedTotal = 0;
        foreach ($campaignSummary as &$reason) {
            $reasonCount = count($reason['products']);
            $calculatedTotal += $reasonCount;
            $reason['products'] = $reasonCount;
        }

        return $calculatedTotal;
    }
}
