<?php

namespace App\Services\MissedProducts\Reasons;

use App\Contracts\MissedProducts\MissedProductReasonContract;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\MissedProducts\MissedProductCategory;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MissedProducts\MissedProductReasonEvent;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class CampaignsPausedReason extends BaseMissedProductReason
{
    protected ?int $daysPaused = null;

    public function getKey(): string
    {
        return 'pauses';
    }

    public function getMissedProductEventType(): ?MissedProductReasonEventType
    {
        return MissedProductReasonEventType::CAMPAIGN_PAUSED;
    }

    public function doesImpact(): bool
    {
        return $this->getDaysPaused() > 0;
    }

    public function getTitle(): string
    {
        return 'Campaigns paused too often';
    }

    public function getSummaryText(): string
    {
        return 'the campaign was paused';
    }

    public function getMessage(): string
    {
        $days = $this->getDaysPaused() === 1 ? 'day' : 'days';

        return "You are missing out on leads because you had campaigns paused on {$this->getDaysPaused()} {$days} in the last {$this->getRangeInDays()}.";
    }

    public function getCategory(): MissedProductCategory
    {
        return MissedProductCategory::CAMPAIGNS;
    }

    public function setFromDate(Carbon $date): MissedProductReasonContract
    {
        $this->daysPaused = null;

        return parent::setFromDate($date);
    }

    /**
     * If no pause events have been recorded, the campaign has been in its current state for the full time period
     * @return Collection|null
     */
    public function getTimeline(): ?Collection
    {
        $baseTimeline = $this->getBaseTimeline() ?? collect();
        if (!$baseTimeline->count()) {
            $daysPaused = $this->handleNoPauseEvents();
            if ($daysPaused)
                $baseTimeline->push(new MissedProductReasonEvent([
                    MissedProductReasonEvent::FIELD_EVENT_TYPE => MissedProductReasonEventType::CAMPAIGN_PAUSED,
                    MissedProductReasonEvent::FIELD_STARTED_AT => now()->subDays($daysPaused)->startOfDay(),
                    MissedProductReasonEvent::FIELD_ENDED_AT   => now(),
                ]));
        }

        return $baseTimeline;
    }

    protected function getDaysPaused(): int
    {
        if ($this->daysPaused === null) {
            $timeline = $this->getTimeline();
            if (!$timeline->count()) {
                $this->daysPaused = $this->handleNoPauseEvents();
            }
            else {
                $days = $timeline->reduce(function (int $output, MissedProductReasonEvent $event) {
                    $fromDate = $event->started_at
                        ? max($event->started_at, $this->fromDate)
                        : $this->fromDate;
                    $toDate = $event->ended_at ?? now();
                    return $output + ceil($toDate->diffInHours($fromDate, true) / 24);
                }, 0);

                $this->daysPaused = min($this->getRangeInDays(), $days);
            }
        }

        return $this->daysPaused;
    }

    // If there are no logged events, the campaign has been in its current status for all days queried
    protected function handleNoPauseEvents(): int
    {
        if ($this->campaign)
            $pausedCampaign = $this->campaign->status !== CampaignStatus::ACTIVE;
        else
            $pausedCampaign = $this->company->futureCampaigns()
                ->whereIn(CompanyCampaign::FIELD_STATUS, [CampaignStatus::PAUSED_TEMPORARILY, CampaignStatus::PAUSED_PERMANENTLY])
                ->where(CompanyCampaign::FIELD_SERVICE_ID, $this->service->id)
                ->exists();

        return $pausedCampaign
            ? $this->getRangeInDays()
            : 0;
    }
}
