<?php

namespace App\Services\MissedProducts\Reasons;

use App\Enums\MissedProducts\MissedProductCategory;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Enums\Odin\BudgetCategory;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;

class UnverifiedReason extends BaseMissedProductReason
{
    public function getKey(): string
    {
        return 'unverified';
    }

    public function getMissedProductEventType(): ?MissedProductReasonEventType
    {
        return null;
    }

    public function doesImpact(): bool
    {
        return !$this->checkUnverifiedBudgetIsOn();
    }

    public function getTitle(): string
    {
        return 'Unverified leads off';
    }

    public function getMessage(): string
    {
        return "Unverified leads have gone through the normal call to action, entering their details and phone number. but our QA team was unable to get them to confirm they want quotes. We sell these leads for 30-40% of the cost of a verified lead. they will have lower contact and appointment set rates but because of the low cost and less competition, can produce excellent cost of acquisition. Many of these people just have jobs that don't allow them to answer their phones when called.";
    }

    public function getCategory(): MissedProductCategory
    {
        return MissedProductCategory::CAMPAIGNS;
    }

    protected function checkUnverifiedBudgetIsOn(): bool
    {
        if ($this->campaign) {
            return $this->campaign->budgetContainer->budgets()
                ->where(Budget::TABLE .'.'. Budget::FIELD_KEY, BudgetCategory::UNVERIFIED)
                ->where(Budget::TABLE .'.'. Budget::FIELD_STATUS, Budget::STATUS_ENABLED)
                ->exists();
        }
        else {
            return Budget::query()
                ->join(BudgetContainer::TABLE, BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_ID, '=', Budget::TABLE .'.'. Budget::FIELD_BUDGET_CONTAINER_ID)
                ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', BudgetContainer::TABLE .'.'. BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID)
                ->where([
                    CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID => $this->company->id,
                    CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_SERVICE_ID => $this->service->id,
                    Budget::TABLE .'.'. Budget::FIELD_KEY => BudgetCategory::UNVERIFIED,
                    Budget::TABLE .'.'. Budget::FIELD_STATUS => Budget::STATUS_ENABLED,
                ])->exists();
        }
    }
}
