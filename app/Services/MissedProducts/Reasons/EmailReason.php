<?php

namespace App\Services\MissedProducts\Reasons;

use App\Enums\MissedProducts\MissedProductCategory;
use App\Enums\MissedProducts\MissedProductReasonEventType;

class EmailReason extends BaseMissedProductReason
{
    public function getKey(): string
    {
        return 'email';
    }

    public function getMissedProductEventType(): ?MissedProductReasonEventType
    {
        return null;
    }

    public function doesImpact(): bool
    {
        // We're not currently selling email-only
        return false;
    }

    public function getTitle(): string
    {
        return 'Email leads off';
    }

    public function getMessage(): string
    {
        return "More and more consumers are indicating a preference to deal via email only. As such we now have a limited number of pages on our websites that allow the consumer to select this option. They have a much lower response rate but because of the cost can yield a very good cost of acquisition. They are great for smaller installers who cannot call phone leads as quickly as big installers who have dedicated call centers and auto-dialers.";
    }

    public function getCategory(): MissedProductCategory
    {
        return MissedProductCategory::CAMPAIGNS;
    }
}
