<?php

namespace App\Services\MissedProducts\Reasons;

use App\Contracts\MissedProducts\MissedProductReasonContract;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MissedProducts\MissedProductReasonEvent;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

abstract class BaseMissedProductReason implements MissedProductReasonContract
{
    protected ?Company $company = null;
    protected ?IndustryService $service = null;
    protected ?CompanyCampaign $campaign = null;
    protected ?Carbon $fromDate;

    public function __construct()
    {
        $this->fromDate = now()->subMonth();
    }

    /**
     * @inheritDoc
     */
    public function getSummaryText(): string
    {
        return $this->getTitle();
    }

    public abstract function getMissedProductEventType(): ?MissedProductReasonEventType;

    /**
     * @inheritDoc
     */
    public function setFromDate(Carbon $date): MissedProductReasonContract
    {
        $this->fromDate = $date;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function setCampaign(CompanyCampaign $campaign): MissedProductReasonContract
    {
        $this->campaign = $campaign;
        $this->company = $this->company ?? $campaign->company;
        $this->service = $this->service ?? $campaign->service;

        return $this;
    }

    /**
     * Gets the start and end date of each missed product reason event in the timeframe
     * Either can be null, if the event started before the query timeline, or hasn't yet ended
     * Returns null if this MissedProductReason does
     *
     * @inheritDoc
     */
    public function getTimeline(): ?Collection
    {
        return $this->getBaseTimeline();
    }

    /**
     * @return Collection|null
     */
    protected function getBaseTimeline(): ?Collection
    {
        $eventType = $this->getMissedProductEventType();
        if ($eventType) {
            return MissedProductReasonEvent::query()
                ->where(MissedProductReasonEvent::FIELD_EVENT_TYPE, $eventType)
                ->where(fn(Builder $query) =>
                $query->where(MissedProductReasonEvent::FIELD_ENDED_AT, '>=', $this->fromDate)
                    ->orWhere(MissedProductReasonEvent::FIELD_STARTED_AT, '>=', $this->fromDate)
                )->when($this->campaign, fn(Builder $query) =>
                $query->where(MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID, $this->campaign->id)
                )->when(!$this->campaign, fn(Builder $query) =>
                $query->where(MissedProductReasonEvent::TABLE .'.'. MissedProductReasonEvent::FIELD_COMPANY_ID, $this->company->id)
                    ->when($this->service, fn(Builder $query) =>
                    $query->join(CompanyCampaign::TABLE, fn(JoinClause $join) =>
                    $join->on(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, MissedProductReasonEvent::FIELD_COMPANY_CAMPAIGN_ID)
                        ->where(CompanyCampaign::FIELD_SERVICE_ID, $this->service->id)
                    ))
                )->select([
                    MissedProductReasonEvent::TABLE .'.'. MissedProductReasonEvent::FIELD_STARTED_AT,
                    MissedProductReasonEvent::FIELD_ENDED_AT
                ])->get();
        }

        return null;
    }

    /**
     * @inheritDoc
     */
    public function setCompany(?Company $company): MissedProductReasonContract
    {
        $this->company = $company;

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function setService(?IndustryService $service): MissedProductReasonContract
    {
        $this->service = $service;

        return $this;
    }

    /**
     * @return int
     */
    protected function getRangeInDays(): int
    {
        return (int) max($this->fromDate->diffInDays(now(), true), 1);
    }
}
