<?php

namespace App\Services\MissedProducts\Reasons;

use App\Enums\MissedProducts\MissedProductCategory;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Repositories\ComputedRejectionStatisticRepository;

class RejectionPercentageReason extends BaseMissedProductReason
{
    public function getKey(): string
    {
        return 'rejection_percentage';
    }

    public function getMissedProductEventType(): ?MissedProductReasonEventType
    {
        return null;
    }

    public function doesImpact(): bool
    {
        /** @var ComputedRejectionStatisticRepository $repository */
        $repository = app()->make(ComputedRejectionStatisticRepository::class);

        // todo: Pass in the specific product we're checking the impact on rather then doing an OR here
        return $repository->getCompanyLeadRejectionPercentage($this->company) > 4;
    }

    public function getTitle(): string
    {
        return 'Rejection % too high';
    }

    public function getMessage(): string
    {
        return 'Your rejection % is higher than some of your peers. This lowers your ranking in the lead allocation queue.';
    }

    public function getCategory(): MissedProductCategory
    {
        return MissedProductCategory::LEADS;
    }
}
