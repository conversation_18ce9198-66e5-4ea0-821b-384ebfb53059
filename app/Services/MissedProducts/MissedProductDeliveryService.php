<?php

namespace App\Services\MissedProducts;

use App\Campaigns\Delivery\Contacts\Strategies\Email\BaseEmailDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\Email\LeadEmailDeliveryStrategy;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MissedProducts\MissedProduct;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class MissedProductDeliveryService
{
    /**
     * @param Company $company
     * @param Collection $consumerProducts
     * @param string $email
     * @param int $notificationConfigId
     * @return int[]
     */
    public function deliverPromotionalLeadsByEmail(Company $company, Collection $consumerProducts, string $email, int $notificationConfigId): array
    {
        /** @var BaseEmailDeliveryStrategy $strategy */
        $strategy = app(LeadEmailDeliveryStrategy::class);
        $campaign = $this->makeDummyCampaign($company);
        $basePayload = [
            BaseEmailDeliveryStrategy::FIELD_EMAIL => $email,
            BaseEmailDeliveryStrategy::FIELD_CONTACT_DELIVERY_MODULE_ID => -1,
        ];
        /** @var MissedProductService $missedProductService */
        $missedProductService = app(MissedProductService::class);
        $exhaustedConsumerProductIds = [];

        $delivered = [];
        $message = null;

        try {
            $consumerProducts->each(function (ConsumerProduct $consumerProduct) use ($company, $strategy, $campaign, $basePayload, $notificationConfigId, $missedProductService, &$exhaustedConsumerProductIds, &$delivered) {
                $initialLegsRemaining = $missedProductService->getConsumerProductsLegsRemaining($consumerProduct);
                if ($initialLegsRemaining > 0) {
                    $productAssignment = $this->createProductAssignment($company, $consumerProduct, $notificationConfigId);
                    if ($productAssignment) {
                        $payload = [...$basePayload, BaseEmailDeliveryStrategy::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id];
                        $success = $strategy->deliver($consumerProduct, $campaign, $payload);
                        if ($success) {
                            $productAssignment->update([
                                ProductAssignment::FIELD_DELIVERED    => true,
                                ProductAssignment::FIELD_DELIVERED_AT => now(),
                            ]);
                            if ($initialLegsRemaining === 1)
                                $exhaustedConsumerProductIds[] = $consumerProduct->id;

                            $delivered[] = $consumerProduct->id;
                            // mailtrap emails-per-second throttling
                            if (!app()->isProduction())
                                sleep(0.5);
                        }
                    }
                }
                else
                    $exhaustedConsumerProductIds[] = $consumerProduct->id;
            });
        }
        catch(Throwable $e) {
            logger()->error($e);
        }

        $this->handleDeliveredAndExhaustedProducts([
            ...$exhaustedConsumerProductIds,
            ...$delivered,
        ]);

        return $delivered;
    }

    /**
     * Remove MissedProducts if the allocation is now exhausted, otherwise decrement sellable legs
     * @param array $consumerProductIds
     * @return void
     */
    protected function handleDeliveredAndExhaustedProducts(array $consumerProductIds): void
    {
        if ($consumerProductIds) {
            DB::table(MissedProduct::TABLE)
                ->whereIn(MissedProduct::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds)
                ->where(MissedProduct::FIELD_SELLABLE_LEGS, '<=', 1)
                ->delete();
            DB::table(MissedProduct::TABLE)
                ->whereIn(MissedProduct::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds)
                ->decrement(MissedProduct::FIELD_SELLABLE_LEGS);
        }
    }

    /**
     * @param Company $company
     * @param ConsumerProduct $consumerProduct
     * @param int $notificationConfigId
     * @return ProductAssignment
     */
    protected function createProductAssignment(Company $company, ConsumerProduct $consumerProduct, int $notificationConfigId): ProductAssignment
    {
        /** @var ProductAssignment */
        return $consumerProduct->productAssignment()->create([
            ProductAssignment::FIELD_COMPANY_ID => $company->id,
            ProductAssignment::FIELD_COST       => 0,
            ProductAssignment::FIELD_CHARGEABLE => false,
            ProductAssignment::FIELD_DELIVERED => false,
            ProductAssignment::FIELD_EXCLUDE_BUDGET => ProductAssignment::EXCLUDE_BUDGET_PROMO,
            ProductAssignment::FIELD_QUALITY_TIER_ID => QualityTierEnum::STANDARD->model()->id,
            ProductAssignment::FIELD_PAYLOAD => [
                ProductAssignment::PAYLOAD_KEY_OPPORTUNITY_NOTIFICATION_CONFIG_ID => $notificationConfigId,
            ],
        ]);
    }

    /**
     * @param Company $company
     * @return CompanyCampaign
     */
    protected function makeDummyCampaign(Company $company): CompanyCampaign
    {
        return new CompanyCampaign([
            CompanyCampaign::FIELD_COMPANY_ID => $company->id,
            CompanyCampaign::FIELD_NAME => "Promo - $company->name",
        ]);
    }
}