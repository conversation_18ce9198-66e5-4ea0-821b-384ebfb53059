<?php

namespace App\Services;

use Google\Cloud\Core\Exception\BadRequestException;
use Illuminate\Http\UploadedFile;

class FileUploadHelperService extends UploadedFile
{
    /**
     * Handles creating an instance of the `UploadedFile` from a URL instead of an actual file upload.
     *
     * @param string      $url
     * @param string      $originalName
     * @param string|null $mimeType
     * @param int|null    $error
     * @param bool        $test
     * @param bool        $throwException
     * @return static
     * @throws BadRequestException
     */
    public static function createFromUrl(string $url,
                                         string $originalName   = '',
                                         string $mimeType       = null,
                                         int    $error          = null,
                                         bool   $test           = false,
                                         bool   $throwException = true,
    ): self
    {
        if (! $stream = @fopen($url, 'r')) {
            $throwException
                ? throw new BadRequestException("The requested file url is deemed invalid: {$url}")
                : logger()->error("The requested file url is deemed invalid: {$url}");
        }

        $tempFile = tempnam(sys_get_temp_dir(), 'url-file-');

        file_put_contents($tempFile, $stream);

        return new static($tempFile, $originalName, $mimeType, $error, $test);
    }

    /**
     * Handles preparing a file name by trimming & replacing the spaces with underscore.
     *
     * @param string $fileName
     * @return string
     */
    public static function prepareFileName(string $fileName): string
    {
        return str_replace(' ', '_', trim($fileName));
    }

    /**
     * Handles generating an encrypted string for the given file name.
     *
     * @param string $fileName
     * @param bool   $appendExtensionSeparately
     * @return string
     */
    public static function encryptFileName(string $fileName, bool $appendExtensionSeparately = false): string
    {
        if($appendExtensionSeparately === true) {
            $list      = explode('.', $fileName);
            $extension = trim(end($list));
        }

        $hash = hash('sha256', $fileName);

        return !empty($extension)
            ? $hash . '.' . $extension
            : $hash;
    }
}
