<?php

namespace App\Services;

use Spatie\Activitylog\ActivityLogger;
use Spatie\Activitylog\Contracts\Activity as ActivityContract;

class CustomActivityLogger extends ActivityLogger
{
    public function log(string $description): ?ActivityContract
    {
        $model = $this->activity?->subject;

        if ($model) {
            if (defined(get_class($model) . '::ACTIVITY_LOG_TABLE')) {
                $this->activity->setTable($model::ACTIVITY_LOG_TABLE);
            }
        }

        return parent::log($description);
    }
}
