<?php

namespace App\Services;

use App\DataModels\ValueType;
use App\Enums\ValueType as ValueTypeEnum;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use App\Workflows\WorkflowPayload;
use Exception;
use Illuminate\Support\Arr;

class ValueTypeProcessingService
{
    public function __construct(protected WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory) {}

    /**
     * Handles processing a given value.
     *
     * @param ValueType $value
     * @param WorkflowPayload $payload
     * @return mixed
     * @throws Exception
     */
    public function process(ValueType $value, WorkflowPayload $payload): mixed
    {
        if (is_string($value->value))
            $value->value = trim($value->value);

        return match ($value->type) {
            ValueTypeEnum::ARRAY => Arr::wrap($value->value),
            ValueTypeEnum::PREVIOUS_RESULT_ENTRY => $payload->get('last_result_value'),
            default => ($this->workflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER))->handle(strval($value->value), $payload)
        };
    }
}
