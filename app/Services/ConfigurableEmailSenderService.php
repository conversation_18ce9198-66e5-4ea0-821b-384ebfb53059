<?php

namespace App\Services;

use App\DTO\EmailService\EmailAttachmentDTO;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Factories\Workflows\WorkflowPayloadFactory;
use App\Mail\OutreachCadence\ConfigurableEmail;
use App\Models\EmailTemplate;
use App\Services\Delivery\Email as DeliverableEmail;
use App\Services\EmailTemplates\EmailTemplateService;
use App\Workflows\Shortcodes\WorkflowShortcodeService;
use App\Workflows\WorkflowEvent;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class ConfigurableEmailSenderService
{
    /**
     * @param string $toEmail
     * @param string $fromEmail
     * @param string $subject
     * @param string $body
     * @param string $fromUserName
     * @param Collection<EmailAttachmentDTO> $attachments
     * @return void
     */
    public function send(
        string $toEmail,
        string $fromEmail,
        string $subject,
        string $body,
        string $fromUserName,
        Collection $attachments = new Collection()
    ): void
    {
        $mailable = new ConfigurableEmail(
            fromEmail: $fromEmail,
            fromName : $fromUserName,
            subject  : $subject,
            body     : $body,
        );

        $attachments->map(function (EmailAttachmentDTO $attachment) use ($mailable) {
            $mailable->attachData(
                data   : $attachment->getData(),
                name   : $attachment->getName(),
                options: $attachment->getOptions(),
            );
        });

        DeliverableEmail::send($toEmail, $mailable);

        // todo - log?
    }

    /**
     * @param EmailTemplate $template
     * @param string $toEmail
     * @param string $fromEmail
     * @param string $fromUserName
     * @param array $data Key-value pairs for shortcode replacement in the template. Example: ['company_id' => 1517, 'campaign_id' => '123']
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function sendWithTemplate(
        EmailTemplate $template,
        string $toEmail,
        string $fromEmail,
        string $fromUserName,
        array $data
    ): void
    {
        /** @var WorkflowShortcodeService $shortcodeService */
        $shortcodeService = app(WorkflowShortcodeService::class);

        /** @var EmailTemplateService $emailService */
        $emailService = app(EmailTemplateService::class);

        $shortCodePayload = WorkflowPayloadFactory::create(
            WorkflowEvent::fromArray([
                'event_category' => EventCategory::GENERIC->value,
                'event_name'     => EventName::GENERIC_EVENT->value,
                'event_data'     => $data
            ])
        );

        DeliverableEmail::send($toEmail, new ConfigurableEmail(
            fromEmail: $fromEmail,
            fromName : $fromUserName,
            subject  : $shortcodeService->handle($template->subject, $shortCodePayload),
            body     : $emailService->convertTemplateMarkdownToHtml(
                markdown    : $shortcodeService->handle($template->content, $shortCodePayload),
                backgroundId: $template->background_id
            ),
        ));
    }
}
