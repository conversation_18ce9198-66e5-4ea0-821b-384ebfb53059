<?php

namespace App\Services\CompanyRegistration;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Http\Requests\CompanyRegistration\V3\CreateNewBuyerProspectRequest;
use App\Http\Requests\CompanyRegistration\V3\UpdateNewBuyerProspectRequest;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\SaleType;
use App\Repositories\LocationRepository;
use App\Services\DatabaseHelperService;
use App\Services\Prospects\ProspectDuplicateMatchingService;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class CompanyRegistrationV3Service
{
    public function __construct(protected LocationRepository $locationRepository) {}

    /**
     * @param CreateNewBuyerProspectRequest $request
     *
     * @return NewBuyerProspect
     */
    public function createNewBuyerProspect(CreateNewBuyerProspectRequest $request): NewBuyerProspect
    {
        /** @var NewBuyerProspect */
        return NewBuyerProspect::query()->create([
            NewBuyerProspect::FIELD_COMPANY_NAME => $request->validated(NewBuyerProspect::FIELD_COMPANY_NAME),
            NewBuyerProspect::FIELD_COMPANY_WEBSITE => $request->validated(NewBuyerProspect::SOURCE_KEY_WEBSITE),
            NewBuyerProspect::FIELD_STATUS => ProspectStatus::INITIAL,
            NewBuyerProspect::FIELD_SOURCE => ProspectSource::REGISTRATION,
            NewBuyerProspect::FIELD_INDUSTRY_SERVICE_IDS => $this->getIndustryServiceIds($request->validated(NewBuyerProspect::SOURCE_KEY_INDUSTRIES)),
            NewBuyerProspect::FIELD_SOURCE_DATA => $request->validated(),
            NewBuyerProspect::FIELD_COMPANY_PHONE => $request->validated(NewBuyerProspect::SOURCE_KEY_PHONE),
            NewBuyerProspect::FIELD_ORDINAL_VALUE => 0
        ]);
    }

    /**
     * @param UpdateNewBuyerProspectRequest $request
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return bool
     */
    public function updateBuyerProspect(UpdateNewBuyerProspectRequest $request, NewBuyerProspect $newBuyerProspect): bool
    {
        $newBuyerProspect->source_data = array_merge(($newBuyerProspect->source_data ?? []), $request->validated());

        return $newBuyerProspect->save();
    }

    /**
     * @param string $email
     *
     * @return bool
     */
    public function emailCanLogin(string $email): bool
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_EMAIL, $email)
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->get()
            ->isEmpty();
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return bool
     */
    public function duplicateCompany(NewBuyerProspect $newBuyerProspect): bool
    {
        if (ProspectDuplicateMatchingService::findCompaniesWithExactWebsite($newBuyerProspect->company_website)->isNotEmpty()) {
            return true;
        }

        if (ProspectDuplicateMatchingService::findCompaniesWithSimilarName($newBuyerProspect->company_name)->isNotEmpty()) {
            return true;
        }

        return false;
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param string $password
     *
     * @return Company
     */
    public function createCompanyAndUser(NewBuyerProspect $newBuyerProspect, string $password): Company
    {
        $company = $this->createCompany($newBuyerProspect);

        $this->createCompanyUser($newBuyerProspect, $company, $password);

        $newBuyerProspect->company_id = $company->id;
        $newBuyerProspect->resolution = ProspectResolution::REGISTRATION_COMPLETE;
        $newBuyerProspect->status = ProspectStatus::CLOSED;
        $newBuyerProspect->save();

        return $company;
    }

    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     *
     * @return void
     */
    public function syncToLegacy(Company $company, CompanyUser $companyUser): void
    {
        /** @var CompanyRegistrationSyncService $syncService */
        $syncService = app(CompanyRegistrationSyncService::class);

        try {
            $legacyResponse = $syncService->post(
                route: '/create-company-and-user',
                data: [
                    'company' => [...$syncService->transformForLegacy(Company::class, $company->toArray()), EloquentCompany::BILLING_VERSION => 'v2'],
                    'user'    => $syncService->transformForLegacy(CompanyUser::class, $companyUser->toArray()),
                ],
                timeout: 10
            )?->json() ?? [];

            if ($legacyResponse['status'] ?? false) {
                $company->update([Company::FIELD_LEGACY_ID => $legacyResponse['legacy_company_id']]);
                $companyUser->update([CompanyUser::FIELD_LEGACY_ID => $legacyResponse['legacy_user_id']]);
            } else {
                logger()->error("Company Registration failed to create legacy company. Company Id: $company->id");
            }
        } catch (Exception $exception) {
            logger()->error("Company Registration failed to create legacy company. Company Id: $company->id, error: {$exception->getMessage()}");
        }
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return bool|null
     */
    public function emailIsRegistered(NewBuyerProspect $newBuyerProspect): ?bool
    {
        return $newBuyerProspect->company?->users()
            ->where(CompanyUser::FIELD_EMAIL, $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_EMAIL])
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->exists();
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return Collection
     */
    public function getLeadPrices(NewBuyerProspect $newBuyerProspect): Collection
    {
        $industries = collect($newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_INDUSTRIES])
            ->map(fn($industry) => "'$industry'")
            ->join(',');
        $states = collect($newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_STATES])
            ->map(fn($state) => "'$state'")
            ->join(',');
        $propertyTypeId = PropertyType::query()->where(PropertyType::FIELD_NAME, PropertyTypeEnum::RESIDENTIAL)->first()->id;
        $product = Product::LEAD->value;
        $solarIndustry = strtolower(IndustryEnum::SOLAR->value);
        $qualityTierId = QualityTier::query()->where(QualityTier::FIELD_NAME, QualityTierEnum::STANDARD)->first()->id;

        return DB::table(DB::raw("(
        select i1.name as industry, l1.state, st1.name as sale_type, state_price.price
        from " . ProductStateFloorPrice::TABLE . " state_price
                join " . Location::TABLE . " l1 on state_price.state_location_id = l1.id
                join " . ServiceProduct::TABLE . " sp1 on state_price.service_product_id = sp1.id
                join " . IndustryService::TABLE . " isr1 on sp1.industry_service_id = isr1.id
                join " . Industry::TABLE . " i1 on isr1.industry_id = i1.id
                join " . ProductModel::TABLE . " p1 on sp1.product_id = p1.id
                join " . SaleType::TABLE . " st1 on state_price.sale_type_id = st1.id
        where l1.state_abbr in ($states)
          and i1.slug in ($industries)
          and p1.name in ('$product')
          and state_price.property_type_id = $propertyTypeId
          AND (i1.slug = '$solarIndustry' OR state_price.quality_tier_id = $qualityTierId)
        union all
        select i2.name as industry, l2.state, st2.name as sale_type, county_price.price
        from " . ProductCountyFloorPrice::TABLE . " county_price
                join " . Location::TABLE . " l2 on county_price.state_location_id = l2.id
                join " . ServiceProduct::TABLE . " sp2 on county_price.service_product_id = sp2.id
                join " . IndustryService::TABLE . " isr2 on sp2.industry_service_id = isr2.id
                join " . Industry::TABLE . " i2 on isr2.industry_id = i2.id
                join " . ProductModel::TABLE . " p2 on sp2.product_id = p2.id
                join " . SaleType::TABLE . " st2 on county_price.sale_type_id = st2.id
        where l2.state_abbr in ($states)
          and i2.slug in ($industries)
          and p2.name in ('$product')
          and county_price.property_type_id = $propertyTypeId
          AND (i2.slug = '$solarIndustry' OR county_price.quality_tier_id = $qualityTierId) ) AS combined_prices"
        ))->select('industry', 'state', 'sale_type', DB::raw('min(price) as min_price'), DB::raw('max(price) as max_price'))
            ->groupBy('industry', 'state', 'sale_type')
            ->orderBy('industry')
            ->orderBy('state')
            ->get();
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return Company
     */
    protected function createCompany(NewBuyerProspect $newBuyerProspect): Company
    {
        /** @var Company $company */
        $company = Company::query()->create([
            Company::FIELD_NAME => $newBuyerProspect->company_name,
            Company::FIELD_ENTITY_NAME => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_ENTITY_NAME],
            Company::FIELD_WEBSITE => $newBuyerProspect->company_website,
            Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_LOCKED
        ]);

        $this->createCompanyLocation($newBuyerProspect, $company);
        $this->createCompanyIndustryAndServices($newBuyerProspect, $company);

        return $company;
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param Company $company
     *
     * @return void
     */
    protected function createCompanyLocation(NewBuyerProspect $newBuyerProspect, Company $company): void
    {
        if (
            !array_key_exists(NewBuyerProspect::SOURCE_KEY_STREET_ADDRESS, $newBuyerProspect->source_data)
            || !array_key_exists(NewBuyerProspect::SOURCE_KEY_CITY, $newBuyerProspect->source_data)
            || !array_key_exists(NewBuyerProspect::SOURCE_KEY_STATE, $newBuyerProspect->source_data)
            || !array_key_exists(NewBuyerProspect::SOURCE_KEY_ZIP_CODE, $newBuyerProspect->source_data)
        ) {
            return;
        }

        /** @var Address $address */
        $address = Address::query()->create([
            Address::FIELD_ADDRESS_1 => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_STREET_ADDRESS],
            Address::FIELD_CITY => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_CITY],
            Address::FIELD_STATE => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_STATE],
            Address::FIELD_ZIP_CODE => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_ZIP_CODE],
            Address::FIELD_COUNTRY => 'US'
        ]);

        $company->locations()->create([
            CompanyLocation::FIELD_NAME => 'Default',
            CompanyLocation::FIELD_ADDRESS_ID => $address->id,
            CompanyLocation::FIELD_IS_PRIMARY => true
        ]);
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param Company $company
     *
     * @return void
     */
    protected function createCompanyIndustryAndServices(NewBuyerProspect $newBuyerProspect, Company $company): void
    {
        $company->industries()->sync(
            $this->getIndustries($newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_INDUSTRIES])->pluck(Industry::FIELD_ID)->toArray()
        );
        $company->services()->sync($newBuyerProspect->industry_service_ids);
    }

    /**
     * @param array $industries
     *
     * @return Collection<Industry>
     */
    protected function getIndustries(array $industries): Collection
    {
        return Industry::query()->whereIn(Industry::FIELD_SLUG, $industries)->get();
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     * @param Company $company
     * @param string $password
     *
     * @return void
     */
    protected function createCompanyUser(NewBuyerProspect $newBuyerProspect, Company $company, string $password): void
    {
        /** @var CompanyUser $user */
        $user = $company->users()->create([
            CompanyUser::FIELD_FIRST_NAME => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_FIRST_NAME],
            CompanyUser::FIELD_LAST_NAME => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_LAST_NAME],
            CompanyUser::FIELD_EMAIL => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_EMAIL],
            CompanyUser::FIELD_CELL_PHONE => $newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_PHONE],
            CompanyUser::FIELD_CAN_LOG_IN => true,
            CompanyUser::FIELD_STATUS => CompanyUser::STATUS_ACTIVE,
            CompanyUser::FIELD_AUTHENTICATION_TYPE => CompanyUser::AUTHENTICATION_TYPE_ADMIN2,
            CompanyUser::FIELD_IS_CONTACT => false
        ]);

        $user->password = Hash::make($password);
        $user->save();
    }

    /**
     * @param array $industries
     *
     * @return array
     */
    protected function getIndustryServiceIds(array $industries): array
    {
        return $this->getIndustries($industries)->map(fn(Industry $industry) => match ($industry->name) {
            IndustryEnum::SOLAR->value => IndustryService::query()->where(IndustryService::FIELD_SLUG, 'solar-installation')->first()?->id,
            IndustryEnum::ROOFING->value => IndustryService::query()->where(IndustryService::FIELD_SLUG, 'roof-replacement')->first()?->id,
            IndustryEnum::SIDING->value => IndustryService::query()->where(IndustryService::FIELD_SLUG, 'siding-replacement')->first()?->id,
            IndustryEnum::HVAC->value => IndustryService::query()->where(IndustryService::FIELD_SLUG, 'central-air-install-replace')->first()?->id,
            IndustryEnum::KITCHENS->value => IndustryService::query()->where(IndustryService::FIELD_SLUG, 'kitchen-remodeling')->first()?->id,
            IndustryEnum::BATHROOMS->value => IndustryService::query()->where(IndustryService::FIELD_SLUG, 'bathroom-remodeling')->first()?->id,
            IndustryEnum::WINDOWS->value => IndustryService::query()->where(IndustryService::FIELD_SLUG, 'windows-install-replace')->first()?->id,
            default => $industry->services()->first()?->id
        })->toArray();
    }
}
