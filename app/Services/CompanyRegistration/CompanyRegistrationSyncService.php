<?php

namespace App\Services\CompanyRegistration;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyAddress;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentOption;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyService;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\IndustryService;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Legacy\APIConsumer;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Exception;
use Illuminate\Support\Facades\Http;

class CompanyRegistrationSyncService extends APIConsumer
{
    const BASE_API_ROUTE            = '/api/v1/company-registration';
    const CREATE_COMPANY_API_ROUTE  = '/create-company';

    const EVENT_COMPANY_REFERENCE = 'company_reference';

    public function __construct(
        protected CompanyRepository $companyRepository
    )
    {
        parent::__construct(config('services.admin_integration.base_url') . self::BASE_API_ROUTE . '/');
    }

    private ?int $timeout = null;

    private ?int $retries = null;

    private ?int $retryInterval = 100;

    /**
     * @param int|null $timeout
     * @param int|null $retries
     * @param int|null $retryInterval
     * @return void
     */
    private function setOptions(?int $retries, ?int $retryInterval, ?int $timeout): void
    {
        $this->timeout = $timeout > 0 ? $timeout : null;
        $this->retries = $retries > 0 ? $retries : null;
        $this->retryInterval = $retryInterval > 0 ? $retryInterval : $this->retryInterval;
    }

    /**
     * @return PendingRequest
     * @throws Exception
     */
    private function getBaseHttpClient(): PendingRequest
    {
        $token = $this->authenticate();
        $baseClient = Http::withToken($token);

        if ($this->timeout) {
            $baseClient->timeout($this->timeout);
        }
        if ($this->retries && $this->retryInterval) {
            $baseClient->retry($this->retries, $this->retryInterval, throw: false);
        }

        return $baseClient;
    }

    /**
     * @param string $route
     * @param array|null $params
     * @param int|null $retries
     * @param int|null $retryInterval
     * @param int|null $timeout
     * @return Response
     * @throws Exception
     */
    public function get(string $route, ?array $params = null, ?int $retries = null, ?int $retryInterval = null, ?int $timeout = null): Response
    {
        $this->setOptions($retries, $retryInterval, $timeout);
        $baseClient = $this->getBaseHttpClient();
        return $baseClient->get($this->baseURL . trim(trim($route), '/'), $params ?? []);
    }

    /**
     * @param string $route
     * @param array|null $data
     * @param int|null $retries
     * @param int|null $retryInterval
     * @param int|null $timeout
     * @return Response
     * @throws Exception
     */
    public function post(string $route, ?array $data = null, ?int $retries = null, ?int $retryInterval = null, ?int $timeout = null): Response
    {
        $this->setOptions($retries, $retryInterval, $timeout);
        $baseClient = $this->getBaseHttpClient();
        return $baseClient->post($this->baseURL . trim(trim($route), '/'), $data ?? []);
    }

    /**
     * @param string $route
     * @param array|null $data
     * @param int|null $retries
     * @param int|null $retryInterval
     * @param int|null $timeout
     * @return Response
     * @throws Exception
     */
    public function patch(string $route, ?array $data = null, ?int $retries = null, ?int $retryInterval = null, ?int $timeout = null): Response
    {
        $this->setOptions($retries, $retryInterval, $timeout);
        $baseClient = $this->getBaseHttpClient();
        return $baseClient->patch($this->baseURL . trim(trim($route), '/'), $data ?? []);
    }

    /**
     * @param string $route
     * @param array|null $data
     * @param int|null $retries
     * @param int|null $retryInterval
     * @param int|null $timeout
     * @return Response
     * @throws Exception
     */
    public function put(string $route, ?array $data = null, ?int $retries = null, ?int $retryInterval = null, ?int $timeout = null): Response
    {
        $this->setOptions($retries, $retryInterval, $timeout);
        $baseClient = $this->getBaseHttpClient();
        return $baseClient->put($this->baseURL . trim(trim($route), '/'), $data ?? []);
    }

    /**
     * @param string $route
     * @param array|null $data
     * @param int|null $retries
     * @param int|null $retryInterval
     * @param int|null $timeout
     * @return Response
     * @throws Exception
     */
    public function delete(string $route, ?array $data = null, ?int $retries = null, ?int $retryInterval = null, ?int $timeout = null): Response
    {
        $this->setOptions($retries, $retryInterval, $timeout);
        $baseClient = $this->getBaseHttpClient();
        return $baseClient->delete($this->baseURL . trim(trim($route), '/'), $data ?? []);
    }


    /**
     * Dispatch Registration data to Legacy async using PubSub
     * Provide $modelClassName to have data automatically transformed from Odin key names
     * Omit $modelClassName to send $modelData unmolested
     * @param EventName $eventName
     * @param string $companyReference
     * @param array $modelData
     * @return bool
     */
    public function syncChangesToLegacy(EventName $eventName, string $companyReference, array $modelData): bool
    {
        $validCompany = $this->companyRepository->findByLegacyReferenceOrFail($companyReference);
        $modelData[self::EVENT_COMPANY_REFERENCE] = $companyReference;
        if ($validCompany) {
            DispatchPubSubEvent::dispatch(EventCategory::ADMIN2, $eventName, $modelData);
            return true;
        }
        else {
            logger()->error("Sync job not sent to Legacy - could not find a Company with reference \"{$companyReference}\"");
            return false;
        }
    }

    /**
     * Transforms data for Legacy before syncing
     * Will ignore any keys which don't exist in mapping
     * This can be disabled with $allowUnmappedKeys, but the keys must be identical to Legacy for that to be any use
     * @param string $className
     * @param array $modelData
     * @param bool $allowUnmappedKeys
     * @return array|null
     */
    public function transformForLegacy(string $className, array $modelData, bool $allowUnmappedKeys = false): ?array
    {
        // Companies transform
        $companyMapping = [
            Company::FIELD_ID           => EloquentCompany::ID,
            Company::FIELD_REFERENCE    => EloquentCompany::REFERENCE,
            Company::FIELD_NAME         => EloquentCompany::COMPANY_NAME,
            Company::FIELD_ENTITY_NAME  => EloquentCompany::COMPANY_LEGAL_ENTITY_NAME,
            Company::FIELD_ADMIN_LOCKED => EloquentCompany::BUYING_LEADS,
            Company::FIELD_STATUS       => EloquentCompany::STATUS,
            Company::FIELD_WEBSITE      => EloquentCompany::WEBSITE,
            Company::FIELD_WATCHDOG_ID  => EloquentCompany::WATCHDOG_ID,
            // Company data payload fields
            GlobalConfigurableFields::YEAR_STARTED_BUSINESS->value  => EloquentCompany::YEAR_STARTED_BUSINESS,
            GlobalConfigurableFields::TECH_SUPPORT_EMAIL->value     => EloquentCompany::TECH_SUPPORT_EMAIL,
            GlobalConfigurableFields::SALES_EMAIL->value            => EloquentCompany::SALES_EMAIL,
            GlobalConfigurableFields::DESCRIPTION->value            => EloquentCompany::DESCRIPTION,
        ];
        $companyMutators = [
            EloquentCompany::BUYING_LEADS   => fn($odinValue) => $odinValue ? -2 : null,
            EloquentCompany::STATUS         => fn() => EloquentCompany::STATUS_REGISTERING, // This will need a proper mapping if statuses apart from 'registering' need to be synced
        ];


        // CompanyUser transform
        $companyUserMapping = [
            CompanyUser::FIELD_ID           => EloquentUser::ID,
            CompanyUser::FIELD_EMAIL        => EloquentUser::EMAIL,
            CompanyUser::FIELD_FIRST_NAME   => EloquentUser::FIRST_NAME,
            CompanyUser::FIELD_LAST_NAME    => EloquentUser::LAST_NAME,
            CompanyUser::FIELD_STATUS       => EloquentUser::STATUS,
            CompanyUser::FIELD_REFERENCE    => 'reference', // Does not exist in Legacy, but required for syncing back
        ];
        $companyUserMutators = [];

        // CompanyUser transform to CompanyContact (used for imports)
        $companyContactMapping = [
            CompanyUser::FIELD_ID           => EloquentCompanyContact::FIELD_CONTACT_ID,
            CompanyUser::FIELD_EMAIL        => EloquentCompanyContact::FIELD_EMAIL,
            CompanyUser::FIELD_FIRST_NAME   => EloquentCompanyContact::FIELD_FIRST_NAME,
            CompanyUser::FIELD_LAST_NAME    => EloquentCompanyContact::FIELD_LAST_NAME,
            CompanyUser::FIELD_CELL_PHONE   => EloquentCompanyContact::FIELD_MOBILE,
            CompanyUser::FIELD_OFFICE_PHONE => EloquentCompanyContact::FIELD_PHONE,
            CompanyUser::FIELD_STATUS       => EloquentCompanyContact::FIELD_STATUS,
            CompanyUser::FIELD_TITLE        => EloquentCompanyContact::FIELD_TITLE,
            CompanyUser::FIELD_ZOOM_INFO_ID => EloquentCompanyContact::FIELD_ZOOM_INFO_ID,
            CompanyUser::FIELD_REFERENCE    => 'reference', // Does not exist in Legacy, but required for syncing back
        ];
        $companyContactMutators = [];

        // Company Location transform
        $companyLocationMapping = [ CompanyLocation::FIELD_NAME => EloquentCompanyAddress::FIELD_NAME ];
        $companyLocationMutators = [];

        // Company Address transform
        $companyAddressMapping = [
            Address::FIELD_ADDRESS_1    => EloquentAddress::ADDRESS1,
            Address::FIELD_ADDRESS_2    => EloquentAddress::ADDRESS2,
            Address::FIELD_CITY         => EloquentAddress::CITY,
            Address::FIELD_STATE        => EloquentAddress::STATE,
            Address::FIELD_COUNTRY      => EloquentAddress::COUNTRY,
            Address::FIELD_ZIP_CODE     => EloquentAddress::ZIP_CODE,
            Address::FIELD_LATITUDE     => EloquentAddress::LATITUDE,
            Address::FIELD_LONGITUDE    => EloquentAddress::LONGITUDE,
            Address::FIELD_PLACE_ID     => EloquentCompanyAddress::FIELD_GOOGLE_PLACE_ID
        ];
        $companyAddressMutators = [];

        // Company Service transform
        $companyServiceMapping = [
            IndustryService::FIELD_NAME         => EloquentOption::DISPLAY_NAME,
            IndustryService::FIELD_SLUG         => EloquentOption::NAME,
            IndustryService::FIELD_INDUSTRY_ID  => EloquentOption::TYPE,
        ];
        $companyServiceMutators = [];

        [ $mapping, $mutators ] = match($className) {
            Company::class                  => [ $companyMapping, $companyMutators ],
            CompanyUser::class              => [ $companyUserMapping, $companyUserMutators ],
            CompanyLocation::class          => [ $companyLocationMapping, $companyLocationMutators ],
            Address::class                  => [ $companyAddressMapping, $companyAddressMutators ],
            EloquentCompanyContact::class   => [ $companyContactMapping, $companyContactMutators ],
            CompanyService::class           => [ $companyServiceMapping, $companyServiceMutators ],
            default                         => [ null, null ]
        };

        if (!$mapping) return null;

        return collect($modelData)->reduce(function(array $output, $value, $key) use ($mapping, $mutators, $allowUnmappedKeys) {
            $legacyKey = $mapping[$key] ?? null;
            if (!$legacyKey) {
                return $allowUnmappedKeys
                    ? [ ...$output, $key => $value ]
                    : $output;
            }
            $value = $mutators && array_key_exists($legacyKey, $mutators)
                ? $mutators[$legacyKey]($value)
                : $value;
            return $legacyKey && $value !== null
                ? [ ...$output, $legacyKey => $value ]
                : $output;
        }, []);
    }

}
