<?php

namespace App\Services;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Models\Call;
use App\Models\ConsumerProcessingActivity;
use App\Models\Odin\ConsumerProduct;
use App\Models\Text;
use App\Models\User;
use App\Repositories\ConsumerProcessingActivityRepository;
use Exception;
use Throwable;

class ConsumerProcessingActivityService
{
    public function __construct(
        protected ConsumerProcessingActivityRepository $consumerProcessingActivityRepository
    ) {}

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProcessingActivityType $type
     * @param User $user
     * @param string|null $comment
     * @param string|null $reason
     * @param Call|Text|null $relatedActivity
     * @param ConsumerProcessingActivityVisibilityScope|null $scope
     * @return ConsumerProcessingActivity|null
     */
    public function createActivity(
        ConsumerProduct $consumerProduct,
        ConsumerProcessingActivityType $type,
        User $user,
        ?string $comment = null,
        ?string $reason = null,
        Call|Text|null $relatedActivity = null,
        ?ConsumerProcessingActivityVisibilityScope $scope = ConsumerProcessingActivityVisibilityScope::INTERNAL,
    ): ?ConsumerProcessingActivity
    {
        try {
            $basePayload = $this->getBasePayload($consumerProduct, $type, $user, $comment, $scope);

            return match ($type) {
                ConsumerProcessingActivityType::CALL,
                ConsumerProcessingActivityType::TEXT,
                    => $this->createFromContactEvent($basePayload, $type, $user, $relatedActivity),
                ConsumerProcessingActivityType::APPROVED,
                ConsumerProcessingActivityType::CANCELLED,
                ConsumerProcessingActivityType::UNDER_REVIEW,
                ConsumerProcessingActivityType::PENDING_REVIEW,
                    => $this->createFromStatusChangeEvent($basePayload, $type, $reason),
                ConsumerProcessingActivityType::USER_COMMENT,
                    => $this->createFromUserCommentEvent($basePayload, $type),
                default
                    => $this->createFromCustomEvent($basePayload, $type, $reason),
            };
        }
        catch(Throwable $e) {
            logger()->error("Error creating ConsumerProcessingActivity: " . $e->getMessage());
        }

        return null;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param array $activityIds
     * @param ConsumerProcessingActivityVisibilityScope $newScope
     * @return bool
     */
    public function modifyCommentScopes(ConsumerProduct $consumerProduct, array $activityIds, ConsumerProcessingActivityVisibilityScope $newScope): bool
    {
        return $consumerProduct->processingActivities()
            ->whereIn(ConsumerProduct::FIELD_ID, $activityIds)
            ->update([
                ConsumerProcessingActivity::FIELD_SCOPE => $newScope
            ]);
    }

    /**
     * @param array $payload
     * @param ConsumerProcessingActivityType $type
     * @param User $user
     * @param Call|Text $relatedActivity
     * @return ConsumerProcessingActivity|null
     * @throws Exception
     */
    protected function createFromContactEvent(array $payload, ConsumerProcessingActivityType $type, User $user, Call|Text $relatedActivity): ?ConsumerProcessingActivity
    {
        $payload[ConsumerProcessingActivity::FIELD_SUMMARY] = $type->getCommentTitle($user->name);
        $payload[ConsumerProcessingActivity::FIELD_ACTIVITY_ID] = $relatedActivity->id;

        return $this->consumerProcessingActivityRepository->createWithRelatedActivity($type, $payload);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProcessingActivityType $type
     * @param User $user
     * @param string|null $comment
     * @param string|null $reason
     * @return ConsumerProcessingActivity|null
     * @throws Exception
     */
    protected function createFromStatusChangeEvent(array $payload, ConsumerProcessingActivityType $type, ?string $reason): ?ConsumerProcessingActivity
    {
        $payload[ConsumerProcessingActivity::FIELD_SUMMARY] = $type->getCommentTitle($reason);

        return $this->consumerProcessingActivityRepository->create($type, $payload);
    }

    /**
     * @param array $payload
     * @param ConsumerProcessingActivityType $type
     * @return ConsumerProcessingActivity|null
     * @throws Exception
     */
    protected function createFromUserCommentEvent(array $payload, ConsumerProcessingActivityType $type): ?ConsumerProcessingActivity
    {
        $payload[ConsumerProcessingActivity::FIELD_SUMMARY] = $type->getCommentTitle();

        return $this->consumerProcessingActivityRepository->create($type, $payload);
    }

    /**
     * @param array $payload
     * @param ConsumerProcessingActivityType $type
     * @param string|null $summaryText
     * @return ConsumerProcessingActivity|null
     * @throws Exception
     */
    protected function createFromCustomEvent(array $payload, ConsumerProcessingActivityType $type, ?string $summaryText): ?ConsumerProcessingActivity
    {
        $payload[ConsumerProcessingActivity::FIELD_SUMMARY] = $summaryText;

        return $this->consumerProcessingActivityRepository->create($type, $payload);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerProcessingActivityType $type
     * @param User $user
     * @param string|null $comment
     * @param ConsumerProcessingActivityVisibilityScope|null $scope
     * @return array
     */
    protected function getBasePayload(ConsumerProduct $consumerProduct, ConsumerProcessingActivityType $type, User $user, ?string $comment = null, ?ConsumerProcessingActivityVisibilityScope $scope = null): array
    {
        return [
            ConsumerProcessingActivity::FIELD_CONSUMER_ID         => $consumerProduct->consumer_id,
            ConsumerProcessingActivity::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
            ConsumerProcessingActivity::FIELD_USER_ID             => $user->id,
            ConsumerProcessingActivity::FIELD_COMMENT             => trim($comment) ?: null,
            ConsumerProcessingActivity::FIELD_ACTIVITY_TYPE       => $type,
            ConsumerProcessingActivity::FIELD_SCOPE               => $scope ?? ConsumerProcessingActivityVisibilityScope::INTERNAL,
        ];
    }
}