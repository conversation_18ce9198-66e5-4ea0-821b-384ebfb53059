<?php

namespace App\Services;

use App\DataModels\SalesBait\SalesBaitPunter;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\SalesBait\SalesBaitCreatedEvent;
use App\Mail\RoofingCalculatorSalesBait;
use App\Mail\SolarReviewsSalesBait;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\SalesBaitLead;
use App\Repositories\SalesBait\SalesBaitRepository;
use App\Services\Delivery\Email;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use App\Services\PubSub\PubSubService;

class SalesBaitService
{
    /**
     * @param SalesBaitRepository $salesBaitRepository
     * @param PubSubService       $pubSubService
     * @param HelperService       $helperService
     */
    public function __construct(
        protected SalesBaitRepository $salesBaitRepository,
        protected PubSubService       $pubSubService,
        protected HelperService       $helperService
    ){}

    /**
     * @param EloquentQuote $lead
     * @param Collection $campaigns
     * @param Collection<EloquentCompany>|null $nonPurchasingCompanies
     * @return void
     */
    public function createSalesBaits(EloquentQuote $lead, Collection $campaigns, ?Collection $nonPurchasingCompanies): void
    {
        // cancel if the lead has no address
        if(!$lead->address){return;}
        $nonPurchasingCompanies = $nonPurchasingCompanies ? $nonPurchasingCompanies : collect();

        $companyRefs = [];
        foreach ($campaigns as $campaign){
            $salesBait = $this->salesBaitRepository->create($lead, $campaign);
            SalesBaitCreatedEvent::dispatch($salesBait->id);

            $companyRefs[] = $campaign->{LeadCampaign::RELATION_COMPANY}->reference;
        }

        foreach($nonPurchasingCompanies as $company) {
            $salesBait = $this->salesBaitRepository->createForCompany($lead, $company);
            SalesBaitCreatedEvent::dispatch($salesBait->id);

            $companyRefs[] = $company->reference;
        }

        /**
         * Temporarily Disabling PubSub event for SALES_BAIT_RECEIVED due to high load
         */
//        $companyRefs = array_unique($companyRefs);
//
//        foreach($companyRefs as $companyRef) {
//            $this->pubSubService->handle(
//                EventCategory::COMPANIES->value,
//                EventName::SALES_BAIT_RECEIVED->value,
//                ['company_reference' => $companyRef, 'lead_id' => $lead->{EloquentQuote::ID}]
//            );
//        }
    }

    /**
     * @param SalesBaitLead $salesBait
     * @param SalesBaitPunter $punters
     * @return void
     */
    public function deliverSMSToPunters(SalesBaitLead $salesBait, Collection $punters): void
    {
        // todo
    }

    /**
     * @param SalesBaitLead $salesBait
     * @param Collection    $punters
     * @return void
     */
    public function deliverEmailToPunters(SalesBaitLead $salesBait, Collection $punters): void
    {
        foreach ($punters as $punter){
            /** @var SalesBaitPunter $punter */
            $okToDeliverEmail = $this->helperService->isEmailValid($punter->email);
            if($okToDeliverEmail) {
                Email::send(
                    $punter->email,
                    $salesBait->lead->getLeadIndustry()->key == EloquentQuote::LEAD_INDUSTRY_ROOFING
                        ? new RoofingCalculatorSalesBait($salesBait->lead, $punter, $salesBait->id)
                        : new SolarReviewsSalesBait($salesBait->lead, $punter, $salesBait->id)
                );
            } else {
                $this->handleInvalidPunterEmail($salesBait, $punter);
            }
        }
    }

    /**
     * @param SalesBaitLead   $salesBait
     * @param SalesBaitPunter $punter
     * @return void
     */
    protected function handleInvalidPunterEmail(SalesBaitLead $salesBait, SalesBaitPunter $punter): void
    {
        logger()->warning(
            "SalesBait Email delivery ignored for the Punter carrying an invalid email - SalesBait ID: `{$salesBait->id}`, Punter Email: `{$punter->email}`, Punter ID: `{$punter->id}`"
        );
    }
}
