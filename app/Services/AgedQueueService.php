<?php

namespace App\Services;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\GlobalConfigurationAgedQueueField;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\Industry;
use App\Jobs\AgedQueue\RemoveDuplicateLeadsFromAgedQueueJob;
use App\Models\AvailableBudget;
use App\Models\AverageProductRevenueByLocation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\RecycledLeads\LeadProcessingAged;
use App\Models\RecycledLeads\RecycledLead;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Fluent;

class AgedQueueService
{
    /**
     * @param int $limit
     *
     * @return void
     */
    public function populateQueue(int $limit = 5000): void
    {
        $startDate = now()->subYear();
        $endDate = now()->subMonths(6);
        $processedConsumerProducts = collect();

        //todo: move aged queue config out of global config table.
        $agedQueueConfig = new Fluent(app(GlobalConfigurationRepository::class)->getConfigurationPayload(GlobalConfigurationKey::AGED_QUEUE)?->toArray() ?? []);
        $unsoldOnly = $agedQueueConfig->get("data." . GlobalConfigurationAgedQueueField::UNSOLD_ONLY->value, false);

        $this->getQuery($startDate, $endDate, [1], $unsoldOnly)->chunk(
            10000,
            fn(Collection $consumerProducts) => $consumerProducts->each(fn(ConsumerProduct $consumerProduct) => $processedConsumerProducts->push([
                LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID => $consumerProduct->id,
                LeadProcessingAged::FIELD_RECENCY_POINTS      => $this->calculateRecencyPointsForConsumerProduct($consumerProduct)
            ]))
        );

        $processedConsumerProducts = $processedConsumerProducts
            ->sortByDesc(LeadProcessingAged::FIELD_RECENCY_POINTS)
            ->take($limit)
            ->map(fn(array $data) => [...$data, LeadProcessingAged::CREATED_AT => now()]);

        LeadProcessingAged::query()->truncate();

        foreach ($processedConsumerProducts->chunk(100) as $chunk) {
            LeadProcessingAged::query()->insert($chunk->toArray());
        }

        RemoveDuplicateLeadsFromAgedQueueJob::dispatch();
    }

    /**
     * @return void
     */
    public function calculateRecencyPoints(): void
    {
        LeadProcessingAged::query()
            ->with(LeadProcessingAged::RELATION_CONSUMER_PRODUCT)
            ->chunk(500, function(Collection $chunk) {
                $revenueAndBudget = $this->getRevenueAndUnlimitedBudget($chunk->pluck(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID)->toArray());

                $chunk->each(function (LeadProcessingAged $leadProcessingAged) use ($revenueAndBudget) {
                    $leadProcessingAged->recency_points = $this->getPointsSoldStatus(
                            $leadProcessingAged->consumerProduct->productAssignment()->where(ProductAssignment::FIELD_DELIVERED, true)->count()
                        ) + $this->getPointForDaysOld(
                            now()->diffInDays($leadProcessingAged->consumerProduct->created_at)
                        ) + $this->getPointForHavingUnlimitedBudget(
                            (int) Arr::get($revenueAndBudget, "$leadProcessingAged->consumer_product_id." . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, 0)
                        ) + (int) Arr::get($revenueAndBudget, "$leadProcessingAged->consumer_product_id." . AverageProductRevenueByLocation::FIELD_AVERAGE_REVENUE)
                          + $this->calculatePointForAvailableCampaigns(
                            (int) Arr::get($revenueAndBudget, "$leadProcessingAged->consumer_product_id.campaign_count", 0)
                        );

                    $leadProcessingAged->save();
                });
            });
    }

    /**
     * @return void
     */
    public function removeDuplicateLeads(): void
    {
        $query = 'select '. LeadProcessingAged::TABLE .'.'. LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID .' from '.LeadProcessingAged::TABLE.'
                       join '. ConsumerProduct::TABLE .' on '. LeadProcessingAged::TABLE .'.'. LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID .' = '. ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID .'
                       join '. Consumer::TABLE .' on '. ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_ID .' = '. Consumer::TABLE .'.'. Consumer::FIELD_ID .'
                       join '. Address::TABLE .' on '. ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ADDRESS_ID .' = '. Address::TABLE .'.'. Address::FIELD_ID .'
                       join '. ServiceProduct::TABLE .' on '. ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID .' = '. ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID .'
                where exists(
                    select 1 from '. ConsumerProduct::TABLE .' cp
                        join '. Consumer::TABLE .' c on cp.'. ConsumerProduct::FIELD_CONSUMER_ID .' = c.'. Consumer::FIELD_ID .'
                        join '. Address::TABLE .' a on cp.'. ConsumerProduct::FIELD_ADDRESS_ID .' = a.'. Address::FIELD_ID .'
                        join '. ServiceProduct::TABLE .' sp on cp.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID .' = sp.'. ServiceProduct::FIELD_ID .'
                    where cp.'. ConsumerProduct::FIELD_STATUS .' != '. ConsumerProduct::STATUS_CANCELLED .'
                    and c.'. Consumer::FIELD_ID .' != '. Consumer::TABLE .'.'. Consumer::FIELD_ID .'
                    and c.'. Consumer::FIELD_EMAIL .' = '. Consumer::TABLE .'.'. Consumer::FIELD_EMAIL .'
                    and abs(datediff(now(), cp.'. ConsumerProduct::CREATED_AT .')) < 90
                    and sp.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID .' = '. ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID .'
                )';

        $consumerProductIds = collect(DB::select($query))->map(fn($result) => $result->consumer_product_id);

        if ($consumerProductIds->isEmpty()) {
            return;
        }

        LeadProcessingAged::query()
            ->whereIntegerInRaw(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID, $consumerProductIds->toArray())
            ->delete();


        foreach ($consumerProductIds->chunk(100) as $chunk) {
            RecycledLead::query()->insert(
                $chunk->map(fn($id) => [
                    RecycledLead::FIELD_CONSUMER_PRODUCT_ID => $id,
                    RecycledLead::FIELD_STATUS => RecycledLead::STATUS_CANCELED,
                    RecycledLead::CREATED_AT => now(),
                    RecycledLead::UPDATED_AT => now()
                ])->toArray()
            );
        }
    }

    /**
     * @param array $consumerProductIds
     *
     * @return array
     */
    protected function getRevenueAndUnlimitedBudget(array $consumerProductIds): array
    {
        return ConsumerProduct::query()
            ->select([
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
                AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT,
                AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_AVERAGE_REVENUE,
                'campaign.campaign_count'
            ])
            ->join(
                Address::TABLE,
                Address::TABLE . '.' . Address::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID
            )->join(
                ServiceProduct::TABLE,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
            )->join(
                IndustryService::TABLE,
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID
            )->leftJoin(
                AvailableBudget::TABLE,
                fn(JoinClause $joinClause) => $joinClause->on(
                    AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_LOCATION_ID,
                    Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
                )->on(
                    AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_INDUSTRY_ID,
                    IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID
                )->where(fn(JoinClause $query) => $query->where(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME, '>', 0)
                    ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS, '>', 0)
                    ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, '>', 0)
                )
            )->leftJoin(
                AverageProductRevenueByLocation::TABLE,
                fn(JoinClause $joinClause) => $joinClause->on(
                    AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID,
                    Address::TABLE . '.' . Address::FIELD_COUNTY_LOCATION_ID
                )->on(
                    AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_INDUSTRY_ID,
                    IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID
                )->on(
                    AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_PRODUCT_ID,
                    ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_PRODUCT_ID
                )
            )->leftJoinSub($this->getCampaignSubQuery([1]), 'campaign', function(JoinClause $joinClause) {
                $joinClause->on(
                    'campaign.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID,
                    Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
                )->on(
                    'campaign.' . CompanyCampaign::FIELD_SERVICE_ID,
                    IndustryService::TABLE . '.' . IndustryService::FIELD_ID
                );
            })
            ->whereIntegerInRaw(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, $consumerProductIds)
            ->get()
            ->keyBy(ConsumerProduct::FIELD_ID)
            ->toArray();
    }

    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param int[] $industryIds
     * @param bool|null $unsoldOnly
     * @return Builder
     */
    protected function getQuery(Carbon $startDate, Carbon $endDate, array $industryIds, ?bool $unsoldOnly = false): Builder
    {
        $excludedConsumerProducts = $this->getConsumerProductsToExclude();

        return ConsumerProduct::query()
            ->select([
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT,
                AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_AVERAGE_REVENUE,
                'pa.sold_count',
                AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT,
                'campaign.campaign_count'
            ])
            ->join(
                Address::TABLE,
                Address::TABLE . '.' . Address::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID
            )->join(
                ServiceProduct::TABLE,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
            )->join(
                IndustryService::TABLE,
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID
            )->join(
                AvailableBudget::TABLE,
                fn(JoinClause $joinClause) => $joinClause->on(
                    AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_LOCATION_ID,
                    Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
                )->on(
                    AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_INDUSTRY_ID,
                    IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID
                )->where(fn(JoinClause $query) => $query->where(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME, '>', 0)
                    ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS, '>', 0)
                    ->orWhere(AvailableBudget::TABLE . '.' . AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, '>', 0)
                )
            )->leftJoin(
                AverageProductRevenueByLocation::TABLE,
                fn(JoinClause $joinClause) => $joinClause->on(
                    AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID,
                    Address::TABLE . '.' . Address::FIELD_COUNTY_LOCATION_ID
                )->on(
                    AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_INDUSTRY_ID,
                    IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID
                )->on(
                    AverageProductRevenueByLocation::TABLE . '.' . AverageProductRevenueByLocation::FIELD_PRODUCT_ID,
                    ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_PRODUCT_ID
                )
            )->leftJoinSub($this->getAllocationSubQuery($startDate), 'pa', fn(JoinClause $joinClause) => $joinClause->on(
                'pa.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
            ))
            ->leftJoinSub($this->getCampaignSubQuery([1]), 'campaign', function(JoinClause $joinClause) {
                $joinClause->on(
                    'campaign.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID,
                    Address::TABLE . '.' . Address::FIELD_ZIP_CODE_LOCATION_ID
                )->on(
                    'campaign.' . CompanyCampaign::FIELD_SERVICE_ID,
                    IndustryService::TABLE . '.' . IndustryService::FIELD_ID
                );
            })->leftJoin(
                RecycledLead::TABLE,
                fn(JoinClause $joinClause) => $joinClause->on(
                    RecycledLead::TABLE . '.' . RecycledLead::FIELD_CONSUMER_PRODUCT_ID,
                    ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
                    ->where(RecycledLead::TABLE . '.' . RecycledLead::CREATED_AT, '>=', $startDate)
            )
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, '!=', ConsumerProduct::STATUS_CANCELLED)
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->whereNull(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CLONED_FROM_ID)
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, '>=', $startDate)
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, '<', $endDate)
            ->when($unsoldOnly,
                fn(Builder $query) => $query->whereNull('pa.sold_count'),
                fn(Builder $query) => $query->where(fn(Builder $query) =>
                    $query->whereNull('pa.sold_count')
                        ->orWhereRaw('pa.sold_count < ' . ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONTACT_REQUESTS))
            )
            ->when(
                !empty($excludedConsumerProducts),
                fn(Builder $query) => $query->whereIntegerNotInRaw(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, $excludedConsumerProducts)
            )->where(
                fn(Builder $query) => $query->whereNull(RecycledLead::TABLE . '.' . RecycledLead::FIELD_STATUS)
                    ->orWhereNotIn(RecycledLead::TABLE . '.' . RecycledLead::FIELD_STATUS, [RecycledLead::STATUS_SOLD, RecycledLead::STATUS_CANCELED])
            )->whereIn(IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID, $industryIds);
    }

    /**
     * @return array
     */
    protected function getConsumerProductsToExclude(): array
    {
        //Exclude ConsumerProducts that have been contacted within the past 14 days.
        return LeadProcessingAged::query()
            ->select(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID)
            ->where(fn(Builder $query) => $query->whereNotNull(LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_SKIPPED_AT)
                ->whereRaw('TIMESTAMPDIFF(DAY, ' . LeadProcessingAged::TABLE . '.' . LeadProcessingAged::FIELD_SKIPPED_AT . ', now()) <= 14')
            )
            ->pluck(LeadProcessingAged::FIELD_CONSUMER_PRODUCT_ID)
            ->toArray();
    }

    /**
     * @param Carbon $startDate
     *
     * @return Builder
     */
    public function getAllocationSubQuery(Carbon $startDate): Builder
    {
        return ProductAssignment::query()
            ->selectRaw(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID . ', COUNT(*) AS sold_count')
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::CREATED_AT, '>=', $startDate)
            ->groupBy(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID);
    }

    /**
     * @param array $serviceIds
     *
     * @return Builder
     */
    public function getCampaignSubQuery(array $serviceIds): Builder
    {
        return CompanyCampaign::query()
            ->select([
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID,
                DB::raw('count(*) as campaign_count')
            ])
            ->join(
                Company::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID
            )->join(
                CompanyCampaignLocationModule::TABLE,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID
            )->join(
                CompanyCampaignLocationModuleLocation::TABLE,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_ID
            )
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            ->whereIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID, $serviceIds)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_PRODUCT_ID, 1)
            ->where(Company::TABLE . '.' . Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
            ->groupBy([
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID
            ]);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return int
     */
    protected function calculateRecencyPointsForConsumerProduct(ConsumerProduct $consumerProduct): int
    {
        return $this->getPointsSoldStatus((int)$consumerProduct->sold_count)
            + $this->getPointForDaysOld(now()->diffInDays($consumerProduct->created_at))
            + (int)$consumerProduct->average_revenue
            + $this->getPointForHavingUnlimitedBudget((int) $consumerProduct->unlimited_budget_count)
            + $this->calculatePointForAvailableCampaigns((int) $consumerProduct->campaign_count);

    }

    /**
     * @param int $campaignCount
     *
     * @return int
     */
    protected function calculatePointForAvailableCampaigns(int $campaignCount): int
    {
        return $campaignCount * 100;
    }

    /**
     * @param int $soldCount
     *
     * @return int
     */
    protected function getPointsSoldStatus(int $soldCount): int
    {
        return match ($soldCount) {
            0       => 300, //unsold
            1       => 150, //only one leg sold
            default => 0,
        };
    }

    /**
     * @param int $daysOld
     *
     * @return int
     */
    protected function getPointForDaysOld(int $daysOld): int
    {
        $ranges = [
            [7, 14, 300],
            [15, 28, 200],
            [29, 90, 100],
            [91, 365, 50],
            [366, 730, 20],
        ];

        foreach ($ranges as [$min, $max, $points]) {
            if ($daysOld >= $min && $daysOld <= $max) {
                return $points;
            }
        }

        return 0;
    }

    /**
     * @param int $unlimitedBudgetCount
     *
     * @return int
     */
    protected function getPointForHavingUnlimitedBudget(int $unlimitedBudgetCount): int
    {
        return $unlimitedBudgetCount * 300;
    }
}
