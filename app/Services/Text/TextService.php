<?php

namespace App\Services\Text;

use App\Contracts\Services\Communication\CommunicationContract;
use App\DTO\SMS;
use App\Exceptions\Text\SendTextFailedException;
use App\Models\Text;
use App\Repositories\CommunicationRepository;
use Throwable;

class TextService
{
    public function __construct(
        protected CommunicationContract $communicationService,
        protected CommunicationRepository $communicationRepository,
    )
    {
    }

    /**
     * @param SMS $sms
     * @param bool|null $store
     * @return Text|null
     * @throws SendTextFailedException
     */
    public function send(
        SMS $sms,
        ?bool $store = true,
    ): ?Text
    {
        try {
            $reference = $this->communicationService->sendSMS(
                from: $sms->getFrom(),
                to: $sms->getToPhone(),
                body: $sms->getMessage(),
                fromType: $sms->getFromType(),
                meta: $sms->getMeta(),
            );
        } catch (Throwable $e) {
            throw new SendTextFailedException(
                message: $e->getMessage(),
                sms: $sms,
                previous: $e,
            );
        }

        $text = null;

        if ($store && $sms->getFromPhoneId()) {
            $text = $this->communicationRepository->updateOrCreateOutboundSMS(
                serviceName: $this->communicationService->getServiceName(),
                reference: $reference,
                fromPhoneId: $sms->getFromPhoneId(),
                toNumber: $sms->getToPhone(),
                body: $sms->getMessage(),
                relType: null,
                relId: null,
            );
        }

        return $text;
    }
}
