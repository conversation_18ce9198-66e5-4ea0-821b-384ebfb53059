<?php

namespace App\Services\Consumer;

use App\Enums\Odin\Product;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Odin\TopCompanies\TopCompaniesService;
use Illuminate\Support\Collection;

class ConsumerService
{
    public function __construct(
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected TopCompaniesService         $topCompaniesService,
    )
    {
    }

    /**
     * @param Consumer $consumer
     * @return Collection
     */
    public function getConsumerBuyerIds(Consumer $consumer): Collection
    {
        $clonedConsumers = collect($consumer->clones);

        $clonedConsumers->add($consumer);

        $companyIds = collect();

        foreach ($clonedConsumers as $con) {
            $assignments = $this->productAssignmentRepository->getAllAssignmentsForConsumer($con);
            $companyIds->push(...$assignments->pluck(ProductAssignment::FIELD_COMPANY_ID)->values()->toArray())->flatten();
        }

        return $companyIds;
    }

    /**
     * @param Consumer $consumer
     * @param int $limit
     * @return Collection
     */
    public function getTopCompaniesAndCampaignsForConsumer(Consumer $consumer, int $limit = 4): Collection
    {
        $excludeCompanyIds = $this->getConsumerBuyerIds($consumer);

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        return $this->topCompaniesService->getTopCampaignsAndCompaniesForConsumerProduct(
            consumerProduct   : $consumerProduct,
            limit             : $limit,
            excludedCompanyIds: $excludeCompanyIds->values()->toArray()
        );
    }

}
