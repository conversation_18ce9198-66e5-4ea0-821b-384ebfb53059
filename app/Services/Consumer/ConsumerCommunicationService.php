<?php

namespace App\Services\Consumer;

use App\Enums\CommunicationType;
use App\Models\Odin\ProductAssignment;
use App\Models\ProductAssignmentCommunication;

class ConsumerCommunicationService
{
    /**
     * @param ProductAssignment $productAssignment
     * @param CommunicationType $type
     * @param string $sid
     * @param array $data
     *
     * @return void
     */
    public function createProductAssignmentCommunication(ProductAssignment $productAssignment, CommunicationType $type, string $sid, array $data = []): void
    {
        $productAssignment->communications()->create([
            ProductAssignmentCommunication::FIELD_SID => $sid,
            ProductAssignmentCommunication::FIELD_COMMUNICATION_TYPE => $type,
            ProductAssignmentCommunication::FIELD_CONTENT => $data,
        ]);
    }

    /**
     * @param string $sid
     * @param array $data
     *
     * @return void
     */
    public function updateProductAssignmentCommunication(string $sid, array $data): void
    {
        $communication = ProductAssignmentCommunication::query()->where(ProductAssignmentCommunication::FIELD_SID, $sid)->first();

        if ($communication) {
            $communication->update([
                ProductAssignmentCommunication::FIELD_CONTENT => array_merge($communication->content ?? [], $data)
            ]);
        }
    }
}
