<?php

namespace App\Services\Roles;

use App\Enums\CompanyUserRelationships\CompanyUserRelationshipType;
use App\Mail\CompanyRegistration\V3\CompanyContractAcceptedNotification;
use App\Mail\CompanyRegistration\V3\CompanyDeletionNotification;
use App\Mail\CompanyRegistration\V3\CompanyUserRelationshipAssignmentNotification;
use App\Mail\CompanyRegistration\V3\CompanyUserRelationshipUnassignmentNotification;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class CompanyRoleNotificationService
{
    /**
     * Send an email notification to the user about the company assignment
     *
     * @param User $user
     * @param Company $company
     * @param string $type
     * @param string $action
     * @param string $reason
     * @param array|null $otherData
     * @return void
     */
    public function notifyUser(
        User    $user,
        Company $company,
        string  $type,
        string  $action,
        string  $reason,
        ?array $otherData = [],
    ): void
    {
        $mailable = match ($action) {
            'company_deleted'   => new CompanyDeletionNotification($user, $company, $type, $reason),
            'assigned'          => new CompanyUserRelationshipAssignmentNotification($user, $company, $type, $reason),
            'unassigned'        => new CompanyUserRelationshipUnassignmentNotification($user, $company, $type, $reason),
            'contract_accepted' => new CompanyContractAcceptedNotification($user, $company, $type, $reason, $otherData),
        };

        try {
            Mail::to($user->email)->queue($mailable);
        } catch (\Exception $e) {
            Log::error("Failed to send notification email to user #{$user->id}", [
                'error'      => $e->getMessage(),
                'company_id' => $company->id
            ]);
        }
    }

    public function notifyAllCompanyManagers(
        Company $company,
        string  $action,
        string  $reason,
        ?array  $otherData = [],
    ): void
    {
        $companyManagers = $company->userRelationships;

        /** @var CompanyUserRelationship $companyManager */
        foreach ($companyManagers as $companyManager) {
            $this->notifyUser(
                user: $companyManager->user,
                company: $company,
                type: Str::headline($companyManager->role->name),
                action: $action,
                reason: $reason,
                otherData: $otherData,
            );
        }
    }

}
