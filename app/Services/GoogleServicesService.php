<?php

namespace App\Services;

use App\Enums\GoogleServiceType;
use App\Models\GoogleUserToken;
use App\Models\User;
use Exception;
use Illuminate\Support\Collection;

class GoogleServicesService
{
    /**
     * @param User $user
     * @return array
     * @throws Exception
     */
    public function getServiceUrls(User $user): array
    {
        /** @var Collection $tokens */
        $tokens = GoogleUserToken::query()
            ->where(GoogleUserToken::FIELD_USER_ID, $user->id)
            ->get()
            ->mapToGroups(function (GoogleUserToken $token) {
                return [
                    $token->service->value => $token
                ];
            });


        $result = [];
        foreach (GoogleServiceType::cases() as $type) {
            $provider = $type->getProvider();

            $hasGivenConsent = $tokens->has($type->value);

            $result[$type->value] = [
                'link'              => !$hasGivenConsent ? $provider->generateOAuthConsentScreenUrl($user->id) : null,
                'has_given_consent' => $hasGivenConsent,
                'service'           => $type->value
            ];
        }

        return $result;
    }
}
