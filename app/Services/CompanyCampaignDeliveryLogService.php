<?php

namespace App\Services;

use App\Repositories\CompanyCampaignDeliveryLogRepository;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;

class CompanyCampaignDeliveryLogService
{
    public function __construct(
        protected CompanyCampaignDeliveryLogRepository $deliveryLogRepository
    )
    {
    }

    /**
     * @param int $perPage
     * @param int $page
     * @param int|null $companyId
     * @param bool|null $succeeded
     * @param string|null $campaign
     * @param int|null $consumerProductId
     * @param array|null $dateRange
     * @param int|null $invoiceId
     *
     * @return LengthAwarePaginator
     */
    public function listDeliveryLogs(
        int     $perPage,
        int     $page,
        ?int    $companyId = null,
        ?bool   $succeeded = null,
        ?string $campaign = null,
        ?int    $consumerProductId = null,
        ?array  $dateRange = null,
        ?int    $invoiceId = null
    ): LengthAwarePaginator
    {
        return $this->deliveryLogRepository->listDeliveryLogs(
            perPage             : $perPage,
            page                : $page,
            companyId           : $companyId,
            succeeded           : $succeeded,
            campaign            : $campaign,
            consumerProductId   : $consumerProductId,
            dateRange           : $dateRange,
            invoiceId           : $invoiceId
        );
    }

}
