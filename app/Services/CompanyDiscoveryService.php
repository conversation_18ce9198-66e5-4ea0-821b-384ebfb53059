<?php

namespace App\Services;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\EventName;
use App\Jobs\SyncCompanyLocationToLegacyJob;
use App\Models\CompanyDiscovery\AddressDiscoveryStatus;
use App\Models\CompanyDiscovery\USZipCodeDiscoveryStatus;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyExternalReview;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Services\CompanyRegistration\CompanyRegistrationSyncService;
use App\Services\ExternalSearch\GooglePlacesService;
use Illuminate\Support\Arr;
use Ramsey\Uuid\Uuid;

class CompanyDiscoveryService
{
    const REVIEW_NAME = 'places';

    const DOMAIN_BLACKLIST = [
        'google.com',
        'facebook.com',
        'wixsite.com',
        'homedepot.com',
        'lowes.com',
        'business.site',
        'energysage.com',
        'angi.com',
        'homeadvisor.com',
        'ecowatch.com',
        'modernize.com',
        'linked.com',
        'instagram.com'
    ];

    /** @var CompanyRegistrationSyncService $syncService */
    protected CompanyRegistrationSyncService $syncService;

    /** @var GooglePlacesService $placesService */
    protected GooglePlacesService $placesService;

    /**
     * @param CompanyRegistrationSyncService $syncService
     */
    public function __construct(CompanyRegistrationSyncService $syncService, GooglePlacesService $placesService)
    {
        $this->syncService = $syncService;
        $this->placesService = $placesService;
    }

    /**
     * @param string $placeId
     * @param CompanyLocation $companyLocation
     * @param array|null $details
     * @return void
     */
    private function updateOrCreateCompanyExternalReview(string $placeId, CompanyLocation $companyLocation, ?array $details): void
    {
        if ($details && Arr::get($details, GooglePlacesService::API_KEY_USER_RATINGS_TOTAL)) {
            CompanyExternalReview::updateOrCreate(
                [
                    CompanyExternalReview::FIELD_COMPANY_LOCATION_ID => $companyLocation->id,
                    CompanyExternalReview::FIELD_REFERENCE           => $placeId,
                ],
                [
                    CompanyExternalReview::FIELD_COMPANY_LOCATION_ID => $companyLocation->id,
                    CompanyExternalReview::FIELD_REFERENCE           => $placeId,
                    CompanyExternalReview::FIELD_NAME                => self::REVIEW_NAME,
                    CompanyExternalReview::FIELD_AGG_COUNT           => Arr::get($details, GooglePlacesService::API_KEY_USER_RATINGS_TOTAL),
                    CompanyExternalReview::FIELD_AGG_VALUE           => Arr::get($details, GooglePlacesService::API_KEY_RATING),
                ]
            );
        }
    }

    /**
     * @param Company $company
     * @param Address $address
     * @param array|null $details
     * @return CompanyLocation
     */
    public function createOrGetCompanyLocation(
        Company $company,
        Address $address,
        array   $details = null
    ): CompanyLocation
    {
        /** @var CompanyLocation $companyLocation */
        $companyLocation = CompanyLocation::query()
            ->where(CompanyLocation::FIELD_COMPANY_ID, $company->id)
            ->where(CompanyLocation::FIELD_ADDRESS_ID, $address->id)
            ->first();

        if (!$companyLocation) {
            $insertArray = [
                CompanyLocation::FIELD_REFERENCE  => Uuid::uuid4(),
                CompanyLocation::FIELD_COMPANY_ID => $company->id,
                CompanyLocation::FIELD_ADDRESS_ID => $address->id,
                CompanyLocation::FIELD_IMPORTED   => true
            ];

            if ($details) {
                $insertArray = array_merge($insertArray, [
                    CompanyLocation::FIELD_PHONE => Arr::get($details, GooglePlacesService::API_KEY_PHONE_NUMBER),
                ]);
            }

            $companyLocation = CompanyLocation::create($insertArray);
        }

        return $companyLocation;
    }

    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     * @return void
     */
    public function syncCompanyContactToLegacy(Company $company, CompanyUser $companyUser): void
    {
        $transformedData                            = $this->syncService->transformForLegacy(EloquentCompanyContact::class, $companyUser->toArray());
        $transformedData['contact_odin_id']         = $companyUser->id;

        $legacyData = [
            'contact' => $transformedData
        ];

        $this->syncService->syncChangesToLegacy(
            EventName::EVENT_COMPANY_CONTACT_IMPORTED,
            $company->{Company::FIELD_REFERENCE},
            $legacyData
        );
    }

    /**
     * @param Company $company
     * @param CompanyLocation $companyLocation
     * @return void
     */
    public function syncCompanyLocationToLegacy(Company $company, CompanyLocation $companyLocation): void
    {
        $transformedData                            = $this->syncService->transformForLegacy(Address::class, $companyLocation->address->toArray());
        $transformedData[EloquentAddress::PHONE]    = $companyLocation->{CompanyLocation::FIELD_PHONE} ?? '5555555555'; // Phone cannot be null in legacy table
        $transformedData['address_odin_id']         = $companyLocation->address_id;

        $legacyData = [
            'location' => $transformedData
        ];

        $this->syncService->syncChangesToLegacy(
            EventName::EVENT_COMPANY_LOCATION_IMPORTED,
            $company->{Company::FIELD_REFERENCE},
            $legacyData
        );
    }

    /**
     * @param array|null $details
     * @return string|bool|null
     */
    public function getWebsiteIfValid(?array $details): bool|string|null
    {
        $website = Arr::get($details, GooglePlacesService::API_KEY_WEBSITE);

        if (is_null($website)) {
            return null;
        }

        $website = getWebsiteDomain($website);

        if (!in_array($website, self::DOMAIN_BLACKLIST, true)) {
            return $website;
        }

        return false;
    }

    /**
     * @param Address $address
     * @return void
     */
    public function processPlaceByAddress(Address $address): void
    {
        $placeId = $address->place_id;
        $details = $this->placesService->getCompanyDetailsByPlaceId($placeId);
        $website = $this->getWebsiteIfValid($details);

        if ($placeId && $website) {
            $industryId = $this->getIndustryId($address);

            $company = $this->createOrGetCompany($website, $details, $industryId);

            $address->update([
                Address::FIELD_ADDRESS_1 => trim($this->getAddressComponentByType($details, GooglePlacesService::API_KEY_STREET_NUMBER) . ' ' . $this->getAddressComponentByType($details, GooglePlacesService::API_KEY_STREET)),
                Address::FIELD_CITY      => $this->getAddressComponentByType($details, GooglePlacesService::API_KEY_CITY),
                Address::FIELD_ZIP_CODE  => $this->getAddressComponentByType($details, GooglePlacesService::API_KEY_ZIP_CODE),
                Address::FIELD_COUNTRY   => $this->getAddressComponentByType($details, GooglePlacesService::API_KEY_COUNTRY),
                Address::FIELD_STATE     => $this->getAddressComponentByType($details, GooglePlacesService::API_KEY_STATE, false)
            ]);

            $companyLocation = $this->createOrGetCompanyLocation($company, $address, $details);
            $this->updateOrCreateCompanyExternalReview($placeId, $companyLocation, $details);

            // Only sync address to legacy if company already exists in legacy
            $company->refresh();
            if($company->legacy_id) {
                $this->syncCompanyLocationToLegacy($company, $companyLocation);
            } else {
                SyncCompanyLocationToLegacyJob::dispatch($company, $companyLocation)->delay(now()->addMinutes(5));
            }
        }
    }

    /**
     * @param Address $address
     * @return int
     */
    private function getIndustryId(Address $address): int
    {
        return AddressDiscoveryStatus::query()
            ->join(USZipCodeDiscoveryStatus::TABLE, AddressDiscoveryStatus::TABLE.'.'.AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE, '=', USZipCodeDiscoveryStatus::TABLE.'.'.USZipCodeDiscoveryStatus::FIELD_DISCOVERY_REFERENCE)
            ->where(AddressDiscoveryStatus::TABLE.'.'.AddressDiscoveryStatus::FIELD_ADDRESS_ID, $address->id)
            ->first()
            ->industry_id;
    }

    /**
     * @param array $result
     * @param string $requestedType
     * @param bool $shortName
     * @return string|null
     */
    private function getAddressComponentByType(array $result, string $requestedType, bool $shortName = true): ?string
    {
        foreach ($result['address_components'] as $addressComponent) {
            foreach ($addressComponent['types'] as $type) {
                if ($type === $requestedType) {
                    return $shortName ? $addressComponent['short_name'] : $addressComponent['long_name'];
                }
            }
        }

        return null;
    }

    /**
     * @param string $website
     * @param array $companyDetails
     * @param int $industryId
     * @return Company
     */
    private function createOrGetCompany(string $website, array $companyDetails, int $industryId): Company
    {
        /** @var Company|null $company */
        $company = Company::query()
            ->where(Company::FIELD_WEBSITE, $website)
            ->first();

        // If no matching website make new company
        if (!$company) {
            $company = $this->createCompany($companyDetails, $website, $industryId);
        } else if (
            CompanyIndustry::query()
                ->where(CompanyIndustry::FIELD_COMPANY_ID, $company->id)
                ->where(CompanyIndustry::FIELD_INDUSTRY_ID, $industryId)
                ->doesntExist()
        ) {
            // If company exists we want to make sure that newly discovered industry exists
            $this->createCompanyIndustry($company->id, $industryId);
        }

        return $company;
    }

    /**
     * todo: Check how new company should be saved
     *
     *
     * @param array $companyDetails
     * @param string $website
     * @param int $industryId
     * @return Company
     */
    private function createCompany(array $companyDetails, string $website, int $industryId): Company
    {
        /** @var Company $company */
        $company = Company::create([
            Company::FIELD_NAME           => Arr::get($companyDetails, 'name'),
            Company::FIELD_ENTITY_NAME    => Arr::get($companyDetails, 'name'),
            Company::FIELD_WEBSITE        => $website,
            Company::FIELD_ADMIN_STATUS   => CompanyAdminStatus::ADMIN_LOCKED,
            Company::FIELD_IMPORTED       => true
        ]);

        $this->createCompanyIndustry($company->id, $industryId);
        $this->syncCompanyToLegacy($company);

        return $company;
    }

    /**
     * @param int $companyId
     * @param int $industryId
     * @return void
     */
    private function createCompanyIndustry(int $companyId, int $industryId): void
    {
        CompanyIndustry::create([
            CompanyIndustry::FIELD_COMPANY_ID  => $companyId,
            CompanyIndustry::FIELD_INDUSTRY_ID => $industryId
        ]);
    }

    /**
     * @param Company $company
     * @return void
     */
    private function syncCompanyToLegacy(Company $company): void
    {
        $legacyData = [
            'company'   => $this->syncService->transformForLegacy(Company::class, $company->toArray())
        ];

        // sync without user
        $this->syncService->syncChangesToLegacy(
            EventName::EVENT_COMPANY_IMPORTED,
            $company->{Company::FIELD_REFERENCE},
            $legacyData
        );
    }

    /**
     * @param string $placeId
     * @param string $latitude
     * @param string $longitude
     * @param string|null $discoveryReference
     * @return Address
     */
    public function createAddress(string $placeId, string $latitude, string $longitude, string $discoveryReference = null): Address
    {
        $address = Address::query()->where(Address::FIELD_PLACE_ID, $placeId)->first();

        if (!$address) {
            $address = Address::create([
                Address::FIELD_PLACE_ID  => $placeId,
                Address::FIELD_LATITUDE  => $latitude,
                Address::FIELD_LONGITUDE => $longitude,
                Address::FIELD_IMPORTED  => true
            ]);
        }

        if($discoveryReference) {
            AddressDiscoveryStatus::query()->firstOrCreate([
                AddressDiscoveryStatus::FIELD_ADDRESS_ID          => $address->id,
                AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE => $discoveryReference,
            ],[
                AddressDiscoveryStatus::FIELD_ADDRESS_ID          => $address->id,
                AddressDiscoveryStatus::FIELD_DISCOVERY_REFERENCE => $discoveryReference,
                AddressDiscoveryStatus::FIELD_CHECKED             => false
            ]);
        }

        return $address;
    }

    /**
     * @param array $companyDetails
     * @param string|null $discoveryReference
     * @return Address|null
     */
    public function processCompany(array $companyDetails, string $discoveryReference = null): ?Address
    {
        $placeId         = Arr::get($companyDetails, GooglePlacesService::API_KEY_PLACE_ID);
        $business_status = Arr::get($companyDetails, GooglePlacesService::API_KEY_BUSINESS_STATUS);
        $latitude        = Arr::get($companyDetails, GooglePlacesService::API_KEY_NEARBY_LATITUDE);
        $longitude       = Arr::get($companyDetails, GooglePlacesService::API_KEY_NEARBY_LONGITUDE);

        if ($business_status !== GooglePlacesService::BUSINESS_STATUS_CLOSED_PERMANENTLY) {
            if ($placeId) {
                return $this->createAddress($placeId, $latitude, $longitude, $discoveryReference);
            }
        }

        return null;
    }
}
