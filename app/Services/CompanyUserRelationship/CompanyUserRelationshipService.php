<?php

namespace App\Services\CompanyUserRelationship;

use App\Enums\ActivityLog\ActivityLogName;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\CompanyUserRelationship\CompanyUserRelationshipRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class CompanyUserRelationshipService
{
    public function __construct(
        protected CompanyUserRelationshipRepository $companyUserRelationshipRepository,
        protected ActivityLogRepository $activityLogRepository,
    )
    {
    }

    /**
     * @param int|null $companyId
     * @param string|null $name
     * @param array|null $roles
     * @param bool|null $active
     * @return Builder
     */
    public function list(
        ?int $companyId = null,
        ?string $name = null,
        ?array $roles = null,
        ?bool $active = null,
    ): Builder
    {
        return $this->companyUserRelationshipRepository->list(
            companyId: $companyId,
            name: $name,
            roles: $roles,
            active: $active,
        );
    }

    /**
     * @param CompanyUserRelationship $companyUserRelationship
     * @param Carbon|null $commissionableAt
     * @param Carbon|null $commissionableTo
     *
     * @return bool
     */
    public function update(
        CompanyUserRelationship $companyUserRelationship,
        ?Carbon                 $commissionableAt = null,
        ?Carbon                 $commissionableTo = null,
    ): bool
    {
        $original = $companyUserRelationship->toArray();

        $updated = $this->companyUserRelationshipRepository->update(
            companyUserRelationship: $companyUserRelationship,
            commissionableAt: $commissionableAt,
            commissionableTo: $commissionableTo,
        );

        $this->activityLogRepository->createActivityLog(
            logName: ActivityLogName::COMPANY_ROLE_UPDATED->value,
            description: ActivityLogRepository::systemOrUser()->value,
            subjectType: Company::class,
            subjectId: $companyUserRelationship->company_id,
            properties: [
                'old' => $original,
                'new' => $companyUserRelationship->refresh()->toArray(),
            ],
        );

        return $updated;
    }

}
