<?php

namespace App\Services;

use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Repositories\LeadCampaignRepository;
use App\Repositories\OffHourLeadCompaniesRepository;
use App\Services\LeadProcessing\LeadProcessingService;
use Illuminate\Support\Collection;

class OffHourLeadCompaniesService
{

    const PROCESSING_MESSAGE                 = 'Allocated outside of processing hours -- without verification';
    const DEFAULT_NUMBER_OF_QUOTES_REQUESTED = 1;

    public function __construct(
        protected OffHourLeadCompaniesRepository $offHourCompanyRepository,
        protected LeadProcessingService $service,
        protected LeadCampaignRepository $leadCampaignRepository
    ) {}

    /**
     * @param EloquentQuote $lead
     * @return int -- represents the amount of companies that the lead was allocated to
     */
    public function attemptAllocation(EloquentQuote $lead): int
    {
        $campaigns = $this->offHourCompanyRepository->companyCampaignsAvailableForLead($lead)->take($lead->numberofquotes ?? self::DEFAULT_NUMBER_OF_QUOTES_REQUESTED);
        if($campaigns->count() === 0){return 0;}
        $salesTypeConfigurations = $this->selectConfigurationsFromCampaigns($campaigns);
        $this->service->forceAllocateLeadNow($lead, $salesTypeConfigurations, self::PROCESSING_MESSAGE);
        return $salesTypeConfigurations->count();
    }

    /**
     * @param Collection $campaigns
     * @return Collection <int, LeadCampaignSalesTypeConfiguration>
     */
    private function selectConfigurationsFromCampaigns(Collection $campaigns): Collection
    {
        $saleType = SaleTypes::mapAllocationsToVerifiedType($campaigns->count());
        $legacySaleTypeId = SaleTypes::mapSaleTypeToLegacyId($saleType);
        return $this->leadCampaignRepository->findCampaignConfigsBySalesType($campaigns->pluck(LeadCampaign::ID)->toArray(), $legacySaleTypeId);
    }
}
