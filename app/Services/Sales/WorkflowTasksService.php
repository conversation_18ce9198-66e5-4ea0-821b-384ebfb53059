<?php

namespace App\Services\Sales;

use App\Models\Odin\Company;
use App\Models\Sales\Task;
use App\Models\Sales\ContactCompanyTask;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class WorkflowTasksService
{
    /**
     * @param int $id
     * @return Task|null
     */
    public function find(int $id): ?Task
    {
        return Task::find($id);
    }

    /**
     * @param int $id
     * @return bool
     */
    public function delete(int $id): bool
    {
        return Task::query()->where(Task::FIELD_ID, $id)->delete();
    }

    /**
     * @param Task $task
     * @return array
     */
    public function getTaskPayload(Task $task): array
    {
        $payload = $task->runningWorkflow ? $task->runningWorkflow?->payload?->toArray() : $task->completedWorkflow?->payload?->toArray();
        $payload["supplementary"] = [];

        if (!Arr::get($payload, 'event.event_data.company_id') && Arr::get($payload, 'event.event_data.company_reference')) {
            /** @var Company|null $company */
            $company = Company::query()->where(Company::FIELD_REFERENCE, Arr::get($payload, 'event.event_data.company_reference'))->first();

            if ($company) {
                Arr::set($payload, 'event.event_data.company_id', $company->id);
                Arr::set($payload, 'event.event_data.company_name', $company->name);
            }
        }

        return $payload;
    }
}
