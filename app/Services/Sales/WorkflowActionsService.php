<?php

namespace App\Services\Sales;

use App\Models\WorkflowAction;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class WorkflowActionsService
{
    /**
     * @param int|null $parentId
     * @param string|null $parentType
     * @param string|null $displayName
     * @param string|null $actionType
     * @return Collection
     */
    public function search(
        ?int $parentId = null,
        ?string $parentType = null,
        ?string $displayName = null,
        ?string $actionType = null
    ): Collection
    {
        $query = WorkflowAction::query();

        if($parentId > 0) {
            $query->where(WorkflowAction::FIELD_PREVIOUS_NODE_ID, $parentId);
        }

        if(strlen($parentType) > 0) {
            $query->where(WorkflowAction::FIELD_PREVIOUS_NODE_TYPE, $parentType);
        }

        if(strlen($displayName) > 0) {
            $query->where(WorkflowAction::FIELD_DISPLAY_NAME, $displayName);
        }

        if(strlen($actionType) > 0) {
            $query->where(WorkflowAction::FIELD_ACTION_TYPE, $actionType);
        }

        return $query->get();
    }

    /**
     * @param int $id
     * @return WorkflowAction|null
     */
    public function getById(int $id): ?WorkflowAction
    {
        return WorkflowAction::find($id);
    }

    /**
     * @param int|null $id
     * @param int|null $parentId
     * @param string|null $parentType
     * @param string|null $displayName
     * @param string|null $actionType
     * @return Model|WorkflowAction
     */
    public function updateOrCreate(
        ?int $id = null,
        ?int $parentId = null,
        ?string $parentType = null,
        ?string $displayName = null,
        ?string $actionType = null
    ): Model|WorkflowAction
    {
        return WorkflowAction::query()->updateOrCreate(
            [
                WorkflowAction::FIELD_ID => $id
            ],
            [
                WorkflowAction::FIELD_PREVIOUS_NODE_ID   => $parentId,
                WorkflowAction::FIELD_PREVIOUS_NODE_TYPE => $parentType,
                WorkflowAction::FIELD_DISPLAY_NAME       => $displayName,
                WorkflowAction::FIELD_ACTION_TYPE        => $actionType
            ]
        );
    }

    /**
     * @param int $id
     * @return bool
     * @throws Exception
     */
    public function delete(int $id): bool
    {
        return WorkflowAction::query()->where(WorkflowAction::FIELD_ID, $id)->delete();
    }
}
