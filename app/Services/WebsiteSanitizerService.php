<?php

namespace App\Services;

use Illuminate\Support\Collection;

class WebsiteSanitizerService
{
    const array BLACKLIST_DOMAINS = [
        'google.com',
        'facebook.com',
        'linkedin.com',
        'instagram.com',
        'x.com',
        'twitter.com',
        'youtube.com',
    ];

    /**
     * @return array|string[]
     */
    public function getBlacklistDomains(): array
    {
        return self::BLACKLIST_DOMAINS;
    }

    /**
     * @param Collection $websites
     * @return Collection
     */
    public function extractDomain(Collection $websites): Collection
    {
        return $websites->map(function ($website) {
            $website = trim((string)$website);

            if (empty($website)) {
                return null;
            }

            if (!preg_match('/^https?:\/\//', $website)) {
                $website = 'http://' . $website;
            }

            $parsed = parse_url($website);

            if (!$parsed || !isset($parsed['host'])) {
                return null;
            }

            $host = $parsed['host'];

            $host = preg_replace('/^www\./i', '', $host);

            $host = strtolower($host);

            if (!filter_var($host, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME)) {
                return null;
            }

            return $host;
        })->filter()->unique()->values();
    }

    /**
     * @param Collection $websites
     * @return Collection
     */
    public function filterOutBlacklistDomains(Collection $websites): Collection
    {
        return $this->extractDomain($websites)->reject(function ($website) {
            return collect($this->getBlacklistDomains())->first(function ($domain) use ($website) {
                return str_contains($website, $domain);
            });
        })->values();
    }
}
