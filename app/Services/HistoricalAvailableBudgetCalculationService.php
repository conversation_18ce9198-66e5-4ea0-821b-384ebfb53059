<?php

namespace App\Services;

use App\Models\AvailableBudget;
use App\Repositories\HistoricalAvailableLocationBudgetRepository;
use App\Repositories\LeadCampaignRepository;
use App\Services\Campaigns\CalculateBudgetService;
use App\Strategies\CampaignBudget\CalculateBudgetFactory;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Collection;

class HistoricalAvailableBudgetCalculationService
{
    /** @var CalculateBudgetService $service */
    protected CalculateBudgetService $service;

    /** @var LeadCampaignRepository $campaignRepository */
    protected LeadCampaignRepository $campaignRepository;

    /** @var HistoricalAvailableLocationBudgetRepository $availableBudgetRepository */
    protected HistoricalAvailableLocationBudgetRepository $availableBudgetRepository;

    public function __construct(
        CalculateBudgetService    $service,
        LeadCampaignRepository    $campaignRepository,
        HistoricalAvailableLocationBudgetRepository $availableBudgetRepository
    )
    {
        $this->service                   = $service;
        $this->campaignRepository        = $campaignRepository;
        $this->availableBudgetRepository = $availableBudgetRepository;
    }

    public function calculateAndStoreHistoricalAvailableBudget(): void
    {
        ini_set('memory_limit', '-1');
        try {
            // Performance.
            DB::disableQueryLog();

            $time = Carbon::now();

            $availableCampaigns = $this->campaignRepository->getAllAvailableCampaigns();
            $verifiedBudgetData = $this->calculateHistoricalVerifiedBudgetsForCampaigns($availableCampaigns);

//        $service->calculateUnverifiedBudgetsForCampaigns($availableCampaigns); //Todo

            DB::transaction(function () use ($verifiedBudgetData, $time) {
                DB::beginTransaction();
                $this->availableBudgetRepository->createLocationData($verifiedBudgetData, $time);
                DB::commit();

                DB::beginTransaction();
                $this->availableBudgetRepository->createCampaignData($verifiedBudgetData, $time);
                DB::commit();
            });

        } catch (\PDOException $e) {
        } // PDO transaction can close, but still goes through.
    }

    /**
     * @param Collection $campaigns
     * @return array
     * @throws BindingResolutionException
     */
    public function calculateHistoricalUnverifiedBudgetsForCampaigns(Collection $campaigns): array
    {
        return $this->calculateBudgetsForCampaigns($campaigns, AvailableBudget::BUDGET_TYPE_UNVERIFIED);
    }

    /**
     * @param Collection $campaigns
     * @return array
     * @throws BindingResolutionException
     */
    public function calculateHistoricalVerifiedBudgetsForCampaigns(Collection $campaigns): array
    {
        return $this->calculateBudgetsForCampaigns($campaigns, AvailableBudget::BUDGET_TYPE_VERIFIED);
    }

    /**
     * @param Collection $campaigns
     * @param string $budgetType
     * @return array
     * @throws BindingResolutionException
     */
    public function calculateBudgetsForCampaigns(Collection $campaigns, string $budgetType = AvailableBudget::BUDGET_TYPE_VERIFIED): array
    {
        $service = CalculateBudgetFactory::make($budgetType);
        return $service->calculateHistoricalBudgetAvailable($campaigns);
    }
}
