<?php

namespace App\Services;

class QueueHelperService
{
    const string QUEUE_CONNECTION = 'redis';

    const string QUEUE_NAME_CRITICAL              = 'critical';
    const string QUEUE_NAME_DEFAULT               = 'default';
    const string QUEUE_NAME_LEAD_ALLOCATION_QUEUE = 'lead_allocation_queue';
    const string QUEUE_NAME_WORKFLOWS             = 'workflows';
    const string QUEUE_NAME_REVIEWS               = 'reviews';
    const string QUEUE_NAME_LEGACY_MIGRATION      = 'legacy-migration';
    const string QUEUE_NAME_LONG_RUNNING          = 'long_running';
    const string QUEUE_NAME_PRIORITY_LONG_RUNNING = 'priority_long_running';
    const string QUEUE_NAME_LONG_RUNNING_SINGLE   = 'long_running_single_attempt';
    const string QUEUE_NAME_PRIVACY               = 'privacy';
    const string QUEUE_NAME_MARKETING             = 'marketing';
    const string QUEUE_NAME_AFFILIATES            = 'affiliates';
    const string QUEUE_NAME_TEST_LEADS            = 'test_leads';
    const string QUEUE_NAME_QA_AUTOMATION         = 'qa_automation';
    const string QUEUE_NAME_DATA_ENRICHMENT       = 'data_enrichment';

}
