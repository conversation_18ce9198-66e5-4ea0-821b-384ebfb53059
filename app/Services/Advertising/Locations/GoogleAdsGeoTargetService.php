<?php

namespace App\Services\Advertising\Locations;

use App\Contracts\Services\Advertising\AdvertisingLocationsServiceContract;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\GoogleAdsGeoTarget;
use App\Models\Legacy\Location;
use App\Services\Advertising\Authentication\AdvertisingAuthServiceFactory;
use Carbon\Carbon;
use Exception;
use Google\Ads\GoogleAds\Lib\V19\GoogleAdsServerStreamDecorator;
use Google\Ads\GoogleAds\Util\V19\ResourceNames;
use Google\Ads\GoogleAds\V19\Services\Client\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V19\Services\GoogleAdsRow;
use Google\Ads\GoogleAds\V19\Services\SearchGoogleAdsStreamRequest;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\ArrayShape;

class GoogleAdsGeoTargetService implements AdvertisingLocationsServiceContract
{
    const COLUMN_CRITERIA_ID = 0;
    const COLUMN_NAME = 1;
    const COLUMN_CANONICAL_NAME = 2;
    const COLUMN_PARENT_ID = 3;
    const COLUMN_COUNTRY_CODE = 4;
    const COLUMN_TARGET_TYPE = 5;
    const COLUMN_STATUS = 6;

    const EXCEPTIONS_FILE_PATH = 'database/seeders/data/google-geo-targets-exceptions.csv';
    const EXCEPTIONS_COUNTY_KEY = 0;
    const EXCEPTIONS_STATE_ABBR = 1;
    const EXCEPTIONS_CANONICAL_NAME = 2;
    const EXCEPTIONS_TARGET_TYPE = 3;

    private string $csvDate;

    private array $countiesLocations = [];

    private array $stateLocations = [];

    private string $stateRegex = '/state|of|island|district|new|\s+of\s+|\s+/i';

    private string $saintRegex = '/^st\.|^st\s/i';

    private string $sainteRegex = '/^ste\.|^ste\s/i';

    private string $countyRegex = "/\s+the\s+|borough|census|\s*area\s*|\s+and\s+|parish|county|city|municipality|\s+of\s+|'|-|\s+/i";

    private array $insertRows = [];

    private array $targetExceptions = [];

    private int $matched = 0;

    /** @inheritDoc */
    public function getPlatformIdsByLocation(?array $locationIds = null): Collection
    {
        if(!empty($locationIds)) {
            $platformIdsByLocation = collect();

            foreach(array_chunk($locationIds, 5000) as $locationIdsChunk) {
                $platformIdsChunk = GoogleAdsGeoTarget::query()
                    ->whereIn(GoogleAdsGeoTarget::FIELD_LOCATION_ID, $locationIdsChunk)
                    ->select([GoogleAdsGeoTarget::FIELD_CRITERIA_ID, GoogleAdsGeoTarget::FIELD_LOCATION_ID])
                    ->get();

                $platformIdsByLocation = $platformIdsByLocation->merge($platformIdsChunk);
            }

            return $platformIdsByLocation->pluck(GoogleAdsGeoTarget::FIELD_CRITERIA_ID, GoogleAdsGeoTarget::FIELD_LOCATION_ID);
        }
        else {
            return GoogleAdsGeoTarget::query()->pluck(GoogleAdsGeoTarget::FIELD_CRITERIA_ID, GoogleAdsGeoTarget::FIELD_LOCATION_ID);
        }
    }

    /** @inheritDoc */
    #[ArrayShape([self::FUNC_RETURN_CAMPAIGN_LOCATIONS => "array", self::FUNC_RETURN_PLATFORM_LOCATION_IDS => "array"])]
    public function fetchCampaignLocationsFromPlatformAPI($accountId, array $campaignIds): array
    {
        $campaignCriterionCampaignIds = [];
        foreach($campaignIds as $campaignId) {
            $campaignCriterionCampaignIds[ResourceNames::forCampaign($accountId, $campaignId)] = $campaignId;
        }

        $campaignCriterionCampaignIdsQuery = implode("','", array_keys($campaignCriterionCampaignIds));

        $locationCriterionQuery = sprintf(
            "SELECT %s FROM %s WHERE %s AND %s",
            "campaign.id, campaign_criterion.campaign, campaign_criterion.location.geo_target_constant, campaign_criterion.resource_name, campaign_criterion.negative",
            "campaign_criterion",
            "campaign_criterion.campaign IN ('{$campaignCriterionCampaignIdsQuery}')",
            "campaign_criterion.type = 'LOCATION'"
        );

        /** @var GoogleAdsServiceClient $googleAdsServiceClient */
        $googleAdsServiceClient = AdvertisingAuthServiceFactory::make(AdvertisingPlatform::GOOGLE->value)->getClient()->getGoogleAdsServiceClient();

        /** @var GoogleAdsServerStreamDecorator $locationStream */
        $locationStream = $googleAdsServiceClient->searchStream(SearchGoogleAdsStreamRequest::build($accountId, $locationCriterionQuery));

        $geoTargetCriteriaIds = collect();
        $campaignLocations = collect();

        // Iterates over all rows in all messages
        /** @var GoogleAdsRow $googleAdsRow */
        foreach ($locationStream->iterateAllElements() as $googleAdsRow) {
            $campaignCriterion = $googleAdsRow->getCampaignCriterion();

            $campaignId = $campaignCriterionCampaignIds[$campaignCriterion->getCampaign()];

            $geoTargetCriteriaId = (int) explode('/', $campaignCriterion->getLocation()->getGeoTargetConstant())[1];

            if(empty($geoTargetCriteriaIds[$campaignId])) {
                $geoTargetCriteriaIds->put($campaignId, collect());
            }

            $geoTargetCriteriaIds->get($campaignId)->push($geoTargetCriteriaId);

            if(empty($campaignLocations[$campaignId])) {
                $campaignLocations->put($campaignId, collect());
            }

            $campaignLocations->get($campaignId)->put($geoTargetCriteriaId, !$campaignCriterion->getNegative());
        }

        return [
            self::FUNC_RETURN_CAMPAIGN_LOCATIONS => $campaignLocations,
            self::FUNC_RETURN_PLATFORM_LOCATION_IDS => $geoTargetCriteriaIds
        ];
    }

    /**
     * @inheritDoc
     */
    public function storePlatformLocations(bool $force = false): bool
    {
        $success = false;

        try {
            $startTime = microtime(true);

            echo "\nUpdating Google Ads GeoTargets\n";

            $previousVersionDate = GoogleAdsGeoTarget::query()->first()?->{GoogleAdsGeoTarget::FIELD_VERSION_DATE};

            $fp = $this->getGeoTargetsCSVFile($previousVersionDate);

            if(!$fp) {
                throw new FileNotFoundException("Could not find GeoTargets file");
            }

            echo "GeoTargets file found. Date: {$this->csvDate}\n";

            $process = true;
            if(!empty($previousVersionDate)) {
                $csvDateParts = explode('-', $this->csvDate);
                $csvDateCarbon = Carbon::createFromDate($csvDateParts[0], $csvDateParts[1], $csvDateParts[2]);

                $previousVersionDateParts = explode('-', $previousVersionDate);
                $previousVersionDateCarbon = Carbon::createFromDate($previousVersionDateParts[0], $previousVersionDateParts[1], $previousVersionDateParts[2]);

                $process = $csvDateCarbon > $previousVersionDateCarbon;
            }

            if($process || $force) {
                $this->targetExceptions = $this->getTargetExceptions();

                $success = $this->cacheGeoTargets($this->transformCSVToArray($fp));

                echo $success ? "\nGeoTargets cached\n" : "\nFailed to cache GeoTargets\n";

                echo "Elapsed: ".floor(microtime(true) - $startTime)." seconds\n";

                logger()->info("Cached Google Ads GeoTargets for file version {$this->csvDate} on ".date('Y-m-d'));
            }
            else {
                echo "File was already imported. Exiting\n";
            }
        }
        catch(Exception $e) {
            logger()->error($e->getFile().": Line ".$e->getLine().". ".$e->getMessage());

            throw $e;
        }

        return $success;
    }

    /**
     * @param array $geoTargets
     * @return bool
     */
    private function cacheGeoTargets(array $geoTargets): bool
    {
        echo "Caching GeoTargets\n";

        $this->countiesLocations = Location::query()
                                        ->select(Location::ID, Location::STATE, Location::COUNTY)
                                        ->distinct()
                                        ->where(Location::TYPE, Location::TYPE_COUNTY)
                                        ->get()
                                        ->keyBy(Location::ID)
                                        ->toArray();

        foreach($this->countiesLocations as &$countyLocation) {
            $countyName = $this->removeCharactersWithRegex($this->saintRegex, $countyLocation[Location::COUNTY], "Saint");
            $countyName = $this->removeCharactersWithRegex($this->sainteRegex, $countyName, "Sainte");
            $countyName = $this->removeCharactersWithRegex($this->countyRegex, $countyName);

            $countyLocation = [
                Location::STATE => $this->removeCharactersWithRegex($this->stateRegex, $countyLocation[Location::STATE]),
                Location::COUNTY => $countyName
            ];
        }

        $this->stateLocations = Location::query()
                                    ->where(Location::TYPE, Location::TYPE_STATE)
                                    ->pluck(Location::STATE, Location::ID)
                                    ->toArray();

        foreach($this->stateLocations as &$stateLocation) {
            $stateLocation = $this->removeCharactersWithRegex($this->stateRegex, $stateLocation);
        }

        DB::transaction(function() use ($geoTargets) {
            GoogleAdsGeoTarget::query()->delete();

            $geoTargetsCount = count($geoTargets[GoogleAdsGeoTarget::TARGET_TYPE_STATE])
                + count($geoTargets[GoogleAdsGeoTarget::TARGET_TYPE_COUNTY])
                + count($geoTargets[GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE])
                + count($geoTargets[GoogleAdsGeoTarget::TARGET_TYPE_COUNTRY])
                + count($geoTargets[GoogleAdsGeoTarget::TARGET_TYPE_MUNICIPALITY])
                + count($geoTargets[GoogleAdsGeoTarget::TARGET_EXCEPTION]);

            $processed = 0;
            foreach($geoTargets as $targetType => $geoTargetsList) {
                foreach($geoTargetsList as $geoTarget) {
                    $this->insertRows[(int) $geoTarget[self::COLUMN_CRITERIA_ID]] = [
                        GoogleAdsGeoTarget::FIELD_LOCATION_ID => $geoTarget[GoogleAdsGeoTarget::FIELD_LOCATION_ID] ?? 0,
                        GoogleAdsGeoTarget::FIELD_CRITERIA_ID => (int) $geoTarget[self::COLUMN_CRITERIA_ID],
                        GoogleAdsGeoTarget::FIELD_PARENT_ID => (int) $geoTarget[self::COLUMN_PARENT_ID],
                        GoogleAdsGeoTarget::FIELD_NAME => $geoTarget[self::COLUMN_NAME],
                        GoogleAdsGeoTarget::FIELD_CANONICAL_NAME => $geoTarget[self::COLUMN_CANONICAL_NAME],
                        GoogleAdsGeoTarget::FIELD_TARGET_TYPE => $targetType,
                        GoogleAdsGeoTarget::FIELD_VERSION_DATE => $this->csvDate
                    ];

                    if(count($this->insertRows) >= 500) {
                        $this->saveRows($this->determineLocationIds($targetType));
                    }

                    $processed++;

                    echo "Processed: {$processed}/{$geoTargetsCount} Matched: {$this->matched}\r";
                }

                //Insert any leftover rows
                if(count($this->insertRows) > 0) {
                    $this->saveRows($this->determineLocationIds($targetType));

                    echo "Processed: {$processed}/{$geoTargetsCount} Matched: {$this->matched}\r";
                }
            }
        });

        return true;
    }

    /**
     * @return false|mixed|resource
     */
    private function getGeoTargetsCSVFile(?string $previousVersionDate)
    {
        echo "Retrieving GeoTargets CSV file\n";

        $currentDay = Carbon::today('UTC');
        $previousFileDate = Carbon::today('UTC');

        if($previousVersionDate) {
            $previousVersionDateParts = explode('-', $previousVersionDate);
            $previousFileDate->setDate($previousVersionDateParts[0], $previousVersionDateParts[1], $previousVersionDateParts[2]);
        }
        else {
            $previousFileDate->subDays(60);
        }

        $fp = false;

        /**
         * This is honestly not the best way to check for the file, but Google does not have a conveniently named ‘latest.csv’ file,
         * so we need to know the specific date the file was uploaded.
         * In addition, the file upload schedule is irregular, so we can only guess at the last time a file was uploaded.
         * As to why we didn’t use API methods to find the geotargets, Google does not have batch searching,
         * which meant we needed to retrieve one geotarget at a time which would cause us to hit our API call quota.
         */
        while($currentDay >= $previousFileDate) {
            $fp = @fopen(config('services.google.ads.geotargets_csv_url')."/geotargets-{$currentDay->format('Y-m-d')}.csv", "r");

            if($fp !== false) {
                $this->csvDate = $currentDay->format('Y-m-d');

                break;
            }

            $currentDay->subDay();
        }

        return $fp;
    }

    /**
     * @param string $targetType
     * @return array
     * @throws Exception
     */
    private function determineLocationIds(string $targetType): array
    {
        $criteriaLocations = [];

        if($targetType === GoogleAdsGeoTarget::TARGET_TYPE_COUNTY || $targetType === GoogleAdsGeoTarget::TARGET_TYPE_MUNICIPALITY) {
            foreach($this->insertRows as $criteriaId => $geoTarget) {
                $criteriaLocations[$criteriaId] = 0;

                $geoTargetState = $this->removeCharactersWithRegex($this->stateRegex, explode(',', $geoTarget[GoogleAdsGeoTarget::FIELD_CANONICAL_NAME])[1]);

                $geoTargetCounty = $this->removeCharactersWithRegex($this->saintRegex, $geoTarget[GoogleAdsGeoTarget::FIELD_NAME], "Saint");
                $geoTargetCounty = $this->removeCharactersWithRegex($this->sainteRegex, $geoTargetCounty, "Sainte");
                $geoTargetCounty = $this->removeCharactersWithRegex($this->countyRegex, $geoTargetCounty);

                foreach($this->countiesLocations as $locationId => $county) {
                    if(preg_match("/^{$geoTargetState}$/i", $county[Location::STATE]) === 1
                    && preg_match("/^{$geoTargetCounty}$/i", $county[Location::COUNTY]) === 1) {
                        $criteriaLocations[$criteriaId] = $locationId;

                        break;
                    }
                }
            }
        }
        else if($targetType === GoogleAdsGeoTarget::TARGET_TYPE_STATE) {
            foreach($this->insertRows as $criteriaId => $geoTarget) {
                $criteriaLocations[$criteriaId] = 0;

                $geoTargetState = $this->removeCharactersWithRegex($this->stateRegex, $geoTarget[GoogleAdsGeoTarget::FIELD_NAME]);

                foreach($this->stateLocations as $locationId => $state) {
                    if(preg_match("/^{$geoTargetState}$/i", $state) === 1) {
                        $criteriaLocations[$criteriaId] = $locationId;

                        break;
                    }
                }
            }
        }
        else if($targetType === GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE) {
            $locationZipCodes = Location::query()
                                    ->whereIn(Location::ZIP_CODE, array_column($this->insertRows, GoogleAdsGeoTarget::FIELD_NAME))
                                    ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
                                    ->pluck(Location::ID, Location::ZIP_CODE)
                                    ->toArray();

            foreach($this->insertRows as $criteriaId => $geoTarget) {
                $criteriaLocations[$criteriaId] = $locationZipCodes[$geoTarget[GoogleAdsGeoTarget::FIELD_NAME]] ?? 0;
            }
        }
        else if($targetType === GoogleAdsGeoTarget::TARGET_TYPE_COUNTRY) {
            foreach($this->insertRows as $criteriaId => $geoTarget) {
                $criteriaLocations[$criteriaId] = 0; //We don't store countries in locations so this is always zero
            }
        }
        else if ($targetType === GoogleAdsGeoTarget::TARGET_EXCEPTION) {
            foreach($this->insertRows as $criteriaId => $geoTarget) {
                $criteriaLocations[$criteriaId] = $geoTarget[GoogleAdsGeoTarget::FIELD_LOCATION_ID];
            }
        }
        else {
            throw new Exception(__METHOD__.": Invalid target type");
        }

        return $criteriaLocations;
    }

    /**
     * @param string $regexPattern
     * @param string $subject
     * @param string $replacement
     * @return string
     */
    private function removeCharactersWithRegex(string $regexPattern, string $subject, string $replacement = ''): string
    {
        return preg_replace($regexPattern, $replacement, $subject);
    }

    /**
     * @param array $locationIds
     * @return bool
     */
    private function saveRows(array $locationIds): bool
    {
        foreach($this->insertRows as &$insertRow) {
            $locationId = $locationIds[$insertRow[GoogleAdsGeoTarget::FIELD_CRITERIA_ID]];

            $insertRow[GoogleAdsGeoTarget::FIELD_LOCATION_ID] = $locationId;

            $this->matched += (int) !empty($locationId);
        }

        GoogleAdsGeoTarget::query()->insert($this->insertRows);

        $this->insertRows = [];

        return true;
    }

    /**
     * @param $fp
     * @return array
     */
    private function transformCSVToArray(&$fp): array
    {
        echo "Transforming CSV into array\n";

        $exceptionLocations = Location::query()
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->get([
                Location::ID,
                Location::STATE_ABBREVIATION,
                Location::COUNTY_KEY,
            ])->groupBy([Location::STATE_ABBREVIATION, Location::COUNTY_KEY]);

        $geoTargetsArray = [
            GoogleAdsGeoTarget::TARGET_TYPE_STATE => [],
            GoogleAdsGeoTarget::TARGET_TYPE_COUNTY => [],
            GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE => [],
            GoogleAdsGeoTarget::TARGET_TYPE_COUNTRY => [],
            GoogleAdsGeoTarget::TARGET_TYPE_MUNICIPALITY => [],
            GoogleAdsGeoTarget::TARGET_EXCEPTION => [],
        ];

        //Remove headers row
        fgetcsv($fp);

        try {
            $subCountryTargetTypes = [
                GoogleAdsGeoTarget::TARGET_TYPE_COUNTY,
                GoogleAdsGeoTarget::TARGET_TYPE_STATE,
                GoogleAdsGeoTarget::TARGET_TYPE_POSTAL_CODE,
            ];

            while(($geoTarget = fgetcsv($fp)) !== false) {
                $targetType = strtolower($geoTarget[self::COLUMN_TARGET_TYPE]);

                if(strtolower($geoTarget[self::COLUMN_STATUS]) === 'active'
                && (
                    (($geoTarget[self::COLUMN_COUNTRY_CODE] === 'US' && in_array($targetType, $subCountryTargetTypes)) ||
                     ($geoTarget[self::COLUMN_COUNTRY_CODE] === 'PR' && $targetType === GoogleAdsGeoTarget::TARGET_TYPE_MUNICIPALITY)) ||
                    $targetType === GoogleAdsGeoTarget::TARGET_TYPE_COUNTRY
                )
                ) {
                    $geoTargetsArray[strtolower($geoTarget[self::COLUMN_TARGET_TYPE])][] = $geoTarget;
                }

                if (is_array($geoTarget) && in_array($geoTarget[self::COLUMN_CANONICAL_NAME], array_keys($this->targetExceptions))) {
                    $exceptionEntry = $this->targetExceptions[$geoTarget[self::COLUMN_CANONICAL_NAME]];

                    // If entry is an exception, add it to the exceptions array with location county key and state abbr
                    $countyKey = $exceptionEntry[Location::COUNTY_KEY] ?? '';
                    $stateAbbr = $exceptionEntry[Location::STATE_ABBREVIATION] ?? '';

                    $geoTarget[GoogleAdsGeoTarget::FIELD_LOCATION_ID] = $exceptionLocations[$stateAbbr][$countyKey][0]->{Location::ID} ?? 0;
                    $geoTargetsArray[GoogleAdsGeoTarget::TARGET_EXCEPTION][] = $geoTarget;
                }
            }
        }
        catch(Exception $e) {
            if(!feof($fp)) {
                throw $e;
            }
        }
        finally {
            fclose($fp);
        }

        return $geoTargetsArray;
    }

    /**
     * Returns Google ads geo target exceptions defined in google-geo-targets-exceptions.csv
     * CSV Format: county_key,state_abbr,google_name,google_type
     * Output: [<canonical name> => ['county_key' => <county key>, 'state_abbr' => <state abbr>]]
     *
     * @return array
     */
    public function getTargetExceptions(): array
    {
        $filePath = base_path(self::EXCEPTIONS_FILE_PATH);
        $handle = fopen($filePath, "r");

        fgetcsv($handle); // skip header

        $targetExceptions = [];

        while ($csvLine = fgetcsv($handle)) {
            $targetExceptions[$csvLine[self::EXCEPTIONS_CANONICAL_NAME]] = [
                Location::COUNTY_KEY => $csvLine[self::EXCEPTIONS_COUNTY_KEY],
                Location::STATE_ABBREVIATION => $csvLine[self::EXCEPTIONS_STATE_ABBR],
                GoogleAdsGeoTarget::FIELD_TARGET_TYPE => $csvLine[self::EXCEPTIONS_TARGET_TYPE],
            ];
        }

        return $targetExceptions;
    }
}
