<?php

namespace App\Services\Advertising\Locations;

use App\Contracts\Services\Advertising\AdvertisingLocationsServiceContract;
use App\Enums\Advertising\AdvertisingPlatform;
use Exception;

class AdvertisingLocationsServiceFactory
{
    const PLATFORM_SERVICE = [
        [
            'platform' => AdvertisingPlatform::GOOGLE,
            'service' => GoogleAdsGeoTargetService::class
        ],
        [
            'platform' => AdvertisingPlatform::META,
            'service' => MetaAdsLocationsService::class
        ]
    ];

    /**
     * @param string $driver
     * @return AdvertisingLocationsServiceContract
     * @throws Exception
     */
    public static function make(string $driver): AdvertisingLocationsServiceContract
    {
        return match($driver) {
            AdvertisingPlatform::GOOGLE->value => app(GoogleAdsGeoTargetService::class),
            AdvertisingPlatform::META->value => app(MetaAdsLocationsService::class),
            default => throw new Exception(__METHOD__.": Invalid driver")
        };
    }
}
