<?php

namespace App\Services\Advertising\Locations;

use App\Contracts\Services\Advertising\AdvertisingLocationsServiceContract;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\Advertiser;
use App\Models\AdvertisingAccount;
use App\Services\Advertising\AdvertisingAccountService;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Authentication\MetaAdsAuthService;
use App\Models\Legacy\Location;
use App\Models\MetaAdsLocation;
use App\Services\Advertising\Campaigns\MetaAdsService;
use Exception;
use FacebookAds\Api;
use FacebookAds\Cursor;
use FacebookAds\Http\Exception\RequestException;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\AbstractObject;
use FacebookAds\Object\AdSet;
use FacebookAds\Object\Fields\AdSetFields;
use FacebookAds\Object\Fields\TargetingFields;
use FacebookAds\Object\Fields\TargetingGeoLocationFields;
use FacebookAds\Object\TargetingSearch;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\ArrayShape;
use Throwable;

class MetaAdsLocationsService implements AdvertisingLocationsServiceContract
{
    const LOCATION_TYPES = [
        MetaAdsLocation::TYPE_COUNTRY,
        MetaAdsLocation::TYPE_REGION,
        //Make sure medium geo area is listed after region so that the region ids are available by the time it searches for medium geo area
        MetaAdsLocation::TYPE_MEDIUM_GEO_AREA,
        MetaAdsLocation::TYPE_ZIP
    ];

    const SINGULAR_PLURAL_LOCATION_TYPES = [
        MetaAdsLocation::TYPE_CITY => TargetingGeoLocationFields::CITIES,
        MetaAdsLocation::TYPE_REGION => TargetingGeoLocationFields::REGIONS,
        MetaAdsLocation::TYPE_MEDIUM_GEO_AREA => TargetingGeoLocationFields::MEDIUM_GEO_AREAS,
        MetaAdsLocation::TYPE_ZIP => TargetingGeoLocationFields::ZIPS
    ];

    private Api $metaAdsClient;

    private array $insertRows = [];

    private int $inserted = 0;

    private int $matched = 0;

    private array $countiesLocations = [];

    private array $stateLocations = [];

    private string $stateRegex = '/state|of|island|district|new|\s+of\s+|\s+/i';

    private string $countyRegex = "/\s+the\s+|borough|census|\s*area\s*|parish|county|\s+of\s+|st\.|ste\.|'|-|–|—|\s+/i";

    private array $nonEnglishLettersRegex = [
        ["/á/i", "a"],
        ["/é/i", "e"],
        ["/í/i", "i"],
        ["/ó/i", "o"],
        ["/ú/i", "u"],
        ["/ü/i", "u"],
        ["/ñ/i", "n"],
        ["/ö/i", "o"],
        ["/ä/i", "a"],
        ["/ü/i", "u"]
    ];

    private array $regions = [];

    private array $insertedCounties = [];

    private array $insertedZips = [];

    /**
     * @param MetaAdsAuthService $metaAdsAuthService
     * @param AdvertisingAccountService $advertisingAccountService
     */
    public function __construct(
        private readonly MetaAdsAuthService $metaAdsAuthService,
        private readonly AdvertisingAccountService $advertisingAccountService
    )
    {

    }

    /**
     * @param AdvertiserEnum $advertiser
     * @return Api
     * @throws Throwable
     */
    private function getClient(AdvertiserEnum $advertiser): Api
    {
        $this->metaAdsClient = $this->metaAdsAuthService->getClient($advertiser);

        if(!App::environment('production')) {
            $this->metaAdsClient->setLogger(new CurlLogger());
        }

        return $this->metaAdsClient;
    }

    /**
     * @inheritDoc
     */
    public function getPlatformIdsByLocation(?array $locationIds = null): Collection
    {
        if(!empty($locationIds)) {
            $platformIdsByLocation = collect();

            foreach(array_chunk($locationIds, 5000) as $locationIdsChunk) {
                $platformIdsChunk = MetaAdsLocation::query()
                                        ->whereIn(MetaAdsLocation::FIELD_LOCATION_ID, $locationIdsChunk)
                                        ->get();

                $platformIdsByLocation = $platformIdsByLocation->merge($platformIdsChunk);
            }

            return $platformIdsByLocation->keyBy(MetaAdsLocation::FIELD_LOCATION_ID);
        }
        else {
            return MetaAdsLocation::query()->get()->keyBy(MetaAdsLocation::FIELD_LOCATION_ID);
        }
    }

    /**
     * @inheritDoc
     *
     * A SR campaign is the equivalent of a Meta Ad Set
     */
    #[ArrayShape([self::FUNC_RETURN_CAMPAIGN_LOCATIONS => "array", self::FUNC_RETURN_PLATFORM_LOCATION_IDS => "array"])]
    public function fetchCampaignLocationsFromPlatformAPI($accountId, array $campaignIds): array
    {
        try {
            $advertiserKey = $this->advertisingAccountService
                ->getAccountByIdAndPlatform($accountId, AdvertisingPlatform::META, [AdvertisingAccount::RELATION_ADVERTISER])
                ->{AdvertisingAccount::RELATION_ADVERTISER}
                ->{Advertiser::FIELD_KEY};

            $this->getClient(AdvertiserEnum::fromKey($advertiserKey));

            $adSetsKeyed = collect();
            foreach(array_chunk($campaignIds, 50) as $campaignIdsChunk) {
                $adSets = AdSet::readIds($campaignIdsChunk, [AdSetFields::ID, AdSetFields::TARGETING]);

                foreach($adSets as $adSet) {
                    $adSetsKeyed->put($adSet->{AdSetFields::ID}, $adSet->getData());
                }
            }

            return $this->extractAdSetLocations($adSetsKeyed);
        }
        catch(Throwable $e) {
            if($e instanceof RequestException) {
                $errMsg = MetaAdsService::getRequestExceptionMessage($e->getResponse());
            }
            else {
                $errMsg = $e->getMessage();
            }

            AdvertisingLoggingService::writeLog(
                $errMsg,
                [
                    "platform" => AdvertisingPlatform::META->value
                ]
            );

            throw $e;
        }
    }

    /**
     * @inheritDoc
     */
    public function storePlatformLocations(bool $force = false): bool
    {
        $start = microtime(true);

        $this->printMessage("Caching Meta Ads Locations");

        $this->countiesLocations = Location::query()
                                        ->select(Location::ID, Location::STATE, Location::COUNTY)
                                        ->distinct()
                                        ->where(Location::TYPE, Location::TYPE_COUNTY)
                                        ->get()
                                        ->keyBy(Location::ID)
                                        ->toArray();

        foreach($this->countiesLocations as &$countyLocation) {
            $countyLocation = [
                Location::STATE => $this->removeCharactersWithRegex($this->stateRegex, $countyLocation[Location::STATE]),
                Location::COUNTY => $this->replaceNonEnglishCharacters($this->removeCharactersWithRegex($this->countyRegex, $countyLocation[Location::COUNTY]))
            ];
        }

        $this->stateLocations = Location::query()
                                    ->where(Location::TYPE, Location::TYPE_STATE)
                                    ->pluck(Location::STATE, Location::ID)
                                    ->toArray();

        foreach($this->stateLocations as &$stateLocation) {
            $stateLocation = $this->removeCharactersWithRegex($this->stateRegex, $stateLocation);
        }

        try {
            DB::transaction(function() {
                $this->getClient(AdvertiserEnum::GABE);

                MetaAdsLocation::query()->delete();

                foreach(self::LOCATION_TYPES as $locationType) {
                    $this->inserted = 0;
                    $this->matched = 0;

                    $this->printMessage("Caching $locationType entries");

                    if($locationType === MetaAdsLocation::TYPE_ZIP) {
                        $queries = ["'0'", "'1'", "'2'", "'3'", "'4'", "'5'", "'6'", "'7'", "'8'", "'9'"];

                        foreach($this->regions as $regionId => $region) {
                            $this->insertedZips = [];

                            foreach($queries as $query) {
                                $searchCursor = $this->searchLocations($locationType, $query, ['country_code' => 'US', 'region_id' => $regionId]);

                                $this->populateInsertRows($searchCursor, $locationType);
                            }
                        }
                    }
                    else if($locationType === MetaAdsLocation::TYPE_MEDIUM_GEO_AREA) {
                        foreach($this->regions as $regionId => $region) {
                            $searchCursor = $this->searchLocations($locationType, $region, ['country_code' => 'US', 'region_id' => $regionId]);

                            $this->populateInsertRows($searchCursor, $locationType);
                        }
                    }
                    else if($locationType === MetaAdsLocation::TYPE_REGION) {
                        $searchCursor = $this->searchLocations($locationType, '', ['country_code' => 'US']);

                        $this->populateInsertRows($searchCursor, $locationType);
                    }
                    else {
                        $searchCursor = $this->searchLocations($locationType);

                        $this->populateInsertRows($searchCursor, $locationType);
                    }
                }
            });

            $this->printMessage("Meta Ads locations cached");

            $elapsed = ceil(microtime(true) - $start);

            $this->printMessage("Elapsed: $elapsed secs");

            return true;
        }
        catch(Throwable $e) {
            if($e instanceof RequestException) {
                $errMsg = MetaAdsService::getRequestExceptionMessage($e->getResponse());
            }
            else {
                $errMsg = $e->getMessage();
            }

            AdvertisingLoggingService::writeLog(
                $errMsg,
                [
                    "platform" => AdvertisingPlatform::META->value
                ]
            );

            throw $e;
        }
    }

    /**
     * @param Cursor $searchCursor
     * @param string $locationType
     * @param int $startIdx
     * @throws Exception
     */
    private function populateInsertRows(Cursor $searchCursor, string $locationType, int $startIdx = 0): void
    {
        if($searchCursor->getLastResponse()->getStatusCode() !== 200) {
            throw new Exception(MetaAdsService::getRequestExceptionMessage($searchCursor->getLastResponse()));
        }

        $currentCount = $searchCursor->count();
        $isRegion = $locationType === MetaAdsLocation::TYPE_REGION;
        $isCounty = $locationType === MetaAdsLocation::TYPE_MEDIUM_GEO_AREA;
        $isZip = $locationType === MetaAdsLocation::TYPE_ZIP;
        $isCountry = $locationType === MetaAdsLocation::TYPE_COUNTRY;

        for($i = $startIdx; $i < $currentCount; $i++) {
            $location = $searchCursor[$i];

            if(!$isRegion
            || ($isRegion && $location->supports_region)) {
                $name = '';

                if($isCounty && !in_array($location->key, $this->insertedCounties)) {
                    $this->insertedCounties[] = $location->key;

                    $region = !empty($location->region) ? ", {$location->region}" : '';

                    $name = ($location->name).$region;
                }
                else if($isZip && !in_array($location->key, $this->insertedZips)) {
                    $this->insertedZips[] = $location->key;

                    $name = $location->name;

                    if(!empty($location->primary_city)) {
                        $name .= ', '.$location->primary_city;
                    }
                    if(!empty($location->region)) {
                        $name .= ', '.$location->region;
                    }
                }
                else if($isRegion) {
                    $this->regions[$location->key] = $location->name;

                    $name = $location->name;
                }
                else if($isCountry) {
                    $name = $location->name;
                }

                if(!empty($name)) {
                    $this->insertRows[$location->key] = [
                        MetaAdsLocation::FIELD_KEY => $location->key,
                        MetaAdsLocation::FIELD_NAME => $name,
                        MetaAdsLocation::FIELD_TYPE => $location->type,
                        MetaAdsLocation::FIELD_LOCATION_ID => 0
                    ];
                }

                if(count($this->insertRows) >= 500) {
                    $this->saveRows($this->determineLocationIds($locationType));
                }
            }
        }

        if(!empty($this->insertRows)) {
            $this->saveRows($this->determineLocationIds($locationType));
        }

        $searchCursor->fetchAfter();
        if($searchCursor->count() > $currentCount) {
            $this->populateInsertRows($searchCursor, $locationType, $currentCount);
        }
    }

    /**
     * @param array $locationIds
     * @return bool
     */
    private function saveRows(array $locationIds): bool
    {
        $numRows = count($this->insertRows);

        foreach($this->insertRows as &$insertRow) {
            $locationId = (int) $locationIds[$insertRow[MetaAdsLocation::FIELD_KEY]] ?? 0;

            $insertRow[MetaAdsLocation::FIELD_LOCATION_ID] = $locationId;

            $this->matched += (int) !empty($locationId);
        }

        MetaAdsLocation::query()->insert($this->insertRows);

        $this->inserted += $numRows;

        $this->insertRows = [];

        $this->printMessage("Processed: {$this->inserted} Matched: {$this->matched}", true);

        return true;
    }

    /**
     * @param string $locationType
     * @return array
     * @throws Exception
     */
    private function determineLocationIds(string $locationType): array
    {
        if(!in_array($locationType, self::LOCATION_TYPES, true)) {
            throw new Exception("Invalid location type: $locationType");
        }

        $metaKeyLocationIds = [];

        if($locationType === MetaAdsLocation::TYPE_REGION) {
            $washingtonDCLocId = Location::query()
                                    ->where(Location::TYPE, Location::TYPE_STATE)
                                    ->where(Location::STATE_ABBREVIATION, 'DC')
                                    ->first()
                                    ->{Location::ID};

            foreach($this->insertRows as $locationKey => $location) {
                if(preg_match("/washington d\. c\./i", $location[MetaAdsLocation::FIELD_NAME]) === 1) {
                   $metaKeyLocationIds[$locationKey] = $washingtonDCLocId;
                }
                else {
                    $metaKeyLocationIds[$locationKey] = 0;

                    $locationState = $this->removeCharactersWithRegex($this->stateRegex, $location[MetaAdsLocation::FIELD_NAME]);

                    foreach($this->stateLocations as $locationId => $state) {
                        if(preg_match("/^{$locationState}$/i", $state) === 1) {
                            $metaKeyLocationIds[$locationKey] = $locationId;

                            break;
                        }
                    }
                }
            }
        }
        else if($locationType === MetaAdsLocation::TYPE_MEDIUM_GEO_AREA) {
            $matchedCounties = [];

            foreach($this->insertRows as $locationKey => $location) {
                $metaKeyLocationIds[$locationKey] = 0;

                $nameParts = explode(',', $location[MetaAdsLocation::FIELD_NAME]);

                $locationState = $this->removeCharactersWithRegex($this->stateRegex, trim($nameParts[1]));

                $locationCounty = $this->replaceNonEnglishCharacters($this->removeCharactersWithRegex($this->countyRegex, trim($nameParts[0])));

                foreach($this->countiesLocations as $locationId => $county) {
                    if(preg_match("/$locationState/i", $county[Location::STATE]) === 1
                    && preg_match("/$locationCounty/i", $county[Location::COUNTY]) === 1) {
                        $metaKeyLocationIds[$locationKey] = $locationId;
                        $matchedCounties[] = $locationId;

                        break;
                    }
                }
            }

            $this->countiesLocations = array_diff_key($this->countiesLocations, array_flip($matchedCounties));
        }
        else if($locationType === MetaAdsLocation::TYPE_ZIP) {
            $zipNames = array_column($this->insertRows, MetaAdsLocation::FIELD_NAME);

            $zips = [];
            foreach($zipNames as $zipName) {
                $zips[$zipName] = explode(',', $zipName)[0];
            }

            $locationZipCodes = Location::query()
                                    ->whereIn(Location::ZIP_CODE, array_values($zips))
                                    ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
                                    ->pluck(Location::ID, Location::ZIP_CODE)
                                    ->toArray();

            foreach($this->insertRows as $locationKey => $location) {
                $zipcode = $zips[$location[MetaAdsLocation::FIELD_NAME]];

                $metaKeyLocationIds[$locationKey] = $locationZipCodes[$zipcode] ?? 0;
            }
        }
        else if($locationType === MetaAdsLocation::TYPE_COUNTRY) {
            foreach($this->insertRows as $locationKey => $location) {
                $metaKeyLocationIds[$locationKey] = 0; //We don't store countries in locations so this is always zero
            }
        }

        return $metaKeyLocationIds;
    }

    /**
     * @param string $regexPattern
     * @param string $subject
     * @return string
     */
    private function removeCharactersWithRegex(string $regexPattern, string $subject): string
    {
        return preg_replace($regexPattern, '', $subject);
    }

    /**
     * @param string $subject
     * @return string
     */
    private function replaceNonEnglishCharacters(string $subject): string
    {
        return preg_replace(array_column($this->nonEnglishLettersRegex, 0), array_column($this->nonEnglishLettersRegex, 1), $subject);
    }

    /**
     * @param string $locationType
     * @param string $query
     * @param array $params
     * @param int $limit
     * @return Cursor
     */
    private function searchLocations(string $locationType, string $query = '', array $params = [], int $limit = 1000): Cursor
    {
        $searchParams = [
            "limit" => $limit,
            "location_types" => [$locationType],
        ];

        return TargetingSearch::search(
            "adgeolocation",
            '',
            $query,
            array_merge($searchParams, $params),
            $this->metaAdsClient
        );
    }

    /**
     * @param string $message
     * @param bool $carriageReturn
     */
    private function printMessage(string $message, bool $carriageReturn = false): void
    {
        echo $carriageReturn ? "$message\r" : "\n{$message}\n";
    }

    /**
     * @return array
     * @throws Throwable
     */
    public function getMetaDmaKeys(): array
    {
        $this->metaAdsClient = $this->metaAdsAuthService->getAdCostClient(AdvertiserEnum::GABE);
        if(!App::environment('production')) {
            $this->metaAdsClient->setLogger(new CurlLogger());
        }
        $searchCursor = $this->searchLocations('geo_market');

        if($searchCursor->getLastResponse()->getStatusCode() !== 200) {
            throw new Exception(MetaAdsService::getRequestExceptionMessage($searchCursor->getLastResponse()));
        }

        $currentCount = $searchCursor->count();

        $dmaKeys = [];

        for($i = 0; $i < $currentCount; $i++) {
            $location = $searchCursor[$i];

            $dmaCode = str_replace('DMA:','',$location->key);
            $dmaName = $location->name;

            $dmaKeys[$dmaCode] = $dmaName;
        }

        return $dmaKeys;
    }

    /**
     * @param Collection $adSets
     * @return array
     * @throws Exception
     */
    #[ArrayShape([self::FUNC_RETURN_CAMPAIGN_LOCATIONS => "array", self::FUNC_RETURN_PLATFORM_LOCATION_IDS => "array", self::FUNC_RETURN_MISC_TARGETING_DATA => "array"])]
    public function extractAdSetLocations(Collection $adSets): array
    {
        $adSetLocationKeys = array_fill_keys(
            $adSets->keys()->toArray(),
            [
                TargetingGeoLocationFields::REGIONS => [],
                TargetingGeoLocationFields::ZIPS => [],
                TargetingGeoLocationFields::MEDIUM_GEO_AREAS => [],
                TargetingGeoLocationFields::CITIES => []
            ]
        );

        $locationKeys = [
            TargetingGeoLocationFields::REGIONS => [],
            TargetingGeoLocationFields::ZIPS => [],
            TargetingGeoLocationFields::MEDIUM_GEO_AREAS => [],
            TargetingGeoLocationFields::CITIES => []
        ];

        $miscData = [];

        foreach($adSets as $adSetId => $adSet) {
            if($adSet instanceof AbstractObject) {
                $adSet = $adSet->getData();
            }

            if(!is_array($adSet)) {
                throw new Exception("Ad set is not array");
            }

            $geoLocations = collect($adSet[AdSetFields::TARGETING][TargetingFields::GEO_LOCATIONS])
                ->only([TargetingGeoLocationFields::REGIONS, TargetingGeoLocationFields::ZIPS, TargetingGeoLocationFields::MEDIUM_GEO_AREAS, TargetingGeoLocationFields::CITIES]);
            foreach($geoLocations as $locationType => $locations) {
                $metaKeys = array_column($locations, MetaAdsLocation::FIELD_KEY);

                $adSetLocationKeys[$adSetId][$locationType] += array_fill_keys($metaKeys, true);
                $locationKeys[$locationType] = array_merge($locationKeys[$locationType], $metaKeys);
            }

            $excludedGeoLocations = collect($adSet[AdSetFields::TARGETING][TargetingFields::EXCLUDED_GEO_LOCATIONS] ?? [])
                ->only([TargetingGeoLocationFields::REGIONS, TargetingGeoLocationFields::ZIPS, TargetingGeoLocationFields::MEDIUM_GEO_AREAS, TargetingGeoLocationFields::CITIES]);
            foreach($excludedGeoLocations as $locationType => $locations) {
                $metaKeys = array_column($locations, MetaAdsLocation::FIELD_KEY);

                $adSetLocationKeys[$adSetId][$locationType] += array_fill_keys($metaKeys, false);
                $locationKeys[$locationType] = array_merge($locationKeys[$locationType], $metaKeys);
            }

            $miscData[$adSetId] = [];
            foreach(collect($adSet[AdSetFields::TARGETING]) as $param => $values) {
                if(!in_array($param, [TargetingFields::EXCLUDED_GEO_LOCATIONS, TargetingFields::GEO_LOCATIONS], true)) {
                    $miscData[$adSetId][$param] = $values;
                }
            }
        }

        return [
            self::FUNC_RETURN_CAMPAIGN_LOCATIONS => $adSetLocationKeys,
            self::FUNC_RETURN_PLATFORM_LOCATION_IDS => $locationKeys,
            self::FUNC_RETURN_MISC_TARGETING_DATA => $miscData
        ];
    }
}
