<?php

namespace App\Services\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\Advertiser;
use App\Models\AdvertisingAccount;
use App\Models\AdvertisingAccountWebsite;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;

class AdvertisingAccountService
{
    /**
     * @param $accountId
     * @param AdvertisingPlatform $advertisingPlatform
     * @param array $relations
     * @return AdvertisingAccount
     */
    public function getAccountByIdAndPlatform($accountId, AdvertisingPlatform $advertisingPlatform, array $relations = []): AdvertisingAccount
    {
        /** @type AdvertisingAccount */
        return AdvertisingAccount::query()
            ->where(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->where(AdvertisingAccount::FIELD_PLATFORM, $advertisingPlatform->value)
            ->with($relations)
            ->firstOrFail();
    }

    /**
     * @param string $platform
     * @return Collection
     */
    public function getAccounts(string $platform): Collection
    {
        return AdvertisingAccount::query()->where(AdvertisingAccount::FIELD_PLATFORM, $platform)->get();
    }

    /**
     * @param string $platform
     * @param int $page
     * @param int $perPage
     * @return Paginator
     */
    public function getAccountsPaginated(string $platform, int $page = 1, int $perPage = 6): Paginator
    {
        $websitesCol = 'websites';
        $advertiserCol = 'advertiser';

        $accounts = AdvertisingAccount::query()
                        ->selectRaw(sprintf(
                            "%s, %s, %s, %s, %s, %s, %s, %s",
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_PLATFORM,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_NAME,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_INDUSTRY,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_TRACKS_CONVERSIONS,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS,
                            Advertiser::TABLE.'.'.Advertiser::FIELD_KEY." AS {$advertiserCol}",
                            "GROUP_CONCAT(DISTINCT ".AdvertisingAccountWebsite::FIELD_WEBSITE_ID." SEPARATOR ',') AS {$websitesCol}"
                        ))
                        ->join(Advertiser::TABLE, function($join) {
                            $join->on(
                                Advertiser::TABLE.'.'.Advertiser::FIELD_ID,
                                '=',
                                AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_ADVERTISER_ID
                            );
                        })
                        ->leftJoin(AdvertisingAccountWebsite::TABLE, function($join) {
                            $join
                                ->on(
                                    AdvertisingAccountWebsite::TABLE.'.'.AdvertisingAccountWebsite::FIELD_PLATFORM,
                                    '=',
                                    AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_PLATFORM
                                )
                                ->on(
                                    AdvertisingAccountWebsite::TABLE.'.'.AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID,
                                    '=',
                                    AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID
                                );
                        })
                        ->where(AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_PLATFORM, $platform)
                        ->groupBy([
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_PLATFORM,
                            $advertiserCol,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_NAME,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_INDUSTRY,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_TRACKS_CONVERSIONS,
                            AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS
                        ])
                        ->orderBy(AdvertisingAccount::TABLE.'.'.AdvertisingAccount::FIELD_ID)
                        ->get()
                        ->map(function($row) use ($websitesCol) {
                            $row[$websitesCol] = array_filter(
                                array_map(
                                    fn($value): int => (int) $value,
                                    explode(',', $row[$websitesCol])
                                )
                            );

                            return $row;
                        })
                        ->toArray();

        return new LengthAwarePaginator(array_slice($accounts, ($page - 1) * $perPage, $perPage), count($accounts), $perPage);
    }

    /**
     * @param string $platform
     * @param string $accountId
     * @param string $name
     * @param string $industry
     * @param bool $tracksConversion
     * @param int $uploadConversionIntervalHours
     * @param AdvertiserEnum $advertiser
     * @param array $websiteIds
     * @return bool
     */
    public function saveAccount(
        string $platform,
        string $accountId,
        string $name,
        string $industry,
        bool $tracksConversion,
        int $uploadConversionIntervalHours,
        AdvertiserEnum $advertiser,
        array $websiteIds
    ): bool
    {
        DB::transaction(function() use ($platform, $accountId, $name, $industry, $tracksConversion, $websiteIds, $uploadConversionIntervalHours, $advertiser) {
            $advertiserId = Advertiser::query()
                ->where(Advertiser::FIELD_KEY, AdvertiserEnum::getKey($advertiser))
                ->firstOrFail()
                ->{Advertiser::FIELD_ID};

            AdvertisingAccount::query()->updateOrCreate(
                [
                    AdvertisingAccount::FIELD_PLATFORM => $platform,
                    AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID => $accountId
                ],
                [
                    AdvertisingAccount::FIELD_NAME => $name,
                    AdvertisingAccount::FIELD_INDUSTRY => $industry,
                    AdvertisingAccount::FIELD_TRACKS_CONVERSIONS => $tracksConversion,
                    AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS => max($uploadConversionIntervalHours, 0),
                    AdvertisingAccount::FIELD_ADVERTISER_ID => $advertiserId
                ]
            );

            AdvertisingAccountWebsite::query()
                ->where(AdvertisingAccountWebsite::FIELD_PLATFORM, $platform)
                ->where(AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                ->delete();

            $insertRows = [];
            foreach($websiteIds as $websiteId) {
                $insertRows[] = [
                    AdvertisingAccountWebsite::FIELD_PLATFORM => $platform,
                    AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID => $accountId,
                    AdvertisingAccountWebsite::FIELD_WEBSITE_ID => $websiteId
                ];
            }

            AdvertisingAccountWebsite::query()->insert($insertRows);
        });

        return true;
    }

    /**
     * @param string $platform
     * @param string $accountId
     * @return bool
     */
    public function deleteAccount(string $platform, string $accountId): bool
    {
        AdvertisingAccount::query()
            ->where(AdvertisingAccount::FIELD_PLATFORM, $platform)
            ->where(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->delete();

        return true;
    }

    /**
     * @param string $platform
     * @param string $accountId
     * @return bool
     */
    public function deleteAccountWebsites(string $platform, string $accountId): bool
    {
        AdvertisingAccountWebsite::query()
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM, $platform)
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->delete();

        return true;
    }

    /**
     * @param string $platform
     * @param string $accountId
     * @return bool
     */
    public function deleteAllAccountInfo(string $platform, string $accountId): bool
    {
        $advertisingCampaignService = app(AdvertisingCampaignService::class);

        DB::transaction(function() use ($advertisingCampaignService, $platform, $accountId) {
            $this->deleteAccount($platform, $accountId);
            $this->deleteAccountWebsites($platform, $accountId);

            $advertisingCampaignService->deleteAccountAutomationStatus($platform, $accountId);
            $advertisingCampaignService->deleteAccountAutomationParameters($platform, $accountId);
        });

        return true;
    }
}
