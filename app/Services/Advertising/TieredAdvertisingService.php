<?php

namespace App\Services\Advertising;

use App\Contracts\Services\Advertising\AdvertisingServiceContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\GoogleAdsGeoTarget;
use App\Models\Legacy\Location;
use App\Models\MetaAdsLocation;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\TieredAdvertisingAccount;
use App\Models\TieredAdvertisingCampaign;
use App\Models\TieredAdvertisingConfiguration;
use App\Models\TieredAdvertisingCounty;
use App\Models\TieredAdvertisingInstance;
use App\Models\TieredAdvertisingLog;
use App\Services\Advertising\Campaigns\AdvertisingServiceFactory;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TieredAdvertisingService
{
    const string INDUSTRY_NAME = 'industry_name';
    const string ACCOUNT_NAME = 'account_name';
    const string INSTANCE_ID = 'instance_id';
    const string INSTANCE_NAME = 'instance_name';
    const string ACCOUNT_PLATFORM_ID = 'account_platform_id';

    const float DEFAULT_ROAS = 2.0;
    const int DEFAULT_FREQUENCY_MINS = 60;
    const int DEFAULT_ADVERTISER = Advertiser::GABE->value;

    const string TIER = 'tier';
    const string UPPER_BOUND = 'upperBound';
    const string LOWER_BOUND = 'lowerBound';
    const string TCPA_BID = 'tcpaBid';
    const string ACCT_ID = 'accountPlatformId';
    const string CAMPAIGN_ID = 'campaignPlatformId';
    const string NEW_CAMPAIGN = 'newCampaign';
    const string NAME = 'name';
    const string MESSAGE = 'message';
    const string INSTANCE = 'instance';

    /**
     * Constructor
     */
    public function __construct()
    {}

    /**
     * @param string $platformSlug
     * @param string $industrySlug
     * @param int|null $instanceId
     * @return Collection
     */
    public function getTieredAdvertisingCampaigns(
        string $platformSlug,
        string $industrySlug,
        ?int $instanceId = null,
    ): Collection
    {
        return TieredAdvertisingCampaign::query()
            ->join(Industry::TABLE, Industry::TABLE.'.'.Industry::FIELD_ID, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_INDUSTRY_ID)
            ->join(TieredAdvertisingAccount::TABLE, TieredAdvertisingAccount::TABLE.'.'.TieredAdvertisingAccount::FIELD_ID, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TIERED_ADVERTISING_ACCOUNT_ID)
            ->leftJoin(TieredAdvertisingInstance::TABLE, TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_INSTANCE_ID)
            ->where(Industry::TABLE.'.'.Industry::FIELD_SLUG, $industrySlug)
            ->where(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_PLATFORM, $platformSlug)
            ->where(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_INSTANCE_ID, $instanceId)
            ->orderBy(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TIER, 'asc')
            ->get([
                DB::raw(TieredAdvertisingCampaign::TABLE.'.*'),
                DB::raw(TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_ID.' as '.self::INSTANCE_ID),
                DB::raw(TieredAdvertisingInstance::TABLE.'.'.TieredAdvertisingInstance::FIELD_NAME.' as '.self::INSTANCE_NAME),
                Industry::TABLE.'.'.Industry::FIELD_NAME.' as '.self::INDUSTRY_NAME,
                TieredAdvertisingAccount::TABLE.'.'.TieredAdvertisingAccount::FIELD_NAME.' as '.self::ACCOUNT_NAME,
                TieredAdvertisingAccount::TABLE.'.'.TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID.' as '.self::ACCOUNT_PLATFORM_ID,
            ]);
    }

    /**
     * @param TieredAdvertisingCampaign $campaign
     * @param array $value
     * @return TieredAdvertisingCampaign
     */
    public function updateCampaignData(TieredAdvertisingCampaign $campaign, array $value): TieredAdvertisingCampaign
    {
        $data = $campaign->{TieredAdvertisingCampaign::FIELD_DATA};
        if ($data)
            $value = array_merge($data, $value);

        $campaign->update([
            TieredAdvertisingCampaign::FIELD_DATA => $value,
        ]);
        return $campaign;
    }

    /**
     * @param string $platform
     * @param int $industryId
     * @param int|null $instanceId
     * @return bool
     */
    public function checkEnabledStatus(string $platform, int $industryId, ?int $instanceId = null): bool
    {
        $config = $this->getConfigModel($platform, $industryId, $instanceId);
        return $config->{TieredAdvertisingConfiguration::FIELD_ENABLED} ?? false;
    }

    /**
     * @param string $platform
     * @param int $industryId
     * @param int|null $instanceId
     * @return TieredAdvertisingConfiguration|null
     */
    public function getConfigModel(string $platform, int $industryId, ?int $instanceId = null): ?TieredAdvertisingConfiguration
    {
        return TieredAdvertisingConfiguration::query()
            ->where(TieredAdvertisingConfiguration::FIELD_PLATFORM, $platform)
            ->where(TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID, $industryId)
            ->where(TieredAdvertisingConfiguration::FIELD_INSTANCE_ID, $instanceId)
            ->get()->first();
    }

    /**
     * @param string $configKey
     * @param string $platform
     * @param int $industryId
     * @param int|null $instanceId
     * @return mixed
     */
    public function getConfigKey(string $configKey, string $platform, int $industryId, ?int $instanceId = null): mixed
    {
        // Check platform and industry specific config
        return TieredAdvertisingConfiguration::query()
                   ->where(TieredAdvertisingConfiguration::FIELD_PLATFORM, $platform)
                   ->where(TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID, $industryId)
                   ->where(TieredAdvertisingConfiguration::FIELD_INSTANCE_ID, $instanceId)
                   ->get([TieredAdvertisingConfiguration::FIELD_CONFIGS])
                   ->first()?->{TieredAdvertisingConfiguration::FIELD_CONFIGS}[$configKey] ?? null;
    }

    /**
     * @param array $config
     * @param string $platform
     * @param int $industryId
     * @param int|null $instanceId
     * @return TieredAdvertisingConfiguration
     */
    public function setConfig(array $config, string $platform, int $industryId, ?int $instanceId = null): TieredAdvertisingConfiguration
    {
        $configModel = TieredAdvertisingConfiguration::query()
            ->where(TieredAdvertisingConfiguration::FIELD_PLATFORM, $platform)
            ->where(TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID, $industryId)
            ->where(TieredAdvertisingConfiguration::FIELD_INSTANCE_ID, $instanceId)
            ->get()
            ->first();

        if (!$configModel) {
            $configModel = TieredAdvertisingConfiguration::query()->create([
                TieredAdvertisingConfiguration::FIELD_PLATFORM => $platform,
                TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID => $industryId,
                TieredAdvertisingConfiguration::FIELD_ROAS => self::DEFAULT_ROAS,
                TieredAdvertisingConfiguration::FIELD_UPDATE_FREQUENCY_MINUTES => self::DEFAULT_FREQUENCY_MINS,
                TieredAdvertisingConfiguration::FIELD_LAST_LOCATION_UPDATE => null,
                TieredAdvertisingConfiguration::CREATED_AT => now(),
                TieredAdvertisingConfiguration::UPDATED_AT => now(),
                TieredAdvertisingConfiguration::FIELD_CONFIGS => $config,
                TieredAdvertisingConfiguration::FIELD_INSTANCE_ID => $instanceId,
            ]);
        } else {
            $configArray = json_decode($configModel->{TieredAdvertisingConfiguration::FIELD_CONFIGS});
            if ($configArray)
                $config = array_merge($configArray, $config);
            $configModel->update([
                TieredAdvertisingConfiguration::FIELD_CONFIGS => $config,
            ]);
        }
        return $configModel;
    }

    /**
     * @param string $platform
     * @param int $industryId
     * @param float $roas
     * @param int $updateFrequency
     * @param bool $enabled
     * @param int $advertiser
     * @param int|null $instanceId
     * @return void
     */
    public function updateConfigs(string $platform, int $industryId, float $roas, int $updateFrequency, bool $enabled, int $advertiser, ?int $instanceId = null): void
    {
        $config = $this->getConfigModel($platform, $industryId, $instanceId);

        // If config exists update, else create it
        if ($config) {
            $config->update([
                TieredAdvertisingConfiguration::FIELD_ROAS => $roas,
                TieredAdvertisingConfiguration::FIELD_UPDATE_FREQUENCY_MINUTES => $updateFrequency,
                TieredAdvertisingConfiguration::FIELD_ENABLED => $enabled,
                TieredAdvertisingConfiguration::FIELD_ADVERTISER => $advertiser,
            ]);
        } else {
            TieredAdvertisingConfiguration::query()->create([
                TieredAdvertisingConfiguration::FIELD_PLATFORM => $platform,
                TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID => $industryId,
                TieredAdvertisingConfiguration::FIELD_ENABLED => $enabled,
                TieredAdvertisingConfiguration::FIELD_ROAS => $roas,
                TieredAdvertisingConfiguration::FIELD_UPDATE_FREQUENCY_MINUTES => $updateFrequency,
                TieredAdvertisingConfiguration::FIELD_INSTANCE_ID => $instanceId,
                TieredAdvertisingConfiguration::FIELD_ADVERTISER => $advertiser,
            ]);
        }
    }

    /**
     * @param string $platform
     * @param int $industryId
     * @param bool $enabled
     * @param int|null $instanceId
     * @return void
     */
    public function setEnabled(string $platform, int $industryId, bool $enabled, ?int $instanceId = null): void
    {
        $config = $this->getConfigModel($platform, $industryId, $instanceId);
        $config->update([
            TieredAdvertisingConfiguration::FIELD_ENABLED => $enabled,
        ]);
    }

    /**
     * @param string $platform
     * @param string $industrySlug
     * @param array $campaigns
     * @param float $roas
     * @param int $updateFrequency
     * @param bool $enabled
     * @param int $advertiser
     * @param int|null $instanceId
     * @return array|string[]
     * @throws Exception
     */
    public function updateCampaigns(
        string $platform,
        string $industrySlug,
        array $campaigns,
        float $roas,
        int $updateFrequency,
        bool $enabled,
        int $advertiser,
        ?int $instanceId = null,
    ): array
    {
        $industryModel = IndustryEnum::fromSlug($industrySlug)->model();
        $oldCampaigns = $this->getTieredAdvertisingCampaigns($platform, $industrySlug, $instanceId);
        $newCampaigns = collect($campaigns)->groupBy(TieredAdvertisingCampaign::FIELD_ID);
        $platformEnum = AdvertisingPlatform::from($platform);
        $advertiserEnum = Advertiser::from($advertiser);

        // Error messages
        $messages = [];

        // TODO: Remove this when Tiered Advertising enabled for Microsoft
        if ($platform === AdvertisingPlatform::MICROSOFT->value)
            return ["Tiered Advertising for Microsoft is not enabled yet."];

        // Need advertising service for TCPA bid updates and name verification
        $advertisingService = AdvertisingServiceFactory::make($platformEnum->value);

        // Verify communication with accounts and campaigns, get names from platform API
        $campaignNames = $this->verifyAccountCampaigns($campaigns, $enabled, $industryModel, $platformEnum, $advertisingService, $advertiserEnum);

        // Campaign verification will return message key with error if it fails
        if ($campaignNames[self::MESSAGE] ?? false)
            return [$campaignNames[self::MESSAGE]];

        // Update configuration model for platform-industry
        $this->updateConfigs($platform, $industryModel->{Industry::FIELD_ID}, $roas, $updateFrequency, $enabled, $advertiser, $instanceId);

        // Update all existing campaigns for this platform / industry
        foreach ($oldCampaigns as $existingCampaign) {
            $updates = $newCampaigns[$existingCampaign->{TieredAdvertisingCampaign::FIELD_ID}] ?? null;

            // If campaign is not included in updates, it is deleted
            if (!$updates) {
                TieredAdvertisingCampaign::query()
                    ->where(TieredAdvertisingCampaign::FIELD_ID, $existingCampaign->{TieredAdvertisingCampaign::FIELD_ID})
                    ->delete();
                continue;
            }

            // Get account platform id (already verified existing above)
            $updates = $updates->first();
            $accountPlatformId = $updates[self::ACCT_ID];
            $accountModel = TieredAdvertisingAccount::query()
                ->where(TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $accountPlatformId)
                ->get()->first();

            // Account model should have been created and verified in verifyAccountCampaigns
            if (!$accountModel)
                return ["Failed to find account model with platform id $accountPlatformId for campaign {$updates[self::CAMPAIGN_ID]} ({$campaignNames[$updates[self::CAMPAIGN_ID]]})"];

            // Only set tcpa bid if it has been updated
            if ($existingCampaign->{TieredAdvertisingCampaign::FIELD_TCPA_BID} != $updates[self::TCPA_BID] && $enabled) {
                $tcpaUpdate = $advertisingService->setTcpaBid($advertiserEnum, $accountModel->{TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID}, $updates[self::CAMPAIGN_ID], $updates[self::TCPA_BID]);

                if (!$tcpaUpdate)
                    $messages[] = 'Failed to set TCPA bid for campaign '.$updates[self::CAMPAIGN_ID].'.';
            }

            // Update campaign model
            $existingCampaign->update([
                TieredAdvertisingCampaign::FIELD_TIERED_ADVERTISING_ACCOUNT_ID => $accountModel->{TieredAdvertisingAccount::FIELD_ID},
                TieredAdvertisingCampaign::FIELD_TIER => $updates[self::TIER],
                TieredAdvertisingCampaign::FIELD_UPPER_BOUND => $updates[self::UPPER_BOUND],
                TieredAdvertisingCampaign::FIELD_LOWER_BOUND => $updates[self::LOWER_BOUND],
                TieredAdvertisingCampaign::FIELD_TCPA_BID => $updates[self::TCPA_BID],
                TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID => $updates[self::CAMPAIGN_ID],
                TieredAdvertisingCampaign::FIELD_NAME => $campaignNames[$updates[self::CAMPAIGN_ID]],
            ]);
        }

        // Create new campaigns for those not updated
        foreach ($campaigns as $newCampaign) {
            if ($newCampaign[self::NEW_CAMPAIGN] ?? false) {

                // Get platform account id (already verified above)
                $accountPlatformId = $newCampaign[self::ACCT_ID];
                $accountModel = TieredAdvertisingAccount::query()
                    ->where(TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $accountPlatformId)
                    ->get()->first();

                // Account model should have been created and verified in verifyAccountCampaigns
                if (!$accountModel)
                    $messages[] = "Failed to find account model with platform id $accountPlatformId for campaign {$newCampaign[self::CAMPAIGN_ID]} ({$campaignNames[$newCampaign[self::CAMPAIGN_ID]]})";

                // Set TCPA Bid for newly added campaign
                if ($enabled) {
                    $tcpaUpdate = $advertisingService->setTcpaBid($advertiserEnum, $accountModel->{TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID}, $newCampaign[self::CAMPAIGN_ID], $newCampaign[self::TCPA_BID]);
                    if (!$tcpaUpdate)
                        $messages[] = 'Failed to set TCPA bid for campaign '.$newCampaign[self::CAMPAIGN_ID].'.';
                }

                // Create model
                TieredAdvertisingCampaign::query()->create([
                    TieredAdvertisingCampaign::FIELD_PLATFORM => $platform,
                    TieredAdvertisingCampaign::FIELD_INDUSTRY_ID => $industryModel->{Industry::FIELD_ID},
                    TieredAdvertisingCampaign::FIELD_TIERED_ADVERTISING_ACCOUNT_ID => $accountModel->{TieredAdvertisingAccount::FIELD_ID},
                    TieredAdvertisingCampaign::FIELD_TIER => $newCampaign[self::TIER],
                    TieredAdvertisingCampaign::FIELD_UPPER_BOUND => $newCampaign[self::UPPER_BOUND],
                    TieredAdvertisingCampaign::FIELD_LOWER_BOUND => $newCampaign[self::LOWER_BOUND],
                    TieredAdvertisingCampaign::FIELD_TCPA_BID => $newCampaign[self::TCPA_BID],
                    TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID => $newCampaign[self::CAMPAIGN_ID],
                    TieredAdvertisingCampaign::FIELD_NAME => $campaignNames[$newCampaign[self::CAMPAIGN_ID]],
                    TieredAdvertisingCampaign::FIELD_INSTANCE_ID => $instanceId,
                ]);
            }
        }

        // Delete tiered advertising account models with no campaigns associated with them
        $this->clearAccountsWithNoCampaigns();

        // Empty messages array means success here, no error messages to report
        return $messages;
    }

    /**
     * If tiered advertising is enabled, verify communication with each ad account and campaign.
     * Return an array of ad campaign ids => names
     * If error, return message as ['message' => <error message>]
     *
     * @param array $campaigns
     * @param bool $enabled
     * @param Industry $industryModel
     * @param AdvertisingPlatform $platformEnum
     * @param AdvertisingServiceContract $advertisingService
     * @param Advertiser $advertiser
     * @return array|string[]
     * @throws Exception
     */
    public function verifyAccountCampaigns(
        array $campaigns,
        bool $enabled,
        Industry $industryModel,
        AdvertisingPlatform $platformEnum,
        AdvertisingServiceContract $advertisingService,
        Advertiser $advertiser,
    ): array
    {
        $campaignNames = [];

        foreach ($campaigns as $campaign) {
            $accountPlatformId = $campaign[self::ACCT_ID];

            // Look for existing model for account
            $accountModel = TieredAdvertisingAccount::query()
                ->where(TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $accountPlatformId)
                ->get()->first();

            // Create model if no existing found
            if (!$accountModel) {
                if ($enabled) {
                    // Verify that we can talk to this account via the platform API
                    $acctName = $advertisingService->getAccountName($advertiser, $accountPlatformId);
                    if (!$acctName)
                        return [self::MESSAGE => "Unable to establish communication with " . $platformEnum->name . " account ID " . $accountPlatformId];

                } else {
                    $acctName = TieredAdvertisingAccount::TEMP_NAME;
                }

                TieredAdvertisingAccount::query()->create([
                    TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID => $accountPlatformId,
                    TieredAdvertisingAccount::FIELD_NAME => $acctName,
                    TieredAdvertisingAccount::FIELD_INDUSTRY_ID => $industryModel->{Industry::FIELD_ID},
                    TieredAdvertisingAccount::FIELD_PLATFORM => $platformEnum->value,
                    TieredAdvertisingAccount::CREATED_AT => now(),
                    TieredAdvertisingAccount::UPDATED_AT => now(),
                ]);
            } else if ($accountModel->{TieredAdvertisingAccount::FIELD_NAME} === TieredAdvertisingAccount::TEMP_NAME) {
                if ($enabled) {
                    // Get account name and update if not retrieved when created
                    $name = $advertisingService->getAccountName($advertiser, $accountPlatformId);
                    if (!$name)
                        return [self::MESSAGE => "Unable to establish communication with " . $platformEnum->name . " account ID " . $accountPlatformId];

                    // Update account with name from platform
                    $accountModel->update([
                        TieredAdvertisingAccount::FIELD_NAME => $name,
                    ]);
                }
            }

            $campaignPlatformId = $campaign[self::CAMPAIGN_ID];

            if ($enabled) {
                // Verify that we can communicate with campaign over platform API
                $campaignName = $advertisingService->getCampaignName($advertiser, $accountPlatformId, $campaignPlatformId);
                if (!$campaignName)
                    return [self::MESSAGE => "Unable to establish communication with " . $platformEnum->name . " campaign ID " . $campaignPlatformId . " using account ID " . $accountPlatformId];
            } else {
                // Use already stored name, or use temporary name if new
                $storedCampaign = TieredAdvertisingCampaign::query()
                    ->where(TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, $campaignPlatformId)
                    ->get()->first();

                if ($storedCampaign) {
                    $campaignName = $storedCampaign->{TieredAdvertisingCampaign::FIELD_NAME};
                } else {
                    $campaignName = TieredAdvertisingCampaign::TEMP_NAME;
                }
            }

            // Confirm that campaign is not already in use for another enabled tier or platform/industry
            if ($enabled) {
                $similarCampaigns = TieredAdvertisingCampaign::query()
                    ->join(TieredAdvertisingConfiguration::TABLE, fn (JoinClause $join) =>
                        $join->on(TieredAdvertisingConfiguration::TABLE.'.'.TieredAdvertisingConfiguration::FIELD_PLATFORM, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_PLATFORM)
                            ->on(TieredAdvertisingConfiguration::TABLE.'.'.TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_INDUSTRY_ID)
                    )
                    ->where(TieredAdvertisingConfiguration::TABLE.'.'.TieredAdvertisingConfiguration::FIELD_ENABLED, true)
                    ->whereNot(function ($query) use ($platformEnum, $industryModel) {
                        return $query->where(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_PLATFORM, '=', $platformEnum->value)
                            ->where(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_INDUSTRY_ID, '=', $industryModel->{Industry::FIELD_ID});
                    })
                    ->where(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, $campaignPlatformId)
                    ->get();

                if ($similarCampaigns->count() > 0) {
                    return [self::MESSAGE => "Campaign ID $campaignPlatformId is already in use with tiered advertising for another platform - industry."];
                }
            }

            // Store campaign name with id in array
            $campaignNames[$campaignPlatformId] = $campaignName;
        }

        return $campaignNames;
    }

    /**
     * Clearing left over accounts with no campaigns associated
     *
     * @return void
     */
    public function clearAccountsWithNoCampaigns(): void
    {
        TieredAdvertisingAccount::query()
            ->whereNotExists(function($query) {
                $query->select(DB::raw(1))
                    ->from(TieredAdvertisingCampaign::TABLE)
                    ->whereColumn(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TIERED_ADVERTISING_ACCOUNT_ID, TieredAdvertisingAccount::TABLE.'.'.TieredAdvertisingAccount::FIELD_ID);
            })->delete();
    }

    /**
     * Used by the advertising services to determine county assignments for each campaign
     *
     * @param string $platform
     * @param int $industryId
     * @param int|null $instanceId
     * @return Collection
     */
    public function getCampaignCountyAssignments(string $platform, int $industryId, ?int $instanceId = null): Collection
    {
        return TieredAdvertisingCounty::query()
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_LOCATION_ID)
            ->join(TieredAdvertisingCampaign::TABLE, TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_ID, '=', TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIERED_ADVERTISING_CAMPAIGN_ID)
            ->join(TieredAdvertisingAccount::TABLE, TieredAdvertisingAccount::TABLE.'.'.TieredAdvertisingAccount::FIELD_ID, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TIERED_ADVERTISING_ACCOUNT_ID)
            ->join(GoogleAdsGeoTarget::TABLE, GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
            ->leftJoin(MetaAdsLocation::TABLE, MetaAdsLocation::TABLE.'.'.MetaAdsLocation::FIELD_LOCATION_ID, '=', TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_LOCATION_ID)
            ->where(TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM, $platform)
            ->where(TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_INDUSTRY_ID, $industryId)
            ->where(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_INSTANCE_ID, $instanceId)
            ->get([
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_ID.' as tiered_ads_campaign_id',
                Location::TABLE.'.'.Location::ID.' as location_id',
                Location::TABLE.'.'.Location::COUNTY,
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TIER,
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TCPA_BID,
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_LOWER_BOUND,
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_UPPER_BOUND,
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID,
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_TIERED_ADVERTISING_ACCOUNT_ID,
                TieredAdvertisingAccount::TABLE.'.'.TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID,
                TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_DATA,
                TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_NEGATIVE_ZIP_CODES,
                GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_CRITERIA_ID,
                MetaAdsLocation::TABLE.'.'.MetaAdsLocation::FIELD_KEY,
            ])
            ->groupBy([TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID]);
    }

    /**
     * Sets the last time updated locations to now
     *
     * @param string $platform
     * @param int $industryId
     * @param int|null $instanceId
     * @return void
     */
    public function setLocationsUpdatedDate(string $platform, int $industryId, ?int $instanceId = null): void
    {
        $campaigns = TieredAdvertisingCampaign::query()
            ->where(TieredAdvertisingCampaign::FIELD_PLATFORM, $platform)
            ->where(TieredAdvertisingCampaign::FIELD_INDUSTRY_ID, $industryId)
            ->where(TieredAdvertisingCampaign::FIELD_INSTANCE_ID, $instanceId)
            ->get();

        // Update campaign last location update date
        foreach ($campaigns as $campaign) {
            $this->updateCampaignData($campaign, [TieredAdvertisingCampaign::DATA_LAST_LOCATION_UPDATE => now()]);
        }
    }

    /**
     * Stores tiered advertising logs
     *
     * @param array $logs
     * @return void
     */
    public function saveTieredAdvertisingLogs(array $logs): void
    {
        DB::transaction(function () use ($logs) {
            foreach (array_chunk($logs, 500) as $chunkInsert) {
                TieredAdvertisingLog::insert($chunkInsert);
            }
        });
    }

    /**
     * @param int $campaignId
     * @return Collection
     */
    public function getCampaignLocations(int $campaignId): Collection
    {
        return TieredAdvertisingCounty::query()
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_LOCATION_ID)
            ->where(TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIERED_ADVERTISING_CAMPAIGN_ID, $campaignId)
            ->get([
                Location::TABLE.'.'.Location::COUNTY,
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                Location::TABLE.'.'.Location::STATE,
                TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TCPA_BID,
                TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_NEGATIVE_ZIP_CODES,
                TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_DATA,
            ]);
    }

    /**
     * @param string $platform
     * @param int $industryId
     * @return array
     */
    public function getInstanceOptions(string $platform, int $industryId): array
    {
        $instances = TieredAdvertisingInstance::query()
            ->where(TieredAdvertisingInstance::FIELD_INDUSTRY_ID, $industryId)
            ->where(TieredAdvertisingInstance::FIELD_PLATFORM, $platform)
            ->get([
                TieredAdvertisingInstance::FIELD_NAME,
                TieredAdvertisingInstance::FIELD_ID,
            ]);

        return $instances->map(function ($instance) {
            return [
                'id'    => $instance->{TieredAdvertisingInstance::FIELD_ID},
                'name'  => $instance->{TieredAdvertisingInstance::FIELD_NAME},
            ];
        })->toArray();
    }

    /**
     * Creates an instance, returns a message if failed, no message if successful
     *
     * @param string $platform
     * @param int $industryId
     * @param string $instanceName
     * @param bool $assignExisting
     * @return array|string[]
     */
    public function createInstance(string $platform, int $industryId, string $instanceName, bool $assignExisting): array
    {
        // Verify instance name is available
        $nameCheck = TieredAdvertisingInstance::query()->where(TieredAdvertisingInstance::FIELD_NAME, $instanceName)->get()->first();
        if ($nameCheck)
            return [self::MESSAGE => "Name already in use."];

        // Create instance entry
        $instanceModel = TieredAdvertisingInstance::create([
            TieredAdvertisingInstance::FIELD_PLATFORM       => $platform,
            TieredAdvertisingInstance::FIELD_INDUSTRY_ID    => $industryId,
            TieredAdvertisingInstance::FIELD_NAME           => $instanceName,
        ]);

        // If assign existing, set campaigns with no instance to new instance
        if ($assignExisting) {
            $campaigns = TieredAdvertisingCampaign::query()
                ->where(TieredAdvertisingCampaign::FIELD_PLATFORM, $platform)
                ->where(TieredAdvertisingCampaign::FIELD_INDUSTRY_ID, $industryId)
                ->whereNull(TieredAdvertisingCampaign::FIELD_INSTANCE_ID)
                ->whereNull(TieredAdvertisingCampaign::FIELD_DELETED_AT)
                ->get();

            // Update each campaign
            foreach ($campaigns as $campaign) {
                $campaign->update([
                    TieredAdvertisingCampaign::FIELD_INSTANCE_ID => $instanceModel->{TieredAdvertisingInstance::FIELD_ID},
                ]);
            }

            // Update config
            $configModel = $this->getConfigModel($platform, $industryId);

            if (!$configModel) {
                // Create config if it does not exist
                $this->setConfig([], $platform, $industryId, $instanceModel->{TieredAdvertisingInstance::FIELD_ID});
            } else {
                // Update instance in config if it does exist
                $configModel->update([
                    TieredAdvertisingConfiguration::FIELD_INSTANCE_ID => $instanceModel->{TieredAdvertisingInstance::FIELD_ID},
                ]);
            }
        }

        return [self::INSTANCE => $instanceModel, self::MESSAGE => null];
    }

    /**
     * Deletes instance and associated data, returns a message if failed, no message if successful
     *
     * @param string $platform
     * @param int $industryId
     * @param int $instanceId
     * @return array
     */
    public function deleteInstance(string $platform, int $industryId, int $instanceId): array
    {
        // Get campaign ids for this instance for location assignments delete
        $instanceCampaignIds = TieredAdvertisingCampaign::query()
            ->where(TieredAdvertisingCampaign::FIELD_INSTANCE_ID, $instanceId)
            ->get([TieredAdvertisingCampaign::FIELD_ID])
            ->pluck(TieredAdvertisingCampaign::FIELD_ID);

        // Delete campaign location assignments
        TieredAdvertisingCounty::query()
            ->whereIn(TieredAdvertisingCounty::FIELD_TIERED_ADVERTISING_CAMPAIGN_ID, $instanceCampaignIds)
            ->delete();

        // Delete campaigns
        TieredAdvertisingCampaign::query()
            ->where(TieredAdvertisingCampaign::FIELD_INSTANCE_ID, $instanceId)
            ->delete();

        // Delete configuration
        TieredAdvertisingConfiguration::query()
            ->where(TieredAdvertisingConfiguration::FIELD_INSTANCE_ID, $instanceId)
            ->delete();

        // Delete instance
        TieredAdvertisingInstance::query()
            ->where(TieredAdvertisingInstance::FIELD_ID, $instanceId)
            ->delete();

        // Clear accounts with no campaign assignments
        $this->clearAccountsWithNoCampaigns();

        return [self::MESSAGE => null];
    }
}
