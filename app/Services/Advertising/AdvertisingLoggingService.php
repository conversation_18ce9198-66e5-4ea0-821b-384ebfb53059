<?php

namespace App\Services\Advertising;

use App\Jobs\RecordMonitoringLog;
use App\Models\AdvertisingCampaignHistoryLog;
use App\Repositories\LogMonitoringRepository;
use Exception;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Log;

class AdvertisingLoggingService
{
    const RATE_LIMITER_KEY = __CLASS__ . '::sendNotificationEmails';
    const MAX_ATTEMPTS = 1;
    const DECAY_MINUTES = 60;

    /**
     * @param string $platform
     * @param string $accountId
     * @param string $campaignId
     * @param string $message
     * @param int $type
     * @return bool
     */
    public static function createLogEntry(string $platform, string $accountId, string $campaignId, string $message, int $type): bool
    {
        AdvertisingCampaignHistoryLog::query()->create([
            AdvertisingCampaignHistoryLog::FIELD_PLATFORM => $platform,
            AdvertisingCampaignHistoryLog::FIELD_PLATFORM_ACCOUNT_ID => $accountId,
            AdvertisingCampaignHistoryLog::FIELD_PLATFORM_CAMPAIGN_ID => $campaignId,
            AdvertisingCampaignHistoryLog::FIELD_MESSAGE => $message,
            AdvertisingCampaignHistoryLog::FIELD_TYPE => $type
        ]);

        return true;
    }

    /**
     * @param array $entries
     * @return bool
     */
    public static function bulkInsertLogEntries(array $entries): bool
    {
        DB::table(AdvertisingCampaignHistoryLog::TABLE)->insert($entries);

        return true;
    }

    /**
     * @param string $message
     * @param array|null $payload
     * @return void
     */
    public static function writeLog(string $message, ?array $payload = []): void
    {
        try {
            $payload['env'] = App::environment();

            RecordMonitoringLog::dispatch($message, $payload, LogMonitoringRepository::LOGGER_ADVERTISING);
        }
        catch(Exception $e) {
            logger()->error($e->getMessage());
        }
    }

    /**
     * @param array $emailRecipients
     * @param string $subject
     * @param string $message
     * @return bool
     */
    public static function sendNotificationEmails(array $emailRecipients, string $subject, string $message): bool
    {
        // Use RateLimiter to limit email sending in non-production environments
        if (!App::isProduction() && RateLimiter::tooManyAttempts(self::RATE_LIMITER_KEY, self::MAX_ATTEMPTS, self::DECAY_MINUTES)) {
            // Log the rate limiting failure
            Log::info('Rate limiting exceeded for sending notification emails');
            return false;
        }

        foreach($emailRecipients as $recipient) {
            Mail::raw(
                $message,
                function(Message $message) use ($recipient, $subject) {
                    $message
                        ->to(trim($recipient))
                        ->subject($subject);
                }
            );
        }

        // Register the attempt with the rate limiter
        if (!App::isProduction()) {
            RateLimiter::hit(self::RATE_LIMITER_KEY, self::DECAY_MINUTES);
        }

        return true;
    }
}
