<?php

namespace App\Services\Advertising\Campaigns;

use App\Contracts\Services\Advertising\AdvertisingLocationsServiceContract;
use App\Contracts\Services\Advertising\AdvertisingServiceContract;
use App\DataModels\Advertising\AccountCampaigns;
use App\DataModels\Advertising\AdCampaignsPaginatorDataModel;
use App\DataModels\Advertising\CampaignLocations;
use App\DataModels\Advertising\DashboardTargetLocation;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Jobs\Advertising\UpdateAdvertisingCampaignLocationsJob;
use App\Models\Advertiser;
use App\Models\AdvertisingAccount;
use App\Models\AdvertisingCampaign;
use App\Models\Legacy\Location;
use App\Models\LockedAdvertisingCampaignLocation;
use App\Models\MetaAdsLocation;
use App\Models\Odin\Industry;
use App\Models\TieredAdvertisingConfiguration;
use App\Models\TieredAdvertisingCounty;
use App\Services\Advertising\AdvertisingAccountService;
use App\Services\Advertising\AdvertisingCampaignService;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Authentication\MetaAdsAuthService;
use App\Services\Advertising\Locations\MetaAdsLocationsService;
use App\Services\Advertising\TieredAdvertisingService;
use App\Services\DatabaseHelperService;
use App\Transformers\Advertising\AdvertisingCampaignsTransformer;
use Exception;
use FacebookAds\Api;
use FacebookAds\ApiConfig;
use FacebookAds\Http\Exception\RequestException;
use FacebookAds\Http\RequestInterface;
use FacebookAds\Http\ResponseInterface;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\AdSet;
use FacebookAds\Object\Campaign;
use FacebookAds\Object\Fields\AdSetFields;
use FacebookAds\Object\Fields\TargetingFields;
use FacebookAds\Object\Fields\TargetingGeoLocationFields;
use FacebookAds\Object\Targeting;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use JetBrains\PhpStorm\ArrayShape;
use Throwable;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;

class MetaAdsService implements AdvertisingServiceContract
{
    const MAX_REQUESTS_PER_BATCH = 40;

    const AD_SET_STATUS_ACTIVE = 'ACTIVE';
    const AD_SET_STATUS_PAUSED = 'PAUSED';
    const AD_SET_STATUS_PENDING_REVIEW = 'PENDING_REVIEW';
    const AD_SET_STATUS_CREDIT_CARD_NEEDED = 'CREDIT_CARD_NEEDED';
    const AD_SET_STATUS_PREAPPROVED = 'PREAPPROVED';
    const AD_SET_STATUS_DISABLED = 'DISABLED';
    const AD_SET_STATUS_ARCHIVED = 'ARCHIVED';
    const AD_SET_STATUS_DELETED = 'DELETED';

    const REQUEST_KEY_METHOD = 'method';
    const REQUEST_KEY_RELATIVE_URL = 'relative_url';
    const REQUEST_KEY_BODY = 'body';

    const ARRAY_KEY_REQUEST = 'request';
    const ARRAY_KEY_CAMPAIGN_ID = 'campaign_id';
    const ARRAY_KEY_AD_SET_ID = 'ad_set_id';

    const TYPE_KEY_SEPARATOR = '+';

    private Api $metaAdsClient;

    /**
     * @param MetaAdsAuthService $metaAdsAuthService
     * @param AdvertisingAccountService $advertisingAccountService
     * @param AdvertisingCampaignService $advertisingCampaignService
     * @param MetaAdsLocationsService $metaAdsLocationsService
     * @param TieredAdvertisingService $tieredAdvertisingService
     */
    public function __construct(
        private readonly MetaAdsAuthService $metaAdsAuthService,
        private readonly AdvertisingAccountService $advertisingAccountService,
        private readonly AdvertisingCampaignService $advertisingCampaignService,
        private readonly MetaAdsLocationsService $metaAdsLocationsService,
        private readonly TieredAdvertisingService $tieredAdvertisingService,
    )
    {

    }

    /**
     * @param $accountId
     * @return Api
     * @throws Throwable
     */
    private function getClient($accountId): Api
    {
        $advertiserKey = $this->advertisingAccountService
            ->getAccountByIdAndPlatform($accountId, AdvertisingPlatform::META, [AdvertisingAccount::RELATION_ADVERTISER])
            ->{AdvertisingAccount::RELATION_ADVERTISER}
            ->{Advertiser::FIELD_KEY};

        $this->metaAdsClient = $this->metaAdsAuthService->getClient(AdvertiserEnum::fromKey($advertiserKey));

        if(!App::environment('production')) {
            $this->metaAdsClient->setLogger(new CurlLogger());
        }

        return $this->metaAdsClient;
    }

    /**
     * @inheritDoc
     *
     * The Meta equivalent of a campaign is an Ad Set
     */
    public function getCampaignStatusOptions(): Collection
    {
        return collect([
            self::AD_SET_STATUS_ACTIVE => 'Active',
            self::AD_SET_STATUS_PAUSED => 'Paused',
            self::AD_SET_STATUS_PENDING_REVIEW => 'Pending Review',
            self::AD_SET_STATUS_CREDIT_CARD_NEEDED => 'Credit Card Needed',
            self::AD_SET_STATUS_PREAPPROVED => 'Preapproved',
            self::AD_SET_STATUS_DISABLED => 'Disabled',
            self::AD_SET_STATUS_ARCHIVED => 'Archived',
            self::AD_SET_STATUS_DELETED => 'Deleted'
        ]);
    }

    /**
     * @inheritDoc
     *
     * The Meta equivalent of a SR campaign is an Ad Set
     * The Meta equivalent of a SR account is a Meta Campaign
     *
     * Meta Campaign => SR Account
     * Meta Ad Set => SR Campaign
     * @throws Exception
     */
    public function getCampaignsPaginated(
        $accountId,
        int $page,
        int $perPage = 10,
        ?int $searchStateLocationId = null,
        ?string $costMetric = null,
        ?string $status = null,
        ?string $name = null,
        ?array &$pageTokens = null
    ): AdCampaignsPaginatorDataModel
    {
        $this->getClient($accountId);

        $metaCampaign = new Campaign($accountId, null, $this->metaAdsClient);

        $params = [
            'limit' => $perPage,
            'summary' => 'total_count'
        ];

        if(!empty($pageTokens['page'])) {
            $priorPage = $pageTokens['page'];

            if($priorPage > $page) {
                $params['before'] = $pageTokens['before'];
            }
            else if($priorPage < $page) {
                $params['after'] = $pageTokens['after'];
            }
        }

        if(!empty($status)
        && in_array($status, $this->getCampaignStatusOptions()->keys()->toArray())) {
            $params['effective_status'] = [$status];
        }

        $adSetsCursor = $metaCampaign->getAdSets(
            [AdSetFields::ID, AdSetFields::STATUS, AdSetFields::NAME, AdSetFields::TARGETING, AdSetFields::DAILY_BUDGET],
            $params
        );

        $adSets = collect($adSetsCursor->getArrayCopy(true))->keyBy(AdSetFields::ID);

        $this->formatAdSetData($accountId, $adSets);
        $this->advertisingCampaignService->attachAutomationStatusToPlatformCampaigns($adSets, AdvertisingPlatform::META->value, $accountId);
        $this->advertisingCampaignService->attachAutomationParametersToPlatformCampaigns($adSets, AdvertisingPlatform::META->value, $accountId);

        $adSets = app(AdvertisingCampaignsTransformer::class)->transformCampaigns($adSets);

        $pageTokens = [
            "page" => $page,
            "before" => $adSetsCursor->getBefore(),
            "after" => $adSetsCursor->getAfter()
        ];

        $totalCount = $adSetsCursor->getLastResponse()->getContent()['summary']['total_count'];

        $paginator = new LengthAwarePaginator($adSets, $totalCount, $perPage, $page);

        return new AdCampaignsPaginatorDataModel(
            $page,
            $perPage,
            $paginator->lastPage(),
            $paginator->firstItem(),
            $paginator->lastItem(),
            $paginator->total(),
            $paginator->getCollection()->toArray(),
            [
                [
                    'url' => $paginator->previousPageUrl(),
                    'label' => 'Previous',
                    'active' => false
                ],
                [
                    'url' => $paginator->nextPageUrl(),
                    'label' => 'Next',
                    'active' => false
                ]
            ],
            $paginator->path(),
            $paginator->url(1),
            $paginator->url($paginator->lastPage()),
            $paginator->nextPageUrl() ?? '',
            $paginator->previousPageUrl() ?? ''
        );
    }

    /**
     * @param $accountId
     * @param Collection $adSets
     * @return Collection
     * @throws Exception
     */
    private function formatAdSetData($accountId, Collection &$adSets): Collection
    {
        $extractedLocationInfo = $this->metaAdsLocationsService->extractAdSetLocations($adSets);

        $adSetLocationKeys = $extractedLocationInfo[MetaAdsLocationsService::FUNC_RETURN_CAMPAIGN_LOCATIONS];
        $locationKeys = $extractedLocationInfo[MetaAdsLocationsService::FUNC_RETURN_PLATFORM_LOCATION_IDS];

        $metaLocationsInfo = MetaAdsLocation::query()
            ->with([
                MetaAdsLocation::RELATION_LOCATION => function ($relation) {
                    $relation->select([
                        Location::ID,
                        Location::TYPE
                    ]);
                }
            ])
            ->where(function($where) use ($locationKeys) {
                $where
                    ->where(function ($subWhere) use ($locationKeys) {
                        $subWhere
                            ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_REGION)
                            ->whereIn(MetaAdsLocation::FIELD_KEY, $locationKeys[TargetingGeoLocationFields::REGIONS]);
                    })
                    ->orWhere(function ($subWhere) use ($locationKeys) {
                        $subWhere
                            ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_MEDIUM_GEO_AREA)
                            ->whereIn(MetaAdsLocation::FIELD_KEY, array_merge($locationKeys[TargetingGeoLocationFields::MEDIUM_GEO_AREAS], $locationKeys[TargetingGeoLocationFields::CITIES]));
                    })
                    ->orWhere(function ($subWhere) use ($locationKeys) {
                        $subWhere
                            ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_CITY)
                            ->whereIn(MetaAdsLocation::FIELD_KEY, array_merge($locationKeys[TargetingGeoLocationFields::MEDIUM_GEO_AREAS], $locationKeys[TargetingGeoLocationFields::CITIES]));
                    })
                    ->orWhere(function ($subWhere) use ($locationKeys) {
                        $subWhere
                            ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_ZIP)
                            ->whereIn(MetaAdsLocation::FIELD_KEY, $locationKeys[TargetingGeoLocationFields::ZIPS]);
                    });
            })
            ->select([
                MetaAdsLocation::FIELD_KEY,
                MetaAdsLocation::FIELD_LOCATION_ID,
                MetaAdsLocation::FIELD_NAME,
                MetaAdsLocation::FIELD_TYPE
            ])
            ->get()
            ->groupBy(MetaAdsLocation::FIELD_TYPE)
            ->mapWithKeys(function ($locations, $type) {
                $typeKey = MetaAdsLocationsService::SINGULAR_PLURAL_LOCATION_TYPES[$type];

                return [
                    $typeKey => $locations->keyBy(MetaAdsLocation::FIELD_KEY)
                ];
            })
            ->toArray();

        $lockedLocations = LockedAdvertisingCampaignLocation::query()
            ->where(LockedAdvertisingCampaignLocation::FIELD_PLATFORM, AdvertisingPlatform::META->value)
            ->where(LockedAdvertisingCampaignLocation::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->whereIn(LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID, $adSets->keys()->toArray())
            ->get()
            ->groupBy([LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID, LockedAdvertisingCampaignLocation::FIELD_LOCATION_ID])
            ->toArray();

        foreach($adSets as $adSetId => $adSet) {
            $includedLocations = [];
            $excludedLocations = [];

            foreach($adSetLocationKeys[$adSetId] as $metaLocationType => $locations) {
                foreach($locations as $metaLocationKey => $targeted) {
                    if(in_array($metaLocationType, [TargetingGeoLocationFields::MEDIUM_GEO_AREAS, TargetingGeoLocationFields::CITIES], true)) {
                        $metaLocation = $metaLocationsInfo[TargetingGeoLocationFields::CITIES][$metaLocationKey] ?? $metaLocationsInfo[TargetingGeoLocationFields::MEDIUM_GEO_AREAS][$metaLocationKey] ?? [];
                    }
                    else {
                        $metaLocation = $metaLocationsInfo[$metaLocationType][$metaLocationKey] ?? [];
                    }

                    $locationId = $metaLocation[MetaAdsLocation::FIELD_LOCATION_ID] ?? 0;
                    $locationType = $metaLocation[MetaAdsLocation::RELATION_LOCATION][Location::TYPE] ?? null;
                    $name = $metaLocation[MetaAdsLocation::FIELD_NAME] ?? "$metaLocationKey (Meta Location ID; Location not in SR database)";
                    $locked = (int) ($lockedLocations[$adSetId][$locationId][0][LockedAdvertisingCampaignLocation::FIELD_TARGETED] ?? LockedAdvertisingCampaignLocation::TARGETED_NONE);

                    $dashboardTargetLocation = (new DashboardTargetLocation($locationId, $name, $locked, $locationType))->toArray();

                    if ($targeted) {
                        $includedLocations[] = $dashboardTargetLocation;
                    }
                    else {
                        $excludedLocations[] = $dashboardTargetLocation;
                    }
                }
            }

            $adSets->put(
                $adSetId,
                [
                    'name' => $adSet->{AdSetFields::NAME},
                    'status' => $adSet->{AdSetFields::STATUS},
                    'campaign_budget_amount' => round($adSet->{AdSetFields::DAILY_BUDGET} / 100, 2),
                    'locations' => [
                        'include' => $includedLocations,
                        'exclude' => $excludedLocations,
                    ]
                ]
            );
        }

        return $adSets;
    }

    /**
     * @inheritDoc
     * @throws Exception|Throwable
     */
    public function getAutomatedCampaignsWithLocations(int $logId, array $retryCampaigns = []): ?AccountCampaigns
    {
        AdvertisingLoggingService::writeLog("Retrieving automated campaigns for locations update", ["id" => $logId, "platform" => AdvertisingPlatform::META->value]);

        /** @var AccountCampaigns $campaignAdSets */
        $campaignAdSets = $this->advertisingCampaignService->getAutomatedCampaignsWithParametersByPlatform(AdvertisingPlatform::META->value, $retryCampaigns);

        if(is_null($campaignAdSets)) {
            AdvertisingLoggingService::writeLog("No ad sets to update", ["id" => $logId, "platform" => AdvertisingPlatform::META->value]);

            return null;
        }

        AdvertisingLoggingService::writeLog(
            "Attaching locations to ad sets",
            [
                "id" => $logId,
                "platform" => AdvertisingPlatform::META->value,
                "automated_campaigns" => $campaignAdSets
                    ->getAccountCampaigns()
                    ->mapWithKeys(function($campaigns, $accountId) {
                        return [(string) $accountId => array_map('strval', $campaigns->keys()->toArray())];
                    })
                    ->toArray()
            ]
        );

        $separator = self::TYPE_KEY_SEPARATOR;
        /**
         * @var int $campaignId
         * @var \App\DataModels\Advertising\Campaign[] $adSets
         */
        foreach($campaignAdSets as $campaignId => &$adSets) {
            $metaLocationKeys = $this->metaAdsLocationsService->fetchCampaignLocationsFromPlatformAPI($campaignId, $adSets->keys()->toArray());
            $locationKeysByType = $metaLocationKeys[AdvertisingLocationsServiceContract::FUNC_RETURN_PLATFORM_LOCATION_IDS];
            $adSetLocationKeys = $metaLocationKeys[AdvertisingLocationsServiceContract::FUNC_RETURN_CAMPAIGN_LOCATIONS];
            $miscData = $metaLocationKeys[AdvertisingLocationsServiceContract::FUNC_RETURN_MISC_TARGETING_DATA];

            $keyLocationIds = MetaAdsLocation::query()
                                ->where(function ($where) use ($locationKeysByType) {
                                    $where
                                        ->where(function ($subWhere) use ($locationKeysByType) {
                                            $subWhere
                                                ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_REGION)
                                                ->whereIn(MetaAdsLocation::FIELD_KEY, $locationKeysByType[TargetingGeoLocationFields::REGIONS]);
                                        })
                                        ->orWhere(function ($subWhere) use ($locationKeysByType) {
                                            $subWhere
                                                ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_MEDIUM_GEO_AREA)
                                                ->whereIn(MetaAdsLocation::FIELD_KEY, array_merge($locationKeysByType[TargetingGeoLocationFields::MEDIUM_GEO_AREAS], $locationKeysByType[TargetingGeoLocationFields::CITIES]));
                                        })
                                        ->orWhere(function ($subWhere) use ($locationKeysByType) {
                                            $subWhere
                                                ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_CITY)
                                                ->whereIn(MetaAdsLocation::FIELD_KEY, array_merge($locationKeysByType[TargetingGeoLocationFields::MEDIUM_GEO_AREAS], $locationKeysByType[TargetingGeoLocationFields::CITIES]));
                                        })
                                        ->orWhere(function ($subWhere) use ($locationKeysByType) {
                                            $subWhere
                                                ->where(MetaAdsLocation::FIELD_TYPE, MetaAdsLocation::TYPE_ZIP)
                                                ->whereIn(MetaAdsLocation::FIELD_KEY, $locationKeysByType[TargetingGeoLocationFields::ZIPS]);
                                        });
                                })
                                ->select([
                                    MetaAdsLocation::FIELD_KEY,
                                    MetaAdsLocation::FIELD_LOCATION_ID,
                                    MetaAdsLocation::FIELD_TYPE
                                ])
                                ->get()
                                ->groupBy(MetaAdsLocation::FIELD_TYPE)
                                ->mapWithKeys(function ($locations, $type) {
                                    $typeKey = MetaAdsLocationsService::SINGULAR_PLURAL_LOCATION_TYPES[$type];

                                    return [
                                        $typeKey => $locations->pluck(MetaAdsLocation::FIELD_KEY, MetaAdsLocation::FIELD_LOCATION_ID)->toArray()
                                    ];
                                })
                                ->toArray();

            /**
             * @var int $adSetId
             * @var \App\DataModels\Advertising\Campaign $adSet
             */
            foreach($adSets as $adSetId => &$adSet) {
                $adSetLocations = [];

                foreach($adSetLocationKeys[$adSetId] as $locationType => $metaAdSetLocationKeys) {
                    $referenceLocationKeys = in_array($locationType, [TargetingGeoLocationFields::CITIES, TargetingGeoLocationFields::MEDIUM_GEO_AREAS], true)
                                            ? array_keys($adSetLocationKeys[$adSetId][TargetingGeoLocationFields::CITIES] + $adSetLocationKeys[$adSetId][TargetingGeoLocationFields::MEDIUM_GEO_AREAS])
                                            : array_keys($metaAdSetLocationKeys);

                    $locationIdMetaKeys = array_intersect(
                        $keyLocationIds[$locationType] ?? [],
                        $referenceLocationKeys
                    );

                    foreach($locationIdMetaKeys as $locationId => &$metaKey) {
                        $metaKey = "{$locationType}{$separator}{$metaKey}";
                    }

                    $adSetLocations = array_merge($adSetLocations, array_flip($locationIdMetaKeys));
                }

                $adSet->setLocations($adSetLocations);
                $adSet->setAdditionalTargetingParameters($miscData[$adSetId]);
            }
        }

        return $campaignAdSets;
    }

    /** @inheritDoc */
    public function populateCampaignLocationsToUpdate($accountId, CampaignLocations &$campaignLocations, Collection $campaigns, array $excludeLocations, int $logId): CampaignLocations
    {
        $locationPlatformIds = $this->metaAdsLocationsService->getPlatformIdsByLocation(collect($excludeLocations)->flatten()->toArray());

        $separator = self::TYPE_KEY_SEPARATOR;
        $missingPlatformLocations = [];
        foreach($excludeLocations as $campaignId => $excludeLocationIds) {
            $missingPlatformLocations[$campaignId] = [];

            foreach($excludeLocationIds as $excludeLocationId) {
                if(!empty($locationPlatformIds[$excludeLocationId])) {
                    $locationType = MetaAdsLocationsService::SINGULAR_PLURAL_LOCATION_TYPES[$locationPlatformIds[$excludeLocationId][MetaAdsLocation::FIELD_TYPE]];

                    $campaignLocations->setCampaignLocation(
                        $accountId,
                        $campaignId,
                        "{$locationType}{$separator}{$locationPlatformIds[$excludeLocationId][MetaAdsLocation::FIELD_KEY]}",
                        true
                    );
                }
                else {
                    $missingPlatformLocations[$campaignId] = $excludeLocationId;
                }
            }

            $includePlatformIds = $campaigns->get($campaignId)->{\App\DataModels\Advertising\Campaign::LOCATIONS}->flip()->except($excludeLocationIds)->values()->toArray();

            foreach($includePlatformIds as $includePlatformId) {
                $campaignLocations->setCampaignLocation($accountId, $campaignId, $includePlatformId, false);
            }
        }

        AdvertisingLoggingService::writeLog(
            "Missing platform locations",
            [
                "id" => $logId,
                "platform" => AdvertisingPlatform::META->value,
                "account_id" => (string) $accountId,
                "missing_locations" => $missingPlatformLocations
            ]
        );

        return $campaignLocations;
    }

    /**
     * @inheritDoc
     */
    public function updateCampaignLocations(int $logId, CampaignLocations $campaignLocations, bool $isRetry = false): bool
    {
        try {
            $results = [];
            $currentTimestamp = Carbon::now()->timestamp;

            $additionalParameters = $campaignLocations->getAdditionalParameters();

            foreach($campaignLocations as $campaignId => $adSets) {
                $batches = [];
                $batchIdx = 0;

                foreach($adSets as $adSetId => $locationTargets) {
                    if(empty($batches[$batchIdx])) {
                        $batches[$batchIdx] = [];
                    }

                    $batches[$batchIdx][$adSetId] = [
                        self::ARRAY_KEY_REQUEST => $this->buildLocationUpdateRequest($campaignId, $adSetId, $locationTargets->toArray(), $additionalParameters[$campaignId][$adSetId], $logId),
                        self::ARRAY_KEY_CAMPAIGN_ID => $campaignId,
                        self::ARRAY_KEY_AD_SET_ID => $adSetId
                    ];

                    $batchIdx++;
                }

                $results[$campaignId] = $this->makeUpdateLocationsCall(
                    $batches,
                    $logId,
                    $campaignId
                );
            }

            $allSuccessfulAdSetIds = [];
            $allFailedAdSetIds = [];
            foreach($results as $campaignId => $adSetResults) {
                $successfulAdSetIds = array_map('strval', array_unique(array_keys(array_filter($adSetResults))));
                $failedAdSetIds = array_map('strval', array_unique(array_keys(array_filter($adSetResults, fn($successful) => !$successful))));

                if(!empty($successfulAdSetIds)) {
                    AdvertisingLoggingService::writeLog(
                        "Updated campaign ad set locations",
                        [
                            "id" => $logId,
                            "platform" => AdvertisingPlatform::META->value,
                            "meta_campaign_id" => (string) $campaignId,
                            "successful_ad_set_ids" => $successfulAdSetIds
                        ]
                    );

                    $allSuccessfulAdSetIds = array_merge($allSuccessfulAdSetIds, $successfulAdSetIds);
                }

                if(!empty($failedAdSetIds)) {
                    $allFailedAdSetIds[$campaignId] = $failedAdSetIds;
                }
            }

            AdvertisingCampaign::query()
                ->whereIn(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, $allSuccessfulAdSetIds)
                ->where(AdvertisingCampaign::FIELD_PLATFORM, AdvertisingPlatform::META->value)
                ->update([
                    AdvertisingCampaign::FIELD_LAST_RUN_TIMESTAMP => $currentTimestamp
                ]);

            $hasFailedSets = !empty($allFailedAdSetIds);

            if(!$isRetry
            && $hasFailedSets) {
                UpdateAdvertisingCampaignLocationsJob::dispatch(AdvertisingPlatform::META->value, $allFailedAdSetIds);

                AdvertisingLoggingService::writeLog(
                    "Ad set locations updated. Retrying failed campaigns.",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::META->value,
                        "failed_campaigns" => $allFailedAdSetIds
                    ]
                );
            }
            else if($hasFailedSets) {
                $failedUpdatesMsg = "";
                foreach($allFailedAdSetIds as $failedCampaignId => $failedAdSetIds) {
                    $failedUpdatesMsg .= "Campaign: {$failedCampaignId} - Ad Sets: ".implode(', ', $failedAdSetIds)."\n";
                }

                AdvertisingLoggingService::sendNotificationEmails(
                    explode(',', config("services.ads.notification_emails")),
                    "SolarReviews: Meta Ad Set Update Failed",
                    "Error updating Meta ad sets. Failed campaigns/ad sets:\n{$failedUpdatesMsg}"
                );

                AdvertisingLoggingService::writeLog(
                    "Ad set locations updated. Some updates failed.",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::META->value,
                        "failed_campaigns" => $allFailedAdSetIds
                    ]
                );
            }
            else {
                AdvertisingLoggingService::writeLog(
                    "Ad set locations updated",
                    [
                        "id" => $logId,
                        "platform" => AdvertisingPlatform::META->value
                    ]
                );
            }

            return !in_array(false, data_get($results, '*.*'), true);
        }
        catch(Throwable $e) {
            if($e instanceof RequestException) {
                $errMsg = self::getRequestExceptionMessage($e->getResponse());
            }
            else {
                $errMsg = $e->getMessage();
            }

            AdvertisingLoggingService::writeLog(
                $errMsg,
                [
                    "id" => $logId,
                    "platform" => AdvertisingPlatform::META->value
                ]
            );

            throw $e;
        }
    }

    /**
     * @param string $campaignId
     * @param string $adSetId
     * @param array $locationTargets
     * @param array $additionalTargets
     * @param int $logId
     * @return array
     */
    #[ArrayShape([self::REQUEST_KEY_METHOD => "string", self::REQUEST_KEY_RELATIVE_URL => "string", self::REQUEST_KEY_BODY => "string"])]
    private function buildLocationUpdateRequest(string $campaignId, string $adSetId, array $locationTargets, array $additionalTargets, int $logId): array
    {
        $separator = self::TYPE_KEY_SEPARATOR;

        AdvertisingLoggingService::writeLog(
            "Building ad set location update operations",
            [
                "id" => $logId,
                "platform" => AdvertisingPlatform::META->value,
                "meta_campaign_id" => $campaignId,
                "ad_set_id" => $adSetId,
                "ad_set_locations" => $locationTargets
            ]
        );

        $geoLocations = [
            TargetingGeoLocationFields::REGIONS => [],
            TargetingGeoLocationFields::MEDIUM_GEO_AREAS => [],
            TargetingGeoLocationFields::CITIES => [],
            TargetingGeoLocationFields::ZIPS => [],
            TargetingGeoLocationFields::COUNTRIES => []
        ];
        $excludedGeoLocations = [
            TargetingGeoLocationFields::REGIONS => [],
            TargetingGeoLocationFields::MEDIUM_GEO_AREAS => [],
            TargetingGeoLocationFields::CITIES => [],
            TargetingGeoLocationFields::ZIPS => []
        ];

        $includedLocationTargets = array_keys(array_filter($locationTargets, fn($excluded) => !$excluded));
        foreach($includedLocationTargets as $locationTypeKey) {
            [$locationType, $metaKey] = explode($separator, $locationTypeKey);

            if(in_array($locationType, [TargetingGeoLocationFields::MEDIUM_GEO_AREAS, TargetingGeoLocationFields::CITIES], true)) {
                $geoLocations[TargetingGeoLocationFields::MEDIUM_GEO_AREAS][] = ["key" => $metaKey];
            }
        }

        $excludedLocationTargets = array_keys(array_filter($locationTargets));
        foreach($excludedLocationTargets as $locationTypeKey) {
            [$locationType, $metaKey] = explode($separator, $locationTypeKey);

            if(in_array($locationType, [TargetingGeoLocationFields::MEDIUM_GEO_AREAS, TargetingGeoLocationFields::CITIES], true)) {
                $excludedGeoLocations[TargetingGeoLocationFields::MEDIUM_GEO_AREAS][] = ["key" => $metaKey];
            }
            else if($locationType === TargetingGeoLocationFields::ZIPS) {
                $excludedGeoLocations[TargetingGeoLocationFields::ZIPS][] = ["key" => $metaKey];
            }
        }

        $data = [
            TargetingFields::GEO_LOCATIONS => $geoLocations,
            TargetingFields::EXCLUDED_GEO_LOCATIONS => $excludedGeoLocations
        ];

        $targetingFields = array_keys((new TargetingFields())->getFieldTypes());

        foreach($additionalTargets as $field => $value) {
            if(in_array($field, $targetingFields, true)) {
                $data[$field] = $value;
            }
        }

        $targeting = (new Targeting())->setData($data)->exportData();

        return [
            self::REQUEST_KEY_METHOD => RequestInterface::METHOD_POST,
            self::REQUEST_KEY_RELATIVE_URL => "v".ApiConfig::APIVersion."/{$adSetId}",
            self::REQUEST_KEY_BODY => AdSetFields::TARGETING."=".urlencode(json_encode($targeting))
        ];
    }

    /**
     * @param array $batches
     * @param int $logId
     * @param string $campaignId
     * @return array
     * @throws Throwable
     */
    private function makeUpdateLocationsCall(
        array $batches,
        int $logId,
        string $campaignId
    ): array
    {
        $this->getClient($campaignId);

        $this->metaAdsClient->getHttpClient()->getAdapter()->getOpts()->offsetSet(CURLOPT_TIMEOUT, 120); //2 minutes

        $results = [];

        foreach($batches as $batchIdx => $requests) {
            try {
                if(count($requests) > 1) {
                    $requests = array_values($requests);

                    $adSetIds = array_map('strval', array_column($requests, self::ARRAY_KEY_AD_SET_ID));

                    AdvertisingLoggingService::writeLog(
                        "Making batch ad set update API call",
                        [
                            "id" => $logId,
                            "platform" => AdvertisingPlatform::META->value,
                            "meta_campaign_ids" => [$campaignId],
                            "ad_set_ids" => $adSetIds
                        ]
                    );

                    $batchRes = $this->metaAdsClient->call(
                        "/",
                        RequestInterface::METHOD_POST,
                        [
                            "batch" => array_column($requests, self::ARRAY_KEY_REQUEST)
                        ]
                    );

                    $resContent = $batchRes->getContent();
                    $resCount = count($resContent);

                    for($i = 0; $i < $resCount; $i++) {
                        $adSetId = $requests[$i][self::ARRAY_KEY_AD_SET_ID];

                        $results[$adSetId] = $resContent[$i]['code'] === 200 && !empty(json_decode($resContent[$i]['body'] ?? '', true)['success']);
                    }

                    AdvertisingLoggingService::writeLog(
                        "Batch ad set update API call finished",
                        [
                            "id" => $logId,
                            "platform" => AdvertisingPlatform::META->value,
                            "meta_campaign_ids" => [$campaignId],
                            "ad_set_ids" => $adSetIds
                        ]
                    );
                }
                else if(count($requests) === 1) {
                    $adSetId = array_keys($requests)[0];

                    $requestBody = $requests[$adSetId][self::ARRAY_KEY_REQUEST][self::REQUEST_KEY_BODY];

                    $targetingData = urldecode(str_replace(AdSetFields::TARGETING."=", '', $requestBody)) ;

                    AdvertisingLoggingService::writeLog(
                        "Making ad set update API call",
                        [
                            "id" => $logId,
                            "platform" => AdvertisingPlatform::META->value,
                            "meta_campaign_id" => $campaignId,
                            "ad_set_id" => (string) $adSetId
                        ]
                    );

                    $adSet = new AdSet($adSetId, null, $this->metaAdsClient);

                    $response = $adSet->updateSelf(
                        [],
                        [
                            AdSetFields::TARGETING => $targetingData
                        ]
                    );

                    $results[$adSetId] = (bool) $response->getData()['success'];

                    AdvertisingLoggingService::writeLog(
                        "Ad set update API call finished",
                        [
                            "id" => $logId,
                            "platform" => AdvertisingPlatform::META->value,
                            "meta_campaign_id" => $campaignId,
                            "ad_set_id" => (string) $adSetId,
                            "success" => $results[$adSetId]
                        ]
                    );
                }
            }
            catch(Throwable $e) {
                if($e instanceof RequestException) {
                    AdvertisingLoggingService::writeLog(
                        self::getRequestExceptionMessage($e->getResponse()),
                        [
                            "id" => $logId,
                            "platform" => AdvertisingPlatform::META->value,
                            "meta_campaign_id" => $campaignId
                        ]
                    );

                    //Mark batch as failed so we can retry it later, and continue with execution
                    foreach($requests as $request) {
                        $results[$request[self::ARRAY_KEY_AD_SET_ID]] = false;
                    }
                }
                else {
                    throw $e;
                }
            }
        }

        return $results;
    }

    /**
     * @param ResponseInterface $response
     * @return string
     */
    public static function getRequestExceptionMessage(ResponseInterface $response): string
    {
        $body = json_decode($response->getBody() ?? '', true);

        if(!empty($body['error'])) {
            $message = $body['error']['message'] ?? '';
            $type = $body['error']['type'] ?? '';
            $code = $body['error']['code'] ?? 0;
            $subCode = $body['error']['error_subcode'] ?? 0;
            $errUserTitle = $body['error']['error_user_title'] ?? '';
            $errUserMsg = $body['error']['error_user_msg'] ?? '';

            $errMsg = "Type: $type. Message: $message. Code: $code - $subCode. User Data: $errUserTitle - $errUserMsg";
        }
        else {
            $errMsg = "Meta Ads API Request encountered an issue";
        }

        return $errMsg;
    }

    /**
     * @param int $industryId
     * @param int|null $instanceId
     * @return bool
     */
    public function updateTieredAdsCampaignLocations(int $industryId, ?int $instanceId = null): bool
    {
        $start = microtime(true);
        $logId = (int) $start;

        $industryModel = Industry::find($industryId);
        $config = $this->tieredAdvertisingService->getConfigModel(AdvertisingPlatform::META->value, $industryModel->{Industry::FIELD_ID}, $instanceId);
        $advertiser = AdvertiserEnum::from($config->{TieredAdvertisingConfiguration::FIELD_ADVERTISER});
        $countyZips = $this->getCountyZips();
        $this->metaAdsClient = $this->metaAdsAuthService->getClient($advertiser);

        if(!App::environment('production')) {
            $this->metaAdsClient->setLogger(new CurlLogger());
        }

        // Get positive counties and negative zip codes
        $campaignCountyAssignments = $this->tieredAdvertisingService->getCampaignCountyAssignments(AdvertisingPlatform::META->value, $industryId, $instanceId);

        foreach ($campaignCountyAssignments as $accountId => $campaignCounties) {
            $cleanedAcctId = preg_replace('/\D/', '', $accountId);

            foreach ($campaignCounties as $campaignId => $countyAssignments) {
                $cleanedCampaignId = preg_replace('/\D/', '', $campaignId);

                $geoLocationsUpdate = [
                    'targeting' => [
                        'geo_locations' => [
                            'zips' => [],
                        ],
                    ],
                ];

                foreach ($countyAssignments as $countyAssignment) {
                    $zipCodes = $countyZips[$countyAssignment->{Location::STATE_ABBREVIATION}][$countyAssignment->{Location::COUNTY}] ?? [];

                    $negativeZipCodes = json_decode($countyAssignment->{TieredAdvertisingCounty::FIELD_NEGATIVE_ZIP_CODES});

                    foreach ($zipCodes as $zipCode) {
                        if (!in_array($zipCode->{Location::ZIP_CODE}, $negativeZipCodes)) {
                            $geoLocationsUpdate['targeting']['geo_locations']['zips'][] = ['key' => 'US:'.$zipCode->{Location::ZIP_CODE}];
                        }
                    }
                }

                try {
                    $response = $this->metaAdsClient->call("/$cleanedCampaignId", RequestInterface::METHOD_POST, $geoLocationsUpdate)->getContent();

                    if (!($response['success'] ?? false)) {
                        throw new Exception("Meta Ads Api returned false.");
                    }
                } catch (Exception $e) {
                    AdvertisingLoggingService::writeLog(
                        "Tiered Advertising failed to update locations for meta account id $accountId, ad set id $cleanedCampaignId. Error: {$e->getMessage()}",
                        [
                            "id" => $logId,
                            "platform" => AdvertisingPlatform::META->value,
                            "success" => false,
                        ]
                    );
                }
            }
        }

        // Update last time location updated field in campaigns
        $this->tieredAdvertisingService->setLocationsUpdatedDate(AdvertisingPlatform::META->value, $industryId, $instanceId);

        return true;
    }

    /**
     * Returns list of zip codes grouped by state and county
     * @return Collection
     */
    public function getCountyZips(): Collection
    {
        return Location::query()
            ->join(DatabaseHelperService::database().'.'.MetaAdsLocation::TABLE, MetaAdsLocation::TABLE.'.'.MetaAdsLocation::FIELD_LOCATION_ID, '=', Location::TABLE.'.'.Location::ID)
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->get([
                Location::TABLE.'.'.Location::ID,
                Location::TABLE.'.'.Location::ZIP_CODE,
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                Location::TABLE.'.'.Location::COUNTY,
            ])->groupBy([Location::STATE_ABBREVIATION, Location::COUNTY]);
    }

    /**
     * Verifies communication with the given account id
     * @param AdvertiserEnum $advertiser
     * @param string $accountId
     * @return ?string
     */
    public function getAccountName(AdvertiserEnum $advertiser, string $accountId): ?string
    {
        $start = microtime(true);
        $logId = (int) $start;

        try {
            $this->metaAdsClient = $this->metaAdsAuthService->getClient($advertiser);
            $res = $this->metaAdsClient->call("/act_$accountId", RequestInterface::METHOD_GET, ['fields' => 'name'])->getContent();

            return $res['name'] ?? null;
        } catch (Exception $e) {
            AdvertisingLoggingService::writeLog(
                "Tiered Advertising failed to retrieve account data for meta account id $accountId. Error: {$e->getMessage()}",
                [
                    "id" => $logId,
                    "platform" => AdvertisingPlatform::META->value,
                    "success" => false,
                ]
            );
        }

        return null;
    }

    /**
     * Verifies communication with the given campaign id using the account id
     * @param AdvertiserEnum $advertiser
     * @param string $accountId
     * @param string $campaignId
     * @return ?string
     */
    public function getCampaignName(AdvertiserEnum $advertiser, string $accountId, string $campaignId): ?string
    {
        $start = microtime(true);
        $logId = (int) $start;

        try {
            $this->metaAdsClient = $this->metaAdsAuthService->getClient($advertiser);
            $res = $this->metaAdsClient->call("/$campaignId", RequestInterface::METHOD_GET, ['fields' => 'name'])->getContent();

            return $res['name'] ?? null;
        } catch (Exception $e) {
            AdvertisingLoggingService::writeLog(
                "Tiered Advertising failed to retrieve saved audiences for meta account id $accountId. Error: {$e->getMessage()}",
                [
                    "id" => $logId,
                    "platform" => AdvertisingPlatform::META->value,
                    "meta_campaign_id" => $campaignId,
                    "success" => false,
                ]
            );
        }

        return null;
    }

    /**
     * Meta does not set the TCPA bid of the campaigns from A2, meta uses audiences to store locations that are not tied to bids
     * @param AdvertiserEnum $advertiser
     * @param string $accountId
     * @param string $campaignId
     * @param float $tcpaBid
     * @return bool
     */
    public function setTcpaBid(AdvertiserEnum $advertiser, string $accountId, string $campaignId, float $tcpaBid): bool
    {
        // Meta does not set the TCPA bid of the campaigns from A2
        return true;
    }
}
