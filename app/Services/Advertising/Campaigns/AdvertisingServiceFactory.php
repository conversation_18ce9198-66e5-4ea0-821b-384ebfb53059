<?php

namespace App\Services\Advertising\Campaigns;

use App\Contracts\Services\Advertising\AdvertisingServiceContract;
use App\Enums\Advertising\AdvertisingPlatform;
use Exception;

class AdvertisingServiceFactory
{
    const PLATFORM_SERVICE = [
        [
            'platform' => AdvertisingPlatform::GOOGLE,
            'service' => GoogleAdsService::class
        ],
        [
            'platform' => AdvertisingPlatform::META,
            'service' => MetaAdsService::class
        ]
    ];

    /**
     * @param string $driver
     * @return AdvertisingServiceContract
     * @throws Exception
     */
    public static function make(string $driver): AdvertisingServiceContract
    {
        return match($driver) {
            AdvertisingPlatform::GOOGLE->value => app(GoogleAdsService::class),
            AdvertisingPlatform::META->value => app(MetaAdsService::class),
            AdvertisingPlatform::MICROSOFT->value => app(MetaAdsService::class),
            default => throw new Exception(__METHOD__.": Invalid driver")
        };
    }
}
