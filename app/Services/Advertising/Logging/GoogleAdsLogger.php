<?php

namespace App\Services\Advertising\Logging;

use App\Services\Advertising\AdvertisingLoggingService;
use Psr\Log\LoggerInterface;
use Psr\Log\LogLevel;

class GoogleAdsLogger implements LoggerInterface
{

    /**
     * @inheritDoc
     */
    #[\Override] public function emergency(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::EMERGENCY;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function alert(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::ALERT;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function critical(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::CRITICAL;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function error(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::ERROR;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function warning(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::WARNING;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function notice(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::NOTICE;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function info(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::INFO;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function debug(\Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = LogLevel::DEBUG;

        $this->writeLog($message, $context);
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function log($level, \Stringable|string $message, array $context = []): void
    {
        $context['severity_level'] = $level;

        $this->writeLog($message, $context);
    }

    private function writeLog(\Stringable|string $message, array $payload = []): void
    {
        AdvertisingLoggingService::writeLog((string) $message, $payload);
    }
}
