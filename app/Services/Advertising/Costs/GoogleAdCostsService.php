<?php

namespace App\Services\Advertising\Costs;

use App\Contracts\Services\Advertising\AdvertisingCostsServiceContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingResolution;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\Industry;
use App\Models\AdvertisingAccount;
use App\Models\DailyAdCostAccount;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\DailyAdCost;
use App\Models\GoogleAdsGeoTarget;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Services\Advertising\Authentication\AdvertisingAuthServiceFactory;
use Carbon\CarbonPeriod;
use Exception;
use Google\Ads\GoogleAds\V19\Resources\CustomerClient;
use Google\Ads\GoogleAds\V19\Services\Client\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V19\Services\GoogleAdsRow;
use Google\Ads\GoogleAds\V19\Services\SearchGoogleAdsStreamRequest;
use Google\ApiCore\ApiException;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class GoogleAdCostsService implements AdvertisingCostsServiceContract
{
    private GoogleAdsServiceClient $googleAdsServiceClient;
    private array $googleGeoTargetMap;
    private array $googleAccountIndustryMap;
    private array $excludedAccounts;
    private array $industrySlugToId;

    const int CONVERT_MICRO_TO_USD = 1_000_000;

    const string EXCLUDE_KEY = 'exclude';

    /**
     * @throws Exception
     */
    public function __construct(
        protected IndustryRepository $industryRepository,
        protected GlobalConfigurationRepository $globalConfigurationRepository,
    ) {
        $this->googleAdsServiceClient = AdvertisingAuthServiceFactory::make(AdvertisingPlatform::GOOGLE->value)->getClient()->getGoogleAdsServiceClient();
        $this->googleGeoTargetMap = [];
        $this->googleAccountIndustryMap = [];
        $this->excludedAccounts = [];
        $this->industrySlugToId = [];
    }

    /**
     * @param CarbonPeriod $period
     * @param array $resolutions
     * @param array $industries
     * @param array $advertisers
     * @return Collection
     * @throws ApiException
     * @throws Exception
     */
    public function retrieveDailyAdCostData(
        CarbonPeriod $period,
        array $resolutions,
        array $industries,
        array $advertisers,
    ): Collection {

        $this->getGoogleGeoAdTargets();
        $this->getIndustryIds();
        $this->initializeGoogleAdAcctIndustryMap();
        $clientAccounts = $this->getAllClientAccounts();

        $dailyAdData = collect([]);
        foreach ($clientAccounts as $clientAccount) {
            foreach ($resolutions as $resolution) {
                $dailyAdData = $dailyAdData->merge($this->googleDailyAdCostQuery($clientAccount, $period, $resolution, $advertisers, $industries));
            }
        }

        return $dailyAdData;
    }

    /**
     * @return array
     * @throws ApiException
     */
    public function getAllClientAccounts(): array
    {
        $clientAccounts = [];

        $accountQuery = "SELECT customer_client.client_customer, customer_client.level, ".
            "customer_client.manager, customer_client.descriptive_name, ".
            "customer_client.currency_code, customer_client.time_zone, ".
            "customer_client.id FROM customer_client";

        try {
            $stream = $this->googleAdsServiceClient->searchStream(SearchGoogleAdsStreamRequest::build(config('services.google.ads.login_customer_id'), $accountQuery));
        } catch (ApiException $ex) {
            logger()->error("Failed to get Google Ads account hierarchy for Google Ads manager account ".config('services.google.ads.login_customer_id').
                ". Error: {$ex->getMessage()}");
            return [];
        }

        /** @var GoogleAdsRow $googleAdsRow */
        foreach ($stream->iterateAllElements() as $googleAdsRow) {
            /** @var CustomerClient $customerClient */
            $customerClient = $googleAdsRow->getCustomerClient();
            if (!$customerClient->getManager())
                $clientAccounts[] = $customerClient;
        }
        return $clientAccounts;
    }

    /**
     * @return void
     */
    public function initializeGoogleAdAcctIndustryMap(): void
    {
        $configMap = $this->globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::GOOGLE_ADS_ACCOUNT_INDUSTRY_MAP)?->data;

        if ($configMap) {
            foreach ($configMap as $account => $industrySlug) {
                if ($industrySlug === self::EXCLUDE_KEY) {
                    $this->excludedAccounts[] = $account;
                } else {
                    try {
                        $this->googleAccountIndustryMap[$account] = Industry::fromSlug($industrySlug);
                    } catch (Exception $e) {
                        // Log error, service will fall back to attempted name mapping for account
                        logger()->error("Invalid industry slug {$industrySlug} mapped to Google Ads account {$account} in global configuration " .
                            GlobalConfigurationKey::GOOGLE_ADS_ACCOUNT_INDUSTRY_MAP->value . ".");
                    }
                }
            }
        } else {
            logger()->warning("No ".GlobalConfigurationKey::GOOGLE_ADS_ACCOUNT_INDUSTRY_MAP->value.
                " defined in Global Configurations Management (account id => industry slug), no google accounts".
                " will be excluded and Advertising Accounts will be used for industry.");
        }
    }

    /**
     * @param CustomerClient $customerClient
     * @param CarbonPeriod $period
     * @param AdvertisingResolution $resolution
     * @param array $advertiserFilter
     * @param array $industryFilter
     * @return Collection
     * @throws Exception
     */
    public function googleDailyAdCostQuery(
        CustomerClient $customerClient,
        CarbonPeriod $period,
        AdvertisingResolution $resolution,
        array $advertiserFilter,
        array $industryFilter,
    ): Collection
    {
        $dailyAdData = collect([]);
        $advertisingAccount = $this->getAdvertisingAccount($customerClient);
        $dailyAdCostAccount = $this->getDailyAdCostAccount($customerClient);
        $industry = $this->getAccountIndustry($customerClient, $dailyAdCostAccount, $advertisingAccount);
        $industryServiceId = null;
        $advertiser = Advertiser::WILL;

        if ($dailyAdCostAccount) {
            $industryServiceId = $dailyAdCostAccount->{DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID};
            $advertiser = $dailyAdCostAccount->advertiser;

            // If account is excluded from automation, return
            if ($dailyAdCostAccount->{DailyAdCostAccount::FIELD_EXCLUDE})
                return collect([]);
        }

        // Only retrieve data if industry is within filter
        if (!in_array($industry, $industryFilter))
            return collect([]);

        // Only retrieve data if advertiser is within filter
        if (!in_array($advertiser, $advertiserFilter))
            return collect([]);

        // Do not retrieve data for excluded accounts
        if (in_array($customerClient->getId(), $this->excludedAccounts))
            return collect([]);

        $query = "SELECT geographic_view.resource_name, geographic_view.location_type, metrics.cost_micros, segments.date, ".
            AdvertisingResolution::getGoogleAdQuerySegment($resolution)." FROM geographic_view WHERE segments.date >= '".
            $period->getStartDate()->format('Y-m-d')."' AND segments.date <= '".$period->getEndDate()->format('Y-m-d')."'";

        try {
            $response = $this->googleAdsServiceClient->searchStream(SearchGoogleAdsStreamRequest::build($customerClient->getId(), $query));
        } catch (ApiException $ex) {
            logger()->error("Failed to get Google Ads daily ad data for account {$customerClient->getDescriptiveName()}: {$customerClient->getId()}".
                " over period {$period->getStartDate()} to {$period->getEndDate()}. Error: {$ex->getMessage()}");
            return collect([]);
        }

        foreach ($response->iterateAllElements() as $googleAdsRow) {
            $newModel = $this->buildAdCostFromGoogleRow($googleAdsRow, $resolution, $industry, $advertiser, $customerClient, $advertisingAccount, $dailyAdCostAccount, $industryServiceId);
            if ($newModel)
                $dailyAdData->push($newModel);
        }
        return $dailyAdData;
    }

    /**
     * @param GoogleAdsRow $googleAdsRow
     * @param AdvertisingResolution $resolution
     * @param Industry $industry
     * @param Advertiser $advertiser
     * @param CustomerClient $customerClient
     * @param AdvertisingAccount|null $advertisingAccount
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @param int|null $industryServiceId
     * @return DailyAdCost|null
     * @throws Exception
     */
    public function buildAdCostFromGoogleRow(
        GoogleAdsRow $googleAdsRow,
        AdvertisingResolution $resolution,
        Industry $industry,
        Advertiser $advertiser,
        CustomerClient $customerClient,
        ?AdvertisingAccount $advertisingAccount,
        ?DailyAdCostAccount $dailyAdCostAccount,
        ?int $industryServiceId,
    ): ?DailyAdCost
    {
        $date = $googleAdsRow->getSegments()->getDate();

        $costMicros = $googleAdsRow->getMetrics()->getCostMicros();
        $cost = $costMicros / self::CONVERT_MICRO_TO_USD; // Convert micros to actual currency

        if ($resolution === AdvertisingResolution::STATE) {
            $locationIdString = $googleAdsRow->getSegments()->getGeoTargetState();
        } else {
            $locationIdString = $googleAdsRow->getSegments()->getGeoTargetCounty();
        }

        // Trim extra string contents off google location
        $googleLocationId = (int)preg_replace('/\D/', '', $locationIdString);

        $locationId = $this->googleGeoTargetMap[$googleLocationId] ?? null;
        if ($locationId === null) {
            logger()->error("Failed to find google ads geo target for location {$googleLocationId}. ".
                "Google Ads Account: {$customerClient->getDescriptiveName()}, Cost: {$cost}, Date: {$date}, Industry: {$industry->value}.");
            return null;
        }

        $data = [
            DailyAdCost::DATA_GOOGLE_ACCOUNT_ID => $customerClient->getId(),
        ];

        return new DailyAdCost([
            DailyAdCost::FIELD_COST                     => $cost,
            DailyAdCost::FIELD_LOCATION_ID              => $locationId,
            DailyAdCost::FIELD_DATE                     => $date,
            DailyAdCost::FIELD_INDUSTRY_ID              => $this->industrySlugToId[$industry->getSlug()],
            DailyAdCost::FIELD_ADVERTISER               => $advertiser->value,
            DailyAdCost::FIELD_PLATFORM                 => AdvertisingPlatform::getInteger(AdvertisingPlatform::GOOGLE->value),
            DailyAdCost::FIELD_DATA                     => json_encode($data),
            DailyAdCost::FIELD_ADVERTISING_ACCOUNT_ID   => $advertisingAccount?->{AdvertisingAccount::FIELD_ID},
            DailyAdCost::FIELD_DAILY_AD_COST_ACCOUNT_ID => $dailyAdCostAccount?->{DailyAdCostAccount::FIELD_ID},
            DailyAdCost::FIELD_INDUSTRY_SERVICE_ID      => $industryServiceId,
        ]);
    }

    /**
     * @param CustomerClient $customerClient
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @param AdvertisingAccount|null $advertisingAccount
     * @return Industry|null
     */
    public function getAccountIndustry(CustomerClient $customerClient, ?DailyAdCostAccount $dailyAdCostAccount, ?AdvertisingAccount $advertisingAccount): ?Industry
    {
        if ($dailyAdCostAccount && $dailyAdCostAccount->{DailyAdCostAccount::FIELD_INDUSTRY_ID}) {
            $industryModel = IndustryModel::find($dailyAdCostAccount->{DailyAdCostAccount::FIELD_INDUSTRY_ID});
            return Industry::fromSlug($industryModel->{IndustryModel::FIELD_SLUG});
        }

        if ($advertisingAccount)
            return Industry::fromSlug(Str::slug($advertisingAccount->{AdvertisingAccount::FIELD_INDUSTRY}));

        if (isset($this->googleAccountIndustryMap[$customerClient->getId()]))
            return $this->googleAccountIndustryMap[$customerClient->getId()];

        return $this->searchIndustryByClientName($customerClient->getDescriptiveName());
    }

    /**
     * @param CustomerClient $customerClient
     * @return AdvertisingAccount|null
     */
    public function getAdvertisingAccount(CustomerClient $customerClient): ?AdvertisingAccount
    {
        return AdvertisingAccount::query()
            ->where(AdvertisingAccount::FIELD_PLATFORM, AdvertisingPlatform::GOOGLE)
            ->where(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $customerClient->getId())
            ->get()
            ->first();
    }

    /**
     * @param CustomerClient $customerClient
     * @return DailyAdCostAccount|null
     */
    public function getDailyAdCostAccount(CustomerClient $customerClient): ?DailyAdCostAccount
    {

        return DailyAdCostAccount::query()
            ->where(DailyAdCostAccount::FIELD_PLATFORM, AdvertisingPlatform::GOOGLE)
            ->where(DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID, (string) $customerClient->getId())
            ->get()
            ->first();
    }

    /**
     * @return void
     * @throws Exception
     */
    public function getGoogleGeoAdTargets(): void
    {
        $googleGeoAdsTargets = GoogleAdsGeoTarget::query()
            ->select(GoogleAdsGeoTarget::FIELD_LOCATION_ID, GoogleAdsGeoTarget::FIELD_CRITERIA_ID)
            ->whereIn(GoogleAdsGeoTarget::FIELD_TARGET_TYPE, [GoogleAdsGeoTarget::TARGET_TYPE_STATE, GoogleAdsGeoTarget::TARGET_TYPE_COUNTY])
            ->get();
        if ($googleGeoAdsTargets->count() === 0) {
            throw new Exception("No google geo ad targets found.");
        }
        $this->googleGeoTargetMap = $googleGeoAdsTargets->pluck(GoogleAdsGeoTarget::FIELD_LOCATION_ID,GoogleAdsGeoTarget::FIELD_CRITERIA_ID)->toArray();
    }

    /**
     * @return void
     */
    public function getIndustryIds(): void
    {
        $this->industrySlugToId = array_flip(IndustryModel::query()
            ->select(IndustryModel::FIELD_SLUG, IndustryModel::FIELD_ID)
            ->get()
            ->pluck(IndustryModel::FIELD_SLUG,IndustryModel::FIELD_ID)
            ->toArray());
    }

    /**
     * @param string $name
     * @return Industry
     */
    public function searchIndustryByClientName(string $name): Industry
    {
        $industrySlugs = Industry::slugsList();
        $name = strtolower($name);
        foreach ($industrySlugs as $slug => $industry) {
            if (str_contains($name, strtolower($industry))) {
                return Industry::fromSlug($slug);
            }
        }
        // Default to solar if no industry contained in name string - warning will bring it to attention that it needs to be mapped
        return Industry::SOLAR;
    }
}
