<?php

namespace App\Services\Advertising\Costs;

use App\Contracts\Services\Advertising\AdvertisingCostsServiceContract;
use App\Enums\Advertising\AdvertisingPlatform;
use Exception;

class AdvertisingCostsServiceFactory
{
    const PLATFORM_SERVICE = [
        [
            'platform'  => AdvertisingPlatform::GOOGLE,
            'service'   => GoogleAdCostsService::class
        ],
         [
             'platform'  => AdvertisingPlatform::MICROSOFT,
             'service'   => MicrosoftAdCostsService::class
         ],
         [
             'platform'  => AdvertisingPlatform::META,
             'service'   => MetaAdCostsService::class
         ],
    ];

    /**
     * @param string $driver
     * @return AdvertisingCostsServiceContract
     * @throws Exception
     */
    public static function make(string $driver): AdvertisingCostsServiceContract
    {
        return match($driver) {
            AdvertisingPlatform::GOOGLE->value => app(GoogleAdCostsService::class),
            AdvertisingPlatform::MICROSOFT->value => app(MicrosoftAdCostsService::class),
            AdvertisingPlatform::META->value => app(MetaAdCostsService::class),
            default => throw new Exception(__METHOD__.": Invalid driver")
        };
    }
}
