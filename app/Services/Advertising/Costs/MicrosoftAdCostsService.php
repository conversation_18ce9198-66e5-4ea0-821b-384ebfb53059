<?php

namespace App\Services\Advertising\Costs;

use App\Contracts\Services\Advertising\AdvertisingCostsServiceContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingResolution;
use App\Enums\Odin\Industry;
use App\Models\AdvertisingAccount;
use App\Models\DailyAdCost;
use App\Models\DailyAdCostAccount;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry as IndustryModel;
use App\Repositories\LocationRepository;
use App\Services\Advertising\Authentication\AdvertisingAuthServiceFactory;
use App\Services\Advertising\Authentication\MicrosoftAdsAuthService;
use App\Services\FileUploadHelperService;
use Carbon\CarbonPeriod;
use Exception;
use Google\Cloud\Core\Exception\BadRequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Str;
use Microsoft\BingAds\Auth\ApiEnvironment;
use Microsoft\BingAds\Auth\AuthorizationData;
use Microsoft\BingAds\Auth\ServiceClient;
use Microsoft\BingAds\Auth\ServiceClientType;
use Microsoft\BingAds\V13\CustomerManagement\Paging;
use Microsoft\BingAds\V13\CustomerManagement\Predicate;
use Microsoft\BingAds\V13\CustomerManagement\PredicateOperator;
use Microsoft\BingAds\V13\CustomerManagement\SearchAccountsRequest;
use Microsoft\BingAds\V13\Reporting\AccountThroughCampaignReportScope;
use Microsoft\BingAds\V13\Reporting\Date;
use Microsoft\BingAds\V13\Reporting\GeographicPerformanceReportColumn;
use Microsoft\BingAds\V13\Reporting\GeographicPerformanceReportRequest;
use Microsoft\BingAds\V13\Reporting\PollGenerateReportRequest;
use Microsoft\BingAds\V13\Reporting\ReportAggregation;
use Microsoft\BingAds\V13\Reporting\ReportFormat;
use Microsoft\BingAds\V13\Reporting\ReportRequestStatusType;
use Microsoft\BingAds\V13\Reporting\ReportTime;
use Microsoft\BingAds\V13\Reporting\SubmitGenerateReportRequest;
use Illuminate\Support\Facades\File;
use SoapVar;
use ZipArchive;

class MicrosoftAdCostsService implements AdvertisingCostsServiceContract
{
    private array $industrySlugToId;
    private AuthorizationData $microsoftAdsClient;
    private Collection $stateCollection;
    private Collection $countyCollection;
    private array $costShareRows;

    const string STATE_COLUMN   = "State";
    const string COUNTY_COLUMN  = "County";

    const string DATE_COLUMN    = "TimePeriod";
    const string COST_COLUMN    = "Spend";

    const int REPORT_MAX_WAIT_TIME_SECONDS  = 600;
    const int REPORT_POLL_INTERVAL_SECONDS  = 5;
    const int MAX_CSV_ROW_LENGTH            = 1000;

    const array COLUMN_HEADERS = [
        self::STATE_COLUMN,
        self::COUNTY_COLUMN,
        self::DATE_COLUMN,
        self::COST_COLUMN,
    ];

    /**
     * @throws Exception
     */
    public function __construct(
        protected LocationRepository $locationRepository,
    ) {
        /** @var MicrosoftAdsAuthService $authService */
        $authService = AdvertisingAuthServiceFactory::make(AdvertisingPlatform::MICROSOFT->value);
        $client = $authService->getClient();
        $authService->attachOAuthTokensToClient($client);
        $this->microsoftAdsClient = $client;
        $this->industrySlugToId = [];
        $this->costShareRows = [];
    }

    /**
     * @param CarbonPeriod $period
     * @param array $resolutions
     * @param array $industries
     * @param array $advertisers
     * @return Collection
     * @throws Exception
     */
    public function retrieveDailyAdCostData(
        CarbonPeriod $period,
        array $resolutions,
        array $industries,
        array $advertisers,
    ): Collection {
        $advertiser = Advertiser::GABE;
        $this->getIndustryIds();
        $this->getLocations();
        $accounts = $this->getAdvertiserAccounts();

        $dailyAdData = collect([]);
        foreach ($accounts as $account) {
            foreach ($resolutions as $resolution) {
                $dailyAdData = $dailyAdData->merge($this->getMicrosoftDailyAdCosts($account, $period, $resolution, $advertiser, $industries, $advertisers));
            }
        }

        return $dailyAdData;
    }

    /**
     * @return array
     * @throws Exception
     */
    public function getAdvertiserAccounts(): array
    {
        $serviceClient = new ServiceClient(
            ServiceClientType::CustomerManagementVersion13,
            $this->microsoftAdsClient,
            ApiEnvironment::Production,
        );

        // Build account search request
        $accountRequest = new SearchAccountsRequest();
        $accountRequest->Ordering = "Descending";
        $accountRequest->PageInfo = new Paging();
        $accountRequest->PageInfo->Index = 0;
        $accountRequest->PageInfo->Size = 100;

        // Create a Predicate for filtering - get all of our accounts with our customer id
        $predicate = new Predicate();
        $predicate->Field = "CustomerId";
        $predicate->Operator = PredicateOperator::Equals;
        $predicate->Value = config('services.microsoft.ads.customer_id');
        $accountRequest->Predicates = [$predicate];

        // Submit the account search request
        $response = $serviceClient->GetService()->SearchAccounts($accountRequest);

        return $response->Accounts->AdvertiserAccount;
    }

    /**
     * @param mixed $account
     * @param CarbonPeriod $period
     * @param AdvertisingResolution $resolution
     * @param Advertiser $advertiser
     * @param array $industriesFilter
     * @param array $advertiserFilter
     * @return Collection
     * @throws BadRequestException
     */
    public function getMicrosoftDailyAdCosts(
        mixed $account,
        CarbonPeriod $period,
        AdvertisingResolution $resolution,
        Advertiser $advertiser,
        array $industriesFilter,
        array $advertiserFilter,
    ): Collection
    {
        // Get associated advertising account model and industry
        $advertisingAccount = $this->getAdvertisingAccount($account);
        $dailyAdCostAccount = $this->getDailyAdCostAccount($account);
        $industry = $this->getAccountIndustry($account, $dailyAdCostAccount, $advertisingAccount);
        $industryServiceId = null;

        if ($dailyAdCostAccount) {
            $advertiser = $dailyAdCostAccount->advertiser;
            $industryServiceId = $dailyAdCostAccount->industry_service_id;

            // If account is excluded from automation, return
            if ($dailyAdCostAccount->{DailyAdCostAccount::FIELD_EXCLUDE})
                return collect([]);
        }

        // Only retrieve data if account industry is within filter
        if (!in_array($industry, $industriesFilter))
            return collect([]);

        // Only retrieve data if advertiser is within filter
        if (!in_array($advertiser, $advertiserFilter))
            return collect([]);

        // Generate daily ad spend report
        $reportDownloadUrl = $this->createApiReport($account, $period, $resolution);

        // Paused accounts return null for report link on days with no data
        if (!$reportDownloadUrl)
            return collect([]);

        // Download and extract report
        $extractedFilePath = $this->downloadApiReport($reportDownloadUrl);

        // Parse CSV into daily ad cost objects
        return $this->parseReportCsv($extractedFilePath, $resolution, $industry, $advertiser, $account, $advertisingAccount, $industryServiceId, $dailyAdCostAccount);
    }

    /**
     * @param mixed $account
     * @return AdvertisingAccount|null
     */
    public function getAdvertisingAccount(mixed $account): ?AdvertisingAccount
    {
        // Get Ads Account for Industry mapping
        return AdvertisingAccount::query()
            ->where(AdvertisingAccount::FIELD_PLATFORM, AdvertisingPlatform::MICROSOFT)
            ->where(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID, $account->Id)
            ->get()
            ->first();
    }

    /**
     * @param mixed $account
     * @return DailyAdCostAccount|null
     */
    public function getDailyAdCostAccount(mixed $account): ?DailyAdCostAccount
    {
        return DailyAdCostAccount::query()
            ->where(DailyAdCostAccount::FIELD_PLATFORM, AdvertisingPlatform::MICROSOFT)
            ->where(DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID, $account->Id)
            ->get()
            ->first();
    }

    /**
     * @param mixed $account
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @param AdvertisingAccount|null $adAccountModel
     * @return Industry
     */
    public function getAccountIndustry(mixed $account, ?DailyAdCostAccount $dailyAdCostAccount, ?AdvertisingAccount $adAccountModel): Industry
    {
        if ($dailyAdCostAccount && $dailyAdCostAccount->{DailyAdCostAccount::FIELD_INDUSTRY_ID}) {
            $industryModel = IndustryModel::find($dailyAdCostAccount->{DailyAdCostAccount::FIELD_INDUSTRY_ID});
            return Industry::fromSlug($industryModel->{IndustryModel::FIELD_SLUG});
        }

        if ($adAccountModel)
            return Industry::fromSlug(Str::slug($adAccountModel->{AdvertisingAccount::FIELD_INDUSTRY}));

        return $this->searchIndustryByAccountName($account->Name);
    }

    /**
     * Send a report creation request to microsoft ads api, wait for creation, return download link
     * @param mixed $account
     * @param CarbonPeriod $period
     * @param AdvertisingResolution $resolution
     * @return string|null
     * @throws Exception
     */
    public function createApiReport(
        mixed $account,
        CarbonPeriod $period,
        AdvertisingResolution $resolution,
    ): ?string
    {
        // Create report creation service client
        $serviceClient = new ServiceClient(
            ServiceClientType::ReportingVersion13,
            $this->microsoftAdsClient,
            ApiEnvironment::Production,
        );

        // Create report request, daily spend report by state or county
        $report = new GeographicPerformanceReportRequest();
        $reportName = 'AdSpendByDay_'
            .$account->Name.'_'
            .$period->getStartDate()->format('Y-m-d').'_to_'.$period->getEndDate()->format('Y-m-d')
            .'_'.$resolution->value;
        $report->ExcludeReportHeader = true;
        $report->ExcludeReportFooter = true;
        $report->ReportName = $reportName;
        $report->Aggregation = ReportAggregation::Daily;
        $report->ReturnOnlyCompleteData = true;
        $report->Columns = [
            GeographicPerformanceReportColumn::State,
            GeographicPerformanceReportColumn::TimePeriod,
            GeographicPerformanceReportColumn::Spend,
        ];
        if ($resolution === AdvertisingResolution::COUNTY)
            $report->Columns[] = GeographicPerformanceReportColumn::County;

        $report->Scope = new AccountThroughCampaignReportScope();
        $report->Scope->AccountIds = array($account->Id);
        $report->Time = new ReportTime();
        $report->Time->PredefinedTime = null;
        $report->Time->CustomDateRangeStart = new Date();
        $report->Time->CustomDateRangeStart->Month = $period->getStartDate()->month;
        $report->Time->CustomDateRangeStart->Day = $period->getStartDate()->day;
        $report->Time->CustomDateRangeStart->Year = $period->getStartDate()->year;
        $report->Time->CustomDateRangeEnd = new Date();
        $report->Time->CustomDateRangeEnd->Month = $period->getEndDate()->month;
        $report->Time->CustomDateRangeEnd->Day = $period->getEndDate()->day;
        $report->Time->CustomDateRangeEnd->Year = $period->getEndDate()->year;
        $report->Format = ReportFormat::Csv;

        // Encode report request before sending
        $encodedReport = new SoapVar(
            $report,
            SOAP_ENC_OBJECT,
            'GeographicPerformanceReportRequest',
            $serviceClient->GetNamespace()
        );

        // Submit report request
        $request = new SubmitGenerateReportRequest();
        $request->ReportRequest = $encodedReport;
        $reportRequestResponse = $serviceClient->getService()->SubmitGenerateReport($request);

        $reportDownloadUrl = null;

        // Poll for the report until ready
        $maxAttempts = self::REPORT_MAX_WAIT_TIME_SECONDS / self::REPORT_POLL_INTERVAL_SECONDS;

        for ($attempt = 0; $attempt < $maxAttempts; $attempt++) {
            sleep(self::REPORT_POLL_INTERVAL_SECONDS);
            $pollRequest = new PollGenerateReportRequest();
            $pollRequest->ReportRequestId = $reportRequestResponse->ReportRequestId;
            $reportRequestStatus = $serviceClient->getService()->PollGenerateReport($pollRequest);

            if ($reportRequestStatus->ReportRequestStatus->Status === ReportRequestStatusType::Success) {
                $reportDownloadUrl = $reportRequestStatus->ReportRequestStatus->ReportDownloadUrl;
                break;
            } else if ($reportRequestStatus->ReportRequestStatus === ReportRequestStatusType::Error) {
                throw new Exception("Microsoft Ads API Daily Cost report failed to generate.");
            }
        }

        // Check for timeout
        if ($attempt === $maxAttempts) {
            throw new Exception("Timed out after ".self::REPORT_MAX_WAIT_TIME_SECONDS.
                " seconds waiting for Microsoft Ads API Daily Cost report generation.");
        }

        return $reportDownloadUrl;
    }

    /**
     * @param string $reportDownloadUrl
     * @return string
     * @throws BadRequestException
     * @throws Exception
     */
    public function downloadApiReport(string $reportDownloadUrl): string
    {
        // Download zipped report from microsoft
        $tempFile = FileUploadHelperService::createFromUrl(
            url            : $reportDownloadUrl,
            throwException : false,
        );

        // Get temporary file path for downloaded zip and report extract destination
        $tempPath = $tempFile->getRealPath();
        $extractPath = sys_get_temp_dir();

        // Unzip
        $zip = new ZipArchive;
        if (!$zip->open($tempPath))
            throw new Exception("Failed to unzip csv from Microsoft Ads Api.");

        // Extract downloaded report
        $zip->extractTo($extractPath);

        // Get extracted file name and close zip archive object
        $extractedFileName = $zip->getNameIndex(0); // Only one report created
        $zip->close();

        // Delete zip after extracting
        File::delete($tempPath);

        return $extractPath . '/' . $extractedFileName;
    }

    /**
     * @param string $extractedFilePath
     * @param AdvertisingResolution $resolution
     * @param Industry $industry
     * @param Advertiser $advertiser
     * @param mixed $account
     * @param AdvertisingAccount|null $advertisingAccount
     * @param int|null $industryServiceId
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @return Collection
     * @throws Exception
     */
    public function parseReportCsv(
        string $extractedFilePath,
        AdvertisingResolution $resolution,
        Industry $industry,
        Advertiser $advertiser,
        mixed $account,
        ?AdvertisingAccount $advertisingAccount,
        ?int $industryServiceId,
        ?DailyAdCostAccount $dailyAdCostAccount,
    ): Collection
    {
        // Open report CSV
        if (($handle = fopen($extractedFilePath, "r")) === false) {
            throw new Exception("Error opening CSV file");
        }

        // Get column index of each column name (name => index)
        $header = fgetcsv($handle, self::MAX_CSV_ROW_LENGTH);
        $columnMap = $this->getCsvColumnIndexMap($header);

        $adCostData = collect([]);

        // Create daily ad cost objects from CSV entries
        while (($row = fgetcsv($handle, self::MAX_CSV_ROW_LENGTH)) !== FALSE) {
            // Process each row of CSV data
            $newModel = $this->buildAdCostFromMicrosoftRow($row, $columnMap, $resolution, $industry, $advertiser, $account, $advertisingAccount, $industryServiceId, $dailyAdCostAccount);
            if ($newModel)
                $adCostData->push($newModel);
        }
        fclose($handle);

        // Delete temporary extracted CSV
        File::delete($extractedFilePath);

        // Handle cost share rows (rows with empty state or county - microsoft's unmapped cost)
        return $adCostData->merge($this->handleCostShareRows($adCostData, $columnMap, $account, $resolution, $industryServiceId, $dailyAdCostAccount));
    }

    /**
     * @param array $row
     * @param array $columnMap
     * @param AdvertisingResolution $resolution
     * @param Industry $industry
     * @param Advertiser $advertiser
     * @param mixed $account
     * @param AdvertisingAccount|null $advertisingAccount
     * @param int|null $industryServiceId
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @return DailyAdCost|null
     * @throws Exception
     */
    public function buildAdCostFromMicrosoftRow(
        array $row,
        array $columnMap,
        AdvertisingResolution $resolution,
        Industry $industry,
        Advertiser $advertiser,
        mixed $account,
        ?AdvertisingAccount $advertisingAccount,
        ?int $industryServiceId,
        ?DailyAdCostAccount $dailyAdCostAccount,
    ): ?DailyAdCost
    {
        $date = $row[$columnMap[self::DATE_COLUMN]];
        $cost = $row[$columnMap[self::COST_COLUMN]];

        // Only create daily ad cost entries if there is cost
        if (floatval($cost) === 0.0)
            return null;

        $stateString = $row[$columnMap[self::STATE_COLUMN]];
        if ($stateString === "") {
            // If state resolution, add to cost share rows. After other rows are created this cost will be distributed across all states.
            if ($resolution === AdvertisingResolution::STATE)
                $this->costShareRows[] = $row;
            return null;
        }

        if ($resolution === AdvertisingResolution::COUNTY) {
            $countyString = $row[$columnMap[self::COUNTY_COLUMN]];
            $countyString = $this->cleanCountyNameString($countyString, $stateString);
            if ($countyString === "") {
                // Add to cost share rows, cost will be distributed across counties in the state
                $this->costShareRows[] = $row;
                return null;
            }
        } else {
            $countyString = "";
        }

        // Get location (county or state)
        $location = $this->getLocationFromNames(Str::slug($stateString), Str::slug($countyString), $resolution);
        if (!$location) {
            logger()->error("Failed to find database location for State: $stateString, County: $countyString, Slug: ".Str::slug($countyString).
                " from Microsoft Ads API locations.");
            return null;
        }

        // Create daily ad cost object
        $data = [
            DailyAdCost::DATA_MICROSOFT_ACCOUNT_ID => $account->Id,
        ];

        return new DailyAdCost([
            DailyAdCost::FIELD_COST                     => $cost,
            DailyAdCost::FIELD_LOCATION_ID              => $location->{Location::ID},
            DailyAdCost::FIELD_DATE                     => $date,
            DailyAdCost::FIELD_INDUSTRY_ID              => $this->industrySlugToId[$industry->getSlug()],
            DailyAdCost::FIELD_ADVERTISER               => $advertiser->value,
            DailyAdCost::FIELD_PLATFORM                 => AdvertisingPlatform::getInteger(AdvertisingPlatform::MICROSOFT->value),
            DailyAdCost::FIELD_DATA                     => json_encode($data),
            DailyAdCost::FIELD_ADVERTISING_ACCOUNT_ID   => $advertisingAccount?->{AdvertisingAccount::FIELD_ID},
            DailyAdCost::FIELD_INDUSTRY_SERVICE_ID      => $industryServiceId,
            DailyAdCost::FIELD_DAILY_AD_COST_ACCOUNT_ID => $dailyAdCostAccount?->id ?? null,
        ]);
    }

    /**
     * This function takes mapped ad cost data from this report and attributes cost share data across
     * @param Collection $adCostData
     * @param array $columnMap
     * @param mixed $account
     * @param AdvertisingResolution $resolution
     * @param int|null $industryServiceId
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @return Collection
     */
    public function handleCostShareRows(
        Collection $adCostData,
        array $columnMap,
        mixed $account,
        AdvertisingResolution $resolution,
        ?int $industryServiceId,
        ?DailyAdCostAccount $dailyAdCostAccount,
    ): Collection
    {
        $sharedAdCostData = collect([]);

        // Iterate through costShareRows
        foreach ($this->costShareRows as $costShareRow) {
            $state = $costShareRow[$columnMap[self::STATE_COLUMN]];
            $date = $costShareRow[$columnMap[self::DATE_COLUMN]];
            $shareCost = floatval($costShareRow[$columnMap[self::COST_COLUMN]]);

            // calculate portions by location for the row's day
            $shareGroup = $adCostData->where(DailyAdCost::FIELD_DATE, $date);
            // Filter by state for county resolution
            if ($resolution === AdvertisingResolution::COUNTY && $state !== '') {
                $locationsGroup = $this->countyCollection->where(Location::STATE, $state);
                // Filter group to only locations within the same state as the row
                $shareGroup = $shareGroup->filter(function ($adCost) use ($locationsGroup) {
                    return $locationsGroup->contains(Location::ID, $adCost->{DailyAdCost::FIELD_LOCATION_ID});
                });
            }

            // Only distribute cost across locations with higher than average values
            $costAverage = $shareGroup->average(DailyAdCost::FIELD_COST);
            $shareGroup = $shareGroup->filter(function ($adCost) use ($costAverage) {
                return $adCost->{DailyAdCost::FIELD_COST} > $costAverage;
            });

            // Sum of share group cost as dividend
            $costDividend = $shareGroup->sum(DailyAdCost::FIELD_COST);

            // Designate as cost share in data column
            $data = [
                DailyAdCost::DATA_MICROSOFT_ACCOUNT_ID  => $account->Id,
                DailyAdCost::DATA_COST_SHARE            => true,
            ];

            // Distribute unmapped cost across locations in report
            foreach ($shareGroup as $shareLocation) {
                $shareCostPercentage = $shareLocation[DailyAdCost::FIELD_COST] / $costDividend;
                $cost = ($shareCostPercentage * $shareCost);
                if ($cost > 0.01) {
                    $cost = round($cost, 2);
                    $sharedAdCostData->push(new DailyAdCost([
                        DailyAdCost::FIELD_COST => $cost,
                        DailyAdCost::FIELD_LOCATION_ID => $shareLocation->{DailyAdCost::FIELD_LOCATION_ID},
                        DailyAdCost::FIELD_DATE => $date,
                        DailyAdCost::FIELD_INDUSTRY_ID => $shareLocation->{DailyAdCost::FIELD_INDUSTRY_ID},
                        DailyAdCost::FIELD_ADVERTISER => $shareLocation->{DailyAdCost::FIELD_ADVERTISER},
                        DailyAdCost::FIELD_PLATFORM => $shareLocation->{DailyAdCost::FIELD_PLATFORM},
                        DailyAdCost::FIELD_DATA => json_encode($data),
                        DailyAdCost::FIELD_ADVERTISING_ACCOUNT_ID => $shareLocation->{DailyAdCost::FIELD_ADVERTISING_ACCOUNT_ID},
                        DailyAdCost::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId,
                        DailyAdCost::FIELD_DAILY_AD_COST_ACCOUNT_ID => $dailyAdCostAccount?->id ?? null,
                    ]));
                }
            }
        }
        // Clear cost share rows
        $this->costShareRows = [];
        return $sharedAdCostData;
    }

    /**
     * Returns key => value array with Column Name => Index in CSV line
     * @param array $header
     * @return array
     */
    public function getCsvColumnIndexMap(array $header): array
    {
        $columnMap = [];
        $index = 0;
        foreach ($header as $columnName) {
            foreach (self::COLUMN_HEADERS as $columnNameSearch) {
                if (strpos($columnName, $columnNameSearch) || $columnName === $columnNameSearch) {
                    $columnMap[$columnNameSearch] = $index;
                }
            }
            $index++;
        }
        return $columnMap;
    }

    /**
     * Initialize Industries before parsing
     * @return void
     */
    public function getIndustryIds(): void
    {
        $this->industrySlugToId = array_flip(IndustryModel::query()
            ->select(IndustryModel::FIELD_SLUG, IndustryModel::FIELD_ID)
            ->get()
            ->pluck(IndustryModel::FIELD_SLUG,IndustryModel::FIELD_ID)
            ->toArray());
    }

    /**
     * Initialize Locations before parsing
     * @return void
     */
    public function getLocations(): void
    {
        $this->stateCollection = Location::query()
            ->select(Location::ID, Location::STATE, Location::STATE_KEY)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get();
        $this->countyCollection = Location::query()
            ->select(Location::ID, Location::STATE, Location::STATE_KEY, Location::COUNTY, Location::COUNTY_KEY)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->get();
    }

    /**
     * Resolving naming convention differences between microsoft county names and our county names
     * @param string $countyString
     * @param string $stateString
     * @return string
     */
    public function cleanCountyNameString(string $countyString, string $stateString): string
    {
        $countyString = str_replace(" County", "", $countyString);
        $countyString = str_replace(" Parish", "", $countyString); // Louisiana calls its counties parishes
        $countyString = str_replace("St.", "Saint", $countyString);
        $countyString = str_replace("De ", "De", $countyString);
        if ($stateString === 'Indiana' && $countyString === 'Saint Joseph')
            $countyString = 'St Joseph';
        if ($stateString === 'Texas' && $countyString === 'DeWitt')
            $countyString = 'De Witt';
        if ($stateString === 'New Mexico' && $countyString === 'DeBaca')
            $countyString = 'De Baca';
        if ($stateString === 'Missouri' && $countyString === 'Ste. Genevieve')
            $countyString = 'Sainte Genevieve';

        return $countyString;
    }

    /**
     * @param string $state
     * @param string $county
     * @param AdvertisingResolution $resolution
     * @return Location|null
     */
    public function getLocationFromNames(string $state, string $county, AdvertisingResolution $resolution): ?Location
    {
        if ($resolution === AdvertisingResolution::STATE) {
            return $this->stateCollection->first(function ($location) use ($state) {
                return $location[Location::STATE_KEY] === $state;
            });
        } else {
            return $this->countyCollection->first(function ($location) use ($state, $county) {
                return ($location[Location::STATE_KEY] === $state && $location[Location::COUNTY_KEY] === $county);
            });
        }
    }

    /**
     * @param string $name
     * @return Industry
     */
    public function searchIndustryByAccountName(string $name): Industry
    {
        $industrySlugs = Industry::slugsList();
        $name = strtolower($name);
        foreach ($industrySlugs as $slug => $industry) {
            if (str_contains($name, strtolower($industry))) {
                return Industry::fromSlug($slug);
            }
        }
        // Default to solar if no industry contained in name string
        return Industry::SOLAR;
    }
}
