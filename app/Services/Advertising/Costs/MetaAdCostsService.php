<?php

namespace App\Services\Advertising\Costs;

use App\Contracts\Services\Advertising\AdvertisingCostsServiceContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingResolution;
use App\Enums\Odin\Industry;
use App\Models\AdvertisingAccount;
use App\Models\DailyAdCost;
use App\Models\DailyAdCostAccount;
use App\Models\Dma;
use App\Models\DmaLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry as IndustryModel;
use App\Repositories\Odin\IndustryRepository;
use App\Services\Advertising\Authentication\MetaAdsAuthService;
use App\Services\Advertising\Campaigns\MetaAdsService;
use App\Services\DatabaseHelperService;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Exception;
use FacebookAds\Api;
use FacebookAds\Cursor;
use FacebookAds\Object\AdAccount;
use FacebookAds\Object\AdsInsights;
use FacebookAds\Object\Fields\AdsInsightsFields;
use Google\Service\MyBusinessVerifications\AddressVerificationData;
use Illuminate\Support\Collection;
use App\Models\Advertiser as AdvertiserModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class MetaAdCostsService implements AdvertisingCostsServiceContract
{
    private Api $metaAdsClient;

    // Industry Slug => industry model id
    private array $industrySlugToId;

    // Collection of State location models
    private Collection $stateCollection;

    // Meta DMA Key => Collection of county location ids
    private array $dmaLocations;

    // County Location ID => County Weight for Ad Cost
    private array $countyWeights;

    // Meta Advertising campaigns from db
    private Collection $metaCampaigns;

    const string REGION     = 'region';
    const string DMA        = 'dma';
    const string SPEND      = 'spend';
    const string DATE       = 'date_start';
    const string UNKNOWN    = 'Unknown';

    /**
     * @param IndustryRepository $industryRepository
     * @param MetaAdsAuthService $metaAdsAuthService
     */
    public function __construct(
        protected IndustryRepository $industryRepository,
        protected MetaAdsAuthService $metaAdsAuthService,
    ) {
        $this->industrySlugToId = [];
        $this->dmaLocations = [];
        $this->countyWeights = [];
    }

    /**
     * @param CarbonPeriod $period
     * @param array $resolutions
     * @param array $industries
     * @param array $advertisers
     * @return Collection
     * @throws Throwable
     */
    public function retrieveDailyAdCostData(
        CarbonPeriod $period,
        array $resolutions,
        array $industries,
        array $advertisers,
    ): Collection {
        $dailyAdData = collect([]);

        $this->getIndustryIds(); // industry slug -> id
        $this->getLocations(); // meta dma key -> location ids,
        $this->getMetaCountyWeights($period); // county -> county weight
        $advertiserModels = $this->getAdvertisers($advertisers); // available advertiser models
        $this->metaCampaigns = $this->getMetaCampaigns(); // meta campaign models

        if ($advertiserModels->isEmpty()) {
            logger()->error("No advertiser account models found.");
            return $dailyAdData;
        }

        $metaAdsClients = [Advertiser::GABE];
        if (in_array(Advertiser::WADE, $advertisers))
            $metaAdsClients[] = Advertiser::WADE;

        foreach ($metaAdsClients as $advertiser) {
            $this->metaAdsClient = $this->metaAdsAuthService->getAdCostClient($advertiser);

            // Get advertising accounts
            $accounts = $this->getMetaAccounts();

            foreach ($accounts as $account) {
                foreach ($resolutions as $resolution) {
                    $this->getMetaDailyAdCosts(
                        dailyAdData: $dailyAdData,
                        adsAccount: $account,
                        period: $period,
                        resolution: $resolution,
                        advertisers: $advertisers,
                        industries: $industries);
                }
            }
        }

        return $dailyAdData;
    }

    /**
     * @param array $advertisers
     * @return Collection
     */
    public function getAdvertisers(array $advertisers): Collection
    {
        return AdvertiserModel::query()
            ->select(AdvertiserModel::FIELD_ID, AdvertiserModel::FIELD_KEY, AdvertiserModel::FIELD_NAME)
            ->whereIn(AdvertiserModel::FIELD_KEY, array_map(function ($advertiser) {return Advertiser::getKey($advertiser);}, $advertisers))
            ->get();
    }

    /**
     * @return Collection
     */
    public function getMetaCampaigns(): Collection
    {
        return AdvertisingAccount::query()
            ->where(AdvertisingAccount::FIELD_PLATFORM,AdvertisingPlatform::META)
            ->get();
    }

    /**
     * Gets the advertising accounts for the advertiser currently represented with metaAdsClient
     * @return array
     */
    public function getMetaAccounts(): array
    {
        // Get accounts from meta api
        $res = $this->metaAdsClient->call("/me/adaccounts")->getContent();

        $accounts = $res['data'];
        $accountIds = [];

        foreach ($accounts as $account) {
            $accountIds[] = $account['id'];
        }
        return $accountIds;
    }

    /**
     * Get daily ad cost for given advertiser, resolution, ads account
     * @param Collection $dailyAdData
     * @param string $adsAccount
     * @param CarbonPeriod $period
     * @param AdvertisingResolution $resolution
     * @param array $advertisers
     * @param array $industries
     * @return Collection
     * @throws Exception
     */
    public function getMetaDailyAdCosts(
        Collection $dailyAdData,
        string $adsAccount,
        CarbonPeriod $period,
        AdvertisingResolution $resolution,
        array $advertisers,
        array $industries,
    ): Collection
    {
        $dailyAdCostAccount = $this->getDailyAdCostAccount($adsAccount);
        $industry = $this->getAccountIndustry($adsAccount, $dailyAdCostAccount);
        $industryServiceId = null;
        $advertiser = Advertiser::WILL;

        if ($dailyAdCostAccount) {
            $industryServiceId = $dailyAdCostAccount->{DailyAdCostAccount::FIELD_INDUSTRY_SERVICE_ID};
            $advertiser = $dailyAdCostAccount->advertiser;

            // If account is excluded from automation, return
            if ($dailyAdCostAccount->{DailyAdCostAccount::FIELD_EXCLUDE})
                return collect([]);
        }

        if (!in_array($industry, $industries))
            return collect([]);

        if (!in_array($advertiser, $advertisers))
            return collect([]);

        // Create an AdAccount object
        $adAccount = new AdAccount($adsAccount, api: $this->metaAdsClient);

        // API Fields, location information is included by requesting breakdown
        $fields = [
            AdsInsightsFields::DATE_START,
            AdsInsightsFields::DATE_STOP,
            AdsInsightsFields::SPEND,
        ];

        // Region returns state data, dma returns data by DMA (groups of counties, cost is shared between them)
        if ($resolution === AdvertisingResolution::STATE) {
            $breakdowns = [self::REGION];
        } else {
            $breakdowns = [self::DMA];
        }

        // Meta can only do one time period at a time, so for daily data we have to loop through days
        $daysDifference = $period->getEndDate()->diffInDays($period->getStartDate());
        $currentDay = $period->getStartDate()->copy();

        // Generate daily ad cost entries from each day
        for ($dayIndex = 0; $dayIndex <= $daysDifference; $dayIndex++) {
            $this->getDayOfSpendFromMetaAccount(
                dailyAdData: $dailyAdData,
                metaAccount: $adAccount,
                fields: $fields,
                breakdowns: $breakdowns,
                adsAccount: $adsAccount,
                day: $currentDay,
                resolution: $resolution,
                advertiser: $advertiser,
                industry: $industry,
                industryServiceId: $industryServiceId,
                dailyAdCostAccount: $dailyAdCostAccount,
            );
            $currentDay->addDay();
        }
        return $dailyAdData;
    }

    /**
     * @param string $adsAccount
     * @return DailyAdCostAccount|null
     */
    public function getDailyAdCostAccount(string $adsAccount): ?DailyAdCostAccount
    {
        return DailyAdCostAccount::query()
            ->where(DailyAdCostAccount::FIELD_PLATFORM, AdvertisingPlatform::META)
            ->where(DailyAdCostAccount::FIELD_PLATFORM_ACCOUNT_ID, $adsAccount)
            ->get()
            ->first();
    }

    /**
     * @param Collection $dailyAdData
     * @param AdAccount $metaAccount
     * @param array $fields
     * @param array $breakdowns
     * @param string $adsAccount
     * @param CarbonInterface $day
     * @param AdvertisingResolution $resolution
     * @param Advertiser $advertiser
     * @param Industry $industry
     * @param int|null $industryServiceId
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @return Collection
     * @throws Exception
     */
    public function getDayOfSpendFromMetaAccount(
        Collection $dailyAdData,
        AdAccount $metaAccount,
        array $fields,
        array $breakdowns,
        string $adsAccount,
        CarbonInterface $day,
        AdvertisingResolution $resolution,
        Advertiser $advertiser,
        Industry $industry,
        ?int $industryServiceId,
        ?DailyAdCostAccount $dailyAdCostAccount,
    ): Collection
    {
        // Define parameters for the report (one day of data by state or dma)
        $params = [
            'limit' => '10000', // Limiting to query per day will not hit this limit
            'time_range' => json_encode([
                'since' => $day->format('Y-m-d'),
                'until' => $day->format('Y-m-d'),
            ]),
            'time_increment' => 1, // Daily data
            'breakdowns' => implode(',',$breakdowns), // State (region) or County (dma)
        ];

        try {
            /** @var Cursor $insightsCursor */
            $insightsCursor = $metaAccount->getInsights($fields, $params);
        } catch (Exception $e) {
            logger()->error("Failed to retrieve ad spend data for account: ".
                $adsAccount.", Advertiser: ".
                $advertiser->getDisplayName().". Error: ".$e->getMessage());
            return collect([]);
        }

        if($insightsCursor->getLastResponse()->getStatusCode() !== 200) {
            throw new Exception(MetaAdsService::getRequestExceptionMessage($insightsCursor->getLastResponse()));
        }

        $currentCount = $insightsCursor->count();

        for ($i = 0; $i < $currentCount; $i++) {
            /** @var AdsInsights $adsInsightsEntry */
            $adsInsightsEntry = $insightsCursor[$i];
            $data = $adsInsightsEntry->getData();
            if (floatval($data[self::SPEND]) === 0.0)
                continue;
            if ($resolution === AdvertisingResolution::STATE) {
                // State builds single daily ad cost entries
                $this->buildStateCostEntry(
                    dailyAdData: $dailyAdData,
                    advertisingAccount: $adsAccount,
                    state: $data[self::REGION],
                    cost: $data[self::SPEND],
                    date: $data[self::DATE],
                    advertiser: $advertiser,
                    industry: $industry,
                    industryServiceId: $industryServiceId,
                    dailyAdCostAccount: $dailyAdCostAccount,
                );
            } else {
                // Data returned by DMA cost, county build returns collection of cost distributed over counties in DMA
                $this->buildCountyCostEntries(
                    dailyAdData: $dailyAdData,
                    advertisingAccount: $adsAccount,
                    dmaKey: $data[self::DMA],
                    cost: $data[self::SPEND],
                    date: $data[self::DATE],
                    advertiser: $advertiser,
                    industry: $industry,
                    industryServiceId: $industryServiceId,
                    dailyAdCostAccount: $dailyAdCostAccount,
                );
            }
        }

        return $dailyAdData;
    }

    /**
     * @param Collection $dailyAdData
     * @param string $advertisingAccount
     * @param string $state
     * @param string $cost
     * @param string $date
     * @param Advertiser $advertiser
     * @param Industry $industry
     * @param int|null $industryServiceId
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @return void
     * @throws Exception
     */
    public function buildStateCostEntry(
        Collection $dailyAdData,
        string $advertisingAccount,
        string $state,
        string $cost,
        string $date,
        Advertiser $advertiser,
        Industry $industry,
        ?int $industryServiceId,
        ?DailyAdCostAccount $dailyAdCostAccount,
    ): void
    {
        $state = $this->cleanStateString($state);
        $location = $this->stateCollection->first(function ($location) use ($state) {
            return $location[Location::STATE_KEY] === Str::slug($state);
        });

        if (!$location) {
            if ($state !== self::UNKNOWN)
                logger()->error("Failed to find database location for State: $state, Slug: ".Str::slug($state).
                    " from Meta Ads API locations. Cost: {$cost}");
            return;
        }

        // Round to two decimal places
        $costFloat = floatval($cost);
        $costFloat = round($costFloat, 2);

        $data = [
            DailyAdCost::DATA_META_ACCOUNT_ID => $advertisingAccount,
        ];

        $dailyAdData->push(new DailyAdCost([
            DailyAdCost::FIELD_COST         => $costFloat,
            DailyAdCost::FIELD_LOCATION_ID  => $location->{Location::ID},
            DailyAdCost::FIELD_DATE         => $date,
            DailyAdCost::FIELD_INDUSTRY_ID  => $this->industrySlugToId[$industry->getSlug()],
            DailyAdCost::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId,
            DailyAdCost::FIELD_ADVERTISER   => $advertiser,
            DailyAdCost::FIELD_PLATFORM     => AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value),
            DailyAdCost::FIELD_DATA         => json_encode($data),
            DailyAdCost::FIELD_DAILY_AD_COST_ACCOUNT_ID => $dailyAdCostAccount?->id ?? null,
        ]));
    }

    /**
     * @param Collection $dailyAdData
     * @param string $advertisingAccount
     * @param string $dmaKey
     * @param string $cost
     * @param string $date
     * @param Advertiser $advertiser
     * @param Industry $industry
     * @param int|null $industryServiceId
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @return void
     * @throws Exception
     */
    public function buildCountyCostEntries(
        Collection $dailyAdData,
        string $advertisingAccount,
        string $dmaKey,
        string $cost,
        string $date,
        Advertiser $advertiser,
        Industry $industry,
        ?int $industryServiceId,
        ?DailyAdCostAccount $dailyAdCostAccount,
    ): void
    {
        // Get Counties within DMA
        $shareCounties = $this->dmaLocations[$dmaKey] ?? null;

        if (!$shareCounties) {
            if ($dmaKey != self::UNKNOWN)
                logger()->error("Failed to find database DMA for Meta DMA Key: $dmaKey. Cost: {$cost}");
            return;
        }

        $dmaWeightTotal = 0;
        foreach ($shareCounties as $county) {
            $dmaWeightTotal += $this->countyWeights[$county] ?? 0;
        }

        if ($dmaWeightTotal > 0) {
            $shareCountyCosts = $shareCounties->mapWithKeys(function($county) use ($dmaWeightTotal, $cost) {
                return [ $county => round(floatval($cost * (($this->countyWeights[$county] ?? 0) / $dmaWeightTotal)), 2) ];
            })->toArray();
        } else {
            $countyCount = $shareCounties->count();
            $shareCountyCosts = $shareCounties->mapWithKeys(function($county) use ($countyCount, $cost) {
                return [ $county => round(floatval($cost / $countyCount), 2) ];
            })->toArray();
        }

        // If cost is less than one dollar, assign to first county in DMA
        if (floatval($cost) < 1) {
            $shareCounties = collect([$shareCounties->first()]);
            $shareCountyCosts = [
                $shareCounties->first() => floatval($cost),
            ];
        }

        $data = [
            DailyAdCost::DATA_META_ACCOUNT_ID   => $advertisingAccount,
        ];

        foreach ($shareCounties as $shareCountyId) {
            $dailyAdData->push(new DailyAdCost([
                DailyAdCost::FIELD_COST         => $shareCountyCosts[$shareCountyId] ?? 0,
                DailyAdCost::FIELD_LOCATION_ID  => $shareCountyId,
                DailyAdCost::FIELD_DATE         => $date,
                DailyAdCost::FIELD_INDUSTRY_ID  => $this->industrySlugToId[$industry->getSlug()],
                DailyAdCost::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId,
                DailyAdCost::FIELD_ADVERTISER   => $advertiser,
                DailyAdCost::FIELD_PLATFORM     => AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value),
                DailyAdCost::FIELD_DATA         => json_encode($data),
                DailyAdCost::FIELD_DAILY_AD_COST_ACCOUNT_ID => $dailyAdCostAccount?->id ?? null,
            ]));
        }
    }

    /**
     * Initialize Locations before parsing
     * @return void
     */
    public function getLocations(): void
    {
        $this->stateCollection = Location::query()
            ->select(Location::ID, Location::STATE, Location::STATE_KEY)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get();

        $counties = Location::query()
            ->select(Location::ID, Location::STATE, Location::STATE_KEY, Location::COUNTY, Location::COUNTY_KEY)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->get();

        $dmas = Dma::query()
            ->select(Dma::FIELD_ID, Dma::FIELD_DMA_CODE, Dma::FIELD_META_DMA_KEY)
            ->get();

        $dmaLocations = DmaLocation::query()
            ->select(DmaLocation::FIELD_ID, DmaLocation::FIELD_DMA_ID, DmaLocation::FIELD_LOCATION_ID)
            ->get();

        foreach ($dmas as $dma) {
            $locationMapping = $dmaLocations->where(DmaLocation::FIELD_DMA_ID, $dma->{Dma::FIELD_ID});
            $shareCounties = $counties->whereIn(Location::ID, $locationMapping->pluck(DmaLocation::FIELD_LOCATION_ID));
            $this->dmaLocations[$dma->{Dma::FIELD_META_DMA_KEY}] = $shareCounties->pluck(Location::ID);
        }
    }

    /**
     * @return void
     */
    public function getIndustryIds(): void
    {
        $this->industrySlugToId = array_flip(IndustryModel::query()
            ->select(IndustryModel::FIELD_SLUG, IndustryModel::FIELD_ID)
            ->get()
            ->pluck(IndustryModel::FIELD_SLUG,IndustryModel::FIELD_ID)
            ->toArray());
    }

    /**
     * @param string $state
     * @return string
     */
    public function cleanStateString(string $state): string
    {
        if ($state === 'Washington, District of Columbia')
            $state = 'District of Columbia';
        if ($state === 'Baja California')
            $state = 'California';
        if ($state === 'Quebec')
            $state = 'Maine';
        if ($state === 'Chihuahua')
            $state = self::UNKNOWN;
        if ($state === 'Sonora')
            $state = self::UNKNOWN;
        if ($state === 'Tamaulipas')
            $state = self::UNKNOWN;

        return $state;
    }

    /**
     * @param string $metaAccountId
     * @return array
     */
    public function getMetaAccountCampaigns(string $metaAccountId): array
    {
        $fields = array(
            'name',
            'objective',
        );
        $params = array(
            'effective_status' => array('ACTIVE','PAUSED'),
        );

        $cursor = (new AdAccount($metaAccountId, api: $this->metaAdsClient))->getCampaigns($fields, $params);
        $currentCount = $cursor->count();
        $campaignIds = [];

        for ($i = 0; $i < $currentCount; $i++) {
            $campaignIds[] = $cursor[$i]->id;
        }
        return $campaignIds;
    }

    /**
     * @param string $account
     * @param DailyAdCostAccount|null $dailyAdCostAccount
     * @return Industry
     */
    public function getAccountIndustry(string $account, ?DailyAdCostAccount $dailyAdCostAccount): Industry
    {
        if ($dailyAdCostAccount && $dailyAdCostAccount->industry_id) {
            $industryModel = IndustryModel::find($dailyAdCostAccount->industry_id);
            return Industry::fromSlug($industryModel->slug);
        }

        // Get Campaign IDs associated with account
        $campaignIds = $this->getMetaAccountCampaigns($account);
        // Look for an advertising account entry with the given id
        $adsCampaignModel = $this->metaCampaigns->filter(function ($campaign) use ($campaignIds) {
            return in_array($campaign->{AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID}, $campaignIds);
        })->first();

        if ($adsCampaignModel) {
            return Industry::fromSlug(Str::slug($adsCampaignModel->{AdvertisingAccount::FIELD_INDUSTRY}));
        } else {
            // Default to solar if no campaigns found for account
            return Industry::SOLAR;
        }
    }

    /**
     * This function defines the individual county weights of the meta allocation by the portion of Google and Microsoft ad
     * spend at each county on the most recent date before the given period.
     * @param CarbonPeriod $period
     * @return void
     * @throws Exception
     */
    public function getMetaCountyWeights(CarbonPeriod $period): void
    {
        $mostRecentAdDataDate = DailyAdCost::query()
            ->select(DB::raw('MAX('.DailyAdCost::FIELD_DATE.') as '.DailyAdCost::FIELD_DATE))
            ->whereNot(DailyAdCost::FIELD_PLATFORM, AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value))
            ->where(DailyAdCost::FIELD_DATE, '<', $period->getStartDate()->format('Y-m-d'))
            ->get()->first()->{DailyAdCost::FIELD_DATE};

        $costByCounty = DailyAdCost::query()
            ->select(Location::TABLE.'.'.Location::ID, DB::raw('SUM('.DailyAdCost::FIELD_COST.') as '.DailyAdCost::FIELD_COST))
            ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_LOCATION_ID)
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_COUNTY)
            ->where(DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_DATE, $mostRecentAdDataDate)
            ->whereNot(DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_PLATFORM, AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value))
            ->groupBy(Location::TABLE.'.'.Location::ID)
            ->get();

        $this->countyWeights = $costByCounty->mapWithKeys(function($county) {
            return [ $county->{Location::ID} => $county->{DailyAdCost::FIELD_COST} ];
        })->toArray();
    }
}
