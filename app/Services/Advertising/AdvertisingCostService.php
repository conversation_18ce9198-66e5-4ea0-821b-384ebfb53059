<?php

namespace App\Services\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingResolution;
use App\Models\DailyAdCost;
use App\Models\Legacy\Location;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Services\Advertising\Costs\AdvertisingCostsServiceFactory;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AdvertisingCostService
{
    const int CHUNK_INSERT_SIZE         = 1000;
    const string CSV_KEY_RESOLUTIONS    = 'resolutions';
    const string CSV_KEY_INDUSTRIES     = 'industries';
    const string CSV_KEY_AD_SOURCES     = 'ad_sources';
    const string CSV_KEY_ADVERTISERS    = 'advertisers';
    const string CSV_UNKNOWN_LOCATION   = 'UNKNOWN';

    /**
     * @param IndustryRepository $industryRepository
     * @param LocationRepository $locationRepository
     */
    public function __construct(
        protected IndustryRepository $industryRepository,
        protected LocationRepository $locationRepository,
    ) {}

    /**
     * @param CarbonPeriod $period
     * @param AdvertisingPlatform $adSource
     * @param array $resolutions
     * @param array $industries
     * @param array $advertisers
     * @param string|null $filePath
     * @param bool $noDb
     * @param bool $noDelete
     * @return void
     * @throws Exception
     */
    public function getDailyAdCostData(
        CarbonPeriod $period,
        AdvertisingPlatform $adSource,
        array $resolutions,
        array $industries,
        array $advertisers,
        ?string $filePath = null,
        bool $noDb = false,
        bool $noDelete = false,
    ): void
    {
        $driver = AdvertisingCostsServiceFactory::make($adSource->value);
        $dailyAdData = $driver->retrieveDailyAdCostData($period, $resolutions, $industries, $advertisers);

        if (!$noDb) {
            if (!$noDelete)
                $this->dbClearExistingDailyAdData($dailyAdData);
            $this->dbInsertDailyAdData($dailyAdData);
        }

        if ($filePath)
            $this->csvWriteDailyAdData($filePath, $dailyAdData);
    }

    /**
     * @param Collection $dailyAdData
     * @return void
     */
    public function dbClearExistingDailyAdData(Collection $dailyAdData): void
    {
        DB::table(DailyAdCost::TABLE)
            ->whereIn(DailyAdCost::FIELD_LOCATION_ID, $dailyAdData->pluck(DailyAdCost::FIELD_LOCATION_ID)->unique()->toArray())
            ->whereIn(DailyAdCost::FIELD_ADVERTISER, $dailyAdData->pluck(DailyAdCost::FIELD_ADVERTISER)->unique()->toArray())
            ->whereIn(DailyAdCost::FIELD_DATE, $dailyAdData->pluck(DailyAdCost::FIELD_DATE)->unique()->toArray())
            ->whereIn(DailyAdCost::FIELD_INDUSTRY_ID, $dailyAdData->pluck(DailyAdCost::FIELD_INDUSTRY_ID)->unique()->toArray())
            ->whereIn(DailyAdCost::FIELD_PLATFORM, $dailyAdData->pluck(DailyAdCost::FIELD_PLATFORM)->unique()->toArray())
            ->delete();
    }

    /**
     * @param Collection $dailyAdData
     * @return void
     */
    public function dbInsertDailyAdData(Collection $dailyAdData): void
    {
        DB::transaction(function() use ($dailyAdData) {
            $dailyAdData->chunk(self::CHUNK_INSERT_SIZE)->each(function ($chunkInsert) {
                DailyAdCost::query()->insert($chunkInsert->toArray());
            });
        });
    }

    /**
     * @param string $filePath
     * @param string $startDate
     * @param string $endDate
     * @param array $industries
     * @param array $resolutions
     * @param array $advertisers
     * @param array $adSources
     * @return void
     * @throws Exception
     */
    public function addCostDataCsvHeader(
        string $filePath,
        string $startDate,
        string $endDate,
        array $industries,
        array $resolutions,
        array $advertisers,
        array $adSources,
    ): void
    {
        // Mapping each enumerator to id used in CSV, IDs stored to save string space for each entry in CSV file
        $metaData = [
            self::CSV_KEY_RESOLUTIONS => array_combine(
                array_map(function($resolution) {return $resolution->value;}, $resolutions),
                array_map(function($resolution) {return AdvertisingResolution::getInteger($resolution->value);}, $resolutions)),
            self::CSV_KEY_ADVERTISERS => array_combine(
                array_map(function($advertiser) {return $advertiser->getDisplayName();}, $advertisers),
                array_map(function($advertiser) {return $advertiser->value;}, $advertisers)),
            self::CSV_KEY_AD_SOURCES  => array_combine(
                array_map(function($adSource) {return $adSource->value;}, $adSources),
                array_map(function($adSource) {return AdvertisingPlatform::getInteger($adSource->value);}, $adSources)),
            self::CSV_KEY_INDUSTRIES  => array_combine(
                array_map(function($industry) {return $industry->value;}, $industries),
                array_map(function($industry) {return $this->industryRepository->getIndustryIdBySlug($industry->getSlug());}, $industries)),
        ];

        $headerString = "Daily Ad Cost Report\n{$startDate} to {$endDate}\nKeys:\n".json_encode($metaData)."\n";
        $columns = "Columns:\nIndustry,State,County,Advertiser,Platform,Cost,Date\n";
        file_put_contents($filePath, $headerString.$columns);
    }

    /**
     * @param $filePath
     * @param Collection $dailyAdData
     * @return void
     */
    public function csvWriteDailyAdData($filePath, Collection $dailyAdData): void
    {
        $states = $this->locationRepository->getStates();
        $counties = $this->locationRepository->getCounties();
        $locations = $states->merge($counties);

        $file = fopen($filePath, 'a'); // a for file append mode

        // Exclusive lock file while writing
        if (flock($file, LOCK_EX)) {
            foreach ($dailyAdData as $dailyAdCost) {
                fwrite($file, $this->getDailyAdCostCsvRow($dailyAdCost, $locations));
            }
            // Release file lock
            flock($file, LOCK_UN);
        }
        fclose($file);
    }

    /**
     * @param DailyAdCost $dailyAdCost
     * @param Collection $locations
     * @return string
     */
    public function getDailyAdCostCsvRow(DailyAdCost $dailyAdCost, Collection $locations): string
    {
        $location = $locations->firstWhere(Location::ID, $dailyAdCost->{DailyAdCost::FIELD_LOCATION_ID});

        if ($location) {
            $state = $location->{Location::STATE};
            if ($location->{Location::TYPE} === Location::TYPE_COUNTY) {
                $county = $location->{Location::COUNTY};
            } else {
                $county = '';
            }
        } else {
            $state = self::CSV_UNKNOWN_LOCATION;
            $county = self::CSV_UNKNOWN_LOCATION;
        }

        return "{$dailyAdCost->{DailyAdCost::FIELD_INDUSTRY_ID}},{$state},{$county},".
            "{$dailyAdCost->{DailyAdCost::FIELD_ADVERTISER}->value},{$dailyAdCost->{DailyAdCost::FIELD_PLATFORM}},".
            "{$dailyAdCost->{DailyAdCost::FIELD_COST}},{$dailyAdCost->{DailyAdCost::FIELD_DATE}}\n";
    }
}
