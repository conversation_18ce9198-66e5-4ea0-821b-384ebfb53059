<?php

namespace App\Services\Advertising\Conversions;

use App\Abstracts\Advertising\AdvertisingConversionsServiceAbstract;
use App\DataModels\Advertising\ConversionRevenueInfo;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Enums\Advertising\AdvertisingTrackType;

class TestDummyConversionsService extends AdvertisingConversionsServiceAbstract
{
    /**
     * @inheritDoc
     */
    public function uploadConversions($accountId, ConversionRevenueInfo $conversionRevenueInfo, AdvertiserEnum $advertiser): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getTrackTypes(): array
    {
        return array_map(fn($case) => $case->value, AdvertisingTrackType::cases());
    }
}
