<?php

namespace App\Services\Advertising\Conversions;

use App\Abstracts\Advertising\AdvertisingConversionsServiceAbstract;
use App\Enums\Advertising\AdvertisingPlatform;
use Exception;

class AdvertisingConversionsServiceFactory
{
    const TEST_DUMMY = 'test_dummy';

    /**
     * @param string $driver
     * @return AdvertisingConversionsServiceAbstract
     * @throws Exception
     */
    public static function make(string $driver): AdvertisingConversionsServiceAbstract
    {
        return match($driver) {
            AdvertisingPlatform::GOOGLE->value => app(GoogleConversionsService::class),
            AdvertisingPlatform::MICROSOFT->value => app(MicrosoftConversionsService::class),
            AdvertisingPlatform::META->value => app(MetaConversionsService::class),
            self::TEST_DUMMY => app(TestDummyConversionsService::class),
            default => throw new Exception(__METHOD__.": Invalid driver")
        };
    }
}
