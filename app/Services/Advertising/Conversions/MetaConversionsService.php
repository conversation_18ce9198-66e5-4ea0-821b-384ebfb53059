<?php

namespace App\Services\Advertising\Conversions;

use App\Abstracts\Advertising\AdvertisingConversionsServiceAbstract;
use App\DataModels\Advertising\ConversionRevenueInfo;
use App\DataModels\Odin\ConsumerProductTrackingPayloadDataModel as CPTPDM;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Advertising\AdvertisingTrackType;
use App\Models\Advertiser;
use App\Models\AdvertisingAccount;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Authentication\MetaAdsAuthService;
use Exception;
use FacebookAds\Api;
use FacebookAds\Http\RequestInterface;
use FacebookAds\Http\ResponseInterface;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\ServerSide\ActionSource;
use FacebookAds\Object\ServerSide\CustomData;
use FacebookAds\Object\ServerSide\Event;
use FacebookAds\Object\ServerSide\UserData;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Throwable;

class MetaConversionsService extends AdvertisingConversionsServiceAbstract
{
    const BATCH_LIMIT = 1000;

    private Api $client;

    public function __construct(
        private readonly MetaAdsAuthService $metaAdsAuthService
    )
    {

    }

    /**
     * @inheritDoc
     */
    public function uploadConversions(string $accountId, ConversionRevenueInfo $conversionRevenueInfo, AdvertiserEnum $advertiser): array
    {
        if($conversionRevenueInfo->isEmpty()) {
            AdvertisingLoggingService::writeLog(
                "No conversions to upload",
                [
                    'platform' => AdvertisingPlatform::META->value,
                    'account' => $accountId,
                    'advertiser' => $advertiser->value
                ]
            );

            return [
                self::SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS => [],
                self::SUCCESSFUL_PRODUCT_REJECTION_IDS => [],
                self::SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS => []
            ];
        }

        try {
            if($advertiser === AdvertiserEnum::GABE) {
                $pixelId = config('services.meta.ads.pixel_id');
            }
            else if($advertiser === AdvertiserEnum::WADE) {
                $pixelId = config('services.meta.wade_ads.pixel_id');
            }
            else {
                throw new Exception("Invalid advertiser");
            }

            $leadConversionInfo = $conversionRevenueInfo->toArray();

            $consumerData = $this->getConsumerData(array_keys($leadConversionInfo));

            $purchaseEvents = [];
            $productAssignmentIds = [];
            $productRejectionIds = [];
            $batchIdx = 0;

            $purchaseEvents[$batchIdx] = [];
            $productAssignmentIds[$batchIdx] = [];
            $productRejectionIds[$batchIdx] = [];
            $consumerProductTrackingIds[$batchIdx] = [];
            foreach($leadConversionInfo as $conversionInfo) {
                if($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE] != $accountId) {
                    AdvertisingLoggingService::writeLog(
                        "Track code doesn't match account ID",
                        [
                            'platform' => AdvertisingPlatform::META->value,
                            'advertiser' => $advertiser->value,
                            'account' => $accountId,
                            'track_code' => $conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE]
                        ]
                    );

                    continue;
                }

                if(count($purchaseEvents[$batchIdx]) >= self::BATCH_LIMIT) {
                    $batchIdx++;

                    $purchaseEvents[$batchIdx] = [];
                    $productAssignmentIds[$batchIdx] = [];
                    $productRejectionIds[$batchIdx] = [];
                    $consumerProductTrackingIds[$batchIdx] = [];
                }

                $trackingPayload = CPTPDM::fromArray($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_PAYLOAD]);

                $fbclid = sprintf(
                    "fb.%s.%s.%s",
                    1,
                    (int) ($conversionInfo[self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP] * 1000),
                    $trackingPayload->get(CPTPDM::FBCLID)
                );

                $trackedConsumer = $consumerData[$conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID]];

                $userData = (new UserData())
                    ->setEmails([$trackedConsumer[Consumer::FIELD_EMAIL]])
                    ->setPhones([$trackedConsumer[Consumer::FIELD_FORMATTED_PHONE]])
                    ->setClientIpAddress($trackedConsumer[ConsumerProduct::FIELD_IP_ADDRESS])
                    ->setClientUserAgent($trackingPayload->get(CPTPDM::USER_AGENT))
                    ->setFbc($fbclid)
                    ->setFbp($trackingPayload->get(CPTPDM::FB_PIXEL));

                $customData = (new CustomData())
                    ->setOrderId($conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID])
                    ->setCurrency('usd')
                    ->setValue($conversionInfo[self::LEAD_REVENUE_INFO_REVENUE]);

                $event = (new Event())
                    ->setEventId($conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID])
                    ->setEventName('Purchase')
                    ->setEventTime($conversionInfo[self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP])
                    ->setEventSourceUrl($conversionInfo[self::LEAD_REVENUE_INFO_CONVERSION_URL])
                    ->setUserData($userData)
                    ->setCustomData($customData)
                    ->setActionSource(ActionSource::SYSTEM_GENERATED);

                $purchaseEvents[$batchIdx][] = $event->normalize();

                $consumerProductTrackingIds[$batchIdx][] = $conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID];

                if(!empty($conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS])) {
                    $productAssignmentIds[$batchIdx] = array_merge($productAssignmentIds[$batchIdx], array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS]));
                }

                if(!empty($conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS])) {
                    $productRejectionIds[$batchIdx] = array_merge($productRejectionIds[$batchIdx], array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS]));
                }
            }

            AdvertisingLoggingService::writeLog(
                "Consolidated purchase events",
                [
                    'platform' => AdvertisingPlatform::META->value,
                    'advertiser' => $advertiser->value,
                    'account' => $accountId,
                    'count' => collect($purchaseEvents)->collapse()->count(),
                    'tracking_ids' => collect($consumerProductTrackingIds)->collapse()->toArray()
                ]
            );

            $this->getClient($advertiser);

            $successfulProductAssignmentIds = [];
            $successfulProductRejectionIds = [];
            $successfulConsumerProductTrackingIds = [];

            foreach($purchaseEvents as $batchIdx => $events) {
                if(empty($events)) {
                    AdvertisingLoggingService::writeLog(
                        "No purchase events for batch {$batchIdx}",
                        [
                            'platform' => AdvertisingPlatform::META->value,
                            'advertiser' => $advertiser->value,
                            'account' => $accountId
                        ]
                    );

                    continue;
                }

                /** @var ResponseInterface $response */
                $response = $this->client->call(
                    "/{$pixelId}/events",
                    RequestInterface::METHOD_POST,
                    [
                        "data" => $events
                    ]
                );

                $statusCode = $response->getStatusCode();

                AdvertisingLoggingService::writeLog(
                    "Called events reporting endpoint",
                    [
                        'platform' => AdvertisingPlatform::META->value,
                        'advertiser' => $advertiser->value,
                        'account' => $accountId,
                        'status' => $statusCode
                    ]
                );

                if($statusCode >= 200
                && $statusCode < 300) {
                    $successfulProductAssignmentIds = array_merge($successfulProductAssignmentIds, $productAssignmentIds[$batchIdx]);
                    $successfulProductRejectionIds = array_merge($successfulProductRejectionIds, $productRejectionIds[$batchIdx]);
                    $successfulConsumerProductTrackingIds = array_merge($successfulConsumerProductTrackingIds, $consumerProductTrackingIds[$batchIdx]);
                }
                else {
                    AdvertisingLoggingService::writeLog(
                        "Offline conversions API call error",
                        array_merge(
                            json_decode($response->getBody(), true),
                            [
                                'platform' => AdvertisingPlatform::META->value,
                                'account' => $accountId
                            ]
                        )
                    );
                }
            }

            return [
                self::SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS => array_unique($successfulProductAssignmentIds),
                self::SUCCESSFUL_PRODUCT_REJECTION_IDS => array_unique($successfulProductRejectionIds),
                self::SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS => array_unique($successfulConsumerProductTrackingIds)
            ];
        }
        catch(Throwable $e) {
            $errLocation = __METHOD__.': Line '.$e->getLine().'. ';

            logger()->error($errLocation.((string) $e));

            AdvertisingLoggingService::writeLog(
                "Offline conversions upload error. ".$errLocation.substr($e->getMessage(), 0, 255),
                [
                    'platform' => AdvertisingPlatform::META->value,
                    'advertiser' => $advertiser->value,
                    'account' => $accountId,
                    'trace' => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @inheritDoc
     */
    public function getTrackTypes(): array
    {
        return [
            AdvertisingTrackType::FACEBOOK->value
        ];
    }

    private function getFacebookCampaignIds(AdvertiserEnum $advertiser): array
    {
        $advertiserId = Advertiser::query()
            ->where(Advertiser::FIELD_KEY, AdvertiserEnum::getKey($advertiser))
            ->firstOrFail()
            ->{Advertiser::FIELD_ID};

        return AdvertisingAccount::query()
            ->where(AdvertisingAccount::FIELD_PLATFORM, AdvertisingPlatform::META->value)
            ->where(AdvertisingAccount::FIELD_ADVERTISER_ID, $advertiserId)
            ->pluck(AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID)
            ->toArray();
    }

    /**
     * @param array $consumerTrackingIds
     * @return array
     */
    private function getConsumerData(array $consumerTrackingIds): array
    {
        return ConsumerProduct::query()
            ->join(Consumer::TABLE, function($join) {
                $join->on(
                    Consumer::TABLE.'.'.Consumer::FIELD_ID,
                    '=',
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID
                );
            })
            ->whereIntegerInRaw(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID, $consumerTrackingIds)
            ->selectRaw(sprintf(
                "%s, %s, %s, CONCAT(1, %s) AS %s",
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID,
                ConsumerProduct::FIELD_IP_ADDRESS,
                Consumer::FIELD_EMAIL,
                Consumer::FIELD_FORMATTED_PHONE,
                Consumer::FIELD_FORMATTED_PHONE,
            ))
            ->get()
            ->mapWithKeys(fn($row) => [
                $row->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID} => [
                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => (int) $row->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID},
                    ConsumerProduct::FIELD_IP_ADDRESS => $row->{ConsumerProduct::FIELD_IP_ADDRESS},
                    Consumer::FIELD_EMAIL => $row->{Consumer::FIELD_EMAIL},
                    Consumer::FIELD_FORMATTED_PHONE => (int) $row->{Consumer::FIELD_FORMATTED_PHONE}
                ]
            ])
            ->toArray();
    }

    /**
     * @param AdvertiserEnum $advertiser
     * @return Api
     * @throws Throwable
     */
    private function getClient(AdvertiserEnum $advertiser): Api
    {
        $this->client = $this->metaAdsAuthService->getClient($advertiser);

        if(!App::environment('production')) {
            $this->client->setLogger(new CurlLogger());
        }

        return $this->client;
    }
}
