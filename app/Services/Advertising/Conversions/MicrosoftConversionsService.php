<?php

namespace App\Services\Advertising\Conversions;

use App\Abstracts\Advertising\AdvertisingConversionsServiceAbstract;
use App\DataModels\Advertising\ConversionRevenueInfo;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Authentication\AdvertisingAuthServiceFactory;
use App\Services\Advertising\Authentication\MicrosoftAdsAuthService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Microsoft\BingAds\Auth\ApiEnvironment;
use Microsoft\BingAds\Auth\AuthorizationData;
use Microsoft\BingAds\Auth\ServiceClient;
use Microsoft\BingAds\Auth\ServiceClientType;
use Microsoft\BingAds\V13\CampaignManagement\ApplyOfflineConversionAdjustmentsRequest;
use Microsoft\BingAds\V13\CampaignManagement\ApplyOfflineConversionsRequest;
use Microsoft\BingAds\V13\CampaignManagement\OfflineConversion;
use Microsoft\BingAds\V13\CampaignManagement\OfflineConversionAdjustment;
use SoapFault;
use Throwable;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;

class MicrosoftConversionsService extends AdvertisingConversionsServiceAbstract
{
    const TRACK_CODE_BING_ADS = 'bing_ads';

    private AuthorizationData $microsoftAdsClient;

    public function __construct() {
        /** @var MicrosoftAdsAuthService $authService */
        $authService = AdvertisingAuthServiceFactory::make(AdvertisingPlatform::MICROSOFT->value);

        $client = $authService->getClient();

        $authService->attachOAuthTokensToClient($client);

        $this->microsoftAdsClient = $client;
    }

    /** @inheritDoc */
    public function getTrackTypes(): array
    {
        return [
            self::TRACK_CODE_BING_ADS
        ];
    }

    /** @inheritDoc */
    public function uploadConversions(string $accountId, ConversionRevenueInfo $conversionRevenueInfo, AdvertiserEnum $advertiser): array
    {
        if($conversionRevenueInfo->isEmpty()) {
            AdvertisingLoggingService::writeLog(
                "No conversions to upload",
                [
                    'platform' => AdvertisingPlatform::MICROSOFT->value
                ]
            );

            return [
                self::SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS => [],
                self::SUCCESSFUL_PRODUCT_REJECTION_IDS => [],
                self::SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS => []
            ];
        }

        try {
            $conversionName = str_replace('-', ' ', config('services.microsoft.ads.offline_conversion_goal_name'));

            $leadConversionInfo = $conversionRevenueInfo->toArray();

            $conversions = [];
            $adjustments = [];

            $successfulProductAssignmentIds = [];
            $successfulProductRejectionIds = [];
            $successfulConsumerProductTrackingIds = [];
            foreach($leadConversionInfo as $conversionInfo) {
                if($conversionInfo[self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE]) {
                    $adjustment = new OfflineConversionAdjustment();

                    $adjustment->AdjustmentCurrencyCode = 'USD';
                    $adjustment->AdjustmentTime = max($conversionInfo[self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP], $conversionInfo[self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP]);;
                    $adjustment->AdjustmentType = "Restate";
                    $adjustment->AdjustmentValue = round(max($conversionInfo[self::LEAD_REVENUE_INFO_REVENUE], 0), 2);

                    $adjustment->ConversionName = $conversionName;
                    $adjustment->ConversionTime = $conversionInfo[self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP];

                    $adjustment->MicrosoftClickId = $conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE];

                    $adjustments[] = $adjustment;
                }
                else {
                    $conversion = new OfflineConversion();

                    $conversion->ConversionCurrencyCode = 'USD';
                    $conversion->ConversionName = $conversionName;
                    $conversion->ConversionTime = max($conversionInfo[self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP], $conversionInfo[self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP]);
                    $conversion->ConversionValue = round(max($conversionInfo[self::LEAD_REVENUE_INFO_REVENUE], 0), 2);
                    $conversion->MicrosoftClickId = $conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE];

                    $conversions[] = $conversion;
                }

                if(!empty($conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS])) {
                    $successfulProductAssignmentIds = array_merge(
                        $successfulProductAssignmentIds,
                        array_map(
                            fn($id) => (int) $id,
                            $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS]
                        )
                    );
                }

                if(!empty($conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS])) {
                    $successfulProductRejectionIds = array_merge(
                        $successfulProductRejectionIds,
                        array_map(
                            fn($id) => (int) $id,
                            $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS]
                        )
                    );
                }

                if(empty($conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS])
                && empty($conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS])) {
                    $successfulConsumerProductTrackingIds[] = $conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID];
                }
            }

            $this->microsoftAdsClient->withAccountId($accountId);

            $campaignManagementService = new ServiceClient(
                ServiceClientType::CampaignManagementVersion13,
                $this->microsoftAdsClient,
                App::environment('production') ? ApiEnvironment::Production : ApiEnvironment::Sandbox
            );

            $this->applyConversions($conversions, $campaignManagementService);
            $this->applyAdjustments($adjustments, $campaignManagementService);

            return [
                self::SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS => array_unique($successfulProductAssignmentIds),
                self::SUCCESSFUL_PRODUCT_REJECTION_IDS => array_unique($successfulProductRejectionIds),
                self::SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS => array_unique($successfulConsumerProductTrackingIds)
            ];
        }
        catch(Throwable $e) {
            $errLocation = __METHOD__.': Line '.$e->getLine().'. ';

            logger()->error($errLocation.((string) $e));

            if($e instanceof SoapFault
            && isset($campaignManagementService)) {
                logger()->error($errLocation." ".$campaignManagementService->GetWsdl());
                logger()->error($errLocation." SOAP request -> ".$campaignManagementService->GetService()->__getLastRequest());
                logger()->error($errLocation." SOAP response -> ".$campaignManagementService->GetService()->__getLastResponse());
            }

            AdvertisingLoggingService::writeLog(
                "Offline conversions upload error. ".$errLocation.substr($e->getMessage(), 0, 255),
                [
                    'platform' => AdvertisingPlatform::MICROSOFT->value,
                    'trace' => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param array $conversions
     * @param $campaignManagementService
     * @return void
     */
    private function applyConversions(array $conversions, $campaignManagementService): void
    {
        if(!empty($conversions)) {
            $offlineConversionsRequest = new ApplyOfflineConversionsRequest();

            $offlineConversionsRequest->OfflineConversions = $conversions;

            $soapRes = $campaignManagementService->GetService()->ApplyOfflineConversions($offlineConversionsRequest);

            //handle partial errors
            if(!empty((array) $soapRes->PartialErrors)
                && !empty($soapRes->PartialErrors?->BatchError)) {
                foreach($soapRes->PartialErrors as $partialError) {
                    foreach($partialError as $batchError) {
                        AdvertisingLoggingService::writeLog("Offline conversions upload partial error", [
                            'platform' => AdvertisingPlatform::MICROSOFT->value,
                            'error' => (array) $batchError
                        ]);
                    }
                }
            }
        }
    }

    /**
     * @param array $adjustments
     * @param $campaignManagementService
     * @return void
     */
    private function applyAdjustments(array $adjustments, $campaignManagementService): void
    {
        if(!empty($adjustments)) {
            $conversionAdjustmentsRequest = new ApplyOfflineConversionAdjustmentsRequest();

            $conversionAdjustmentsRequest->OfflineConversionAdjustments = $adjustments;

            $soapRes = $campaignManagementService->GetService()->ApplyOfflineConversionAdjustments($conversionAdjustmentsRequest);

            //handle partial errors
            if(!empty((array) $soapRes->PartialErrors)
                && !empty($soapRes->PartialErrors?->BatchError)) {
                foreach($soapRes->PartialErrors as $partialError) {
                    foreach($partialError as $batchError) {
                        AdvertisingLoggingService::writeLog("Offline conversion adjustments partial error", [
                            'platform' => AdvertisingPlatform::MICROSOFT->value,
                            'error' => (array) $batchError
                        ]);
                    }
                }
            }
        }
    }
}
