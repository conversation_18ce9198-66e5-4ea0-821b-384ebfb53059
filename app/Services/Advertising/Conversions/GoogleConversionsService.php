<?php

namespace App\Services\Advertising\Conversions;

use App\Abstracts\Advertising\AdvertisingConversionsServiceAbstract;
use App\DataModels\Advertising\ConversionRevenueInfo;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Authentication\AdvertisingAuthServiceFactory;
use Exception;
use Google\Ads\GoogleAds\Lib\V19\GoogleAdsClient;
use Google\Ads\GoogleAds\Util\V19\GoogleAdsErrors;
use Google\Ads\GoogleAds\Util\V19\PartialFailures;
use Google\Ads\GoogleAds\Util\V19\ResourceNames;
use Google\Ads\GoogleAds\V19\Enums\ConversionAdjustmentTypeEnum\ConversionAdjustmentType;
use Google\Ads\GoogleAds\V19\Services\ClickConversion;
use Google\Ads\GoogleAds\V19\Services\ClickConversionResult;
use Google\Ads\GoogleAds\V19\Services\ConversionAdjustmentResult;
use Google\Ads\GoogleAds\V19\Services\GoogleAdsRow;
use Google\Ads\GoogleAds\V19\Services\Client\GoogleAdsServiceClient;
use Google\Ads\GoogleAds\V19\Services\ConversionAdjustment;
use Google\Ads\GoogleAds\V19\Services\RestatementValue;
use Google\Ads\GoogleAds\V19\Services\SearchGoogleAdsStreamRequest;
use Google\Ads\GoogleAds\V19\Services\UploadClickConversionsRequest;
use Google\Ads\GoogleAds\V19\Services\UploadConversionAdjustmentsRequest;
use Google\ApiCore\ApiException;
use Google\Protobuf\Internal\Message;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Throwable;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;

class GoogleConversionsService extends AdvertisingConversionsServiceAbstract
{
    const TRACK_TYPE_ADWORDS = 'adwords';
    const TRACK_TYPE_WBRAID = 'wbraid';
    const TRACK_TYPE_GOOGLE_ADS_IOS = 'google_ads_ios';
    const TRACK_TYPE_GBRAID = 'gbraid';

    const API_REQUEST_MAX_SIZE = 2000;

    private GoogleAdsClient $googleAdsClient;
    private GoogleAdsServiceClient $googleAdsServiceClient;

    public function __construct()
    {
        $this->googleAdsClient = AdvertisingAuthServiceFactory::make(AdvertisingPlatform::GOOGLE->value)->getClient(true);
        $this->googleAdsServiceClient = $this->googleAdsClient->getGoogleAdsServiceClient();
    }

    /** @inheritDoc */
    public function getTrackTypes(): array
    {
        return [
            self::TRACK_TYPE_ADWORDS,
            self::TRACK_TYPE_GOOGLE_ADS_IOS,
            self::TRACK_TYPE_WBRAID,
            self::TRACK_TYPE_GBRAID
        ];
    }

    /**
     * @param $accountId
     * @param string $conversionActionName
     * @return int|null
     * @throws \Google\ApiCore\ApiException
     */
    public function getConversionActionId($accountId, string $conversionActionName): ?int
    {
        $conversionActionId = null;

        $query = sprintf(
            "SELECT %s FROM %s WHERE %s AND %s AND %s",
            "conversion_action.id",
            "conversion_action",
            "conversion_action.type = 'UPLOAD_CLICKS'",
            "conversion_action.status = 'ENABLED'",
            "conversion_action.name = '{$conversionActionName}'"
        );

        $searchRes = $this->googleAdsServiceClient->searchStream(
            SearchGoogleAdsStreamRequest::build($accountId, $query)
        );

        /** @var GoogleAdsRow $googleAdsRow */
        foreach($searchRes->iterateAllElements() as $googleAdsRow) {
            $conversionActionId = $googleAdsRow->getConversionAction()->getId();

            break; //Only need the single action
        }

        return $conversionActionId;
    }

    /** @inheritDoc */
    public function uploadConversions(string $accountId, ConversionRevenueInfo $conversionRevenueInfo, AdvertiserEnum $advertiser): array
    {
        if($conversionRevenueInfo->isEmpty()) {
            AdvertisingLoggingService::writeLog(
                "No conversions to upload",
                [
                    'platform' => AdvertisingPlatform::GOOGLE->value,
                    'account' => $accountId
                ]
            );

            return [
                self::SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS => [],
                self::SUCCESSFUL_PRODUCT_REJECTION_IDS => [],
                self::SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS => []
            ];
        }

        try {
            AdvertisingLoggingService::writeLog(
                "Retrieving conversion action ID",
                [
                    'platform' => AdvertisingPlatform::GOOGLE->value,
                    'account' => $accountId
                ]
            );

            $conversionActionName = str_replace('-', ' ', config('services.google.ads.conversion_action_name'));

            $conversionActionId = $this->getConversionActionId($accountId, $conversionActionName);

            if(empty($conversionActionId)) {
                AdvertisingLoggingService::writeLog(
                    "Missing conversion action ID",
                    [
                        'platform' => AdvertisingPlatform::GOOGLE->value,
                        'account' => $accountId
                    ]
                );

                throw new Exception(__METHOD__.": Missing conversion action ID");
            }

            AdvertisingLoggingService::writeLog(
                "Preparing offline conversion upload payload",
                [
                    'platform' => AdvertisingPlatform::GOOGLE->value,
                    'account' => $accountId
                ]
            );

            $twentyFourHoursAgo = Carbon::now('UTC')->startOfHour()->subHours(24)->timestamp;

            $leadConversionInfo = $conversionRevenueInfo->toArray();

            $clickConversions = [];
            $conversionAdjustments = [];
            $offlineConversionsRecords = [];
            $conversionAdjustmentsRecords = [];
            foreach($leadConversionInfo as $conversionInfo) {
                $orderId = $conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID].'-'.$conversionInfo[self::LEAD_REVENUE_INFO_TRACK_TYPE];

                $operationTimestamp = max($conversionInfo[self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP], $conversionInfo[self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP]);

                $operationDateTime = Carbon::createFromTimestampUTC($operationTimestamp)->format("Y-m-d H:i:sP");

                if($conversionInfo[self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE]
                && $conversionInfo[self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP] <= $twentyFourHoursAgo) {
                    $conversionAdjustments[] = $this->buildConversionAdjustment(
                        $accountId,
                        $conversionActionId,
                        $operationDateTime,
                        $conversionInfo,
                        $orderId,
                        $conversionAdjustmentsRecords
                    );
                }
                else if(empty($conversionInfo[self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE])) {
                    $clickConversions[] = $this->buildClickConversion(
                        $accountId,
                        $conversionActionId,
                        $operationDateTime,
                        $conversionInfo,
                        $orderId,
                        $offlineConversionsRecords
                    );
                }
            }

            $successfulOps = [];
            $successfulProductAssignmentIds = [];
            $successfulProductRejectionIds = [];
            $successfulConsumerProductTrackingIds = [];

            foreach(array_chunk($clickConversions, self::API_REQUEST_MAX_SIZE) as $ccc) {
                $this->uploadClickConversions(
                    $ccc,
                    $offlineConversionsRecords,
                    $accountId,
                    $successfulOps,
                    $successfulProductAssignmentIds,
                    $successfulProductRejectionIds,
                    $successfulConsumerProductTrackingIds
                );
            }

            foreach(array_chunk($conversionAdjustments, self::API_REQUEST_MAX_SIZE) as $cac) {
                $this->uploadConversionAdjustments(
                    $cac,
                    $conversionAdjustmentsRecords,
                    $accountId,
                    $successfulOps,
                    $successfulProductAssignmentIds,
                    $successfulProductRejectionIds
                );
            }

            AdvertisingLoggingService::writeLog(
                "Offline conversion upload successful operations",
                [
                    'platform' => AdvertisingPlatform::GOOGLE->value,
                    'operations' => $successfulOps,
                    'account' => $accountId
                ]
            );

            return [
                self::SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS => array_unique($successfulProductAssignmentIds),
                self::SUCCESSFUL_PRODUCT_REJECTION_IDS => array_unique($successfulProductRejectionIds),
                self::SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS => array_unique($successfulConsumerProductTrackingIds)
            ];
        }
        catch(Throwable $e) {
            $errLocation = __METHOD__.': Line '.$e->getLine().'. ';

            logger()->error($errLocation.((string) $e));

            AdvertisingLoggingService::writeLog(
                "Offline conversions upload error. ".$errLocation.substr($e->getMessage(), 0, 255),
                [
                    'platform' => AdvertisingPlatform::GOOGLE->value,
                    'account' => $accountId,
                    'trace' => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param $accountId
     * @param int $conversionActionId
     * @param string $operationDateTime
     * @param array $conversionInfo
     * @param string $orderId
     * @param array $offlineConversionsRecords
     * @return ClickConversion
     * @throws Exception
     */
    private function buildClickConversion(
        $accountId,
        int $conversionActionId,
        string $operationDateTime,
        array $conversionInfo,
        string $orderId,
        array &$offlineConversionsRecords
    ): ClickConversion
    {
        $clickConversion = new ClickConversion([
            'conversion_action' => ResourceNames::forConversionAction($accountId, $conversionActionId),
            'conversion_value' => max($conversionInfo[self::LEAD_REVENUE_INFO_REVENUE], 0),
            'conversion_date_time' => $operationDateTime,
            'currency_code' => 'USD'
        ]);

        $clickConversion->setOrderId($orderId);

        if ($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_TYPE] === self::TRACK_TYPE_ADWORDS) {
            $clickConversion->setGclid($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE]);

            $offlineConversionsRecords[] = [
                "click_id" => self::TRACK_TYPE_ADWORDS.':'.$conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE],
                "time" => $operationDateTime,
                "product_assignment_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS]),
                "product_rejection_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS]),
                "consumer_product_tracking_id" => $conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID],
                "order_id" => $orderId
            ];
        }
        else if (in_array($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_TYPE], [self::TRACK_TYPE_GOOGLE_ADS_IOS, self::TRACK_TYPE_WBRAID], true)) {
            $clickConversion->setWbraid($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE]);

            $offlineConversionsRecords[] = [
                "click_id" => self::TRACK_TYPE_WBRAID.':'.$conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE],
                "time" => $operationDateTime,
                "product_assignment_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS]),
                "product_rejection_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS]),
                "consumer_product_tracking_id" => $conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID],
                "order_id" => $orderId
            ];
        }
        else if($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_TYPE] === self::TRACK_TYPE_GBRAID) {
            $clickConversion->setGbraid($conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE]);

            $offlineConversionsRecords[] = [
                "click_id" => self::TRACK_TYPE_GBRAID.':'.$conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE],
                "time" => $operationDateTime,
                "product_assignment_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS]),
                "product_rejection_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS]),
                "consumer_product_tracking_id" => $conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID],
                "order_id" => $orderId
            ];
        }
        else {
            throw new Exception(__METHOD__ . ": Invalid track code type");
        }

        return $clickConversion;
    }

    /**
     * @param $accountId
     * @param int $conversionActionId
     * @param string $operationDateTime
     * @param array $conversionInfo
     * @param string $orderId
     * @param array $conversionAdjustmentsRecords
     * @return ConversionAdjustment
     */
    private function buildConversionAdjustment(
        $accountId,
        int $conversionActionId,
        string $operationDateTime,
        array $conversionInfo,
        string $orderId,
        array &$conversionAdjustmentsRecords
    ): ConversionAdjustment
    {
        $conversionAdjustment = new ConversionAdjustment([
            'conversion_action' => ResourceNames::forConversionAction($accountId, $conversionActionId),
            'adjustment_type' => ConversionAdjustmentType::RESTATEMENT,
            'adjustment_date_time' => $operationDateTime,
            'order_id' => $orderId,
            'restatement_value' => new RestatementValue([
                'adjusted_value' => max($conversionInfo[self::LEAD_REVENUE_INFO_REVENUE], 0)
            ])
        ]);

        $trackType = $conversionInfo[self::LEAD_REVENUE_INFO_TRACK_TYPE];
        if($trackType === self::TRACK_TYPE_GOOGLE_ADS_IOS) {
            $trackType = self::TRACK_TYPE_WBRAID;
        }

        $conversionAdjustmentsRecords[] = [
            "click_id" => $trackType.':'.$conversionInfo[self::LEAD_REVENUE_INFO_TRACK_CODE],
            "time" => $operationDateTime,
            "product_assignment_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS]),
            "product_rejection_ids" => array_map(fn($id) => (int) $id, $conversionInfo[self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS]),
            "consumer_product_tracking_id" => $conversionInfo[self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID],
            "order_id" => $orderId
        ];

        return $conversionAdjustment;
    }

    /**
     * @param array $clickConversions
     * @param array $offlineConversionsRecords
     * @param string $accountId
     * @param array $successfulOps
     * @param array $successfulProductAssignmentIds
     * @param array $successfulProductRejectionIds
     * @param array $successfulConsumerProductTrackingIds
     * @return bool
     * @throws ApiException
     */
    private function uploadClickConversions(
        array $clickConversions,
        array $offlineConversionsRecords,
        string $accountId,
        array &$successfulOps,
        array &$successfulProductAssignmentIds,
        array &$successfulProductRejectionIds,
        array &$successfulConsumerProductTrackingIds
    ): bool
    {
        $uploadRes = $this->googleAdsClient->getConversionUploadServiceClient()->uploadClickConversions(
            UploadClickConversionsRequest::build($accountId, $clickConversions, true)
        );

        $results = $uploadRes->getResults();
        $resultsCount = count($results);
        if($uploadRes->hasPartialFailureError()) {
            $partialFailureErr = $uploadRes->getPartialFailureError();

            for($i = 0; $i < $resultsCount; $i++) {
                /** @var ClickConversionResult|Message $result */
                $result = $results[$i];

                if(PartialFailures::isPartialFailure($result)) {
                    $errors = GoogleAdsErrors::fromStatus($i, $partialFailureErr);

                    foreach($errors as $error) {
                        AdvertisingLoggingService::writeLog(
                            "Partial failure error during offline conversion upload - operation #{$i}",
                            [
                                'platform' => AdvertisingPlatform::GOOGLE->value,
                                'message' => $error->getMessage(),
                                'account' => $accountId,
                                'conversion_info' => $offlineConversionsRecords[$i]
                            ]
                        );
                    }
                }
                else {
                    if(empty($offlineConversionsRecords[$i])) {
                        continue;
                    }

                    $successfulOps[$i] = $this->extractUploadResId($result);

                    if(!empty($offlineConversionsRecords[$i]["product_assignment_ids"])
                    || !empty($offlineConversionsRecords[$i]["product_rejection_ids"])) {
                        $successfulProductAssignmentIds = array_merge($successfulProductAssignmentIds, $offlineConversionsRecords[$i]["product_assignment_ids"]);
                        $successfulProductRejectionIds = array_merge($successfulProductRejectionIds, $offlineConversionsRecords[$i]["product_rejection_ids"]);
                    }
                    else {
                        $successfulConsumerProductTrackingIds[] = $offlineConversionsRecords[$i]["consumer_product_tracking_id"];
                    }
                }
            }
        }
        else {
            for($i = 0; $i < $resultsCount; $i++) {
                if(empty($offlineConversionsRecords[$i])) {
                    continue;
                }

                $successfulOps[$i] = $this->extractUploadResId($results[$i]);

                $successfulProductAssignmentIds = array_merge($successfulProductAssignmentIds, $offlineConversionsRecords[$i]["product_assignment_ids"]);
                $successfulProductRejectionIds = array_merge($successfulProductRejectionIds, $offlineConversionsRecords[$i]["product_rejection_ids"]);
            }
        }

        return true;
    }

    /**
     * @param array $conversionAdjustments
     * @param array $conversionAdjustmentsRecords
     * @param string $accountId
     * @param array $successfulOps
     * @param array $successfulProductAssignmentIds
     * @param array $successfulProductRejectionIds
     * @return bool
     * @throws ApiException
     */
    private function uploadConversionAdjustments(
        array $conversionAdjustments,
        array $conversionAdjustmentsRecords,
        string $accountId,
        array &$successfulOps,
        array &$successfulProductAssignmentIds,
        array &$successfulProductRejectionIds
    ): bool
    {
        $uploadRes = $this->googleAdsClient->getConversionAdjustmentUploadServiceClient()->uploadConversionAdjustments(
            UploadConversionAdjustmentsRequest::build($accountId, $conversionAdjustments, true)
        );

        $results = $uploadRes->getResults();
        $resultsCount = count($results);
        if($uploadRes->hasPartialFailureError()) {
            $partialFailureErr = $uploadRes->getPartialFailureError();

            for($i = 0; $i < $resultsCount; $i++) {
                /** @var ConversionAdjustmentResult|Message $result */
                $result = $results[$i];

                if(PartialFailures::isPartialFailure($result)) {
                    $errors = GoogleAdsErrors::fromStatus($i, $partialFailureErr);

                    foreach($errors as $error) {
                        AdvertisingLoggingService::writeLog(
                            "Partial failure error during conversion adjustment upload - operation #{$i}",
                            [
                                'platform' => AdvertisingPlatform::GOOGLE->value,
                                'message' => $error->getMessage(),
                                'account' => $accountId,
                                'conversion_info' => $conversionAdjustmentsRecords[$i]
                            ]
                        );
                    }
                }
                else {
                    if(empty($conversionAdjustmentsRecords[$i]['click_id'])) {
                        continue;
                    }

                    $successfulOps[$i] = $conversionAdjustmentsRecords[$i]['click_id'];

                    $successfulProductAssignmentIds = array_merge($successfulProductAssignmentIds, $conversionAdjustmentsRecords[$i]["product_assignment_ids"]);
                    $successfulProductRejectionIds = array_merge($successfulProductRejectionIds, $conversionAdjustmentsRecords[$i]["product_rejection_ids"]);
                }
            }
        }
        else {
            for($i = 0; $i < $resultsCount; $i++) {
                if(empty($conversionAdjustmentsRecords[$i]['click_id'])) {
                    continue;
                }

                $successfulOps[$i] = $conversionAdjustmentsRecords[$i]['click_id'];

                $successfulProductAssignmentIds = array_merge($successfulProductAssignmentIds, $conversionAdjustmentsRecords[$i]["product_assignment_ids"]);
                $successfulProductRejectionIds = array_merge($successfulProductRejectionIds, $conversionAdjustmentsRecords[$i]["product_rejection_ids"]);
            }
        }

        return true;
    }

    /**
     * @param ClickConversionResult $result
     * @return string
     */
    private function extractUploadResId(ClickConversionResult $result): string
    {
        $id = '';
        $type = '';
        if($result->getGclid()) {
            $type = self::TRACK_TYPE_ADWORDS;
            $id = $result->getGclid();
        }
        else if($result->getGbraid()) {
            $type = self::TRACK_TYPE_GBRAID;
            $id = $result->getGbraid();
        }
        else if($result->getWbraid()) {
            $type = self::TRACK_TYPE_WBRAID;
            $id = $result->getWbraid();
        }

        return "{$type}:{$id}";
    }
}
