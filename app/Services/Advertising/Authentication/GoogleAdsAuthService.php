<?php

namespace App\Services\Advertising\Authentication;

use App\Contracts\Services\Advertising\AdvertisingAuthServiceContract;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Logging\GoogleAdsLogger;
use Google\Ads\GoogleAds\Lib\OAuth2TokenBuilder;
use Google\Ads\GoogleAds\Lib\V19\GoogleAdsClient;
use Google\Ads\GoogleAds\Lib\V19\GoogleAdsClientBuilder;
use Google\Auth\FetchAuthTokenInterface;
use Psr\Log\LogLevel;
use Throwable;

class GoogleAdsAuthService implements AdvertisingAuthServiceContract
{
    /**
     * @param bool|null $log
     * @return GoogleAdsClient
     * @throws Throwable
     */
    public function getClient(?bool $log = false): GoogleAdsClient
    {
        try {
            // Generate a refreshable OAuth2 credential for authentication.
            $oAuth2Credential = config('services.google.ads.auth_driver') === "service-account"
                ? $this->getCredentialUsingServiceAccountMode()
                : $this->getCredentialUsingApplicationMode();

            // Construct a Google Ads client configured from a properties file and the
            // OAuth2 credentials above.
            $builder = (new GoogleAdsClientBuilder());

            if($log) {
                $builder
                    ->withLogLevel(LogLevel::DEBUG)
                    ->withLogger(new GoogleAdsLogger());
            }

            return $builder
                ->withDeveloperToken(config('services.google.ads.developer_token'))
                ->withOAuth2Credential($oAuth2Credential)
                ->withLoginCustomerId(config('services.google.ads.login_customer_id'))
                ->build();
        }
        catch(Throwable $e) {
            AdvertisingLoggingService::writeLog(
                __METHOD__.": ".$e->getMessage(),
                [
                    "platform" => AdvertisingPlatform::GOOGLE->value
                ]
            );

            AdvertisingLoggingService::sendNotificationEmails(
                explode(',', config("services.ads.notification_emails")),
                "SolarReviews: Google Ads Authentication Error",
                "Google Ads authentication error. Automation is currently unable to proceed."
            );

            throw $e;
        }
    }

    /**
     * Uses the traditional method for authenticating w/ google ads.
     *
     * @return FetchAuthTokenInterface
     */
    private function getCredentialUsingApplicationMode(): FetchAuthTokenInterface
    {
        return (new OAuth2TokenBuilder())
            ->withClientId(config('services.google.ads.client_id'))
            ->withClientSecret(config('services.google.ads.client_secret'))
            ->withRefreshToken(config('services.google.ads.refresh_token'))
            ->build();
    }

    /**
     * Uses the service account to authenticate w/ the google cloud scope.
     *
     * @return FetchAuthTokenInterface
     */
    private function getCredentialUsingServiceAccountMode(): FetchAuthTokenInterface
    {
        return (new OAuth2TokenBuilder())
            ->withScopes("https://www.googleapis.com/auth/adwords")
            ->withImpersonatedEmail(config('services.google.ads.impersonated_email'))
            ->withJsonKeyFilePath("/google/oauth.json")
            ->build();
    }
}
