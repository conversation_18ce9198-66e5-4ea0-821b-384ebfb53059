<?php

namespace App\Services\Advertising\Authentication;

use App\Contracts\Services\Advertising\AdvertisingTokenAuthServiceContract;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Services\Advertising\AdvertisingLoggingService;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Microsoft\BingAds\Auth\ApiEnvironment;
use Microsoft\BingAds\Auth\AuthorizationData;
use Microsoft\BingAds\Auth\OAuthTokenRequestException;
use Microsoft\BingAds\Auth\OAuthTokens;
use Microsoft\BingAds\Auth\OAuthWebAuthCodeGrant;
use Ramsey\Uuid\Uuid;

class MicrosoftAdsAuthService implements AdvertisingTokenAuthServiceContract
{
    const MICROSOFT_ADS_AUTH_STATE = 'microsoft_ads_auth_state';
    const MICROSOFT_ADS_AUTH_CODE_VERIFIER = 'microsoft_ads_auth_code_verifier';

    /** @inheritDoc */
    public function getClient(): mixed
    {
        try {
            $authentication = (new OAuthWebAuthCodeGrant())
                ->withClientId(config('services.microsoft.ads.client_id'))
                ->withClientSecret(config('services.microsoft.ads.client_secret'))
                ->withEnvironment(App::environment('production') ? ApiEnvironment::Production : ApiEnvironment::Sandbox)
                ->withRedirectUri(config('services.microsoft.ads.oauth_redirect_uri'))
                ->withState(Uuid::uuid4()->toString());

            return (new AuthorizationData())
                        ->withAuthentication($authentication)
                        ->withCustomerId(config('services.microsoft.ads.customer_id'))
                        ->withDeveloperToken(config('services.microsoft.ads.developer_token'));
        }
        catch(Exception $e) {
            $errMsg = __METHOD__.": ".$e->getMessage();

            AdvertisingLoggingService::writeLog($errMsg, ['platform' => AdvertisingPlatform::MICROSOFT->value]);

            logger()->error($errMsg);

            AdvertisingLoggingService::sendNotificationEmails(
                explode(',', config("services.ads.notification_emails")),
                "SolarReviews: Microsoft Ads Authentication Error",
                "Microsoft Ads authentication error. Automation is currently unable to proceed. Please try re-authenticating from the SolarReviews advertising dashboard."
            );

            throw $e;
        }
    }

    /**
     * @param AuthorizationData $client
     * @return AuthorizationData
     * @throws OAuthTokenRequestException
     */
    public function attachOAuthTokensToClient(AuthorizationData &$client): AuthorizationData
    {
        try {
            $serviceId = ClientTokenService::query()
                ->where(ClientTokenService::FIELD_SERVICE_KEY, '=', ClientTokenService::MICROSOFT_ADS_API_SERVICE_KEY)
                ->first()
                ->{ClientTokenService::FIELD_ID};

            $currentOAuthTokens = ClientToken::query()
                ->where(ClientToken::FIELD_SERVICE_ID, '=', $serviceId)
                ->firstOrFail();

            $oAuthTokens = new OAuthTokens();

            $oAuthTokens->withAccessToken($currentOAuthTokens->{ClientToken::FIELD_CLIENT_TOKEN});
            $oAuthTokens->withRefreshToken($currentOAuthTokens->{ClientToken::FIELD_REFRESH_TOKEN} ?? null);
            $oAuthTokens->withAccessTokenExpiresInSeconds($currentOAuthTokens->{ClientToken::FIELD_EXPIRES_IN});

            $client->Authentication->withOAuthTokens($oAuthTokens);

            if($client->Authentication->OAuthTokens->RefreshToken) {
                /** @var OAuthTokens $latestOAuthTokens */
                $latestOAuthTokens = $client->Authentication->RequestOAuthTokensByRefreshToken($client->Authentication->OAuthTokens->RefreshToken);

                $now = Carbon::now();

                $currentOAuthTokens->{ClientToken::FIELD_CLIENT_TOKEN} = $latestOAuthTokens->AccessToken;
                $currentOAuthTokens->{ClientToken::FIELD_REFRESH_TOKEN} = $latestOAuthTokens->RefreshToken;
                $currentOAuthTokens->{ClientToken::FIELD_EXPIRES_IN} = $latestOAuthTokens->AccessTokenExpiresInSeconds;
                $currentOAuthTokens->{ClientToken::FIELD_CREATED_AT} = $now;
                $currentOAuthTokens->{ClientToken::FIELD_UPDATED_AT} = $now;

                $currentOAuthTokens->save();
            }

            return $client;
        }
        catch(Exception $e) {
            $errMsg = __METHOD__.": ".($e instanceof OAuthTokenRequestException ? $e->Error : $e->getMessage());

            AdvertisingLoggingService::writeLog($errMsg, ['platform' => AdvertisingPlatform::MICROSOFT->value]);

            logger()->error($errMsg);

            if($e instanceof OAuthTokenRequestException
            || $e instanceof ModelNotFoundException) {
                AdvertisingLoggingService::sendNotificationEmails(
                    explode(',', config("services.ads.notification_emails")),
                    "SolarReviews: Microsoft Ads Authentication Error",
                    "Microsoft Ads authentication error. Automation is currently unable to proceed. Please try re-authenticating from the SolarReviews advertising dashboard."
                );
            }

            throw $e;
        }
    }

    /** @inheritDoc */
    public function getTokenAuthorizationUri(): string
    {
        $client = $this->getClient();

        $authorizationUrl = $client->Authentication->GetAuthorizationEndpoint();

        $codeVerifier = Str::random(128);

        $codeChallenge = Str::base64UrlEncode(hash('sha256', $codeVerifier, true));

        $authorizationUrl .= "&code_challenge={$codeChallenge}&code_challenge_method=S256";

        $fiveMinutesInSecs = 5 * 60;

        Cache::put(self::MICROSOFT_ADS_AUTH_STATE, $client->Authentication->State, $fiveMinutesInSecs);
        Cache::put(self::MICROSOFT_ADS_AUTH_CODE_VERIFIER, $codeVerifier, $fiveMinutesInSecs);

        return $authorizationUrl;
    }

    /** @inheritDoc */
    public function getAccessTokens(?string $responseUri = null): bool
    {
        try {
            $parsedUrl = parse_url($responseUri);

            parse_str($parsedUrl["query"], $queryParts);

            $state = (string) $queryParts['state'] ?? null;

            $referenceState = Cache::pull(self::MICROSOFT_ADS_AUTH_STATE);
            $codeVerifier = Cache::pull(self::MICROSOFT_ADS_AUTH_CODE_VERIFIER);

            if($state !== $referenceState) {
                throw new Exception("OAuth response state ({$state}) does not match request state ({$referenceState})");
            }

            /** @var OAuthTokens $tokens */
            $tokens = $this
                        ->getClient()
                        ->Authentication
                        ->RequestOAuthTokensByResponseUri($responseUri, ['code_verifier' => $codeVerifier]);

            $serviceId = ClientTokenService::query()
                            ->where(ClientTokenService::FIELD_SERVICE_KEY, '=', ClientTokenService::MICROSOFT_ADS_API_SERVICE_KEY)
                            ->first()
                            ->{ClientTokenService::FIELD_ID};

            $now = Carbon::now();

            $clientToken = ClientToken::firstOrNew([ClientToken::FIELD_SERVICE_ID => $serviceId]);

            $clientToken->{ClientToken::FIELD_CLIENT_TOKEN} = $tokens->AccessToken;
            $clientToken->{ClientToken::FIELD_REFRESH_TOKEN} = $tokens->RefreshToken;
            $clientToken->{ClientToken::FIELD_EXPIRES_IN} = $tokens->AccessTokenExpiresInSeconds;
            $clientToken->{ClientToken::FIELD_CREATED_AT} = $now;
            $clientToken->{ClientToken::FIELD_UPDATED_AT} = $now;

            $clientToken->save();

            return true;
        }
        catch(Exception $e) {
            $errMsg = __METHOD__.": ".($e instanceof OAuthTokenRequestException ? $e->Error : $e->getMessage());

            AdvertisingLoggingService::writeLog($errMsg, ['platform' => AdvertisingPlatform::MICROSOFT->value]);

            logger()->error($errMsg);

            if($e instanceof OAuthTokenRequestException) {
                AdvertisingLoggingService::sendNotificationEmails(
                    explode(',', config("services.ads.notification_emails")),
                    "SolarReviews: Microsoft Ads Authentication Error",
                    "Microsoft Ads authentication error. Automation is currently unable to proceed. Please try re-authenticating from the SolarReviews advertising dashboard."
                );
            }
        }

        return false;
    }
}
