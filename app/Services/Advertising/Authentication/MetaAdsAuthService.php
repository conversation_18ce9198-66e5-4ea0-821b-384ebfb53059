<?php

namespace App\Services\Advertising\Authentication;

use App\Contracts\Services\Advertising\AdvertisingTokenAuthServiceContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Services\Advertising\AdvertisingLoggingService;
use Exception;
use FacebookAds\Api;
use FacebookAds\Http\RequestInterface;
use FacebookAds\Logger\CurlLogger;
use FacebookAds\Object\Business;
use FacebookAds\Object\Fields\SystemUserFields;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Throwable;

class MetaAdsAuthService implements AdvertisingTokenAuthServiceContract
{
    const APP_SECRET_TIME = 'appsecret_time';
    const APP_SECRET_PROOF = 'appsecret_proof';

    /**
     * @inheritDoc
     */
    public function getClient(?Advertiser $advertiser = null): mixed
    {
        try {
            if(empty($advertiser)) {
                throw new Exception("Missing advertiser");
            }

            if($advertiser === Advertiser::GABE) {
                $appId = config('services.meta.ads.app_id');
                $appSecret = config('services.meta.ads.app_secret');
            }
            else if($advertiser === Advertiser::WADE) {
                $appId = config('services.meta.wade_ads.app_id');
                $appSecret = config('services.meta.wade_ads.app_secret');
            }
            else {
                throw new Exception("Invalid advertiser");
            }

            $accessToken = ClientToken::query()
                ->whereHas(ClientToken::RELATION_CLIENT_TOKEN_SERVICE, function($has) {
                    $has->where(ClientTokenService::TABLE.'.'.ClientTokenService::FIELD_SERVICE_KEY, ClientTokenService::META_ADS_API_SERVICE_KEY);
                })
                ->where(ClientToken::FIELD_ADDITIONAL_DATA.'->'.ClientToken::ADDITIONAL_DATA_ADVERTISER, Advertiser::getKey($advertiser))
                ->firstOrFail()
                ->{ClientToken::FIELD_CLIENT_TOKEN};

            $client = Api::init(
                $appId,
                $appSecret,
                $accessToken ?? ''
            );

            $client->getHttpClient()->getAdapter()->getOpts()->offsetSet(CURLOPT_CONNECTTIMEOUT, 300);

            return $client;
        }
        catch(Throwable $e) {
            AdvertisingLoggingService::writeLog(
                __METHOD__.": ".$e->getMessage(),
                [
                    "platform" => AdvertisingPlatform::META->value
                ]
            );

            AdvertisingLoggingService::sendNotificationEmails(
                explode(',', config("services.ads.notification_emails")),
                "SolarReviews: Meta Ads Authentication Error",
                "Meta Ads authentication error. Automation is currently unable to proceed. Please confirm that system users are configured, the application is installed, and the admin system user token is correct."
            );

            throw $e;
        }
    }

    /**
     * @param Advertiser|null $advertiser
     * @return mixed
     * @throws Throwable
     */
    public function getAdCostClient(?Advertiser $advertiser = null): mixed
    {
        try {
            if(empty($advertiser)) {
                throw new Exception("Missing advertiser");
            }

            if($advertiser === Advertiser::GABE) {
                $appId = config('services.meta.ad_cost_api.gabe.app_id');
                $appSecret = config('services.meta.ad_cost_api.gabe.app_secret');
                $token = config('services.meta.ad_cost_api.gabe.token');
            }
            else if($advertiser === Advertiser::WADE) {
                $appId = config('services.meta.ad_cost_api.wade.app_id');
                $appSecret = config('services.meta.ad_cost_api.wade.app_secret');
                $token = config('services.meta.ad_cost_api.wade.token');
            }
            else {
                throw new Exception("Invalid advertiser");
            }

            $client = Api::init($appId, $appSecret, $token);

            $client->getHttpClient()->getAdapter()->getOpts()->offsetSet(CURLOPT_CONNECTTIMEOUT, 300);

            return $client;
        }
        catch(Throwable $e) {
            AdvertisingLoggingService::writeLog(
                __METHOD__.": ".$e->getMessage(),
                [
                    "platform" => AdvertisingPlatform::META->value
                ]
            );

            AdvertisingLoggingService::sendNotificationEmails(
                explode(',', config("services.ads.notification_emails")),
                "SolarReviews: Meta Ads Authentication Error",
                "Meta Ads authentication error. Automated Ad Cost data retrieval is currently unable to proceed. ".
                    "Please confirm that system users are configured, the application is installed, and the Ad Cost environment variables are correct.");

            throw $e;
        }
    }

    /**
     * @inheritDoc
     */
    public function getTokenAuthorizationUri(): string
    {
        /**
         * Authorization URI goes straight to the oauth redirect, since Meta doesn't require a human to login via the OAuth flow
         * when requesting system user access tokens
         */
        return route("advertising-oauth-redirect", ['platform' => AdvertisingPlatform::META->value]);
    }

    /**
     * @inheritDoc
     */
    public function getAccessTokens(?string $responseUri = null): bool
    {
        try {
            $adminSystemUserToken = config('services.meta.ads.admin_system_user_token');
            $appSecret = config('services.meta.ads.app_secret');

            $client = Api::init(
                config('services.meta.ads.app_id'),
                $appSecret,
                $adminSystemUserToken
            );

            if(!App::environment('production')) {
                $client->setLogger(new CurlLogger());
            }

            $businessId = config('services.meta.ads.business_id');

            $business = new Business($businessId, null, $client);
            $systemUsersRes = $business->getSystemUsers();

            $systemUserId = null;
            foreach($systemUsersRes as $sur) {
                if($sur->{SystemUserFields::ROLE} === "EMPLOYEE"
                && strtolower($sur->{SystemUserFields::NAME}) === strtolower(config('services.meta.ads.automation_system_user_name'))) {
                    $systemUserId = $sur->{SystemUserFields::ID};

                    break;
                }
            }

            if(empty($systemUserId)) {
                throw new Exception("Missing system user");
            }

            $appId = config('services.meta.ads.app_id');

            $accessTokenRes = $client->call(
                "/{$systemUserId}/access_tokens",
                RequestInterface::METHOD_POST,
                [
                    "business_app" => $appId,
                    "scope" => "ads_management",
                    "access_token" => $adminSystemUserToken
                ]
            );

            $body = json_decode($accessTokenRes->getBody() ?? '', true);

            if($accessTokenRes->getStatusCode() !== 200) {
                if($body
                && !empty($body["error"])) {
                    $type = $body['error']['type'] ?? '';
                    $message = $body['error']['message'] ?? '';
                    $code = $body['error']['code'] ?? 0;
                    $subCode = $body['error']['error_subcode'] ?? 0;

                    throw new Exception("Type: $type. Message: $message. Codes: $code - $subCode");
                }
                else {
                    throw new Exception("API call to retrieve access token failed");
                }
            }

            $clientTokenServiceId = ClientTokenService::query()
                                        ->where(ClientTokenService::FIELD_SERVICE_KEY, ClientTokenService::META_ADS_API_SERVICE_KEY)
                                        ->first()
                                        ->{ClientTokenService::FIELD_ID};

            $now = Carbon::now();

            $clientToken = ClientToken::firstOrNew([ClientToken::FIELD_SERVICE_ID => $clientTokenServiceId]);

            $clientToken->{ClientToken::FIELD_CLIENT_TOKEN} = $body['access_token'];
            $clientToken->{ClientToken::FIELD_REFRESH_TOKEN} = '';
            $clientToken->{ClientToken::FIELD_EXPIRES_IN} = 0;
            $clientToken->{ClientToken::FIELD_CREATED_AT} = $now;
            $clientToken->{ClientToken::FIELD_UPDATED_AT} = $now;

            $clientToken->save();

            return true;
        }
        catch(Throwable $e) {
            AdvertisingLoggingService::writeLog(
                __METHOD__.": ".$e->getMessage(),
                [
                    "platform" => AdvertisingPlatform::META->value
                ]
            );

            AdvertisingLoggingService::sendNotificationEmails(
                explode(',', config("services.ads.notification_emails")),
                "SolarReviews: Meta Ads Authentication Error",
                "Meta Ads authentication error. Automation is currently unable to proceed. Please confirm that system users are configured, the application is installed, and the admin system user token is correct."
            );
        }

        return false;
    }

    /**
     * @param string $accessToken
     * @param string $appSecret
     * @return array
     */
    public function generateAppSecretProof(
        string $accessToken,
        string $appSecret
    ): array
    {
        $appSecretTime = time();

        $appSecretProof = hash_hmac(
            'sha256',
            $accessToken.'|'.$appSecretTime,
            $appSecret
        );

        return [
            self::APP_SECRET_TIME => $appSecretTime,
            self::APP_SECRET_PROOF => $appSecretProof
        ];
    }
}
