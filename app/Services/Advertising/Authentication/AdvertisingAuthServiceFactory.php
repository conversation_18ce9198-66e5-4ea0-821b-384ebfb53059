<?php

namespace App\Services\Advertising\Authentication;

use App\Contracts\Services\Advertising\AdvertisingAuthServiceContract;
use App\Enums\Advertising\AdvertisingPlatform;
use Exception;

class AdvertisingAuthServiceFactory
{
    const PLATFORM_SERVICE = [
        [
            'platform' => AdvertisingPlatform::GOOGLE,
            'service' => GoogleAdsAuthService::class
        ],
        [
            'platform' => AdvertisingPlatform::MICROSOFT,
            'service' => MicrosoftAdsAuthService::class
        ],
        [
            'platform' => AdvertisingPlatform::META,
            'service' => MetaAdsAuthService::class
        ]
    ];

    /**
     * @param string $driver
     * @return AdvertisingAuthServiceContract
     * @throws Exception
     */
    public static function make(string $driver): AdvertisingAuthServiceContract
    {
        return match($driver) {
            AdvertisingPlatform::GOOGLE->value => app(GoogleAdsAuthService::class),
            AdvertisingPlatform::MICROSOFT->value => app(MicrosoftAdsAuthService::class),
            AdvertisingPlatform::META->value => app(MetaAdsAuthService::class),
            default => throw new Exception(__METHOD__.": Invalid driver")
        };
    }
}
