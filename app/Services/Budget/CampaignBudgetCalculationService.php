<?php

namespace App\Services\Budget;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Repositories\LeadCampaignRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class CampaignBudgetCalculationService
{

    const LEAD_TYPE_VERIFIED       = 'verified';
    const LEAD_TYPE_UNVERIFIED     = 'unverified';
    const LEAD_TYPE_EMAIL_ONLY     = 'email_only';
    const VERIFIED_SALES_TYPE_IDS  = [1, 2, 3, 4];
    const UNVERIFIED_SALES_TYPE_ID = 5;
    const EMAIL_ONLY_SALES_TYPE_ID = 6;
    const BUDGET_TYPE_NO_LIMIT     = 'no_limit';
    const BUDGET_TYPE_LEAD         = 'lead';
    const BUDGET_TYPE_SPEND        = 'spend';
    const BUDGET_FLOOR_LEAD        = 1;
    const BUDGET_FLOOR_SPEND       = 50;
    const OPTIONAL_SALES_TYPE_IDS  = [
        self::UNVERIFIED_SALES_TYPE_ID,
        self::EMAIL_ONLY_SALES_TYPE_ID
    ];

    public function __construct(protected LeadCampaignRepository $leadCampaignRepository) {}

    /**
     * returns % of budget currently utilized as a decimal
     *
     * @param LeadCampaign $campaign
     * @param string $leadType
     * @return float
     */
    public function getCurrentUtilization(LeadCampaign $campaign, string $leadType = self::LEAD_TYPE_VERIFIED): float
    {
        $dailySpendLimit = $this->getDailySpendLimit($campaign, $leadType);
        $dailyLeadLimit = $this->getDailyLeadLimit($campaign, $leadType);

        if($dailySpendLimit){
            $dailySpend = $this->getAverageDailySpend($campaign, $leadType);
            if($dailySpend == 0){return 0.00;}
            return $dailySpend / $dailySpendLimit;
        }else if($dailyLeadLimit){
            $dailyLeads = $this->getAverageDailyLeads($campaign, $leadType);
            if($dailyLeads == 0){return 0.00;}
            return $dailyLeads / $dailyLeadLimit;
        }else{
            return 0.00; // no limit budget
        }
    }

    public function getCurrentUtilizationForCampaigns(array $campaignIds, string $leadType = self::LEAD_TYPE_VERIFIED): array
    {
        $utilization = [];
        foreach ($campaignIds as $campaignId){
            $campaign = $this->leadCampaignRepository->find($campaignId);
            if($campaign){
                $utilization[$campaignId] = $this->getCurrentUtilization($campaign, $leadType);
            }
        }
        return $utilization;
    }

    /**
     * @param LeadCampaign $campaign
     * @param string $leadType
     * @return int|null
     */
    private function getDailySpendLimit(LeadCampaign $campaign, string $leadType): ?int
    {
        $limit = null;
        switch ($leadType) {
            case self::LEAD_TYPE_VERIFIED:
                $limit =  $campaign->max_daily_spend;
                break;
            case self::LEAD_TYPE_UNVERIFIED:
                $config = $campaign->leadSalesTypeConfigurations->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, self::UNVERIFIED_SALES_TYPE_ID)->first();
                $limit =  $config ? $config->max_daily_spend : null;
                break;
            case self::LEAD_TYPE_EMAIL_ONLY:
                $config = $campaign->leadSalesTypeConfigurations->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, self::EMAIL_ONLY_SALES_TYPE_ID)->first();
                $limit =  $config ? $config->max_daily_spend : null;
                break;
        }
        return $limit;
    }

    /**
     * @param LeadCampaign $campaign
     * @param string $leadType
     * @return int|null
     */
    private function getDailyLeadLimit(LeadCampaign $campaign, string $leadType): ?int
    {
        $limit = null;
        switch ($leadType) {
            case self::LEAD_TYPE_VERIFIED:
                $limit =  $campaign->max_daily_lead;
                break;
            case self::LEAD_TYPE_UNVERIFIED:
                $config = $campaign->leadSalesTypeConfigurations->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, self::UNVERIFIED_SALES_TYPE_ID)->first();
                $limit =  $config ? $config->max_daily_lead : null;
                break;
            case self::LEAD_TYPE_EMAIL_ONLY:
                $config = $campaign->leadSalesTypeConfigurations->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, self::EMAIL_ONLY_SALES_TYPE_ID)->first();
                $limit =  $config ? $config->max_daily_lead : null;
                break;
        }
        return $limit;
    }

    /**
     * @param LeadCampaign $campaign
     * @param string $leadType
     * @return float
     */
    public function getAverageDailySpend(LeadCampaign $campaign, string $leadType): float
    {
        $budgetStartTimestamp = $this->getStartingTimestamp($campaign);
        $quoteCompanies = $this->getSoldLeadsSinceTimestamp($campaign, $budgetStartTimestamp, $leadType);
        $totalSpend = $quoteCompanies->sum(EloquentQuoteCompany::COST);
        if($totalSpend == 0){
            return 0.00;
        }else{
            return $totalSpend / $this->daysSinceTimeStamp($budgetStartTimestamp);
        }
    }

    /**
     * @param LeadCampaign $campaign
     * @param string $leadType
     * @return float
     */
    public function getAverageDailyLeads(LeadCampaign $campaign, string $leadType): float
    {
        $budgetStartTimestamp = $this->getStartingTimestamp($campaign);
        $quoteCompanies = $this->getSoldLeadsSinceTimestamp($campaign, $budgetStartTimestamp, $leadType);
        $totalLeads = $quoteCompanies->count();
        if($totalLeads == 0){
            return 0.00;
        }else{
            return $totalLeads / $this->daysSinceTimeStamp($budgetStartTimestamp);
        }
    }

    /**
     * @param LeadCampaign|null $leadCampaign
     * @return int
     */
    private function getStartingTimestamp(LeadCampaign $leadCampaign): int
    {
        $startingTimestamp = 0;
        $thirtyDaysAgo = Carbon::today()->subDays(29)->timestamp;
        if ($leadCampaign->last_modified_lead_limit) {
            $startingTimestamp = $leadCampaign->last_modified_lead_limit->timestamp;
        }
        return max($startingTimestamp, $thirtyDaysAgo);
    }

    /**
     * @param LeadCampaign $campaign
     * @param int $timestamp
     * @param string|null $leadType
     * @return Collection
     */
    private function getSoldLeadsSinceTimestamp(LeadCampaign $campaign, int $timestamp, ?string $leadType = null): Collection
    {
        /** @var QuoteCompanyRepository $repository */
        $repository = app()->make(QuoteCompanyRepository::class);
        $quoteCompanies = $repository->getChargeableAndDeliveredLeadsForCampaign($campaign, $timestamp);

        if ($quoteCompanies->count() === 0){$quoteCompanies = collect();}

        // filter quote companies to specified lead type
        $salesTypeIds = [];
        if($leadType){
            switch ($leadType) {
                case self::LEAD_TYPE_VERIFIED:
                    $salesTypeIds = self::VERIFIED_SALES_TYPE_IDS;
                    break;
                case self::LEAD_TYPE_UNVERIFIED:
                    $salesTypeIds = [self::UNVERIFIED_SALES_TYPE_ID];
                    break;
                case self::LEAD_TYPE_EMAIL_ONLY:
                    $salesTypeIds = [self::EMAIL_ONLY_SALES_TYPE_ID];
                    break;
            }
            $quoteCompanies = $quoteCompanies->filter(function($quoteCompany) use($salesTypeIds) {
                return in_array($quoteCompany->leadCampaignSalesTypeConfiguration->lead_sales_type_id, $salesTypeIds);
            });
        }
        return $quoteCompanies;
    }

    /**
     * @param int $timestamp
     * @return int
     */
    private function daysSinceTimeStamp(int $timestamp): int
    {
        return floor((time() - $timestamp) / 86400) + 1; // this is being used in the legacy CalculateQuoteCompanyService
    }

    /**
     * @param int|null $maxDailyLeads
     * @param int|null $maxDailySpend
     * @return string
     */
    public function getBudgetType(?int $maxDailyLeads, ?int $maxDailySpend): string
    {
        if($maxDailyLeads){
            return self::BUDGET_TYPE_LEAD;
        }else if($maxDailySpend){
            return self::BUDGET_TYPE_SPEND;
        }else{
            return self::BUDGET_TYPE_NO_LIMIT;
        }
    }

}
