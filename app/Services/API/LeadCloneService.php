<?php

namespace App\Services\API;

use App\Enums\ConsumerProductChannel;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\RecycledLeads\RecycledLeadsRepository;
use App\Services\ConsumerRevalidationService;

class LeadCloneService
{
    public function __construct(protected RecycledLeadsRepository $recycledLeadsRepository, protected ConsumerRevalidationService $consumerRevalidationService) {}

    public function cloneLead(
        ConsumerProduct $consumerProduct,
        ConsumerProductChannel $channel = ConsumerProductChannel::AI_REVALIDATED_LEAD,
        bool $removeAffiliateRecord = true,
        bool $createInitialQueue = true,
        ?string $reason = null
    ): ConsumerProduct
    {
        $clonedConsumerProduct = $this->recycledLeadsRepository->cloneConsumerProduct($consumerProduct, $channel);

        if ($clonedConsumerProduct->consumerProductAffiliateRecord && $removeAffiliateRecord) {
            $clonedConsumerProduct->consumer_product_affiliate_record_id = null;
            $clonedConsumerProduct->save();
        }

        if ($createInitialQueue) {
            $this->consumerRevalidationService->addInInitialQueue($clonedConsumerProduct->consumer, $reason ?? 'AI revalidated aged lead');
        }

        return $clonedConsumerProduct;
    }
}
