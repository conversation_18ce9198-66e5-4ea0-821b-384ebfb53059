<?php

namespace App\Services;

use App\Repositories\UserFilterPresetRepository;

class UserFilterPresetService
{

    /**
     * UserFilterPresetService constructor.
     *
     * Initializes a new instance of the UserFilterPresetService class..
     *
     * @param UserFilterPresetRepository $userFilterPresetRepository
     */
    public function __construct(protected UserFilterPresetRepository $userFilterPresetRepository)
    {

    }

    /**
     * Takes care of adding and updating user settings.
     *
     * @param mixed $data
     * @param int $userId
     * @return bool
     */
    public function createOrUpdateFilterPreset(mixed $data, int $userId): bool
    {
        return $this->userFilterPresetRepository->createOrUpdateFilterPreset($data, $userId);
    }
}
