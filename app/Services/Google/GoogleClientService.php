<?php

namespace App\Services\Google;

use App\DTO\Mail\AccessTokenResponse;
use App\Models\GoogleUserToken;
use Google\Client;
use Exception;
use Illuminate\Support\Arr;

class GoogleClientService
{
    protected Client $client;

    public function __construct()
    {
        $this->client = new Client();
        $this->client->setApplicationName(config('services.google.application_name'));
        $this->client->setClientId(config('services.google.client_id'));
        $this->client->setClientSecret(config('services.google.client_secret'));
        $this->client->setRedirectUri(route('google-oauth-callback'));
        $this->client->setAccessType('offline');
        $this->client->setPrompt('consent');
        //        $this->client->setUseBatch($batch);
    }

    /**
     * @param int $userId
     * @param string $service
     * @param array $scopes
     * @return string
     */
    public function generateOAuthConsentScreenUrl(
        int $userId,
        string $service,
        array $scopes
    ): string
    {
        $this->client->setScopes($scopes);
        $this->client->setState(safeBase64Encode(json_encode([
            'user_id' => $userId,
            'service' => $service,
        ])));

        return $this->client->createAuthUrl();
    }

    /**
     * @param string $code
     * @return AccessTokenResponse
     */
    public function handleAuthCallback(string $code): AccessTokenResponse
    {
        $accessToken = $this->client->fetchAccessTokenWithAuthCode($code);

        return new AccessTokenResponse(
            accessToken : Arr::get($accessToken, 'access_token'),
            refreshToken: Arr::get($accessToken, 'refresh_token'),
        );
    }

    /**
     * @param GoogleUserToken $token
     * @return void
     * @throws Exception
     */
    public function setAccessToken(GoogleUserToken $token): void
    {
        $scopes = explode(' ', $token->{GoogleUserToken::FIELD_SCOPES});
        $this->client->setAccessToken($token->{GoogleUserToken::FIELD_TOKEN});
        $this->client->setScopes($scopes);

        if ($this->client->isAccessTokenExpired()) {
            $this->refreshToken($token->{GoogleUserToken::FIELD_REFRESH_TOKEN});
        }
    }

    /**
     * @param string $serviceClass
     * @return mixed
     * @throws Exception
     */
    public function getService(string $serviceClass)
    {
        if (!class_exists($serviceClass)) {
            throw new Exception("Invalid Google service: {$serviceClass}");
        }

        return new $serviceClass($this->client);
    }

    /**
     * @param string $refreshToken
     * @return void
     * @throws Exception
     */
    private function refreshToken(string $refreshToken): void
    {
        $tokenData = $this->client->fetchAccessTokenWithRefreshToken($refreshToken);

        $errorDescription = Arr::get($tokenData, 'error_description');
        $newToken = Arr::get($tokenData, 'access_token');

        if (filled($errorDescription)) {
            throw new Exception("Error refreshing token: $errorDescription");
        }

        $this->client->setAccessToken($newToken);
    }
}
