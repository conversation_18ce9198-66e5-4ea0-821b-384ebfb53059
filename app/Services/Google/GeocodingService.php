<?php

namespace App\Services\Google;

use App\DTO\Geocoding\GeocodingResponseDTO;
use App\Models\Odin\Address;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class GeocodingService
{
    const string GOOGLE_GEOCODE_API_URL = 'https://maps.googleapis.com/maps/api/geocode/json?';

    public function getGeocodeData(string $rawAddress): GeocodingResponseDTO
    {
        $response = Http::get(self::GOOGLE_GEOCODE_API_URL, urlencode($rawAddress))->throw()->json();

        if (!$response) {
            throw new Exception('Error retrieving geocoding');
        }

        return GeocodingResponseDTO::fromArray($response);
    }

    /**
     * @param Address $address
     * @param bool $throw
     *
     * @return array
     * @throws RequestException
     */
    public function getLatLngFromAddress(Address $address, bool $throw = true): array
    {
        $response = Http::get(self::GOOGLE_GEOCODE_API_URL, $this->buildRequestParamsForAddress($address));

        if ($throw)
            $response->throw();

        $response = $response->json();

        if (strtolower(Arr::get($response, 'status', '')) == 'ok') {
            return [
                Arr::get($response, 'results.0.geometry.location.lat', 0),
                Arr::get($response, 'results.0.geometry.location.lng', 0)
            ];
        }

        logger()->error("Failed to get lat lng for address ID: $address->id. Error: " . Arr::get($response, 'error_message', 'unknown'));

        return [0, 0];
    }

    /**
     * @param Address $address
     *
     * @return array
     */
    protected function buildRequestParamsForAddress(Address $address): array
    {
        return [
            'key'     => config('services.google.maps.geocoding_api_key'),
            'address' => collect([
                $address->address_1,
                $address->address_2,
                $address->city,
                $address->state,
                $address->country ?? 'US'
            ])->filter()->join(' ')
        ];
    }
}
