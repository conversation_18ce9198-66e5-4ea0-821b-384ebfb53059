<?php

namespace App\Services\Emails;

use App\DTO\EmailService\DomainDTO;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\Emails\DomainStatus;
use App\Exceptions\MarketingCampaign\EmailBatchSendFailedException;
use Illuminate\Support\Collection;

interface EmailServiceProviderContract
{
    /**
     * Send an email
     *
     * @param string $toEmail
     * @param string $fromEmail
     * @param string $subject
     * @param string $body
     * @param string $fromUserName
     * @return void
     */
    public function send(
        string $toEmail,
        string $fromEmail,
        string $subject,
        string $body,
        string $fromUserName
    ): void;

    /**
     * @param Collection<OutgoingEmailDTO> $outgoingEmails
     * @param string $subject
     * @param string $body
     * @param string $fromEmail
     * @param string $fromName
     * @return Collection
     */
    public function batchSend(
        Collection $outgoingEmails,
        string $subject,
        string $body,
        string $fromEmail,
        string $fromName,
    ): Collection;

    /**
     * @param Collection $outgoingEmails
     * @param string $subject
     * @param string $body
     * @param string $fromEmail
     * @param string $fromName
     * @param string|null $header
     * @param string|null $footer
     * @param int|null $templateId
     * @param int|null $backgroundId
     * @return bool
     * @throws EmailBatchSendFailedException
     */
    public function rawBatchSend(
        Collection $outgoingEmails,
        string     $subject,
        string     $body,
        string     $fromEmail,
        string     $fromName,
        ?string $header = null,
        ?string $footer = null,
        ?int $templateId = null,
        ?int $backgroundId  = null,
    ): bool;

    public function rawSend(
        OutgoingEmailDTO $outgoingEmail,
        string     $subject,
        string     $body,
        string     $fromEmail,
        string     $fromName,
        ?string $header = null,
        ?string $footer = null,
        ?int $templateId = null,
        ?int $backgroundId  = null,
    ): bool;

    /**
     * @param Collection<DomainStatus> $statuses
     * @return Collection<DomainDTO>
     */
    public function listSendingDomains(
        ?Collection $statuses = null,
        ?array $excludedDomains = [],
    ): Collection;
}



