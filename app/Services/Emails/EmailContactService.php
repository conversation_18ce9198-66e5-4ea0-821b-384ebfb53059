<?php

namespace App\Services\Emails;

use App\DTO\Mail\EmailContactDTO;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Support\Collection;

class EmailContactService
{
    /**
     * @return Collection<EmailContactDTO>
     */
    public function getEmailContacts(
        ?string $excludeEmail = null,
        ?int $companyId = null
    ): Collection
    {
        $contacts = $this->getStaffContacts(
            excludeEmails: collect($excludeEmail)->filter()
        );

        if (filled($companyId)) {
            $companyContacts = $this->getCompanyContactEmails(
                companyId: $companyId
            );

            $contacts->merge($contacts->diff($companyContacts));
        }

        return $contacts
            ->sortBy(fn(EmailContactDTO $emailContactDTO) => $emailContactDTO->getEmail())
            ->unique(fn(EmailContactDTO $emailContactDTO) => $emailContactDTO->getEmail())
            ->filter(fn(EmailContactDTO $emailContactDTO) => $emailContactDTO->getEmail() !== $excludeEmail)
            ->values();
    }

    /**
     * @param Collection $excludeEmails
     * @return Collection
     */
    public function getStaffContacts(Collection $excludeEmails = new Collection()): Collection
    {
        return User::query()
            ->when($excludeEmails->isNotEmpty(), fn($query) => $query->whereNotIn(User::FIELD_EMAIL, $excludeEmails))
            ->whereNull(User::FIELD_DELETED_AT)
            ->pluck(User::FIELD_EMAIL)
            ->filter()
            ->unique()
            ->map(fn(string $email) => new EmailContactDTO(
                email: $email,
            ));
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getCompanyContactEmails(int $companyId): Collection
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->whereNull(CompanyUser::FIELD_DELETED_AT)
            ->pluck(CompanyUser::FIELD_EMAIL)
            ->filter()
            ->unique()
            ->map(fn(string $email) => new EmailContactDTO(
                email: $email,
            ));
    }
}
