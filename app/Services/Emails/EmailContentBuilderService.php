<?php

namespace App\Services\Emails;

use App\Models\User;
use App\Services\Shortcode\ShortcodeReplacerService;
use Exception;
use Illuminate\Support\Str;

class EmailContentBuilderService
{
    public function __construct(
        protected ShortcodeReplacerService $replacerService
    )
    {

    }

    /**
     * @param User $user
     * @param string $content
     * @param array $shortcodeMap
     * @return string
     * @throws Exception
     */
    public function buildContent(
        User $user,
        string $content,
        array $shortcodeMap = []
    ): string
    {
        $content = $this->replacerService->process(
            rawText     : $content,
            shortcodeMap: $shortcodeMap
        );

        return $this->appendSignature($user, $content);
    }

    /**
     * @param User $user
     * @param string $content
     * @return string
     */
    public function appendSignature(User $user, string $content): string
    {
        $content .= $user->{User::FIELD_EMAIL_SIGNATURE} ?? '';

        return $content;
    }
}
