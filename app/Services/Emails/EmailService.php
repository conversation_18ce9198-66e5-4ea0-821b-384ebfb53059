<?php

namespace App\Services\Emails;

use App\Enums\Emails\EmailProvider;
use App\Services\EmailTemplates\EmailTemplateImageService;
use App\Services\EmailTemplates\EmailTemplateService;
use App\Services\Shortcode\ShortcodeReplacerService;
use Illuminate\Contracts\Container\BindingResolutionException;

class EmailService
{
    public function __construct(
        protected EmailTemplateService      $emailTemplateService,
        protected EmailTemplateImageService $emailTemplateImageService,
        protected ShortcodeReplacerService  $shortcodeReplacerService,
    )
    {}

    /**
     * @param string $toEmail
     * @param string $fromEmail
     * @param string $fromUserName
     * @param string $subject
     * @param string $body
     * @param EmailProvider $emailProvider
     * @return void
     * @throws BindingResolutionException
     */
    public function sendEmail(
        string        $toEmail,
        string        $fromEmail,
        string        $fromUserName,
        string        $subject,
        string        $body,
        EmailProvider $emailProvider = EmailProvider::DEFAULT,
    ): void
    {
        $emailProvider = EmailServiceProviderFactory::make($emailProvider);

        $emailProvider->send(
            toEmail     : $toEmail,
            fromEmail   : $fromEmail,
            subject     : $subject,
            body        : $body,
            fromUserName: $fromUserName,
        );
    }



}
