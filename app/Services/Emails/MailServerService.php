<?php

namespace App\Services\Emails;

use Exception;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;

class MailServerService
{
    public const string PRIMARY_MAIL_SERVER = 'smtp';
    public const string SECONDARY_MAIL_SERVER = 'smtp_secondary';
    public const string TERTIARY_MAIL_SERVER = 'smtp_tertiary';


    public function getDefaultMailServer(): string
    {
        return self::PRIMARY_MAIL_SERVER;
    }

    public function getMailServerList(): array
    {
        return [
            self::PRIMARY_MAIL_SERVER,
            self::SECONDARY_MAIL_SERVER,
            self::TERTIARY_MAIL_SERVER,
        ];
    }

    /**
     * Send emails using separate SMTP servers to minimize complaints and avoid IP blacklisting
     *
     * @param string $smtpConfigKey
     * @param $mailable
     * @param $to
     * @return bool
     * @throws Exception
     */
    public function sendEmail(string $smtpConfigKey, $mailable, $to = null): bool
    {
        if (!in_array($smtpConfigKey, $this->getMailServerList())) {
            throw new Exception("SMTP configuration '{$smtpConfigKey}' is not defined in available configurations");
        }

        $smtpConfig = config("mail.mailers.{$smtpConfigKey}");

        if (!$smtpConfig) {
            throw new \Exception("SMTP configuration '{$smtpConfigKey}' not found in config");
        }

        $originalMailer = config('mail.default');

        Config::set('mail.default', 'smtp');
        Config::set('mail.mailers.smtp', $smtpConfig);

        if ($to) {
            $sent = Mail::to($to)->send($mailable);
        } else {
            $sent = Mail::send($mailable);
        }

        Config::set('mail.default', $originalMailer);

        return (bool) $sent;
    }

}
