<?php

namespace App\Services\Emails;

use App\Enums\Emails\EmailProvider;
use Illuminate\Contracts\Container\BindingResolutionException;

class EmailServiceProviderFactory
{
    /**
     * @throws BindingResolutionException
     */
    static function make(EmailProvider $provider): EmailServiceProviderContract
    {
        return match ($provider) {
            EmailProvider::SOCKET_LABS => app()->make(SocketLabsEmailServiceProvider::class),
            default               => app()->make(DefaultEmailServiceProvider::class),
        };
    }

}