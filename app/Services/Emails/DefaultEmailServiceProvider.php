<?php

namespace App\Services\Emails;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\Services\ConfigurableEmailSenderService;
use Illuminate\Support\Collection;

class DefaultEmailServiceProvider implements EmailServiceProviderContract
{
    public function __construct(
        protected ConfigurableEmailSenderService $defaultEmailSenderService
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function send(
        string $toEmail,
        string $fromEmail,
        string $subject,
        string $body,
        string $fromUserName
    ): void
    {
        $this->defaultEmailSenderService->send(
            toEmail     : $toEmail,
            fromEmail   : $fromEmail,
            subject     : $subject,
            body        : $body,
            fromUserName: $fromUserName,
        );
    }

    /**
     * @param Collection $outgoingEmails
     * @param string $subject
     * @param string $body
     * @param string $fromEmail
     * @param string $fromName
     * @inheritDoc
     */
    public function batchSend(
        Collection $outgoingEmails,
        string $subject,
        string $body,
        string $fromEmail,
        string $fromName
    ): Collection
    {
        foreach ($outgoingEmails as $outgoingEmail) {
            $this->send(
                toEmail     : $outgoingEmail->getToEmail(),
                fromEmail   : $outgoingEmail->getFromEmail(),
                subject     : $outgoingEmail->getSubject(),
                body        : $outgoingEmail->getBody(),
                fromUserName: $outgoingEmail->getFromUsername(),
            );
        }

        return collect();
    }

    /**
     * @param Collection|null $statuses
     * @param array|null $excludedDomains
     * @inheritDoc
     */
    public function listSendingDomains(?Collection $statuses = null, ?array $excludedDomains = []): Collection
    {
        // TODO: Implement listSendingDomains() method.
        return collect();
    }

    public function rawBatchSend(Collection $outgoingEmails, string $subject, string $body, string $fromEmail, string $fromName, ?string $header = null, ?string $footer = null, ?int $templateId = null, ?int $backgroundId = null,): bool
    {
        // TODO: Implement rawBatchSend() method.
    }

    public function rawSend(OutgoingEmailDTO $outgoingEmail, string $subject, string $body, string $fromEmail, string $fromName, ?string $header = null, ?string $footer = null, ?int $templateId = null, ?int $backgroundId = null,): bool
    {
        // TODO: Implement rawSend() method.
    }
}
