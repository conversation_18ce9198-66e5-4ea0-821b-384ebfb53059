<?php

namespace App\Services;

use Illuminate\Support\Str;

class SlugService
{
    /**
     * @param string $modelToCheck
     * @param string $slugField
     * @param string $slugValue
     * @return string
     */
    public function createUniqueSlug(
        string $modelToCheck,
        string $slugField,
        string $slugValue
    ): string {
        $baseSlug = Str::slug($slugValue);
        $counter = 0;

        do {
            $slug = $counter ? "{$baseSlug}-{$counter}" : $baseSlug;
            $exists = $modelToCheck::query()->where($slugField, $slug)->exists();
            $counter++;
        } while ($exists);

        return $slug;
    }
}
