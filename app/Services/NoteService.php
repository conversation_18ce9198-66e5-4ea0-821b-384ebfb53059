<?php

namespace App\Services;

use App\DTO\Notes\CreateNoteParam;
use App\Enums\Notes\NoteRelationType;
use App\Models\Note;
use App\Repositories\NoteRepository;
use Illuminate\Database\Eloquent\Collection;

class NoteService
{
    public function __construct(protected NoteRepository $noteRepository)
    {

    }

    /**
     * @param int $relationId
     * @param NoteRelationType $relationType
     * @return Collection
     */
    public function getNotesForEntity(int $relationId, NoteRelationType $relationType): Collection
    {
        return $this->noteRepository->getNotesForEntity($relationId, $relationType);
    }

    /**
     * @param CreateNoteParam $createNoteParam
     * @return Note
     */
    public function createNote(CreateNoteParam $createNoteParam): Note
    {
        return $this->noteRepository->create($createNoteParam);
    }
}
