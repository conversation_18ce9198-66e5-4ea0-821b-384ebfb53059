<?php

namespace App\Services\FloorPrices;

use App\Models\PricingMargin;

class PricingMarginService
{
    const int DEFAULT_EXCLUSIVE_MARGIN = 200;
    const int DEFAULT_DUO_MARGIN = 165;
    const int DEFAULT_TRIO_MARGIN = 120;
    const int DEFAULT_QUAD_MARGIN = 100;

    /**
     * @param int $industryServiceId
     *
     * @return PricingMargin
     */
    public function findByIndustryServiceOrNew(int $industryServiceId): PricingMargin
    {
        /** @var PricingMargin */
        return PricingMargin::query()
            ->firstOrCreate([
                PricingMargin::FIELD_INDUSTRY_SERVICE_ID => $industryServiceId
            ], [
                PricingMargin::FIELD_EXCLUSIVE_MARGIN => self::DEFAULT_EXCLUSIVE_MARGIN,
                PricingMargin::FIELD_DUO_MARGIN => self::DEFAULT_DUO_MARGIN,
                PricingMargin::FIELD_TRIO_MARGIN => self::DEFAULT_TRIO_MARGIN,
                PricingMargin::FIELD_QUAD_MARGIN => self::DEFAULT_QUAD_MARGIN,
            ]);
    }

    /**
     * @param PricingMargin $pricingMargin
     * @param float $exclusiveMargin
     * @param float $duoMargin
     * @param float $trioMargin
     * @param float $quadMargin
     *
     * @return bool
     */
    public function update(PricingMargin $pricingMargin, float $exclusiveMargin, float $duoMargin, float $trioMargin, float $quadMargin): bool
    {
        $pricingMargin->exclusive_margin = $exclusiveMargin;
        $pricingMargin->duo_margin = $duoMargin;
        $pricingMargin->trio_margin = $trioMargin;
        $pricingMargin->quad_margin = $quadMargin;

        return $pricingMargin->save();
    }
}
