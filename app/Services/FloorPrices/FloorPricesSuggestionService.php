<?php

namespace App\Services\FloorPrices;

use App\DataModels\FloorPrices\SuggestedPriceDataModel;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\DailyAdCost;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\LocationRepository;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use function Laravel\Prompts\select;

class FloorPricesSuggestionService
{
    const string CONST_TOTAL = 'cost_total';
    const string TOTAL_LEADS = 'total_leads';

    public function __construct(protected LocationRepository $locationRepository, protected PricingMarginService $pricingMarginService) {}

    /**
     * @param Industry $industry
     * @param IndustryService $industryService
     * @param Location $stateLocation
     * @param Carbon $startDate
     * @param Carbon $endDate
     *
     * @return Collection
     */
    public function calculateFloorPriceSuggestionForStateAndIndustry(Industry $industry, IndustryService $industryService, Location $stateLocation, Carbon $startDate, Carbon $endDate): Collection
    {
        $countyLocations = $this->locationRepository->getCountiesInState($stateLocation->state_key);
        $pricingMargin = $this->pricingMarginService->findByIndustryServiceOrNew($industryService->id);

        $costData = $this->getCostDataForLocationsAndIndustry(
            $industry,
            [$stateLocation->id, ...$countyLocations->pluck(Location::ID)->toArray()],
            $startDate,
            $endDate
        )->toArray();

        $goodToSellLeadData = $this->getGoodToSellPaidLeadsByCountyInState($industry, $stateLocation, $startDate, $endDate);
        $totalStateLeads = $goodToSellLeadData->sum(self::TOTAL_LEADS);
        $stateAddCost = Arr::get($costData, "$stateLocation->id." . self::CONST_TOTAL, 0);
        $goodToSellLeadData = $goodToSellLeadData->toArray();

        $suggestedPrices = $countyLocations->map(fn(Location $location) => (new SuggestedPriceDataModel(
            pricingMargin: $pricingMargin,
            location: $location,
            adCost: Arr::get($costData, "$location->id." . self::CONST_TOTAL, 0),
            goodToSellLeads: Arr::get($goodToSellLeadData, "$location->county_key." . self::TOTAL_LEADS, 0),
        ))->calculatePrices()->toArray());

        return $suggestedPrices->push((new SuggestedPriceDataModel(
            pricingMargin: $pricingMargin,
            location: $stateLocation,
            adCost: $stateAddCost,
            goodToSellLeads: $totalStateLeads
        ))->calculatePrices()->toArray());
    }

    /**
     * @param IndustryService $industryService
     * @param Location $stateLocation
     * @param Product $product
     *
     * @return Collection
     */
    public function getCampaignStatistics(IndustryService $industryService, Location $stateLocation, Product $product): Collection
    {
        $campaigns =  $this->getBaseCampaignQuery()
            ->select([
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                Location::TABLE . '.' . Location::STATE_KEY,
                Location::TABLE . '.' . Location::COUNTY_KEY,
            ])
            ->where(Location::TABLE . '.' . Location::STATE_KEY, $stateLocation->state_key)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID, $industryService->id)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_PRODUCT_ID, $product->id)
            ->distinct()
            ->get();

        $stateData = $campaigns->pluck(CompanyCampaign::FIELD_ID)->unique()->count();

        return $campaigns->toBase()
            ->groupBy(Location::COUNTY_KEY)
            ->map(fn(Collection $locations) => $locations->count())
            ->put($stateLocation->state_key, $stateData);
    }

    /**
     * @param ServiceProduct $serviceProduct
     * @param Location $stateLocation
     * @param Carbon $startDate
     * @param Carbon $endDate
     *
     * @return Collection
     */
    public function getLegsSoldStatistics(ServiceProduct $serviceProduct, Location $stateLocation, Carbon $startDate, Carbon $endDate): Collection
    {
        $assignments = $this->getBaseLegsSoldQuery($serviceProduct, $startDate, $endDate)
            ->select([
                Location::TABLE . '.' . Location::COUNTY_KEY,
                DB::raw('COUNT('. ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID .') AS assignments'),
                DB::raw('COUNT(DISTINCT '.ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID . ') AS leads')
            ])
            ->where(Location::TABLE . '.' . Location::STATE_KEY, $stateLocation->state_key)
            ->groupBy(Location::TABLE . '.' . Location::COUNTY_KEY)
            ->get();

        $stateAverage = round($assignments->sum('assignments') / $this->getTotal($assignments->sum('leads')), 2);

        return $assignments->keyBy(Location::COUNTY_KEY)->map(fn($data) => round($data->assignments / $this->getTotal($data->leads), 2))
            ->put($stateLocation->state_key, $stateAverage);
    }

    /**
     * @param int $total
     *
     * @return int
     */
    protected function getTotal(int $total): int
    {
        if ($total > 0) {
            return $total;
        }

        return 1;
    }

    /**
     * @param ServiceProduct $serviceProduct
     * @param Carbon $startDate
     * @param Carbon $endDate
     *
     * @return Builder
     */
    protected function getBaseLegsSoldQuery(ServiceProduct $serviceProduct, Carbon $startDate, Carbon $endDate): Builder
    {
        return ConsumerProduct::query()
            ->join(
                ProductAssignment::TABLE,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
            )
            ->join(
                Address::TABLE,
                Address::TABLE . '.' . Address::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID
            )
            ->join(
                Location::TABLE,
                Location::TABLE . '.' . Location::ZIP_CODE,
                Address::TABLE . '.' . Address::FIELD_ZIP_CODE
            )
            ->where(    Location::TABLE . '.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereBetween(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, [$startDate, $endDate])
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID, $serviceProduct->id)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true);
    }

    /**
     * @return Builder
     */
    protected function getBaseCampaignQuery(): Builder
    {
        return CompanyCampaign::query()
            ->join(
                Company::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID
            )
            ->join(
                CompanyCampaignLocationModule::TABLE,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID
            )->join(
                CompanyCampaignLocationModuleLocation::TABLE,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_ID
            )
            ->join(
                Location::TABLE,
                Location::TABLE . '.' . Location::ID,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID
            )
            ->whereNull(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_DELETED_AT)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            ->where(Company::TABLE . '.' . Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS);
    }

    /**
     * @param Industry $industry
     * @param array $locationsIds
     * @param Carbon $startDate
     * @param Carbon $endDate
     *
     * @return Collection
     */
    protected function getCostDataForLocationsAndIndustry(Industry $industry, array $locationsIds, Carbon $startDate, Carbon $endDate): Collection
    {
        return DailyAdCost::query()
            ->select([
                DailyAdCost::FIELD_LOCATION_ID,
                DB::raw('SUM(' . DailyAdCost::FIELD_COST . ') AS ' . self::CONST_TOTAL)
            ])
            ->whereBetween(DailyAdCost::FIELD_DATE, [
                $startDate,
                $endDate
            ])
            ->whereIntegerInRaw(DailyAdCost::FIELD_LOCATION_ID, $locationsIds)
            ->where(DailyAdCost::FIELD_INDUSTRY_ID, $industry->id)
            ->groupBy(DailyAdCost::FIELD_LOCATION_ID)
            ->get()
            ->keyBy(DailyAdCost::FIELD_LOCATION_ID);
    }

    /**
     * @param Industry $industry
     * @param Location $stateLocation
     * @param Carbon $startDate
     * @param Carbon $endDate
     *
     * @return Collection
     */
    protected function getGoodToSellPaidLeadsByCountyInState(Industry $industry, Location $stateLocation, Carbon $startDate, Carbon $endDate): Collection
    {
        return $this->getBaseQuery($startDate, $endDate)
            ->select([
                Location::TABLE . '.' . Location::COUNTY_KEY,
                DB::raw('COUNT(*) AS ' . self::TOTAL_LEADS)
            ])
            ->where(IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID, $industry->id)
            ->where(Location::TABLE . '.' . Location::STATE_KEY, $stateLocation->state_key)
            ->whereNotNull(ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::AD_TRACK_TYPE)
            ->groupBy(Location::TABLE . '.' . Location::COUNTY_KEY)
            ->get()
            ->keyBy(Location::COUNTY_KEY);
    }


    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     *
     * @return Builder
     */
    protected function getBaseQuery(Carbon $startDate, Carbon $endDate): Builder
    {
        return ConsumerProduct::query()
            ->join(
                Address::TABLE,
                Address::TABLE . '.' . Address::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID
            )
            ->join(
                Location::TABLE,
                Location::TABLE . '.' . Location::ZIP_CODE,
                Address::TABLE . '.' . Address::FIELD_ZIP_CODE
            )->join(
                ServiceProduct::TABLE,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
            )->join(
                IndustryService::TABLE,
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID,
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID
            )
            ->join(
                ConsumerProductTracking::TABLE,
                ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID
            )
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->whereBetween(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, [
                $startDate,
                $endDate
            ]);
    }
}
