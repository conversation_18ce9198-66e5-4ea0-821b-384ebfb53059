<?php
namespace App\Services\CompanyConsumerReviews;

use App\Http\Requests\ConsumerReviews\CompanyConsumerReviewsRequest;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Repositories\CompanyConsumerReviewRepository\CompanyConsumerReviewRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class CompanyConsumerReviewService
{
    const int EMPTY_REVIEWS                 = 5;
    const float EMPTY_REVIEW_VALUE          = 3.5;
    const float LESS_THAN_ONE_YEAR_WEIGHT   = 1.0;
    const float ONE_TO_TWO_YEAR_WEIGHT      = 0.66;
    const float MORE_THAN_TWO_YEAR_WEIGHT   = 0.33;
    const int AVERAGE_PRECISION             = 2;

    public function __construct(
        protected CompanyConsumerReviewRepository $companyConsumerReviewRepository,
    ){}

    /**
     * Calculates an overall company rating from the Bayesian Average algorithm
     * @param Collection $reviews
     * @return float
     */
    public function calculateBayesianAvgCompanyRating(Collection $reviews) : float {
        $total_review_value = $this::EMPTY_REVIEWS * $this::EMPTY_REVIEW_VALUE;
        $total_review_divisor = $this::EMPTY_REVIEWS;

        foreach ($reviews as $review) {
            $reviewWeight = $this->getReviewWeightByDate($review[Review::FIELD_CREATED_AT]);
            $total_review_value += ($review->reviewData[ReviewData::FIELD_OVERALL_SCORE] * $reviewWeight);
            $total_review_divisor += $reviewWeight;
        }
        return round(($total_review_value / $total_review_divisor), $this::AVERAGE_PRECISION);
    }

    /**
     * @param Carbon $date
     * @return float
     */
    public function getReviewWeightByDate(Carbon $date) : float {
        if ($date->gt(now()->subYear())) {
            // Less than 12 months old
            return $this::LESS_THAN_ONE_YEAR_WEIGHT;
        } else if ($date->gt(now()->subYears(2))) {
            // 13-24 Months old
            return $this::ONE_TO_TWO_YEAR_WEIGHT;
        } else {
            // More than 24 Years old
            return $this::MORE_THAN_TWO_YEAR_WEIGHT;
        }
    }

    /**
     * @param Collection $filterData
     * @param $companyId
     * @return LengthAwarePaginator
     */
    public function getCompanyConsumerReviewsPaginated(Collection $filterData, $companyId): LengthAwarePaginator
    {
        $sortOrder = preg_match("/asc/i", $filterData[CompanyConsumerReviewsRequest::REQUEST_SORT_BY] ?? '')
            ? 'ASC'
            : 'DESC';
        return $this->companyConsumerReviewRepository->getAllConsumerReviews(
            $companyId,
            $sortOrder,
            $filterData[CompanyConsumerReviewsRequest::REQUEST_STATUS] ?? null,
            $filterData[CompanyConsumerReviewsRequest::REQUEST_SCORE] ?? null,
            $filterData[CompanyConsumerReviewsRequest::REQUEST_REVIEW_TEXT] ?? null,
        )->paginate(
            $filterData->get(CompanyConsumerReviewsRequest::REQUEST_PER_PAGE) ?? 10,
            columns: ['*'],
            pageName: 'page',
            page: $filterData->get(CompanyConsumerReviewsRequest::REQUEST_PAGE) ?? 1
        );
    }
}
