<?php

namespace App\Services\RoundRobins;

use App\Enums\RoundRobinType;
use App\Repositories\RoundRobins\RoundRobinRepository;
use App\Workflows\WorkflowPayload;

class RoundRobinService
{
    public function __construct(protected RoundRobinRepository $repository) {}

    /**
     * Performs the round-robin and returns the next id.
     *
     * @param RoundRobinType $type
     * @param bool $burn Whether to burn a users turn in the round-robin or not.
     * @return int|null
     */
    public function executeRoundRobin(RoundRobinType $type, bool $burn = true): ?int
    {
        $next = $this->repository->getNextUser($type);

        if($burn)
            $this->repository->updateRoundRobin($type, $next);

        return $next;
    }

    /**
     * @param WorkflowPayload|null $workflowPayload
     *
     * @return $this
     */
    public function setWorkflowPayload(?WorkflowPayload $workflowPayload): static
    {
        $this->repository->setWorkflowPayload($workflowPayload);

        return $this;
    }
}
