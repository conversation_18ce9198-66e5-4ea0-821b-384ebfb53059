<?php

namespace App\Services\CompanyProfile;

use App\DTO\CompanyProfile\CompanyProfileAddressDTO;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Repositories\CompanyProfile\CompanyProfileLocationRepository;
use App\Repositories\LocationRepository;
use Exception;

class CompanyProfileLocationService
{
    public function __construct(
        protected CompanyProfileLocationRepository $repository,
        protected LocationRepository               $locationRepository,
    )
    {
    }


    /**
     * @param CompanyProfile $profile
     * @param CompanyProfileAddressDTO $address
     * @return CompanyProfileLocation|null
     * @throws Exception
     */
    public function firstOrCreateProfileLocation(
        CompanyProfile           $profile,
        CompanyProfileAddressDTO $address,
    ): ?CompanyProfileLocation
    {
        if ($address->getPostCode()) {
            $location = $this->locationRepository->getCountyFromZipcode(
                zipCode: $address->getPostCode(),
            );
        } else if ($address->getCounty()) {
            $location = $this->locationRepository->getCounty(
                stateKey: $address->getState(),
                countyKey: $address->getCounty(),
            );
        } else {
            $location = $this->locationRepository->getStateByStateAbbr(
                stateAbbr: $address->getState()
            );
        }

        if (!$location) {
            throw new Exception('Unable to retrieve location');
        }

        return $this->repository->firstOrCreate(
            profile: $profile,
            location: $location,
            raw: $address->getRaw(),
        );
    }

}
