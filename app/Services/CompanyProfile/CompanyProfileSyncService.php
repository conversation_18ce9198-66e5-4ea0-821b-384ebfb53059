<?php

namespace App\Services\CompanyProfile;

use App\DTO\CompanyProfile\CompanyProfileDTO;
use App\Enums\AppFeature;
use App\Jobs\CompanyProfile\PromoteCompanyProfileJob;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\Legacy\Location;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\CompanyProfile\CompanyProfileRepository;
use App\Repositories\CompanyProfile\CompanyProfileReviewRepository;
use App\Repositories\CompanyProfile\CompanyProfileServiceAreaRepository;
use App\Repositories\LocationRepository;
use App\Services\AppLogger;
use Exception;
use Illuminate\Support\Str;

class CompanyProfileSyncService
{
    private AppLogger $logger;
    public function __construct(
        protected CompanyProfileRepository             $companyProfileRepository,
        protected CompanyProfileReviewRepository       $companyProfileReviewRepository,
        protected CompanyProfileLocationService        $companyProfileLocationService,
        protected CompanyProfileIndustryServiceService $companyProfileIndustryServiceService,
        protected CompanyProfileServiceAreaRepository  $companyProfileServiceAreaRepository,
        protected ActivityLogRepository                $activityLogRepository,
        protected LocationRepository                   $locationRepository
    )
    {
        $this->logger = AppLogger::make(
            feature: AppFeature::COMPANY_PROFILE,
        );
    }

    /**
     * @param CompanyProfileDTO $profileDTO
     * @return CompanyProfile
     */
    public function execute(CompanyProfileDTO $profileDTO): CompanyProfile
    {
        $profile = $this->identify(profileDTO: $profileDTO);

        $companyProfile = $this->save(profileDTO: $profileDTO, profile: $profile);

        // Only dispatch on creation
        if (!$profile) {
            PromoteCompanyProfileJob::dispatch($companyProfile);
        }

        return $companyProfile;
    }

    /**
     * @param CompanyProfileDTO $profileDTO
     * @return CompanyProfile|null
     */
    public function identify(CompanyProfileDTO $profileDTO): ?CompanyProfile
    {
        $domains = $profileDTO->getDomains();

        return empty($domains)
            ? null
            : $this->companyProfileRepository->find(domains: $domains);
    }

    /**
     * @param string $companyName
     * @param string $zipcode
     * @param string $suffix
     * @return string
     * @throws Exception
     */
    public function createUniqueCompanySlug(string $companyName, string $zipcode, string $suffix = ''): string
    {
        $zipLocation = $this->locationRepository->getZipCode($zipcode);

        if (!$zipLocation) {
            throw new Exception('Location for zipcode not found');
        }

        $nameParts = collect([
            $companyName,
            $zipLocation->{Location::STATE_ABBREVIATION},
            $zipLocation->{Location::COUNTY_KEY},
            $suffix
        ])->filter()
            ->map(fn (string $item) => Str::of($item)->trim()->lower()->replaceMatches('/[^a-zA-Z0-9]/', '-')->replaceMatches('/-+/', '-'))
            ->filter();

        if ($nameParts->count() < 3) {
            throw new Exception('Some part is missing to compose the slug');
        }

        $profileSlug = $nameParts->join('-');

        $dupeCount = CompanyProfile::query()->where(CompanyProfile::FIELD_PROFILE_SLUG, $profileSlug)->count();

        if ($dupeCount > 0) {
            return $this->createUniqueCompanySlug($companyName, $zipcode, $dupeCount);
        }

        // Apply final dash collapse to the entire result
        return preg_replace('/-+/', '-', $profileSlug);
    }

    /**
     * @param CompanyProfileDTO $profileDTO
     * @param CompanyProfile|null $profile
     * @return CompanyProfile
     * @throws Exception
     */
    public function save(CompanyProfileDTO $profileDTO, ?CompanyProfile $profile = null): CompanyProfile
    {
        $slug = filled($profile?->profile_slug)
            ? $profile->profile_slug
            : $this->createUniqueCompanySlug(
                companyName: $profileDTO->getName(),
                zipcode    : $profileDTO->getPrimaryAddress()->getPostCode()
            );

        $profile = $this->companyProfileRepository->update(
            profileDTO: $profileDTO,
            slug: $slug,
            companyProfile: $profile,
        );

        foreach ($profileDTO->getReviews() as $review) {
            $this->companyProfileReviewRepository->create(
                profile: $profile,
                companyProfileReviewDTO: $review
            );
        }

        $companyProfileIndustryService = $this->companyProfileIndustryServiceService->firstOrCreateProfileIndustryService(
            profile: $profile,
            industryServiceSlug: $profileDTO->getIndustryService(),
        );

        foreach ($profileDTO->getAddresses() as $address) {
            try {
                $profileLocation = $this->companyProfileLocationService->firstOrCreateProfileLocation(
                    profile: $profile,
                    address: $address
                );

                $this->companyProfileServiceAreaRepository->firstOrCreate(
                    profile: $profile,
                    profileLocation: $profileLocation,
                    profileIndustryService: $companyProfileIndustryService,
                );
            } catch (Exception $e) {
                $this->logger->error(message: $e->getMessage());
            }
        };

        return $profile;
    }
}
