<?php

namespace App\Services\CompanyProfile;

use App\DTO\SalesIntel\PersonDTO;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\BaseModel;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyService;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Services\AppLogger;
use App\Services\Google\GeocodingService;
use App\Services\SalesIntel\SalesIntelService;
use App\Services\WebsiteSanitizerService;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Nette\Schema\ValidationException;
use Throwable;

class CompanyProfilePromotionService
{
    protected AppLogger $logger;

    public function __construct(
        protected SalesIntelService $salesIntelService,
        protected WebsiteSanitizerService $websiteSanitizerService,
        protected CompanyRepository $companyRepository,
        protected AddressRepository $addressRepository,
        protected CompanyLocationRepository $companyLocationRepository,
        protected GeocodingService $geocodingService,
        protected LocationRepository $locationRepository
    )
    {
        $this->logger = AppLogger::make();
    }

    /**
     * @param CompanyProfile $companyProfile
     * @return Collection
     * @throws Throwable
     */
    public function promote(CompanyProfile $companyProfile): Collection
    {
        $this->logger = AppLogger::make(
            relations: [$companyProfile]
        );

        $logContext = [];

        try {
            // We fail the promotion if company is linked to company or prospect
            $this->validatePromotionEligibility($companyProfile);

            // Retrieve whitelisted websites and related models (by website)
            $validWebsites = $this->validateWebsites($companyProfile);
            $relatedModels = $this->getRelatedModels($validWebsites);

            if ($relatedModels->isNotEmpty()) {
                $this->linkPromotedEntities(
                    companyProfile: $companyProfile,
                    models        : $relatedModels
                );
            }

            $employees = $this->fetchEmployees($validWebsites);

            $logContext = $this->buildInitialLogContext(
                relatedModels: $relatedModels,
                employees    : $employees
            );

            $promotionResult = $this->executePromotion(
                companyProfile: $companyProfile,
                relatedModels : $relatedModels,
                employees     : $employees
            );

            $this->linkPromotedEntities($companyProfile, $promotionResult);

            $logContext['new_related_models'] = $this->mapRelatedModelsToLog($promotionResult);

            $this->logger->info('Promotion finished', $logContext);

            return $promotionResult;
        } catch (Throwable $throwable) {
            $this->logger->exception(
                exception: $throwable,
                context  : $logContext,
            );

            throw $throwable;
        }
    }

    /**
     * @param CompanyProfile $companyProfile
     * @return void
     * @throws Exception
     */
    protected function validatePromotionEligibility(CompanyProfile $companyProfile): void
    {
        if ($companyProfile->company || $companyProfile->newBuyerProspect) {
            throw new Exception('Company already promoted');
        }
    }


    /**
     * @param CompanyProfile $companyProfile
     * @return Collection
     */
    protected function validateWebsites(CompanyProfile $companyProfile): Collection
    {
        $websites = collect($companyProfile->websites);

        if ($websites->isEmpty()) {
            throw new ValidationException('Company profile websites is required');
        }

        $validWebsites = $this->websiteSanitizerService->filterOutBlacklistDomains($websites);

        if ($validWebsites->isEmpty()) {
            throw new ValidationException('Company profile does not have a valid website');
        }

        return $validWebsites;
    }

    /**
     * @param Collection $websites
     * @return Collection
     */
    protected function getRelatedModels(Collection $websites): Collection
    {
        return collect([
            $this->findExistingCompany($websites),
            $this->findExistingNewBuyerProspect($websites),
        ])->filter()->values();
    }

    /**
     * @param Collection $websites
     * @return Collection
     * @throws Exception
     */
    protected function fetchEmployees(Collection $websites): Collection
    {
        return $this->salesIntelService->findEmployees($websites);
    }

    /**
     * @param CompanyProfile $companyProfile
     * @param Collection $relatedModels
     * @param Collection $employees
     * @return Collection
     * @throws Exception
     */
    protected function executePromotion(
        CompanyProfile $companyProfile,
        Collection $relatedModels,
        Collection $employees
    ): Collection
    {
        $createdModels = collect();

        $company = $relatedModels->first(fn($model) => $model instanceof Company);

        // TODO - Do we want to avoid creating a company if it is a prospect?
        if (!$company && $employees->isNotEmpty()) {
            $company = $this->createCompany($companyProfile);
            $createdModels->push($company);
        }

        if ($relatedModels->isEmpty() && $employees->isEmpty()) {
            $newBuyerProspect = $this->createNewBuyerProspect($companyProfile);
            $createdModels->push($newBuyerProspect);
        }

        if ($company) {
            $this->createContacts($company, $employees);
        }

        return $createdModels;
    }

    /**
     * @param Company $reference
     * @param Collection $employees
     * @return void
     */
    protected function createContacts(Company $reference, Collection $employees): void
    {
        $existingEmails = CompanyUser::query()
            ->select('email')
            ->where(CompanyUser::FIELD_COMPANY_ID, $reference->id)
            ->pluck('email');

        $contactData = $employees->reject(fn(array $personData) => $existingEmails->search($personData['email']))
            ->map(function (array $personData) use ($reference) {
                return [
                    ...$personData,
                    'status'                      => 1,
                    'is_contact'                  => 1,
                    'import_source'               => CompanyUser::IMPORT_SOURCE_SALES_INTEL,
                    CompanyUser::FIELD_COMPANY_ID => $reference->id,
                    'created_at'                  => now(),
                    'updated_at'                  => now(),
                ];
            })->toArray();

        CompanyUser::query()->insert($contactData);
    }

    /**
     * @param CompanyProfile $companyProfile
     * @return NewBuyerProspect
     */
    protected function createNewBuyerProspect(CompanyProfile $companyProfile): NewBuyerProspect
    {
        $primaryLocation = $companyProfile->companyProfileLocations()->first();

        return NewBuyerProspect::query()->create([
            NewBuyerProspect::FIELD_REFERENCE            => Str::uuid()->toString(),
            NewBuyerProspect::FIELD_EXTERNAL_REFERENCE   => $companyProfile->id,
            NewBuyerProspect::FIELD_STATUS               => ProspectStatus::INITIAL,
            NewBuyerProspect::FIELD_INDUSTRY_SERVICE_IDS => $this->getIndustryServiceIds($companyProfile),
            NewBuyerProspect::FIELD_SOURCE               => ProspectSource::FIXR_DISCOVERY,
            NewBuyerProspect::FIELD_COMPANY_NAME         => $companyProfile->name,
            NewBuyerProspect::FIELD_COMPANY_WEBSITE      => $companyProfile->getPrimaryWebsite(),
            NewBuyerProspect::FIELD_COMPANY_DESCRIPTION  => $companyProfile->description,
            NewBuyerProspect::FIELD_COMPANY_PHONE        => $companyProfile->getPrimaryPhone(),
            NewBuyerProspect::FIELD_ADDRESS_STREET       => $primaryLocation?->{CompanyProfileLocation::FIELD_RAW},
            NewBuyerProspect::FIELD_ADDRESS_CITY_KEY     => $primaryLocation?->{CompanyProfileLocation::RELATION_LOCATION}->{Location::CITY_KEY},
            NewBuyerProspect::FIELD_ADDRESS_STATE_ABBR   => $primaryLocation?->{CompanyProfileLocation::RELATION_LOCATION}->{Location::STATE_ABBREVIATION},
            NewBuyerProspect::FIELD_ORDINAL_VALUE        => 9999 // Low priority
        ]);
    }

    /**
     * @param CompanyProfile $companyProfile
     * @return Company
     * @throws Exception
     */
    public function createCompany(CompanyProfile $companyProfile): Company
    {
        $company = $this->companyRepository->createCompanyFromAttributes([
            Company::FIELD_NAME    => $companyProfile->name,
            Company::FIELD_WEBSITE => $companyProfile->getPrimaryWebsite(),
        ]);

        $this->createCompanyLocations($companyProfile, $company);
        $this->createCompanyIndustryServices($companyProfile, $company);

        return $company;
    }

    /**
     * @param CompanyProfile $companyProfile
     * @param Company $company
     * @return void
     * @throws Exception
     */
    protected function createCompanyLocations(CompanyProfile $companyProfile, Company $company): void
    {
        $locations = $companyProfile->{CompanyProfile::RELATION_COMPANY_PROFILE_LOCATIONS};

        if ($locations->isEmpty()) {
            return;
        }

        foreach ($locations as $location) {
            $this->createCompanyLocation($location, $company);
        }
    }

    /**
     * @param CompanyProfileLocation $location
     * @param Company $company
     * @return void
     * @throws Exception
     */
    protected function createCompanyLocation(CompanyProfileLocation $location, Company $company): void
    {
        $geocodingResponse = $this->geocodingService->getGeocodeData($location->{CompanyProfileLocation::FIELD_RAW});
        $firstResult = $geocodingResponse->getFirstResult();

        if (!$firstResult) {
            throw new Exception('Address not found for location: ' . $location->{CompanyProfileLocation::FIELD_RAW});
        }

        $zipLocation = $this->locationRepository->getLocationIdsByZipcode($firstResult->getPostalCode());

        if (!$zipLocation) {
            throw new Exception('Zip code not found in database: ' . $firstResult->getPostalCode());
        }

        $address = $this->createAddressFromGeocodingResult($firstResult, $zipLocation);
        $this->createCompanyLocationFromAddress($company, $address);
    }

    /**
     * @param object $geocodingResult
     * @param array $zipLocation
     * @return Address
     */
    protected function createAddressFromGeocodingResult(object $geocodingResult, array $zipLocation): Address
    {
        return $this->addressRepository->createAddressFromAttributes([
            Address::FIELD_ADDRESS_1            => $geocodingResult->getStreetName(),
            Address::FIELD_CITY                 => $geocodingResult->getCity(),
            Address::FIELD_STATE                => $geocodingResult->getState(),
            Address::FIELD_ZIP_CODE             => $geocodingResult->getPostalCode(),
            Address::FIELD_COUNTRY              => $geocodingResult->getCountryCode(),
            Address::FIELD_LATITUDE             => $geocodingResult->getLatitude(),
            Address::FIELD_LONGITUDE            => $geocodingResult->getLongitude(),
            Address::FIELD_PLACE_ID             => $geocodingResult->place_id,
            Address::FIELD_IMPORTED             => true,
            Address::FIELD_COUNTY               => $geocodingResult->getCountyLongName(),
            Address::FIELD_ZIP_CODE_LOCATION_ID => $zipLocation['zip_code_location_id'],
            Address::FIELD_COUNTY_LOCATION_ID   => $zipLocation['county_location_id'],
            Address::FIELD_STATE_LOCATION_ID    => $zipLocation['state_location_id'],
        ]);
    }

    /**
     * @param Company $company
     * @param Address $address
     * @return CompanyLocation
     */
    protected function createCompanyLocationFromAddress(Company $company, Address $address): CompanyLocation
    {
        return $this->companyLocationRepository->updateOrCreateCompanyLocation([
            CompanyLocation::FIELD_COMPANY_ID => $company->id,
            CompanyLocation::FIELD_ADDRESS_ID => $address->id,
            CompanyLocation::FIELD_IMPORTED   => true,
        ]);
    }

    /**
     * @param CompanyProfile $companyProfile
     * @param Company $company
     * @return void
     */
    protected function createCompanyIndustryServices(CompanyProfile $companyProfile, Company $company): void
    {
        $serviceData = $companyProfile->industryServices->map(fn(IndustryService $service) => [
            CompanyService::FIELD_INDUSTRY_SERVICE_ID => $service->id,
            CompanyService::FIELD_COMPANY_ID          => $company->id,
            'created_at'                              => now(),
            'updated_at'                              => now(),
        ])->toArray();

        if (!empty($serviceData)) {
            CompanyService::query()->insert($serviceData);
        }
    }

    /**
     * @param CompanyProfile $companyProfile
     * @param Collection $models
     * @return void
     * @throws Exception
     */
    protected function linkPromotedEntities(CompanyProfile $companyProfile, Collection $models): void
    {
        foreach ($models as $model) {
            $this->linkToEntity($companyProfile, $model);
        }
    }

    /**
     * @param CompanyProfile $companyProfile
     * @param BaseModel $model
     * @return void
     * @throws Exception
     */
    protected function linkToEntity(CompanyProfile $companyProfile, BaseModel $model): void
    {
        $entityColumn = match ($model::class) {
            Company::class          => CompanyProfile::FIELD_COMPANY_ID,
            NewBuyerProspect::class => CompanyProfile::FIELD_NEW_BUYER_PROSPECT_ID,
            default                 => throw new Exception('Unknown entity type: ' . $model::class),
        };

        $companyProfile->update([$entityColumn => $model->id]);
    }

    /**
     * @param Collection $websites
     * @return Company|null
     */
    protected function findExistingCompany(Collection $websites): ?Company
    {
        return $websites->map(function (string $website) {
            return Company::query()
                ->where(Company::FIELD_WEBSITE, 'LIKE', "%$website%")
                ->first();
        })->flatten()->filter()->first();
    }

    /**
     * @param Collection $websites
     * @return NewBuyerProspect|null
     */
    protected function findExistingNewBuyerProspect(Collection $websites): ?NewBuyerProspect
    {
        return $websites->map(function (string $website) {
            return NewBuyerProspect::query()
                ->where(NewBuyerProspect::FIELD_COMPANY_WEBSITE, 'LIKE', "%$website%")
                ->first();
        })->flatten()->filter()->first();
    }

    /**
     * @param CompanyProfile $companyProfile
     * @return Collection
     */
    protected function getIndustryServiceIds(CompanyProfile $companyProfile): Collection
    {
        return $companyProfile->industryServices
            ->pluck(IndustryService::FIELD_ID);
    }

    /**
     * @param Collection $relatedModels
     * @param Collection $employees
     * @return array
     */
    protected function buildInitialLogContext(Collection $relatedModels, Collection $employees): array
    {
        return [
            'related_models' => $this->mapRelatedModelsToLog($relatedModels),
            'employees'      => $employees->toArray(),
        ];
    }

    /**
     * @param Collection $relatedModels
     * @return array
     */
    protected function mapRelatedModelsToLog(Collection $relatedModels): array
    {
        return $relatedModels->map(fn(BaseModel $model) => $model::class . ':' . $model->id)->toArray();
    }
}
