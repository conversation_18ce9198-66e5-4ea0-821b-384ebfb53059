<?php

namespace App\Services\CompanyProfile;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use App\Models\SaleType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class TopCompanyProfileService
{
    /**
     * @param string $industryServiceSlug
     * @param string|null $countryAbbreviation
     * @param string|null $stateAbbreviation
     * @param string|null $countyKey
     * @return Builder
     */
    public function getSortSubQuery(
        string $industryServiceSlug,
        ?string $countryAbbreviation = null,
        ?string $stateAbbreviation = null,
        ?string $countyKey = null,
    ): Builder
    {
        $locationVariant = $countyKey ? 'county' : 'state';
        $bidPricesTable = "product_" . $locationVariant . "_bid_prices";

        return CompanyCampaign::query()
            ->select([
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_NAME,
                "$bidPricesTable.price"
            ])
            ->join(CompanyCampaignBidPriceModule::TABLE, CompanyCampaignBidPriceModule::TABLE . '.company_campaign_id', CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID)
            ->join($bidPricesTable, $bidPricesTable . '.module_id', CompanyCampaignBidPriceModule::TABLE . '.' . CompanyCampaignBidPriceModule::FIELD_ID)
            ->join(SaleType::TABLE, SaleType::TABLE . '.' . SaleType::FIELD_ID, $bidPricesTable . '.sale_type_id')
            ->join(IndustryService::TABLE, IndustryService::TABLE . '.' . IndustryService::FIELD_ID, CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID)
            ->join(Location::TABLE, Location::TABLE . '.' . Location::ID, $bidPricesTable . '.' . $locationVariant . '_location_id')
            ->where(IndustryService::TABLE . '.' . IndustryService::FIELD_SLUG, $industryServiceSlug)
            ->where(CompanyCampaign::FIELD_STATUS, 2) // only active campaigns ?
            ->when($stateAbbreviation, fn($query) => $query->where(Location::TABLE . '.' . Location::STATE_ABBREVIATION, $stateAbbreviation))
            ->when($countyKey, fn($query) => $query->where(Location::TABLE . '.' . Location::COUNTY_KEY, $countyKey))
            ->where(SaleType::TABLE . '.' . SaleType::FIELD_KEY, 'exclusive')
            ->groupBy(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID)
            ->orderByDesc("$bidPricesTable.price")
            ->limit(10);
    }

    /**
     * @param string $industryServiceSlug
     * @param string|null $countryAbbreviation
     * @param string|null $stateAbbreviation
     * @param string|null $countyKey
     * @param array|null $prioritizedCompanyIds
     * @return Collection
     */
    public function listTopCompanyProfiles(
        string $industryServiceSlug,
        ?string $countryAbbreviation = null,
        ?string $stateAbbreviation = null,
        ?string $countyKey = null,
        ?array $prioritizedCompanyIds = []
    ): Collection
    {
        $query = CompanyProfile::query()
            ->select([
                CompanyProfile::TABLE . '.*',
                'sub.name as campaign_name',
                'sub.price as price',
            ])
            ->leftJoinSub($this->getSortSubQuery(
                industryServiceSlug: $industryServiceSlug,
                countryAbbreviation: $countryAbbreviation,
                stateAbbreviation  : $stateAbbreviation,
                countyKey          : $countyKey,
            ), 'sub', 'sub.company_id', CompanyProfile::TABLE . '.' . CompanyProfile::FIELD_COMPANY_ID)
            ->leftJoin(CompanyProfileLocation::TABLE, CompanyProfile::TABLE . '.' . CompanyProfile::FIELD_ID, CompanyProfileLocation::TABLE . '.' . CompanyProfileLocation::FIELD_COMPANY_PROFILE_ID)
            ->leftJoin(Location::TABLE, Location::TABLE . '.' . Location::ID, CompanyProfileLocation::TABLE . '.' . CompanyProfileLocation::FIELD_LOCATION_ID)
            ->when($industryServiceSlug, function ($query) use ($industryServiceSlug) {
                $query->whereHas(CompanyProfile::RELATION_INDUSTRY_SERVICES, function ($query) use ($industryServiceSlug) {
                    $query->where(IndustryService::FIELD_SLUG, $industryServiceSlug);
                });
            })
            ->when($stateAbbreviation, fn($query) => $query->where(Location::TABLE . '.' . Location::STATE_ABBREVIATION, Str::upper($stateAbbreviation)))
            ->when($countyKey, fn($query) => $query->where(Location::TABLE . '.' . Location::COUNTY_KEY, $countyKey))
            ->orderByDesc(CompanyProfile::TABLE . '.' . CompanyProfile::FIELD_RATING)
            ->groupBy(CompanyProfile::TABLE . '.' . CompanyProfile::FIELD_ID)
            ->limit(10);

        if (filled($prioritizedCompanyIds)) {
            $idsString = collect($prioritizedCompanyIds)->join(',');

            $query->orderByRaw("
                CASE
                    WHEN company_profiles.company_id IN ($idsString) THEN 0
                    ELSE 1
                END
            ")
                ->orderByRaw("FIELD(company_profiles.company_id, $idsString)");
        }

        $query->orderByDesc('sub.price')
            ->orderByDesc(CompanyProfile::TABLE . '.' . CompanyProfile::FIELD_RATING);

        return $query->get();
    }
}
