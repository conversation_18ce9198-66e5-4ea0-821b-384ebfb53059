<?php

namespace App\Services\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Repositories\CompanyProfile\CompanyProfileIndustryServiceRepository;
use App\Repositories\Odin\IndustryServiceRepository;

class CompanyProfileIndustryServiceService
{
    public function __construct(
        protected CompanyProfileIndustryServiceRepository $companyProfileIndustryServiceRepository,
        protected IndustryServiceRepository               $industryServiceRepository
    )
    {
    }

    /**
     * @param CompanyProfile $profile
     * @param string $industryServiceSlug
     * @return CompanyProfileIndustryService
     */
    public function firstOrCreateProfileIndustryService(CompanyProfile $profile, string $industryServiceSlug): CompanyProfileIndustryService
    {
        $industryService = $this->industryServiceRepository->getIndustryServiceBySlug(slug: $industryServiceSlug);

        return $this->companyProfileIndustryServiceRepository->firstOrCreate(
            profile: $profile,
            industryService: $industryService,
        );
    }
}
