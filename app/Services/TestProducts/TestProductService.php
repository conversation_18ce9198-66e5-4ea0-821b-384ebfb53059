<?php

namespace App\Services\TestProducts;

use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Campaigns\CampaignFilterOperator;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Enums\TestProductStatus;
use App\Jobs\DispatchPubSubEvent;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignFilter;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\OptInCompany;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Models\TestProduct;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\Campaigns\ProductBiddingService;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\Odin\ConsumerProductService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;

class TestProductService
{
    const int DEFAULT_EXPIRATION_GAP_IN_DAYS = 14;

    public function __construct(
        protected TestProductGeneratorService $generatorService,
        protected LeadDeliveryService         $leadDeliveryService,
        protected ConsumerProductRepository   $consumerProductRepository,
        protected ProductBiddingService       $productBiddingService,
        protected CampaignMediator            $campaignMediator,
        protected ConsumerProductService      $consumerProductService,
        protected GlobalConfigurationRepository $globalConfigurationRepository
    ) {}

    /**
     * Handles generating a test product for a given company.
     *
     * @param Company $company
     * @param IndustryService $service
     * @param Carbon $revealAt
     * @param ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign
     * @param Carbon|null $expireAt
     * @param int|null $userId
     * @return TestProduct|null
     * @throws \Exception|\GuzzleHttp\Exception\GuzzleException
     */
    public function generateProduct(
        Company $company,
        IndustryService $service,
        Carbon $revealAt,
        ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign,
        ?Carbon $expireAt = null,
        ?int $userId = null
    ): TestProduct | string
    {
        if(!isset($campaign)) {
            return "Unable to retrieve Campaign";
        }
        /** @var ConsumerProduct|null $product */
        $product = $this->generatorService->generateInternalProduct($company, $service, $campaign);
        if(!$product) {
            return "Unable to generate Internal Product.";
        }

        $this->createOptIn(
            consumerProduct: $product,
            company: $company,
            campaignId: $campaign->id
        );


        $testProduct = new TestProduct();
        $testProduct->product_id = $product->id;
        $testProduct->company_id = $company->id;
        $testProduct->relation_id = $campaign->id;
        $testProduct->relation_type = $campaign::class;
        $testProduct->email = $product->consumer->email;
        $testProduct->phone = $product->consumer->phone;
        $testProduct->status = TestProductStatus::INITIAL;
        $testProduct->reveal_at = $revealAt;
        $testProduct->expire_at = $expireAt ?: $this->calculateExpireDate($revealAt);
        $testProduct->created_by_id = $userId;
        $this->updateTestProductForCampaignFilters($campaign, $testProduct);
        $testProduct->save();

        DispatchPubSubEvent::dispatch(EventCategory::TEST_LEADS, EventName::TEST_LEAD_GENERATED, $this->getEventData($testProduct));

        return $testProduct;
    }

    /**
     *
     * Ensure Generated test leads match the campaign filters
     * @todo must be revised as soon as filters are updated to include types
     *
     * @param CompanyCampaign $campaign
     * @param TestProduct $testProduct
     * @return TestProduct
     */
    protected function updateTestProductForCampaignFilters(CompanyCampaign $campaign, TestProduct $testProduct): void
    {
        $payloadUpdates = [];

        $campaign->filters->each(function($filter) use (&$payloadUpdates) {
            /** @var CompanyCampaignFilter $filter */
            switch($filter->operator) {
                case CampaignFilterOperator::EQ:
                case CampaignFilterOperator::GTE:
                case CampaignFilterOperator::LTE:
                    $payloadUpdates[$filter->key] = $filter->value;
                    break;
                case CampaignFilterOperator::LT:
                    $payloadUpdates[$filter->key] = (int) $filter->value - 1;
                    break;
                case CampaignFilterOperator::NET:
                    if (!is_numeric($filter->value)) {
                        $payloadUpdates[$filter->key] = "--";
                    } else {
                        $payloadUpdates[$filter->key] = (int) $filter->value + 1;
                    }
                    break;
                case CampaignFilterOperator::GT:
                    $payloadUpdates[$filter->key] = (int) $filter->value + 1;
                    break;
                default:
                break;
            }
        });

        if (!empty($payloadUpdates)) {
            $this->consumerProductRepository->updateConsumerProductData($testProduct->product, $payloadUpdates);
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Company $company
     * @param int $campaignId
     *
     * @return void
     */
    protected function createOptIn(
        ConsumerProduct $consumerProduct,
        Company $company,
        int $campaignId,
    ): void
    {
        $consumerProduct->optInCompanies()->create([
            OptInCompany::FIELD_COMPANY_ID => $company->id,
            OptInCompany::FIELD_COMPANY_CAMPAIGN_ID => $campaignId
        ]);
    }

    /**
     * @param Carbon $revealAt
     * @return Carbon
     */
    protected function calculateExpireDate(Carbon $revealAt): Carbon
    {
        $config = $this->globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::TEST_PRODUCTS_EXPIRATION_GAP_IN_DAYS);

        $days = $config?->data?->get('days') ?? self::DEFAULT_EXPIRATION_GAP_IN_DAYS;

        return $revealAt->addDays($days);
    }

    /**
     * @param TestProduct $product
     * @param ProductCampaign|LeadCampaign|null $campaign
     * @return array|null
     * @throws BindingResolutionException
     */
    protected function deliverProductAndLeadCampaign(
        TestProduct $product,
        ProductCampaign|LeadCampaign|null $campaign
    ): ?array
    {
        if (!isset($campaign)) {
            return null;
        }

        $configuration = match (true) {
            $campaign instanceof LeadCampaign       => $campaign->leadSalesTypeConfigurations->filter(fn($item) => $item->lead_sales_type_id === 1)->first(),
            $campaign instanceof ProductCampaign    => $campaign->legacyCampaign->leadSalesTypeConfigurations->filter(fn($item) => $item->lead_sales_type_id === 1)->first(),
            default                                 => null
        };

        if (!isset($configuration)) {
            return null;
        }

        $product->status = TestProductStatus::DELIVERED;
        $product->relation_id = $campaign->id;
        $product->relation_type = $campaign::class;
        $product->save();

        $product->product->status = ConsumerProduct::STATUS_ALLOCATED;
        $product->product->save();

        $result = $this->leadDeliveryService->saveAndDeliverQuoteCompany(
            $product->product->consumer->legacyLead->uuid,
            [
                [
                    'companyid' => $product->company->legacy_id,
                    'lead_sales_type_configuration_id' => $configuration->id,
                    'chargeable' => 0,
                    'non_budget_premium_sale' => false,
                    'price' => 0 // Test leads shouldn't have cost by default
                ]
            ],
            1,
            false,
            "Test Product - Do Not Touch"
        );

        return $this->processLegacyDeliveryResult($result);
    }

    /**
     * Handles delivering a test product to the given company.
     *
     * @param TestProduct $product
     * @param ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign
     * @return ProductAssignment|null
     * @throws BindingResolutionException
     * @throws \Exception
     */
    public function deliverProduct(
        TestProduct $product,
        ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign,
    ): ?array
    {
        if (!isset($campaign)) {
            $campaigns = $product->company
                ->productCampaigns()
                ->where(ProductCampaign::FIELD_INDUSTRY_SERVICE_ID, $product->product->industryService->id)
                ->where(ProductCampaign::FIELD_STATUS, true)
                ->with([ProductCampaign::RELATION_LEGACY_CAMPAIGN . '.' . LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS])
                ->get();

            $campaign = $campaigns->filter(fn(ProductCampaign $campaign) => $campaign->legacyCampaign->leadCampaignZipLocations->pluck(LeadCampaignLocation::RELATION_LOCATION . '.' . Location::ZIP_CODE)->contains($product->product->address->zip_code))->first();
        }

        // TODO - Split allocation and delivery ?
        return match (true) {
            $campaign instanceof LeadCampaign, $campaign instanceof ProductCampaign => $this->deliverProductAndLeadCampaign($product, $campaign),
            $campaign instanceof CompanyCampaign                                    => $this->deliveryCompanyCampaign($product, $campaign),
            default                                                                 => null
        };
    }

    /**
     * @param TestProduct $product
     * @param CompanyCampaign|null $campaign
     * @return void
     * @throws \Exception
     */
    public function deliveryCompanyCampaign(
        TestProduct $product,
        ?CompanyCampaign $campaign
    ): array
    {
        if (!$campaign) {
            logger()->error("Need a campaign to deliver test product: $product->id");
            return [];
        }

        $proposedAssignment = new ProposedProductAssignment(
            companyId        : $campaign->company_id,
            campaignId       : $campaign->id,
            budgetId         : $this->getBudgetId($campaign),
            price            : 0, // Test leads shouldn't have cost by default
            productId        : $this->getProductId(),
            salesTypeId      : $this->getSaleTypeId(),
            consumerProductId: $product->product_id,
            qualityTierId    : $this->getQualityTierId(),
            chargeable       : false
        );

        try {
            $allocationData = $this->campaignMediator->allocate(collect([$proposedAssignment]));
            $postAllocation = $this->campaignMediator->postAllocation(
                new ConsumerProject($product->product->consumer, $product->product->address, null),
                $allocationData->getProductAssignments(),
                false
            );

            $this->handlePostDelivery($product);

            return ['delivered' => !$postAllocation->hasFailures()];
        } catch (Exception $e) {
            logger()->error("Delivery of test product ($product->id) was failed. Error: {$e->getMessage()}");

            return ['delivered' => false];
        }
    }

    /**
     * @param TestProduct $product
     *
     * @return void
     */
    protected function handlePostDelivery(TestProduct $product): void
    {
        $product->status = TestProductStatus::DELIVERED;

        $product->save();
        $product->product->productAssignment()->update([ProductAssignment::FIELD_CHARGEABLE => false]);
        $this->consumerProductService->updateConsumerProductStatus($product->product->id);
    }

    /**
     * @param TestProduct $product
     * @param CompanyCampaign $campaign
     *
     * @return float
     */
    protected function getPrice(TestProduct $product, CompanyCampaign $campaign): float
    {
        $locationRepository = app(LocationRepository::class);

        $countyLocation = $locationRepository->getCountyFromZipcode($product->product->address->zip_code);
        $stateLocationId = $locationRepository->getStateByStateAbbr($countyLocation->state_abbr)->id;
        $countyLocationId = $countyLocation->id;

        $propertyTypeId = PropertyType::RESIDENTIAL->model()->id;
        $qualityTierId  = QualityTier::STANDARD->model()->id;
        $salesTypeId    = SaleType::query()
            ->where(SaleType::FIELD_NAME, SaleTypes::EXCLUSIVE->name)
            ->first()->id;

        return $this->productBiddingService->getProductBid(
            companyCampaign: $campaign,
            countyLocationId: $countyLocationId,
            stateLocationId: $stateLocationId,
            propertyTypeId: $propertyTypeId,
            qualityTierId: $qualityTierId,
            salesTypeId: $salesTypeId,
        );
    }

    /**
     * @param CompanyCampaign $campaign
     *
     * @return int
     */
    protected function getBudgetId(CompanyCampaign $campaign): int
    {
        return $campaign->budgetContainer->budgets->where(Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_VERIFIED)->first()?->id ?? 0;
    }

    /**
     * @return int
     */
    protected function getProductId(): int
    {
        return Product::query()->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD)->firstOrFail()->id;
    }

    /**
     * @return int
     */
    protected function getSaleTypeId(): int
    {
        return SaleType::query()->where(SaleType::FIELD_NAME, SaleTypes::EXCLUSIVE)->firstOrFail()->id;
    }

    /**
     * @return int
     */
    protected function getQualityTierId(): int
    {
        return \App\Models\Odin\QualityTier::query()->where(\App\Models\Odin\QualityTier::FIELD_NAME, QualityTier::STANDARD)->firstOrFail()->id;
    }
    /**
     * Handle revealing the product.
     *
     * @param TestProduct $product
     * @return bool
     */
    public function revealProduct(TestProduct $product): bool
    {
        $product->status = TestProductStatus::REVEALED;
        $product->save();

        // TODO: Emails, events, etc.
        //          reserve phone number for 90 days before it can be used again.

        DispatchPubSubEvent::dispatch(EventCategory::TEST_LEADS, EventName::TEST_LEAD_REVEALED, $this->getEventData($product));

        return true;
    }

    /**
     * Handle expiring the product.
     *
     * @param TestProduct $product
     * @return bool
     */
    public function expireProduct(TestProduct $product): bool
    {
        $product->status = TestProductStatus::EXPIRED;
        $product->save();

        DispatchPubSubEvent::dispatch(
            EventCategory::TEST_LEADS,
            EventName::TEST_LEAD_EXPIRED,
            $this->getEventData($product)
        );

        return true;
    }

    /**
     * @param string $phone
     *
     * @return TestProduct|null
     */
    public function findTestProductByPhone(string $phone): ?TestProduct
    {
        /** @var TestProduct|null */
        return TestProduct::query()->where(TestProduct::FIELD_PHONE,'LIKE',  "%$phone%")->latest()->first();
    }

    /**
     * @param string $email
     *
     * @return TestProduct|null
     */
    public function findTestProductByEmail(string $email): ?TestProduct
    {
        /** @var TestProduct|null */
        return TestProduct::query()->where(TestProduct::FIELD_EMAIL, $email)->latest()->first();
    }

    /**
     * @param int $companyId
     * @param IndustryService $industryService
     *
     * @return TestProduct|null
     */
    public function getLatestTestProductForCompanyAndService(int $companyId, IndustryService $industryService): ?TestProduct
    {
        /** @var TestProduct|null */
        return TestProduct::query()
            ->selectRaw(TestProduct::TABLE . '.*')
            ->join(
                ConsumerProduct::TABLE,
                TestProduct::TABLE . '.' . TestProduct::FIELD_PRODUCT_ID,
                '=',
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
            )->join(
                ServiceProduct::TABLE,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID,
                '=',
                ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID
            )->where(ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryService->id)
            ->where(TestProduct::TABLE . '.' . TestProduct::FIELD_COMPANY_ID, $companyId)
            ->latest(TestProduct::TABLE . '.' . TestProduct::CREATED_AT)
            ->first();
    }

    /**
     * @param TestProduct $product
     *
     * @return array
     */
    public function getEventData(TestProduct $product): array
    {
        return [
            'company_id' => $product->company_id,
            'test_product_id' => $product->id
        ];
    }

    /**
     * @param array $result
     *
     * @return array
     */
    protected function processLegacyDeliveryResult(array $result): array
    {
        $result['delivered'] = Arr::get($result, 'status', false) && Arr::get($result, 'send_count', 0) > 0;

        return $result;
    }
}
