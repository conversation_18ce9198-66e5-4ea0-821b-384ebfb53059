<?php

namespace App\Services\TestProducts;

use App\Enums\LegacyQuoteOrigin;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry;
use App\Enums\Odin\SolarConfigurableFields;
use App\Enums\PhoneType;
use App\Enums\TestProductStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Models\Phone;
use App\Models\TestProduct;
use App\Services\Odin\API\OdinAuthoritativeAPILegacySyncService;
use App\Services\OutreachCadence\TimeZoneHelperService;
use App\Services\TestProducts\Communication\TestProductCommunicationService;
use App\Services\Twilio\TwilioPhoneNumberService;
use App\Transformers\Odin\v2\EloquentQuoteTransformer;
use Carbon\Carbon;
use Faker\Factory;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use MailSlurp\ApiException;
use Ramsey\Uuid\Uuid;
use Twilio\Exceptions\TwilioException;

class TestProductGeneratorService
{
    const MAX_SEARCHES                = 10000;
    const RESPONSE_STATUS             = 'status';
    const RESPONSE_UUID               = 'uuid';
    const RESPONSE_CONSUMER           = 'consumer';
    const RESPONSE_LEGACY_ID          = 'legacy_id';
    const RESPONSE_LEGACY_UUID        = 'legacy_uuid';
    const RESPONSE_ELOQUENT_QUOTE     = 'eloquent_quote';
    const RESPONSE_ELOQUENT_ADDRESS   = 'eloquent_address';
    const RESPONSE_ELOQUENT_LATITUDE  = 'eloquent_latitude';
    const RESPONSE_ELOQUENT_LONGITUDE = 'eloquent_longitude';

    /**
     * @TODO: In the future make this configuration driven.
     *        I.e, we might want a round-robin for this, such that installers
     *        don't catch wind onto the domain we use for test leads.
     */
    const EMAIL_DOMAIN = 'mailus.co';

    protected \Faker\Generator $faker;

    public function __construct(
        protected TwilioPhoneNumberService              $phoneNumberService,
        protected EloquentQuoteTransformer              $quoteTransformer,
        protected OdinAuthoritativeAPILegacySyncService $legacySyncService,
        protected MailSlurpService $mailSlurpService,
        protected RealAddressGeneratorService $realAddressGeneratorService,
        protected TimeZoneHelperService $timeZoneHelperService,
    )
    {
        $this->faker = Factory::create();
    }

    /**
     * Handles generating a consumer, and a consumer product.
     *
     * @param Company $company
     * @param IndustryService $service
     * @param ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign
     * @return ConsumerProduct|null
     * @throws TwilioException|GuzzleException
     */
    public function generateInternalProduct(
        Company $company,
        IndustryService $service,
        ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign
    ): ?ConsumerProduct
    {
        $address = $this->getValidAddress($campaign);
        if(!$address) {
            return null;
        }

        $consumer = $this->generateConsumer($company, $service, $address);
        if (!$consumer)
            return null;

        $product = $this->generateConsumerProduct($consumer, $service, $address);
        $this->syncToLegacy($consumer, $product);

        return $product;
    }

    /**
     * Generates a consumer.
     *
     * @param Company $company
     * @param IndustryService $service
     * @param Address|null $address
     * @return Consumer|null
     * @throws TwilioException
     */
    protected function generateConsumer(Company $company, IndustryService $service, ?Address $address): ?Consumer
    {
        if (!$address) return null;

        $consumer = new Consumer();
        $consumer->reference = Uuid::uuid4();
        $consumer->website_id = Website::query()->where(Website::FIELD_ABBREVIATION, 'fixr')->first()?->id;
        $consumer->first_name = $this->faker->firstName();
        $consumer->last_name = $this->faker->lastName();
        $consumer->email = $this->generateEmailForConsumer($consumer->first_name, $consumer->last_name);
        $consumer->phone = $this->generatePhoneForConsumer($address, $company);
        $consumer->status = Consumer::STATUS_INITIAL;
        $consumer->classification = Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS;
        $consumer->max_contact_requests = 1;
        $consumer->save();

        return $consumer;
    }

    /**
     * Returns an address that is valid for the given campaign.
     *
     * @param ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign
     * @return Address|null
     * @throws GuzzleException
     */
    public function getValidAddress(ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign): ?Address
    {
        if (!$campaign) return null;

        $zipCodes = match (true) {
            $campaign instanceof LeadCampaign    => $this->getValidZipCodesLegacy($campaign),
            $campaign instanceof ProductCampaign => $this->getValidZipCodes($campaign),
            $campaign instanceof CompanyCampaign => $this->getValidZipCodesFromCompanyCampaign($campaign),
            default => collect()
        };

        if($zipCodes->isEmpty()) {
            return null;
        }

        // Try to find an address within the zip codes
        $baseAddress = $this->findAddressInZipCodes(zipCodes: $zipCodes, requireGoodToSell: true);

        // Create a new one if none of the zip codes exist in our database
        if (!isset($baseAddress)) {
            $baseAddress = $this->generateNewAddress($zipCodes);
        }

        /** @var Address $address */
        $address = $baseAddress?->replicate();
        $address?->save();

        return $address;
    }

    /**
     * Given a list of zip codes, request google and create a new address
     * @param Collection $zipCodes
     * @return Address|null
     * @throws GuzzleException
     */
    public function generateNewAddress(Collection $zipCodes): ?Address
    {
        // return the first zip code and the random address that has a valid address
        do {
            $currentZip = $zipCodes->random();
            $zipCodes = $zipCodes->filter(fn($zip) => $zip !== $currentZip);

            $googleAddress = $this->realAddressGeneratorService->getRandomAddress($currentZip);
        } while (!isset($googleAddress) && count($zipCodes) > 0);

        if (empty($googleAddress)) return null;

        // Extract data from locations, google address to create a new address in our database
        /** @var Location $location */
        $location = Location::query()
            ->where(Location::ZIP_CODE, $currentZip)
            ->whereNotNull(Location::STATE)
            ->whereNotNull(Location::CITY)
            ->first();

        // Split full address by comma and get only the street name
        [$streetName] = collect(explode(',', $googleAddress['formatted_address'] ?? ''))->map(fn (string $str) => trim($str));

        [
            'lat' => $lat,
            'lng' => $lng,
        ] = $googleAddress['geometry']['location'] ?? [];

        /** @var Address $address */
        $address = Address::query()->create([
            Address::FIELD_ADDRESS_1 => $streetName,
            Address::FIELD_CITY      => $location->{Location::CITY},
            Address::FIELD_STATE     => $location->{Location::STATE_ABBREVIATION},
            Address::FIELD_ZIP_CODE  => $currentZip,
            Address::FIELD_COUNTRY   => 'US',
            Address::FIELD_LATITUDE  => $lat,
            Address::FIELD_LONGITUDE => $lng,
            Address::FIELD_UTC       => $this->timeZoneHelperService->getTimeZoneOffsetForZipcode($currentZip)
        ]);

        return $address;
    }

    protected function findAddressInZipCodes(
        Collection $zipCodes,
        bool       $requireGoodToSell = false,
        int        $i = 0,
    ): ?Address
    {
        if ($i >= self::MAX_SEARCHES)
            return null;

        /** @var string $searchZip */
        $searchZip = $zipCodes->random();

        /** @var Address $address */
        $address = Address::query()
            ->when($requireGoodToSell, function ($query) {
                $query->whereHas(Address::RELATION_CONSUMER_PRODUCTS, function ($query) {
                    $query->where(ConsumerProduct::FIELD_GOOD_TO_SELL, true);
                });
            })
            ->where(Address::TABLE .'.'. Address::FIELD_ZIP_CODE, $searchZip)
            ->inRandomOrder()
            ->first();

        return $address ?? $this->findAddressInZipCodes(
            zipCodes: $zipCodes,
            requireGoodToSell: $requireGoodToSell,
            i: $i + 1
        );
    }

    /**
     * Returns all valid zip codes to find an address in.
     *
     * @param ProductCampaign $productCampaign
     * @return Collection
     */
    protected function getValidZipCodes(ProductCampaign $productCampaign): Collection
    {
        $campaigns = ProductCampaign::query()
            ->where(ProductCampaign::FIELD_ID, $productCampaign->id)
            ->with([
                ProductCampaign::RELATION_LEGACY_CAMPAIGN,
                ProductCampaign::RELATION_LEGACY_CAMPAIGN . '.' . LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS,
                ProductCampaign::RELATION_LEGACY_CAMPAIGN . '.' . LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS . '.' . LeadCampaignLocation::RELATION_LOCATION,
            ])
            ->get();

        return $campaigns->flatMap(fn(ProductCampaign $item) => $item->legacyCampaign?->leadCampaignZipLocations?->pluck('location.zip_code'));
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @return Collection
     */
    protected function getValidZipCodesFromCompanyCampaign(CompanyCampaign $companyCampaign): Collection
    {
        return $companyCampaign
            ?->{CompanyCampaign::RELATION_LOCATION_MODULE}
            ?->{CompanyCampaignLocationModule::RELATION_LOCATIONS}()
            ?->pluck(CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE) ?? collect();
    }

    /**
     * @param LeadCampaign $leadCampaign
     * @return Collection
     */
    protected function getValidZipCodesLegacy(LeadCampaign $leadCampaign): Collection
    {
        $leadCampaigns = LeadCampaign::query()
            ->where(LeadCampaign::ID, $leadCampaign->id)
            ->with([
                LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS,
                LeadCampaign::RELATION_LEAD_CAMPAIGN_ZIP_LOCATIONS . '.' . LeadCampaignLocation::RELATION_LOCATION
            ])
            ->get();

        return $leadCampaigns->flatMap(fn(LeadCampaign $item) => $item->leadCampaignZipLocations?->pluck('location.zip_code'));
    }

    /**
     * Returns a product campaign against the requested combination of company & industry service.
     *
     * @param Company         $company
     * @param IndustryService $service
     * @return ProductCampaign|null
     */
    public function getProductCampaign(Company $company, IndustryService $service): ?ProductCampaign
    {
        return $company->productCampaigns()
            ->where(ProductCampaign::FIELD_STATUS, true)
            ->where(ProductCampaign::FIELD_INDUSTRY_SERVICE_ID, $service->id)
            ->inRandomOrder()
            ->first();
    }

    /**
     * Generates a consumer product.
     *
     * @param Consumer $consumer
     * @param IndustryService $service
     * @param Address|null $address
     * @return ConsumerProduct|null
     */
    protected function generateConsumerProduct(Consumer $consumer, IndustryService $service, ?Address $address): ?ConsumerProduct
    {
        if (!$address)
            return null;

        $consumerProductData = new ConsumerProductData();
        $consumerProductData->payload = $this->findValidProductDataPayload($service, $address);
        $consumerProductData->save();

        $product = new ConsumerProduct();
        $product->service_product_id = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $service->id)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, 1) // TODO: Don't hard code this.
            ->first()?->id;
        $product->consumer_id = $consumer->id;
        $product->address_id = $address->id;
        $product->good_to_sell = true;
        $product->consumer_product_data_id = $consumerProductData->id;
        $product->status = ConsumerProduct::STATUS_PENDING_ALLOCATION;
        $product->contact_requests = 1;
        $product->consumer_product_tcpa_record_id = null;
        $product->consumer_product_tracking_id = null;
        $product->consumer_product_affiliate_record_id = null;
        $product->property_type_id = 1;
        $product->save();

        return $product;
    }

    /**
     * Returns product data for a given service.
     *
     * @param IndustryService $service
     * @param Address|null $address
     * @return array
     */
    public function findValidProductDataPayload(IndustryService $service, ?Address $address = null): array
    {
        // Override the product data with a real utility only for solar industry
        // Other industries try to use consumer product data or factory
        if ($service->{IndustryService::RELATION_INDUSTRY}->slug === Industry::SOLAR->getSlug()) {
            return ConsumerProductData::factory()->withSolarFields()->create()->{ConsumerProductData::FIELD_PAYLOAD};
        }

        /** @var ConsumerProduct $product */
        $product = ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_INDUSTRY_SERVICE, fn($item) => $item->where(IndustryService::TABLE . '.' . IndustryService::FIELD_ID, $service->id))
            ->inRandomOrder()
            ->first();

        $payload = $product?->consumerProductData
            ? $product->consumerProductData->payload
            : ConsumerProductData::factory()->create()->toArray();

        $payload[GlobalConfigurableFields::ORIGIN->value] = $this->getLeadOrigin($service);

        $payload[GlobalConfigurableFields::TRUSTED_FORM_URL->value] = $this->getTrustedFormUrl();

        return $payload;
    }

    /**
     * @param IndustryService $service
     * @return string
     */
    protected function getLeadOrigin(IndustryService $service): string
    {
        return match ($service->{IndustryService::RELATION_INDUSTRY}->slug) {
            Industry::SOLAR->getSlug() => LegacyQuoteOrigin::SE->value,
            Industry::ROOFING->getSlug() => LegacyQuoteOrigin::RC->value,
            default => LegacyQuoteOrigin::FIXR->value
        };
    }

    /**
     * Generates ane mail for the consumer, given their first name and last name.
     *
     * @param string $firstName
     * @param string $lastName
     * @return string
     * @throws ApiException
     */
    protected function generateEmailForConsumer(string $firstName, string $lastName): string
    {
        $baseEmail = Str::slug($firstName) . '.' . Str::slug($lastName);
        $email = $this->getAvailableEmail($baseEmail);

        $this->mailSlurpService->createEmailAddress($email, "$firstName $lastName");

        return $email;
    }

    /**
     * Returns an email that's not in use currently.
     *
     * @param string $base
     * @param int|null $i
     * @return string
     */
    protected function getAvailableEmail(string $base, ?int $i = null): string
    {
        $base = $i ? $base . $i : $base;

        $testProduct = TestProduct::query()
            ->where(TestProduct::FIELD_EMAIL, $base . '@' . self::EMAIL_DOMAIN)
            ->whereIn(TestProduct::FIELD_STATUS, [TestProductStatus::INITIAL, TestProductStatus::DELIVERED])
            ->first();

        return $testProduct ? $this->getAvailableEmail($base, $i ? $i++ : 1) : $base . '@' . self::EMAIL_DOMAIN;
    }

    /**
     * Handles generating a phone number for a consumer.
     *
     * @param Address $address
     * @param Company $company
     * @return string
     * @throws TwilioException
     */
    protected function generatePhoneForConsumer(Address $address, Company $company): string
    {
        return $this->getAvailablePhoneNumber($address, $company)->phone;
    }


    /**
     * Handles finding an available number in our pool of numbers, or buying a new one if one doesnt exist
     * for the area the address is in.
     *
     * @param Address $address
     * @param Company $company
     * @return Phone
     * @throws TwilioException
     */
    public function getAvailablePhoneNumber(Address $address, Company $company): Phone
    {
        $phones = $this->getAvailablePhoneNumberQuery($address)
            ->get()
            ->filter(
                fn(Phone $phone) => TestProduct::query()
                        ->where(TestProduct::FIELD_PHONE, $phone->phone)
                        //we do not want to send the same phone number to the company twice
                        ->where(TestProduct::FIELD_COMPANY_ID, $company->id)
                        ->whereIn(TestProduct::FIELD_STATUS, [TestProductStatus::DELIVERED, TestProductStatus::INITIAL])
                        ->count() === 0
            );

        if ($phones->count() === 0) {
            return $this->phoneNumberService->acquireNumber(
                $address->state,
                PhoneType::TEST_PRODUCT,
                "test_product",
                [
                    'voiceUrl' => route('test-product-voice'),
                    'smsUrl'   => route('test-product-sms'),
                ]
            );
        }

        return $phones->random();
    }

    /**
     * Returns the base query for finding a given phone number.
     *
     * @param Address $address
     * @return Builder
     */
    protected function getAvailablePhoneNumberQuery(Address $address): Builder
    {
        return Phone::query()
            ->where(Phone::FIELD_TYPE, PhoneType::TEST_PRODUCT)
            ->where(Phone::FIELD_REGION, $address->state);
    }


    /**
     * Handles syncing the consumer and product to legacy.
     *
     * @param Consumer $consumer
     * @param ConsumerProduct $product
     * @return bool
     */
    protected function syncToLegacy(Consumer $consumer, ConsumerProduct $product): bool
    {
        $legacyConsumer = $this->quoteTransformer->transform($consumer, true);
        $legacyResponse = $this->legacySyncService->post('/consumer/create', $legacyConsumer)?->json();
        $legacyStatus = $legacyResponse[self::RESPONSE_STATUS] ?? null;

        if (!$legacyStatus)
            return false;

        $consumer->legacy_id = $legacyResponse[self::RESPONSE_ELOQUENT_QUOTE][self::RESPONSE_LEGACY_ID];
        $consumer->save();

        $product->address->legacy_id = $legacyResponse[self::RESPONSE_ELOQUENT_ADDRESS][self::RESPONSE_LEGACY_ID];
        $product->address->save();

        return true;
    }

    /**
     * Get the trusted form url from the latest fake and cancelled consumer product
     *
     * @return string|null
     */
    public function getTrustedFormUrl(): ?string
    {
        return ConsumerProduct::query()
            ->whereHas(ConsumerProduct::RELATION_CONSUMER, function($query) {
                $query->where(function($subQuery) {
                    $subQuery->where(Consumer::FIELD_EMAIL, 'like', '%fixr.com')
                             ->orWhere(Consumer::FIELD_EMAIL, 'like', '%solarreviews.com');
                })->orWhere(function ($subQuery) {
                    $subQuery->where(Consumer::FIELD_CLASSIFICATION, Consumer::CLASSIFICATION_EMAIL_ONLY)
                        ->where(Consumer::FIELD_EMAIL, 'like', '%test%');
                });
            })
            ->whereHas(ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA, function($query) {
                $query->whereNotNull(ConsumerProductData::FIELD_PAYLOAD . '->' . GlobalConfigurableFields::TRUSTED_FORM_URL->value);
            })
            ->where(ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_CANCELLED)
            // trusted form default is 72 hour expiry - 2 hour buffer for installers CRM integration
            ->where(ConsumerProduct::CREATED_AT, '>=', Carbon::now()->subHours(70))
            ->latest(ConsumerProduct::FIELD_CREATED_AT)
            ->with(ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA)
            ->first()
            ?->consumerProductData
            ?->payload[GlobalConfigurableFields::TRUSTED_FORM_URL->value] ?? null;
    }
}
