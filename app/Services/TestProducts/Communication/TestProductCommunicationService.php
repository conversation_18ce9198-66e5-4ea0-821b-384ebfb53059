<?php

namespace App\Services\TestProducts\Communication;

use App\Enums\PhoneType;
use App\Enums\TestProductCommunicationType;
use App\Enums\TestProductStatus;
use App\Models\Odin\Address;
use App\Models\Phone;
use App\Models\TestProduct;
use App\Models\TestProductCommunication;
use App\Repositories\Odin\TestLeadApiRepository;
use App\Services\TestProducts\MailSlurpService;
use App\Services\TestProducts\TestProductService;
use App\Services\Twilio\TwilioPhoneNumberService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Http;
use MailSlurp\ApiException;
use MailSlurp\Apis\EmailControllerApi;
use MailSlurp\Configuration;

class TestProductCommunicationService
{
    const TO   = 'To';
    const FROM = 'From';
    const BODY = 'Body';

    const EMAIL_EVENT_NAME = 'eventName';
    const EMAIL_TO         = 'to';
    const EMAIL_FROM       = 'from';
    const EMAIL_INBOX_ID   = 'inboxId';
    const EMAIL_ID         = 'emailId';

    const EMAIL_EVENT_NAME_NEW_EMAIL = 'NEW_EMAIL';

    public function __construct(
        protected TestProductService    $testProductService,
        protected TestLeadApiRepository $testLeadApiRepository,
        protected MailSlurpService      $mailSlurpService
    ) {}

    /**
     * Handles tracking a communication between a company and a given lead.
     *
     * @param TestProduct $product
     * @param string $from
     * @param string $to
     * @param TestProductCommunicationType $type
     * @param array|null $content
     *
     * @return TestProductCommunication
     */
    public function trackCommunication(TestProduct $product, string $from, string $to, TestProductCommunicationType $type, ?array $content = null): TestProductCommunication
    {
        if ($type === TestProductCommunicationType::CALL) {
            $communication = TestProductCommunication::query()
                ->where(TestProductCommunication::FIELD_FROM, $from)
                ->where(TestProductCommunication::FIELD_TO, $to)
                ->where(TestProductCommunication::FIELD_COMMUNICATION_TYPE, TestProductCommunicationType::CALL->value)
                ->where(TestProductCommunication::FIELD_TEST_PRODUCT_ID, $product->id)
                ->whereJsonContains(TestProductCommunication::FIELD_CONTENT . '->' . 'CallSid', $content['CallSid'])
                ->first();
        }

        if (isset($communication)) {
            $communication->update([
                TestProductCommunication::FIELD_CONTENT => $content
            ]);
        } else {
            $communication = new TestProductCommunication();
            $communication->test_product_id = $product->id;
            $communication->from = $from;
            $communication->to = $to;
            $communication->communication_type = $type;
            $communication->content = $content;
            $communication->save();
        }

        $product->contacted = true;
        $product->save();

        return $communication;
    }

    /**
     * @param array $data
     *
     * @return void
     */
    public function trackIncomingSMS(array $data): void
    {
        $testProduct = $this->testProductService->findTestProductByPhone($this->preparePhoneForQuery($data[self::TO]));

        if (!$testProduct) {
            $this->logMessage("Test product does not exist for phone: {$data[self::TO]}. Type: SMS");
            return;
        }

        $this->trackCommunication($testProduct, $data[self::FROM], $data[self::TO], TestProductCommunicationType::SMS, $data);
    }

    /**
     * @param array $data
     *
     * @return void
     */
    public function trackIncomingCall(array $data): void
    {
        $testProduct = $this->testProductService->findTestProductByPhone($this->preparePhoneForQuery($data[self::TO]));

        if (!$testProduct) {
            $this->logMessage("Test product does not exist for phone: {$data[self::TO]}. Type: call");
            return;
        }

        $this->trackCommunication($testProduct, $data[self::FROM], $data[self::TO], TestProductCommunicationType::CALL, $data);
    }

    /**
     * @param array $data
     *
     * @return void
     */
    public function trackNewEmail(array $data): void
    {
        if ($data[self::EMAIL_EVENT_NAME] !== self::EMAIL_EVENT_NAME_NEW_EMAIL) return;

        $toEmail = $data[self::EMAIL_TO][0];
        $testProduct = $this->testProductService->findTestProductByEmail($toEmail);

        if (!$testProduct) {
            $this->logMessage("Test product does not exist for email: {$data[self::EMAIL_ID]}.");
            return;
        }

        $emailId = $data[self::EMAIL_ID];
        $data['Body'] = $this->getEmailContent($emailId);

        $this->trackCommunication($testProduct, $data[self::EMAIL_FROM], $toEmail, TestProductCommunicationType::EMAIL, $data);
    }

    /**
     * @param string $message
     *
     * @return void
     */
    protected function logMessage(string $message): void
    {
        //todo: proper logging

        logger()->error($message);
    }

    /**
     * @param string $phone
     *
     * @return string
     */
    protected function preparePhoneForQuery(string $phone): string
    {
        $phone = preg_replace('/\D/', '', $phone);

        if (strlen($phone) === 10) return $phone;

        return substr($phone, 1);
    }

    /**
     * @param string $emailId
     * @return string|null
     */
    protected function getEmailContent(string $emailId): ?string
    {
        try {
            $email = $this->mailSlurpService->getEmail($emailId);
            return $email->getBody();
        } catch (Exception $e) {
            logger("Test Product Email Tracking: failed to get email content for email id: $emailId. Error: {$e->getMessage()}");
        }

        return null;
    }

    /**
     * @param string $phone
     * @return int|null
     */
    public function getFraudScore(string $phone): ?int
    {
        $response = Http::get(config('services.ip_quality_score.base_url') . "phone/" . config('services.ip_quality_score.api_key') . '/' . $phone);
        if ($response->successful()) {
            return (int)$response->json()["fraud_score"];
        } else {
            logger()->error("Couldn't retrieve phone data from Api");
        }

        return null;
    }
}
