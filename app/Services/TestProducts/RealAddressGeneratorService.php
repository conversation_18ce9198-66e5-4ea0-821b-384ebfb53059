<?php

namespace App\Services\TestProducts;


use App\Models\Legacy\Location;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class RealAddressGeneratorService
{
    const TOTAL_PAGES_TO_QUERY = '10';

    protected Client $client;

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://maps.googleapis.com/maps/api/place'
        ]);
    }

    /**
     * @param string $query
     * @param string|null $pageToken
     * @return array
     * @throws GuzzleException
     */
    protected function getAddresses(string $query, ?string $pageToken = null): array
    {
        $response = $this->client->get('https://maps.googleapis.com/maps/api/place/textsearch/json', [
            'query' => [
                'query' => $query,
                'key' => config('services.google.maps.static_maps_key'),
                'pagetoken' => $pageToken,
                'radius' => 10000, // 10Km
//                'type' => ''
            ],
        ]);

        $places = json_decode($response->getBody(), true);

        return [$places['results'], $places['next_page_token'] ?? null];
    }

    /**
     * @param string $zipCode
     * @return array|null
     * @throws GuzzleException
     */
    public function getRandomAddress(string $zipCode): ?array
    {
        $page = 1;
        $pageToken = null;
        $addresses = [];

        // TODO - Loop locations not zipcodes
        /** @var Location $location */
        $location = Location::query()
            ->where(Location::ZIP_CODE, $zipCode)
            ->whereNotNull(Location::STATE)
            ->whereNotNull(Location::CITY)
            ->first();

        if (!isset($location)) return null;

        $query = 'neighborhood in ' . $location->city . ' ' . $location->state . ' ' . $location->zip_code . ' USA';

//        $query = 'neighborhood in ' . $zipCode . ' USA';
//
//        if (isset($location)) {
//        }

        do {
            [$addressesFound, $nextPageToken] = $this->getAddresses($query, $pageToken);

            $addresses = array_merge($addresses, $addressesFound);
            $pageToken = $nextPageToken;

            $page++;
        } while ($pageToken && $page < self::TOTAL_PAGES_TO_QUERY);

        // Only same zip code
        $validAddresses = collect($addresses)->filter(fn (array $data) => strpos($data['formatted_address'], $zipCode));

        if ($validAddresses->count() === 0) return null;

        return $validAddresses->random();
    }

}
