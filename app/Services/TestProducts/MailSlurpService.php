<?php

namespace App\Services\TestProducts;

use MailSlurp\ApiException;
use MailSlurp\Apis\EmailControllerApi;
use MailSlurp\Apis\InboxControllerApi;
use MailSlurp\Configuration;
use MailSlurp\Models\Email;

class MailSlurpService
{
    protected InboxControllerApi $inboxController;
    protected EmailControllerApi $emailControllerApi;

    public function __construct()
    {
        $this->inboxController = new InboxControllerApi(null, $this->getConfig());
        $this->emailControllerApi = new EmailControllerApi(null, $this->getConfig());
    }

    /**
     * @throws ApiException
     */
    public function createEmailAddress(string $email, ?string $name = null): void
    {
        $this->inboxController->createInbox(
            email_address: $email,
            name: $name
        );
    }

    /**
     * @param string $emailId
     *
     * @return Email
     * @throws ApiException
     */
    public function getEmail(string $emailId): Email
    {
        return $this->emailControllerApi->getEmail($emailId);
    }

    /**
     * @return Configuration
     */
    protected function getConfig(): Configuration
    {
        return Configuration::getDefaultConfiguration()->setApiKey('x-api-key', config('services.mailslurp.api_key'));
    }
}
