<?php

namespace App\Services\SalesBait;

use App\Models\Legacy\Location;
use App\Models\SalesBaitConfiguration;
use App\Repositories\LocationRepository;
use Illuminate\Support\Collection;

class SalesBaitConfigurationService
{
    public function __construct(public LocationRepository $locationRepository) {}

    /**
     * Returns if sales bait is enabled nationally for a given industry.
     *
     * @param string $industry
     * @return bool
     */
    public function nationalSalesBaitEnabled(string $industry = SalesBaitConfiguration::INDUSTRY_SOLAR): bool
    {
        return SalesBaitConfiguration::query()
                   ->where(SalesBaitConfiguration::FIELD_TYPE, SalesBaitConfiguration::TYPE_NATIONAL)
                   ->where(SalesBaitConfiguration::FIELD_INDUSTRY, $industry)
                   ->first()?->enabled ?? false;
    }

    /**
     * Returns if sales bait is enabled for an industry, in a given state
     *
     * @param int $locationId
     * @param string $industry
     * @return bool
     */
    public function stateSalesBaitEnabled(int $locationId, string $industry = SalesBaitConfiguration::INDUSTRY_SOLAR): bool
    {
        return SalesBaitConfiguration::query()
                   ->where(SalesBaitConfiguration::FIELD_TYPE, SalesBaitConfiguration::TYPE_STATE)
                   ->where(SalesBaitConfiguration::FIELD_LOCATION_ID, $locationId)
                   ->where(SalesBaitConfiguration::FIELD_INDUSTRY, $industry)
                   ->first()?->enabled ?? false;
    }

    /**
     * Returns if sales bait is enabled for an industry, in a given county
     *
     * @param int $locationId
     * @param string $industry
     * @return bool
     */
    public function countySalesBaitEnabled(int $locationId, string $industry = SalesBaitConfiguration::INDUSTRY_SOLAR): bool
    {
        return SalesBaitConfiguration::query()
                   ->where(SalesBaitConfiguration::FIELD_TYPE, SalesBaitConfiguration::TYPE_COUNTY)
                   ->where(SalesBaitConfiguration::FIELD_LOCATION_ID, $locationId)
                   ->where(SalesBaitConfiguration::FIELD_INDUSTRY, $industry)
                   ->first()?->enabled ?? false;
    }

    /**
     * Checks if sales bait is enabled for a given zip code.
     *
     * @param string $zipCode
     * @param string $industry
     * @return bool
     */
    public function isSalesBaitEnabledForZipCode(string $zipCode, string $industry = SalesBaitConfiguration::INDUSTRY_SOLAR): bool
    {
        $zipCode = $this->locationRepository->getZipCode($zipCode);

        if (!$zipCode)
            return false;

        $state = $this->locationRepository->getState($zipCode->state_key);
        $county = $this->locationRepository->getCounty($zipCode->state_key, $zipCode->county_key);

        return $this->nationalSalesBaitEnabled($industry) && $this->stateSalesBaitEnabled($state->id, $industry) && $this->countySalesBaitEnabled($county->id, $industry);
    }

    /**
     * Gets the global configuration for an industry (National and states returned).
     *
     * @param string $industry
     * @return Collection
     */
    public function getGlobalConfiguration(string $industry = SalesBaitConfiguration::INDUSTRY_SOLAR, array $with = []): Collection
    {
        return SalesBaitConfiguration::query()
            ->where(SalesBaitConfiguration::FIELD_INDUSTRY, $industry)
            ->whereIn(SalesBaitConfiguration::FIELD_TYPE, [SalesBaitConfiguration::TYPE_NATIONAL, SalesBaitConfiguration::TYPE_STATE])
            ->with($with)
            ->get();
    }

    /**
     * Returns the global configuration for a state
     *
     * @param int $locationId
     * @param string $industry
     * @return Collection
     */
    public function getCountyConfiguration(int $locationId, string $industry = SalesBaitConfiguration::INDUSTRY_SOLAR): Collection
    {
        /** @var Location $state */
        $state = Location::query()->findOrFail($locationId);
        $locations = $this->locationRepository->getCountiesInState($state->state_key);

        return SalesBaitConfiguration::query()
            ->where(SalesBaitConfiguration::FIELD_INDUSTRY, $industry)
            ->where(SalesBaitConfiguration::FIELD_TYPE, SalesBaitConfiguration::TYPE_COUNTY)
            ->whereIn(SalesBaitConfiguration::FIELD_LOCATION_ID, $locations->pluck(Location::ID)->toArray())
            ->get();
    }
}
