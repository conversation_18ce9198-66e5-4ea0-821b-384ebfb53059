<?php

namespace App\Services\SalesBait;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRegisteredInterest;
use App\Services\PubSub\ContactSubscriptionService;
use App\Services\PubSub\PubSubService;

class SalesBaitEventService
{
    const DATA_USER_TYPE   = 'user-type';
    const DATA_ID          = 'id';
    const DATA_TRACKING_ID = 'tracking-id';

    const TYPE_MAPPINGS = [
        "user"    => SalesBaitRegisteredInterest::TYPE_USER,
        "contact" => SalesBaitRegisteredInterest::TYPE_CONTACT
    ];

    public function __construct(
        protected SalesBaitTrackingService   $salesBaitTrackingService,
        protected ContactSubscriptionService $contactSubscriptionService,
        protected PubSubService $pubSubService
    ) {}

    /**
     * Handles an incoming sales bait event.
     *
     * @param string $event
     * @param array $data
     * @return void
     */
    public function handle(string $event, array $data): void
    {
        switch ($event) {
            case "link-clicked":
                $this->handleSalesBaitLinkClicked($data);
                break;

            case "unsubscribe":
                $this->handleUnsubscribe($data);
                break;

            default:
                break;
        }
    }

    /**
     * Handles the logic for a sales bait link being clicked.
     *
     * @param array $data
     * @return void
     */
    protected function handleSalesBaitLinkClicked(array $data): void
    {
        if (!isset($data[self::DATA_ID]) || !isset($data[self::DATA_USER_TYPE]) || !isset($data[self::DATA_TRACKING_ID]))
            return;

        try {
            /** @var SalesBaitLead $salesBait */
            $salesBait = SalesBaitLead::query()->findOrFail($data[self::DATA_TRACKING_ID]);
            $type = self::TYPE_MAPPINGS[$data[self::DATA_USER_TYPE]] ?? SalesBaitRegisteredInterest::TYPE_CONTACT;
            $id = $data[self::DATA_ID];

            if (!$this->salesBaitTrackingService->hasPunterRegisteredInterest($salesBait, $type, $id)) {
                $this->salesBaitTrackingService->recordPunterInterest($salesBait, $type, $id);
            }

            $this->salesBaitTrackingService->recordLinkClicked($salesBait);
        } catch (\Exception $e) {
            logger()->error($e->getMessage());
        }
    }

    /**
     * Handles unsubscribing a user from sales bait emails.
     *
     * @param array $data
     * @return void
     */
    protected function handleUnsubscribe(array $data): void
    {
        if (!isset($data[self::DATA_ID]) || !isset($data[self::DATA_USER_TYPE]) || !isset($data[self::DATA_TRACKING_ID]))
            return;

        try {
            $salesBait = SalesBaitLead::query()->findOrFail($data[self::DATA_TRACKING_ID]);
            $type = self::TYPE_MAPPINGS[$data[self::DATA_USER_TYPE]] ?? SalesBaitRegisteredInterest::TYPE_CONTACT;
            $contactId = $data[self::DATA_ID];

            $this->contactSubscriptionService->updateOrCreateContactSubscription(
                $this->contactSubscriptionService->searchContactSubscriptions(
                    $contactId,
                    $type == SalesBaitRegisteredInterest::TYPE_CONTACT
                        ? \App\Models\ContactSubscription::CONTACT_TYPE_CONTACT
                        : \App\Models\ContactSubscription::CONTACT_TYPE_USER,
                    \App\Models\ContactSubscription::CONTACT_METHOD_EMAIL,
                    \App\Models\ContactSubscription::NOTIFICATION_TYPE_SALESBAIT
                )->first()?->id,
                $contactId,
                $type == SalesBaitRegisteredInterest::TYPE_CONTACT
                    ? \App\Models\ContactSubscription::CONTACT_TYPE_CONTACT
                    : \App\Models\ContactSubscription::CONTACT_TYPE_USER,
                \App\Models\ContactSubscription::CONTACT_METHOD_EMAIL,
                \App\Models\ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
                true
            );

            $this->pubSubService->handle(
                EventCategory::COMPANIES->value,
                EventName::SALES_BAIT_UNSUBSCRIBED->value,
                [
                    'company_reference' => $salesBait->{SalesBaitLead::RELATION_COMPANY}->reference,
                    'lead_id' => $salesBait->{SalesBaitLead::FIELD_LEAD_ID},
                    'contact_id' => $contactId
                ]
            );
        } catch (\Exception $e) {
            logger()->error($e->getMessage());
        }
    }
}
