<?php

namespace App\Services\SalesBait;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\SalesBaitLead;
use App\Services\DatabaseHelperService;
use Illuminate\Support\Collection;
use Illuminate\Database\Eloquent\Builder;

class SalesBaitLeadService
{
    public function __construct() {}

    /**
     * Retrieves the total number of sales bait been sent out
     *
     * @return int
     */
    public function getTotalSalesBaits(): int
    {
        return SalesBaitLead::query()->count();
    }

    /**
     * Retrieves the total number of clicks made
     *
     * @return int
     */
    public function getTotalClicks(): int
    {
        return SalesBaitLead::query()->sum(SalesBaitLead::FIELD_CLICKS);
    }

    /**
     * Retrieves the total number of actions taken
     *
     * @return int
     */
    public function getTotalActionsTaken(): int
    {
        return SalesBaitLead::query()->sum(SalesBaitLead::FIELD_ACTIONS_TAKEN);
    }

    /**
     * Prepares sales bait collection against the requested filters
     *
     * @param string|null $state
     * @param string|null $county
     * @param string|null $industry
     * @param string|null $startDate
     * @param string|null $endDate
     *
     * @return Collection
     */
    public function getFilteredSalesBaits(
        ?string  $state,
        ?string  $county,
        ?string  $industry,
        ?string  $startDate,
        ?string  $endDate
    ): Collection
    {
        $query = SalesBaitLead::query();
        return $this->querySalesBaitLeadUsingFilters($query, $state, $county, $industry, $startDate, $endDate)
            ->groupBy(SalesBaitLead::TABLE .'.'. SalesBaitLead::FIELD_ID)->get();
    }

    /**
     * Prepares sales bait collection against the requested filters
     *
     * @param string|null $state
     * @param string|null $county
     * @param string|null $industry
     * @param string|null $startDate
     * @param string|null $endDate
     *
     * @return Builder
     */
    public function getFilteredSalesBaitsWithCompanyInformation(
        ?string  $state,
        ?string  $county,
        ?string  $industry,
        ?string  $startDate,
        ?string  $endDate
    ): Builder
    {
        $query = SalesBaitLead::query();

        $query = $query->selectRaw(
            SalesBaitLead::TABLE.'.'.SalesBaitLead::FIELD_LEAD_ID.
            ', count('.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID.
            ') as sent_to ,count('.SalesBaitLead::TABLE.'.'.SalesBaitLead::FIELD_CLICKS.') as clicked');

        $query = $this->querySalesBaitLeadUsingFilters($query, $state, $county, $industry, $startDate, $endDate)
            ->groupBy(SalesBaitLead::TABLE.'.'.SalesBaitLead::FIELD_LEAD_ID);

        return $query;
    }

    public function querySalesBaitLeadUsingFilters($query, ?string $state, ?string $county, ?string $industry, ?string $startDate, ?string $endDate)
    {
        $filteredSalesBaitLeads = [];
        $query
            ->join
            (
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE,
                SalesBaitLead::TABLE . '.' . SalesBaitLead::FIELD_COMPANY_ID,
                '=',
                EloquentCompany::TABLE . '.' . EloquentCompany::ID
            );
        if($industry || $state || $county) {

            if ($industry) {
                $industry = $industry == EloquentCompany::TYPE_ROOFER ? EloquentCompany::TYPE_ROOFER : EloquentCompany::TYPE_INSTALLER;
                $query->where(EloquentCompany::TABLE . '.' . EloquentCompany::TYPE, $industry);
            }

            if ($state || $county) {
                $query
                    ->join
                    (
                        DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLocation::TABLE,
                        SalesBaitLead::TABLE . '.' . SalesBaitLead::FIELD_CAMPAIGN_ID,
                        '=',
                        LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID
                    )
                    ->join
                    (
                        Location::TABLE,
                        LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LOCATION_ID,
                        '=',
                        Location::TABLE . '.' . Location::ID
                    );

                if ($state) {
                    $query->where(Location::TABLE . '.' . Location::STATE, $state);
                }
                if ($county) {
                    $query->where(Location::TABLE . '.' . Location::COUNTY, $county);
                }
            }

            $filteredSalesBaitLeads = $query
                ->distinct()
                ->pluck(SalesBaitLead::TABLE . '.' . SalesBaitLead::FIELD_LEAD_ID)
                ->toArray();
        }

        if($filteredSalesBaitLeads) {$query->whereIn(SalesBaitLead::FIELD_LEAD_ID, $filteredSalesBaitLeads);}
        if($startDate) {$query->whereDate(SalesBaitLead::TABLE . '.' . SalesBaitLead::FIELD_CREATED_AT, '>=', $startDate);}
        if($endDate) {$query->whereDate(SalesBaitLead::TABLE . '.' . SalesBaitLead::FIELD_CREATED_AT, '<=', $endDate);}

        return $query;
    }

    public function getCompaniesSentSalesBait($leadId): Collection
    {
        return SalesBaitLead::query()
            ->select(EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID, EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_NAME)
            ->join
            (
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentCompany::TABLE,
                SalesBaitLead::TABLE . '.' . SalesBaitLead::FIELD_COMPANY_ID,
                '=',
                EloquentCompany::TABLE . '.' . EloquentCompany::ID
            )
            ->where(SalesBaitLead::FIELD_LEAD_ID, $leadId)
            ->groupBy(SalesBaitLead::FIELD_COMPANY_ID)
            ->get();
    }
}
