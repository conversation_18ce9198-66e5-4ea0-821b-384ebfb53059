<?php

namespace App\Services\SalesBait;

use App\Events\SalesBait\PunterRecordedInterest;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRegisteredInterest;

class SalesBaitTrackingService
{
    /**
     * Returns whether a given punter has registered interest in a given sales bait.
     *
     * @param SalesBaitLead $salesBait
     * @param int $relationType
     * @param int $relationId
     * @return bool
     */
    public function hasPunterRegisteredInterest(SalesBaitLead $salesBait, int $relationType, int $relationId): bool
    {
        return SalesBaitRegisteredInterest::query()
                   ->where(SalesBaitRegisteredInterest::FIELD_SALES_BAIT_ID, $salesBait->id)
                   ->where(SalesBaitRegisteredInterest::FIELD_RELATION_TYPE, $relationType)
                   ->where(SalesBaitRegisteredInterest::FIELD_RELATION_ID, $relationId)
                   ->first() !== null;
    }

    /**
     * Records a link being clicked for a given sales bait.
     *
     * @param SalesBaitLead $salesBaitLead
     * @return bool'
     */
    public function recordLinkClicked(SalesBaitLead $salesBaitLead): bool
    {
        $salesBaitLead->clicks++;

        return $salesBaitLead->save();
    }

    /**
     * Records a punters interest in a sales bait.
     *
     * @param SalesBaitLead $salesBait
     * @param int $relationType
     * @param int $relationId
     * @return void
     */
    public function recordPunterInterest(SalesBaitLead $salesBait, int $relationType, int $relationId): void
    {
        /** @var SalesBaitRegisteredInterest $model */
        $model = SalesBaitRegisteredInterest::query()->updateOrCreate(
            [SalesBaitRegisteredInterest::FIELD_SALES_BAIT_ID => $salesBait->id, SalesBaitRegisteredInterest::FIELD_RELATION_TYPE => $relationType, SalesBaitRegisteredInterest::FIELD_RELATION_ID => $relationId],
            [SalesBaitRegisteredInterest::FIELD_SALES_BAIT_ID => $salesBait->id, SalesBaitRegisteredInterest::FIELD_RELATION_TYPE => $relationType, SalesBaitRegisteredInterest::FIELD_RELATION_ID => $relationId]
        );

        PunterRecordedInterest::dispatch($model->sales_bait_id, $model->id);
    }
}
