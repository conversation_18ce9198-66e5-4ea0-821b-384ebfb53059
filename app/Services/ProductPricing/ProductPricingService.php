<?php
namespace App\Services\ProductPricing;
use App\Contracts\Services\ProductPricingServiceContract;
use App\DataModels\Odin\Prices\BidPriceTable;
use App\DataModels\Odin\Prices\FloorPriceTable;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Enums\Odin\StateAbbreviation;
use App\Repositories\LocationRepository;
use App\Repositories\ProductPriceRepository;
use Exception;
use Illuminate\Support\Collection;

class ProductPricingService implements ProductPricingServiceContract
{
    /**
     * @param ProductPriceRepository $productPriceRepository
     */
    public function __construct(
        private ProductPriceRepository $productPriceRepository,
        protected LocationRepository $locationRepository
    ) {}

    /** @inheritDoc */
    public function getFloorTable(
        int $serviceProductId,
        Product $product,
        string $industry,
        PropertyType $propertyType,
        ?StateAbbreviation $stateAbbreviation = null,
        ?array $countyKeys = null,
        ?QualityTier $qualityTier = null,
        SaleTypes|array|null $saleType = null
    ): FloorPriceTable
    {
        $floorPrices = new FloorPriceTable($product, $industry, $propertyType);

        if (!$stateAbbreviation) return $floorPrices;

        $stateLocation = $this->locationRepository->getStateByStateAbbr($stateAbbreviation->value);

        if ($countyKeys) {
            $countyLocation = $this->locationRepository->getCounty($stateLocation->state_key, $countyKeys[0]);
            $prices = $this->productPriceRepository->getCountyFloorPrices($serviceProductId, $stateLocation->id, $countyLocation->id, $propertyType, $saleType, $qualityTier);
        }
        else $prices = $this->productPriceRepository->getStateFloorPrices($serviceProductId, $stateLocation->id, $propertyType, $saleType, $qualityTier);

        $floorPrices->ingestData($prices);


        return $floorPrices;
    }

    /** @inheritDoc */
    public function getBidTable(
        int $serviceProductId,
        Product $product,
        string $industry,
        PropertyType $propertyType,
        Collection $productCampaignIds,
        ?StateAbbreviation $stateAbbreviation = null,
        ?string $countyKey = null,
        ?QualityTier $qualityTier = null
    ): BidPriceTable
    {
        $prices = $countyKey
            ? $this->productPriceRepository->getCountyBids($serviceProductId, $productCampaignIds, $stateAbbreviation, $propertyType, null, $qualityTier, $countyKey)
            : $this->productPriceRepository->getStateBids($serviceProductId, $productCampaignIds, $stateAbbreviation, $propertyType, null, $qualityTier);

        $priceTable = new BidPriceTable(
            $product,
            $industry,
            $propertyType,
            true,
            [$stateAbbreviation->value],
            $countyKey ? [$countyKey] : null
        );

        $priceTable->ingestData($prices);

        return $priceTable;
    }

    private function getLeadFloors(): FloorPriceTable
    {
        //TODO
    }


    //TODO
    private function getProductFloors(): FloorPriceTable
    {
        $leadFloorTable = $this->getLeadFloors();

        //return $leadFloorTable->convertToAppointment();
    }
}
