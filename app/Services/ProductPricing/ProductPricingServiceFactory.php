<?php

namespace App\Services\ProductPricing;

use App\Contracts\Services\ProductPricingServiceContract;

class ProductPricingServiceFactory
{
    const DRIVER_DEFAULT = 'default';
    const DRIVER_LEGACY = 'legacy';

    /**
     * @param string $driver
     * @return ProductPricingServiceContract
     */
    public static function make(string $driver = self::DRIVER_DEFAULT): ProductPricingServiceContract
    {
        return match($driver) {
            self::DRIVER_LEGACY => app(LegacyProductPricingService::class),
            default => app(ProductPricingService::class)
        };
    }
}
