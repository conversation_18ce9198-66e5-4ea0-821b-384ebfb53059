<?php
namespace App\Services\ProductPricing;
use App\Contracts\Services\ProductPricingServiceContract;
use App\DataModels\Odin\Prices\BidPriceTable;
use App\DataModels\Odin\Prices\FloorPriceTable;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Enums\Odin\StateAbbreviation;
use App\Repositories\Legacy\LeadPriceRepository;
use App\Repositories\Odin\ProductProcessing\FloorPriceFormulaRepository;
use Exception;
use Illuminate\Support\Collection;

class LegacyProductPricingService implements ProductPricingServiceContract
{
    /**
     * @param LeadPriceRepository $priceRepository
     * @param FloorPriceFormulaRepository $floorPriceFormulaRepository
     */
    public function __construct(
        private readonly LeadPriceRepository $priceRepository,
        private readonly FloorPriceFormulaRepository $floorPriceFormulaRepository
    ) {}

    /** @inheritDoc */
    public function getFloorTable(
        int $serviceProductId,
        Product $product,
        string $industry,
        PropertyType $propertyType,
        ?StateAbbreviation $stateAbbreviation = null,
        ?array $countyKeys = null,
        ?QualityTier $qualityTier = null,
        SaleTypes|array|null $saleType = null
    ): FloorPriceTable
    {
        return match ($product) {
            Product::LEAD => $this->getLeadFloors($industry, $propertyType, $stateAbbreviation, $countyKeys),
            Product::APPOINTMENT => $this->getAppointmentFloors($industry, $propertyType, $stateAbbreviation, $countyKeys),
            default => new FloorPriceTable(),
        };
    }

    /** @inheritDoc */
    public function getBidTable(
        int $serviceProductId,
        Product $product,
        string $industry,
        PropertyType $propertyType,
        Collection $productCampaignIds,
        ?StateAbbreviation $stateAbbreviation = null,
        ?string $countyKey = null,
        ?QualityTier $qualityTier = null
    ): BidPriceTable
    {
        return match ($product) {
            Product::LEAD => $this->getLeadBids($productCampaignIds, $industry, $propertyType, $stateAbbreviation, $countyKey),
            default => new BidPriceTable(),
        };
    }

    /**
     * @param string $industry
     * @param PropertyType $propertyType
     * @param StateAbbreviation|null $stateAbbreviation
     * @param array|null $countyKeys
     * @return FloorPriceTable
     */
    private function getLeadFloors(string $industry, PropertyType $propertyType, ?StateAbbreviation $stateAbbreviation = null, ?array $countyKeys = null): FloorPriceTable
    {
        $prices = $stateAbbreviation ?
            $this->priceRepository->getCountyFloors($industry, $propertyType, $stateAbbreviation, $countyKeys) :
            $this->priceRepository->getStateFloors($industry, $propertyType);

        $priceTable = new FloorPriceTable(
            Product::LEAD,
            $industry,
            $propertyType,
            $stateAbbreviation !== null,
            $stateAbbreviation ? [$stateAbbreviation->value] : null,
            $countyKeys
        );

        $priceTable->ingestLegacyData($prices);

        return $priceTable;
    }

    /**
     * @param string $industry
     * @param PropertyType $propertyType
     * @param StateAbbreviation|null $stateAbbreviation
     * @param array|null $countyKeys
     * @return FloorPriceTable
     * @throws Exception
     */
    private function getAppointmentFloors(string $industry, PropertyType $propertyType, ?StateAbbreviation $stateAbbreviation = null, ?array $countyKeys = null): FloorPriceTable
    {
        $priceTable = new FloorPriceTable(
            Product::APPOINTMENT,
            $industry,
            $propertyType,
            $stateAbbreviation !== null,
            $stateAbbreviation ? [$stateAbbreviation->value] : null,
            $countyKeys
        );

        $priceTable->applyFormulaeAndCalculatePrices(
            $this->getLeadFloors($industry, $propertyType, $stateAbbreviation, $countyKeys),
            $this->floorPriceFormulaRepository->getFormulasByStates($priceTable->getAllStates())
        );

        return $priceTable;
    }

    /**
     * @param Collection $campaignIds
     * @param string $industry
     * @param PropertyType $propertyType
     * @param StateAbbreviation $stateAbbreviation
     * @param string|null $countyKey
     * @return BidPriceTable
     */
    private function getLeadBids(Collection $campaignIds, string $industry, PropertyType $propertyType, StateAbbreviation $stateAbbreviation, ?string $countyKey = null): BidPriceTable
    {
        $prices = $countyKey
                ? $this->priceRepository->getCountyBids($campaignIds, $industry, $propertyType, $stateAbbreviation, $countyKey)
                : $this->priceRepository->getStateBids($campaignIds, $industry, $propertyType, $stateAbbreviation);

        $priceTable = new BidPriceTable(
            Product::LEAD,
            $industry,
            $propertyType,
            true,
            [$stateAbbreviation->value],
            $countyKey ? [$countyKey] : null
        );

        $priceTable->ingestLegacyData($prices);

        return $priceTable;
    }
}
