<?php

namespace App\Services\ProductPricing\BestRevenueScenario;

use App\Abstracts\ProductPricing\BestRevenueScenarioServiceAbstract;
use Illuminate\Contracts\Container\BindingResolutionException;

class BestRevenueScenarioServiceFactory
{
    const DRIVER_LEGACY  = 'legacy';
    const DRIVER_ODIN    = 'odin';
    const DRIVER_DUMMY   = 'dummy';
    const DRIVER_DEFAULT = 'default';

    /**
     * @param string $driver
     * @return BestRevenueScenarioServiceAbstract
     * @throws BindingResolutionException
     */
    public static function make(string $driver = self::DRIVER_DEFAULT): BestRevenueScenarioServiceAbstract
    {
        return match ($driver) {
            self::DRIVER_LEGACY => app(LegacyBestRevenueScenarioService::class),
            self::DRIVER_ODIN => app()->make(OdinBestRevenueScenarioService::class),
            self::DRIVER_DUMMY => app()->make(DummyBestRevenueScenarioService::class),
            default => app(BestRevenueScenarioService::class)
        };
    }
}
