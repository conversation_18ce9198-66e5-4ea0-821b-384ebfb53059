<?php

namespace App\Services\ProductPricing\BestRevenueScenario;

use App\Abstracts\ProductPricing\BestRevenueScenarioServiceAbstract;
use App\Builders\LegacyBrsPotentialCampaignsBuilder;
use App\DataModels\Odin\Prices\BidPriceTable;
use App\DataModels\Odin\Prices\BRSCampaignPrices;
use App\DataModels\Odin\Prices\BudgetUsageData;
use App\DataModels\Odin\Prices\FilteredBRSPrices;
use App\DataModels\Odin\Prices\FloorPriceTable;
use App\DataModels\Odin\Prices\PotentialBRSCampaigns;
use App\DataModels\Odin\Prices\SaleTypeLimits;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SolarConfigurableFields;
use App\Enums\Odin\StateAbbreviation;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadSalesType;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ServiceProduct;
use App\Repositories\LeadCampaignRepository;
use App\Repositories\Legacy\DatabaseLocationRepository;
use App\Repositories\Odin\ProductCampaignBudgetRepository;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\ProductPricing\LegacyProductPricingService;
use App\Services\ProductPricing\ProductPricingService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Throwable;

class LegacyBestRevenueScenarioService extends BestRevenueScenarioServiceAbstract
{
    const EXCLUSIVE_SALES_TYPE_ID = 1;
    const DUO_SALES_TYPE_ID = 2;
    const TRIO_SALES_TYPE_ID = 3;
    const QUAD_SALES_TYPE_ID = 4;
    const UNVERIFIED_SALES_TYPE_ID = 5;
    const EMAIL_ONLY_SALES_TYPE_ID = 6;

    const ARRAY_KEY_CONSUMER_PRODUCT = 'consumerProduct';
    const ARRAY_KEY_PRODUCT = 'product';
    const ARRAY_KEY_INDUSTRY = 'industry';
    const ARRAY_KEY_LEGACY_LEAD = 'legacyLead';
    const ARRAY_KEY_ZIPCODE = 'zipcode';
    const ARRAY_KEY_STATE_ABBR = 'stateAbbr';
    const ARRAY_KEY_SALE_TYPE_IDS = 'saleTypeIds';
    const ARRAY_KEY_LEAD_CATEGORY_ID = 'leadCategoryId';
    const ARRAY_KEY_REJECTION_PERCENTAGE_THRESHOLD = 'rejectionPercentageThreshold';

    /**
     * @param LegacyProductPricingService $legacyProductPricingService
     * @param ProductPricingService $productPricingService
     * @param ProductCampaignBudgetRepository $productCampaignBudgetRepository
     * @param LeadCampaignRepository $leadCampaignRepository
     */
    public function __construct(
        private LegacyProductPricingService $legacyProductPricingService,
        private ProductPricingService $productPricingService,
        private ProductCampaignBudgetRepository $productCampaignBudgetRepository,
        private LeadCampaignRepository $leadCampaignRepository
    ) {

    }

    /** @inheritDoc */
    public function getPotentialCampaigns(int $consumerProductId, ?array $excludedCompanyIds = [], ?int $overrideSaleTypeId = null): ?PotentialBRSCampaigns
    {
        extract($this->getConsumerProductInformation($consumerProductId, $overrideSaleTypeId));

        $queryBuilder = LegacyBrsPotentialCampaignsBuilder::query(ProductEnum::tryFrom($product), $consumerProductId);

        if($product === ProductEnum::LEAD->value) {
            if($industry === IndustryEnum::SOLAR->value) {
                $companyTypes = [EloquentCompany::TYPE_INSTALLER, EloquentCompany::TYPE_AGGREGATOR];
            }
            else if($industry === IndustryEnum::ROOFING->value) {
                $companyTypes = [EloquentCompany::TYPE_ROOFER];
            }

            $excludedLegacyCompanyIds = Company::query()
                ->whereIn(Company::FIELD_ID, $excludedCompanyIds ?? [])
                ->pluck(Company::FIELD_LEGACY_ID)
                ->filter()
                ->toArray();

            $queryBuilder->attachLeadConstraints(
                $rejectionPercentageThreshold,
                $excludedLegacyCompanyIds,
                $companyTypes
            );
        }
        else if($product === ProductEnum::APPOINTMENT->value) {
            $queryBuilder->attachAppointmentConstraints(
                $industry,
                $excludedCompanyIds ?? []
            );
        }

        $queryBuilder->attachSharedConstraints(
            $saleTypeIds,
            $leadCategoryId,
            $zipcode,
            $industry,
            $stateAbbr,
            $legacyLead->getUtilityId(),
            $legacyLead->getUtilityName()
        );

        $campaigns = $queryBuilder->get();

        $propertyType = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD}[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value;
        $propertyType = ucwords(strtolower($propertyType));

        $countyKey = app(DatabaseLocationRepository::class)->getZipCode($zipcode)->{Location::COUNTY_KEY};

        $leadTypeId = $industry === IndustryEnum::ROOFING->value ? $legacyLead->{EloquentQuote::ROOFING_LEAD_TYPE_ID} : $legacyLead->{EloquentQuote::LEAD_TYPE_ID};

        if($product === ProductEnum::LEAD->value) {
            $qualityTier = $leadTypeId === 2 ? QualityTier::PREMIUM : QualityTier::STANDARD;
        }
        else if($product === ProductEnum::APPOINTMENT->value) {
            $qualityTier = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT_TYPE};
        }

        $potentialBrsCampaigns = new PotentialBRSCampaigns(
            $consumerProduct->{ConsumerProduct::FIELD_SERVICE_PRODUCT_ID},
            ProductEnum::tryFrom($product),
            $industry,
            PropertyType::tryFrom($propertyType),
            $qualityTier,
            StateAbbreviation::tryFrom(strtoupper($stateAbbr)),
            $countyKey
        );

        foreach($campaigns as $campaign) {
            $potentialBrsCampaigns->addCampaign(
                $campaign[LegacyBrsPotentialCampaignsBuilder::SALE_TYPE_ID_COL],
                $campaign[LegacyBrsPotentialCampaignsBuilder::CAMPAIGN_ID_COL],
                $campaign[LegacyBrsPotentialCampaignsBuilder::COMPANY_ID_COL],
                $campaign[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY] ?? 0,
                $campaign[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID] ?? 0
            );
        }

        return $potentialBrsCampaigns;
    }

    /** @inheritDoc */
    public function populatePrices(PotentialBRSCampaigns $potentialBRSCampaigns): ?BRSCampaignPrices
    {
        $floorTable = $this->legacyProductPricingService->getFloorTable(
            $potentialBRSCampaigns->serviceProductId,
            $potentialBRSCampaigns->product,
            $potentialBRSCampaigns->industry,
            $potentialBRSCampaigns->propertyType,
            $potentialBRSCampaigns->stateAbbreviation,
            [$potentialBRSCampaigns->countyKey]
        )->toArray();

        $productCampaignIds = $potentialBRSCampaigns->getCampaignIds()->collapse()->unique();

        if($potentialBRSCampaigns->product === ProductEnum::LEAD) {
            $bidTable = $this->legacyProductPricingService->getBidTable(
                $potentialBRSCampaigns->serviceProductId,
                $potentialBRSCampaigns->product,
                $potentialBRSCampaigns->industry,
                $potentialBRSCampaigns->propertyType,
                $productCampaignIds,
                $potentialBRSCampaigns->stateAbbreviation,
                $potentialBRSCampaigns->countyKey
            )->toArray();
        }
        else {
            $bidTable = $this->productPricingService->getBidTable(
                $potentialBRSCampaigns->serviceProductId,
                $potentialBRSCampaigns->product,
                $potentialBRSCampaigns->industry,
                $potentialBRSCampaigns->propertyType,
                $productCampaignIds,
                $potentialBRSCampaigns->stateAbbreviation,
                $potentialBRSCampaigns->countyKey
            )->toArray();
        }

        $qualityTier = $potentialBRSCampaigns->qualityTier->value;
        $stateAbbr = $potentialBRSCampaigns->stateAbbreviation->value;
        $countyKey = $potentialBRSCampaigns->countyKey;

        $stateFloorPrices = $floorTable[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_SALES_TYPES];
        $countyFloorPrices = $floorTable[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_COUNTIES][$countyKey][FloorPriceTable::ARRAY_KEY_SALES_TYPES];

        $stateBidPrices = $bidTable[BidPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][BidPriceTable::ARRAY_KEY_STATES][$stateAbbr][BidPriceTable::ARRAY_KEY_SALES_TYPES];
        $countyBidPrices = $bidTable[BidPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][BidPriceTable::ARRAY_KEY_STATES][$stateAbbr][BidPriceTable::ARRAY_KEY_COUNTIES][$countyKey][BidPriceTable::ARRAY_KEY_SALES_TYPES];

        $leadSalesTypes = LeadSalesType::query()->pluck(LeadSalesType::KEY_VALUE, LeadSalesType::ID)->toArray();

        $brsCampaignPrices = new BRSCampaignPrices(
            $potentialBRSCampaigns->product,
            $potentialBRSCampaigns->qualityTier
        );

        foreach($potentialBRSCampaigns as $saleTypeId => $campaigns) {
            $saleTypeKey = ucwords(strtolower($leadSalesTypes[$saleTypeId]));

            //might need better null coalescing
            $stateSaleTypeFloorPrice = $stateFloorPrices[$saleTypeKey][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;
            $countySaleTypeFloorPrice = $countyFloorPrices[$saleTypeKey][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;

            foreach($campaigns as $productCampaignId => $campaignInfo) {
                $countySaleTypeBidPrice = $countyBidPrices[$saleTypeKey][$productCampaignId][BidPriceTable::ARRAY_KEY_PRICE] ?? null;
                $stateSaleTypeBidPrice = $stateBidPrices[$saleTypeKey][$productCampaignId][BidPriceTable::ARRAY_KEY_PRICE] ?? null;

                $unrectifiedPrice = round($countySaleTypeBidPrice ?? $stateSaleTypeBidPrice ?? $countySaleTypeFloorPrice ?? $stateSaleTypeFloorPrice ?? 0.00, 2);

                if($unrectifiedPrice > 0.00) {
                    $price = $unrectifiedPrice * (1 - ($campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID] / 100));

                    $brsCampaignPrices->setPrice(
                        $saleTypeId,
                        $productCampaignId,
                        $campaignInfo[PotentialBRSCampaigns::COMPANY_ID],
                        $price,
                        $unrectifiedPrice,
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        $campaignInfo[PotentialBRSCampaigns::SCHEDULE_ID] ?? 0
                    );
                }
            }
        }

        return $brsCampaignPrices;
    }

    /** @inheritDoc */
    public function filterOverBudgetCampaigns(BRSCampaignPrices $BRSCampaignPrices, int $consumerProductId): ?FilteredBRSPrices
    {
        $productCampaignIds = $BRSCampaignPrices->getCampaignIds()->collapse()->unique()->toArray();

        if($BRSCampaignPrices->product === ProductEnum::LEAD) {
            $budgetUsageData = $this->leadCampaignRepository->getLeadCampaignBudgetUsage($productCampaignIds, true);
        }
        else {
            $budgetUsageData = $this->productCampaignBudgetRepository->getProductCampaignBudgetUsage($productCampaignIds, $BRSCampaignPrices->qualityTier);

            AppointmentService::writeAppointmentLog(
                "Budget usage data: $consumerProductId",
                [
                    'budget_usage_data' => $budgetUsageData->toArray(),
                    'type' => $BRSCampaignPrices->qualityTier->value,
                    'campaign_ids' => $BRSCampaignPrices->getCampaignCompanyIds()->toArray()
                ]
            );
        }

        $filteredBrsPrices = new FilteredBRSPrices(
            $BRSCampaignPrices->qualityTier
        );

        $noLimitLabel = ucwords(str_replace('_', ' ', ProductCampaignBudget::VALUE_TYPE_NO_LIMIT));

        $productBudgetUnits = [LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS];

        foreach($BRSCampaignPrices as $saleTypeId => $campaignPrices) {
            $budgetCategory = match($saleTypeId) {
                self::EXCLUSIVE_SALES_TYPE_ID, self::DUO_SALES_TYPE_ID, self::TRIO_SALES_TYPE_ID, self::QUAD_SALES_TYPE_ID => BudgetCategory::VERIFIED,
                self::UNVERIFIED_SALES_TYPE_ID => BudgetCategory::UNVERIFIED,
                self::EMAIL_ONLY_SALES_TYPE_ID => BudgetCategory::EMAIL_ONLY
            };

            foreach($campaignPrices as $campaignId => $campaignInfo) {
                $campaignBudgetUsageData = $budgetUsageData->getBudgetUsageData($campaignId);

                if($campaignBudgetUsageData->isEmpty()) {
                    AppointmentService::writeAppointmentLog(
                        "No campaign budget usage data: $consumerProductId",
                        [
                            'campaign_id' => $campaignId,
                            'sale_type_id' => $saleTypeId
                        ]
                    );

                    continue;
                }

                $budgetData = $campaignBudgetUsageData->get(BudgetUsageData::BUDGET_INFO)->get($budgetCategory->value);
                $companyData = $campaignBudgetUsageData->get(BudgetUsageData::COMPANY_INFO);

                if(empty($budgetData->get(BudgetUsageData::BUDGET_UNIT))) {
                    AppointmentService::writeAppointmentLog(
                        "No budget unit: $consumerProductId",
                        [
                            'budget_data' => $budgetData->toArray(),
                            'sale_type_id' => $saleTypeId
                        ]
                    );

                    continue;
                }
                else if(in_array($budgetData->get(BudgetUsageData::BUDGET_UNIT),  [LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED, ProductCampaignBudget::VALUE_TYPE_NO_LIMIT], true)) {
                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        $budgetData[BudgetUsageData::CAMPAIGN_BUDGET_ID],
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        0,
                        $noLimitLabel,
                        $campaignInfo[BRSCampaignPrices::SCHEDULE_ID] ?? 0
                    );

                    AppointmentService::writeAppointmentLog(
                        "Unlimited budget: $consumerProductId",
                        [
                            'budget_data' => $budgetData->toArray(),
                            'sale_type_id' => $saleTypeId
                        ]
                    );

                    continue;
                }

                $dailyBudget = $budgetData->get(BudgetUsageData::DAILY_BUDGET);
                $budgetUnit = $budgetData->get(BudgetUsageData::BUDGET_UNIT);

                $estimatedPrice = in_array($budgetUnit, $productBudgetUnits, true) ? 1 : $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE];

                $saleTypeBudgetSpent = $budgetData->get(BudgetUsageData::BUDGET_SPENT);

                $budgetSpent = $saleTypeBudgetSpent + ($companyData->get(BudgetUsageData::NEVER_EXCEED_BUDGET) ? $estimatedPrice : 0);
                $usagePercentage = round($budgetSpent / $budgetData->get(BudgetUsageData::BUDGET_TIMEFRAME_DAYS) * 100 / $dailyBudget, 2);

                if($usagePercentage <= $budgetData->get(BudgetUsageData::MAX_BUDGET_USAGE)) {
                    $displayUnit = 'Dollars';

                    if(in_array($budgetUnit,  [LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS], true)) {
                        $displayUnit = 'Leads';
                    }
                    else if($budgetUnit === ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS) {
                        $displayUnit = 'Appointments';
                    }

                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        $budgetData[BudgetUsageData::CAMPAIGN_BUDGET_ID],
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        $usagePercentage,
                        "{$dailyBudget} {$displayUnit}",
                        $campaignInfo[BRSCampaignPrices::SCHEDULE_ID] ?? 0
                    );

                    AppointmentService::writeAppointmentLog(
                        "Price set after budget filtering: $consumerProductId",
                        [
                            'budget_data' => $budgetData->toArray(),
                            'sale_type_id' => $saleTypeId
                        ]
                    );
                }
            }
        }

        return $filteredBrsPrices;
    }

    /** @inheritDoc */
    public function getSaleTypeLimits(ConsumerProduct $product): SaleTypeLimits
    {
        $saleTypeLimits = new SaleTypeLimits();

        foreach(LeadSalesType::query()->pluck(LeadSalesType::SALE_LIMIT, LeadSalesType::ID)->toArray() as $id => $limit) {
            $saleTypeLimits->setLimit($id, $limit);
        }

        return $saleTypeLimits;
    }

    /**
     * @param int $consumerProductId
     * @param int|null $overrideSaleTypeId
     * @return array
     * @throws Exception
     */
    private function getConsumerProductInformation(int $consumerProductId, ?int $overrideSaleTypeId = null): array
    {
        $consumerProduct = ConsumerProduct::query()
            ->with([
                ConsumerProduct::RELATION_ADDRESS,
                ConsumerProduct::RELATION_CONSUMER,
                ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
                ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT,
                ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY,
                ConsumerProduct::RELATION_APPOINTMENT
            ])
            ->where(ConsumerProduct::FIELD_ID, $consumerProductId)
            ->firstOrFail();

        $product = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{ProductModel::FIELD_NAME};

        $industry = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{IndustryModel::FIELD_NAME};

        $minElecSpend = match($product) {
            ProductEnum::LEAD->value => config('sales.leads.minimum_electric_spend'),
            ProductEnum::APPOINTMENT->value => config('sales.appointments.minimum_electric_spend')
        };

        $errorMsgs = [];
        if(!in_array($product, [ProductEnum::APPOINTMENT->value, ProductEnum::LEAD->value], true)) {
            $errorMsgs[] = "Not appointment or lead";
        }
        else if(!in_array($industry, [IndustryEnum::ROOFING->value, IndustryEnum::SOLAR->value], true)) {
            $errorMsgs[] = "Invalid legacy industry: $industry";
        }
        else if(empty($consumerProduct->{ConsumerProduct::FIELD_GOOD_TO_SELL})) {
            $errorMsgs[] = "Not good to sell";
        }
        else if($consumerProduct->{ConsumerProduct::FIELD_STATUS} === ConsumerProduct::STATUS_CANCELLED) {
            $errorMsgs[] = "Was cancelled";
        }
        else if(($consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}?->{ConsumerProductData::FIELD_PAYLOAD}[SolarConfigurableFields::ELECTRIC_COST->value] ?? 0) < $minElecSpend) {
            $errorMsgs[] = "Electric spend below minimum";
        }

        if(!empty($errorMsgs)) {
            throw new Exception(__METHOD__.': Consumer product issues - '.implode(', ', $errorMsgs));
        }

        $legacyLead = EloquentQuote::query()
            ->where(EloquentQuote::ID, $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_LEGACY_ID})
            ->firstOrFail();

        $zipcode = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_ZIP_CODE};

        $stateAbbr = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_STATE};

        if(in_array($consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_CLASSIFICATION}, [Consumer::CLASSIFICATION_EMAIL_ONLY, Consumer::CLASSIFICATION_VERIFIED_EMAIL_ONLY], true)) {
            $emailOnlySalesTypeId = LeadSalesType::query()->where(LeadSalesType::KEY_VALUE, LeadSalesType::KEY_VALUE_EMAIL_ONLY)->first()->{LeadSalesType::ID};
            $saleTypeIds = [$emailOnlySalesTypeId];
        }
        else if($consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_CLASSIFICATION} === Consumer::CLASSIFICATION_UNVERIFIED_PHONE
            && !empty($consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_PHONE})) {
            $unverifiedSalesTypeId = LeadSalesType::query()->where(LeadSalesType::KEY_VALUE, LeadSalesType::KEY_VALUE_UNVERIFIED)->first()->{LeadSalesType::ID};
            $saleTypeIds = [$unverifiedSalesTypeId];
        }
        else if($product === ProductEnum::LEAD->value) {
            $saleTypeIds = $overrideSaleTypeId ? [$overrideSaleTypeId] : range(1, $consumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS});
        }
        else if($product === ProductEnum::APPOINTMENT->value) {
            $countApptsRequested = ProductAppointment::query()
                ->where(ProductAppointment::LEAD_CONSUMER_PRODUCT_ID, $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID})
                ->count();

            $saleTypeIds = $overrideSaleTypeId ? [$overrideSaleTypeId] : range(1, $countApptsRequested);
        }
        else {
            throw new Exception(__METHOD__.": Unable to obtain sale type ID's");
        }

        $leadCategoryId = $legacyLead->{EloquentQuote::LEAD_CATEGORY_ID};

        $rejectionPercentageThreshold = match($product) {
            ProductEnum::LEAD->value => ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY === ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE ?
                config('sales.leads.crm_rejection_percentage_threshold') :
                config('sales.leads.overall_rejection_percentage_threshold'),
            ProductEnum::APPOINTMENT->value => ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY === ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE ?
                config('sales.appointments.crm_rejection_percentage_threshold') :
                config('sales.appointments.overall_rejection_percentage_threshold')
        };

        return [
            self::ARRAY_KEY_CONSUMER_PRODUCT => $consumerProduct,
            self::ARRAY_KEY_PRODUCT => $product,
            self::ARRAY_KEY_INDUSTRY => $industry,
            self::ARRAY_KEY_LEGACY_LEAD => $legacyLead,
            self::ARRAY_KEY_ZIPCODE => $zipcode,
            self::ARRAY_KEY_STATE_ABBR =>$stateAbbr ,
            self::ARRAY_KEY_SALE_TYPE_IDS => $saleTypeIds,
            self::ARRAY_KEY_LEAD_CATEGORY_ID => $leadCategoryId,
            self::ARRAY_KEY_REJECTION_PERCENTAGE_THRESHOLD => $rejectionPercentageThreshold
        ];
    }

    /**
     * @inheritDoc
     */
    public function investigateAllocationFailure(Company $company, ConsumerProduct $consumerProduct, ?ProductCampaign $productCampaign = null, ?array $excludedCompanyIds = []): string
    {
        try {
            $companyId = $company->{Company::FIELD_ID};
            $productCampaignId = $productCampaign?->{ProductCampaign::FIELD_ID};
            $product = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{ProductModel::FIELD_NAME};

            $checkFunc = function (Builder $query) use ($companyId, $productCampaignId) {
                if($productCampaignId) {
                    $campaignIds = $query
                        ->selectRaw(implode(',', [
                            ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_COMPANY_ID . " AS " . LegacyBrsPotentialCampaignsBuilder::COMPANY_ID_COL,
                            ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_ID . " AS " . LegacyBrsPotentialCampaignsBuilder::CAMPAIGN_ID_COL
                        ]))
                        ->pluck(LegacyBrsPotentialCampaignsBuilder::COMPANY_ID_COL, LegacyBrsPotentialCampaignsBuilder::CAMPAIGN_ID_COL);

                    return $campaignIds->get($productCampaignId) === $companyId;
                }
                else {
                    $query->selectRaw(ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_COMPANY_ID . " AS " . LegacyBrsPotentialCampaignsBuilder::COMPANY_ID_COL);

                    return in_array($companyId, $query->pluck(LegacyBrsPotentialCampaignsBuilder::COMPANY_ID_COL)->toArray(), true);
                }
            };

            $results = [];

            if ($product === ProductEnum::APPOINTMENT->value) {
                $results[] = $this->investigateAppointmentAllocationFailure($consumerProduct, $excludedCompanyIds, $checkFunc);

                $potentialBrsCampaigns = $this->getPotentialCampaigns($consumerProduct->{ConsumerProduct::FIELD_ID});

                if($potentialBrsCampaigns?->isNotEmpty()) {
                    $companyNames = Company::query()
                        ->whereIn(Company::FIELD_ID, $potentialBrsCampaigns->getCompanyIds())
                        ->pluck(Company::FIELD_NAME, Company::FIELD_ID)
                        ->toArray();

                    $campaignNames = ProductCampaign::query()
                        ->whereIn(ProductCampaign::FIELD_ID, $potentialBrsCampaigns->getCampaignIds()->values()->collapse())
                        ->pluck(ProductCampaign::FIELD_NAME, ProductCampaign::FIELD_ID)
                        ->toArray();

                    $potentialAllocations = [];
                    foreach ($potentialBrsCampaigns as $saleTypeId => $campaigns) {
                        foreach($campaigns as $campaignId => $info) {
                            $potentialAllocations[] = sprintf(
                                "Company: %s - Sale Type: %s - Campaign: %s",
                                "{$companyNames[$info[PotentialBRSCampaigns::COMPANY_ID]]} ({$info[PotentialBRSCampaigns::COMPANY_ID]})",
                                $saleTypeId,
                                "{$campaignNames[$campaignId]} ({$campaignId})"
                            );
                        }
                    }

                    $results[] = "\nPotential allocations found:";
                    $results = array_merge($results, $potentialAllocations);
                }
                else {
                    $results[] = "Problem retrieving potential allocations";
                }

                return implode(';', $results);
            }
            else if ($product === ProductEnum::LEAD->value) {
                //TODO: implement for leads
            }
            else {
                throw new Exception("Invalid product: $product");
            }
        }
        catch(Throwable $e) {
            report($e);
        }

        return 'Problem investigating allocation failure';
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param array $excludedCompanyIds
     * @param $checkFunc
     * @return string
     * @throws Exception
     */
    private function investigateAppointmentAllocationFailure(
        ConsumerProduct $consumerProduct,
        array $excludedCompanyIds,
        $checkFunc
    ): string
    {
        extract($this->getConsumerProductInformation($consumerProduct->{ConsumerProduct::FIELD_ID}));

        $queryBuilder = LegacyBrsPotentialCampaignsBuilder::query(ProductEnum::APPOINTMENT, $consumerProduct->{ConsumerProduct::FIELD_ID});

        $query = $queryBuilder->getQuery();

        $queryBuilder->joinLeadCampaign($query);
        if (!$checkFunc(clone $query)) {
            return "Missing legacy lead campaign";
        }

        $queryBuilder->whereProductCampaignActive($query);
        if (!$checkFunc(clone $query)) {
            return "Appointment campaign is inactive";
        }

        $queryBuilder->joinProduct($query);
        $queryBuilder->whereProductIsAppointment($query);
        if (!$checkFunc(clone $query)) {
            return "Consumer product is not an appointment";
        }

        $queryBuilder->joinCompany($query);
        $queryBuilder->whereCompanyIsNotExcluded($query, $excludedCompanyIds);
        $queryBuilder->whereCompanyIsActive($query);
        if (!$checkFunc(clone $query)) {
            return "Company was either excluded or is inactive";
        }

        $queryBuilder->joinCompanyIndustry($query);
        $queryBuilder->joinIndustry($query);
        $queryBuilder->whereIndustryIs($query, $industry);
        if (!$checkFunc(clone $query)) {
            return "Company doesn't have '$industry' industry";
        }

        $queryBuilder->joinComputedRejectionStatistics($query);
        if (!$checkFunc(clone $query)) {
            return "Company doesn't have rejection statistics";
        }

        $queryBuilder->setQuery($query);

        $queryBuilder->whereLeadCampaignIsNotManagedByAdmin2();
        if (!$checkFunc($queryBuilder->getQuery())) {
            return "Lead campaigns are managed by Admin 2";
        }

        $queryBuilder->joinLeadCampaignLocation();
        $queryBuilder->joinLocation();
        $queryBuilder->whereZipCodeIs($zipcode);
        if (!$checkFunc($queryBuilder->getQuery())) {
            return "Campaigns don't target zipcode $zipcode";
        }

        $queryBuilder->joinLeadCampaignSalesTypeConfiguration();
        $queryBuilder->whereLeadCampaignSalesTypeConfigurationIn($saleTypeIds);
        if (!$checkFunc($queryBuilder->getQuery())) {
            return "Campaigns don't have sale types setup";
        }

        $queryBuilder->joinLeadCampaignLeadCategory();
        $queryBuilder->whereLeadCategoryIs($leadCategoryId);
        if (!$checkFunc($queryBuilder->getQuery())) {
            return "Campaigns don't target lead category $leadCategoryId";
        }

        if (
            config('sales.are_utility_filters_active') &&
            $industry === IndustryEnum::SOLAR->value
        ) {
            $utilityId = $legacyLead->getUtilityId();

            $queryBuilder->whereUtilityIs($stateAbbr, $utilityId);
            if (!$checkFunc($queryBuilder->getQuery())) {
                return "Campaigns don't target utility '" . $legacyLead->getUtilityName() . "' ($utilityId) in $stateAbbr";
            }
        }

        return '';
    }
}
