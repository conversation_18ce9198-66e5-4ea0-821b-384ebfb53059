<?php

namespace App\Services\ProductPricing\BestRevenueScenario;

use App\Models\BestRevenueScenarioLog;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;

class BestRevenueScenarioLogsService
{
    /**
     * @param int $companyId
     * @return EloquentBuilder
     */
    public function getCompanyLogsQuery(int $companyId): EloquentBuilder
    {
        return BestRevenueScenarioLog::query()
            ->with([
                BestRevenueScenarioLog::RELATION_PRODUCT_CAMPAIGN,
                BestRevenueScenarioLog::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER
            ])
            ->whereHas(BestRevenueScenarioLog::RELATION_COMPANY, function ($where) use ($companyId) {
                $where->where(Company::TABLE.'.'.Company::FIELD_ID, $companyId);
            })
            ->orderBy(BestRevenueScenarioLog::FIELD_CONSUMER_PRODUCT_ID, 'desc')
            ->orderBy(BestRevenueScenarioLog::FIELD_STEP)
            ->orderBy(BestRevenueScenarioLog::FIELD_SALE_TYPE_ID);
    }
}
