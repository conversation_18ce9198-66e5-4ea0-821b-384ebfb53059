<?php

namespace App\Services\ProductPricing\BestRevenueScenario;

use App\Abstracts\ProductPricing\BestRevenueScenarioServiceAbstract;
use App\DataModels\Odin\Prices\BRSCampaignPrices;
use App\DataModels\Odin\Prices\FilteredBRSPrices;
use App\DataModels\Odin\Prices\PotentialBRSCampaigns;
use App\DataModels\Odin\Prices\SaleTypeLimits;
use App\Enums\Odin\Industry;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\StateAbbreviation;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductCampaign;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\Product as ProductModel;
use App\Models\SaleType;

class DummyBestRevenueScenarioService extends BestRevenueScenarioServiceAbstract
{
    /**
     * @inheritDoc
     */
    public function getPotentialCampaigns(int $consumerProductId, ?array $excludedCompanyIds = [], ?int $overrideSaleTypeId = null): ?PotentialBRSCampaigns
    {
        $potentialBRSCampaigns = new PotentialBRSCampaigns(
            1,
            ProductEnum::APPOINTMENT,
            Industry::SOLAR->value,
            PropertyType::RESIDENTIAL,
            QualityTier::IN_HOME,
            StateAbbreviation::CA,
            'los-angeles'
        );

        $apptProductCampaignIds = ProductCampaign::query()
            ->whereHas(ProductCampaign::RELATION_PRODUCT, function($has) {
                $has->where(ProductModel::FIELD_NAME, ProductEnum::APPOINTMENT);
            })
            ->pluck(ProductCampaign::FIELD_COMPANY_ID, ProductCampaign::FIELD_ID)
            ->toArray();

        $campaignsCount = $overrideSaleTypeId ?? 4;

        for($i = 1; $i <= $campaignsCount; $i++) {
            foreach($apptProductCampaignIds as $productCampaignId => $companyId) {
                $potentialBRSCampaigns->addCampaign(
                    $i,
                    $productCampaignId,
                    $companyId,
                    0,
                    0
                );
            }
        }

        return $potentialBRSCampaigns;
    }

    /**
     * @inheritDoc
     */
    public function populatePrices(PotentialBRSCampaigns $potentialBRSCampaigns): ?BRSCampaignPrices
    {
        $brsCampaignPrices = new BRSCampaignPrices(
            $potentialBRSCampaigns->product,
            $potentialBRSCampaigns->qualityTier
        );

        foreach($potentialBRSCampaigns as $saleTypeId => $campaigns) {
            foreach($campaigns as $productCampaignId => $campaignInfo) {
                $brsCampaignPrices->setPrice(
                    $saleTypeId,
                    $productCampaignId,
                    $campaignInfo[PotentialBRSCampaigns::COMPANY_ID],
                    100,
                    100,
                    $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                    $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                    $campaignInfo[PotentialBRSCampaigns::SCHEDULE_ID] ?? 0
                );
            }
        }

        return $brsCampaignPrices;
    }

    /**
     * @inheritDoc
     */
    public function filterOverBudgetCampaigns(BRSCampaignPrices $BRSCampaignPrices, int $consumerProductId): ?FilteredBRSPrices
    {
        $filteredBrsPrices = new FilteredBRSPrices(
            $BRSCampaignPrices->qualityTier
        );

        foreach($BRSCampaignPrices as $saleTypeId => $campaignPrices) {
            foreach($campaignPrices as $campaignId => $campaignInfo) {
                $filteredBrsPrices->setPrice(
                    $saleTypeId,
                    $campaignId,
                    $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                    $campaignId,
                    $campaignInfo[BRSCampaignPrices::PRICE],
                    $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                    $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                    $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                    0,
                    "Test Budget",
                    $campaignInfo[BRSCampaignPrices::SCHEDULE_ID] ?? 0
                );
            }
        }

        return $filteredBrsPrices;
    }

    /**
     * @inheritDoc
     */
    public function getSaleTypeLimits(ConsumerProduct $product): SaleTypeLimits
    {
        $saleTypeLimits = new SaleTypeLimits();

        foreach(SaleType::query()->pluck(SaleType::FIELD_SALE_LIMIT, SaleType::FIELD_ID)->toArray() as $id => $limit) {
            $saleTypeLimits->setLimit($id, $limit);
        }

        return $saleTypeLimits;
    }

    /**
     * @inheritDoc
     */
    public function investigateAllocationFailure(Company $company, ConsumerProduct $consumerProduct, ?ProductCampaign $productCampaign = null, ?array $excludedCompanyIds = []): string
    {
        return 'Dummy';
    }
}
