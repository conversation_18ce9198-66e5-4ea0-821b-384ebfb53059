<?php

namespace App\Services\ProductPricing\BestRevenueScenario;

use App\Abstracts\ProductPricing\BestRevenueScenarioServiceAbstract;
use App\Abstracts\ProductPricing\PriceTableDataModelAbstract;
use App\DataModels\Odin\Prices\BidPriceTable;
use App\DataModels\Odin\Prices\BRSCampaignPrices;
use App\DataModels\Odin\Prices\BudgetUsageData;
use App\DataModels\Odin\Prices\FilteredBRSPrices;
use App\DataModels\Odin\Prices\FloorPriceTable;
use App\DataModels\Odin\Prices\PotentialBRSCampaigns;
use App\DataModels\Odin\Prices\SaleTypeLimits;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\StateAbbreviation;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentUtility;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLeadCategory;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadCampaignUtility;
use App\Models\Legacy\LeadSalesType;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Legacy\DatabaseLocationRepository;
use App\Repositories\Odin\ProductCampaignBudgetRepository;
use App\Services\DatabaseHelperService;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\ProductPricing\ProductPricingService;
use Exception;
use Illuminate\Support\Str;

class BestRevenueScenarioService extends BestRevenueScenarioServiceAbstract
{
    const EXCLUSIVE_SALES_TYPE_ID = 1;
    const DUO_SALES_TYPE_ID = 2;
    const TRIO_SALES_TYPE_ID = 3;
    const QUAD_SALES_TYPE_ID = 4;
    const UNVERIFIED_SALES_TYPE_ID = 5;
    const EMAIL_ONLY_SALES_TYPE_ID = 6;

    public function __construct(protected ProductPricingService $productPricingService, protected ProductCampaignBudgetRepository $productCampaignBudgetRepository) {}

    /**
     * @inheritDoc
     */
    public function getPotentialCampaigns(int $consumerProductId, ?array $excludedCompanyIds = [], ?int $overrideSaleTypeId = null): ?PotentialBRSCampaigns
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::query()
            ->where(ConsumerProduct::FIELD_ID, $consumerProductId)
            ->with([
                ConsumerProduct::RELATION_ADDRESS,
                ConsumerProduct::RELATION_CONSUMER,
                ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT,
                ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE.'.'.IndustryService::RELATION_INDUSTRY
            ])
            ->firstOrFail();

        $qualityTier = QualityTier::STANDARD; //todo: determine quality tier
        $leadCategory = 1; //todo: determine lead category

        $productSaleCategory = $this->determineSaleCategory($consumerProduct);
        $saleTypeId = 'sale_type_id';
        $campaignId = 'campaign_id';
        $companyId = 'company_id';

        $query = ProductCampaign::query()
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE,
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID
            )
            ->join(
                Company::TABLE,
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_COMPANY_ID,
                '=',
                Company::TABLE . '.' . Company::FIELD_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLeadCategory::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLeadCategory::TABLE . '.' . LeadCampaignLeadCategory::LEAD_CAMPAIGN_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLocation::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID
            )
            ->join(
                Location::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LOCATION_ID,
                '=',
                Location::TABLE . '.' . Location::ID
            )
            ->join(
                ProductCampaignBudget::TABLE,
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_ID,
                '=',
                ProductCampaignBudget::TABLE . '.' . ProductCampaignBudget::FIELD_PRODUCT_CAMPAIGN_ID
            )
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE,
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::ID,
                '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID
            )
            ->join(
                CompanyIndustry::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                '=',
                CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_COMPANY_ID
            )
            ->join(
                Product::TABLE,
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PRODUCT_ID,
                '=',
                Product::TABLE . '.' . Product::FIELD_ID
            )
            ->leftJoin(DatabaseHelperService::database(). '.' . ComputedRejectionStatistic::TABLE, function($join) {
                $join
                    ->on(
                        DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_COMPANY_ID,
                        '=',
                        DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_COMPANY_ID
                    )
                    ->on(
                        DatabaseHelperService::database().'.'.ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_PRODUCT_ID,
                        '=',
                        DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PRODUCT_ID
                    );
            })
            ->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value)
            ->where(CompanyIndustry::TABLE . '.' . CompanyIndustry::FIELD_INDUSTRY_ID, $consumerProduct->serviceProduct->service->industry->id)
            ->where(Product::TABLE . '.' . Product::FIELD_ID, $consumerProduct->serviceProduct->product->id)
            ->where(ProductCampaignBudget::TABLE . '.' . ProductCampaignBudget::FIELD_CATEGORY, $productSaleCategory)
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::STATUS, true)
            ->whereNull(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaign::TABLE . '.' . LeadCampaign::DELETED_AT)
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignLeadCategory::TABLE . '.' . LeadCampaignLeadCategory::LEAD_CATEGORY_ID, $leadCategory)
            ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::TABLE . '.' . Location::ZIP_CODE, $consumerProduct->address->zip_code)
            ->where(DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::STATUS, true);

        if (!empty($excludedCompanyIds)) $query->whereNotIn(Company::TABLE . '.' . Company::FIELD_ID, $excludedCompanyIds);

        $industry = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{IndustryModel::FIELD_NAME};

        $legacyLead = EloquentQuote::query()
            ->where(EloquentQuote::ID, $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_LEGACY_ID})
            ->firstOrFail();

        $stateAbbr = $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_STATE};

        if(
            config('sales.are_utility_filters_active') &&
            $industry === IndustryEnum::SOLAR->value &&
            trim(strtolower($legacyLead->getUtilityName())) !== 'other'
        ) {
            $legacyUtilityId = $legacyLead->getUtilityId();

            $query
                ->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignUtility::TABLE, function($join) {
                    $join
                        ->on(
                            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignUtility::TABLE.'.'.LeadCampaignUtility::LEAD_CAMPAIGN_ID,
                            '=',
                            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
                        );
                })
                ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentUtility::TABLE, function($join) {
                    $join
                        ->on(
                            DatabaseHelperService::readOnlyDatabase().'.'.EloquentUtility::TABLE.'.'.EloquentUtility::ID,
                            '=',
                            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignUtility::TABLE.'.'.LeadCampaignUtility::UTILITY_ID
                        );
                })
                ->where(EloquentUtility::TABLE.'.'.EloquentUtility::FIELD_STATE, $stateAbbr)
                ->where(EloquentUtility::TABLE.'.'.EloquentUtility::ID, $legacyUtilityId);
        }

        $query
            ->selectRaw(implode(',', [
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_ID . " AS {$campaignId}",
                DatabaseHelperService::readOnlyDatabase() . '.' . LeadCampaignSalesTypeConfiguration::TABLE . '.' . LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID . " AS {$saleTypeId}",
                ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_COMPANY_ID . " AS {$companyId}",
                "COALESCE(".ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY.", 0) AS " . ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY,
                "COALESCE(".ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_BID.", 0) AS " . ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID,
            ]))
            ->groupBy([
                $campaignId,
                $saleTypeId
            ]);

        if(config('sales.appointments.log_brs_queries')) {
            AppointmentService::writeAppointmentLog(
                "Legacy BRS query",
                [
                    "consumer_product_id" => $consumerProductId,
                    "query" => base64_encode(gzcompress(Str::replaceArray('?', $query->getBindings(), $query->toSql()), 9))
                ]
            );
        }

        $campaigns = $query->get();

        $potentialBrsCampaigns = new PotentialBRSCampaigns(
            serviceProductId: $consumerProduct->service_product_id,
            product: ProductEnum::tryFrom($consumerProduct->serviceProduct->product->name),
            industry: $consumerProduct->serviceProduct->service->industry->name,
            propertyType: PropertyType::tryFrom(ucfirst(strtolower($consumerProduct->consumerProductData->payload[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value))),
            qualityTier: $qualityTier,
            stateAbbreviation: StateAbbreviation::tryFrom($consumerProduct->address->state),
            countyKey: app(DatabaseLocationRepository::class)->getZipCode($consumerProduct->address->zip_code)->county_key
        );

        foreach ($campaigns as $campaign) {
            $potentialBrsCampaigns->addCampaign(
                $campaign[$saleTypeId],
                $campaign[$campaignId],
                $campaign[$companyId],
                $campaign[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY] ?? 0,
                $campaign[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID] ?? 0,
            );
        }

        return $potentialBrsCampaigns;
    }

    /** @inheritDoc */
    public function populatePrices(PotentialBRSCampaigns $potentialBRSCampaigns): ?BRSCampaignPrices
    {
        $saleTypes = $potentialBRSCampaigns->product === ProductEnum::LEAD ? null : PriceTableDataModelAbstract::APPOINTMENT_SALES_TYPES;
        $appointmentQualityTier = $potentialBRSCampaigns->product === ProductEnum::LEAD ? null : $potentialBRSCampaigns->qualityTier;

        $floorPrices = $this->productPricingService->getFloorTable(
            $potentialBRSCampaigns->serviceProductId,
            $potentialBRSCampaigns->product,
            $potentialBRSCampaigns->industry,
            $potentialBRSCampaigns->propertyType,
            $potentialBRSCampaigns->stateAbbreviation,
            [$potentialBRSCampaigns->countyKey],
            $potentialBRSCampaigns->qualityTier,
            $saleTypes
        )->toArray();

        $bidPrices = $this->productPricingService->getBidTable(
            $potentialBRSCampaigns->serviceProductId,
            $potentialBRSCampaigns->product,
            $potentialBRSCampaigns->industry,
            $potentialBRSCampaigns->propertyType,
            $potentialBRSCampaigns->getCampaignIds()->collapse()->unique(),
            $potentialBRSCampaigns->stateAbbreviation,
            $potentialBRSCampaigns->countyKey,
            $appointmentQualityTier
        )->toArray();

        $qualityTier = $potentialBRSCampaigns->qualityTier->value;
        $stateAbbr = $potentialBRSCampaigns->stateAbbreviation->value;
        $countyKey = $potentialBRSCampaigns->countyKey;

        $stateFloorPrices = $floorPrices[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_SALES_TYPES];
        $countyFloorPrices = $floorPrices[FloorPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][FloorPriceTable::ARRAY_KEY_STATES][$stateAbbr][FloorPriceTable::ARRAY_KEY_COUNTIES][$countyKey][FloorPriceTable::ARRAY_KEY_SALES_TYPES];

        $stateBidPrices = $bidPrices[BidPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][BidPriceTable::ARRAY_KEY_STATES][$stateAbbr][BidPriceTable::ARRAY_KEY_SALES_TYPES];
        $countyBidPrices = $bidPrices[BidPriceTable::ARRAY_KEY_QUALITY_TIERS][$qualityTier][BidPriceTable::ARRAY_KEY_STATES][$stateAbbr][BidPriceTable::ARRAY_KEY_COUNTIES][$countyKey][BidPriceTable::ARRAY_KEY_SALES_TYPES];

        $brsCampaignPrices = new BRSCampaignPrices(
            $potentialBRSCampaigns->product,
            $potentialBRSCampaigns->qualityTier
        );

        $this->populateBRSCampaignPrices($brsCampaignPrices, $potentialBRSCampaigns, $stateFloorPrices, $countyFloorPrices, $stateBidPrices, $countyBidPrices);

        return $brsCampaignPrices;
    }

    /** @inheritDoc */
    public function filterOverBudgetCampaigns(BRSCampaignPrices $BRSCampaignPrices, int $consumerProductId): ?FilteredBRSPrices
    {
        $productCampaignIds = $BRSCampaignPrices->getCampaignIds()->collapse()->unique()->toArray();

        $budgetUsageData = $this->productCampaignBudgetRepository->getProductCampaignBudgetUsage($productCampaignIds, $BRSCampaignPrices->qualityTier);

        $filteredBrsPrices = new FilteredBRSPrices(
            $BRSCampaignPrices->qualityTier
        );

        $noLimitLabel = ucwords(str_replace('_', ' ', ProductCampaignBudget::VALUE_TYPE_NO_LIMIT));

        $productBudgetUnits = [LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS];

        foreach($BRSCampaignPrices as $saleTypeId => $campaignPrices) {
            $budgetCategory = match($saleTypeId) {
                self::EXCLUSIVE_SALES_TYPE_ID, self::DUO_SALES_TYPE_ID, self::TRIO_SALES_TYPE_ID, self::QUAD_SALES_TYPE_ID => BudgetCategory::VERIFIED,
                self::UNVERIFIED_SALES_TYPE_ID => BudgetCategory::UNVERIFIED,
                self::EMAIL_ONLY_SALES_TYPE_ID => BudgetCategory::EMAIL_ONLY
            };

            foreach($campaignPrices as $campaignId => $campaignInfo) {
                $campaignBudgetUsageData = $budgetUsageData->getBudgetUsageData($campaignId);
                $budgetData = $campaignBudgetUsageData->get(BudgetUsageData::BUDGET_INFO)->get($budgetCategory->value);
                $companyData = $campaignBudgetUsageData->get(BudgetUsageData::COMPANY_INFO);

                if(empty($budgetData->get(BudgetUsageData::BUDGET_UNIT))) {
                    continue;
                }
                else if(in_array($budgetData->get(BudgetUsageData::BUDGET_UNIT),  [LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED, ProductCampaignBudget::VALUE_TYPE_NO_LIMIT], true)) {
                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        $budgetData[BudgetUsageData::CAMPAIGN_BUDGET_ID],
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        0,
                        $noLimitLabel,
                        $campaignInfo[BRSCampaignPrices::SCHEDULE_ID] ?? 0
                    );

                    continue;
                }

                $dailyBudget = $budgetData->get(BudgetUsageData::DAILY_BUDGET);
                $budgetUnit = $budgetData->get(BudgetUsageData::BUDGET_UNIT);

                $estimatedPrice = in_array($budgetUnit, $productBudgetUnits, true) ? 1 : $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE];

                $saleTypeBudgetSpent = $budgetData->get(BudgetUsageData::BUDGET_SPENT);

                $budgetSpent = $saleTypeBudgetSpent + ($companyData->get(BudgetUsageData::NEVER_EXCEED_BUDGET) ? $estimatedPrice : 0);
                $usagePercentage = round($budgetSpent / $budgetData->get(BudgetUsageData::BUDGET_TIMEFRAME_DAYS) * 100 / $dailyBudget, 2);

                if($usagePercentage <= $budgetData->get(BudgetUsageData::MAX_BUDGET_USAGE)) {
                    $displayUnit = 'Dollars';

                    if(in_array($budgetUnit,  [LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS], true)) {
                        $displayUnit = 'Leads';
                    }
                    else if($budgetUnit === ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS) {
                        $displayUnit = 'Appointments';
                    }

                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        $budgetData[BudgetUsageData::CAMPAIGN_BUDGET_ID],
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        $usagePercentage,
                        "{$dailyBudget} {$displayUnit}",
                        $campaignInfo[BRSCampaignPrices::SCHEDULE_ID] ?? 0
                    );
                }
            }
        }

        return $filteredBrsPrices;
    }

    /** @inheritDoc */
    public function getSaleTypeLimits(ConsumerProduct $product): SaleTypeLimits
    {
        //todo: use SalesType Model instead of legacy model
        $saleTypeLimits = new SaleTypeLimits();

        foreach(LeadSalesType::query()->pluck(LeadSalesType::SALE_LIMIT, LeadSalesType::ID)->toArray() as $id => $limit) {
            $saleTypeLimits->setLimit($id, $limit);
        }

        return $saleTypeLimits;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return BudgetCategory
     */
    protected function determineSaleCategory(ConsumerProduct $consumerProduct): BudgetCategory
    {
        return match ($consumerProduct->consumer->classification) {
            Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS, Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL => BudgetCategory::VERIFIED,
            Consumer::CLASSIFICATION_EMAIL_ONLY, Consumer::CLASSIFICATION_VERIFIED_EMAIL_ONLY => BudgetCategory::EMAIL_ONLY,
            default => BudgetCategory::UNVERIFIED
        };
    }

    /**
     * @param BRSCampaignPrices $brsCampaignPrices
     * @param PotentialBRSCampaigns $potentialBRSCampaigns
     * @param array $stateFloorPrices
     * @param array $countyFloorPrices
     * @param array $stateBidPrices
     * @param array $countyBidPrices
     *
     * @return void
     * @throws Exception
     */
    protected function populateBRSCampaignPrices(
        BRSCampaignPrices $brsCampaignPrices,
        PotentialBRSCampaigns $potentialBRSCampaigns,
        array $stateFloorPrices,
        array $countyFloorPrices,
        array $stateBidPrices,
        array $countyBidPrices
    ): void
    {
        $leadSalesTypes = $this->getSalesTypes();

        foreach($potentialBRSCampaigns as $saleTypeId => $campaigns) {
            $saleTypeKey = ucwords(strtolower($leadSalesTypes[$saleTypeId]));

            $stateSaleTypeFloorPrice = $stateFloorPrices[$saleTypeKey][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;
            $countySaleTypeFloorPrice = $countyFloorPrices[$saleTypeKey][FloorPriceTable::ARRAY_KEY_PRICE] ?? null;

            foreach($campaigns as $productCampaignId => $campaignInfo) {
                $countySaleTypeBidPrice = $countyBidPrices[$saleTypeKey][$productCampaignId][BidPriceTable::ARRAY_KEY_PRICE] ?? null;
                $stateSaleTypeBidPrice = $stateBidPrices[$saleTypeKey][$productCampaignId][BidPriceTable::ARRAY_KEY_PRICE] ?? null;

                $unrectifiedPrice = round($countySaleTypeBidPrice ?? $stateSaleTypeBidPrice ?? $countySaleTypeFloorPrice ?? $stateSaleTypeFloorPrice ?? 0.00, 2);

                if($unrectifiedPrice > 0.00) {
                    $price = $unrectifiedPrice * (1 - ($campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID] / 100));

                    $brsCampaignPrices->setPrice(
                        $saleTypeId,
                        $productCampaignId,
                        $campaignInfo[PotentialBRSCampaigns::COMPANY_ID],
                        $price,
                        $unrectifiedPrice,
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        $campaignInfo[PotentialBRSCampaigns::SCHEDULE_ID] ?? 0
                    );
                }
            }
        }
    }

    /**
     * @return array
     */
    protected function getSalesTypes(): array
    {
        //todo: use SalesType Model instead of legacy model
        return LeadSalesType::query()->pluck(LeadSalesType::KEY_VALUE, LeadSalesType::ID)->toArray();
    }

    /**
     * @inheritDoc
     */
    public function investigateAllocationFailure(Company $company, ConsumerProduct $consumerProduct, ?ProductCampaign $productCampaign = null, ?array $excludedCompanyIds = []): string
    {
        //TODO: implement independent logic???
        return BestRevenueScenarioServiceFactory::make(BestRevenueScenarioServiceFactory::DRIVER_ODIN)->investigateAllocationFailure($company, $consumerProduct, $productCampaign, $excludedCompanyIds);
    }
}
