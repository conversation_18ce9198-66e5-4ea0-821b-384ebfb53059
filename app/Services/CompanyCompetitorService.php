<?php

namespace App\Services;

use App\Models\Odin\CompanyData;
use App\Repositories\CompetitorRepository;
use Illuminate\Database\Eloquent\Builder;

class CompanyCompetitorService
{
    const string STRATEGY_KEY_VALUE_PAIR = 'key_value_pair';
    const string STRATEGY_BUY_FROM_COMPETITORS_ARRAY = 'buys_from_competitors_array';

    protected string $queryStrategy;

    public function __construct(
        protected CompetitorRepository $competitorRepository
    ) {
        $this->queryStrategy = config('companies.search.filters.purchasing_from_competitor.query_strategy');
    }

    /**
     * @param  Builder  $query
     * @param  array  $options
     * @return void
     */
    public function queryIfCompanyHasAllCompetitors(Builder $query, array $options): void
    {
        $keyValuePairStrategy = function () use ($query, $options) {
            foreach ($options as $competitor) {
                $query->where(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->".$competitor, true);
            }
        };

        $buysFromCompetitorsArrayStrategy = function () use ($query, $options) {
            $query->whereJsonContains(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->buys_from_competitors",
                $options);
        };

        match ($this->queryStrategy) {
            self::STRATEGY_BUY_FROM_COMPETITORS_ARRAY => $buysFromCompetitorsArrayStrategy(),
            default => $keyValuePairStrategy(),
        };
    }

    /**
     * @param  Builder  $query
     * @param  array  $options
     * @return void
     */
    public function queryIfCompanyHasOneOrMoreOfCompetitors(Builder $query, array $options): void
    {
        $keyValuePairStrategy = function () use ($query, $options) {
            foreach ($options as $competitor) {
                $query->orWhere(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->".$competitor, true);
            }
        };

        $buysFromCompetitorsArrayStrategy = function () use ($query, $options) {
            foreach ($options as $competitor) {
                $query->orWhereJsonContains(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->buys_from_competitors",
                    $competitor);
            }
        };

        match ($this->queryStrategy) {
            self::STRATEGY_BUY_FROM_COMPETITORS_ARRAY => $buysFromCompetitorsArrayStrategy(),
            default => $keyValuePairStrategy(),
        };
    }
}
