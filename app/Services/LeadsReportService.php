<?php

namespace App\Services;

use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\LeadsReport\LeadsReportQueryEnum;
use App\Repositories\LeadsReportRepository;
use App\Repositories\Odin\IndustryRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LeadsReportService
{
    const string BASE_REPORT        = 'base_report';
    const string COMPARE_REPORT     = 'compare_report';
    const string RETURNED_COLUMNS   = 'returned_columns';
    const string FILTER_NAME        = 'name';
    const string FILTER_OPTIONS     = 'options';

    /**
     * @param LeadsReportRepository $leadsReportRepository
     * @param IndustryRepository $industryRepository
     */
    public function __construct(
        protected LeadsReportRepository     $leadsReportRepository,
        protected IndustryRepository        $industryRepository
    ) {}

    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param Carbon|null $startCompareDate
     * @param Carbon|null $endCompareDate
     * @param LeadsReportGroupEnum $group
     * @param mixed $filters
     * @param array $columns
     * @param Collection|null $limitedLocations
     * @param int|null $companyId
     * @param array|null $campaigns
     * @return array
     * @throws Exception
     */
    public function buildReport(
        Carbon $startDate,
        Carbon $endDate,
        ?Carbon $startCompareDate,
        ?Carbon $endCompareDate,
        LeadsReportGroupEnum $group,
        mixed $filters,
        array $columns,
        ?Collection $limitedLocations,
        ?int $companyId,
        ?array $campaigns,
    ): array
    {
        DB::statement('SET SESSION group_concat_max_len = 10000');
        $baseReport = [];
        $compareReport = $startCompareDate ? [] : null;

        // Check end date is not past current date
        if ($endDate->isFuture()) {
            $endDate = Carbon::now();
        }

        // Confirm base and compare date ranges match
        if ($startCompareDate && $endCompareDate) {
            if ($startDate->diffInDays($endDate) !== $startCompareDate->diffInDays($endCompareDate)) {
                $endCompareDate = $startCompareDate->copy()->addDays($startDate->diffInDays($endDate));
            }
        }

        // Add day to end dates for non-inclusive upper bound
        $endDate->addDay();
        $endCompareDate?->addDay();
        $returnedColumns = $this->getDependentColumns($columns);

        // Get data for each query
        foreach (LeadsReportQueryEnum::cases() as $queryType) {
            // Get Columns applicable for type
            $queryColumns = $this->getColumnsOfType($queryType, $returnedColumns, $group);

            // Get base data for query if columns present
            if (!empty($queryColumns)) {
                // Get query
                $builtQuery = $this->leadsReportRepository->getLeadReportQuery($queryType, $startDate, $endDate, $group, $queryColumns, $filters, $limitedLocations, $companyId, $campaigns);
                // Run query
                $baseResults = $builtQuery->get()->toArray();
                // Add query to report
                $this->addBaseData($baseResults, $queryColumns, $baseReport);

                if ($startCompareDate) {
                    // Compare data for column
                    $compareQuery = $this->leadsReportRepository->getLeadReportQuery($queryType, $startCompareDate, $endCompareDate, $group, $queryColumns, $filters, $limitedLocations, $companyId, $campaigns);
                    // Run Query
                    $compareResults = $compareQuery->get()->toArray();
                    // Add compare data to compare report
                    $this->addCompareData($group, $compareResults, $queryColumns, $baseReport, $compareReport);
                }
            }
        }

        return [
            self::BASE_REPORT       => $baseReport,
            self::COMPARE_REPORT    => $compareReport,
            self::RETURNED_COLUMNS  => $returnedColumns,
        ];
    }

    /**
     * Include column dependencies for calculated columns in search
     * @param array $columns
     * @return array
     */
    protected function getDependentColumns(array $columns): array
    {
        $returnedColumns = $columns;

        foreach ($columns as $column) {
            array_push($returnedColumns, ...LeadsReportColumnEnum::getDependencies($column));
            $returnedColumns = array_values(array_unique(array_map(function ($col) {return $col->value;}, $returnedColumns)));
            $returnedColumns = array_map(function ($col) {return LeadsReportColumnEnum::from($col);}, $returnedColumns);
        }
        return $returnedColumns;
    }

    /**
     * @param LeadsReportQueryEnum $type
     * @param array $columns
     * @param LeadsReportGroupEnum $group
     * @return array
     */
    protected function getColumnsOfType(LeadsReportQueryEnum $type, array $columns, LeadsReportGroupEnum $group): array
    {
        if ($type === LeadsReportQueryEnum::CAMPAIGN && in_array($group, LeadsReportColumnEnum::NOT_APPLICABLE_FOR_CAMPAIGNS))
            return [];
        if ($type === LeadsReportQueryEnum::COST && in_array($group, LeadsReportColumnEnum::NOT_APPLICABLE_FOR_AD_COST))
            return [];
        return array_filter($columns, function($column) use ($type, $group) {
            return in_array($type, LeadsReportColumnEnum::getQueryType($column));
        });
    }

    /**
     * @param array $results
     * @param array $columns
     * @param array $baseReport
     * @return void
     */
    protected function addBaseData(array $results, array $columns, array &$baseReport): void
    {
        $calculatedColumns = LeadsReportColumnEnum::getAllCalculatedColumns();

        foreach ($results as $result) {
            if (!array_key_exists($result->{LeadsReportColumnEnum::GROUP_KEY}, $baseReport))
                $baseReport[$result->{LeadsReportColumnEnum::GROUP_KEY}] = [];
            foreach ($columns as $column) {
                if (!in_array($column, $calculatedColumns))
                    $baseReport[$result->{LeadsReportColumnEnum::GROUP_KEY}][$column->value] = $result->{LeadsReportColumnEnum::getKey($column)};
            }
        }
    }

    /**
     * @param LeadsReportGroupEnum $group
     * @param array $results
     * @param array $columns
     * @param array $baseReport
     * @param array $compareReport
     * @return void
     */
    protected function addCompareData(LeadsReportGroupEnum $group, array $results, array $columns, array &$baseReport, array &$compareReport): void
    {
        $calculatedColumns = LeadsReportColumnEnum::getAllCalculatedColumns();
        $baseKeys = array_keys($baseReport);
        $baseKeyIndex = 0;
        foreach ($results as $result) {
            // Locations and industries are mapped to the same keys for compare
            if (in_array($group, LeadsReportGroupEnum::getMatchGroups())) {
                if (!array_key_exists($result->{LeadsReportColumnEnum::GROUP_KEY}, $baseReport))
                    $baseReport[$result->{LeadsReportColumnEnum::GROUP_KEY}] = [];
                if (!array_key_exists($result->{LeadsReportColumnEnum::GROUP_KEY}, $compareReport))
                    $compareReport[$result->{LeadsReportColumnEnum::GROUP_KEY}] = [];
                if (array_key_exists(LeadsReportColumnEnum::COMPARE_GROUP, $baseReport[$result->{LeadsReportColumnEnum::GROUP_KEY}])) {
                    $baseReport[$result->{LeadsReportColumnEnum::GROUP_KEY}][LeadsReportColumnEnum::COMPARE_COST] = $result->{LeadsReportColumnEnum::GROUP_KEY};
                } else {
                    $baseReport[$result->{LeadsReportColumnEnum::GROUP_KEY}][LeadsReportColumnEnum::COMPARE_GROUP] = $result->{LeadsReportColumnEnum::GROUP_KEY};
                }
                foreach ($columns as $column) {
                    if (!in_array($column, $calculatedColumns))
                        $compareReport[$result->{LeadsReportColumnEnum::GROUP_KEY}][$column->value] = $result->{LeadsReportColumnEnum::getKey($column)};
                }
            } else {
                // Dates are mapped to different keys in compare
                if (array_key_exists($baseKeyIndex, $baseKeys)) {
                    if (!array_key_exists($baseKeys[$baseKeyIndex], $compareReport))
                        $compareReport[$baseKeys[$baseKeyIndex]] = [];
                    if (array_key_exists(LeadsReportColumnEnum::COMPARE_GROUP, $baseReport[$baseKeys[$baseKeyIndex]])) {
                        $baseReport[$baseKeys[$baseKeyIndex]][LeadsReportColumnEnum::COMPARE_COST] = $result->{LeadsReportColumnEnum::GROUP_KEY};
                    } else {
                        $baseReport[$baseKeys[$baseKeyIndex]][LeadsReportColumnEnum::COMPARE_GROUP] = $result->{LeadsReportColumnEnum::GROUP_KEY};
                    }
                    foreach ($columns as $column) {
                        if (!in_array($column, $calculatedColumns))
                            $compareReport[$baseKeys[$baseKeyIndex]][$column->value] = $result->{LeadsReportColumnEnum::getKey($column)};
                    }
                }
                $baseKeyIndex++;
            }
        }
    }
}
