<?php

namespace App\Services\Delivery;

use Illuminate\Mail\Mailable;
use Illuminate\Mail\SentMessage;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class Email
{
    /**
     * Handles sending an email.
     *
     * @param string|array $to
     * @param Mailable $email
     * @param bool $forceTest
     * @return SentMessage|null
     */
    static function send(string|array $to, Mailable $email, bool $forceTest = false): ?SentMessage
    {
        if (config('app.outgoing_communication.test_mode') === true || $forceTest === true)
            $to = config('app.outgoing_communication.test_email');

        return Mail::to($to)
                   ->send($email);
    }
}
