<?php

namespace App\Services;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class IndustryServicesService
{
    public function all(?int $industryId = null): Collection
    {
        $query = IndustryService::query()->with(IndustryService::RELATION_INDUSTRY);

        if ($industryId) $query->where(IndustryService::FIELD_INDUSTRY_ID, $industryId);

        return $query->get()->keyBy(IndustryService::FIELD_ID);
    }

    /**
     * @param string $groupByProperty
     * @return Collection
     */
    public function allServicesByIndustry(string $groupByProperty = Industry::FIELD_ID): Collection
    {
        return IndustryService::query()
            ->with(IndustryService::RELATION_INDUSTRY)
            ->get()
            ->groupBy(IndustryService::RELATION_INDUSTRY . '.' . $groupByProperty)
            ->map(fn($industryGroup) => $industryGroup->map(fn(IndustryService $service) => [
                'name'  => $service->{IndustryService::FIELD_NAME},
                'slug'  => $service->{IndustryService::FIELD_SLUG},
                'id'    => $service->{IndustryService::FIELD_ID},
                'industry_id'   => $service->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_ID},
                'industry_name' => $service->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
            ]),
        );
    }

    /**
     * @param string $groupByProperty
     * @param ?array $filterByIds
     * @return Collection
     */
    public function allServicesByIndustryWithProducts(string $groupByProperty = Industry::FIELD_ID, ?array $filterByIds = null): Collection
    {
        return IndustryService::query()
            ->with([IndustryService::RELATION_INDUSTRY, IndustryService::RELATION_PRODUCTS])
            ->when($filterByIds !== null, fn(Builder $query) =>
                $query->whereIn(IndustryService::FIELD_INDUSTRY_ID, $filterByIds)
            )->get()
            ->groupBy(IndustryService::RELATION_INDUSTRY . '.' . $groupByProperty)
            ->map(fn($industryGroup) => $industryGroup->map(fn(IndustryService $service) => [
                'name'  => $service->{IndustryService::FIELD_NAME},
                'slug'  => $service->{IndustryService::FIELD_SLUG},
                'id'    => $service->{IndustryService::FIELD_ID},
                'industry_id'   => $service->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_ID},
                'industry_name' => $service->{IndustryService::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
                'products'      => $service->products->reduce(function(Collection $output, Product $product) {
                    if (!$output->some(fn($v) => $v[Product::FIELD_ID] === $product->id)) {
                        $output->push([
                            Product::FIELD_ID   => $product->id,
                            Product::FIELD_NAME => $product->name,
                        ]);
                    }
                    return $output;
                }, collect()),
            ]));
    }
}
