<?php

namespace App\Services;

use App\Enums\RoleType;
use App\Mail\LeadRefunds\LeadRefundCommentAdded;
use App\Mail\LeadRefunds\LeadRefundRequested;
use App\Mail\LeadRefunds\LeadRefundReviewed;
use App\Models\LeadRefund;
use App\Models\User;
use App\Repositories\UserRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;
use JetBrains\PhpStorm\NoReturn;

class LeadRefundNotificationService
{
    public function __construct(
        protected UserRepository $userRepository,
    )
    {

    }

    /**
     * @return Collection
     */
    public function getReviewers(): Collection
    {
        return $this->userRepository->getUsersByRole(RoleType::LEAD_REFUNDS_REVIEWER);
    }

    /**
     * @param LeadRefund $refund
     * @return void
     */
    public function notifyReviewersRequestOpened(LeadRefund $refund): void
    {
        $reviewers = $this->getReviewers();

        foreach ($reviewers as $reviewer) {
            Mail::to($reviewer->email)->send(new LeadRefundRequested(
                recipientName: $reviewer->name,
                company      : $refund->company->name,
                total        : $refund->total,
                requesterName: $refund->requestedBy->name,
                requestDate  : $refund->created_at,
            ));
        }
    }

    /**
     * @param LeadRefund $refund
     * @param array $comments
     * @return void
     */
    public function notifyRequesterLeadRefundHasBeenReviewed(
        LeadRefund $refund,
        array $comments = []
    ): void
    {
        Mail::to($refund->requestedBy->email)->send(new LeadRefundReviewed(
            recipientName : $refund->requestedBy->name,
            leadRequestId : $refund->id,
            company       : $refund->company->name,
            total         : $refund->total,
            requestDate   : $refund->created_at,
            approvalStatus: $refund->status->getName(),
            comments      : $comments
        ));
    }

    /**
     * @param LeadRefund $refund
     * @param array $comments
     * @param int $userId
     * @return void
     */
    public function notifyRelevantUsersToRefundOfCommentsAdded(
        LeadRefund $refund,
        array $comments,
        int $userId,
    ): void
    {
        $reviewers = $this->getReviewers();
        $requester = $refund->requestedBy;

        $users = collect([
            ...$reviewers,
            $requester
        ])->unique()->filter(fn(User $user) => $user->{User::FIELD_ID} !== $userId);

        if ($users->isNotEmpty()) {
            foreach ($reviewers as $reviewer) {
                Mail::to($reviewer->email)->send(new LeadRefundCommentAdded(
                    company      : $refund->company->name,
                    total        : $refund->total,
                    recipientName: $reviewer->name,
                    leadRefundId : $refund->id,
                    requesterName: $refund->requestedBy->name,
                    requestDate  : $refund->created_at,
                    comments     : $comments,
                ));
            }
        }

    }
}

