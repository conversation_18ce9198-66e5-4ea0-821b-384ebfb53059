<?php

namespace App\Services\EmailAddress;

use App\Contracts\Services\EmailAddressValidationServiceContract;
use App\DTO\EmailAddress\EmailValidationResponseDTO;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;

class IPQualityScoreEmailAddressValidationService implements EmailAddressValidationServiceContract
{
    private Client $client;
    private string $apiKey;

    public function __construct()
    {
        $this->client = new Client(['base_uri' => config('services.ip_quality_score.base_url')]);
        $this->apiKey = config('services.ip_quality_score.api_key');
    }

    /**
     * @inheritDoc
     * @throws GuzzleException
     */
    public function validate(string $email): EmailValidationResponseDTO
    {
        $response = $this->client->request('GET', "email/$this->apiKey/$email");

        $response = json_decode($response->getBody()->getContents(), true);

        return EmailValidationResponseDTO::fromArray($response);
    }
}
