<?php

namespace App\Services\EmailAddress;

use App\Contracts\Services\EmailAddressValidationServiceContract;
use App\Enums\EmailAddress\EmailAddressValidationSource;

class EmailAddressValidationServiceFactory
{
    /**
     * @param EmailAddressValidationSource $source
     * @return EmailAddressValidationServiceContract
     */
    public static function make(
        EmailAddressValidationSource $source,
    ): EmailAddressValidationServiceContract
    {
        return match ($source) {
            EmailAddressValidationSource::IP_QUALITY_SCORE => new IPQualityScoreEmailAddressValidationService(),
        };
    }

}
