<?php

namespace App\Services\EmailAddress;

use App\DTO\EmailAddress\EmailValidationResponseDTO;
use App\Enums\EmailAddress\EmailAddressValidationSource;
use App\Models\EmailAddress;
use App\Repositories\EmailAddress\EmailAddressRepository;
use App\Services\ContactCacheService;

class EmailAddressService
{
    public function __construct(
        protected EmailAddressRepository $repository,
        protected ContactCacheService $contactCacheService,
    )
    {
    }

    /**
     * @param string $email
     * @return array
     */
    public function authenticateEmail(
        string $email,
    ): array
    {
        $email = $this->findOrCreateByEmail(email: $email);

        $errors = [];

        $errors[] = !$email->valid ? 'Email is invalid' : null;

        return array_filter($errors);
    }

    /**
     * @param string $email
     * @return bool
     */
    public function recentlyContacted(
        string $email
    ): bool
    {
        $email = $this->formatEmailAddress(email: $email);

        return $this->contactCacheService->recentlyContacted(
            contactVector: $email
        );
    }

    /**
     * @param string $email
     * @return void
     */
    public function markEmailContacted(
        string $email
    ): void
    {
        $email = $this->formatEmailAddress(email: $email);

        $this->contactCacheService->markRecentlyContacted(
            contactVector: $email
        );
    }

    /**
     * @param string $email
     * @return string
     */
    public function formatEmailAddress(string $email): string
    {
        return strtolower(filter_var($email, FILTER_SANITIZE_EMAIL));
    }

    /**
     * @param string $email
     * @return EmailAddress|null
     */
    public function findByEmail(
        string $email,
    ): ?EmailAddress
    {
        return $this->repository->findByEmail(
            email: $email,
        );
    }

    /**
     * @param string $email
     * @return EmailAddress
     */
    public function findOrCreateByEmail(
        string $email,
    ): EmailAddress
    {
        $formattedEmail = $this->formatEmailAddress(email: $email);

        $emailAddress = $this->findByEmail(email: $formattedEmail);

        return $emailAddress ?? $this->validateAndCreate(email: $formattedEmail);
    }

    /**
     * @param string $email
     * @return EmailAddress
     */
    public function validateAndCreate(string $email): EmailAddress
    {
        $response = $this->validate(
            email: $email,
            source: EmailAddressValidationSource::IP_QUALITY_SCORE
        );

        return $this->createFromDTO(
            email: $email,
            responseDTO: $response
        );
    }

    /**
     * @param string $email
     * @param EmailValidationResponseDTO $responseDTO
     * @return EmailAddress
     */
    public function createFromDTO(
        string $email,
        EmailValidationResponseDTO $responseDTO
    ): EmailAddress
    {
        return $this->repository->create(
            email: $email,
            sanitizedEmail: $responseDTO->getSanitizedEmail(),
            valid: $responseDTO->getValid(),
            disposable: $responseDTO->getDisposable(),
            frequentComplainer: $responseDTO->getFrequentComplainer(),
            spamTrapScore: $responseDTO->getSpamTrapScore(),
            fraudScore: $responseDTO->getFraudScore(),
            payload: $responseDTO->toArray(),
        );
    }

    /**
     * @param string $email
     * @param EmailAddressValidationSource $source
     * @return EmailValidationResponseDTO
     */
    public function validate(
        string                       $email,
        EmailAddressValidationSource $source
    ): EmailValidationResponseDTO
    {
        $validationService = EmailAddressValidationServiceFactory::make($source);

        return $validationService->validate($email);
    }

}
