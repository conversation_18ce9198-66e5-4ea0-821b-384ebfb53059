<?php

namespace App\Services\PaymentGateway;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\DTO\Billing\AddressData;
use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Odin\Company;
use App\Services\Billing\BillingLogService;
use App\Services\Legacy\Exceptions\PaymentException;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Stripe\Exception\ApiErrorException;
use Stripe\Exception\AuthenticationException;
use Stripe\Exception\CardException;
use Stripe\Exception\InvalidRequestException;
use Stripe\PaymentMethod;
use Stripe\StripeClient;
use Stripe\Customer;

class StripePaymentGatewayService implements PaymentGatewayServiceContract
{
    protected StripeClient $client;

    public function __construct()
    {
        $this->client = new StripeClient(config('services.payment_gateways.stripe.gateway_mode')
            ? config('services.payment_gateways.stripe.api_key_secret_live')
            : config('services.payment_gateways.stripe.api_key_secret_test'));
    }

    /**
     * @return PaymentMethodServices
     */
    public function getPaymentServiceProviderCode(): PaymentMethodServices
    {
        return PaymentMethodServices::STRIPE;
    }

    /**
     * @param string $invoicePaymentChargeUuid
     * @param string $invoiceUuid
     * @param int $total
     * @param array $metadata
     * @param string|null $description
     * @param string|null $customerCode
     * @param string|null $paymentMethod
     * @return bool
     * @throws ApiErrorException
     */
    public function makeChargeRequest(
        string $invoicePaymentChargeUuid,
        string $invoiceUuid,
        int $total,
        array $metadata,
        ?string $description,
        ?string $customerCode,
        ?string $paymentMethod
    ): bool
    {
        $paymentIntent = $this->client->paymentIntents->create(
            [
                'amount'         => $total,
                'currency'       => 'usd',
                'confirm'        => true,
                'off_session'    => true,
                'customer'       => $customerCode,
                'payment_method' => $paymentMethod,
                'description'    => $description,
                'metadata'       => [
                    'companyId'                => Arr::get($metadata, 'companyId'),
                    'invoiceUuid'              => $invoiceUuid,
                    'invoicePaymentChargeUuid' => $invoicePaymentChargeUuid,
                ],
            ],
            [
                'idempotency_key' => $invoicePaymentChargeUuid
            ]
        );

        return $paymentIntent->status === 'succeeded';
    }

    /**
     * @param string $invoiceRefundChargeUuid
     * @param int $total
     * @param string $chargeId
     * @param string $invoiceUuid
     * @return bool
     * @throws ApiErrorException
     */
    public function makeRefundRequest(
        string $invoiceRefundChargeUuid,
        int $total,
        string $chargeId,
        string $invoiceUuid
    ): bool
    {
        $refund = $this->client->refunds->create(
            [
                'charge'   => $chargeId,
                'amount'   => $total,
                'metadata' => [
                    'invoiceRefundChargeUuid' => $invoiceRefundChargeUuid,
                    'invoiceUuid'             => $invoiceUuid
                ]
            ],
            [
                'idempotency_key' => $invoiceRefundChargeUuid
            ]
        );


        return $refund->status === 'succeeded';
    }

    /**
     * @param Company $company
     * @param string $email
     * @return string
     */
    public function createCustomer(Company $company, string $email): string
    {
        /** @var Customer $customer */
        $customer = $this->executeRequest(function () use ($email, $company) {
            return $this->client->customers->create([
                'email'            => $email,
                'metadata'         => [
                    'Solar Reviews Company Id' => $company->{Company::FIELD_ID}
                ],
                'description'      => $company->{Company::FIELD_NAME},
                'invoice_settings' => []
            ]);
        });

        if (!$customer) {
            throw new PaymentException('Failed to create customer account');
        }

        return $customer->id;
    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function addPaymentMethod(string $customerCode, string $paymentMethod): void
    {
        $this->executeRequest(function () use ($customerCode, $paymentMethod) {
            $this->client->paymentMethods->attach(
                $paymentMethod,
                ['customer' => $customerCode]
            );
        });
    }

    /**
     * @param callable $apiRequest
     * @return mixed
     */
    protected function executeRequest(callable $apiRequest): mixed
    {
        try {
            return $apiRequest();
        } catch (AuthenticationException $e) {
            // Supplied Credentials could not be authenticated.
            throw $this->makePaymentException($e, "Authentication Error");
        } catch (InvalidRequestException $e) {
            return null;
        } catch (CardException $e) {
            throw $this->makePaymentException($e, "Payment Method Error");
        } catch (ApiErrorException $e) {
            // Library could not connect to Stripe servers.
            throw $this->makePaymentException($e, "API Connection Error");
        }
    }

    /**
     * @param ApiErrorException $exception
     * @param $type
     * @return PaymentException
     */
    protected function makePaymentException(ApiErrorException $exception, $type = null): PaymentException
    {
        return new PaymentException(($type ?: "Error") . ": {$exception->getMessage()}", 0, null, [
            "Request ID" => $exception->getRequestId(),
        ]);
    }

    /**
     * @throws ApiErrorException
     */
    public function createNewPaymentMethod(string $token): string
    {
        return $this->client->paymentMethods->create(
            ['type' => 'card', 'card' => ['token' => $token]]
        )->id;
    }

    /**
     * @param string $customerCode
     * @return Collection<PaymentMethodDTO>
     * @throws ApiErrorException
     */
    public function getPaymentMethods(string $customerCode): Collection
    {
        $paymentMethods = collect();
        $customer = $this->client->customers->retrieve($customerCode, []);
        $paymentMethodsList = $this->client->customers->allPaymentMethods(
            $customerCode,
            ['type' => 'card']
        );

        $paymentMethodInstances = Arr::get($paymentMethodsList, 'data');

        if (!$paymentMethodInstances) {
            return $paymentMethods;
        }

        //populate credit card payment method instances
        /** @var PaymentMethod $stripePaymentMethodInstance */
        foreach ($paymentMethodInstances as $stripePaymentMethodInstance) {
            if (!($stripePaymentMethodInstance instanceof PaymentMethod)) {
                $stripePaymentMethodType = get_class($stripePaymentMethodInstance);
                throw new PaymentException("Unable to handle payment method of type '{$stripePaymentMethodType}'");
            }

            $card = $stripePaymentMethodInstance->card;
            $billingDetails = $stripePaymentMethodInstance->billing_details;
            $address = $billingDetails->offsetGet('address') ?? [];
            $id = $stripePaymentMethodInstance->offsetGet('id');

            $expiryMonth = str_pad($card->offsetGet('exp_month'), 2, '0', STR_PAD_LEFT);
            $expiryYear = substr($card->offsetGet('exp_year'), 2, 2);

            $type = ucwords($card->offsetGet('funding')) . " Card";
            $brand = $card->offsetGet('brand');
            $name = $billingDetails->offsetGet('name');

            $addressDTO = new AddressData(
                line_one : $address->offsetGet('line1'),
                city     : $address->offsetGet('city'),
                state    : $address->offsetGet('state'),
                post_code: $address->offsetGet('postal_code'),
                country  : $address->offsetGet('country'),
                line_two : $address->offsetGet('line2'),
            );

            $paymentMethodDTO = new PaymentMethodDTO(
                id          : $id,
                type        : $type,
                brand       : $brand,
                last4       : $card->offsetGet('last4'),
                expiry_month: $expiryMonth,
                expiry_year : $expiryYear,
                name        : $name,
                status      : $card->offsetGet('status'),
                is_default  : $customer->invoice_settings->default_payment_method === $stripePaymentMethodInstance->offsetGet('id'),
                address     : $addressDTO,
                full_year   : $card->offsetGet('exp_year')
            );

            $paymentMethods->push($paymentMethodDTO);
        }

        return $paymentMethods;

    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function setPaymentMethodPrimary(string $customerCode, string $paymentMethod): void
    {
        try {
            $this->client->customers->update(
                $customerCode,
                [
                    'invoice_settings' => [
                        'default_payment_method' => $paymentMethod
                    ]
                ]);
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception: $exception,
            );
            throw new PaymentException('Failed to save default payment method');
        }
    }

    /**
     * @param string $paymentMethod
     * @return void
     * @throws ApiErrorException
     */
    public function deletePaymentMethod(string $paymentMethod): void
    {
        $this->client->paymentMethods->detach($paymentMethod, []);
    }
}
