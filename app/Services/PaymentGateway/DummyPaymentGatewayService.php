<?php

namespace App\Services\PaymentGateway;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Odin\Company;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class DummyPaymentGatewayService implements PaymentGatewayServiceContract
{
    /**
     * @return PaymentMethodServices
     */
    public function getPaymentServiceProviderCode(): PaymentMethodServices
    {
        return PaymentMethodServices::DUMMY;
    }

    /**
     * @throws Exception
     */
    public function makeChargeRequest(
        string $invoicePaymentChargeUuid,
        string $invoiceUuid,
        int $total,
        array $metadata,
        ?string $description,
        ?string $customerCode,
        ?string $paymentMethod
    ): bool
    {
        $rand = mt_rand(1, 10);

        if ($rand >= 5) throw new Exception('Charge request failed');

        return true;
    }

    /**
     * @param string $invoiceRefundChargeUuid
     * @param int $total
     * @param string $chargeId
     * @param string $invoiceUuid
     * @return bool
     */
    public function makeRefundRequest(
        string $invoiceRefundChargeUuid,
        int $total,
        string $chargeId,
        string $invoiceUuid
    ): bool
    {
        return true;
    }

    /**
     * @param Company $company
     * @param string $email
     * @return string
     */
    public function createCustomer(Company $company, string $email): string
    {
        return Str::random();
    }

    /**
     * @param string $token
     * @return string
     */
    public function createNewPaymentMethod(string $token): string
    {
        return Str::random();
    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function addPaymentMethod(string $customerCode, string $paymentMethod): void
    {
    }

    /**
     * @param string $customerCode
     * @return Collection
     */
    public function getPaymentMethods(string $customerCode): Collection
    {
        return collect();
    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return void
     */
    public function setPaymentMethodPrimary(string $customerCode, string $paymentMethod): void
    {
        // TODO: Implement setPaymentMethodPrimary() method.
    }

    /**
     * @param string $paymentMethod
     * @return void
     */
    public function deletePaymentMethod(string $paymentMethod): void
    {
        // TODO: Implement deletePaymentMethod() method.
    }
}
