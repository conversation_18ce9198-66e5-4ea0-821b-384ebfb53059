<?php

namespace App\Services\PaymentGateway;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\Enums\Billing\PaymentMethodServices;
use Stripe\StripeClient;

class PaymentGatewayServiceFactory
{
    static function make(PaymentMethodServices $type): PaymentGatewayServiceContract
    {
        return match ($type) {
            PaymentMethodServices::STRIPE => new StripePaymentGatewayService(),
            PaymentMethodServices::MANUAL => new ManualPaymentGatewayService(),
            default                       => new DummyPaymentGatewayService()
        };
    }
}
