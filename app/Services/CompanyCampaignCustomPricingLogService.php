<?php

namespace App\Services;

use App\DTO\FloorPricing\CustomFloorPricingHistoryLog;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\FloorPriceActivityLog;
use App\Models\GlobalConfiguration;
use App\Models\Legacy\Location;
use App\Models\Odin\QualityTier;
use App\Models\SaleType;
use App\Models\User;
use App\Repositories\CompanyCampaignCustomPricingLogRepository;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CompanyCampaignCustomPricingLogService
{
    public function __construct(
        protected CompanyCampaignCustomPricingLogRepository $companyCampaignCustomPricingLogRepository
    )
    {}

    /**
     * @param FloorPriceActivityLog $activityLog
     * @return CustomFloorPricingHistoryLog
     */
    protected function transformLog(FloorPriceActivityLog $activityLog): CustomFloorPricingHistoryLog
    {
        $relatedSubject = $activityLog->{FloorPriceActivityLog::RELATION_SUBJECT};

        $typeName = $relatedSubject instanceof CustomCampaignStateFloorPrice
            ? $relatedSubject->{CustomCampaignStateFloorPrice::RELATION_SALE_TYPE}->{SaleType::FIELD_NAME}
            : $relatedSubject->{CustomCampaignCountyFloorPrice::RELATION_SALE_TYPE}->{SaleType::FIELD_NAME};

        $referenceLocation = $relatedSubject instanceof CustomCampaignStateFloorPrice
            ? $relatedSubject->{CustomCampaignStateFloorPrice::RELATION_STATE_LOCATION}
            : $relatedSubject->{CustomCampaignCountyFloorPrice::RELATION_COUNTY_LOCATION};

        $company = $relatedSubject->companyCampaign->company;

        $hasSolarIndustry = $company->industries()->where('name', 'Solar')->exists();

        if ($hasSolarIndustry) {
            $qualityTierName = $relatedSubject instanceof CustomCampaignStateFloorPrice
                ? $relatedSubject->{CustomCampaignStateFloorPrice::RELATION_QUALITY_TIER}->{QualityTier::FIELD_NAME}
                : $relatedSubject->{CustomCampaignCountyFloorPrice::RELATION_QUALITY_TIER}->{QualityTier::FIELD_NAME};
        } else {
            $qualityTierName = null;
        }

        $campaign = $relatedSubject->companyCampaign;


        $globalConfig = GlobalConfiguration::where(GlobalConfiguration::FIELD_CONFIGURATION_KEY, 'default-floor-pricing')->first();

        $pricingData = $globalConfig ? $globalConfig->{GlobalConfiguration::FIELD_CONFIGURATION_PAYLOAD} : null;

        $pricingArray = $pricingData ? $pricingData->toArray() : [];

        $priceFrom = Arr::get($activityLog->properties->get('old'), 'price');
        if (!$priceFrom && !empty($pricingArray)) {
            $qualityTier = $qualityTierName ?? 'Standard';
            $priceFrom = $pricingArray['data']['Lead'][$qualityTier][$typeName]['price'] ?? null;
        }

        $priceTo = Arr::get($activityLog->properties->get('attributes'), 'price');

        return new CustomFloorPricingHistoryLog(
            causerId    : $activityLog->{FloorPriceActivityLog::FIELD_CAUSER_ID},
            causerName  : $activityLog->{FloorPriceActivityLog::RELATION_CAUSER}->{User::FIELD_NAME},
            county      : $referenceLocation?->{Location::COUNTY},
            countyKey   : $referenceLocation?->{Location::COUNTY_KEY},
            date        : $activityLog->{FloorPriceActivityLog::FIELD_CREATED_AT},
            priceFrom   : $priceFrom,
            priceTo     : $priceTo,
            saleType    : $typeName,
            state       : $referenceLocation?->{Location::STATE},
            stateAbbr   : $referenceLocation?->{Location::STATE_ABBREVIATION},
            stateKey    : $referenceLocation?->{Location::STATE_KEY},
            companyId   : $company->id,
            companyName : $company->name,
            campaignId  : $campaign->id,
            campaignName: $campaign->name,
            qualityTier : $qualityTierName,
        );
    }

    /**
     * @param string|null $campaign
     * @param int|null $companyId
     * @return Collection
     */
    public function listCustomStatePricingLogs(
        ?string $campaign = null,
        ?int $companyId = null,
    ): Collection
    {
        return $this->companyCampaignCustomPricingLogRepository->getCustomStatePricingLogs(
            campaign: $campaign,
            companyId: $companyId,
        )
            ->get()
            ->map(fn (FloorPriceActivityLog $activityLog) => $this->transformLog($activityLog))
            ->sortByDesc(fn(CustomFloorPricingHistoryLog $item) => Carbon::parse($item->getDate()))
            ->values();
    }

    public function listCustomCountyPricingLogs(
        ?string $campaign = null,
        ?int $companyId = null,
    ): Collection
    {
        return $this->companyCampaignCustomPricingLogRepository->getCustomCountyPricingLogs(
            campaign: $campaign,
            companyId: $companyId,
        )
            ->get()
            ->map(fn (FloorPriceActivityLog $activityLog) => $this->transformLog($activityLog))
            ->sortByDesc(fn(CustomFloorPricingHistoryLog $item) => Carbon::parse($item->getDate()))
            ->values();
    }
}
