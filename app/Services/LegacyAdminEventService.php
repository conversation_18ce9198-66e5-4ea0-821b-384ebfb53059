<?php

namespace App\Services;

//todo: This service is added to handle any legacy Admin Pub/Sub events. Delete once legacy admin is deprecated

use App\Jobs\RecordMonitoringLog;
use App\Models\AccountManager;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\CompanyAccountManagerRepository;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Throwable;

class LegacyAdminEventService
{
    const LAST_LEAD_CREATION_TIMESTAMP = 'last_lead_creation_timestamp';

    public function __construct(protected CompanyAccountManagerRepository $accountManagerRepository) {}

    /**
     * @param string $event
     * @param array $data
     *
     * @return void
     */
    public function handle(string $event, array $data): void
    {
        switch ($event) {
            case 'account-manager-assigned':
                $this->updateAccountManager($data);
                break;
            case 'lead-created':
                $this->updateLeadCreatedTimestamp($data);
                break;
            case 'write-log':
                $this->writeCloudLog($data);
                break;
            default:
                break;
        }
    }

    /**
     * @param array $data
     *
     * @return void
     */
    protected function updateAccountManager(array $data): void
    {
        $companyReference     = Arr::get($data, 'company_reference');
        $accountManagerUserId = Arr::get($data, 'account_manager_user_id');

        if (!$companyReference || !$accountManagerUserId) return;

        try {
            /** @var Company|null $company */
            $company = Company::query()->where(Company::FIELD_REFERENCE, $companyReference)->first();

            if (!$company) return;

            /** @var User|null $user */
            $user = User::query()->where(User::FIELD_LEGACY_USER_ID, $accountManagerUserId)->first();

            if (!$user) return;

            /** @var AccountManager $accountManager */
            $accountManager = AccountManager::query()->firstOrCreate(
                [AccountManager::FIELD_USER_ID => $user->id],
                [AccountManager::FIELD_USER_ID => $user->id, AccountManager::FIELD_TYPE => AccountManager::TYPE_JUNIOR]
            );

            $this->accountManagerRepository->assignAccountManager($company->{Company::FIELD_ID}, $accountManager->{AccountManager::FIELD_ID});
        } catch (Exception $e) {
            logger()->error("Failed to update account manager (legacy admin event) due to: {$e->getMessage()}");
        }
    }

    /**
     * @param array $data
     */
    protected function updateLeadCreatedTimestamp(array $data): void
    {
        try {
            $createdTimestamp = (int) Arr::get($data, self::LAST_LEAD_CREATION_TIMESTAMP);

            Cache::put(self::LAST_LEAD_CREATION_TIMESTAMP, $createdTimestamp);
        }
        catch(Exception $e) {
            logger()->error(__METHOD__.": {$e->getMessage()}");
        }
    }

    /**
     * @param array $data
     * @return void
     */
    protected function writeCloudLog(array $data): void
    {
        try {
            $data['env'] = App::environment();

            RecordMonitoringLog::dispatchSync(
                $data['message_text'] ?? 'Legacy SR log',
                $data
            );
        }
        catch(Throwable $e) {
            logger()->error($e);
        }
    }
}
