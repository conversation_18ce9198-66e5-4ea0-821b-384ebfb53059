<?php

namespace App\Services\EmailTemplates;

use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Google\Cloud\Storage\StorageObject;
use GuzzleHttp\Promise\PromiseInterface;

class EmailTemplateImageService
{
    const IMAGE_DATA_URL_REGEX = "/data:\w+\/\w+;base64,[^()]+/";
    const IMAGE_NAME_REGEX = "[a-zA-Z0-9_.\/]+";
    const SHORTCODES_REGEX = "/{image:(?<filename>".self::IMAGE_NAME_REGEX.")}/";

    const UPLOAD_BASE_PATH = '/email_template_images';
    const BUCKET_DOMAIN    = 'https://storage.googleapis.com/';

    /**
     * @param GoogleCloudStorageService $googleCloudStorageService
     */
    public function __construct(private GoogleCloudStorageService $googleCloudStorageService)
    {

    }

    /**
     * @param string $markdown
     * @return array
     */
    public function extractShortcodes(string $markdown): array
    {
        preg_match_all(self::SHORTCODES_REGEX, $markdown, $matches);

        return $matches[1];
    }

    /**
     * @param string $markdown
     * @param int $templateId
     * @return array|string|null
     */
    public function shortcodesToUrlImageTags(string $markdown, int $templateId): array|string|null
    {
        $templateImagesBasePath = self::BUCKET_DOMAIN . config('services.google.storage.buckets.email_template_images') .'/' . self::UPLOAD_BASE_PATH."/$templateId";

        return preg_replace_callback(
            self::SHORTCODES_REGEX,
            function(array $matches) use ($templateImagesBasePath) {

                $dataUrl = "$templateImagesBasePath/{$matches['filename']}";
                return "<img src=\"{$dataUrl}\" alt=\"{$matches["filename"]}\"\">";
            },
            $markdown
        );
    }

    /**
     * @param string $markdown
     * @param int $templateId
     * @return string
     * @throws Exception
     */
    public function shortcodesToMarkdownImageTags(string $markdown, int $templateId): string
    {
        $shortcodes = $this->extractShortcodes($markdown);

        $templateImagesBasePath = self::UPLOAD_BASE_PATH."/$templateId";

        $objects = [];
        foreach($shortcodes as $shortcode) {
            $objects[] = "$templateImagesBasePath/$shortcode";
        }

        $fileContents = $this->downloadImageDataUrlsFromCloudStorage($templateId, $objects);

        return preg_replace_callback(
            self::SHORTCODES_REGEX,
            function(array $matches) use ($fileContents, $templateImagesBasePath) {
                $dataUrl = $fileContents["$templateImagesBasePath/{$matches['filename']}"];

                return "![{$matches['filename']}]($dataUrl)";
            },
            $markdown
        );
    }

    /**
     * @param int $templateId
     * @param string $imageName
     * @param string $imageDataUrl
     * @param string $uploadBasePath
     * @return StorageObject
     * @throws Exception
     */
    public function uploadImageDataUrlToCloudStorage(
        int $templateId,
        string $imageName,
        string $imageDataUrl,
        string $uploadBasePath = self::UPLOAD_BASE_PATH
    ): StorageObject
    {
        $this->googleCloudStorageService->setCurrentBucket(config('services.google.storage.buckets.email_template_images'));

        return $this->googleCloudStorageService->upload(
            $uploadBasePath . "/$templateId/$imageName",
            $this->base64ToBinary($imageDataUrl)
        );
    }

    /**
     * @param int $templateId
     * @param array $filenames
     * @return array
     * @throws Exception
     */
    public function downloadImageDataUrlsFromCloudStorage(int $templateId, array $filenames): array
    {
        $this->googleCloudStorageService->setCurrentBucket(config('services.google.storage.buckets.email_template_images'));

        $responses = collect($this->googleCloudStorageService->downloadMultipleObjects($filenames));

        return $responses->mapWithKeys(function(array $response, string $filename) use ($templateId) {
            if($response['state'] !== PromiseInterface::FULFILLED) {
                throw new Exception(__METHOD__.": Error retrieving objects. Template ID: $templateId, File: $filename");
            }
            return [$filename => $this->binaryToBase64($response['value']->getContents())];
        })->toArray();
    }


    /**
     * @param string $markdown
     * @param int $templateId
     * @return string
     * @throws Exception
     */
    public function shortcodesToHTMLImageTags(string $markdown): string
    {
        return preg_replace_callback('/!\[(.*?)\]\((.*?)\)/', function($match) {
            $alt_text = $match[1];
            $image_url = $match[2];
            return "<img src=\"$image_url\" alt=\"$alt_text\">";
        }, $markdown);
    }


    /**
     * @param string $b64
     * @return string
     */
    public function base64ToBinary(string $b64): string
    {
        list(, $data) = explode(';', $b64);
        list(, $data)      = explode(',', $data);
        return base64_decode($data);
    }

    /**
     * @param string $bin
     * @return string
     * @throws Exception
     */
    public function binaryToBase64(string $bin): string
    {
        $type = $this->getImgType($bin);
        return "data:image/{$type};base64,".base64_encode($bin);
    }


    /**
     * @param $bin
     * @return string
     * @throws Exception
     */
    protected function getImgType($bin): string
    {
        $types = array('jpeg' => "\xFF\xD8\xFF", 'gif' => 'GIF', 'png' => "\x89\x50\x4e\x47\x0d\x0a", 'bmp' => 'BM', 'psd' => '8BPS', 'swf' => 'FWS');
        $found = 'other';
        $bytes = substr($bin, 0, 8);
        foreach ($types as $type => $header) {
            if (str_starts_with($bytes, $header)) {
                $found = $type;
                break;
            }
        }
        return $found;
    }

    /**
     * @param string $pathTo
     * @param $imageName
     * @return string
     */
    public function getImageUrl(string $pathTo, $imageName): string
    {
        return self::BUCKET_DOMAIN . config('services.google.storage.buckets.email_template_images') . "/{$pathTo}/{$imageName}";
    }

    /**
     * @param int    $templateFrom
     * @param int    $templateTo
     * @param string $markdown
     * @return void
     * @throws Exception
     */
    public function uploadImagesOnCloudFromOneTemplateToAnother(int $templateFrom, int $templateTo, string $markdown): void
    {
        $shortcodes = $this->extractShortcodes($markdown);

        $imageNames = [];
        foreach($shortcodes as $shortcode) {
            $imageNames[self::UPLOAD_BASE_PATH."/$templateFrom/$shortcode"] = $shortcode;
        }

        $cloudObjects = $this->downloadImageDataUrlsFromCloudStorage($templateFrom, array_keys($imageNames));

        foreach($cloudObjects as $path => $object) {
            $this->uploadImageDataUrlToCloudStorage($templateTo, $imageNames[$path], $object);
        }
    }
}
