<?php

namespace App\Services\EmailTemplates\TemplateAttachment;

use App\DTO\EmailService\EmailAttachmentDTO;
use App\Enums\EmailTemplateAttachmentType;
use Illuminate\Support\Collection;

abstract class BaseEmailAttachment
{
    public function __construct(protected Collection $args)
    {

    }

    /**
     * @return EmailTemplateAttachmentType
     */
    abstract static function getType(): EmailTemplateAttachmentType;

    /**
     * @return string
     */
    abstract public function getName(): string;

    /**
     * @return mixed
     */
    abstract public function getData(): mixed;

    public function getEmailAttachment(): EmailAttachmentDTO
    {
        return new EmailAttachmentDTO(
            data: $this->getData(),
            name: $this->getName(),
        );
    }
}
