<?php

namespace App\Services\EmailTemplates\TemplateAttachment;

use App\Enums\EmailTemplateAttachmentType;
use App\Models\Billing\Invoice;
use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;

class InvoicePdfAttachment extends BaseEmailAttachment
{
    /**
     * @return EmailTemplateAttachmentType
     */
    static function getType(): EmailTemplateAttachmentType
    {
        return EmailTemplateAttachmentType::INVOICE_PDF;
    }

    /**
     * @return string
     * @throws Exception
     */
    public function getName(): string
    {
        $invoiceId = $this->getInvoice()->id;

        return "invoice_$invoiceId.pdf";
    }

    /**
     * @return Invoice
     * @throws Exception
     */
    protected function getInvoice(): Invoice
    {
        $invoice = $this->args->get('invoice');

        if (empty($invoice)) {
            throw new Exception('Invoice required');
        }

        return $invoice;
    }

    /**
     * @return mixed
     * @throws BindingResolutionException
     */
    public function getData(): mixed
    {
        $googleCloudStorageService = app()->make(GoogleCloudStorageService::class);

        $googleCloudStorageService->setCurrentBucket(config('services.google.storage.buckets.invoices'));

        $stream = $googleCloudStorageService->downloadObject($this->getInvoice()->{Invoice::FIELD_INVOICE_URL});

        return $stream->getContents();
    }
}
