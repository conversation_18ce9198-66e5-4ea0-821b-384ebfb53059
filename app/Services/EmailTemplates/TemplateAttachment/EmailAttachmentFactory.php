<?php

namespace App\Services\EmailTemplates\TemplateAttachment;

use App\Enums\EmailTemplateAttachmentType;
use Illuminate\Support\Collection;

class EmailAttachmentFactory
{
    /**
     * @param EmailTemplateAttachmentType $type
     * @param Collection $args
     * @return BaseEmailAttachment|null
     */
    static public function make(
        EmailTemplateAttachmentType $type,
        Collection $args = new Collection()
    ): ?BaseEmailAttachment
    {
        return match ($type) {
            EmailTemplateAttachmentType::INVOICE_PDF => new InvoicePdfAttachment($args),
            default                                  => null
        };
    }
}
