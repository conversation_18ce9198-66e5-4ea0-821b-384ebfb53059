<?php

namespace App\Services\EmailTemplates;

use App\Enums\EmailTemplateScope;
use App\Models\EmailTemplateBackground;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Factories\Workflows\WorkflowPayloadFactory;
use App\Mail\CustomUserMarkdown;
use App\Workflows\WorkflowEvent;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;

class EmailTemplateBackgroundService
{
    /**
     * @param EmailTemplateImageService $emailTemplateImageService
     */
    public function __construct(
        protected EmailTemplateImageService $emailTemplateImageService
    ) {}

    /**
     * @param EmailTemplateScope|null $scope
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedBackgroundTemplates(?EmailTemplateScope $scope = null, int $perPage = 10): LengthAwarePaginator
    {
        return EmailTemplateBackground::byScope($scope)->paginate($perPage);
    }
    /**
     * @param int|null $industryId
     * @return Collection
     */
    public function getUserBackgroundTemplatesByIndustry(?int $industryId = null): Collection
    {
        return EmailTemplateBackground::query()
                                        ->where(function($query) {
                                            $query
                                                ->where(EmailTemplateBackground::FIELD_OWNER_USER_ID, Auth::id())
                                                ->orWhere(EmailTemplateBackground::FIELD_PERSONAL, false);
                                        })
                                        ->where(EmailTemplateBackground::FIELD_INDUSTRY_ID, $industryId)
                                        ->get();
    }

    /**
     * @param int $id
     * @return EmailTemplateBackground
     */
    public function getBackgroundTemplateById(int $id): EmailTemplateBackground
    {
        return EmailTemplateBackground::findOrFail($id);
    }

    /**
     * @param int $ownerUserId
     * @param string $name
     * @param string|null $header
     * @param string|null $footer
     * @param bool $personal
     * @param int|null $industryId
     * @return EmailTemplateBackground|Model
     */
    public function createTemplate(
        int $ownerUserId,
        string $name,
        ?string $header,
        ?string $footer,
        bool $personal,
        ?int $industryId = null
    ): EmailTemplateBackground|Model
    {
        return EmailTemplateBackground::query()->create([
            EmailTemplateBackground::FIELD_OWNER_USER_ID => $ownerUserId,
            EmailTemplateBackground::FIELD_NAME => $name,
            EmailTemplateBackground::FIELD_HEADER => $header ?? '',
            EmailTemplateBackground::FIELD_FOOTER => $footer ?? '',
            EmailTemplateBackground::FIELD_PERSONAL => $personal,
            EmailTemplateBackground::FIELD_INDUSTRY_ID => $industryId,
        ]);
    }

    /**
     * @param int $id
     * @param int|null $ownerUserId
     * @param string|null $name
     * @param string|null $header
     * @param string|null $footer
     * @param bool|null $personal
     * @param int|null $industryId
     * @return bool
     */
    public function updateTemplate(
        int $id,
        ?int $ownerUserId = null,
        ?string $name = null,
        ?string $header = null,
        ?string $footer = null,
        ?bool $personal = null,
        ?int $industryId = null
    ): bool
    {
        return EmailTemplateBackground::query()->where(EmailTemplateBackground::FIELD_ID, $id)->update([
            EmailTemplateBackground::FIELD_OWNER_USER_ID => $ownerUserId,
            EmailTemplateBackground::FIELD_NAME => $name,
            EmailTemplateBackground::FIELD_HEADER => $header,
            EmailTemplateBackground::FIELD_FOOTER => $footer,
            EmailTemplateBackground::FIELD_PERSONAL => $personal,
            EmailTemplateBackground::FIELD_INDUSTRY_ID => $industryId,
        ]);
    }

    /**
     * @param int $id
     * @return bool
     */
    public function deleteBackgroundTemplate(int $id): bool
    {
        return EmailTemplateBackground::query()->where(EmailTemplateBackground::FIELD_ID, $id)->delete();
    }

    /**
     * @param string $header
     * @param string $footer
     * @return string
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function convertTemplateMarkdownToHtml(string $header, string $footer): string
    {
        return app()->makeWith(CustomUserMarkdown::class, [
            'data' => [
                'header' => $header,
                'content' => "[User content goes here]",
                "content_template_id" => 0,
                'footer' => $footer
            ],
            'payload' => WorkflowPayloadFactory::create(WorkflowEvent::fromArray([
                "event_category" => EventCategory::COMPANIES->value,
                "event_name" => EventName::BUDGET_INCREASE->value,
                "event_data" => []
            ]))
        ])->preview();
    }

    /**
     * Handles creating a duplicate template (background) against the requested user.
     *
     * @param int                     $ownerUserId
     * @param string                  $name
     * @param EmailTemplateBackground $templateToDuplicate
     * @return EmailTemplateBackground|Model
     * @throws Exception
     */
    public function createDuplicateTemplate(int                     $ownerUserId,
                                            string                  $name,
                                            EmailTemplateBackground $templateToDuplicate,
    ): EmailTemplateBackground|Model
    {
        $dataToMoveOver = collect($templateToDuplicate)
            ->except([EmailTemplateBackground::FIELD_ID])
            ->toArray();

        $now = Carbon::now();

        $attributes = [
            ...$dataToMoveOver,
            EmailTemplateBackground::FIELD_OWNER_USER_ID  => $ownerUserId,
            EmailTemplateBackground::FIELD_NAME           => $name,
            EmailTemplateBackground::FIELD_CREATED_AT     => $now,
            EmailTemplateBackground::FIELD_UPDATED_AT     => $now,
        ];

        /** @var EmailTemplateBackground $newTemplate */
        $newTemplate = EmailTemplateBackground::query()->create($attributes);

        $this->setupImagesOnCloudFromTheGivenTemplate($templateToDuplicate, $newTemplate->{EmailTemplateBackground::FIELD_ID});

        return $newTemplate;
    }

    /**
     * Handles copying/uploading images on cloud from the given template (background).
     *
     * @param EmailTemplateBackground $templateToCopyFrom
     * @param int                     $templateIDToUploadImagesFor
     * @return void
     * @throws Exception
     */
    public function setupImagesOnCloudFromTheGivenTemplate(EmailTemplateBackground $templateToCopyFrom, int $templateIDToUploadImagesFor): void
    {
        $this->emailTemplateImageService->uploadImagesOnCloudFromOneTemplateToAnother(
            templateFrom : $templateToCopyFrom->{EmailTemplateBackground::FIELD_ID},
            templateTo   : $templateIDToUploadImagesFor,
            markdown     : $templateToCopyFrom->{EmailTemplateBackground::FIELD_HEADER},
        );

        $this->emailTemplateImageService->uploadImagesOnCloudFromOneTemplateToAnother(
            templateFrom : $templateToCopyFrom->{EmailTemplateBackground::FIELD_ID},
            templateTo   : $templateIDToUploadImagesFor,
            markdown     : $templateToCopyFrom->{EmailTemplateBackground::FIELD_FOOTER},
        );
    }
}
