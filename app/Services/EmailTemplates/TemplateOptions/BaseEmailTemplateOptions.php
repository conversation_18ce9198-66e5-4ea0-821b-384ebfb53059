<?php

namespace App\Services\EmailTemplates\TemplateOptions;

use App\Enums\EmailTemplateType;
use App\Services\EmailTemplates\TemplateAttachment\BaseEmailAttachment;

abstract class BaseEmailTemplateOptions
{
    /**
     * @var array<BaseEmailAttachment>
     */
    protected array $attachments = [];

    abstract static function getType(): EmailTemplateType;

    /**
     * @return array
     */
    protected function getShortcodes(): array
    {
        return [];
    }

    /**
     * @return array
     */
    protected function getAttachments(): array
    {
        return array_map(function ($attachment) {
            return [
                'id'    => $attachment::getType()->value,
                'label' => $attachment::getType()->getLabel(),
            ];
        }, $this->attachments);
    }

    /**
     * @return array
     */
    public function getOptions(): array
    {
        return [
            'shortcodes'  => $this->getShortcodes(),
            'attachments' => $this->getAttachments(),
        ];
    }
}
