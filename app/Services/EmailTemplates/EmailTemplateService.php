<?php

namespace App\Services\EmailTemplates;

use App\Enums\EmailTemplateScope;
use App\Enums\EmailTemplateType;
use App\Mail\ConsumerCalculatorResults\ConsumerCalculatorResultsEmail;
use App\Models\EmailTemplateBackground;
use App\Models\Odin\Consumer;
use App\Models\TestProduct;
use App\Models\User;
use Carbon\Carbon;
use App\Models\Odin\Industry;
use Illuminate\Contracts\Container\BindingResolutionException;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Factories\Workflows\WorkflowPayloadFactory;
use App\Mail\CustomUserMarkdown;
use App\Models\EmailTemplate;
use App\Workflows\WorkflowEvent;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Mail;

class EmailTemplateService
{
    /**
     * @param EmailTemplateImageService $emailTemplateImageService
     */
    public function __construct(
        protected EmailTemplateImageService $emailTemplateImageService
    ) {}

    /**
     * @param EmailTemplateScope|null $scope
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedUserTemplates(?EmailTemplateScope $scope = null, int $perPage = 10): LengthAwarePaginator
    {
        return EmailTemplate::byScope($scope)->paginate($perPage);
    }

    /**
     * @param int $id
     * @return EmailTemplate
     */
    public function getTemplateById(int $id): EmailTemplate
    {
        return EmailTemplate::findOrFail($id);
    }

    /**
     * @param int $ownerUserId
     * @param string $name
     * @param string $subject
     * @param string $content
     * @param bool $personal
     * @param int $type
     * @param bool $active
     * @param int|null $industryId
     * @param int|null $backgroundId
     * @param string|null $engine
     * @return EmailTemplate|Model
     */
    public function createTemplate(
        int $ownerUserId,
        string $name,
        string $subject,
        string $content,
        bool $personal,
        int $type,
        bool $active,
        ?int $industryId = null,
        ?int $backgroundId = null,
        ?string $engine = null,
    ): EmailTemplate|Model
    {
        return EmailTemplate::query()->create([
            EmailTemplate::FIELD_OWNER_USER_ID  => $ownerUserId,
            EmailTemplate::FIELD_NAME           => $name,
            EmailTemplate::FIELD_SUBJECT        => $subject,
            EmailTemplate::FIELD_CONTENT        => $content,
            EmailTemplate::FIELD_PERSONAL       => $personal,
            EmailTemplate::FIELD_TYPE           => $type,
            EmailTemplate::FIELD_INDUSTRY_ID    => $industryId,
            EmailTemplate::FIELD_BACKGROUND_ID  => $backgroundId,
            EmailTemplate::FIELD_ENGINE         => $engine,
            EmailTemplate::FIELD_ACTIVE         => $active,
        ]);
    }

    /**
     * @param int $id
     * @param int $ownerUserId
     * @param string $name
     * @param string $subject
     * @param string $content
     * @param bool $personal
     * @param int $type
     * @param bool $active
     * @param int|null $industryId
     * @param int|null $backgroundId
     * @param string|null $engine
     * @return bool
     */
    public function updateTemplate(
        int $id,
        int $ownerUserId,
        string $name,
        string $subject,
        string $content,
        bool $personal,
        int $type,
        bool $active,
        ?int $industryId = null,
        ?int $backgroundId = null,
        ?string $engine = null,
        ?array $payload = []
    ): bool
    {
        return EmailTemplate::query()->where(EmailTemplate::FIELD_ID, $id)->update([
            EmailTemplate::FIELD_OWNER_USER_ID  => $ownerUserId,
            EmailTemplate::FIELD_NAME           => $name,
            EmailTemplate::FIELD_SUBJECT        => $subject,
            EmailTemplate::FIELD_CONTENT        => $content,
            EmailTemplate::FIELD_PERSONAL       => $personal,
            EmailTemplate::FIELD_TYPE           => $type,
            EmailTemplate::FIELD_INDUSTRY_ID    => $industryId,
            EmailTemplate::FIELD_BACKGROUND_ID  => $backgroundId,
            EmailTemplate::FIELD_ENGINE         => $engine,
            EmailTemplate::FIELD_ACTIVE         => $active,
            EmailTemplate::FIELD_PAYLOAD        => $payload,
        ]);
    }

    /**
     * @param int $id
     * @return bool
     */
    public function deleteTemplate(int $id): bool
    {
        return EmailTemplate::query()->where(EmailTemplate::FIELD_ID, $id)->delete();
    }

    /**
     * @param string $markdown
     * @param int|null $backgroundId
     * @return string
     * @throws BindingResolutionException
     */
    public function convertTemplateMarkdownToHtml(string $markdown, ?int $backgroundId = null): string
    {
        if($backgroundId > 0) {
            $background = EmailTemplateBackground::findOrFail($backgroundId);

            $imageService = app(EmailTemplateImageService::class);

            $header = $imageService->shortcodesToMarkdownImageTags($background->{EmailTemplateBackground::FIELD_HEADER}, $backgroundId);
            $footer = $imageService->shortcodesToMarkdownImageTags($background->{EmailTemplateBackground::FIELD_FOOTER}, $backgroundId);
        }

        return app()->makeWith(CustomUserMarkdown::class, [
            'data' => [
                'header' => !empty($header) ? $header : '' ,
                'content' => $markdown,
                'content_template_id' => 0,
                'footer' => !empty($footer) ? $footer : '',
                'background_template_id' => $backgroundId
            ],
            'payload' => WorkflowPayloadFactory::create(WorkflowEvent::fromArray([
                "event_category" => EventCategory::COMPANIES->value,
                "event_name" => EventName::BUDGET_INCREASE->value,
                "event_data" => []
            ]))
        ])->preview();
    }

    /**
     * Handles creating a duplicate template against the requested user.
     *
     * @param int           $ownerUserId
     * @param string        $name
     * @param EmailTemplate $templateToDuplicate
     * @return EmailTemplate|Model
     * @throws Exception
     */
    public function createDuplicateTemplate(int           $ownerUserId,
                                            string        $name,
                                            EmailTemplate $templateToDuplicate,
    ): EmailTemplate|Model
    {
        $dataToMoveOver = collect($templateToDuplicate)
            ->except([EmailTemplate::FIELD_ID])
            ->toArray();

        $now = Carbon::now();

        $attributes = [
            ...$dataToMoveOver,
            EmailTemplate::FIELD_OWNER_USER_ID  => $ownerUserId,
            EmailTemplate::FIELD_NAME           => $name,
            EmailTemplate::FIELD_CREATED_AT     => $now,
            EmailTemplate::FIELD_UPDATED_AT     => $now,
        ];

        /** @var EmailTemplate $newTemplate */
        $newTemplate = EmailTemplate::query()->create($attributes);

        $this->setupImagesOnCloudFromTheGivenTemplate($templateToDuplicate, $newTemplate->{EmailTemplate::FIELD_ID});

        return $newTemplate;
    }

    /**
     * Handles copying/uploading images on cloud from the given template.
     *
     * @param EmailTemplate $templateToCopyFrom
     * @param int           $templateIDToUploadImagesFor
     * @return void
     * @throws Exception
     */
    public function setupImagesOnCloudFromTheGivenTemplate(EmailTemplate $templateToCopyFrom, int $templateIDToUploadImagesFor): void
    {
        $this->emailTemplateImageService->uploadImagesOnCloudFromOneTemplateToAnother(
            templateFrom : $templateToCopyFrom->{EmailTemplate::FIELD_ID},
            templateTo   : $templateIDToUploadImagesFor,
            markdown     : $templateToCopyFrom->{EmailTemplate::FIELD_CONTENT},
        );
    }

    /**
     * Returns the lead delivery template email for a given industry.
     *
     * @param Industry $industry
     * @return EmailTemplate|null
     */
    public function getLeadDeliveryTemplate(Industry $industry): ?EmailTemplate
    {
        return $industry->deliveryEmailTemplate;
    }

    /**
     * Returns the default lead delivery template.
     *
     * @return EmailTemplate
     */
    public function getDefaultLeadDeliveryTemplate(): EmailTemplate
    {
        /** @var EmailTemplate */
        return EmailTemplate::query()
            ->where(EmailTemplate::FIELD_DEFAULT_LEAD_DELIVERY_TEMPLATE, true)
            ->firstOrFail();
    }

    /**
     * @return Collection
     */
    public function getAllEmailTemplates(): Collection
    {
        return EmailTemplate::query()->orderBy(EmailTemplate::FIELD_NAME)->get();
    }

    /**
     * @param EmailTemplate $template
     *
     * @return bool
     */
    public function updateDefaultLeadEmailTemplate(EmailTemplate $template): bool
    {
        EmailTemplate::query()->update([EmailTemplate::FIELD_DEFAULT_LEAD_DELIVERY_TEMPLATE => false]);

        $template->default_lead_delivery_template = true;
        return $template->save();
    }

    /**
     * @param Consumer|TestProduct $consumer
     * @param mixed $engineName
     * @param mixed $calcOutputs
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function sendConsumerCalculatorResult(Consumer|TestProduct $consumer, string $engineName, array $calcOutputs): void
    {
        $emailTemplate = $this->getCalcResultEmailTemplateForEngineName($engineName);

        if($emailTemplate)
            Mail::to($consumer->email)->send(new ConsumerCalculatorResultsEmail($consumer, $emailTemplate, $calcOutputs));
    }

    /**
     * @param string $engineName
     * @return EmailTemplate|null
     * @throws Exception
     */
    public function getCalcResultEmailTemplateForEngineName(string $engineName): EmailTemplate|null
    {
        return EmailTemplate::query()
                ->where(EmailTemplate::FIELD_TYPE, EmailTemplateType::STATUS_CALCULATOR_RESULTS->value)
                ->where(EmailTemplate::FIELD_ENGINE, $engineName)
                ->where(EmailTemplate::FIELD_ACTIVE, true)
                ->orderBy(Model::UPDATED_AT, 'DESC')
                ->first();
    }

    public function getUsersEmailTemplatesForTemplateType(User $user, EmailTemplateType $emailTemplateType): Collection
    {
        return EmailTemplate::query()
            ->select([
                EmailTemplate::FIELD_ID,
                EmailTemplate::FIELD_NAME,
                EmailTemplate::FIELD_SUBJECT,
                EmailTemplate::FIELD_CONTENT
            ])
            ->where(EmailTemplate::FIELD_OWNER_USER_ID, $user->id)
            ->where(EmailTemplate::FIELD_PERSONAL, true)
            ->where(EmailTemplate::FIELD_TYPE, $emailTemplateType->value)
            ->get();
    }

}
