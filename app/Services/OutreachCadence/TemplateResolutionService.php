<?php

namespace App\Services\OutreachCadence;

use App\Http\Controllers\API\OutreachCadence\CadenceTemplateController;
use App\Mail\OutreachCadence\ConfigurableEmail;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Repositories\OutreachCadence\CadenceTemplateRepository;
use App\Repositories\OutreachCadence\CadenceUserContactRepository;
use App\Services\EmailTemplates\EmailTemplateImageService;
use Exception;
use Illuminate\Support\Str;
use ReflectionException;

class TemplateResolutionService
{

    public function __construct(
        protected ShortCodeService             $shortCodeService,
        protected CadenceUserContactRepository $contactRepository,
        protected CadenceUserService           $userService,
    ) {}

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return array
     */
    public function getResolvedTask(CompanyCadenceScheduledGroupAction $action): array
    {
        $template         = $action->taskTemplate;
        $shortCodeMapping = $this->shortCodeService->getMapping($action);
        $taskName         = $this->shortCodeService->resolve($template->task_name, $shortCodeMapping);
        $notes            = $this->shortCodeService->resolve($template->notes, $shortCodeMapping) . "\n\n";
        $notes            .= $this->extraTaskNoteInfo($action);
        return [
            'task_name' => $taskName,
            'notes'     => $notes
        ];
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return string
     */
    private function extraTaskNoteInfo(CompanyCadenceScheduledGroupAction $action): string
    {
        $contactExclusions = $this->contactRepository->getExcludedCompanyUsers($action->group->routine)
                                                     ->map(function ($user) {
                                                         /** @var CompanyUser $user */
                                                         return $user->first_name . ' ' . $user->last_name;
                                                     })->toArray();
        return " - Who: " . ($action->group->routine->contact_decision_makers_only ? "Decision Makers Only" : "All Contacts") . "\n" .
               " - When: " . ($action->group->routine->contact_on_weekdays_only ? "Weekdays Only" : " Any Day") . "\n" .
               " - Contact Exclusions: " . (count($contactExclusions) > 0 ? implode(",", $contactExclusions) : "None");
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param CompanyUser $companyUser
     * @return array
     */
    public function getResolvedSms(CompanyCadenceScheduledGroupAction $action, CompanyUser $companyUser): array
    {
        $template         = $action->smsTemplate;
        $shortCodeMapping = $this->shortCodeService->getMapping($action, $companyUser);
        $body             = $this->shortCodeService->resolve($template->body, $shortCodeMapping);
        return ['body' => $body];
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param CompanyUser $companyUser
     * @return array
     * @throws Exception
     */
    public function getResolvedEmail(CompanyCadenceScheduledGroupAction $action, CompanyUser $companyUser): array
    {
        $fromUser                = $this->userService->getCommunicationUser($action->group->routine);
        $template                = $action->emailTemplate;
        $shortCodeSubjectMapping = $this->shortCodeService->getMapping($action, $companyUser);
        $shortCodeBodyMapping    = $this->shortCodeService->getMapping($action, $companyUser, true);

        $subject = $this->shortCodeService->resolve($template->subject, $shortCodeSubjectMapping);

        $body = $this->shortCodeService->resolve($template->body, $shortCodeBodyMapping);
        $body = $this->replaceImageShortCodes($body, 'email', $action->email_template_id);
        $body = $this->addHeaderAndFooter($body, $fromUser, $shortCodeBodyMapping);
        $body = Str::markdown($body);

        return [
            'subject' => $subject,
            'body'    => $body
        ];
    }

    /**
     * @param User|null $user
     * @param int $templateId
     * @param string $subject
     * @param string $body
     * @return array
     * @throws ReflectionException
     * @throws Exception
     */
    public function getEmailPreview(?User $user, int $templateId, string $subject, string $body): array
    {
        $shortCodeSubjectMapping = $this->shortCodeService->getPreviewMapping($user);
        $shortCodeBodyMapping    = $this->shortCodeService->getPreviewMapping($user, true);
        $subject                 = $this->shortCodeService->resolve($subject, $shortCodeSubjectMapping);
        $body                    = $this->shortCodeService->resolve($body, $shortCodeBodyMapping);

        if ($user)
            $body = $this->addHeaderAndFooter($body, $user, $shortCodeBodyMapping);

        $mailable = new ConfigurableEmail(
            $user?->email ?? fake()->companyEmail,
            $user?->name ?? fake()->name,
            $subject,
            Str::markdown($body)
        );

        return [
            'subject' => $subject,
            'body'    => html_entity_decode($mailable->render())
        ];
    }

    /**
     * @param string|null $string
     * @param string $templateType
     * @param int $modelId
     * @return string|null
     * @throws Exception
     */
    private function replaceImageShortCodes(?string $string, string $templateType, int $modelId): null|string
    {
        if (!$string)
            return null;

        /** @var EmailTemplateImageService $emailTemplateImageService */
        $emailTemplateImageService = app(EmailTemplateImageService::class);
        $shortCodes                = $emailTemplateImageService->extractShortcodes($string);
        $path                      = CadenceTemplateController::IMAGE_UPLOAD_BASE_PATH . "/{$templateType}/{$modelId}";

        foreach ($shortCodes as $shortCode) {
            $url    = $emailTemplateImageService->getImageUrl($path, $shortCode);
            $string = str_replace("{image:{$shortCode}}", "<img src='{$url}'>", $string);
        }

        return $string;
    }

    /**
     * @param string $body
     * @param User $user
     * @param array $shortCodeMapping
     * @return string
     * @throws Exception
     */
    private function addHeaderAndFooter(string $body, User $user, array $shortCodeMapping): string
    {
        /** @var CadenceTemplateRepository $repository */
        $repository = app(CadenceTemplateRepository::class);

        [$header, $footer] = array_values($repository->getUserHeaderAndFooter($user->id));
        $header = $header ? $this->shortCodeService->resolve($header->content, $shortCodeMapping) : null;
        $header = $this->replaceImageShortCodes($header, 'header', $user->id);
        $footer = $footer ? $this->shortCodeService->resolve($footer->content, $shortCodeMapping) : null;
        $footer = $this->replaceImageShortCodes($footer, 'footer', $user->id);

        $concatBody = '';
        if ($header)
            $concatBody .= $header . "<br><br>";
        $concatBody .= $body;
        if ($footer)
            $concatBody .= "<br><br>" . $footer;

        return $concatBody;
    }
}
