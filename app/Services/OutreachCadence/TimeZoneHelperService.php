<?php

namespace App\Services\OutreachCadence;

use App\Enums\Timezone;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\ZipCodeRepository;
use Carbon\Carbon;
use Exception;
use JetBrains\PhpStorm\ArrayShape;

class TimeZoneHelperService
{

    const int FALLBACK_UTC_OFFSET = -5;

    public function __construct(
        protected ZipCodeRepository $zipCodeRepository
    ) {}

    /**
     * @param string|null $zipCodeString
     * @return int
     */
    public function getTimeZoneOffsetForZipcode(?string $zipCodeString): int
    {
        if(!$zipCodeString)
            return self::FALLBACK_UTC_OFFSET;

        $zipCode = $this->zipCodeRepository->getByZipCodeString($zipCodeString);
        [$standardUTCOffset, $observingDST] = $zipCode ? [$zipCode->utc, $zipCode->dst] : [self::FALLBACK_UTC_OFFSET, false];
        return $standardUTCOffset + (($observingDST && $this->isActiveDST()) ? 1 : 0);
    }

    /**
     * @param string $zipcode
     * @return array
     * @throws Exception
     */
    #[ArrayShape(['standard_utc_offset' => "int", 'observing_dst' => "bool"])]
    public function getTimezoneOffsetDataFromZipcode(string $zipcode): array
    {
        $usZipCode = $this->zipCodeRepository->getByZipCodeString($zipcode);

        if ($usZipCode) {
            $standardUTCOffset = $usZipCode->utc;
            $observingDST      = $usZipCode->dst;
        }
        else {
            $standardUTCOffset = self::FALLBACK_UTC_OFFSET;
            $observingDST      = false;
        }

        return [
            'standard_utc_offset' => $standardUTCOffset,
            'observing_dst'       => $observingDST
        ];
    }

    /**
     * @param bool $observingDST
     * @param bool|null $isActiveDST
     * @return int
     */
    public function getDSTAdjustment(bool $observingDST, ?bool $isActiveDST = null): int
    {
        $isActiveDST = $isActiveDST ?? $this->isActiveDST();

        return ($isActiveDST && $observingDST) ? 1 : 0;
    }

    /**
     * @return bool
     */
    private function isActiveDST(): bool
    {
        $currentTimeZone    = date_default_timezone_get();
        $timeZoneToCheckDST = 'America/Los_Angeles';
        date_default_timezone_set($timeZoneToCheckDST);
        $isActiveDST = date("I");
        date_default_timezone_set($currentTimeZone);

        return $isActiveDST;
    }

    /**
     * @param int $targetUtcOffset
     * @return Carbon
     */
    public function getLocalTime(int $targetUtcOffset): Carbon
    {
        return Carbon::now('UTC')->utcOffset($targetUtcOffset*60);
    }

    /**
     * @param Carbon $time
     * @param int|null $startHour
     * @param int|null $startMinute
     * @param int|null $endHour
     * @param int|null $endMinute
     * @return bool
     */
    public function timeIsWithinHours(Carbon $time, ?int $startHour, ?int $startMinute, ?int $endHour, ?int $endMinute): bool
    {
        $afterStart = $time->hour > $startHour || ($time->hour === $startHour && $time->minute >= $startMinute);
        if(!$afterStart)
            return false;
        return $time->hour < $endHour || ($time->hour === $endHour && $time->minute <= $endMinute);
    }

    /**
     * @param Company $company
     * @param Timezone $default
     * @return Timezone
     */
    public static function getCompanyTimezone(Company $company, Timezone $default = Timezone::MOUNTAIN): Timezone
    {
        $offset = $company->locations()->first()?->address?->utc ?? null;

        return $offset
            ? Timezone::tryFromWithDST($offset) ?? $default
            : $default;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return int
     * @throws Exception
     */
    public function getUTCOpenHourForProduct(ConsumerProduct $consumerProduct): int
    {
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->getTimezoneOffsetDataFromZipcode(
            $consumerProduct->address->zip_code
        );

        $timezoneConfig = $this->getTimezoneConfigurationByStandardUTCOffset($standardUTCOffset);
        $localOpen      = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR};

        $dstAdjustment = $this->getDSTAdjustment($observingDST);

        return $localOpen - $standardUTCOffset - $dstAdjustment;
    }

    /**
     * @param int $offset
     * @return LeadProcessingTimeZoneConfiguration|null
     */
    public function getTimezoneConfigurationByStandardUTCOffset(int $offset): LeadProcessingTimeZoneConfiguration|null
    {
        return LeadProcessingTimeZoneConfiguration::query()
            ->where(LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET, $offset)
            ->first();
    }
}
