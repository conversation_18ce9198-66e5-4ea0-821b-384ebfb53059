<?php

namespace App\Services\OutreachCadence;

use App\Mail\OutreachCadence\ConfigurableEmail;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Email;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Repositories\OutreachCadence\CadenceUserContactRepository;
use App\Services\Delivery\Email as DeliverableEmail;
use Exception;

class EmailService
{
    public function __construct(
        protected CadenceUserContactRepository $contactExclusionRepository,
        protected TemplateResolutionService    $templateService,
        protected CadenceUserService           $userService,
    ) {}

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return void
     */
    public function execute(CompanyCadenceScheduledGroupAction $action): void
    {
        $deliveryIssue = ActionLogger::logCommonDeliveryIssues($action);
        if($deliveryIssue)
            return;

        $fromUser = $this->userService->getCommunicationUser($action->group->routine);

        if (!$fromUser || !$fromUser->email) {
            ActionLogger::noManagerEmail($action);
            return;
        }
        $companyUsersToContact = $this->contactExclusionRepository->getContactableCompanyUsersForAction($action);
        if (count($companyUsersToContact) === 0) {
            ActionLogger::noAvailableContacts($action);
            return;
        }

        // Remove contacts with duplicate email address
        $companyUsersToContact = $companyUsersToContact->unique(CompanyUser::FIELD_EMAIL);

        foreach ($companyUsersToContact as $companyUser) {
            $toEmail = $companyUser->email;
            if (!$toEmail) {
                ActionLogger::noContactEmail($action, $companyUser);
                return;
            }

            try {
                $emailContent = $this->templateService->getResolvedEmail($action, $companyUser);
                $action->update([
                    CompanyCadenceScheduledGroupAction::FIELD_PREVIEW => "{$emailContent['subject']} \n {$emailContent['body']}"
                ]);
                $fromEmail = $this->getManagerEmail($fromUser, $action->group->routine);
                $subject = $emailContent['subject'];
                $body = $emailContent['body'];
                $mailable = new ConfigurableEmail(
                    $fromEmail,
                    $fromUser->name,
                    $subject,
                    $body
                );

                DeliverableEmail::send($toEmail, $mailable);

                // create email record
                Email::create([
                    Email::FIELD_TO_ADDRESS => $toEmail,
                    Email::FIELD_FROM_ADDRESS => $fromEmail,
                    Email::FIELD_SUBJECT => $subject,
                    Email::FIELD_BODY => $body,
                    Email::FIELD_TO_COMPANY_USER_ID => $companyUser->id,
                    Email::FIELD_FROM_USER_ID => $fromUser->id,
                    Email::FILED_COMPANY_CADENCE_GROUP_ACTION_ID => $action->id,
                ]);

                ActionLogger::success($action, "{$companyUser->first_name} {$companyUser->last_name}({$companyUser->id})");

                // Increment groups success count
                $action->group->success_count++;
                $action->group->save();
            } catch (Exception $e) {
                ActionLogger::unexpectedError($action, "{$companyUser->first_name} {$companyUser->last_name}({$companyUser->id})", $e);
            }
        }
    }

    /**
     * @param User $managerUser
     * @param CompanyCadenceRoutine $routine
     * @return string
     */
    private function getManagerEmail(User $managerUser, CompanyCadenceRoutine $routine): string
    {
        $userName = strtok($managerUser->email, '@');
        $domain = $routine->domain;
        return "{$userName}@{$domain}";
    }
}
