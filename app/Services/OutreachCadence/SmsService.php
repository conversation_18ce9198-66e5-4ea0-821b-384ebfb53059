<?php

namespace App\Services\OutreachCadence;

use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Odin\CompanyUser;
use App\Models\Phone;
use App\Repositories\ActivityFeedRepository;
use App\Repositories\CommunicationRepository;
use App\Repositories\OutreachCadence\CadenceUserContactRepository;
use App\Services\Communication\TwilioCommunicationService;

class SmsService
{
    const NO_FROM_NUMBER_MESSAGE = 'Account Manager Phone Not Found';
    const NO_TO_NUMBER_MESSAGE   = 'Contact Phone Not Found';
    const NO_CONTACTS_MESSAGE    = 'No Valid Contacts Found';

    public function __construct(
        protected CadenceUserContactRepository $contactExclusionRepository,
        protected TemplateResolutionService    $templateService,
        protected TwilioCommunicationService   $communicationService,
        protected CommunicationRepository      $communicationRepository,
        protected ActivityFeedRepository       $activityFeedRepository,
        protected CadenceUserService           $userService,
    ) {}

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return void
     */
    public function execute(CompanyCadenceScheduledGroupAction $action): void
    {
        $deliveryIssue = ActionLogger::logCommonDeliveryIssues($action);
        if($deliveryIssue)
            return;

        $fromUser = $this->userService->getCommunicationUser($action->group->routine);

        $userPhone = $fromUser?->primaryPhone();
        if (!$userPhone || !$userPhone->phone) {
            ActionLogger::noManagerPhone($action);
            return;
        }
        $companyUsersToContact = $this->contactExclusionRepository->getContactableCompanyUsersForAction($action);
        if (count($companyUsersToContact) === 0) {
            ActionLogger::noAvailableContacts($action);
            return;
        }

        // Remove contacts with duplicate phone numbers
        $companyUsersToContact = $companyUsersToContact->unique(CompanyUser::FIELD_FORMATTED_CELL_PHONE);

        foreach ($companyUsersToContact as $companyUser) {
            $toNumber = $companyUser->cell_phone;
            if (!$toNumber) {
                ActionLogger::noContactPhone($action, $companyUser);
                return;
            }
            $smsContent = $this->templateService->getResolvedSms($action, $companyUser);
            $action->update([CompanyCadenceScheduledGroupAction::FIELD_PREVIEW => $smsContent['body']]);
            $fromNumber = $userPhone->phone;
            $reference  = $this->communicationService->sendSMS($fromNumber, $toNumber, $smsContent['body']);
            $this->createTextAndAddActivity($reference, $userPhone, $toNumber, $smsContent['body'], $action);
            ActionLogger::success($action, "{$companyUser->first_name} {$companyUser->last_name}({$companyUser->id})", ['reference' => $reference]);

            // Increment groups success count
            $action->group->success_count++;
            $action->group->save();
        }
    }

    private function createTextAndAddActivity(string $reference, Phone $fromPhone, string $toNumber, string $content, CompanyCadenceScheduledGroupAction $action): void
    {
        $sms = $this->communicationRepository->updateOrCreateOutboundSMS(
            $this->communicationService->getServiceName(),
            $reference,
            $fromPhone->id,
            $toNumber,
            $content,
            null,
            null,
            $action->id
        );
    }
}
