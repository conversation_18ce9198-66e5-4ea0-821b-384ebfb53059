<?php

namespace App\Services\OutreachCadence;

use App\DataModels\Workflows\TaskResultDataModel;
use App\Enums\TaskResultType;
use App\Models\Cadence\CompanyCadenceScheduledGroup;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Sales\Task;
use App\Models\User;
use App\Repositories\TaskRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Str;

class TaskService
{
    const DEFAULT_SUBJECT = '(OC) - ';

    public function __construct(
        protected TaskRepository               $taskRepository,
        protected CompanyCadenceRoutineService $routineService,
        protected TemplateResolutionService    $templateResolutionService,
        protected CadenceUserService           $userService,
    ) {}

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param Carbon $dueDateTime
     * @return void
     * @throws Exception
     */
    public function createActionTask(CompanyCadenceScheduledGroupAction $action, Carbon $dueDateTime): void
    {
        try {
            $task        = $this->createCompanyTask($action);
            $taskContent = $this->templateResolutionService->getResolvedTask($action);
            $action->update([
                CompanyCadenceScheduledGroupAction::FIELD_TASK_ID => $task->id,
                CompanyCadenceScheduledGroupAction::FIELD_PREVIEW => $taskContent['task_name'] . "\n" . $taskContent['notes']
            ]);
        } catch (Exception $e) {
            ActionLogger::unexpectedError($action, "failed to create task", $e);
            if (!$action->task_id) {
                $this->routineService->concludeAction($action);
                return;
            }
        }

        try {
            $systemUser = User::systemUser();
            $this->taskRepository->addTaskNote($task, $systemUser->id, $taskContent['notes']);
            $task->update([
                Task::FIELD_SUBJECT      => '(OC) - ' . $taskContent['task_name'],
                Task::FIELD_RESULTS      => $task->results->push(new TaskResultDataModel(0, 'Terminate Cadence', TaskResultType::TERMINATE_CADENCE, [])),
                Task::FIELD_AVAILABLE_AT => $dueDateTime
            ]);
        } catch (Exception $e) {
            ActionLogger::unexpectedError($action, "failed to add task note to task({$task->id})", $e);
        }

    }

    /**
     * @param CompanyCadenceScheduledGroupAction $issueAlertAction
     * @param CompanyCadenceScheduledGroup $originalGroup
     * @return void
     * @throws Exception
     */
    public function createIssueAlertTask(CompanyCadenceScheduledGroupAction $issueAlertAction, CompanyCadenceScheduledGroup $originalGroup): void
    {
        try {
            $task = $this->createCompanyTask($issueAlertAction);
            $issueAlertAction->update([CompanyCadenceScheduledGroupAction::FIELD_TASK_ID => $task->id]);
        } catch (Exception $e) {
            ActionLogger::unexpectedError($issueAlertAction, "failed to create resolution task", $e);
            if (!$issueAlertAction->task_id) {
                $this->routineService->concludeAction($issueAlertAction);
                return;
            }
        }

        try {
            $systemUser = User::systemUser();
            foreach ($originalGroup->resolution_notes as $note){
                $this->taskRepository->addTaskNote($task, $systemUser->id, ucwords($note['message']));
            }

            $task->update([
                Task::FIELD_SUBJECT => '(OC)[' . ucfirst($originalGroup->actions->first()->action_type) . ' Failure]',
                Task::FIELD_RESULTS => collect([
                    new TaskResultDataModel(0, 'Terminate Cadence', TaskResultType::TERMINATE_CADENCE, []),
                    new TaskResultDataModel(0, 'Issue Resolved - Reattempt Failed Action', TaskResultType::REATTEMPT_FAILED_ACTION, []),
                    new TaskResultDataModel(0, 'Skip Failed Action', TaskResultType::VOID, []),
                ])
            ]);
        } catch (Exception $e) {
            ActionLogger::unexpectedError($issueAlertAction, "failed to add task note to task({$task->id})", $e);
        }
    }


    private function createCompanyTask(CompanyCadenceScheduledGroupAction $action): ?Task
    {
        try {
            $assigneeUserId = $this->userService->getTaskAssigneeUser($action->group->routine)?->id;
        } catch (Exception $e) {
            $this->routineService->failAction($action, CompanyCadenceRoutineService::FAIL_REASON_NO_ACCOUNT_MANAGER);
            return null;
        }
        $taskType               = $this->taskRepository->getTaskTypeByName('Company Contact');
        $taskCategory           = $this->taskRepository->getTaskCategoryByName('System - Outreach Cadence');
        $temporaryUniqueSubject = self::DEFAULT_SUBJECT . Str::uuid();
        try {
            $this->taskRepository->createTask(
                $temporaryUniqueSubject,
                $taskType->id,
                2, // 'Medium'
                $taskCategory->id,
                Carbon::now()->format('Y-m-d H:i'),
                $action->group->routine->company_id,
                $assigneeUserId
            );
        } catch (Exception $e) {
            ActionLogger::unexpectedError($action, "failed to create company task", $e);
            if (!$action->task_id) {
                $this->routineService->concludeAction($action);
                return null;
            }
        }

        return $this->taskRepository->getTaskByAssigneeAndSubject($assigneeUserId, $temporaryUniqueSubject);
    }
}
