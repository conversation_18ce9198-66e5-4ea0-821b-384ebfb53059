<?php

namespace App\Services\OutreachCadence;

use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\User;

class CadenceUserService
{

    /**
     * @param CompanyCadenceRoutine $routine
     * @return User|null
     */
    public function getCommunicationUser(CompanyCadenceRoutine $routine): ?User
    {
        return $this->getUser($routine);
    }

    /**
     * @param CompanyCadenceRoutine $routine
     * @return User|null
     */
    public function getTaskAssigneeUser(CompanyCadenceRoutine $routine): ?User
    {
        return $this->getUser($routine);
    }

    /**
     * @param CompanyCadenceRoutine $routine
     * @return User|null
     */
    private function getUser(CompanyCadenceRoutine $routine): ?User
    {
        return ($routine->use_account_manager || !$routine->user_id) ? $routine->company?->accountManager : $routine->user;
    }

}
