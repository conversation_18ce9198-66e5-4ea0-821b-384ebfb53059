<?php

namespace App\Services\OutreachCadence;

use App\Jobs\OutreachCadence\ExecuteAutomatedActionJob;
use App\Jobs\OutreachCadence\InitializeCompanyCadenceJob;
use App\Models\Cadence\BaseModel;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Cadence\CompanyCadenceScheduledGroup;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Odin\CompanyLocation;
use App\Models\User;
use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;

class CompanyCadenceRoutineService
{

    /**
     * This is to prevent the following scenario:
     * If you have two groups set to an exact time of 9AM with a delay of 24 hours, the expected result is that the second
     * action would execute exactly 24 hours after the first, but since the execution time of the second action is only calculated
     * after the conclusion of the first action, the 24-hour delay would likely result in an execution time of 9:02AM,
     * and would therefore roll over to the following (third) day at 9AM.
     * A grace period of 5 minutes will instead calculate to 8:57AM, and correctly roll it up to the second day's 9AM.
     */
    const GRACE_PERIOD_MINUTES = 5;

    const FAIL_REASON_NO_ACCOUNT_MANAGER = 'no_account_manager';
    const ISSUES                         = 'issues';
    const POST_WEEKEND_SCHEDULE_TIME     = '09:00';

    public function __construct(protected CompanyCadenceRoutineRepository $repository) {}

    /**
     * @param array $companyIds
     * @param int $routineConfigId
     * @param bool $useAccountManager
     * @return void
     */
    public function initializeCompanyRoutines(array $companyIds, int $routineConfigId, bool $useAccountManager): void
    {
        /** @var User $user */
        $assigningUser = \Auth::user();
        foreach ($companyIds as $companyId) {
            InitializeCompanyCadenceJob::dispatch($companyId, $routineConfigId, $useAccountManager, $assigningUser->id);
        }
    }

    /**
     * @param CompanyCadenceRoutine $companyRoutine
     * @return void
     * @throws Exception
     */
    public function scheduleNextGroup(CompanyCadenceRoutine $companyRoutine): void
    {
        $group = $this->getNextGroup($companyRoutine);
        if ($group)
            $this->scheduleGroupActions($group);
    }

    /**
     * @param CompanyCadenceRoutine $companyRoutine
     * @return CompanyCadenceScheduledGroup|null
     */
    private function getNextGroup(CompanyCadenceRoutine $companyRoutine): ?CompanyCadenceScheduledGroup
    {
        $groups = $companyRoutine->scheduledGroups;

        // exit if there is already a group pending execution/action
        if ($groups->whereIn(
                CompanyCadenceScheduledGroup::FIELD_STATUS,
                [CompanyCadenceScheduledGroup::STATUS_QUEUED, CompanyCadenceScheduledGroup::STATUS_PENDING])->count() > 0)
            return null;

        $groupsToBeQueued = $groups->where(CompanyCadenceScheduledGroup::FIELD_STATUS, CompanyCadenceScheduledGroup::STATUS_NOT_STARTED);
        $groupsToBeQueued = $this->setExecutionOrder($groupsToBeQueued);

        if ($groupsToBeQueued->count() > 0) {
            return $groupsToBeQueued->first();
        } else {
            $companyRoutine->update([CompanyCadenceRoutine::FIELD_STATUS => CompanyCadenceRoutine::STATUS_CONCLUDED]);
            return null;
        }
    }

    /**
     * @param Collection $groups
     * @return Collection<int, CompanyCadenceScheduledGroup>
     */
    private function setExecutionOrder(Collection $groups): Collection
    {
        // if all groups have a valid and unique ordinal value, sort by ordinal value, else use id
        $ordinalValues           = $groups->pluck(CompanyCadenceScheduledGroup::FIELD_ORDINAL_VALUE)->toArray();
        $nullOrdinalValuePresent = in_array(null, $ordinalValues, true);
        $uniqueOrdinalValues     = count($ordinalValues) === count(array_unique($ordinalValues));
        $sortingColumn           = (!$nullOrdinalValuePresent && $uniqueOrdinalValues) ? CompanyCadenceScheduledGroup::FIELD_ORDINAL_VALUE : BaseModel::FIELD_ID;
        return $groups->sortBy($sortingColumn);
    }

    /**
     * @param CompanyCadenceScheduledGroup $group
     * @return void
     * @throws Exception
     */
    public function scheduleGroupActions(CompanyCadenceScheduledGroup $group): void
    {
        $group->update([CompanyCadenceScheduledGroup::FIELD_STATUS => CompanyCadenceScheduledGroup::STATUS_QUEUED]);
        $executionTime = $this->getSoonestLocalExecutionTime($group);
        $group->update([CompanyCadenceScheduledGroup::FIELD_TARGET_EXECUTION_TIMESTAMP => $executionTime->format('Y-m-d H:i e')]);
        $delay = (int) Carbon::now('UTC')->diffInSeconds($executionTime->utcOffset(0), true);

        /** @var CompanyCadenceScheduledGroupAction $action */
        foreach ($group->actions as $action) {
            $this->scheduleAction($action, $delay);
        }
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param int $delaySeconds
     * @return void
     * @throws Exception
     */
    private function scheduleAction(CompanyCadenceScheduledGroupAction $action, int $delaySeconds): void
    {
        switch ($action->action_type) {
            case CompanyCadenceScheduledGroupAction::ACTION_TYPE_EMAIL:
            case CompanyCadenceScheduledGroupAction::ACTION_TYPE_SMS:
                $this->scheduleAutomatedAction($action, $delaySeconds);
                break;
            case CompanyCadenceScheduledGroupAction::ACTION_TYPE_TASK:
                $this->scheduleTask($action, $delaySeconds);
                break;
        }
    }

    /**
     * @param CompanyCadenceScheduledGroup $group
     * @return Carbon
     */
    public function getSoonestLocalExecutionTime(CompanyCadenceScheduledGroup $group): Carbon
    {
        $company = $group->routine->company;

        // Using primary company location to determine zipcode for all company contacts
        $zipCode   = null;
        $locations = $company->locations;
        if ($locations && $locations->count() > 0) {
            /** @var CompanyLocation $location */
            $location = ($locations->where(CompanyLocation::FIELD_IS_PRIMARY, true)->count() > 0) ?
                $locations->where(CompanyLocation::FIELD_IS_PRIMARY, true)->first() :
                $locations->first();
            $zipCode  = $location->address?->zip_code;
        }

        /** @var TimeZoneHelperService $timezoneService */
        $timezoneService = app(TimeZoneHelperService::class);
        $targetUtcOffset = $timezoneService->getTimeZoneOffsetForZipcode($zipCode);
        $localNowTime    = $timezoneService->getLocalTime($targetUtcOffset);

        $localExecutionTime = $localNowTime->addMinutes($group->execution_delay_minutes);

        if ($group->execution_time_exact) {
            if ($group->execution_delay_minutes >= 24 * 60)
                $localExecutionTime = $localExecutionTime->subMinutes(self::GRACE_PERIOD_MINUTES);
            $localExecutionTime = $localExecutionTime->next($group->execution_time_exact);
        } elseif ($group->execution_time_window_start && $group->execution_time_window_end) {
            [$startHour, $startMinute] = $group->parseStartTime();
            [$endHour, $endMinute] = $group->parseEndTime();
            if (!$timezoneService->timeIsWithinHours($localExecutionTime, $startHour, $startMinute, $endHour, $endMinute)) {
                if ($group->execution_delay_minutes >= 24 * 60)
                    $localExecutionTime = $localExecutionTime->subMinutes(self::GRACE_PERIOD_MINUTES);
                $localExecutionTime = $localExecutionTime->next($group->execution_time_window_start);
            }
        }

        // skip weekend
        if ($group->routine->contact_on_weekdays_only && $localExecutionTime->isWeekend()) {
            $time = $group->execution_time_exact ?? $group->execution_time_window_start ?? self::POST_WEEKEND_SCHEDULE_TIME;
            $localExecutionTime->nextWeekday()->setTimeFromTimeString($time);
        }

        return $localExecutionTime;
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param int $delaySeconds
     * @return void
     */
    private function scheduleAutomatedAction(CompanyCadenceScheduledGroupAction $action, int $delaySeconds): void
    {
        ExecuteAutomatedActionJob::dispatch($action)->delay($delaySeconds);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param int $delaySeconds
     * @return void
     * @throws Exception
     */
    private function scheduleTask(CompanyCadenceScheduledGroupAction $action, int $delaySeconds): void
    {
        // handle deleted routine
        if (!$action->group?->routine) {
            ActionLogger::routineDeleted($action);
            return;
        }

        try {
            /** @var TaskService $taskService */
            $taskService = app(TaskService::class);
            $taskService->createActionTask($action, Carbon::now()->addSeconds($delaySeconds));
        } catch (Exception $e) {
            ActionLogger::unexpectedError($action, "issue creating task", $e);
            if (!$action->task_id)
                $this->concludeAction($action);
        }
    }

    /**
     * @param CompanyCadenceScheduledGroup $group
     * @return void
     * @throws Exception
     */
    public function evaluateGroupStatusThenScheduleNext(CompanyCadenceScheduledGroup $group): void
    {
        $pendingActionCount = $group->actions
            ->where(CompanyCadenceScheduledGroupAction::FIELD_STATUS, CompanyCadenceScheduledGroupAction::STATUS_PENDING)
            ->count();
        if ($pendingActionCount === 0) {
            $group->update([CompanyCadenceScheduledGroup::FIELD_STATUS => CompanyCadenceScheduledGroup::STATUS_CONCLUDED]);

            // check for deleted routine
            if ($group->routine) {
                if (!$group->isSuccessful())
                    $this->createIssueAlertTask($group);
                else
                    $this->scheduleNextGroup($group->routine);
            }

        } else {
            $group->update([CompanyCadenceScheduledGroup::FIELD_STATUS => CompanyCadenceScheduledGroup::STATUS_PENDING]);
        }
    }

    /**
     * @param CompanyCadenceScheduledGroup $group
     * @return void
     * @throws Exception
     */
    public function skipGroup(CompanyCadenceScheduledGroup $group): void
    {
        $group->update([CompanyCadenceScheduledGroup::FIELD_STATUS => CompanyCadenceScheduledGroup::STATUS_SKIPPED]);
        ActionLogger::groupSkipped($group);
        // todo: terminate pending sibling action jobs
        $this->scheduleNextGroup($group->routine);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param string $reason
     * @return void
     * @throws Exception
     */
    public function failAction(CompanyCadenceScheduledGroupAction $action, string $reason): void
    {
        ActionLogger::genericLog($action, "action failed - {$reason}");
        $action->update([CompanyCadenceScheduledGroupAction::FIELD_STATUS => CompanyCadenceScheduledGroupAction::STATUS_CONCLUDED]);
        $this->evaluateGroupStatusThenScheduleNext($action->group);
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @return void
     * @throws Exception
     */
    public function concludeAction(CompanyCadenceScheduledGroupAction $action): void
    {
        $action->update([CompanyCadenceScheduledGroupAction::FIELD_STATUS => CompanyCadenceScheduledGroupAction::STATUS_CONCLUDED]);
        $this->evaluateGroupStatusThenScheduleNext($action->group);
    }

    /**
     * @param CompanyCadenceScheduledGroup $group
     * @return void
     * @throws Exception
     */
    private function createIssueAlertTask(CompanyCadenceScheduledGroup $group): void
    {
        // create group
        /** @var CompanyCadenceScheduledGroup $alertGroup */
        $alertGroup = $group->routine->scheduledGroups()->create([
            CompanyCadenceScheduledGroup::FIELD_EXECUTION_DELAY_MINUTES => 0,
            CompanyCadenceScheduledGroup::FIELD_STATUS => CompanyCadenceScheduledGroup::STATUS_QUEUED,
        ]);

        // create action
        /** @var CompanyCadenceScheduledGroupAction $alertAction */
        $alertAction = $alertGroup->actions()->create([
            CompanyCadenceScheduledGroupAction::FIELD_ACTION_TYPE => CompanyCadenceScheduledGroupAction::ACTION_TYPE_TASK,
        ]);

        $this->scheduleIssueAlertTask($alertAction, $group);
    }

    private function scheduleIssueAlertTask(CompanyCadenceScheduledGroupAction $issueAlertAction, CompanyCadenceScheduledGroup $originalGroup): void
    {
        try {
            /** @var TaskService $taskService */
            $taskService = app(TaskService::class);
            $taskService->createIssueAlertTask($issueAlertAction, $originalGroup);
        } catch (Exception $e) {
            ActionLogger::unexpectedError($issueAlertAction, "issue creating resolution task", $e);
            if (!$issueAlertAction->task_id)
                $this->concludeAction($issueAlertAction);
        }
    }

}
