<?php

namespace App\Services\OutreachCadence;

use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;

class ShortCodeService
{

    // Client Company
    const SHORTCODE_COMPANY_NAME             = '{{company_name}}';
    const SHORTCODE_COMPANY_INDUSTRIES_OR    = '{{company_industries_or}}';
    const SHORTCODE_COMPANY_INDUSTRIES_AND   = '{{company_industries_and}}';
    const SHORTCODE_COMPANY_INDUSTRY_DEFAULT = '{{company_industry_default}}';

    // Client Contact
    const SHORTCODE_CONTACT_NAME       = '{{contact_name}}';
    const SHORTCODE_CONTACT_FIRST_NAME = '{{contact_first_name}}';
    const SHORTCODE_CONTACT_LAST_NAME  = '{{contact_last_name}}';

    // Internal Account Manager
    const SHORTCODE_ACCOUNT_MANAGER_NAME        = '{{account_manager_name}}';
    const SHORTCODE_ACCOUNT_MANAGER_FIRST_NAME  = '{{account_manager_first_name}}';
    const SHORTCODE_ACCOUNT_MANAGER_LAST_NAME   = '{{account_manager_last_name}}';
    const SHORTCODE_ACCOUNT_MANAGER_PHONE       = '{{account_manager_phone}}';
    const SHORTCODE_ACCOUNT_MANAGER_EMAIL       = '{{account_manager_email}}';
    const SHORTCODE_ACCOUNT_MANAGER_MEETING_URL = '{{account_manager_meeting_url}}';

    // Other
    const SHORTCODE_DOMAIN = '{{domain}}';

    /**
     * @param User|null $user
     * @param bool $isEmailBody
     * @return array
     */
    public function getPreviewMapping(?User $user, bool $isEmailBody = false): array
    {
        $firstName   = fake()->firstName;
        $lastName    = fake()->lastName;
        $amFirstName = $this->getUserFirstName($user) ?? fake()->firstName;
        $amLastName  = $this->getUserLastName($user) ?? fake()->lastName;
        return [
            // Client Company
            self::SHORTCODE_COMPANY_NAME                => fake()->company . ' ' . ['Solar', 'Roofing', 'Siding'][rand(0, 2)],
            self::SHORTCODE_COMPANY_INDUSTRIES_OR       => 'solar, roofing or',
            self::SHORTCODE_COMPANY_INDUSTRIES_AND      => 'solar, roofing and',
            self::SHORTCODE_COMPANY_INDUSTRY_DEFAULT    => 'solar',

            // Client Contact
            self::SHORTCODE_CONTACT_NAME                => $firstName . ' ' . $lastName,
            self::SHORTCODE_CONTACT_FIRST_NAME          => $firstName,
            self::SHORTCODE_CONTACT_LAST_NAME           => $lastName,

            // Internal Account Manager
            self::SHORTCODE_ACCOUNT_MANAGER_NAME        => $amFirstName . ' ' . $amLastName,
            self::SHORTCODE_ACCOUNT_MANAGER_FIRST_NAME  => $amFirstName,
            self::SHORTCODE_ACCOUNT_MANAGER_LAST_NAME   => $amLastName,
            self::SHORTCODE_ACCOUNT_MANAGER_PHONE       => $user?->primaryPhone()?->phone ?? $this->getCommonPhone(),
            self::SHORTCODE_ACCOUNT_MANAGER_EMAIL       => $user?->email ?? fake()->email,
            self::SHORTCODE_ACCOUNT_MANAGER_MEETING_URL => $user?->meeting_url ?? 'https://my.meeting.link.com',

            // Other
            self::SHORTCODE_DOMAIN                      => $isEmailBody ? '<a href=https://fixr.com>FIXr.com</a>' : 'FIXr.com',
        ];
    }

    /**
     * @param CompanyCadenceScheduledGroupAction $action
     * @param CompanyUser|null $contact
     * @param bool $isEmailBody
     * @return array
     */
    public function getMapping(CompanyCadenceScheduledGroupAction $action, ?CompanyUser $contact = null, bool $isEmailBody = false): array
    {
        /** @var CadenceUserService $userService */
        $userService = app(CadenceUserService::class);

        $company            = $action->group?->routine?->company;
        $fromUser           = $userService->getCommunicationUser($action->group->routine);
        $companyIndustries  = $this->getCompanyIndustries($company);
        $defaultIndustry    = $companyIndustries[0]; // todo: in the future, AM's can select a default industry per company

        return [
            // Client Company
            self::SHORTCODE_COMPANY_NAME                => $company ? $company->name : '',
            self::SHORTCODE_COMPANY_INDUSTRIES_OR       => $company ? $this->implodeWithWordBeforeLastItem($companyIndustries, 'or', ', ') : '',
            self::SHORTCODE_COMPANY_INDUSTRIES_AND      => $company ? $this->implodeWithWordBeforeLastItem($companyIndustries, 'and', ', ') : '',
            self::SHORTCODE_COMPANY_INDUSTRY_DEFAULT    => $company ? $defaultIndustry : '',

            // Client Contact
            self::SHORTCODE_CONTACT_NAME                => $contact ? $contact->first_name . ' ' . $contact->last_name : '',
            self::SHORTCODE_CONTACT_FIRST_NAME          => $contact ? $contact->first_name : '',
            self::SHORTCODE_CONTACT_LAST_NAME           => $contact ? $contact->last_name : '',

            // Internal Account Manager
            self::SHORTCODE_ACCOUNT_MANAGER_NAME        => $fromUser ? $fromUser->name : '',
            self::SHORTCODE_ACCOUNT_MANAGER_FIRST_NAME  => $this->getUserFirstName($fromUser) ?? '',
            self::SHORTCODE_ACCOUNT_MANAGER_LAST_NAME   => $this->getUserLastName($fromUser) ?? '',
            self::SHORTCODE_ACCOUNT_MANAGER_PHONE       => $fromUser?->primaryPhone() ? $fromUser->primaryPhone()->phone : $this->getCommonPhone(),
            self::SHORTCODE_ACCOUNT_MANAGER_EMAIL       => $this->getAccountManagerEmail($fromUser, $action->group->routine),
            self::SHORTCODE_ACCOUNT_MANAGER_MEETING_URL => $fromUser ? $fromUser->meeting_url : '',

            // Other
            self::SHORTCODE_DOMAIN                      => $this->getDomain($action->group->routine, $isEmailBody),
        ];
    }

    /**
     * @param string $string
     * @param array $shortCodeMapping
     * @return string
     */
    public function resolve(string $string, array $shortCodeMapping): string
    {
        $string = str_replace('\_', '_', $string);
        foreach (array_keys($shortCodeMapping) as $shortCodeKey) {
            $string = str_replace($shortCodeKey, $shortCodeMapping[$shortCodeKey], $string);
        }
        return $string;
    }

    /**
     * @param Company|null $company
     * @return array
     */
    private function getCompanyIndustries(?Company $company): array
    {
        if (!$company)
            return [];
        return $company->industries->map(function ($industry) {
            return strtolower($industry->name);
        })->toArray();
    }

    /**
     * @param array $arr
     * @param string $word
     * @param string $separator
     * @return string
     */
    public function implodeWithWordBeforeLastItem(array $arr, string $word, string $separator = ', '): string
    {
        if (count($arr) > 1) {
            $lastItem = array_pop($arr);
            return implode($separator, $arr) . " {$word} " . $lastItem;
        } else {
            return $arr[0];
        }
    }

    /**
     * Todo: in the future, this should be set from a config, that does not currently exist
     *
     * @return string
     */
    private function getCommonPhone(): string
    {
        return '************';
    }

    private function getAccountManagerEmail(?User $fromUser, CompanyCadenceRoutine $routine): string
    {
        if (!$fromUser)
            return '';
        $userName = strtok($fromUser->email, '@');
        $domain   = $routine->domain;
        return "{$userName}@{$domain}";
    }

    /**
     * @param CompanyCadenceRoutine $routine
     * @param bool $isEmail
     * @return string
     */
    private function getDomain(CompanyCadenceRoutine $routine, bool $isEmail = false): string
    {
        $prettyName = match ($routine->domain) {
            'solarreviews.com' => 'SolarReviews.com',
            'roofingcalculator.com' => 'RoofingCalculator.com',
            default => 'FIXr.com',
        };

        if (!$isEmail)
            return $prettyName;

        $fullUrl = match ($routine->domain) {
            'solarreviews.com' => 'https://www.solarreviews.com/',
            'roofingcalculator.com' => 'https://roofingcalculator.com/',
            default => 'https://www.fixr.com/',
        };

        return "<a href={$fullUrl}>{$prettyName}</a>";
    }

    /**
     * @param User|null $user
     * @return string|null
     */
    private function getUserFirstName(?User $user): ?string
    {
        if (!$user)
            return null;
        $parts = explode(' ', $user->name);
        return count($parts) > 0 ? $parts[0] : null;
    }

    /**
     * @param User|null $user
     * @return string|null
     */
    private function getUserLastName(?User $user): ?string
    {
        if (!$user)
            return null;
        $parts = explode(' ', $user->name);
        return count($parts) > 1 ? $parts[count($parts) - 1] : null;
    }
}
