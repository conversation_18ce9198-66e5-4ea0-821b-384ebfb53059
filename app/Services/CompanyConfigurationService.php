<?php

namespace App\Services;

use App\Enums\CompanyConfigurationEnum;
use App\Models\Odin\CompanyConfiguration;
use App\Models\User;
use App\Repositories\CompanyConfigurationRepository;

class CompanyConfigurationService
{
    public function __construct(protected CompanyConfigurationRepository $configurationRepository)
    {

    }

    /**
     * @param int $companyId
     * @return CompanyConfiguration|null
     */
    public function getCompanyConfiguration(int $companyId): ?CompanyConfiguration
    {
        return $this->configurationRepository->getCompanyConfiguration($companyId);
    }

    /**
     * @param array $configAttributes
     * @param User $user
     * @return array
     */
    public function filterConfigurationsUserHasAccess(
        array $configAttributes,
        User $user
    ): array
    {
        $userPermissions = $user->getAllPermissions()->pluck('name');

        return collect($configAttributes)->filter(function ($value, $key) use ($userPermissions) {
            $type = CompanyConfigurationEnum::tryFrom($key);

            $requiredPermissions = collect($type?->getRequiredPermissions() ?? []);

            if (empty($type) || $requiredPermissions->isEmpty()) {
                return true;
            }

            return $requiredPermissions->intersect($userPermissions)->isNotEmpty();
        })->toArray();
    }

    /**
     * @param int $companyId
     * @param bool|null $allowLeadsNoCc
     * @param bool|null $enableTcpaPlayback
     * @param bool|null $neverExceedBudget
     * @param bool|null $disallowRanking
     * @param bool|null $receiveOffHourLeads
     * @param bool|null $appointmentsActive
     * @param bool|null $miAppointmentsActive
     * @param bool|null $requireAppointmentsCalendar
     * @param bool|null $missedProductsActive
     * @param bool|null $reviewsEnabled
     * @param bool|null $acceptUnderReviewLeads
     * @return CompanyConfiguration|null
     */
    public function saveCompanyConfiguration(
        int $companyId,
        ?bool $allowLeadsNoCc = null,
        ?bool $enableTcpaPlayback = null,
        ?bool $neverExceedBudget = null,
        ?bool $disallowRanking = null,
        ?bool $receiveOffHourLeads = null,
        ?bool $appointmentsActive = null,
        ?bool $miAppointmentsActive = null,
        ?bool $requireAppointmentsCalendar = null,
        ?bool $missedProductsActive = null,
        ?bool $reviewsEnabled = null,
        ?bool $acceptUnderReviewLeads = null,
    ): ?CompanyConfiguration
    {
        return $this->configurationRepository->updateOrCreateCompanyConfiguration(
            companyId                  : $companyId,
            allowLeadsNoCc             : $allowLeadsNoCc,
            enableTcpaPlayback         : $enableTcpaPlayback,
            neverExceedBudget          : $neverExceedBudget,
            disallowRanking            : $disallowRanking,
            receiveOffHourLeads        : $receiveOffHourLeads,
            appointmentsActive         : $appointmentsActive,
            miAppointmentsActive       : $miAppointmentsActive,
            requireAppointmentsCalendar: $requireAppointmentsCalendar,
            missedProductsActive       : $missedProductsActive,
            reviewEnabled              : $reviewsEnabled,
            acceptUnderReviewLeads     : $acceptUnderReviewLeads
        );
    }
}
