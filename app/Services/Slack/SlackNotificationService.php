<?php

namespace App\Services\Slack;

use App\Notifications\Slack\BaseSlackNotification;
use Illuminate\Support\Facades\Notification;

class SlackNotificationService implements SlackNotificationServiceContract
{
    /**
     * @param BaseSlackNotification $notification
     * @return void
     */
    public function sendSlackNotification(BaseSlackNotification $notification): void
    {
        Notification::send(
            Notification::route('slack', $notification->getSlackRoute()),
            $notification
        );
    }

}
