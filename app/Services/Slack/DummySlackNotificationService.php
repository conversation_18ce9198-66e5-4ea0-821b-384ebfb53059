<?php

namespace App\Services\Slack;

use App\Notifications\Slack\BaseSlackNotification;
use Illuminate\Support\Facades\Notification;

class DummySlackNotificationService implements SlackNotificationServiceContract
{
    /**
     * @param BaseSlackNotification $notification
     * @return void
     */
    public function sendSlackNotification(BaseSlackNotification $notification): void
    {
        logger()->info("slack notification sent");
    }

}
