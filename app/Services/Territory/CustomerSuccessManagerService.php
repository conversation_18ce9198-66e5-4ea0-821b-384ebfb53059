<?php

namespace App\Services\Territory;

use App\Enums\ActivityLog\ActivityLogName;
use App\Enums\ActivityLog\ActivityLogSubjectType;
use App\Models\Odin\Company;
use App\Models\Territory\CustomerSuccessManager;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Territory\CustomerSuccessManagerRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Spatie\Activitylog\Models\Activity;

class CustomerSuccessManagerService
{
    public function __construct(
        protected CustomerSuccessManagerRepository $customerSuccessManagerRepository,
        protected CompanyRepository                $companyRepository,
        protected ActivityLogRepository            $activityLogRepository,
    )
    {
    }

    /**
     * @param string|null $customerSuccessManagerName
     * @param string|null $companyName
     * @param bool|null $active
     * @return Builder
     */
    public function listCustomerSuccessManagers(
        ?string $customerSuccessManagerName = null,
        ?string $companyName = null,
        ?bool   $active = null,
    ): Builder
    {
        return $this->customerSuccessManagerRepository->listCustomerSuccessManagers(
            customerSuccessManagerName: $customerSuccessManagerName,
            companyName               : $companyName,
            active                    : $active,
        );
    }

    /**
     * @param int $managerId
     * @return CustomerSuccessManager|null
     */
    public function getCustomerSuccessManager(
        int $managerId,
    ): ?CustomerSuccessManager
    {
        return $this->customerSuccessManagerRepository->getCustomerSuccessManager(
            id: $managerId,
        );
    }

    /**
     * @param int $companyId
     * @return mixed
     */
    public function getCompanyCustomerSuccessManagers(
        int $companyId,
    ): mixed
    {
        $company = $this->companyRepository->findOrFail($companyId);
        return $company->{Company::RELATION_ALL_CUSTOMER_SUCCESS_MANAGERS};
    }

}
