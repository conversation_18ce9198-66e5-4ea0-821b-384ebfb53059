<?php

namespace App\Services;

use App\DTO\LeadRefund\LeadRefundItemPayload;
use App\DTO\LeadRefund\LeadRefundPayload;
use App\DTO\Notes\CreateNoteParam;
use App\Enums\Billing\BillingVersion;
use App\Enums\LeadRefundItemChargeRefundStatus;
use App\Enums\LeadRefundItemStatus;
use App\Enums\LeadRefundItemType;
use App\Enums\LeadRefundStatus;
use App\Enums\Notes\NoteRelationType;
use App\Jobs\RefundLeadJob;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceCredit;
use App\Models\Billing\InvoiceItem;
use App\Models\LeadRefundItem;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Odin\ProductAssignment;
use App\Models\LeadRefund;
use App\Repositories\LeadRefundRepository;
use App\Repositories\UserRepository;
use App\Services\RefundLeadService\RefundLeadServiceFactory;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class LeadRefundService
{
    public function __construct(
        protected LeadRefundRepository $leadRefundRepository,
        protected NoteService $noteService,
        protected UserRepository $userRepository,
        protected LeadRefundNotificationService $leadRefundNotificationService
    )
    {

    }

    /**
     * @param ProductAssignment $productAssignment
     * @return LeadRefundItemType
     */
    public function calculateLegacyProductAssignmentRefundType(ProductAssignment $productAssignment): LeadRefundItemType
    {
        return !!$productAssignment->{ProductAssignment::RELATION_INVOICE_ITEM}
            ?->{EloquentInvoiceItem::RELATION_ELOQUENT_INVOICE}
            ->iscredit ? LeadRefundItemType::CREDIT : LeadRefundItemType::CASH;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return LeadRefundItemType
     */
    public function calculateAdminProductAssignmentRefundType(ProductAssignment $productAssignment): LeadRefundItemType
    {
        $isCredit = InvoiceCredit::query()
            ->whereHas(InvoiceCredit::RELATION_INVOICE, function ($query) use ($productAssignment) {
                $query->whereHas(Invoice::RELATION_INVOICE_ITEMS, function ($query) use ($productAssignment) {
                    $query->where(InvoiceItem::FIELD_BILLABLE_TYPE, ProductAssignment::class)
                        ->where(InvoiceItem::FIELD_BILLABLE_ID, $productAssignment->id);
                });
            })->exists();

        return $isCredit ? LeadRefundItemType::CREDIT : LeadRefundItemType::CASH;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return LeadRefundItemType
     * @throws Exception
     */
    public function getProductAssignmentRefundType(ProductAssignment $productAssignment): LeadRefundItemType
    {
        $billingVersion = $productAssignment->getBillingVersion();

        return match ($billingVersion) {
            BillingVersion::V1 => $this->calculateLegacyProductAssignmentRefundType($productAssignment),
            BillingVersion::V2 => $this->calculateAdminProductAssignmentRefundType($productAssignment),
            default            => throw new Exception('Billing version not supported')
        };
    }


    /**
     * @param array $productAssignmentIds
     * @return Collection<LeadRefundItemPayload>
     * @throws Exception
     */
    public function getRefundLeadData(array $productAssignmentIds): Collection
    {
        $productAssignments = ProductAssignment::query()
            ->whereIn(ProductAssignment::FIELD_ID, $productAssignmentIds)
            ->get();

        $refundData = collect();

        /** @var ProductAssignment $productAssignment */
        foreach ($productAssignments as $productAssignment) {
            $refundType = $this->getProductAssignmentRefundType($productAssignment);

            $payload = new LeadRefundItemPayload(
                productAssignmentId: $productAssignment->{ProductAssignment::FIELD_ID},
                cost               : $productAssignment->{ProductAssignment::FIELD_COST},
                refundType         : $refundType->value,
                refundReason       : null,
            );

            $refundData->push($payload);
        }

        return $refundData;

    }

    /**
     * @param LeadRefundPayload $leadRefundPayload
     * @return LeadRefund
     * @throws Exception
     */
    public function createRefundRequest(LeadRefundPayload $leadRefundPayload): LeadRefund
    {
        $refund = $this->leadRefundRepository->createRequestRefund(
            companyId: $leadRefundPayload->getCompanyId(),
            authorId : $leadRefundPayload->getAuthorId(),
            total    : $leadRefundPayload->getTotal(),
            items    : $leadRefundPayload->getItems(),
            status   : LeadRefundStatus::PENDING_REVIEW->value
        );

        $this->leadRefundNotificationService->notifyReviewersRequestOpened($refund);

        return $refund;
    }

    /**
     * @param int|null $companyId
     * @param int|null $reviewedBy
     * @param int|null $requestedBy
     * @param string|null $status
     * @param int|null $leadIdLegacyId
     * @return Builder
     */
    public function getLeadRefundQuery(
        ?int $companyId = null,
        ?int $reviewedBy = null,
        ?int $requestedBy = null,
        ?string $status = null,
        ?int $leadIdLegacyId = null,
    ): Builder
    {
        return $this->leadRefundRepository->getLeadRefundQuery(
            companyId     : $companyId,
            reviewedBy    : $reviewedBy,
            requestedBy   : $requestedBy,
            status        : $status,
            leadIdLegacyId: $leadIdLegacyId
        );
    }

    /**
     * @param LeadRefund $refund
     * @param array $comments
     * @param int $userId
     * @return void
     */
    public function addCommentsToRefund(
        LeadRefund $refund,
        array $comments,
        int $userId,
    ): void
    {
        foreach ($comments as $comment) {
            $this->noteService->createNote(
                new CreateNoteParam(
                    content     : $comment,
                    relationId  : $refund->{LeadRefund::FIELD_ID},
                    relationType: NoteRelationType::LEAD_REFUNDS,
                    userId      : $userId,
                )
            );
        }
    }

    /**
     * @param LeadRefund $refund
     * @param array $comments
     * @param int $userId
     * @return void
     */
    public function addComments(
        LeadRefund $refund,
        array $comments,
        int $userId,
    ): void
    {
        if (empty($comments)) {
            return;
        }

        $this->addCommentsToRefund(
            refund  : $refund,
            comments: $comments,
            userId  : $userId,
        );

        $this->leadRefundNotificationService->notifyRelevantUsersToRefundOfCommentsAdded(
            refund  : $refund,
            comments: $comments,
            userId  : $userId,
        );
    }

    /**
     * @param LeadRefund $approval
     * @param int $reviewerId
     * @param array $items
     * @param array $comments
     * @return void
     */
    public function reviewApproval(
        LeadRefund $approval,
        int $reviewerId,
        array $items,
        array $comments,
    ): void
    {
        foreach ($items as $item) {
            LeadRefundItem::query()
                ->where(LeadRefundItem::FIELD_ID, $item['id'])
                ->update([
                    LeadRefundItem::FIELD_STATUS => $item['status']
                ]);
        }

        $this->addCommentsToRefund(
            refund  : $approval,
            comments: $comments,
            userId  : $reviewerId,
        );

        $itemStatuses = $approval->items->pluck('status');

        if ($itemStatuses->contains(LeadRefundItemStatus::MORE_INFORMATION_NEEDED)) {
            $approvalStatus = LeadRefundStatus::MORE_INFORMATION_NEEDED;
        } elseif ($itemStatuses->every(fn($status) => $status === LeadRefundItemStatus::APPROVED)) {
            $approvalStatus = LeadRefundStatus::APPROVED;
        } elseif ($itemStatuses->every(fn($status) => $status === LeadRefundItemStatus::REJECTED)) {
            $approvalStatus = LeadRefundStatus::REJECTED;
        } else if ($itemStatuses->contains(LeadRefundItemStatus::PENDING)) {
            $approvalStatus = LeadRefundStatus::PENDING_REVIEW;
        } else {
            $approvalStatus = LeadRefundStatus::APPROVED_WITH_REJECTIONS;
        }

        $approval->update([
            LeadRefund::FIELD_STATUS      => $approvalStatus->value,
            LeadRefund::FIELD_REVIEWED_BY => $reviewerId,
            LeadRefund::FIELD_REVIEWED_AT => now(),
        ]);

        if ($approval->refresh()->canRefund()) {
            RefundLeadJob::dispatch($approval);
        }

        $this->leadRefundNotificationService->notifyRequesterLeadRefundHasBeenReviewed(
            refund  : $approval,
            comments: $comments
        );
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return bool
     * @throws Exception
     */
    public function checkIfProductAssignmentIsRefundable(ProductAssignment $productAssignment): bool
    {
        $billingVersion = $productAssignment->getBillingVersion();

        $refundLeadService = RefundLeadServiceFactory::make($billingVersion->value);
        $isRefundableForBilling = $refundLeadService->checkIfProductAssignmentIsRefundable($productAssignment);

        if (!$isRefundableForBilling) {
            return false;
        }

        $latestLeadRefundItem = $productAssignment->{ProductAssignment::RELATION_LATEST_LEAD_REFUND_ITEM};

        if ($latestLeadRefundItem?->status && $this->isStatusRejected($latestLeadRefundItem->status, LeadRefundItemStatus::REJECTED)) {
            return true;
        }

        if ($latestLeadRefundItem?->{LeadRefundItem::RELATION_LATEST_REFUND_ITEM_REFUND}) {
            $refundItemRefundStatus = $latestLeadRefundItem->{LeadRefundItem::RELATION_LATEST_REFUND_ITEM_REFUND}->status;
            if ($this->isStatusRejected($refundItemRefundStatus, LeadRefundItemChargeRefundStatus::STRIPE_ERROR)) {
                return true;
            }
        }

        if ($latestLeadRefundItem?->{LeadRefundItem::RELATION_REFUND}) {
            $leadRefundStatus = $latestLeadRefundItem->{LeadRefundItem::RELATION_REFUND}->status;
            if ($this->isStatusRejected($leadRefundStatus, LeadRefundStatus::REJECTED)) {
                return true;
            }
        } else {
            return true;
        }

        return false;
    }

    /**
     * @param $status
     * @param $rejectedStatus
     * @return bool
     */
    private function isStatusRejected($status, $rejectedStatus): bool
    {
        return collect([$rejectedStatus])->contains($status);
    }
}

