<?php

namespace App\Services;

use App\Enums\Billing\BillingLogLevel;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Billing\BillingLogService;
use Exception;

class CompanySuspensionService
{
    public function __construct(
        protected CompanyRepository $companyRepository
    )
    {

    }

    /**
     * @param int $companyId
     * @return void
     */
    public function suspend(int $companyId): void
    {
        $company = Company::query()->find($companyId);

        if (in_array($company->system_status, [CompanySystemStatus::SUSPENDED_PAYMENT, CompanySystemStatus::SUSPENDED_PAYMENT_METHOD])) {
            BillingLogService::log(
                message    : 'Company already suspended.',
                level      : BillingLogLevel::WARNING,
                namespace  : 'company_suspension',
                relatedType: Company::class,
                relatedId  : $companyId,
            );

            return;
        }

        $this->dispatchPubSubEvent($company);
    }

    /**
     * @param Company $company
     * @return void
     */
    public function dispatchPubSubEvent(Company $company): void
    {
        try {
            DispatchPubSubEvent::dispatch(
                EventCategory::COMPANIES,
                EventName::SUSPENDED_FOR_FAILED_PAYMENT,
                [
                    'company_reference' => $company->{Company::FIELD_REFERENCE}
                ]
            );
        } catch (Exception $exception) {
            logger()->error($exception->getMessage());
        }
    }
}
