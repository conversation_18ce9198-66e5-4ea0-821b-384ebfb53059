<?php

namespace App\Services;

use App\Contracts\Services\CompanyContractServiceContract;
use App\Enums\ContractType;
use App\Models\CompanyContract;
use App\Models\Contract;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\CompanyContractRepository;
use App\Services\CloudStorage\GoogleCloudStorageService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use SplFileObject;

class CompanyContractService implements CompanyContractServiceContract
{
    public function __construct(
        protected CompanyContractRepository $contractRepository,
    ) {}

    /**
     * @inheritDoc
     */
    public function createNewContract(Company $company, CompanyUser $companyUser, ContractType $contractType, string $ip, Contract $contract, string $signatureId = null): ?CompanyContract
    {
        $contractData = [
            CompanyContract::FIELD_COMPANY_ID           => $company->{Company::FIELD_ID},
            CompanyContract::FIELD_COMPANY_USER_ID      => $companyUser->{CompanyUser::FIELD_ID},
            CompanyContract::FIELD_CONTRACT_TYPE        => $contractType->value,
            CompanyContract::FIELD_IP_ADDRESS           => $ip,
            CompanyContract::FIELD_CONTRACT_ID          => $contract->id,
            CompanyContract::FIELD_SIGNATURE_ID         => $signatureId
        ];

        return $this->contractRepository->createCompanyContractFromAttributes($contractData);
    }

    /**
     * @inheritDoc
     */
    public function agreeToContract(Company $company, CompanyUser $companyUser, CompanyContract $companyContract): bool
    {
        if ($this->verifyContractDetails($company, $companyUser, $companyContract)) {
            return $companyContract->agree();
        }
        else return false;
    }

    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     * @param CompanyContract $companyContract
     * @return bool
     */
    protected function verifyContractDetails(Company $company, CompanyUser $companyUser, CompanyContract $companyContract): bool
    {
        return
            $companyContract->{CompanyContract::FIELD_COMPANY_ID} === $company->{Company::FIELD_ID}
            && $companyContract->{CompanyContract::FIELD_COMPANY_USER_ID} === $companyUser->{CompanyUser::FIELD_ID};
    }

    /**
     * @inheritDoc
     */
    public function cleanUpRedundantContracts(CompanyContract $companyContract): void
    {
        $companyContracts = $companyContract->{CompanyContract::RELATION_COMPANY}->{Company::RELATION_CONTRACTS};
        $contractType = $companyContract->contractModel->contractKey->name;
        $contractId = $companyContract->{CompanyContract::FIELD_ID};
        $userId = $companyContract->{CompanyContract::FIELD_COMPANY_USER_ID};

        $companyContracts->each(function(CompanyContract $contract) use ($contractType, $userId, $contractId) {
            if (
                $contractType === $contract->contractModel->contractKey->name
                 && !$contract->hasAgreed()
                 && $contract->{CompanyContract::FIELD_COMPANY_USER_ID} === $userId
                 && $contract->{CompanyContract::FIELD_ID} !== $contractId
            ) {
                $contract->delete();
            }
        });
    }

    /**
     * @throws BindingResolutionException
     */
    public function getSignedContractFileUrl(?string $companyContractUrl): ?string
    {
        if(!$companyContractUrl)
            return null;

        $googleCloudStorageService = app()->make(GoogleCloudStorageService::class);

        return $googleCloudStorageService->generateSignedUrl(config('services.google.storage.buckets.contracts'), $companyContractUrl);
    }

    public function uploadContractToGoogleBucket(CompanyContract $companyContract, UploadedFile $file): bool
    {
        $file->storeAs("/", "contract".$companyContract->id.".pdf", "local");

        $bucket = config('services.google.storage.buckets.contracts');
        $googleCloudStorageService = app()->make(GoogleCloudStorageService::class);
        $googleCloudStorageService->setCurrentBucket($bucket);

        $path = "/company-contracts/{$companyContract->id}/signature_request/{$companyContract->id}.pdf";

        $googleCloudStorageService->upload($path, fopen(storage_path("app/contract".$companyContract->id.".pdf"), 'r'));

        Storage::delete("contract".$companyContract->id.".pdf");

        $companyContract->file_url = $path;

        return $companyContract->save();
    }
}
