<?php

namespace App\Services;

use App\Models\CompanySlug;
use App\Models\Odin\Company;
use App\Repositories\CompanySlugRepository;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Random\RandomException;

class CompanySlugService
{
    const DEFAULT_SEPARATOR  = '-';
    const DEFAULT_LANGUAGE   = 'en';
    const DEFAULT_DICTIONARY = [];

    public function __construct(protected CompanySlugRepository $companySlugRepository)
    {
    }

    /**
     * @param  Company  $company
     * @return void
     * @throws RandomException
     */
    public function formatSlugForCompany(Company $company): void
    {
        $activeCompanySlug = $company->{Company::RELATION_ACTIVE_COMPANY_SLUG};
        if (empty($activeCompanySlug)) {
            $this->setSlugForNewCompany($company, true);
        } else {
            $slug = $this->slugify($activeCompanySlug->{CompanySlug::FIELD_SLUG});

            $isSlugUrlFriendly = $slug === $activeCompanySlug->{CompanySlug::FIELD_SLUG};

            if (!$isSlugUrlFriendly) {
                $this->companySlugRepository->createCompanySlug([
                    CompanySlug::FIELD_SLUG       => $slug,
                    CompanySlug::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                ]);
            }
        }
    }

    /**
     * @param  Company  $company
     * @param  bool  $allowRandomlyGeneratedNumbersAttached
     * @return void
     * @throws RandomException|Exception
     */
    public function setSlugForNewCompany(Company $company, bool $allowRandomlyGeneratedNumbersAttached = false): void
    {
        $slug = $this->slugify($company->{Company::FIELD_NAME});

        $slug = $this->setSlug($slug, $allowRandomlyGeneratedNumbersAttached);

        $this->companySlugRepository->createCompanySlugForCompany($company, [
            CompanySlug::FIELD_SLUG => $slug,
            CompanySlug::FIELD_REDIRECT_COMPANY_SLUG_ID => null
        ]);
    }

    /**
     * @param  string  $slug
     * @param  bool  $allowRandomlyGeneratedNumbersAttached
     * @return string
     * @throws RandomException|Exception
     */
    public function setSlug(string $slug, bool $allowRandomlyGeneratedNumbersAttached = false): string
    {
        $instanceOfSlugExists = $this->checkIfSlugExists($slug);

        $newSlug = $slug;

        $randomNumber = random_int(10, 99);

        $i = 0;

        if ($allowRandomlyGeneratedNumbersAttached) {
            while ($instanceOfSlugExists) {
                $newSlug = $slug.'-'.$randomNumber;

                $instanceOfSlugExists = $this->checkIfSlugExists($newSlug);

                $i++;

                $randomNumber = random_int(10, 999);

                if ($i === 4) {
                    throw new Exception('Could not create a unique slug after 5 tries.');
                }
            }
        } else {
            throw new Exception('The slug cannot be created without attaching numbers to it, however \'$allowRandomlyGeneratedNumbersAttached\' is set to false.');
        }

        return $newSlug;
    }

    /**
     * @param  string  $slug
     * @return bool
     */
    private function checkIfSlugExists(string $slug): bool
    {
        return $this->companySlugRepository->checkIfSlugExists($slug);
    }

    /**
     * @param  CompanySlug  $model
     * @return void
     */
    public function redirectPreviousSlug(CompanySlug $model): void
    {
        $company = $this->companySlugRepository->getCompanyForCompanySlug($model);

        $activeCompanySlug = $this->companySlugRepository->getActiveCompanySlugForCompany($company);

        $activeCompanySlugIsNotModel = $activeCompanySlug && $activeCompanySlug->{CompanySlug::FIELD_ID} !== $model->{CompanySlug::FIELD_ID};

        if ($activeCompanySlugIsNotModel) {
            $this->companySlugRepository->setSlugRedirect($activeCompanySlug, $model->{CompanySlug::FIELD_ID});
        }
    }

    /**
     * @param  string|null  $companyName
     * @param  string  $attemptedSlug
     * @return array
     * @throws RandomException
     */
    public function getAlternateSlugNames(?string $companyName, string $attemptedSlug): array
    {
        $alternateSlugNames = [];

        if ($companyName) {
            $companyNameSlug = $this->slugify($companyName);

            $exists = $this->checkIfSlugExists($companyNameSlug);

            if (!$exists) {
                $alternateSlugNames[] = $companyNameSlug;
            }
        }


        $altSlugOne = $attemptedSlug.'-'.random_int(10, 99);

        $exists = $this->checkIfSlugExists($altSlugOne);

        if (!$exists) {
            $alternateSlugNames[] = $altSlugOne;
        }

        $altSlugTwo = $attemptedSlug.'-'.random_int(10, 99);

        $exists = $this->checkIfSlugExists($altSlugTwo);

        if (!$exists) {
            $alternateSlugNames[] = $altSlugTwo;
        }

        return $alternateSlugNames;
    }

    /**
     * @param  Company  $company
     * @param  string|null  $slug
     * @return void
     */
    public function setSlugForCompany(Company $company, ?string $slug): void
    {
        if (!$slug) {
            return;
        }

        $slugAlreadyExists = $this->checkIfSlugExists($slug);

        if ($slugAlreadyExists) {
            return;
        }

        $this->companySlugRepository->createCompanySlugForCompanyQuietly($company, [
            CompanySlug::FIELD_SLUG => $slug,
        ]);
    }

    /**
     * @param  string  $slug
     * @return string
     */
    private function slugify(string $slug): string {
        return Str::slug(
            $slug,
            self::DEFAULT_SEPARATOR,
            self::DEFAULT_LANGUAGE,
            self::DEFAULT_DICTIONARY
        );
    }

    /**
     * @param string|null $slug
     * @return bool
     */
    public function isValid(?string $slug): bool
    {
        if (empty($slug)) {
            return false;
        }

        $validSlug = (string) Str::of($slug)->slug(
            self::DEFAULT_SEPARATOR,
            self::DEFAULT_LANGUAGE,
            self::DEFAULT_DICTIONARY
        );

        return $slug === $validSlug;
    }

    public function getFinalRedirectSlug(CompanySlug $companySlug): CompanySlug
    {
        $redirectId = $companySlug->redirect_company_slug_id;
        while($redirectId){
            $redirectCompanySlug = $this->companySlugRepository->find($redirectId);

            if( $redirectCompanySlug?->redirect_company_slug_id ){
                $redirectId = $redirectCompanySlug->redirect_company_slug_id;
                $companySlug = $redirectCompanySlug;
            }else{
                return $redirectCompanySlug ?? $companySlug;
            }

        }
        return $companySlug;
    }

    /**
     * Create a slug for all existing companies lacking one
     * This is a one-off function that should be run once (or to catch slugs back up in case of a lapse)
     *
     * @return void
     */
    public function createSlugsForExistingCompanies(): void
    {
        Company::query()
            ->doesntHave(Company::RELATION_COMPANY_SLUGS)
            ->chunkById(100, function (Collection $companies) {
                foreach ($companies as $company) {
                    try {
                        $this->setSlugForNewCompany($company, true);
                    }catch (Exception $exception){}
                }
            });
    }
}
