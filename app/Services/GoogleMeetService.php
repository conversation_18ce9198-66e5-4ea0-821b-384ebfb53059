<?php

namespace App\Services;

use App\DTO\Meet\ConferenceDTO;
use App\Enums\AppFeature;
use App\Enums\GoogleServiceType;
use App\Exceptions\GoogleMeet\GoogleMeetTokenNotFoundException;
use App\Models\Calendar\CalendarEvent;
use App\Models\GoogleUserToken;
use App\Services\Calendar\Google\GoogleMeetProvider;
use Google\Service\Exception;
use Illuminate\Support\Collection;

class GoogleMeetService
{
    /**
     * @param CalendarEvent $calendarEvent
     * @return Collection<ConferenceDTO>
     * @throws Exception|GoogleMeetTokenNotFoundException
     */
    public function getConferenceData(CalendarEvent $calendarEvent): Collection
    {
        $appLogger = AppLogger::make(
            relations: [$calendarEvent],
            feature  : AppFeature::CALENDAR,
            function : 'getConferenceData'
        );

        $conferenceCode = $calendarEvent->getConferenceCode();

        if (empty($conferenceCode)) {
            logger()->error('Calendar event does not have a conference attached.');
            return collect();
        }

        $googleMeetToken = GoogleUserToken::query()
            ->where(GoogleUserToken::FIELD_SERVICE, GoogleServiceType::MEET->value)
            ->where(GoogleUserToken::FIELD_USER_ID, $calendarEvent->{CalendarEvent::FIELD_USER_ID})
            ->first();

        if (!$googleMeetToken) {
            throw new GoogleMeetTokenNotFoundException($calendarEvent->{CalendarEvent::FIELD_USER_ID});
        }

        $meetProvider = new GoogleMeetProvider($googleMeetToken);

        $conferenceRecords = $meetProvider->getConferenceRecords(
            conferenceCode: $conferenceCode
        );

        $conferenceComponents = collect([
            'recordings'   => function (ConferenceDTO $conferenceRecord) use ($meetProvider) {
                $recordings = $meetProvider->getConferenceRecordings($conferenceRecord->getName());
                $conferenceRecord->setRecordings($recordings);
            },
            'participants' => function (ConferenceDTO $conferenceRecord) use ($meetProvider) {
                $participants = $meetProvider->getConferenceParticipants($conferenceRecord->getName());
                $conferenceRecord->setParticipants($participants);
            },
            'transcripts'  => function (ConferenceDTO $conferenceRecord) use ($meetProvider) {
                $transcripts = $meetProvider->getConferenceTranscripts($conferenceRecord->getName());
                $conferenceRecord->setTranscripts($transcripts);
            }
        ]);

        foreach ($conferenceRecords as $conferenceRecord) {
            foreach ($conferenceComponents as $component => $callback) {
                try {
                    $callback($conferenceRecord);
                } catch (Exception $exception) {
                    $appLogger->exception(
                        exception: $exception,
                        message: "Failed to retrieve conference $component. Conference id " . $conferenceRecord->getName()
                    );
                }
            }
        }

        // Remove non relevant conference records
        return $conferenceRecords->filter(fn(ConferenceDTO $conference) => $conference->getParticipants()->isNotEmpty());
    }
}

