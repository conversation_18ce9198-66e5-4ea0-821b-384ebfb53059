<?php

namespace App\Services;

use App\Enums\PermissionType;
use App\Http\Middleware\Verify2FAMiddleware;
use App\Repositories\ImpersonateRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Session;

class ImpersonateService
{
    const SESSION_KEY = 'impersonator_id';

    public function __construct(protected ImpersonateRepository $repository) {}

    /**
     * Handles impersonating a given user.
     *
     * @param int $impersonateId
     * @return bool
     */
    public function impersonate(int $impersonateId): bool
    {
        if(!Gate::check(PermissionType::IMPERSONATE_USERS->value))
            return false;

        $user = Auth::id();
        Session::put(self::SESSION_KEY, $user);
        Session::put(Verify2FAMiddleware::SESSION_KEY, true);
        Auth::loginUsingId($impersonateId);

        return $this->repository->log($user, $impersonateId) !== null;
    }

    /**
     * <PERSON>les clearing any impersonations.
     *
     * @return bool
     */
    public function clear(): bool
    {
        if(!Session::has(self::SESSION_KEY))
            return false;

        $user = Session::get(self::SESSION_KEY);
        Session::forget([self::SESSION_KEY,Verify2FAMiddleware::SESSION_KEY]);
        Auth::loginUsingId($user);

        return true;
    }
}
