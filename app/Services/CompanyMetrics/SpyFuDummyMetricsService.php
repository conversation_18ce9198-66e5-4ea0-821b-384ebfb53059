<?php

namespace App\Services\CompanyMetrics;

use App\Contracts\Services\CompanyMetricsServiceContract;
use App\DTO\PPCResponse;
use App\DTO\SpyFuPPCPayloadResponse;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\Odin\Company;
use DateTime;
use Faker\Factory as FakerFactory;

class SpyFuDummyMetricsService implements CompanyMetricsServiceContract
{
    const string REQUEST_RESPONSE_AVERAGE_ORGANIC_RANK      = 'averageOrganicRank';
    const string REQUEST_RESPONSE_MONTHLY_PAID_CLICKS       = 'monthlyPaidClicks';
    const string REQUEST_RESPONSE_AVERAGE_AD_RANK           = 'averageAdRank';
    const string REQUEST_RESPONSE_TOTAL_ORGANIC_RESULTS     = 'totalOrganicResults';
    const string REQUEST_RESPONSE_MONTHLY_ORGANIC_VALUE     = 'monthlyOrganicValue';
    const string REQUEST_RESPONSE_TOTAL_ADS_PURCHASED       = 'totalAdsPurchased';
    const string REQUEST_RESPONSE_MONTHLY_ORGANIC_CLICKS    = 'monthlyOrganicClicks';
    const string REQUEST_RESPONSE_STRENGTH                  = 'strength';
    const string REQUEST_RESPONSE_TOTAL_INVERSE_RANK        = 'totalInverseRank';
    const string REQUEST_RESPONSE_RESULTS                   = 'results';
    const string REQUEST_RESPONSE_SEARCH_MONTH              = 'searchMonth';
    const string REQUEST_RESPONSE_SEARCH_YEAR               = 'searchYear';
    const string REQUEST_RESPONSE_MONTHLY_BUDGET            = 'monthlyBudget';
    const string REQUEST_RESPONSE_DOMAIN                    = 'domain';
    public function __construct() {}

    /**
     * @inheritDoc
     */
    public function getServiceType(): CompanyMetricSources
    {
        return CompanyMetricSources::SPY_FU;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyMetrics(Company $company, ?DateTime $startDate = null, ?DateTime $endDate = null): ?PPCResponse
    {
        $faker = FakerFactory::create();

        $domain = $company->{Company::FIELD_WEBSITE} !== '' ? $company->{Company::FIELD_WEBSITE} : 'test.com';

        $response = [
            "resultCount" => 1,
            "domain" => $domain,
            "results" => [
                [
                    "searchMonth"           => now()->subMonth()->month,
                    "searchYear"            => now()->year,
                    "averageOrganicRank"    => $faker->randomFloat(2,1,100),
                    "monthlyPaidClicks"     => $faker->randomFloat(2,1,10000000),
                    "averageAdRank"         => $faker->randomFloat(2,1,100),
                    "totalOrganicResults"   => $faker->numberBetween(),
                    "monthlyBudget"         => $faker->randomFloat(2,0,1000000),
                    "monthlyOrganicValue"   => $faker->randomFloat(2,0,1000000),
                    "totalAdsPurchased"     => $faker->numberBetween(),
                    "monthlyOrganicClicks"  => $faker->randomFloat(),
                    "strength"              => $faker->numberBetween(),
                    "totalInverseRank"      => $faker->numberBetween(),
                ],
            ],
        ];

        $spyFuPPCResponsePayload = new SpyFuPPCPayloadResponse(
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_AVERAGE_ORGANIC_RANK],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_PAID_CLICKS],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_AVERAGE_AD_RANK],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_TOTAL_ORGANIC_RESULTS],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_ORGANIC_VALUE],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_TOTAL_ADS_PURCHASED],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_ORGANIC_CLICKS],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_STRENGTH],
            $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_TOTAL_INVERSE_RANK],
        );

        return new PPCResponse(
            month:          $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_SEARCH_MONTH],
            year:           $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_SEARCH_YEAR],
            monthlySpend:   $response[self::REQUEST_RESPONSE_RESULTS][0][self::REQUEST_RESPONSE_MONTHLY_BUDGET],
            targetDomain:   $response[self::REQUEST_RESPONSE_DOMAIN],
            request:        'dummyrequest',
            payload:        $spyFuPPCResponsePayload
        );
    }
}
