<?php

namespace App\Services\CompanyMetrics;

use App\Contracts\Services\CompanyMetricsServiceContract;
use App\DTO\PPCResponse;
use App\DTO\SimilarWebPPCPayloadResponse;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\Odin\Company;
use Carbon\Carbon;
use DateTime;
use Faker\Factory as FakerFactory;

class SimilarWebDummyMetricsService implements CompanyMetricsServiceContract
{
    const string REQUEST_RESPONSE_META                  = 'meta';
    const string REQUEST_RESPONSE_REQUEST               = 'request';
    const string REQUEST_RESPONSE_GRANULARITY           = 'granularity';
    const string REQUEST_RESPONSE_MAIN_DOMAIN_ONLY      = 'main_domain_only';
    const string REQUEST_RESPONSE_FORMAT                = 'format';
    const string REQUEST_RESPONSE_START_DATE            = 'start_date';
    const string REQUEST_RESPONSE_END_DATE              = 'end_date';
    const string REQUEST_RESPONSE_COUNTRY               = 'country';
    const string REQUEST_RESPONSE_STATUS                = 'status';
    const string REQUEST_RESPONSE_LAST_UPDATED          = 'last_updated';
    const string REQUEST_RESPONSE_BREAKDOWN             = 'breakdown';
    const string REQUEST_RESPONSE_DATE                  = 'date';
    const string REQUEST_RESPONSE_CURRENCIES            = 'currencies';
    const string REQUEST_RESPONSE_USD                   = 'usd';
    const string REQUEST_RESPONSE_DOMAIN                = 'domain';
    public function __construct() {}

    /**
     * @inheritDoc
     */
    public function getServiceType(): CompanyMetricSources
    {
        return CompanyMetricSources::SIMILAR_WEB;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyMetrics(Company $company, ?DateTime $startDate = null, ?DateTime $endDate = null): ?PPCResponse
    {
        $faker = FakerFactory::create();
        $domain = $company->{Company::FIELD_WEBSITE} !== '' ? $company->{Company::FIELD_WEBSITE} : 'test.com';

        $startDate = $faker->dateTimeBetween('-5 years', '-1 years')->modify('first day of this month');
        $endDate = $faker->dateTimeBetween($startDate, 'now')->modify('last day of this month');

        $breakdown = [];

        $currentMonth = clone $startDate;

        $currency = $faker->randomFloat(2, 0, 200000);

        $breakdown[] = [
            'date' => $currentMonth->format('Y-m-d'),
            'currencies' => [
                "aud" => $currency,
                "eur" => $currency,
                "gbp" => $currency,
                "jpy" => $currency,
                "usd" => $currency,
            ],
        ];

        $response = [
            "meta" => [
                "request" => [
                    "granularity"       => "Monthly",
                    "main_domain_only"  => false,
                    "format"            => "json",
                    "domain"            => $domain,
                    "start_date"        => $startDate->format('Y-m-d'),
                    "end_date"          => $endDate->format('Y-m-d'),
                    "country"           => "us",
                ],
                "status"        => "Success",
                "last_updated"  => $faker->date(),
            ],
            "breakdown" => $breakdown
        ];

        $similarWebPPCResponsePayload = new SimilarWebPPCPayloadResponse(
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_GRANULARITY],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_MAIN_DOMAIN_ONLY],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_FORMAT],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_START_DATE],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_END_DATE],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_COUNTRY],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_STATUS],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_LAST_UPDATED],
        );

        return new PPCResponse(
            month:          Carbon::createFromFormat('Y-m-d', $response[self::REQUEST_RESPONSE_BREAKDOWN][0][self::REQUEST_RESPONSE_DATE])->month,
            year:           Carbon::createFromFormat('Y-m-d', $response[self::REQUEST_RESPONSE_BREAKDOWN][0][self::REQUEST_RESPONSE_DATE])->year,
            monthlySpend:   $response[self::REQUEST_RESPONSE_BREAKDOWN][0][self::REQUEST_RESPONSE_CURRENCIES][self::REQUEST_RESPONSE_USD],
            targetDomain:   $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_DOMAIN],
            request:        'dummyrequest',
            payload:        $similarWebPPCResponsePayload
        );
    }
}
