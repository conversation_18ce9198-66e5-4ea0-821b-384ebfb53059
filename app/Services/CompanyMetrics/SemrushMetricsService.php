<?php

namespace App\Services\CompanyMetrics;

use App\Contracts\Services\CompanyMetricsServiceContract;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\Odin\Company;
use DateTime;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use ReflectionClass;

class SemrushMetricsService implements CompanyMetricsServiceContract
{
    const string RESPONSE_COLUMN_TARGET = 'target';
    const string RESPONSE_COLUMN_RANK = 'rank';
    const string RESPONSE_COLUMN_VISITS = 'visits';
    const string RESPONSE_COLUMN_CATEGORIES = 'categories';
    const string RESPONSE_COLUMN_DESKTOP_VISITS = 'desktop_visits';
    const string RESPONSE_COLUMN_MOBILE_VISITS = 'mobile_visits';
    const string RESPONSE_COLUMN_USERS = 'users';
    const string RESPONSE_COLUMN_DESKTOP_USERS = 'desktop_users';
    const string RESPONSE_COLUMN_MOBILE_USERS = 'mobile_users';
    const string RESPONSE_COLUMN_DIRECT = 'direct';
    const string RESPONSE_COLUMN_REFERRAL = 'referral';
    const string RESPONSE_COLUMN_SOCIAL = 'social';
    const string RESPONSE_COLUMN_SEARCH = 'search';
    const string RESPONSE_COLUMN_PAID = 'paid';
    const string RESPONSE_COLUMN_SEARCH_ORGANIC = 'search_organic';
    const string RESPONSE_COLUMN_SEARCH_PAID = 'search_paid';
    const string RESPONSE_COLUMN_SOCIAL_ORGANIC = 'social_organic';
    const string RESPONSE_COLUMN_SOCIAL_PAID = 'social_paid';
    const string RESPONSE_COLUMN_MAIL = 'mail';
    const string RESPONSE_COLUMN_DISPLAY_AD = 'display_ad';
    const string RESPONSE_COLUMN_UNKNOWN_CHANNEL = 'unknown_channel';
    const string RESPONSE_COLUMN_TIME_ON_SITE = 'time_on_site';
    const string RESPONSE_COLUMN_DESKTOP_TIME_ON_SITE = 'desktop_time_on_site';
    const string RESPONSE_COLUMN_MOBILE_TIME_ON_SITE = 'mobile_time_on_site';
    const string RESPONSE_COLUMN_PAGES_PER_VISIT = 'pages_per_visit';
    const string RESPONSE_COLUMN_DESKTOP_PAGES_PER_VISIT = 'desktop_pages_per_visit';
    const string RESPONSE_COLUMN_MOBILE_PAGES_PER_VISIT = 'mobile_pages_per_visit';
    const string RESPONSE_COLUMN_BOUNCE_RATE = 'bounce_rate';
    const string RESPONSE_COLUMN_DESKTOP_BOUNCE_RATE = 'desktop_bounce_rate';
    const string RESPONSE_COLUMN_MOBILE_BOUNCE_RATE = 'mobile_bounce_rate';
    const string RESPONSE_COLUMN_DESKTOP_SHARE = 'desktop_share';
    const string RESPONSE_COLUMN_MOBILE_SHARE ='mobile_share';
    const string RESPONSE_COLUMN_ACCURACY = 'accuracy';
    const string RESPONSE_COLUMN_DISPLAY_DATE = 'display_date';
    const string RESPONSE_COLUMN_COUNTRY = 'country';
    const string RESPONSE_COLUMN_DEVICE_TYPE = 'device_type';
    const string RESPONSE_COLUMN_DESKTOP_HITS = 'desktop_hits';
    const string RESPONSE_COLUMN_MOBILE_HITS = 'mobile_hits';

    protected ReflectionClass $reflectionClass;

    protected string $apiKey;
    protected string $baseUrl;
    protected string $trendsApiVersion;
    protected string $trendsUrl;

    public function __construct(protected Client $client) {
        $this->apiKey = config('services.company_metrics.semrush.api_key');
        $this->baseUrl = 'https://api.semrush.com/';
        $this->trendsApiVersion = 'v3';
        $this->trendsUrl = $this->baseUrl . 'analytics/ta/api/' . $this->trendsApiVersion;

        $this->reflectionClass = new ReflectionClass(self::class);
    }
    public function getServiceType(): CompanyMetricSources
    {
        return CompanyMetricSources::SEMRUSH;
    }

    /**
     * todo build this request base on the type of request given
     * dont want to save for now because the full request will have the key
     *
     * @return string
     */
    public function getRequestUrl(): string
    {
        return $this->trendsUrl . '/summary';
    }

    public function getApiCount(): int
    {
        try {
            $response = $this->client->request('GET', 'http://api.semrush.com/analytics/ta/limits/key/' . $this->apiKey);
            return json_decode((string)$response->getBody())[0]->requests_left;
        } catch (Exception $e) {;
            throw new Exception("Semrush API Count Failed: ". $e->getMessage());
        }
    }

    /**
     *
     * @param Company $company
     * @param DateTime|null $startDate
     * @param DateTime|null $endDate
     * @return array|null
     * @throws Exception|GuzzleException
     */
    public function getCompanyMetrics(Company $company, ?DateTime $startDate = null, ?DateTime $endDate = null): array|null
    {
        if(empty($company->website))
            throw new Exception("Company must have a website to get semrush metrics");

        try {
//            this request will get a traffic summary for a given domain.
//            not sure why but normal request params fail with the website string
            $response = $this->client->request('GET', $this->getRequestUrl() . '?' . http_build_query($this->buildRequestParameters($company)));
            $responseString = (string)$response->getBody();
        } catch (Exception $e) {;
            throw new Exception($e->getMessage());
        }

        $parsedResults = $this->parseResult($responseString);

        return array_key_exists(1, $parsedResults) ? $parsedResults[1] : throw new Exception('No results found');
    }

    /**
     * Builds array of parameters used in the API request
     * @param Company $company
     * @return array
     */
    private function buildRequestParameters(Company $company): array
    {
        return [
            'key'               => $this->apiKey,
            'targets'           => str_ireplace('www.', '', $company->website),
        ];
    }

    /**
     * Parse a result into an array.
     * we expected the API to return.
     *
     * @param string $data
     * @return array
     * @throws Exception
     */
    public function parseResult(string $data): array
    {
        $columns = $this->getColumnsIntoArray($data);

        $rows = $this->splitStringIntoArray($data);

        foreach ($rows as &$row) {
            $row = $this->parseRow($columns, $row);
        }
        unset($row);

        return $rows;
    }

    protected function getColumnsIntoArray(string $data): array
    {
        $rows = explode("\n", $data);
        return str_getcsv($rows[0], ";");
    }

    protected function splitStringIntoArray(string $data): array
    {
        $rows = explode("\n", $data);
        unset($rows[0]);
        foreach ($rows as $index => $row) {
            if ($row == "") {
                unset($rows[$index]);
            }
        }
        return $rows;
    }

    /**
     * @throws Exception
     */
    protected function parseRow(array $columns, string $data): array
    {
        $rows = str_getcsv($data, ";");
        $expectedColumnCount = count($columns);
        $actualColumnCount = count($rows);

        if ($expectedColumnCount != $actualColumnCount) {
            throw new Exception("Number of columns in row [{$actualColumnCount}] did not equal number of expected columns [{$expectedColumnCount}]");
        }

        return array_combine($columns, $rows);
    }

}