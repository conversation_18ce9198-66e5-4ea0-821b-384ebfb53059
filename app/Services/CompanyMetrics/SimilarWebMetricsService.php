<?php

namespace App\Services\CompanyMetrics;

use App\Contracts\Services\CompanyMetricsServiceContract;
use App\DTO\PPCResponse;
use App\DTO\SimilarWebPPCPayloadResponse;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Exception;
use DateTime;
use Illuminate\Support\Facades\Http;

class SimilarWebMetricsService implements CompanyMetricsServiceContract
{
    const string REQUEST_PARAMETERS_API_KEY             = "api_key";
    const string REQUEST_PARAMETERS_START_DATE          = "start_date";
    const string REQUEST_PARAMETERS_END_DATE            = "end_date";
    const string REQUEST_PARAMETERS_COUNTRY             = "country";
    const string REQUEST_PARAMETERS_GRANULARITY         = "granularity";
    const string REQUEST_PARAMETERS_MAIN_DOMAIN_ONLY    = "main_domain_only";
    const string REQUEST_PARAMETERS_FORMAT              = "format";
    const string REQUEST_RESPONSE_META                  = 'meta';
    const string REQUEST_RESPONSE_REQUEST               = 'request';

    const string REQUEST_RESPONSE_GRANULARITY           = 'granularity';
    const string REQUEST_RESPONSE_MAIN_DOMAIN_ONLY      = 'main_domain_only';
    const string REQUEST_RESPONSE_FORMAT                = 'format';
    const string REQUEST_RESPONSE_START_DATE            = 'start_date';
    const string REQUEST_RESPONSE_END_DATE              = 'end_date';
    const string REQUEST_RESPONSE_COUNTRY               = 'country';
    const string REQUEST_RESPONSE_STATUS                = 'status';
    const string REQUEST_RESPONSE_LAST_UPDATED          = 'last_updated';
    const string REQUEST_RESPONSE_BREAKDOWN             = 'breakdown';
    const string REQUEST_RESPONSE_DATE                  = 'date';
    const string REQUEST_RESPONSE_CURRENCIES            = 'currencies';
    const string REQUEST_RESPONSE_USD                   = 'usd';
    const string REQUEST_RESPONSE_DOMAIN                = 'domain';

    private readonly string $apiKey;

    public function __construct() {
        $this->apiKey = config('services.company_metrics.similar_web.api_key')? : "";
    }

    /**
     * @inheritDoc
     */
    public function getServiceType(): CompanyMetricSources
    {
        return CompanyMetricSources::SIMILAR_WEB;
    }

    /**
     * @inheritDoc
     *
     * Returns a json response from the Similar Web API
     */
    public function getCompanyMetrics(Company $company, ?DateTime $startDate = null, ?DateTime $endDate = null): ?PPCResponse
    {
        $requestParameters = $this->buildRequestParameters($startDate, $endDate);
        $requestPath = config('services.company_metrics.similar_web.base_url') . '/website/' . $company[Company::FIELD_WEBSITE] . '/ppc-spend/desktop';

        try {
            $res = Http::get($requestPath, $requestParameters);

            if($res->ok()) {
                $contents = json_decode($res->body());

                if($contents->meta->status !== "Success"){
                    throw new Exception("{$contents->meta->status}: Error within Similar Web api response.");
                }
            }
            else {
                throw new Exception("Request failed, status: " . $res->status());
            }
        }
        catch(Exception $e) {
            logger()->error("Failed to retrieve SimilarWeb Company Metrics for Company: " . $company[Company::FIELD_NAME]);

            throw $e;
        }

        $response = json_decode($res->body(), true);



        $similarWebPPCResponsePayload = new SimilarWebPPCPayloadResponse(
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_GRANULARITY],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_MAIN_DOMAIN_ONLY],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_FORMAT],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_START_DATE],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_END_DATE],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_COUNTRY],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_STATUS],
            $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_LAST_UPDATED],
        );

        return new PPCResponse(
            month:          Carbon::createFromFormat('Y-m-d', $response[self::REQUEST_RESPONSE_BREAKDOWN][0][self::REQUEST_RESPONSE_DATE])->month,
            year:           Carbon::createFromFormat('Y-m-d', $response[self::REQUEST_RESPONSE_BREAKDOWN][0][self::REQUEST_RESPONSE_DATE])->year,
            monthlySpend:   $response[self::REQUEST_RESPONSE_BREAKDOWN][0][self::REQUEST_RESPONSE_CURRENCIES][self::REQUEST_RESPONSE_USD],
            targetDomain:   $response[self::REQUEST_RESPONSE_META][self::REQUEST_RESPONSE_REQUEST][self::REQUEST_RESPONSE_DOMAIN],
            request:        $requestPath . http_build_query($requestParameters),
            payload:        $similarWebPPCResponsePayload
        );
    }

    /**
     * Builds array of parameters used in the API request
     * @param DateTime $startDate
     * @param DateTime $endDate
     * @return array
     */
    private function buildRequestParameters(DateTime $startDate, DateTime $endDate): array
    {
        return [
            self::REQUEST_PARAMETERS_API_KEY           => $this->apiKey,
            self::REQUEST_PARAMETERS_START_DATE        => $startDate->format("Y-m"),
            self::REQUEST_PARAMETERS_END_DATE          => $endDate->format("Y-m"),
            self::REQUEST_PARAMETERS_COUNTRY           => "us",
            self::REQUEST_PARAMETERS_GRANULARITY       => "monthly",
            self::REQUEST_PARAMETERS_MAIN_DOMAIN_ONLY  => "false",
            self::REQUEST_PARAMETERS_FORMAT            => "json",
        ];
    }

}
