<?php

namespace App\Services\Mailbox;

use App\Enums\AppFeature;
use App\Jobs\Mailbox\HandleMailProviderEventJob;
use App\Jobs\Mailbox\SetupUserEmailsListenerJob;
use App\Models\Mailbox\MailboxUserToken;
use App\Models\User;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\AppLogger;
use App\Services\Mailbox\Mail\MailProvider;
use App\Services\Mailbox\Mail\MailProviderFactory;
use Exception;

class MailboxWebhookService
{
    protected MailProvider $mailProvider;

    /**
     * @throws Exception
     */
    public function __construct(protected MailboxUserTokenRepository $mailboxUserTokenRepository)
    {
        $this->mailProvider = MailProviderFactory::make();
    }

    /**
     * @throws Exception
     */
    public function getMailboxAccessToken(string $userId, string $code): void
    {
        /** @var User $user */
        $user = User::query()->findOrFail($userId);

        $mailProvider = MailProviderFactory::make();

        $accessToken = $mailProvider->getAccessToken($code);

        $this->mailboxUserTokenRepository->createOne([
            MailboxUserToken::FIELD_USER_ID       => $userId,
            MailboxUserToken::FIELD_TOKEN         => $accessToken->getAccessToken(),
            MailboxUserToken::FIELD_REFRESH_TOKEN => $accessToken->getRefreshToken(),
        ]);

        SetupUserEmailsListenerJob::dispatch($user);
    }

    /**
     * @param string $email
     * @param int $historyId
     * @return void
     */
    public function dispatchJobToImportReceivedEmail(string $email, int $historyId): void
    {
        $user = User::query()
            ->where(User::FIELD_EMAIL, $email)
            ->orWhereJsonContains(User::FIELD_EMAIL_ALIASES, $email)
            ->first();

        $appLogger = AppLogger::make(
            feature: AppFeature::MAILBOX,
        );

        $appLogger->debug(
            message  : 'Email event received',
            context  : [
                'email'        => $email,
                'history_id'   => $historyId,
                'uses_mailbox' => $user?->{User::FIELD_USES_MAILBOX}
            ],
            relations: [$user]
        );

        if ($user?->{User::FIELD_USES_MAILBOX}) {
            HandleMailProviderEventJob::dispatch($user, $historyId);
        }
    }
}
