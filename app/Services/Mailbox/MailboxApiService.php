<?php

namespace App\Services\Mailbox;

use App\DTO\Mailbox\ListUserEmailsParam;
use App\Enums\Mailbox\EmailDirection;
use App\Enums\Mailbox\EmailModificationAction;
use App\Enums\Mailbox\EmailType;
use App\Jobs\Mailbox\ModifyEmailInMailProviderJob;
use App\Jobs\Mailbox\SendEmailJob;
use App\Models\Cadence\CadenceEmailHeaderFooter;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\User;
use App\Repositories\Mailbox\MailboxEmailRepository;
use App\Repositories\Mailbox\MailboxUserLabelRepository;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\Mailbox\Mail\MailProvider;
use App\Services\Mailbox\Mail\MailProviderFactory;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class MailboxApiService
{
    protected MailProvider $mailer;

    /**
     * @param MailboxEmailRepository $mailboxEmailRepository
     * @param MailboxUserTokenRepository $mailboxUserTokenRepository
     * @param MailboxUserLabelRepository $mailboxUserLabelRepository
     * @throws Exception
     */
    public function __construct(
        protected MailboxEmailRepository     $mailboxEmailRepository,
        protected MailboxUserTokenRepository $mailboxUserTokenRepository,
        protected MailboxUserLabelRepository $mailboxUserLabelRepository,
    )
    {
        $this->mailer = MailProviderFactory::make();
    }

    /**
     * @param int $userId
     * @param ListUserEmailsParam $listUserEmailsParams
     * @return Builder
     * @throws Exception
     */
    public function getListEmailsQuery(int $userId, ListUserEmailsParam $listUserEmailsParams): Builder
    {
        return $this->mailboxEmailRepository->getListUserEmailsQuery($userId, $listUserEmailsParams);
    }

    /**
     * TODO - Move to repository
     * @param string $userEmailUuid
     * @return MailboxUserEmail
     */
    public function getMailboxUserEmailByUuid(string $userEmailUuid): MailboxUserEmail
    {
        /** @var MailboxUserEmail $userEmail */
        $userEmail = MailboxUserEmail::query()->where(MailboxUserEmail::FIELD_UUID, $userEmailUuid)->firstOrFail();
        return $userEmail;
    }

    /**
     * @param string $userEmailUuid
     * @return Collection<MailboxUserEmail>
     */
    public function getEmailsRelatedByThread(string $userEmailUuid): Collection
    {
        $userEmail = $this->getMailboxUserEmailByUuid($userEmailUuid);

        return MailboxUserEmail::query()
            ->where(MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID, $userEmail->{MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID})
            ->orderBy(MailboxUserEmail::FIELD_SENT_AT)
            ->get();
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     */
    public function readEmail(MailboxUserEmail $email): bool
    {
        $email->update([MailboxUserEmail::FIELD_IS_READ  => true]);

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::READ);

        return true;
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     */
    public function unreadEmail(MailboxUserEmail $email): bool
    {
        $email->update([MailboxUserEmail::FIELD_IS_READ  => false]);

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::UNREAD);

        return true;
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function starEmail(MailboxUserEmail $email): bool
    {
        $email->update([MailboxUserEmail::FIELD_IS_STARRED  => true]);

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::STAR);

        return true;
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     */
    public function unstarEmail(MailboxUserEmail $email): bool
    {
        $email->update([MailboxUserEmail::FIELD_IS_STARRED  => false]);

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::UNSTAR);

        return true;
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function archiveEmail(MailboxUserEmail $email): bool
    {
        $email->update(
            [
                MailboxUserEmail::FIELD_IS_ARCHIVED  => true,
                MailboxUserEmail::FIELD_IS_INBOX  => false
            ]
        );

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::ARCHIVE);

        return true;
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     */
    public function unarchiveEmail(MailboxUserEmail $email): bool
    {
        $email->update(
            [
                MailboxUserEmail::FIELD_IS_ARCHIVED  => false,
                MailboxUserEmail::FIELD_IS_INBOX  => true
            ]
        );

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::UNARCHIVE);

        return true;
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function importantEmail(MailboxUserEmail $email): bool
    {
        $email->update([MailboxUserEmail::FIELD_IS_IMPORTANT  => true]);

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::IMPORTANT);

        return true;
    }

    /**
     * @param MailboxUserEmail $email
     * @return bool
     */
    public function unimportantEmail(MailboxUserEmail $email): bool
    {
        $email->update([MailboxUserEmail::FIELD_IS_IMPORTANT  => false]);

        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::UNIMPORTANT);

        return true;
    }

    /**
     * Send email
     * @param User $from
     * @param array $to
     * @param string $subject
     * @param string $content
     * @param array|null $bcc
     * @param array|null $cc
     * @return true
     * @throws Exception
     */
    public function sendEmail(
        User $from,
        array $to,
        string $subject,
        string $content,
        ?array $bcc = [],
        ?array $cc = [],
    ): true
    {
        $mailboxEmail = $this->mailboxEmailRepository->createEmailAndRecipients([
            MailboxEmail::FIELD_FROM_USER_ID        => $from->{User::FIELD_ID},
            MailboxEmail::FIELD_FROM_USER_EMAIL     => $from->{User::FIELD_EMAIL},
            MailboxEmail::FIELD_DIRECTION           => EmailDirection::OUTBOUND->value,
            MailboxEmail::FIELD_SUBJECT             => $subject,
            MailboxEmail::FIELD_TYPE                => EmailType::SEND->value,
            MailboxEmail::FIELD_SNIPPET             => strip_tags(substr($content, 0, 200)),
            MailboxEmail::FIELD_CONTENT             => $content,
            MailboxEmail::FIELD_FROM_A20            => true
        ], $to, $bcc, $cc);

        $mailboxUserEmail = $this->createMailboxUserEmailForOutgoing($from, $mailboxEmail);

        SendEmailJob::dispatch($mailboxUserEmail);

        return true;
    }

    /**
     * Reply email
     * @param string $emailUuidToReply
     * @param User $from
     * @param array $to
     * @param string $content
     * @param array $bcc
     * @param array $cc
     * @return true
     * @throws Exception
     */
    public function replyEmail(
        string $emailUuidToReply,
        User $from,
        array $to,
        string $content,
        array $bcc,
        array $cc,
    ): true
    {
        $emailToReply = $this->getMailboxUserEmailByUuid($emailUuidToReply);

        $token = $this->mailboxUserTokenRepository->getLatestUserEmailToken($from);

        $email = $this->mailer->getEmail($token, $emailToReply->{MailboxUserEmail::FIELD_EXTERNAL_ID});

        $mailboxEmail = $this->mailboxEmailRepository->createEmailAndRecipients([
            MailboxEmail::FIELD_FROM_USER_ID        => $from->{User::FIELD_ID},
            MailboxEmail::FIELD_FROM_USER_EMAIL     => $from->{User::FIELD_EMAIL},
            MailboxEmail::FIELD_DIRECTION           => EmailDirection::OUTBOUND->value,
            MailboxEmail::FIELD_SUBJECT             => $email->getMeta()?->getSubject(),
            MailboxEmail::FIELD_SNIPPET             => strip_tags(substr($content, 0, 200)),
            MailboxEmail::FIELD_TYPE                => EmailType::REPLY->value,
            MailboxEmail::FIELD_CONTENT             => $content,
            MailboxEmail::FIELD_FROM_A20            => true
        ], $to, $bcc, $cc);

        $mailboxUserEmail = $this->createMailboxUserEmailForOutgoing($from, $mailboxEmail, $emailToReply);

        SendEmailJob::dispatch($mailboxUserEmail, $emailToReply);

        return true;
    }

    /**
     * @param User $from
     * @param MailboxEmail $mailboxEmail
     * @param MailboxUserEmail|null $relatedEmail
     * @return MailboxUserEmail
     */
    private function createMailboxUserEmailForOutgoing(User $from, MailboxEmail $mailboxEmail, ?MailboxUserEmail $relatedEmail = null): MailboxUserEmail
    {
        /** @var MailboxUserEmail $mailboxUserEmail */
        $mailboxUserEmail = MailboxUserEmail::query()->create([
            MailboxUserEmail::FIELD_USER_ID                 => $from->{User::FIELD_ID},
            MailboxUserEmail::FIELD_EMAIL_ID                => $mailboxEmail->{MailboxEmail::FIELD_ID},
            MailboxUserEmail::FIELD_IS_INBOX                => false,
            MailboxUserEmail::FIELD_IS_SENT                 => true,
            MailboxUserEmail::FIELD_IS_STARRED              => false,
            MailboxUserEmail::FIELD_IS_IMPORTANT            => false,
            MailboxUserEmail::FIELD_IS_ARCHIVED             => false,
            MailboxUserEmail::FIELD_IS_READ                 => true,
            MailboxUserEmail::FIELD_EXTERNAL_ID             => Str::uuid(),
            MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID      => $relatedEmail?->{MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID} ?: Str::uuid(),
            MailboxUserEmail::FIELD_EXTERNAL_HISTORY_ID     => Str::uuid(),
            MailboxUserEmail::FIELD_EXTERNAL_REFERENCES     => Str::uuid(),
            MailboxUserEmail::FIELD_UUID                    => Str::uuid(),
            MailboxUserEmail::FIELD_SENT_AT                 => now(),
        ]);

        return $mailboxUserEmail;
    }

    /**
     * Forward email
     * @param string $emailUuidToForward
     * @param User $from
     * @param array $to
     * @param string $content
     * @param array $bcc
     * @param array $cc
     * @return true
     * @throws Exception
     */
    public function forwardEmail(
        string $emailUuidToForward,
        User $from,
        array $to,
        string $content,
        array $bcc,
        array $cc,
    ): true
    {
        $emailToForward = $this->getMailboxUserEmailByUuid($emailUuidToForward);


        $token = $this->mailboxUserTokenRepository->getLatestUserEmailToken($from);

        $email = $this->mailer->getEmail($token, $emailToForward->{MailboxUserEmail::FIELD_EXTERNAL_ID});

        $mailboxEmail = $this->mailboxEmailRepository->createEmailAndRecipients([
            MailboxEmail::FIELD_FROM_USER_ID        => $from->{User::FIELD_ID},
            MailboxEmail::FIELD_FROM_USER_EMAIL     => $from->{User::FIELD_EMAIL},
            MailboxEmail::FIELD_DIRECTION           => EmailDirection::OUTBOUND->value,
            MailboxEmail::FIELD_SUBJECT             => 'Fwd: ' . $email?->getMeta()?->getSubject(),
            MailboxEmail::FIELD_SNIPPET             => strip_tags(substr($content, 0, 200)),
            MailboxEmail::FIELD_TYPE                => EmailType::FORWARD->value,
            MailboxEmail::FIELD_CONTENT             => $content,
            MailboxEmail::FIELD_FROM_A20            => true
        ],$to, $bcc, $cc);

        $mailboxUserEmail = $this->createMailboxUserEmailForOutgoing($from, $mailboxEmail);

        SendEmailJob::dispatch($mailboxUserEmail);

        return true;
    }

    /**
     * Generate consent screen URL for redirect
     * @param int $userId
     * @return string
     */
    public function generateUrlForPermissionToMailbox(int $userId): string
    {
        return $this->mailer->generateOAuthConsentScreenUrl($userId);
    }

    /**
     * Delete email and related
     * @param string $userEmailUuid
     * @param bool $deleteThread
     * @return bool
     * @throws Exception
     */
    public function deleteEmailByUuid(string $userEmailUuid, bool $deleteThread = false): bool
    {
        if ($deleteThread){
            $relatedEmails = $this->getEmailsRelatedByThread($userEmailUuid);
            foreach ($relatedEmails as $relatedEmail) {
                $this->deleteEmail($relatedEmail);
            }

        } else {
            $mailboxUserEmail = $this->getMailboxUserEmailByUuid($userEmailUuid);
            $this->deleteEmail($mailboxUserEmail);
        }

        return true;
    }

    /**
     * @param array $emailUuids
     * @param bool $deleteThread
     * @return array
     * @throws Exception
     */
    public function deleteEmails(array $emailUuids, bool $deleteThread = false): array
    {
        $deletedEmailStatuses = [];
        $deletedEmailStatuses['status'] = true;
        $deletedEmailStatuses['individual_status'] = [];

        foreach ($emailUuids as $uuid) {
            $deletedEmailStatuses['individual_status'][$uuid] = $this->deleteEmailByUuid($uuid, $deleteThread);
        }

        if (in_array(false, $deletedEmailStatuses)) {
            $deletedEmailStatuses['all_deleted'] = false;
        }

        return $deletedEmailStatuses;
    }

    /**
     * @param MailboxUserEmail $email
     * @return void
     * @throws Exception
     */
    public function deleteEmail(MailboxUserEmail $email): void
    {
        ModifyEmailInMailProviderJob::dispatch($email, EmailModificationAction::DELETE);
        $email->delete();
    }

    /**
     * @param EmailModificationAction $modificationAction
     * @param array $emailUuids
     * @return array
     * @throws Exception
     */
    public function modifyEmails(EmailModificationAction $modificationAction, array $emailUuids): array
    {
        $modifiedEmailStatuses = [];
        $modifiedEmailStatuses['status'] = true;
        $modifiedEmailStatuses['individual_status'] = [];

        foreach ($emailUuids as $uuid) {
            $modifiedEmailStatuses['individual_status'][$uuid] = $this->modifyEmailAndRelated($modificationAction, $uuid);
        }

        return $modifiedEmailStatuses;
    }

    /**
     * @param EmailModificationAction $modificationAction
     * @param string $userEmailUuid
     * @return true
     * @throws Exception
     */
    public function modifyEmailAndRelated(EmailModificationAction $modificationAction, string $userEmailUuid): true
    {
        $relatedEmails = $this->getEmailsRelatedByThread($userEmailUuid);

        foreach ($relatedEmails as $email) {
            match ($modificationAction) {
                EmailModificationAction::READ           =>  $this->readEmail($email),
                EmailModificationAction::UNREAD         =>  $this->unreadEmail($email),
                EmailModificationAction::STAR           =>  $this->starEmail($email),
                EmailModificationAction::UNSTAR         =>  $this->unstarEmail($email),
                EmailModificationAction::ARCHIVE        =>  $this->archiveEmail($email),
                EmailModificationAction::UNARCHIVE      =>  $this->unarchiveEmail($email),
                EmailModificationAction::IMPORTANT      =>  $this->importantEmail($email),
                EmailModificationAction::UNIMPORTANT    =>  $this->unimportantEmail($email),
                default => throw new \Exception('Unexpected action value for ' . $modificationAction->value),
            };
        }

        return true;
    }

    /**
     * @param User $user
     * @return string
     */
    public function getUserEmailSignature(User $user): string
    {
        $cadenceFooter = CadenceEmailHeaderFooter::query()
            ->where(CadenceEmailHeaderFooter::FIELD_TYPE, CadenceEmailHeaderFooter::TYPE_FOOTER)
            ->where(CadenceEmailHeaderFooter::FIELD_USER_ID, $user->{User::FIELD_ID})
            ->first();

        if (!isset($cadenceFooter)) return '';

        return Str::markdown($cadenceFooter?->{CadenceEmailHeaderFooter::FIELD_CONTENT});
    }

    /**
     * @param int $userId
     * @return Collection
     */
    public function getLabels(int $userId): Collection
    {
        return $this->mailboxUserLabelRepository->getMailboxLabels($userId);
    }
}
