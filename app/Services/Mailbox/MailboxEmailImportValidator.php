<?php

namespace App\Services\Mailbox;

use App\DTO\Mail\Email;
use App\Enums\GlobalConfigurationKey;
use App\Services\GlobalConfigurationService;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class MailboxEmailImportValidator
{
    protected GlobalConfigurationService $globalConfigurationService;

    public function __construct()
    {
        $this->globalConfigurationService = app()->make(GlobalConfigurationService::class);
    }

    /**
     * @return Collection
     */
    public function getStoredRules(): Collection
    {
        $storedRules = $this->globalConfigurationService->getConfigData(GlobalConfigurationKey::MAILBOX_BLACKLIST);

        $allRules = collect();

        foreach ($storedRules as $key => $rules) {
            $allRules->push(...(explode(',', $rules)));
        }

        return $allRules->filter()->unique();
    }

    /**
     * @param Email $email
     * @return bool
     */
    public function checkIfEmailSatisfiesStoredRules(Email $email): bool
    {
        $rules = $this->getStoredRules();

        return $this->isValidEmail(
            email         : $email,
            blacklistRules: $rules
        );
    }

    /**
     * TODO - We don't support aliases. <NAME_EMAIL> is not <NAME_EMAIL>
     * Check if given email is valid to get imported.
     * The email should satisfy the rule in both ways (from -> to and to -> to)
     * The rules are:
     *  - Specific email address. Eg: <EMAIL>:<EMAIL>
     *  - Domain wildcard. Eg: <EMAIL>:*@email.com
     *  - Any. Eg: <EMAIL>:*
     *  - Block all. Eg: *:*
     * @param Email $email
     * @param Collection $blacklistRules
     * @return bool
     */
    public function isValidEmail(Email $email, Collection $blacklistRules): bool
    {
        $from = Str::of($email->getMeta()->getFrom())->trim()->lower();
        $recipients = collect([
            $email->getMeta()->getTo(),
            $email->getMeta()->getBcc(),
            $email->getMeta()->getCc(),
        ])->flatten()->filter();

        foreach ($blacklistRules as $rule) {
            $rule = Str::of($rule)->trim()->lower();

            if ($rule->exactly('*:*')) {
                return false; // Block all emails
            }

            [$firstCondition, $secondCondition] = explode(':', $rule, 2);

            foreach ($recipients as $recipient) {
                $isBlocked = $this->isBlocked(
                    from           : Str::of($from)->trim()->lower(),
                    recipient      : Str::of($recipient)->trim()->lower(),
                    firstCondition : Str::of($firstCondition)->trim()->lower(),
                    secondCondition: Str::of($secondCondition)->trim()->lower()
                );

                if ($isBlocked) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * @param string $from
     * @param string $recipient
     * @param string $firstCondition
     * @param string $secondCondition
     * @return bool
     */
    private function isBlocked(string $from, string $recipient, string $firstCondition, string $secondCondition): bool
    {
        // The first condition is the string before the :
        // In other words, $firstCondition:$secondCondition

        // Block any email involving a specific address
        // Rule: <EMAIL>:*
        // $firstCondition = <EMAIL>
        // $secondCondition = *
        if ($secondCondition === '*' && ($from === $firstCondition || $recipient === $firstCondition)) {
            return true;
        }

        // Block any email involving a specific address
        // Rule: *:<EMAIL>
        // $firstCondition = *
        // $secondCondition = <EMAIL>
        if ($firstCondition === '*' && ($from === $secondCondition || $recipient === $secondCondition)) {
            return true;
        }

        // Block emails involving a specific email and any recipient from a domain.
        // Example: <EMAIL>:*@domain.com
        // $firstCondition = <EMAIL>
        // $secondCondition = *@domain.com
        if (str_starts_with($secondCondition, '*@')) {
            if ($this->matchesDomainRule($from, $recipient, $firstCondition, $secondCondition)) {
                return true;
            }
        }

        // Block emails involving any sender from a domain and a specific email
        // Example: *@domain.com:<EMAIL>
        // $firstCondition = *@domain.com
        // $secondCondition = <EMAIL>
        if (str_starts_with($firstCondition, '*@')) {
            if ($this->matchesDomainRule($recipient, $from, $secondCondition, $firstCondition)) {
                return true;
            }
        }

        // Block all emails between senders/recipients from two specific domains
        // Example: *@domain.com:*@test.com
        // $firstCondition = *@domain.com
        // $secondCondition = *@test.com
        if (str_starts_with($firstCondition, '*@') && str_starts_with($secondCondition, '*@')) {
            if ($this->matchesBothDomainRule($from, $recipient, $firstCondition, $secondCondition)) {
                return true;
            }
        }

        // Block specific sender-recipient pair
        // Example <EMAIL>:<EMAIL>
        // $firstCondition = <EMAIL>
        // $secondCondition = <EMAIL>
        if (($from === $firstCondition && $recipient === $secondCondition) ||
            ($recipient === $firstCondition && $from === $secondCondition)) {
            return true;
        }

        return false;
    }

    /**
     * @param string $emailA
     * @param string $emailB
     * @param string $specificEmail
     * @param string $wildcardDomain
     * @return bool
     */
    private function matchesDomainRule(string $emailA, string $emailB, string $specificEmail, string $wildcardDomain): bool
    {
        $domain = substr($wildcardDomain, 2); // Remove "*@" from the condition to get the domain
        return ($emailA === $specificEmail && str_ends_with($emailB, '@' . $domain)) ||
            ($emailB === $specificEmail && str_ends_with($emailA, '@' . $domain));
    }

    /**
     * @param string $from
     * @param string $recipient
     * @param string $domainA
     * @param string $domainB
     * @return bool
     */
    private function matchesBothDomainRule(string $from, string $recipient, string $domainA, string $domainB): bool
    {
        $domainA = substr($domainA, 2); // Remove "*@" to get the domain
        $domainB = substr($domainB, 2); // Remove "*@" to get the domain

        return (str_ends_with($from, '@' . $domainA) && str_ends_with($recipient, '@' . $domainB)) ||
            (str_ends_with($recipient, '@' . $domainA) && str_ends_with($from, '@' . $domainB));
    }
}
