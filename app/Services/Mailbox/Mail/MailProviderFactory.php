<?php

namespace App\Services\Mailbox\Mail;

use App\Enums\Mailbox\MailProviderType;
use App\Services\Mailbox\Mail\Google\GoogleMailProvider;
use Exception;

class MailProviderFactory
{
    /**
     * Generates a instance of a MailProvider
     * @throws Exception
     */
    public static function make(): MailProvider
    {
        return match(config('services.mailbox.provider')) {
            MailProviderType::GOOGLE->value => app()->make(GoogleMailProvider::class),
            default => throw new Exception(__METHOD__.": Invalid mail provider")
        };
    }
}
