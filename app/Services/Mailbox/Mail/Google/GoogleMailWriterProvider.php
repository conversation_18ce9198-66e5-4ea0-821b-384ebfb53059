<?php

namespace App\Services\Mailbox\Mail\Google;

use App\DTO\Mail\AccessTokenResponse;
use App\DTO\Mail\EmailListenerResponse;
use App\DTO\Mail\SendEmailParam;
use App\Enums\Mailbox\MailProviderLabel;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\Mailbox\MailboxUserToken;
use App\Services\Mailbox\Mail\SmtpEmailMessage;
use Google\Service\Exception;
use Google\Service\Gmail\Message;
use Google\Service\Gmail\ModifyMessageRequest;
use Google_Service_Gmail_Message;
use Google_Service_Gmail_WatchRequest;
use Illuminate\Support\Carbon;


class GoogleMailWriterProvider
{
    const string USER_ID = 'me';

    public function __construct(
        protected GoogleMailClient $googleMailClient
    )
    {

    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param SendEmailParam $params
     * @return Message
     * @throws Exception
     */
    public function sendEmail(MailboxUserToken $mailboxUserToken, SendEmailParam $params): Message
    {
        $smtpMessage = new SmtpEmailMessage(
            $params->getFrom(),
            $params->getTo(),
            $params->getSubject(),
            $params->getContent(),
            $params->getCc(),
            $params->getBcc(),
            $params->getReferences(),
            $params->getReplyToMessageId(),
            $params->getThreadId(),
        );

        $msg = new Google_Service_Gmail_Message();
        $msg->setRaw(safeBase64Encode($smtpMessage->toString()));

        if (!empty($params->getThreadId())) {
            $msg->setThreadId($params->getThreadId());
        }

        return $this->googleMailClient
            ->getService($mailboxUserToken)
            ->users_messages
            ->send(self::USER_ID, $msg);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $email
     * @return EmailListenerResponse
     * @throws Exception
     */
    public function setupEmailListener(MailboxUserToken $mailboxUserToken, string $email): EmailListenerResponse
    {
        $watchRequest = new Google_Service_Gmail_WatchRequest();
        $watchRequest->setTopicName(config('services.google.gmail.email_listener_topic'));
        $watchRequest->setLabelFilterBehavior('exclude');
        $watchRequest->setLabelIds([MailProviderLabel::DRAFT->value]);

        $response = $this->googleMailClient->getService($mailboxUserToken)->users->watch($email, $watchRequest);

        return new EmailListenerResponse(Carbon::createFromTimestamp(+$response->getExpiration() / 1000));
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $email
     * @return void
     * @throws Exception
     */
    public function removeEmailListener(MailboxUserToken $mailboxUserToken, string $email): void
    {
        $this->googleMailClient->getService($mailboxUserToken)->users->stop($email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $code
     * @inheritDoc
     */
    public function getAccessToken(string $code): AccessTokenResponse
    {
        return $this->googleMailClient->getAccessToken($code);
    }

    /**
     * Star email
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function starEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setAddLabelIds([MailProviderLabel::STARRED->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unstarEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setRemoveLabelIds([MailProviderLabel::STARRED->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @param string $messageId
     * @param bool $flag
     * @inheritDoc
     */
    public function readEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setRemoveLabelIds([MailProviderLabel::UNREAD->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unreadEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setAddLabelIds([MailProviderLabel::UNREAD->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function archiveEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setRemoveLabelIds([MailProviderLabel::INBOX->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unarchiveEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setAddLabelIds([MailProviderLabel::INBOX->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }


    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function importantEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setAddLabelIds([MailProviderLabel::IMPORTANT->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unimportantEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        $modifyRequest = new ModifyMessageRequest();
        $modifyRequest->setRemoveLabelIds([MailProviderLabel::IMPORTANT->value]);

        return $this->modifyMessage($mailboxUserToken, $modifyRequest, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param ModifyMessageRequest $modifyRequest
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    private function modifyMessage(MailboxUserToken $mailboxUserToken, ModifyMessageRequest $modifyRequest, MailboxUserEmail $email): bool
    {
        $modifiedMessage = $this->googleMailClient
            ->getService($mailboxUserToken)
            ->users_messages
            ->modify(self::USER_ID, $email->{MailboxUserEmail::FIELD_EXTERNAL_ID}, $modifyRequest);

        return $modifiedMessage->getId() === $email->{MailboxUserEmail::FIELD_EXTERNAL_ID};
    }
}
