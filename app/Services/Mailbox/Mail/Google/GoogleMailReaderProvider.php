<?php

namespace App\Services\Mailbox\Mail\Google;

use App\DTO\Mail\Email;
use App\DTO\Mail\EmailFlags;
use App\DTO\Mail\EmailLabel;
use App\DTO\Mail\ListEmailQueryDTO;
use App\DTO\Mail\EmailMeta;
use App\DTO\Mail\EmailPart;
use App\DTO\Mail\SendAs;
use App\Enums\Mailbox\EmailPartMimeType;
use App\Enums\Mailbox\MailProviderLabel;
use App\Models\Mailbox\MailboxUserToken;
use App\Services\CloudStorage\GoogleCloudStorageService;
use App\Services\Mailbox\Mail\SmtpEmailMessage;
use Google\Service\Exception;
use Google\Service\Gmail;
use Google\Service\Gmail\Message;
use Google\Service\Gmail\MessagePart;
use Google\Service\Gmail\MessagePartBody;
use Google\Service\Gmail\SendAs as GmailSendAs;
use Illuminate\Support\Collection;
use Psr\Http\Message\RequestInterface;

/**
 * TODO - Move the parse code to a proper class
 */
class GoogleMailReaderProvider
{
    const string USER_ID = 'me';

    public function __construct(
        protected GoogleCloudStorageService $googleCloudStorageService,
        protected GoogleMailClient $googleMailClient
    )
    {

    }

    /**
     * @param Message $message
     * @return EmailMeta
     */
    private function getEmailMeta(Gmail\Message $message): EmailMeta
    {
        $meta = new EmailMeta();

        if (!$message->getPayload()) return $meta;

        foreach ($message->getPayload()->getHeaders() as $header) {
            if (empty($header->getValue())) continue;

            switch (strtolower($header->getName())) {
                case strtolower(SmtpEmailMessage::SMTP_FIELD_FROM):
                    $meta->setFrom($header->getValue());
                    break;
                case strtolower(SmtpEmailMessage::SMTP_FIELD_DATE):
                    $meta->setDate($header->getValue());
                    break;
                case strtolower(SmtpEmailMessage::SMTP_FIELD_TO):
                    $meta->setTo($header->getValue());
                    break;
                case strtolower(SmtpEmailMessage::SMTP_FIELD_SUBJECT):
                    $meta->setSubject($header->getValue());
                    break;
                case strtolower(SmtpEmailMessage::SMTP_FIELD_BCC):
                    $meta->setBcc($header->getValue());
                    break;
                case strtolower(SmtpEmailMessage::SMTP_FIELD_CC):
                    $meta->setCc($header->getValue());
                    break;
                case strtolower(SmtpEmailMessage::SMTP_FIELD_REFERENCES):
                    $meta->setReferences($header->getValue());
                    break;
                case strtolower(SmtpEmailMessage::SMTP_FIELD_MESSAGE_ID):
                    $meta->setMessageId($header->getValue());
                    break;
            }
        }

        return $meta;
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param Message $message
     * @param array $parts
     * @return array
     * @throws Exception
     */
    private function processParts(MailboxUserToken $mailboxUserToken, Gmail\Message $message, array $parts): array
    {
        $result = [];

        /** @var MessagePart $part */
        foreach ($parts as $part) {
            $body = $part->getBody();
            $size = $body->getSize();

            if ($size > 0) {
                $attachmentId = $body->getAttachmentId();
                $id = $part->getPartId();
                $mimeType = $part->getMimeType();
                $fileName = $part->getFilename();

                /** @var ?Gmail\MessagePartHeader $contentIdHeader */
                $contentIdHeader = collect($part->getHeaders())->first(function ($header) {
                    return strtolower($header->getName()) === strtolower(SmtpEmailMessage::SMTP_FIELD_CONTENT_ID);
                });

                // Extract the id from <FILE_ID>
                preg_match("/<(.*?)>/", $contentIdHeader?->getValue() ?? '', $matches);
                $contentId = $matches[1] ?? "";

                $partData = isset($attachmentId)
                    ? $this->getAttachment($mailboxUserToken, $message->getId(), $attachmentId)?->getData()
                    : $body->getData();

                $content = safeBase64Decode($partData ?? '');

                $result[] = new EmailPart(
                    $id,
                    $mimeType,
                    $content,
                    $fileName,
                    $attachmentId,
                    $size,
                    $contentId
                );
            }

            $result = array_merge($result, $this->processParts($mailboxUserToken, $message, $part->getParts()));
        }

        return $result;
    }

    /**
     * Return the main part of the email. It can be text or html
     * @param MailboxUserToken $mailboxUserToken
     * @param Message $message
     * @return array [EmailPart, EmailPart[]]
     * @throws Exception
     */
    private function getEmailParts(MailboxUserToken $mailboxUserToken, Gmail\Message $message): array
    {
        $payload = $message->getPayload();

        $parts = [];

        if ($payload?->getBody()?->getSize() > 0) {
            $parts[] = new EmailPart(
                null,
                $payload->getMimeType(),
                safeBase64Decode($payload->getBody()->getData()),
                null,
                null,
                $payload->getBody()->getSize()
            );
        }

        $parts = array_merge($parts, $this->processParts($mailboxUserToken, $message, $payload->getParts() ?? []));

        $emailContent = $this->getEmailMainPart($parts);

        $attachments = collect($parts)->filter(
            fn(EmailPart $emailPart) => !in_array(
                $emailPart->getMimeType(),
                [EmailPartMimeType::TEXT_PLAIN->value, EmailPartMimeType::TEXT_HTML->value]
            )
        )->values()->all();

        return [
            'content'     => $emailContent,
            'attachments' => $attachments
        ];
    }


    /**
     * Get the email main part
     * @param EmailPart[] $parts
     * @return EmailPart|null
     */
    private function getEmailMainPart(array $parts): ?EmailPart
    {
        $mainTypes = [
            EmailPartMimeType::TEXT_HTML,
            EmailPartMimeType::TEXT_PLAIN,
        ];

        foreach ($mainTypes as $mainType) {
            $emailPart = collect($parts)
                ->first(fn(EmailPart $emailPart) => $emailPart->getMimeType() === $mainType->value);

            if ($emailPart) return $emailPart;
        }

        return null;
    }

    /**
     * TODO - Split this code into small functions
     * @param MailboxUserToken $mailboxUserToken
     * @param array $messageIds
     * @return Email[]
     * @throws Exception
     */
    public function getEmailsInIds(MailboxUserToken $mailboxUserToken, array $messageIds): array
    {
        $size = 100; // MAX NUMBER OF EMAILS WE CAN READ PER BATCH
        $count = 0; //
        $res = [];

        // This code basically reduce the number of requests using the batch to avoid reaching rate limit
        // and retry the failed ones
        do {
            // I'm scared of infinite loops
            if ($count > 10) {
                logger()
                    ->channel('mailbox')
                    ->error('Infinite loop detected when retrieving emails');
                throw new Exception('Infinite loop detected');
            }

            $messageIdsChunks = collect($messageIds)->chunk($size);
            $failedIds = [];

            foreach ($messageIdsChunks as $ref => $messageIdsChuck) {
                // Start a batch request
                $batch = $this->googleMailClient->getService($mailboxUserToken, true)->createBatch();

                foreach ($messageIdsChuck as $id) {
                    /** @var RequestInterface $request */
                    $request = $this->getEmailMessage($mailboxUserToken, $id);
                    $batch->add($request);
                }

                $batchResponse = $batch->execute();

                foreach (array_values($batchResponse) as $idx => $item) {
                    // Calculates the message position in the batched array
                    $messagePosition = $idx + ($ref * $size);
                    $messageId = $messageIdsChuck[$messagePosition];

                    // TODO - Create a proper error handler
                    if ($item instanceof Exception && str_contains($item->getMessage(), 'rateLimitExceeded')) {
                        $failedIds[] = $messageId;
                        continue;
                    }

                    if ($item instanceof Exception) {
                        logger()
                            ->channel('mailbox')
                            ->error('Error when retrieving email message from provider ' . $item->getMessage() . ' email id ' . $messageId);
                        continue;
                    }

                    try {
                        $res[$item->getId()] = $this->parseMessageToEmail($mailboxUserToken, $item);
                    } catch (Exception $exception) {
                        logger()
                            ->channel('mailbox')
                            ->error('Error when parsing message to email ' . $exception->getMessage());
                    }
                }
            }

            $messageIds = $failedIds;
            $count += 1;
        } while (!empty($messageIds));

        return $res;
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param int $perPage
     * @param string|null $pageToken
     * @param ListEmailQueryDTO|null $query
     * @return array
     * @throws Exception
     */
    public function getEmails(
        MailboxUserToken $mailboxUserToken,
        int $perPage,
        ?string $pageToken = null,
        ?ListEmailQueryDTO $query = new ListEmailQueryDTO()
    ): array
    {
        [$messageIds, $nextPageToken] = $this->getEmailIds(
            mailboxUserToken: $mailboxUserToken,
            perPage         : $perPage,
            query           : $query,
            pageToken       : $pageToken
        );

        $emails = $this->getEmailsInIds($mailboxUserToken, $messageIds);

        return [$emails, $nextPageToken];
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param int $perPage
     * @param ListEmailQueryDTO $query
     * @param string|null $pageToken
     * @return array
     * @throws Exception
     */
    public function getEmailIds(
        MailboxUserToken $mailboxUserToken,
        int $perPage,
        ListEmailQueryDTO $query,
        ?string $pageToken = null,
    ): array
    {
        $service = $this->googleMailClient->getService($mailboxUserToken);

        $queryString = $query->toQueryString();

        $fullQueryString = collect([
            $queryString,
            '-label:draft'
        ])->filter()->implode(' AND ');

        $paginatedResponse = $service->users_messages
            ->listUsersMessages(self::USER_ID, [
                'maxResults'       => $perPage,
                'pageToken'        => $pageToken,
                'includeSpamTrash' => false,
                'q'                => $fullQueryString
            ]);

        $emailIds = array_map(fn(Message $message) => $message->getId(), $paginatedResponse->getMessages());

        return [
            $emailIds,
            $paginatedResponse->getNextPageToken()
        ];
    }

    /**
     * @param Message $message
     * @return EmailFlags
     */
    private function getEmailFlags(Message $message): EmailFlags
    {
        $labelIds = $message->getLabelIds();
        $emailFlags = new EmailFlags();

        $emailFlags->setIsRead(true);

        foreach ($labelIds as $labelId) {
            switch ($labelId) {
                case MailProviderLabel::INBOX->value:
                    $emailFlags->setIsInbox(true);
                    break;
                case MailProviderLabel::STARRED->value:
                    $emailFlags->setIsStarred(true);
                    break;
                case MailProviderLabel::IMPORTANT->value:
                    $emailFlags->setIsImportant(true);
                    break;
                case MailProviderLabel::SENT->value:
                    $emailFlags->setIsSent(true);
                    break;
                case MailProviderLabel::DRAFT->value:
                    $emailFlags->setIsDraft(true);
                    break;
                case MailProviderLabel::SPAM->value:
                    $emailFlags->setIsSpam(true);
                    break;
                case MailProviderLabel::TRASH->value:
                    $emailFlags->setIsTrash(true);
                    break;
                case MailProviderLabel::UNREAD->value:
                    $emailFlags->setIsRead(false);
                    break;
            }
        }

        $emailFlags->setIsArchived(!$emailFlags->getIsInbox());

        return $emailFlags;
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param Message $message
     * @return Email
     * @throws Exception
     */
    private function parseMessageToEmail(MailboxUserToken $mailboxUserToken, Message $message): Email
    {
        $email = new Email();

        $email->setId($message->getId());
        $email->setSnippet($message->getSnippet());
        $email->setThreadId($message->getThreadId());
        $email->setHistoryId($message->getHistoryId());

        $labelIds = $message->getLabelIds();
        $email->setLabelIds($labelIds);

        $emailFlags = $this->getEmailFlags($message);
        $email->setFlags($emailFlags);

        $meta = $this->getEmailMeta($message);
        $email->setMeta($meta);

        /** @var EmailPart $mainPart */
        ['content' => $mainPart, 'attachments' => $attachments] = $this->getEmailParts($mailboxUserToken, $message);

        // If email only contain not supported parts, create a new with the message below
        if (!isset($mainPart)) {
            $message = "We don't support this type of email yet. Please view it in Gmail. Contact support (<EMAIL>) for any questions";

            $mainPart = EmailPart::fromArray([
                EmailPart::FIELD_MIMETYPE => EmailPartMimeType::TEXT_PLAIN->value,
                EmailPart::FIELD_CONTENT  => $message,
                EmailPart::FIELD_SIZE     => strlen($message),
            ]);
        }

        $email->setContent($mainPart->getContent());

        $email->setAttachments($attachments);

        return $email;
    }


    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $messageId
     * @return Message
     * @throws Exception
     */
    public function getEmailMessage(MailboxUserToken $mailboxUserToken, string $messageId)
    {
        return $this->googleMailClient
            ->getService($mailboxUserToken, true)
            ->users_messages
            ->get(self::USER_ID, $messageId);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $messageId
     * @param string $attachmentId
     * @return MessagePartBody|null
     * @throws Exception
     */
    public function getAttachment(MailboxUserToken $mailboxUserToken, string $messageId, string $attachmentId): ?MessagePartBody
    {
        return $this->googleMailClient
            ->getService($mailboxUserToken)
            ->users_messages_attachments
            ->get(self::USER_ID, $messageId, $attachmentId);
    }


    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $latestHistoryId
     * @return Collection
     * @throws Exception
     */
    public function getMailboxChangesSinceHistoryId(MailboxUserToken $mailboxUserToken, string $latestHistoryId): Collection
    {
        $history = $this->googleMailClient->getService($mailboxUserToken)->users_history->listUsersHistory(self::USER_ID, ['startHistoryId' => $latestHistoryId]);

        $messages = collect();

        foreach ($history->getHistory() as $record) {
            $messageIds = collect($record->getMessages())
                ->filter(fn(Message $message) => $message?->getId())
                ->map(fn(Message $message) => $message?->getId())
                ->all();

            $messages = $messages->merge($messageIds)->filter()->unique();
        }

        return $messages;
    }

    /**
     * Get only user custom email labels
     * @param MailboxUserToken $mailboxUserToken
     * @return EmailLabel[]
     * @throws Exception
     */
    public function getUserCustomEmailLabels(MailboxUserToken $mailboxUserToken): array
    {
        $response = $this->googleMailClient
            ->getService($mailboxUserToken)
            ->users_labels
            ->listUsersLabels(self::USER_ID);

        return collect($response->getLabels())
            ->filter(fn(Gmail\Label $label) => $label->getType() === 'user')
            ->map(fn(Gmail\Label $label) => new EmailLabel($label->getId(), $label->getName()))
            ->all();
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $messageId
     * @return Email|null
     * @throws Exception
     */
    public function getEmail(MailboxUserToken $mailboxUserToken, string $messageId): ?Email
    {
        $emails = collect($this->getEmailsInIds($mailboxUserToken, [$messageId]));

        return $emails->first();
    }


    /**
     * @param MailboxUserToken $mailboxUserToken
     * @return Collection<SendAs>
     * @throws Exception
     */
    public function getSignatures(MailboxUserToken $mailboxUserToken): Collection
    {
        $response = $this->googleMailClient
            ->getService($mailboxUserToken)
            ->users_settings_sendAs
            ->listUsersSettingsSendAs(self::USER_ID);

        return collect($response->getSendAs())
            ->map(function (GmailSendAs $as) {
                return new SendAs(
                    displayName: $as->getDisplayName(),
                    signature  : $as->getSignature(),
                    sendAsEmail: $as->getSendAsEmail(),
                    isDefault  : $as->getIsDefault(),
                    isPrimary  : $as->getIsPrimary()
                );
            });
    }
}
