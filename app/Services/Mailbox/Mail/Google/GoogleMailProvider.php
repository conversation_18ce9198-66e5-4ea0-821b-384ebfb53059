<?php

namespace App\Services\Mailbox\Mail\Google;

use App\DTO\Mail\AccessTokenResponse;
use App\DTO\Mail\Email;
use App\DTO\Mail\EmailLabel;
use App\DTO\Mail\EmailListenerResponse;
use App\DTO\Mail\ListEmailQueryDTO;
use App\DTO\Mail\SendAs;
use App\DTO\Mail\SendEmailParam;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\Mailbox\MailboxUserToken;
use App\Services\CloudStorage\GoogleCloudStorageService;
use App\Services\Mailbox\Mail\MailProvider;
use Google\Service\Exception;
use Illuminate\Support\Collection;


class GoogleMailProvider extends MailProvider
{
    public function __construct(
        protected GoogleCloudStorageService $googleCloudStorageService,
        protected GoogleMailClient $googleMailClient,
        protected GoogleMailWriterProvider $googleMailWriterProvider,
        protected GoogleMailReaderProvider $googleMailReaderProvider,
    )
    {

    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param SendEmailParam $params
     * @inheritDoc
     */
    public function sendEmail(MailboxUserToken $mailboxUserToken, SendEmailParam $params): Email
    {
        $message = $this->googleMailWriterProvider->sendEmail($mailboxUserToken, $params);

        // Ensure we have the smtp message id
        return $this->googleMailReaderProvider->getEmail($mailboxUserToken, $message->getId());
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param array $messageIds
     * @return Email[]
     * @throws Exception
     */
    public function getEmailsInIds(MailboxUserToken $mailboxUserToken, array $messageIds): array
    {
        return $this->googleMailReaderProvider->getEmailsInIds($mailboxUserToken, $messageIds);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param int $perPage
     * @param string|null $pageToken
     * @param string|null $query
     * @param bool|null $emailIdsOnly
     * @inheritDoc
     */
    public function getEmails(
        MailboxUserToken $mailboxUserToken,
        int $perPage,
        ListEmailQueryDTO $query,
        ?string $pageToken = null,
    ): array
    {
        return $this->googleMailReaderProvider->getEmails(
            mailboxUserToken: $mailboxUserToken,
            perPage         : $perPage,
            pageToken       : $pageToken,
            query           : $query,
        );
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param int $perPage
     * @param ListEmailQueryDTO $query
     * @param string|null $pageToken
     * @return array
     * @throws Exception
     */
    public function getEmailIds(
        MailboxUserToken $mailboxUserToken,
        int $perPage,
        ListEmailQueryDTO $query,
        ?string $pageToken = null,
    ): array
    {
        return $this->googleMailReaderProvider->getEmailIds(
            mailboxUserToken: $mailboxUserToken,
            perPage         : $perPage,
            query           : $query,
            pageToken       : $pageToken,
        );
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $email
     * @return EmailListenerResponse
     * @throws Exception
     */
    public function setupEmailListener(MailboxUserToken $mailboxUserToken, string $email): EmailListenerResponse
    {
        return $this->googleMailWriterProvider->setupEmailListener(
            mailboxUserToken: $mailboxUserToken,
            email           : $email,
        );
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $email
     * @return void
     * @throws Exception
     */
    public function removeEmailListener(MailboxUserToken $mailboxUserToken, string $email): void
    {
        $this->googleMailWriterProvider->removeEmailListener(
            mailboxUserToken: $mailboxUserToken,
            email           : $email,
        );
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $latestHistoryId
     * @return Collection
     * @throws Exception
     */
    public function getMailboxChangesSinceHistoryId(MailboxUserToken $mailboxUserToken, string $latestHistoryId): Collection
    {
        return $this->googleMailReaderProvider->getMailboxChangesSinceHistoryId(
            mailboxUserToken: $mailboxUserToken,
            latestHistoryId : $latestHistoryId,
        );
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $code
     * @inheritDoc
     */
    public function getAccessToken(string $code): AccessTokenResponse
    {
        return $this->googleMailClient->getAccessToken($code);
    }

    /**
     * @param int $userId
     * @param array $params
     * @return string
     */
    public function generateOAuthConsentScreenUrl(int $userId, array $params = []): string
    {
        $redirectURI = $this->getApplicationsRedirectUrlForOAuthCallback();

        return $this->googleMailClient
            ->getConsentScreenClient($redirectURI, $userId)
            ->createAuthUrl();
    }

    /**
     * Star email
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function starEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->starEmail(
            mailboxUserToken: $mailboxUserToken,
            email           : $email,
        );
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unstarEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->unstarEmail($mailboxUserToken, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @param string $messageId
     * @param bool $flag
     * @inheritDoc
     */
    public function readEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->readEmail($mailboxUserToken, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unreadEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->unreadEmail($mailboxUserToken, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function archiveEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->archiveEmail($mailboxUserToken, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unarchiveEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->unarchiveEmail($mailboxUserToken, $email);
    }


    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function importantEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->importantEmail($mailboxUserToken, $email);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function unimportantEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->googleMailWriterProvider->unimportantEmail($mailboxUserToken, $email);
    }

    /**
     * Get only user custom email labels
     * @param MailboxUserToken $mailboxUserToken
     * @return EmailLabel[]
     * @throws Exception
     */
    public function getUserCustomEmailLabels(MailboxUserToken $mailboxUserToken): array
    {
        return $this->googleMailReaderProvider->getUserCustomEmailLabels($mailboxUserToken);
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $messageId
     * @return Email|null
     * @throws Exception
     */
    public function getEmail(MailboxUserToken $mailboxUserToken, string $messageId): ?Email
    {
        return $this->googleMailReaderProvider->getEmail(
            mailboxUserToken: $mailboxUserToken,
            messageId       : $messageId,
        );
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @return Collection<SendAs>
     * @throws Exception
     */
    public function getEmailSignatures(MailboxUserToken $mailboxUserToken): Collection
    {
        return $this->googleMailReaderProvider->getSignatures(
            mailboxUserToken: $mailboxUserToken,
        );
    }
}
