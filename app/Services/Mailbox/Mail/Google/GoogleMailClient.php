<?php

namespace App\Services\Mailbox\Mail\Google;

use App\DTO\Mail\AccessTokenResponse;
use App\Models\Mailbox\MailboxUserToken;
use Google\Client;
use Google\Service\Gmail;
use Google_Service_Gmail;


class GoogleMailClient
{
    /**
     * @param bool $batch
     * @return Client
     */
    private function initBaseClient(bool $batch = false): Client
    {
        $client = new Client();
        $client->setApplicationName(config('services.google.gmail.application_name'));
        $client->setClientId(config('services.google.gmail.client_id'));
        $client->setClientSecret(config('services.google.gmail.client_secret'));
        $client->setRedirectUri(url('/api/webhooks/mail/oauth'));
        $client->setUseBatch($batch);
        $client->setScopes([Gmail::MAIL_GOOGLE_COM]);
        return $client;
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param bool $batch
     * @return Google_Service_Gmail
     * @throws \Exception
     */
    public function getService(MailboxUserToken $mailboxUserToken, bool $batch = false): Google_Service_Gmail
    {
        return new Google_Service_Gmail($this->initClient($mailboxUserToken, $batch));
    }

    /**
     * @param MailboxUserToken $accessToken
     * @param bool $batch
     * @return Client
     * @throws \Exception
     */
    public function initClient(MailboxUserToken $accessToken, bool $batch = false): Client
    {
        $client = $this->initBaseClient($batch);

        $client->setAccessToken($accessToken->{MailboxUserToken::FIELD_TOKEN});

        if (\Carbon\Carbon::parse($accessToken->{MailboxUserToken::FIELD_CREATED_AT})->addHour() < now()) {
            $token = $client->fetchAccessTokenWithRefreshToken($accessToken->{MailboxUserToken::FIELD_REFRESH_TOKEN});

            if (isset($token['error'])) {
                throw new \Exception('Error to get access token for the user token ' . $accessToken->{MailboxUserToken::FIELD_ID} . ' - ' . $token['error_description']);
            }

            MailboxUserToken::query()
                ->where(MailboxUserToken::FIELD_USER_ID, $accessToken->{MailboxUserToken::FIELD_USER_ID})
                ->delete();

            MailboxUserToken::query()->create(
                [
                    MailboxUserToken::FIELD_USER_ID => $accessToken->{MailboxUserToken::FIELD_USER_ID},
                    MailboxUserToken::FIELD_TOKEN => $token['access_token'],
                    MailboxUserToken::FIELD_REFRESH_TOKEN => $token['refresh_token'],
                ]
            );

            $client->setAccessToken($token['access_token']);
        }

        return $client;
    }

    /**
     * @param string $redirectURI
     * @param int $userId
     * @return Client
     */
    public function getConsentScreenClient(string $redirectURI, int $userId): Client
    {
        $client = $this->initBaseClient();

        $client->setRedirectUri($redirectURI);
        $client->setAccessType('offline');
        $client->setPrompt('consent');
        $client->setState(safeBase64Encode(json_encode(['userId' => $userId])));

        return $client;
    }

    /**
     * @param string $code
     * @return AccessTokenResponse
     */
    public function getAccessToken(string $code): AccessTokenResponse
    {
        $client = $this->initBaseClient();
        $client->getRefreshToken();
        $client->fetchAccessTokenWithAuthCode($code);

        $token = $client->getAccessToken();

        return new AccessTokenResponse(
            $token['access_token'],
            $token['refresh_token'],
        );
    }
}
