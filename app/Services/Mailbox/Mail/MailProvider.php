<?php

namespace App\Services\Mailbox\Mail;

use App\DTO\Mail\AccessTokenResponse;
use App\DTO\Mail\Email;
use App\DTO\Mail\EmailLabel;
use App\DTO\Mail\EmailListenerResponse;
use App\DTO\Mail\ListEmailQueryDTO;
use App\DTO\Mail\SendAs;
use App\DTO\Mail\SendEmailParam;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\Mailbox\MailboxUserToken;
use Exception;
use Illuminate\Support\Collection;

abstract class MailProvider
{
    /**
     * @param string $code
     * @return AccessTokenResponse
     */
    public abstract function getAccessToken(string $code): AccessTokenResponse;

    /**
     * Make email as read
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function readEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function unreadEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function starEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function unstarEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;


    /**
     * Archive email
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function archiveEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;

    /**
     * Archive email
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function unarchiveEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function importantEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public abstract function unimportantEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool;

    /**
     * Archive an email
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $email
     * @return bool
     * @throws Exception
     */
    public function deleteEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $email): bool
    {
        return $this->archiveEmail($mailboxUserToken, $email);
    }

    /**
     * Send email
     * @param MailboxUserToken $mailboxUserToken
     * @param SendEmailParam $params
     * @return Email
     * @throws Exception
     */
    public abstract function sendEmail(MailboxUserToken $mailboxUserToken, SendEmailParam $params): Email;

    /**
     * Reply an email
     * @param MailboxUserToken $mailboxUserToken
     * @param MailboxUserEmail $replyToEmail
     * @param SendEmailParam $params
     * @return Email
     * @throws Exception
     */
    public function replyEmail(MailboxUserToken $mailboxUserToken, MailboxUserEmail $replyToEmail, SendEmailParam $params): Email
    {
        $email = $this->getEmail($mailboxUserToken, $replyToEmail->{MailboxUserEmail::FIELD_EXTERNAL_ID});

        $params->setReferences($replyToEmail->{MailboxUserEmail::FIELD_EXTERNAL_REFERENCES});
        $params->setSubject($email->getMeta()->getSubject());
        $params->setThreadId($replyToEmail->{MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID});
        $params->setReplyToMessageId($replyToEmail->{MailboxUserEmail::RELATION_EMAIL}->{MailboxEmail::FIELD_EXTERNAL_MESSAGE_ID});

        return $this->sendEmail($mailboxUserToken, $params);
    }

    /**
     * Get paginated emails
     * @param MailboxUserToken $mailboxUserToken
     * @param int $perPage
     * @param ListEmailQueryDTO $query
     * @param string|null $pageToken
     * @return array<Email[], string|null>
     * @throws Exception
     */
    public abstract function getEmails(
        MailboxUserToken $mailboxUserToken,
        int $perPage,
        ListEmailQueryDTO $query,
        ?string $pageToken = null,
    ): array;

    /**
     * Get paginated email ids
     * @param MailboxUserToken $mailboxUserToken
     * @param int $perPage
     * @param ListEmailQueryDTO $query
     * @param string|null $pageToken
     * @return array<Email[], string|null>
     * @throws Exception
     */
    public abstract function getEmailIds(
        MailboxUserToken $mailboxUserToken,
        int $perPage,
        ListEmailQueryDTO $query,
        ?string $pageToken = null,
    ): array;

    /**
     * Get email by id
     * @param MailboxUserToken $mailboxUserToken
     * @param string $messageId
     * @return Email|null
     * @throws Exception
     */
    public abstract function getEmail(MailboxUserToken $mailboxUserToken, string $messageId): ?Email;

    /**
     * Set up listener for new emails
     * @param MailboxUserToken $mailboxUserToken
     * @param string $email
     * @return EmailListenerResponse
     */
    public abstract function setupEmailListener(MailboxUserToken $mailboxUserToken, string $email): EmailListenerResponse;


    /**
     * @param MailboxUserToken $mailboxUserToken
     * @param string $email
     * @return void
     */
    public abstract function removeEmailListener(MailboxUserToken $mailboxUserToken, string $email): void;

    /**
     * Get all emails from since last history id
     * @param MailboxUserToken $mailboxUserToken
     * @param string $latestHistoryId
     * @return Collection [EmailDTO[], int]
     */
    public abstract function getMailboxChangesSinceHistoryId(
        MailboxUserToken $mailboxUserToken,
        string $latestHistoryId
    ): Collection;

    /**
     * Get only user custom email labels
     * @param MailboxUserToken $mailboxUserToken
     * @return EmailLabel[]
     */
    public abstract function getUserCustomEmailLabels(MailboxUserToken $mailboxUserToken): array;

    /**
     * @param int $userId
     * @param array $params
     * @return string
     */
    public abstract function generateOAuthConsentScreenUrl(int $userId, array $params = []): string;

    /**
     * Get application's redirect url for OAuth callback
     * @return string
     */
    public function getApplicationsRedirectUrlForOAuthCallback(): string
    {
        return url('/api/webhooks/mail/oauth');
    }

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @return Collection
     */
    abstract public function getEmailSignatures(MailboxUserToken $mailboxUserToken): Collection;

    /**
     * @param MailboxUserToken $mailboxUserToken
     * @return SendAs|null
     */
    public function getPrimaryEmailSignature(MailboxUserToken $mailboxUserToken): ?SendAs
    {
        $items = $this->getEmailSignatures(
            mailboxUserToken: $mailboxUserToken,
        );

        return $items->first(fn(SendAs $as) => $as->getIsPrimary());
    }
}
