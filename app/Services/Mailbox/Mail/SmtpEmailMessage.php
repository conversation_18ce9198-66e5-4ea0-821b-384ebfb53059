<?php

namespace App\Services\Mailbox\Mail;

use App\Enums\Mailbox\EmailPartMimeType;

class SmtpEmailMessage
{
    const SMTP_FIELD_FROM                      = 'From';
    const SMTP_FIELD_DATE                      = 'Date';
    const SMTP_FIELD_TO                        = 'To';
    const SMTP_FIELD_SUBJECT                   = 'Subject';
    const SMTP_FIELD_BCC                       = 'Bcc';
    const SMTP_FIELD_CC                        = 'Cc';
    const SMTP_FIELD_MIME_VERSION              = 'MIME-Version';
    const SMTP_FIELD_REFERENCES                = 'References';
    const SMTP_FIELD_MESSAGE_ID                = 'Message-ID';
    const SMTP_FIELD_CONTENT_ID                = 'Content-ID';
    const SMTP_FIELD_IN_REPLY_TO               = 'In-Reply-To';
    const SMTP_FIELD_CONTENT_TYPE              = 'Content-Type';
    const SMTP_FIELD_CONTENT_TRANSFER_ENCODING = 'Content-Transfer-Encoding';


    const HTML_ATTRIBUTE_SUFFIX = "3D";


    public function __construct(
        protected string $from,
        protected array $to,
        protected string $subject,
        protected string $content,
        protected ?array $cc = [],
        protected ?array $bcc = [],
        protected ?string $references = null,
        protected ?string $replyToMessageId = null,
        protected ?string $threadId = null,
        protected ?EmailPartMimeType $contentType = EmailPartMimeType::TEXT_HTML
    )
    {

    }

    /**
     * Place a suffix before every attribute to force providers render html emails correctly
     * @param string $html
     * @return string
     */
    function prepareHtmlForSending(string $html): string
    {
        $pattern = '/=("[^"]*")/';
        $replacement = "=" . self::HTML_ATTRIBUTE_SUFFIX . "$1";
        return preg_replace($pattern, $replacement, $html);
    }

    private function getFromSMTPString(): string
    {
        return self::SMTP_FIELD_FROM . ": <" . $this->from . ">";
    }

    private function getToSmtpString(): string
    {
        return self::SMTP_FIELD_TO . ": " . implode(',', $this->to);
    }

    private function getCcSmtpString(): string
    {
        return !empty($this->cc)
            ? self::SMTP_FIELD_CC . ": " . implode(',', $this->cc)
            : "";
    }

    private function getBccSmtpString(): string
    {
        return !empty($this->bcc)
            ? self::SMTP_FIELD_BCC . ": " . implode(',', $this->bcc)
            : "";
    }

    private function getReferenceSmtpString(): string
    {
        return !empty($this->references)
            ? self::SMTP_FIELD_REFERENCES . ": " . $this->references
            : "";
    }

    private function getReplyToSmtpString(): string
    {
        return !empty($this->replyToMessageId)
            ? self::SMTP_FIELD_IN_REPLY_TO . ": " . $this->replyToMessageId
            : "";
    }

    private function getSubjectSmtpString(): string
    {
        return self::SMTP_FIELD_SUBJECT . ": " . mb_encode_mimeheader($this->subject, 'UTF-8');
    }

    private function getContent(): string
    {
        return $this->contentType === EmailPartMimeType::TEXT_HTML ? $this->prepareHtmlForSending($this->content) : $this->content;
    }

    private function getMimeVersionSmtpString(): string
    {
        return self::SMTP_FIELD_MIME_VERSION . ": 1.0";
    }

    private function getContentTypeSmtpString(): string
    {
        return self::SMTP_FIELD_CONTENT_TYPE . ": " . $this->contentType->value . "; charset=utf-8";
    }

    private function getContentTransferEncodingSmtpString(): string
    {
        return self::SMTP_FIELD_CONTENT_TRANSFER_ENCODING . ": quoted-printable";
    }

    public function toString(): string
    {
        $strRawMessage = collect([
            $this->getFromSmtpString(),
            $this->getToSmtpString(),
            $this->getCcSmtpString(),
            $this->getBccSmtpString(),
            $this->getReferenceSmtpString(),
            $this->getReplyToSmtpString(),
            $this->getSubjectSmtpString(),
            $this->getMimeVersionSmtpString(),
            $this->getContentTypeSmtpString(),
            $this->getContentTransferEncodingSmtpString(),
        ])->filter(fn(string $str) => !empty($str))
            ->join("\r\n");

        $strRawMessage .= "\r\n\r\n" . $this->getContent();

        return $strRawMessage;
    }
}
