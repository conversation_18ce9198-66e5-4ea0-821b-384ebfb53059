<?php

namespace App\Services;

use App\Models\Legacy\EloquentQuote;

class LeadQualificationService
{
    const SALES_BAIT_ELECTRIC_COST_MINIMUM = 200; // $
    const SALES_BAIT_ROOF_AREA_MINIMUM     = 3000; // sq ft
    const VERIFIED_VIA_CONVERSATION        = 'Qualified via conversation';

    /**
     * @param EloquentQuote $lead
     * @param string $processingScenario
     * @return bool
     */
    public function qualifyUndersoldLeadForSalesBait(EloquentQuote $lead, string $processingScenario): bool
    {
        return match ($lead->getLeadIndustry()->key) {
            EloquentQuote::LEAD_INDUSTRY_SOLAR => $this->qualifyUndersoldSolarForSalesBait($lead, $processingScenario),
            EloquentQuote::LEAD_INDUSTRY_ROOFING => $this->qualifyUndersoldRoofingForSalesBait($lead)
        };
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function qualifyUnsoldLeadForSalesBait(EloquentQuote $lead): bool
    {
        return match ($lead->getLeadIndustry()->key) {
            EloquentQuote::LEAD_INDUSTRY_SOLAR => $this->qualifyUnsoldSolarForSalesBait($lead),
            EloquentQuote::LEAD_INDUSTRY_ROOFING => $this->qualifyUnsoldRoofingForSalesBait($lead)
        };
    }

    /**
     * @param EloquentQuote $lead
     * @param string $processingScenario
     * @return bool
     */
    protected function qualifyUndersoldSolarForSalesBait(EloquentQuote $lead, string $processingScenario): bool
    {
        return $this->electricCostQualifiedForSalesBait($lead) &&
               $this->hasBestTimeToCallOrAppointment($lead) &&
               $processingScenario === self::VERIFIED_VIA_CONVERSATION;
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    protected function qualifyUndersoldRoofingForSalesBait(EloquentQuote $lead): bool
    {
        return $this->hasBestTimeToCallOrAppointment($lead) &&
               $this->roofAreaQualifiedForSalesBait($lead);
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    protected function qualifyUnsoldSolarForSalesBait(EloquentQuote $lead): bool
    {
        return $this->electricCostQualifiedForSalesBait($lead) &&
               $this->hasBestTimeToCallOrAppointment($lead);
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    protected function qualifyUnsoldRoofingForSalesBait(EloquentQuote $lead): bool
    {
        return $this->hasBestTimeToCallOrAppointment($lead) &&
               $this->roofAreaQualifiedForSalesBait($lead);
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    protected function electricCostQualifiedForSalesBait(EloquentQuote $lead): bool
    {
        return $lead->electriccost >= self::SALES_BAIT_ELECTRIC_COST_MINIMUM;
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function hasBestTimeToCallOrAppointment(EloquentQuote $lead): bool
    {
        return !empty($lead->besttimetocall) ||
               !empty($lead->besttimetocallother);
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    private function roofAreaQualifiedForSalesBait(EloquentQuote $lead): bool
    {
        return $lead?->roofDetails?->roof_replacement_area > self::SALES_BAIT_ROOF_AREA_MINIMUM;
    }
}
