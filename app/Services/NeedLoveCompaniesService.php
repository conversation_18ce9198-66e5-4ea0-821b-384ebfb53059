<?php

namespace App\Services;

use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class NeedLoveCompaniesService
{

    /**
     * @param User $user
     * @param int $companyCount
     * @return Collection
     */
    public static function getCompaniesNeedingTouchByUser(User $user, int $companyCount = 10): Collection
    {
        return Company::query()
            ->select([
                'companies.id',
                'companies.name',
                'company_user_relationships.user_id',
                DB::raw('MAX(activity_feeds.created_at) as last_activity'),
                DB::raw('MIN(tasks.available_at) as next_task_date'),
                DB::raw('DATE_ADD(company_user_relationships.created_at, INTERVAL 42 DAY) as probation_end_date')
            ])
            ->join('company_user_relationships', function ($join) use ($user) {
                $join->on('company_user_relationships.company_id', '=', 'companies.id')
                    ->where('company_user_relationships.user_id', '=', $user->id)
                    ->whereNull('company_user_relationships.deleted_at');
            })
            ->join('roles', function ($join) {
                $join->on('roles.id', '=', 'company_user_relationships.role_id')
                    ->where('roles.name', '=', 'business-development-manager');
            })
            ->join('activity_feeds', function ($join) {
                $join->on('activity_feeds.company_id', '=', 'companies.id')
                    ->whereIn('activity_feeds.item_type', ['text', 'call', 'email']);
            })
            ->leftJoin('tasks', function ($join) {
                $join->on('tasks.manual_company_id', '=', 'companies.id')
                    ->where('tasks.completed', '=', false);
            })
            ->groupBy('companies.id', 'companies.name')
            ->having('last_activity', '<', now()->subWeeks(2))
            ->orderBy('last_activity')
            ->limit($companyCount)
            ->get();
    }

}
