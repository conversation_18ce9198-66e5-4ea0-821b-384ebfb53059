<?php

namespace App\Services;

use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class TinymceFileUploadService
{
    public function __construct(protected GoogleCloudStorageService $googleCloudStorageService) {}

    /**
     * @param UploadedFile $file
     *
     * @return string
     * @throws Exception
     */
    public function upload(UploadedFile $file): string
    {
        $storagePath = 'tinymce_files/' . date('Y') . '/' . date('M');
        $disk        = config('services.tiny_mce.storage_disk');

        if ($disk === 'cloud') return $this->uploadInCloud($storagePath, $file);

        return $this->uploadInDisk($disk, $storagePath, $file);
    }

    /**
     * @param string $disk
     * @param string $storagePath
     * @param UploadedFile $file
     *
     * @return string
     */
    protected function uploadInDisk(string $disk, string $storagePath, UploadedFile $file): string
    {
        $path = $file->store($storagePath, $disk);

        return Storage::disk($disk)->url($path);
    }

    /**
     * @param string $storagePath
     * @param UploadedFile $file
     *
     * @return string
     * @throws Exception
     */
    protected function uploadInCloud(string $storagePath, UploadedFile $file): string
    {
        $path   = $file->store($storagePath, 'local');
        $bucket = config('services.google.storage.buckets.tinymce_files');
        $url    = config('services.google.storage.urls.tinymce_file_url');

        $this->googleCloudStorageService->setCurrentBucket($bucket);
        $this->googleCloudStorageService->upload($path, fopen(storage_path("app/{$path}"), 'r'));

        return rtrim($url, '/') . "/{$bucket}/$path";
    }
}
