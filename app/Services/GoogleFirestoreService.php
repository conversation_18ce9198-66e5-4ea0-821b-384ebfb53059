<?php

namespace App\Services;

use Google\Cloud\Firestore\CollectionReference;
use Google\Cloud\Firestore\DocumentSnapshot;
use Google\Cloud\Firestore\FirestoreClient;
use Google\Cloud\Core\Exception\GoogleException;
use Google\Cloud\Firestore\QuerySnapshot;
use Illuminate\Support\Collection;

class GoogleFirestoreService
{
    public ?FirestoreClient $db = null;

    /**
     * @throws GoogleException
     */
    public function __construct()
    {
        $this->db = $this->makeFirestoreClient();
    }

    /**
     * @return FirestoreClient
     * @throws GoogleException
     */
    private function makeFirestoreClient(): FirestoreClient
    {
        $projectId = config("services.google.firestore.project_id");
        $keyFile = json_decode(base64_decode(config("services.google.firestore.key_file")), true);

        return new FirestoreClient([
            'projectId' => $projectId,
            'keyFile'   => $keyFile,
        ]);
    }

    /**
     * Formats the collection so it's using the base prefix.
     *
     * @param string $collection
     * @return string
     */
    protected function formatCollection(string $collection): string
    {
        return config('services.google.firestore.collection_prefix') . '/' . ltrim($collection, '/');
    }

    /**
     * Get a single document by collection reference and document id
     * @param string $documentName
     * @param string $collectionReference
     * @return array|null
     */
    public function getDocumentByReference(string $collectionReference, string $documentName): ?array
    {
        $documentSnapshot = $this->db->collection($this->formatCollection($collectionReference))->document($documentName)->snapshot();
        return $documentSnapshot->exists()
            ? [
                'id'    => $documentSnapshot->id(),
                ...$documentSnapshot->data(),
            ]
            : null;
    }

    /**
     * Retrieve a Document along with keyed SubCollections
     *
     * @param string $collectionReference
     * @param string $documentName
     * @param array $subCollectionKeys
     * @return array|null
     */
    public function getDocumentByReferenceWithSubCollections(string $collectionReference, string $documentName, array $subCollectionKeys): ?array
    {
        $baseDocumentRef = $this->db->collection($this->formatCollection($collectionReference))->document($documentName);
        $output = $baseDocumentRef->snapshot()->data();

        foreach ($subCollectionKeys as $key) {
            $subCollection = $baseDocumentRef->collection($key)->documents();
            if ($subCollection) {
                $output[$key] = [];
                foreach($subCollection as $document) {
                    $output[$key][] = $document->data();
                }
            }
        }

        return $output;
    }

    /**
     * Get all documents nested with the same Collection id
     * @param string $groupName
     * @return Collection
     */
    public function getDocumentsByCollectionGroup(string $groupName): Collection
    {
        # $groupDocuments = $this->db->collectionGroup($this->formatCollection($groupName))->documents();
        $groupDocuments = $this->db->collectionGroup($groupName)->documents();
        return $this->makeLaravelCollection($groupDocuments);
    }

    /**
     * @param string $collectionPath
     * @param bool|null $keyById
     * @param string|null $paginateFrom
     * @param int|null $paginateLimit
     * @return Collection
     */
    public function getDocumentsByCollection(string $collectionPath, ?bool $keyById = false, ?string $paginateFrom = null, ?int $paginateLimit = null): Collection
    {
        $collectionReference = $this->db->collection($this->formatCollection($collectionPath));
        if ($paginateLimit) {
            if ($paginateFrom) {
                $documentReference = $this->db->collection($this->formatCollection($collectionPath))->document($paginateFrom)->snapshot();
                if ($documentReference->exists()) {
                    $collectionReference->startAfter($documentReference);
                }
            }
            $collectionReference->limit($paginateLimit);
        }

        return $this->makeLaravelCollection($collectionReference->documents(), $keyById);
    }

    /**
     * @param string $collectionPath
     * @param string $documentId
     * @param array $documentData
     * @param bool $allowOverwrite
     * @return bool
     */
    public function addDocumentToCollection(string $collectionPath, string $documentId, array $documentData, bool $allowOverwrite = false): bool
    {
        $documentRef = $this->db->collection($this->formatCollection($collectionPath))->document($documentId);
        if ($documentRef->snapshot()->exists() && !$allowOverwrite) {
            logger()->warning("Tried to create Firebase document $collectionPath/$documentId, but it already exists");
            return false;
        }

        $result = $documentRef->set($documentData);

        return !!$result['updateTime'];
    }

    /**
     * Provide key value pairs to update a Document with. This method supports dot notation for updating nested keys.
     * @param string $collectionPath
     * @param string $documentId
     * @param array $documentData
     * @return bool
     */
    public function updateDocument(string $collectionPath, string $documentId, array $documentData): bool
    {
        $documentRef = $this->db->collection($this->formatCollection($collectionPath))->document($documentId);
        if (!$documentRef->snapshot()->exists()) {
            logger()->warning("Tried to update Firebase document $collectionPath/$documentId, but it does not exist");
            return false;
        }

        $pathData = array_map(function($key, $value) {
            return ['path' => $key, 'value' => $value];
        }, array_keys($documentData), $documentData);

        $result = $documentRef->update($pathData);

        return !!$result['updateTime'];
    }

    /**
     * @param string $groupName
     * @param bool|null $returnQuerySnapshot
     * @return Collection|QuerySnapshot
     */
    public function getCollectionGroup(string $groupName, ?bool $returnQuerySnapshot = false): Collection | QuerySnapshot
    {
        # $querySnapshot = $this->db->collectionGroup($this->formatCollection($groupName));
        $querySnapshot = $this->db->collectionGroup($groupName);

        return $returnQuerySnapshot
            ? $querySnapshot->documents()
            : $this->makeLaravelCollection($querySnapshot->documents());
    }

    /**
     * @param string $collectionPath
     * @param string $documentId
     * @return bool
     */
    public function deleteDocument(string $collectionPath, string $documentId): bool
    {
        $documentRef = $this->db->collection($this->formatCollection($collectionPath))->document($documentId);
        if (!$documentRef->snapshot()->exists()) {
            logger()->warning("Tried to delete Firebase document $collectionPath/$documentId, but it doesn't exist");
            return false;
        }

        $result = $documentRef->delete();

        return gettype($result) === 'array';
    }

    /**
     * @param string $collectionPath
     * @return bool
     */
    public function deleteCollection(string $collectionPath): bool
    {
        $documents = $this->db->collection($this->formatCollection($collectionPath))->documents();

        /** @var DocumentSnapshot $document */
        foreach ($documents as $document) {
            if ($document->exists()) {
                if (gettype($document->reference()->delete()) !== 'array') {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * @param QuerySnapshot $data
     * @param string $regex
     * @param bool|null $keyById
     * @param array|null $filterByKeyValues
     * @param Collection|null $addToCollection
     * @return Collection
     */
    public function groupLaravelCollectionByPathRegex(QuerySnapshot $data, string $regex, ?bool $keyById = false, ?array $filterByKeyValues = [], ?Collection $addToCollection = null): Collection
    {
        $output = $addToCollection ?? collect();

        /** @var DocumentSnapshot $document */
        foreach ($data as $document) {
            if (!$document->exists()) continue;

            if ($filterByKeyValues) {
                $reject = false;
                foreach ($filterByKeyValues as $key => $value) {
                    if (($document[$key] ?? null) !== $value) {
                        $reject = true;
                        break;
                    }
                }

                if ($reject) continue;
            }

            preg_match($regex, $document->path(), $matches);
            $parentFlow = $matches[1] ?? false;

            if (!$parentFlow) continue;
            if (!$output->has($parentFlow)) $output->put($parentFlow, collect());

            /** @var Collection $targetGroup */
            $targetGroup = $output->get($parentFlow);
            if ($keyById) {
                $targetGroup->put($document->id(), $document->data());
            }
            else {
                $targetGroup->push([
                    'id' => $document->id(),
                    ...$document->data(),
                ]);
            }
        }

        return $output;
    }

    /**
     * Transform a Firestore query snapshot to Laravel Collection
     * @param QuerySnapshot $data
     * @param ?bool $keyById
     * @return Collection
     */
    private function makeLaravelCollection(QuerySnapshot $data, ?bool $keyById = false): Collection
    {
        $output = collect();

        /** @var DocumentSnapshot $document */
        foreach ($data as $document) {
            if (!$document->exists()) continue;

            if ($keyById) {
                $output->put($document->id(), $document->data());
            }
            else {
                $output->push([
                    'id' => $document->id(),
                    ...$document->data(),
                ]);
            }
        }

        return $output;
    }

    /**
     * Update all documents in a collection. Overwrites existing documents.
     * Providing $filterByIds array will delete any documents whose ID is NOT in the array
     *
     * @param string $collectionPath
     * @param array $documents
     * @param array|null $filterByIds
     * @return bool
     */
    public function updateCollection(string $collectionPath, array $documents, ?array $filterByIds = null): bool
    {
        if (!count($documents)) return true;

        $batch = $this->db->batch();

        $collection = $this->db->collection($this->formatCollection($collectionPath));

        foreach($documents as $id => $document) {
            $ref = $collection->document($id);
            $batch->set($ref, $document);
        }

        if ($filterByIds) {
            $documents = $collection->documents();
            foreach($documents as $document) {
                $id = $document->id();
                if (!in_array($id, $filterByIds)) {
                    $batch->delete($collection->document($id));
                }
            }
        }

        $batch->commit();

        return true;
    }

    /**
     * Clone a Collection from a source path to a target path
     *
     * @param string $sourceCollection
     * @param string $targetCollection
     * @return bool
     */
    public function cloneCollection(string $sourceCollection, string $targetCollection): bool
    {
        $documents = $this->db->collection($this->formatCollection($sourceCollection))
            ->documents();

        $batch = $this->db->batch();

        /** @var DocumentSnapshot $document */
        foreach($documents as $document) {
            $target = $this->db->collection($this->formatCollection($targetCollection))->document($document->id());
            $batch->set($target, $document->data());
        }
        $batch->commit();

        return true;
    }

    /**
     * @param string $collectionPath
     * @return CollectionReference
     */
    public function getCollectionReference(string $collectionPath): CollectionReference
    {
        return $this->db->collection($this->formatCollection($collectionPath));
    }

}
