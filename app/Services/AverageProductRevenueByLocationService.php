<?php

namespace App\Services;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\AverageProductRevenueByLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class AverageProductRevenueByLocationService
{
    const float DEFAULT_AVERAGE_LEAD_REVENUE        = 200;
    const float DEFAULT_AVERAGE_DIRECT_LEAD_REVENUE = 200;
    const int   DEFAULT_DAYS_AVERAGE                = 7;
    const int   CHUNK_SIZE                          = 1000;

    /**
     * @param int $productId
     * @param int $industryId
     * @param int|null $daysAverage
     * @return void
     */
    public function calculateAverageRevenueByCounty(int $productId, int $industryId, ?int $daysAverage = null): void
    {
        $daysAverage = $daysAverage ?? self::DEFAULT_DAYS_AVERAGE;

        $defaultPrice = $this->getDefaultProductRevenue($productId);

        ProductAssignment::query()
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, '=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join(Address::TABLE, Address::TABLE .'.'. Address::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ADDRESS_ID)
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ZIP_CODE, '=', Address::TABLE .'.'. Address::FIELD_ZIP_CODE)
            ->join(Location::TABLE .' as l2', fn(JoinClause $join) =>
                $join->on(Location::TABLE .'.'. Location::COUNTY_KEY, '=', 'l2.' . Location::COUNTY_KEY)
                    ->on(Location::TABLE .'.'. Location::STATE_KEY, '=', 'l2.' . Location::STATE_KEY)
                    ->where('l2.' . Location::TYPE, Location::TYPE_COUNTY)
            )
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, '>=', now()->subDays($daysAverage))
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::CREATED_AT, '>=', now()->subDays($daysAverage))
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED, true)
            ->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $industryId)
            ->where(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, $productId)
            ->groupBy('l2.' . Location::ID)
            ->selectRaw(
                'l2.' . Location::ID . ' as ' . AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID . ','
                . 'sum(' . ProductAssignment::FIELD_COST . ') as total_revenue,'
                . 'count(distinct ' . ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID . ') as total_products'
            )->orderBy(AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID)
            ->chunk(self::CHUNK_SIZE, function(Collection $chunk) use ($productId, $industryId, $defaultPrice) {
               $upsertData = $chunk->map(fn($price) => [
                   AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID   => $price->county_location_id,
                   AverageProductRevenueByLocation::FIELD_ZIP_CODE_LOCATION_ID => null,
                   AverageProductRevenueByLocation::FIELD_PRODUCT_ID           => $productId,
                   AverageProductRevenueByLocation::FIELD_INDUSTRY_ID          => $industryId,
                   AverageProductRevenueByLocation::FIELD_AVERAGE_REVENUE      => $price->total_products > 0 ? $price->total_revenue / $price->total_products : $defaultPrice,
               ])->toArray();

               AverageProductRevenueByLocation::query()
                   ->upsert($upsertData, [], [AverageProductRevenueByLocation::FIELD_AVERAGE_REVENUE]);
            });
    }

    /**
     * @param int $productId
     * @param int $industryId
     * @param array|null $countyIds
     * @return array
     */
    public function getAverageProductRevenueByCountyId(int $productId, int $industryId, ?array $countyIds = null): array
    {
        return AverageProductRevenueByLocation::query()
            ->where(AverageProductRevenueByLocation::FIELD_PRODUCT_ID, $productId)
            ->where(AverageProductRevenueByLocation::FIELD_INDUSTRY_ID, $industryId)
            ->when($countyIds, fn(Builder $query) =>
                $query->whereIn(AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID, $countyIds)
            )->select([AverageProductRevenueByLocation::FIELD_AVERAGE_REVENUE, AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID])
            ->get()
            ->mapWithKeys(fn($countyAverage) => [
                $countyAverage[AverageProductRevenueByLocation::FIELD_COUNTY_LOCATION_ID] => $countyAverage[AverageProductRevenueByLocation::FIELD_AVERAGE_REVENUE],
            ])->toArray();
    }

    /**
     * @param int $productId
     * @return float
     */
    public function getDefaultProductRevenue(int $productId): float
    {
        $product = ProductEnum::tryFrom(Product::find($productId)?->name ?? 0);
        return match($product) {
            ProductEnum::DIRECT_LEADS => self::DEFAULT_AVERAGE_DIRECT_LEAD_REVENUE,
            default                   => self::DEFAULT_AVERAGE_LEAD_REVENUE,
        };
    }
}
