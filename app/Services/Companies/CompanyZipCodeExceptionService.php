<?php

namespace App\Services\Companies;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyZipCodeException;
use App\Repositories\CompanyZipCodeExceptionRepository;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class CompanyZipCodeExceptionService
{
    public function __construct(
        protected CompanyZipCodeExceptionRepository $repository,
    ) {}

    /**
     * @param int $companyId
     * @return bool
     */
    public function companyHasExceptions(int $companyId): bool
    {
        return !!$this->getCompanyExceptionsQuery($companyId)->count();
    }

    /**
     * @param int $companyId
     * @return array
     */
    public function getCompanyExceptionsKeyedByState(int $companyId): array
    {
        return $this->getCompanyExceptions($companyId, [CompanyZipCodeException::RELATION_LOCATION])
            ->groupBy(CompanyZipCodeException::RELATION_LOCATION.'.'.Location::STATE_ABBREVIATION)->map(fn($stateAbbr) =>
                $stateAbbr->pluck(CompanyZipCodeException::RELATION_LOCATION.'.'.Location::COUNTY_KEY)
            )->toArray();
    }

    /**
     * @param int $companyId
     * @return array
     */
    public function getCompanyExceptionsAsZipLocationIds(int $companyId): array
    {
        return $this->getCompanyExceptionsQuery($companyId)
            ->select(['loc2.' . Location::ID])
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ID, '=', CompanyZipCodeException::TABLE .'.'. CompanyZipCodeException::FIELD_COUNTY_LOCATION_ID)
            ->join(Location::TABLE . ' as loc2', fn(JoinClause $join) =>
                $join->on('loc2.' . Location::COUNTY_KEY, '=', Location::TABLE .'.'. Location::COUNTY_KEY)
                    ->where('loc2.' . Location::TYPE, Location::TYPE_ZIP_CODE)
            )->pluck('loc2.' . Location::ID)
            ->toArray();
    }

    /**
     * @param int $companyId
     * @param array $relations
     * @return Collection
     */
    public function getCompanyExceptions(int $companyId, array $relations = []): Collection
    {
        return $this->getCompanyExceptionsQuery($companyId, $relations)->get();
    }



    /**
     * @param int $companyId
     * @param array $relations
     * @return Builder
     */
    protected function getCompanyExceptionsQuery(int $companyId, array $relations = []): Builder
    {
        $legacyId = Company::query()
            ->findOrFail($companyId)
            ->legacy_id;

        return CompanyZipCodeException::query()
            ->where(CompanyZipCodeException::FIELD_COMPANY_ID, $legacyId)
            ->with($relations);
    }

    public function updateExceptions(int $companyId, array $payload): array
    {
        $removedLocationArray = [];

        $originalExceptions = $this->getCompanyExceptionsKeyedByState($companyId);
        foreach ($payload as $stateAbbr => $newCountyKeys){
            $originalCountyKeys = $originalExceptions[$stateAbbr] ?? [];
            $addCountyKeys = array_unique(array_diff($newCountyKeys, $originalCountyKeys));
            $removeCountyKeys = array_unique(array_diff($originalCountyKeys, $newCountyKeys));

            if(count($addCountyKeys) > 0)
                $this->repository->addCounties($companyId, $stateAbbr, $addCountyKeys);

            if(count($removeCountyKeys)){
                $this->repository->removeCounties($companyId, $stateAbbr, $removeCountyKeys);
                $removedLocationArray[$stateAbbr] = $removeCountyKeys;
                $this->rectifyExistingCampaignLocations($companyId, $stateAbbr, $removeCountyKeys);
            }
        }

        return $removedLocationArray;
    }

    /**
     * This removes locations from a CompanyCampaign according to the zip code exceptions deleted by Legacy
     * This will need to be updated when the zip code exception model is moved to A2
     *
     * @param int $companyId
     * @param array $removedLocations
     * @return void
     */
    public function updateCampaignsForRemovedLocations(int $companyId, array $removedLocations): void
    {
        foreach ($removedLocations as $stateAbbr => $countyKeys){
            $this->rectifyExistingCampaignLocations($companyId, $stateAbbr, $countyKeys);
        }
    }

    /**
     * @param int $companyId
     * @param array $zipCodeLocationIds
     * @return ?array
     */
    public function validateZipCodeLocationIdsForCompany(int $companyId, array $zipCodeLocationIds): ?array
    {
        /** @var Company $company */
        $company = Company::query()
            ->findOrFail($companyId);
        $unrestricted = $company->configuration
            ?->unrestricted_zip_code_targeting ?? false;
        if ($unrestricted) {
            return [
                'valid_zip_codes'   => $zipCodeLocationIds,
                'invalid_zip_codes' => [],
            ];
        }

        $companyExceptions = $this->getCompanyExceptionsAsZipLocationIds($companyId);
        if (!$companyExceptions || !$zipCodeLocationIds) return null;

        $validatedIds = array_reduce($zipCodeLocationIds, function($output, $id) use ($companyExceptions) {
            if (in_array($id, $companyExceptions))
                $output['valid_zip_codes'][] = $id;
            else
                $output['invalid_zip_codes'][] = $id;
            return $output;
        }, [
            'valid_zip_codes'   => [],
            'invalid_zip_codes' => []
        ]);

        if ($validatedIds['invalid_zip_codes']) {
            $invalidZipCodes = Location::query()
                ->whereIn(Location::ID, $validatedIds['invalid_zip_codes'])
                ->whereNotNull(Location::ZIP_CODE)
                ->pluck(Location::ZIP_CODE)
                ->toArray();
            $validatedIds['invalid_zip_codes'] = $invalidZipCodes;
        }

        return $validatedIds;
    }

    /**
     * Remove any campaign locations from zip code targeted campaigns which are no longer valid exceptions
     *
     * @param int $companyId
     * @param string $stateAbbr
     * @param array $removedCountyKeys
     * @return void
     */
    protected function rectifyExistingCampaignLocations(int $companyId, string $stateAbbr, array $removedCountyKeys): void
    {
        /** @var CompanyCampaignRepository $repository */
        $repository = app(CompanyCampaignRepository::class);
        $campaigns = $repository->getZipCodeTargetCampaignForCompany($companyId);

        $campaigns->each(fn(CompanyCampaign $campaign) =>
            $campaign->locationModule->locations()
                ->join(Location::TABLE, Location::TABLE .'.'. Location::ID, '=', CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
                ->whereIn(Location::TABLE .'.'. Location::COUNTY_KEY, $removedCountyKeys)
                ->where(Location::TABLE .'.'. Location::STATE_ABBREVIATION, $stateAbbr)
                ->delete()
        );
    }

}
