<?php

namespace App\Services\Companies;

use App\Enums\PhoneType;
use App\Models\CompanyProfileCall;
use App\Models\CompanyProfileCallNumber;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Phone;
use App\Services\Twilio\TwilioPhoneNumberService;
use Carbon\Carbon;
use Twilio\Exceptions\TwilioException;

class CompanyProfileCallService
{
    public function __construct(protected TwilioPhoneNumberService $twilioPhoneNumberService) {}

    /**
     * @param Company $company
     *
     * @return string
     * @throws TwilioException
     */
    public function getProxyPhone(Company $company): string
    {
        $availablePhone = CompanyProfileCallNumber::notInUse()->first();

        if ($availablePhone) {
            $availablePhone->update([
                CompanyProfileCallNumber::FIELD_PHONE_COMPANY_ID => $company->id,
                CompanyProfileCallNumber::FIELD_IN_USE => true,
                CompanyProfileCallNumber::FIELD_IN_USE_SINCE => now()
            ]);

            return $availablePhone->phone->phone;
        }

        $phone = $this->twilioPhoneNumberService->acquireNumber(
            type: PhoneType::COMPANY_PROFILE_CALL,
            name: 'company_profile_call',
            configuration: [
                'voiceUrl' => route('company-profile-voice')
            ]
        );

        $this->createCompanyProfileCallNumber(
            phoneId: $phone->id,
            companyId: $company->id,
            inUse: true,
            inUseSince: now()
        );

        return $phone->phone;
    }

    /**
     * @param int $phoneId
     * @param int $companyId
     * @param bool $inUse
     * @param Carbon|null $inUseSince
     *
     * @return CompanyProfileCallNumber
     */
    public function createCompanyProfileCallNumber(int $phoneId, int $companyId, bool $inUse = false, ?Carbon $inUseSince = null): CompanyProfileCallNumber
    {
        return CompanyProfileCallNumber::query()->create([
            CompanyProfileCallNumber::FIELD_PHONE_COMPANY_ID => $companyId,
            CompanyProfileCallNumber::FIELD_PHONE_ID => $phoneId,
            CompanyProfileCallNumber::FIELD_IN_USE => $inUse,
            CompanyProfileCallNumber::FIELD_IN_USE_SINCE => $inUseSince
        ]);
    }

    public function findCompanyFromProxyPhone(string $phone): ?Company
    {
        return CompanyProfileCallNumber::inUse()
            ->select([CompanyProfileCallNumber::TABLE . '.' . CompanyProfileCallNumber::FIELD_PHONE_COMPANY_ID])
            ->join(
                Phone::TABLE,
                Phone::TABLE . '.' . Phone::FIELD_ID,
                CompanyProfileCallNumber::TABLE . '.' . CompanyProfileCallNumber::FIELD_PHONE_ID
            )
            ->where(Phone::TABLE . '.' . Phone::FIELD_PHONE, $phone)
            ->first()?->company;
    }

    public function getCompanyPhone(Company $company): ?string
    {
        if ($company->primaryLocation?->phone) {
            return $company->primaryLocation->phone;
        }

        return $company->locations->first(fn(CompanyLocation $location) => !empty($location->phone))?->phone;
    }

    public function findCompanyProfileCallBySid(string $sid): ?CompanyProfileCall
    {
        return CompanyProfileCall::query()->where(CompanyProfileCall::FIELD_SID, $sid)->first();
    }

    public function createCompanyProfileCall(int $companyId, string $sid, string $to, string $from, string $proxyPhone, array $content = []): CompanyProfileCall
    {
        return CompanyProfileCall::query()->create([
            CompanyProfileCall::FIELD_COMPANY_ID => $companyId,
            CompanyProfileCall::FIELD_SID => $sid,
            CompanyProfileCall::FIELD_TO => $to,
            CompanyProfileCall::FIELD_FROM => $from,
            CompanyProfileCall::FIELD_PROXY_PHONE => $proxyPhone,
            CompanyProfileCall::FIELD_CONTENT => $content
        ]);
    }
}
