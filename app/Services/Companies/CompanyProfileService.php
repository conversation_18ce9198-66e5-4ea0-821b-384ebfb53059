<?php

namespace App\Services\Companies;

use App\Enums\CompanyMediaAssetType;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyMediaAsset;
use App\Services\CloudStorage\GoogleCloudStorageService;
use App\Services\FileUploadHelperService;
use Google\Cloud\Storage\ObjectIterator;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Exception;

class CompanyProfileService
{
    public function __construct(protected GoogleCloudStorageService $storageService) {}

    public function getCompanyLogoURL(Company $company): ?string
    {
        return $company->link_to_logo;
    }

    protected function wrapUrl(string $url): string
    {
        if(Str::startsWith($url, "/"))
            $url = url($url);

        return $url;
    }

    /**
     * @param Company      $company
     * @param UploadedFile $file
     * @param bool         $encrypt
     * @return string|null
     * @throws Exception
     */
    public function updateCompanyLogo(Company $company, UploadedFile $file, bool $encrypt = true): ?string
    {
        $disk = config('services.google.storage.company_logos_disk');

        $path     = $this->getLogoUploadPath($company);
        $fileName = $this->getFileNameToUpload($file, $encrypt);

        $url = $disk === 'cloud'
            ? $this->uploadInCloud($path, $file, $fileName)
            : $this->uploadOnDisk($disk, $path, $file, $fileName);

        $company->link_to_logo = $url;
        $company->save();

        return $url;
    }

    /**
     * @param Company $company
     * @param array   $files
     * @param bool    $encrypt
     * @return Collection
     * @throws Exception
     */
    public function uploadCompanyMediaAssets(Company $company, array $files, bool $encrypt = true): Collection
    {
        $disk = config('services.google.storage.company_logos_disk');

        $uploaded = collect();
        collect($files)->each(function (UploadedFile $file) use ($disk, $company, &$uploaded, $encrypt) {
            $path     = $this->getMediaUploadPath($company);
            $fileName = $this->getFileNameToUpload($file, $encrypt);

            $url = $disk === 'cloud'
                ? $this->uploadInCloud($path, $file, $fileName)
                : $this->uploadOnDisk($disk, $path, $file, $fileName);

            if ($url) {
                /** @var CompanyMediaAsset $asset */
                $asset = CompanyMediaAsset::query()
                    ->create([
                        CompanyMediaAsset::FIELD_COMPANY_ID => $company->id,
                        CompanyMediaAsset::FIELD_NAME       => $file->getClientOriginalName(),
                        CompanyMediaAsset::FIELD_TYPE       => CompanyMediaAssetType::MEDIA->value,
                        CompanyMediaAsset::FIELD_URL        => $url,
                    ]);
                $uploaded->push($asset);
            }
        });
        return $uploaded;
    }

    public function addYoutubeLink(Company $company, string $url): ?CompanyMediaAsset
    {
        /** @var CompanyMediaAsset $newAsset */
        $newAsset = CompanyMediaAsset::query()
            ->create([
                CompanyMediaAsset::FIELD_COMPANY_ID => $company->id,
                CompanyMediaAsset::FIELD_TYPE       => CompanyMediaAssetType::LINK->value,
                CompanyMediaAsset::FIELD_URL        => $url,
            ]);
        return $newAsset;
    }

    public function deleteCompanyMediaAsset(Company $company, int $assetId): bool
    {
        /** @var CompanyMediaAsset $targetAsset */
        $targetAsset = CompanyMediaAsset::query()
            ->findOrFail($assetId);

        return $targetAsset->company_id === $company->id
            ? $targetAsset->delete()
            : false;
    }

    /**
     * @param string       $disk
     * @param string       $path
     * @param UploadedFile $file
     * @param string|null  $customFileName
     * @return string
     */
    public function uploadOnDisk(string       $disk,
                                 string       $path,
                                 UploadedFile $file,
                                 string       $customFileName = null,
    ): string
    {
        $path = !empty($customFileName)
            ? $file->storeAs(
                path    : $path,
                name    : $customFileName,
                options : $disk)
            : $file->store($path, $disk);

        return $this->wrapUrl(Storage::disk($disk)->url($path));
    }

    /**
     * @param string       $path
     * @param UploadedFile $file
     * @param string|null  $customFileName
     * @return string|null
     * @throws Exception
     */
    public function uploadInCloud(string       $path,
                                  UploadedFile $file,
                                  string       $customFileName = null,
    ): ?string
    {
        $storageOption = 'local';

        $path = !empty($customFileName)
            ? $file->storeAs(
                path    : $path,
                name    : $customFileName,
                options : $storageOption)
            : $file->store($path, $storageOption);

        $bucket = config('services.google.storage.buckets.company_logos');
        $url    = config('services.google.storage.urls.company_logos_file_url');

        $this->storageService->setCurrentBucket($bucket);
        $this->storageService->upload($path, fopen(storage_path("app/{$path}"), 'r'));

        return rtrim($url, '/') . "/{$bucket}/$path";
    }

    /**
     * @param string $path
     * @param bool   $transformToFiles
     * @return array|ObjectIterator
     * @throws Exception
     */
    public function getObjectsFromBucket(string $path, bool $transformToFiles = false): array|ObjectIterator
    {
        $bucket = config('services.google.storage.buckets.company_logos');
        $url    = config('services.google.storage.urls.company_logos_file_url');

        $this->storageService->setCurrentBucket($bucket);
        $objects = $this->storageService->getBucketObjects($path);

        return $transformToFiles
            ? collect($this->storageService->getFilesFromObjects($objects))
                ->map(fn ($file) => rtrim($url, '/') . "/{$bucket}/{$file}")
                ->toArray()
            : $objects;
    }

    /**
     * Returns path to store media assets (files/attachments) for the given company.
     *
     * @param Company $company
     * @return string
     */
    public function getMediaUploadPath(Company $company): string
    {
        return "company_logos/{$company->{Company::FIELD_REFERENCE}}/media_assets";
    }

    /**
     * Returns path to store logo for the given company.
     *
     * @param Company $company
     * @return string
     */
    public function getLogoUploadPath(Company $company): string
    {
        return "company_logos/{$company->{Company::FIELD_REFERENCE}}";
    }

    /**
     * Accepts file object and prepare its name for uploading on Cloud based on the encryption flag.
     *
     * @param UploadedFile $file
     * @param bool         $encrypt
     * @return string
     */
    public function getFileNameToUpload(UploadedFile $file, bool $encrypt = true): string
    {
        return $encrypt === true
            ? FileUploadHelperService::encryptFileName($file->getClientOriginalName(), true)
            : FileUploadHelperService::prepareFileName($file->getClientOriginalName());
    }

}
