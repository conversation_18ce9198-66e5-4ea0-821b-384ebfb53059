<?php

namespace App\Services\Companies;

use App\Models\Odin\Company;
use App\Services\Companies\Delete\CompanyDeleteService;

class CompanyLinkService
{
    public function getDashboardUrl(Company $company): string
    {
        $prospect = $company->latestProspect;

        return empty($prospect)
            ? $company->getAdminProfileUrl()
            : $prospect->getBDMProfileUrl();
    }

    public function getCancelDeletionUrl(Company $company): string
    {
        $deleteService = new CompanyDeleteService($company);
        return $deleteService->cancelUrl();
    }


}
