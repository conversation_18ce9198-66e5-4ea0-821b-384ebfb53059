<?php

namespace App\Services\Companies;

use App\Enums\Campaigns\CampaignExternalRelationType;
use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ProductCampaign;
use App\Repositories\LeadCampaignRepository;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\ProductCampaignRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class CompanyService
{
    public function __construct(
        protected ProductCampaignRepository $productCampaignRepository,
        protected LeadCampaignRepository $leadCampaignRepository,
        protected CompanyCampaignRepository $companyCampaignRepository,
        protected CompanyRepository $companyRepository,
    )
    {

    }

    /**
     * @param int $companyId
     * @param int|null $productId
     * @param bool|null $status
     * @param bool|null $withDeleted
     * @return array{product_campaigns: Collection, lead_campaigns: Collection, campaigns: Collection}
     */
    public function getAllCampaignsForCompany(
        int $companyId,
        ?int $productId = null,
        ?bool $status = null,
        ?bool $withDeleted = null,
    ): array
    {
        $productCampaigns = $this->productCampaignRepository->getCompanyCampaignsQuery(
            companyId  : $companyId,
            status     : $status,
            productId  : $productId,
            withDeleted: $withDeleted
        )
            ->whereNotExists(fn(Builder $query) => $query->select(CompanyCampaignRelation::FIELD_ID)
                ->from(CompanyCampaignRelation::TABLE)
                ->where(CompanyCampaignRelation::FIELD_RELATION_ID, DB::raw(ProductCampaign::TABLE . '.' . ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID))
                ->where(CompanyCampaignRelation::FIELD_RELATION_TYPE, CampaignExternalRelationType::LEGACY_LEAD_CAMPAIGN)
            )
            ->orderByDesc(ProductCampaign::FIELD_ID)
            ->get();

        // TODO - Remove if product is not lead
        $legacyCompanyId = $this->companyRepository->find($companyId)->{Company::FIELD_LEGACY_ID};
        $leadCampaigns = $this->leadCampaignRepository->getCompanyLeadCampaigns(
            legacyCompanyId: $legacyCompanyId,
            withDeleted    : $withDeleted,
            status         : $status,
        )
            ->orderByDesc(LeadCampaign::TABLE . '.' . LeadCampaign::ID)
            ->get();

        $campaigns = $this->companyCampaignRepository->getCompanyCampaignsQuery(
            companyId  : $companyId,
            productId  : $productId,
            withDeleted: $withDeleted,
            status     : $status,
        )
            ->orderByDesc(CompanyCampaign::FIELD_ID)
            ->get();

        return [
            'product_campaigns' => $productCampaigns,
            'lead_campaigns'    => $leadCampaigns,
            'campaigns'         => $campaigns
        ];
    }

    /**
     * Returns data on CompanyCampaigns only
     * @param Company $company
     * @return array
     */
    public function getCampaignStatusSummaryForCompany(Company $company): array
    {
        return $company->futureCampaigns()
            ->select(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_STATUS)
            ->get()
            ->reduce(function (array $output, CompanyCampaign $campaign) {
                $output[$campaign->status->value] ++;
                return $output;
            }, [
                CampaignStatus::ACTIVE->value             => 0,
                CampaignStatus::PAUSED_TEMPORARILY->value => 0,
                CampaignStatus::PAUSED_PERMANENTLY->value => 0,
            ]);
    }

    /**
     * @param Company $company
     * @param string $default
     *
     * @return string
     */
    public function getRegistrationDomain(Company $company, string $default = 'fixr.com'): string
    {
        if (!$company->registration_domain) {
            return $default;
        }

        if (Str::startsWith(strtolower($company->registration_domain), ['localhost', '127'])) {
            return $default;
        }

        return $company->registration_domain;
    }
}
