<?php

namespace App\Services;

use App\Campaigns\Delivery\CRM\BaseInteractableCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\UnauthorizedException;

class CompanyCRMTemplateService
{
    const string PAYLOAD_CAMPAIGN_REFERENCES = 'campaigns';

    /**
     * @param array $templatePayload
     * @param bool $syncCampaigns
     * @return bool
     */
    public function saveCompanyCRMTemplate(array $templatePayload, bool $syncCampaigns): bool
    {
        $existingTemplateId = $templatePayload[CompanyCRMTemplate::FIELD_ID] ?? null;
        if (!$existingTemplateId) {
            $template = $this->createCompanyCRMTemplate($templatePayload);
        }
        else {
            /** @var CompanyCRMTemplate $template */
            $template = CompanyCRMTemplate::query()
                ->findOrFail($existingTemplateId);
            if ($template->company_id !== $templatePayload[CompanyCRMTemplate::FIELD_COMPANY_ID])
                throw new UnauthorizedException("Target Template does not belong to this Company.");

            $template = $this->updateCompanyCRMTemplate($template, $templatePayload)
                ? $template->refresh()
                : null;
        }

        if (!$template)
            return false;

        if ($syncCampaigns) {
            $campaignReferences = $templatePayload[self::PAYLOAD_CAMPAIGN_REFERENCES] ?? [];
            $this->handleCampaignDelivererSync($template, $campaignReferences);
        }

        return true;
    }

    /**
     * Handle syncing a Template with individual campaign deliveries
     * This is scoped to whatever campaign references the front end provides, not necessarily company-wide
     *
     * @param CompanyCRMTemplate $template
     * @param array $campaignReferences
     * @return void
     */
    protected function handleCampaignDelivererSync(CompanyCRMTemplate $template, array $campaignReferences): void
    {
        $scopedCampaigns = array_keys($campaignReferences);
        $validCampaigns = $template->company->futureCampaigns()
            ->whereIn(CompanyCampaign::FIELD_REFERENCE, $scopedCampaigns)
            ->get();
        foreach ($validCampaigns as $campaign) {
            if ($campaignReferences[$campaign->reference]) {
                $campaign->deliveryModule->crms()
                    ->firstOrCreate([
                        CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID => $template->id,
                    ], [
                        CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID => $template->id,
                        CompanyCampaignDeliveryModuleCRM::FIELD_CRM_TYPE    => 0,
                        CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => "",
                        CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE => true,
                        CompanyCampaignDeliveryModuleCRM::FIELD_PAYLOAD => [],
                    ]);
            }
            else {
                $campaign->deliveryModule->crms()
                    ->where(CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID, $template->id)
                    ->delete();
            }
        }
    }

    /**
     * @param CompanyCRMTemplate $template
     * @return bool
     */
    public function deleteCompanyCRMTemplate(CompanyCRMTemplate $template): bool
    {
        DB::beginTransaction();
        try {
            $template->campaignDeliveries()->delete();
            $template->delete();
        }
        catch(Exception $e) {
            DB::rollBack();
            logger()->error("Error deleting CompanyCRMTemplate or deliveries: " . $e->getMessage());

            return false;
        }

        DB::commit();

        return true;
    }

    /**
     * @param array $payload
     * @return CompanyCRMTemplate
     */
    protected function createCompanyCRMTemplate(array $payload): CompanyCRMTemplate
    {
        /** @var CompanyCRMTemplate */
        return CompanyCRMTemplate::query()
            ->create([
                CompanyCRMTemplate::FIELD_COMPANY_ID   => $payload[CompanyCRMTemplate::FIELD_COMPANY_ID],
                CompanyCRMTemplate::FIELD_CRM_TYPE     => $payload[CompanyCRMTemplate::FIELD_CRM_TYPE],
                CompanyCRMTemplate::FIELD_DISPLAY_NAME => $payload[CompanyCRMTemplate::FIELD_DISPLAY_NAME],
                CompanyCRMTemplate::FIELD_PAYLOAD      => $payload[CompanyCRMTemplate::FIELD_PAYLOAD],
            ]);
    }

    /**
     * @param CompanyCRMTemplate $template
     * @param array $payload
     * @return bool
     */
    protected function updateCompanyCRMTemplate(CompanyCRMTemplate $template, array $payload): bool
    {
        $interactableIds = [CRMType::PIPEDRIVE->value];
        // Do not overwrite Pipedrive CRM payloads if the fields were never fetched, only update status/name changes
        if (in_array($payload[CompanyCRMTemplate::FIELD_CRM_TYPE], $interactableIds)) {
            if (!array_key_exists(BaseInteractableCRMDeliverer::INTERACTABLE_FIELDS_KEY, $payload[CompanyCRMTemplate::FIELD_PAYLOAD]) || !$payload[CompanyCRMTemplate::FIELD_PAYLOAD][BaseInteractableCRMDeliverer::INTERACTABLE_FIELDS_KEY]) {
                return $template->update([
                    CompanyCRMTemplate::FIELD_DISPLAY_NAME => $payload[CompanyCRMTemplate::FIELD_DISPLAY_NAME],
                ]);
            }
        }

        return $template->update([
            CompanyCRMTemplate::FIELD_DISPLAY_NAME => $payload[CompanyCRMTemplate::FIELD_DISPLAY_NAME],
            CompanyCRMTemplate::FIELD_PAYLOAD      => $payload[CompanyCRMTemplate::FIELD_PAYLOAD],
        ]);
    }
}
