<?php

namespace App\Services\MarketingCampaign;

use App\Builders\Odin\ConsumerSearchBuilder;
use App\Contracts\Services\MarketingCampaign\EmailMarketingServiceFactory;
use App\DTO\MarketingCampaign\CampaignMetrics;
use App\DTO\MarketingCampaign\EmailCampaignMetrics;
use App\DTO\MarketingCampaign\EmailCampaignUser;
use App\DTO\MarketingCampaign\InternalEmailCampaignMetrics;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Jobs\MarketingCampaign\AddConsumersToMarketingCampaign;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\Filterables\Consumer\ConsumerFilterableService;
use App\Services\MarketingCampaign\CallbackPayload\BaseCallbackPayload;
use App\Services\MarketingCampaign\CallbackPayload\CallbackPayloadType;
use App\Services\MarketingCampaign\CallbackPayload\DirectAllocationPayload;
use App\Services\MarketingCampaign\CallbackPayload\SimpleSingleSlideCallbackPayload;
use App\Services\MarketingCampaign\CallbackPayload\SolarValidatePayload;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class MarketingCampaignService
{
    const string EXTERNALLY_CREATED_MARKETING_CAMPAIGN_DESCRIPTION = 'Marketing Campaign Created Externally';

    const int CHUNK = 8000;

    public function __construct(
        protected MarketingCampaignRepository         $repository,
        protected ActivityLogRepository               $activityLogRepository,
        protected ConsumerFilterableService           $filterableService,
        protected MarketingCampaignConsumerRepository $marketingCampaignConsumerRepository,
        protected GlobalConfigurationRepository       $globalConfigurationRepository,

    )
    {
    }

    /**
     * @param MarketingCampaignStatus|null $status
     * @param string|null $name
     * @param array<MarketingCampaignType>|null $types
     * @return Builder
     */
    public function listMarketingCampaigns(
        ?MarketingCampaignStatus $status = null,
        ?string                  $name = null,
        ?array                   $types = null,
    ): Builder
    {
        return $this->repository->listMarketingCampaigns(
            status: $status,
            name: $name,
            types: $types,
        );
    }

    /**
     * @param int $authorId
     * @param string $name
     * @param array $consumerFilters
     * @param MarketingCampaignType $type
     * @param string|null $description
     * @param MarketingCampaignCallbackType|null $campaignCallbackType
     * @param array|null $marketingCampaignCallbackCustomInputs
     * @param array|null $configuration
     * @return MarketingCampaign
     * @throws Exception
     */
    public function createMarketingCampaign(
        int                            $authorId,
        string                         $name,
        array                          $consumerFilters,
        MarketingCampaignType          $type,
        ?string                        $description = null,
        ?MarketingCampaignCallbackType $campaignCallbackType = null,
        ?array                         $marketingCampaignCallbackCustomInputs = null,
        ?array                         $configuration = null,
    ): MarketingCampaign
    {
        $marketingCampaignPayload = $this->createMarketingCampaignPayload($campaignCallbackType, $marketingCampaignCallbackCustomInputs);

        $campaignType = $type->getClass();

        $configuration = $campaignType->prepareConfiguration(
            configuration: $configuration,
            filters: $consumerFilters
        );

        $campaign = $this->repository->createNewMarketingCampaign(
            authorId: $authorId,
            name: $name,
            status: MarketingCampaignStatus::DRAFT,
            type: $type,
            callbackPayloadType: $marketingCampaignPayload,
            description: $description,
            configuration: $configuration,
        );

        MarketingLogService::log(
            message: 'Marketing Campaign created',
            namespace: MarketingLogType::MARKETING_CAMPAIGN,
            level: LogLevel::INFO,
            context: [
                'filters'       => $consumerFilters,
                'configuration' => $configuration,
            ],
            relations: [$campaign],
        );

        $campaignType->initialise(
            campaign: $campaign,
            filters: $consumerFilters,
            authorId: $authorId
        );

        return $campaign->refresh();
    }

    /**
     * @param int $authorId
     * @param int $marketingCampaignId
     * @param array $filters
     * @return MarketingCampaign
     */
    public function updateMarketingCampaignTargets(
        int   $authorId,
        int   $marketingCampaignId,
        array $filters,
    ): MarketingCampaign
    {
        $campaign = $this->repository->getMarketingCampaignById($marketingCampaignId);

        $this->repository->updateMarketingCampaign(
            marketingCampaign: $campaign,
            authorId: $authorId,
        );

        $this->activityLogRepository->createActivityLog(
            logName: 'marketing_campaign',
            description: 'updated',
            subjectType: MarketingCampaign::class,
            subjectId: $marketingCampaignId,
            properties: ['consumer_filters' => $filters]
        );

        AddConsumersToMarketingCampaign::dispatch(
            $campaign->{MarketingCampaign::FIELD_ID},
            $filters
        );

        return $campaign;
    }

    /**
     * @param string $externalReference
     * @param Carbon|null $sentAt
     * @param MarketingCampaignStatus|null $status
     * @return bool
     * @throws BindingResolutionException
     */
    public function updateMarketingCampaignByExternalReference(
        string                   $externalReference,
        ?Carbon                  $sentAt = null,
        ?MarketingCampaignStatus $status = null,
    ): bool
    {
        $campaign = $this->repository->getMarketingCampaignByExternalReference($externalReference);

        if (empty($campaign)) {
            //must have been created externally
            $campaign = $this->initialiseUnknownMarketingCampaign($externalReference);
        }

        return $this->repository->updateMarketingCampaign(
            marketingCampaign: $campaign,
            status: $status,
            sentAt: $sentAt,
        );
    }

    /**
     * @param array $filters
     * @param int $marketingCampaignId
     * @return Collection|\Illuminate\Support\Collection
     */
    public function initialiseMarketingCampaignConsumers(array $filters, int $marketingCampaignId): Collection|\Illuminate\Support\Collection
    {
        $query = ConsumerSearchBuilder::query()
            ->searchId(Arr::get($filters, 'search_id'))
            ->searchText(Arr::get($filters, 'search_text'))
            ->companyId(Arr::get($filters, 'company_id'))
            ->campaignIds(Arr::get($filters, 'campaign_id'))
            ->soldToCompany(Arr::get($filters, 'sold'))
            ->excludeMarketingCampaign($marketingCampaignId)
            ->excludeCloned(true)
            ->excludeConsumerProductStatus(ConsumerProduct::STATUS_CANCELLED)
            ->getMarketingFieldsQuery();

        $consumers = collect();

        $this->filterableService->runQuery($filters['filters'], $query)->chunk(self::CHUNK, function (Collection $chunk) use ($marketingCampaignId, $consumers) {
            $this->marketingCampaignConsumerRepository->batchCreateMarketingCampaignConsumers($marketingCampaignId, $chunk);
            $consumers->push(...$this->transformConsumersToEmailCampaignConsumers($chunk));
        });

        return $consumers;
    }

    /**
     * @param Collection $consumers
     * @return Collection|\Illuminate\Support\Collection
     */
    public function transformConsumersToEmailCampaignConsumers(Collection $consumers): Collection|\Illuminate\Support\Collection
    {
        return $consumers->map(function (Consumer $consumer) {
            $cleanedEmail = strtolower(preg_replace('/\s+/', '', trim($consumer->{Consumer::FIELD_EMAIL})));

            return new EmailCampaignUser(
                firstName: $consumer->{Consumer::FIELD_FIRST_NAME},
                lastName: $consumer->{Consumer::FIELD_LAST_NAME},
                email: $cleanedEmail,
                consumerReference: $consumer->{Consumer::FIELD_REFERENCE},
                marketingCampaignReference: Str::uuid()->toString(),
                status: MarketingCampaignConsumerStatus::INITIALISED,
                externalReference: $consumer->{'mcc_external_reference'}
            );
        });
    }

    /**
     * @param MarketingCampaign $campaign
     * @return CallbackPayloadType|null
     */
    public function getMarketingCampaignPayload(MarketingCampaign $campaign): ?CallbackPayloadType
    {
        $type = $campaign->{MarketingCampaign::FIELD_CALLBACK_PAYLOAD}[CallbackPayloadType::TYPE] ?? null;

        $type = MarketingCampaignCallbackType::tryFrom($type);

        return match ($type) {
            MarketingCampaignCallbackType::SIMPLE_SINGLE_SLIDE => SimpleSingleSlideCallbackPayload::fromMarketingCampaign($campaign),
            MarketingCampaignCallbackType::DIRECT_ALLOCATION   => DirectAllocationPayload::fromMarketingCampaign($campaign),
            MarketingCampaignCallbackType::SOLAR_VALIDATE      => SolarValidatePayload::fromMarketingCampaign($campaign),
            default                                            => BaseCallbackPayload::fromMarketingCampaign($campaign),
        };
    }

    /**
     * @param MarketingCampaignCallbackType|null $callbackType
     * @param array|null $marketingCampaignCallbackInputs
     * @return CallbackPayloadType
     */
    public function createMarketingCampaignPayload(
        ?MarketingCampaignCallbackType $callbackType = null,
        ?array                         $marketingCampaignCallbackInputs = null,
    ): CallbackPayloadType
    {
        return match ($callbackType) {
            MarketingCampaignCallbackType::SIMPLE_SINGLE_SLIDE => SimpleSingleSlideCallbackPayload::fromArray($marketingCampaignCallbackInputs),
            MarketingCampaignCallbackType::DIRECT_ALLOCATION   => DirectAllocationPayload::fromArray($marketingCampaignCallbackInputs),
            MarketingCampaignCallbackType::SOLAR_VALIDATE      => SolarValidatePayload::fromArray($marketingCampaignCallbackInputs),
            default                                            => new BaseCallbackPayload()
        };
    }

    /**
     * @param MarketingCampaign $marketingCampaign
     * @return CampaignMetrics
     */
    public function getMarketingCampaignMetrics(MarketingCampaign $marketingCampaign): CampaignMetrics
    {
        $campaignType = $marketingCampaign->{MarketingCampaign::FIELD_TYPE};

        return match ($campaignType) {
            MarketingCampaignType::MAILCHIMP_EMAIL                                   => EmailCampaignMetrics::fromMarketingCampaign($marketingCampaign),
            MarketingCampaignType::INTERNAL_EMAIL, MarketingCampaignType::DRIP_EMAIL => InternalEmailCampaignMetrics::fromMarketingCampaign($marketingCampaign),
            default                                                                  => CampaignMetrics::fromMarketingCampaign($marketingCampaign),
        };
    }

    /**
     * @param \Illuminate\Support\Collection $response
     * @param int $campaignId
     * @return void
     */
    public function updateMarketingCampaignConsumers(\Illuminate\Support\Collection $response, int $campaignId): void
    {
        $this->marketingCampaignConsumerRepository->updateMarketingCampaignConsumers($response, $campaignId);
    }

    /**
     * @param int $marketingCampaignId
     * @return bool
     */
    public function checkCampaignStillProcessing(int $marketingCampaignId): bool
    {
        return MarketingCampaignConsumer::query()
            ->where(MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID, '=', $marketingCampaignId)
            ->where(MarketingCampaignConsumer::FIELD_STATUS, '=', MarketingCampaignConsumerStatus::INITIALISED)
            ->exists();
    }

    /**
     * @param MarketingCampaign $campaign
     * @return void
     */
    public function syncCampaignProcessing(MarketingCampaign $campaign): void
    {
        $stillProcessing = $this->checkCampaignStillProcessing($campaign->{MarketingCampaign::FIELD_ID});

        if ($campaign->{MarketingCampaign::FIELD_PROCESSING} !== $stillProcessing) {
            $campaign->{MarketingCampaign::FIELD_PROCESSING} = $stillProcessing;
            $campaign->save();
        }
    }

    /**
     * @param string $externalReference
     * @return MarketingCampaign
     * @throws BindingResolutionException
     */
    public function initialiseUnknownMarketingCampaign(string $externalReference): MarketingCampaign
    {
        $marketingCampaignService = EmailMarketingServiceFactory::make(MarketingCampaignType::MAILCHIMP_EMAIL);

        $campaignInfo = $marketingCampaignService->findCampaignByExternalReference($externalReference);
        $authorId = User::systemUser()->id;

        return $this->repository->createNewMarketingCampaign(
            authorId: $authorId,
            name: $campaignInfo ? $campaignInfo['name'] : "Unknown Campaign",
            status: MarketingCampaignStatus::SENT,
            type: MarketingCampaignType::MAILCHIMP_EMAIL,
            callbackPayloadType: new BaseCallbackPayload(),
            description: self::EXTERNALLY_CREATED_MARKETING_CAMPAIGN_DESCRIPTION,
            externalReference: $externalReference,
        );
    }

    /**
     * @param MarketingCampaign $campaign
     * @param bool|null $deleteMarketingCampaignConsumers
     * @return bool|null
     */
    public function deleteMarketingCampaign(
        MarketingCampaign $campaign,
        ?bool             $deleteMarketingCampaignConsumers = true,
    ): ?bool
    {
        if ($deleteMarketingCampaignConsumers) {
            $this->marketingCampaignConsumerRepository->deleteMarketingCampaignConsumers(campaign: $campaign);
        }
        return $this->repository->deleteMarketingCampaign(campaign: $campaign);
    }

    public function updateCampaignMetrics(
        MarketingCampaign $campaign,
        ?int              $incrementSendCount = null,
        ?int              $incrementTargetCount = null,
    ): void
    {
        if ($campaign->{MarketingCampaign::FIELD_TYPE} === MarketingCampaignType::MAILCHIMP_EMAIL) {
            return;
        }

        /** @var InternalEmailCampaignMetrics $metrics */
        $metrics = $this->getMarketingCampaignMetrics(marketingCampaign: $campaign);

        if (filled($incrementSendCount)) {
            $metrics->incrementSentCount(incremental: $incrementSendCount);
        }

        if (filled($incrementTargetCount)) {
            $metrics->incrementTargets(incremental: $incrementTargetCount);
        }

        $this->repository->updateMarketingCampaign(
            marketingCampaign: $campaign,
            metrics: $metrics
        );
    }
}

