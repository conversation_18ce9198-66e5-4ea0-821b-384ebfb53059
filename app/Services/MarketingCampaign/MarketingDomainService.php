<?php

namespace App\Services\MarketingCampaign;

use App\Contracts\Services\MarketingCampaign\EmailMarketingServiceFactory;
use App\DTO\EmailService\DomainDTO;
use App\Enums\Emails\DomainStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Models\MarketingDomain;
use App\Repositories\MarketingCampaign\MarketingDomainRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;

class MarketingDomainService
{
    const string SOLARREVIEWS_DOMAIN = 'solarreviews.com';

    /** @var array|string[] domains that should not be use for marketing */
    const array BLACKLISTED_DOMAINS = [
        self::SOLARREVIEWS_DOMAIN,
    ];
    public function __construct(
        protected MarketingDomainRepository $repository,
    )
    {
    }

    /**
     * Syncs local domains with the given marketing campaign type
     *
     * @param MarketingCampaignType $type
     * @return void
     * @throws BindingResolutionException
     */
    public function sync(MarketingCampaignType $type): void
    {
        $emailMarketingService = EmailMarketingServiceFactory::make($type);

        $serviceDomains = $emailMarketingService->listValidSendingDomains(excludedDomains: self::BLACKLISTED_DOMAINS);

        $domains = $this->list()->get();

        foreach ($serviceDomains as $serviceDomain) {
            /** @var ?MarketingDomain $foundInDb */
            $foundInDb = $domains->first(function (MarketingDomain $domain, $index) use ($serviceDomain, &$domains) {
                if ($domain->domain === $serviceDomain->getName()) {
                    $domains->forget($index);
                    return true;
                }

                return false;
            });

            if ($foundInDb) {
                $foundInDb->update([
                    MarketingDomain::FIELD_STATUS => $serviceDomain->getStatus(),
                ]);

            } else {
                $this->create(
                    name: $serviceDomain->getName(),
                    status: $serviceDomain->getStatus(),
                );
            }

        }

        //domains not found in marketing service
        $domains->each(function (MarketingDomain $domain) {
            $domain->update([
                MarketingDomain::FIELD_STATUS => DomainStatus::UNVERIFIED,
            ]);
        });
    }

    /**
     * @param DomainStatus|null $status
     * @param string|null $search
     * @param int|null $maxSentCount
     * @param Carbon|null $maxSentCutoff
     * @param int|null $marketingCampaignId
     * @param array|null $domains
     * @return Builder
     */
    public function list(
        ?DomainStatus $status = null,
        ?string $search = null,
        ?int $maxSentCount = null,
        ?Carbon $maxSentCutoff = null,
        ?int $marketingCampaignId = null,
        ?array $domains = null,
    ): Builder
    {
        return $this->repository->list(
            status: $status,
            search: $search,
            maxSentCount: $maxSentCount,
            maxSentCutoff: $maxSentCutoff,
            marketingCampaignId: $marketingCampaignId,
            domains: $domains,
        );
    }

    /**
     * @param string $name
     * @param DomainStatus $status
     * @return MarketingDomain
     */
    public function create(
        string       $name,
        DomainStatus $status,
    ): MarketingDomain
    {
        return $this->repository->create(
            name: $name,
            status: $status,
        );
    }

    /**
     * @param string $name
     * @return MarketingDomain|null
     */
    public function findByName(string $name): ?MarketingDomain
    {
        return $this->repository->findByName(
            name: $name,
        );
    }

}
