<?php

namespace App\Services\MarketingCampaign;

use App\Contracts\Marketing\MarketingEventInterpreterContract;
use App\Services\MarketingCampaign\Events\CampaignSentEvent;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class MailchimpEventInterpreter implements MarketingEventInterpreterContract
{
    const string REQUEST_TYPE     = 'type';
    const string REQUEST_DATA     = 'data';
    const string REQUEST_ID       = 'id';
    const string REQUEST_FIRED_AT = 'fired_at';
    const string MAILCHIMP_CAMPAIGN_SENT = 'campaign';

    public function interpret(Request $request): ?Events\MarketingEvent
    {
        $content = $request->all();

        if (empty($content)) {
            return null;
        };

        $eventType = Arr::get($content, self::REQUEST_TYPE);

        return match ($eventType) {
            self::MAILCHIMP_CAMPAIGN_SENT => new CampaignSentEvent(
                externalReference: $content[self::REQUEST_DATA][self::REQUEST_ID],
                sentAt           : Carbon::parse($content[self::REQUEST_FIRED_AT]),
            ),
            default                      => null
        };
    }
}
