<?php

namespace App\Services\MarketingCampaign\Types;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\MarketingCampaigns\SendingStrategy;
use App\Jobs\MarketingCampaign\InitialiseMarketingCampaign;
use App\Jobs\MarketingCampaign\SendMarketingEmail;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\EmailAddress\EmailAddressService;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\MarketingCampaign\Sending\MarketingCampaignSendingService;
use App\Services\Shortcode\EmailTemplateShortcodeReplacerService;
use App\Services\Shortcode\ShortcodeImplementation\EmailMarketingShortcodeUseCase;
use Carbon\Carbon;
use Error;
use Illuminate\Support\Arr;
use Illuminate\Support\Fluent;
use Illuminate\Validation\Rule;
use InvalidArgumentException;

abstract class BaseMarketingCampaignType
{
    const string USED_SHORTCODES = 'used_shortcodes';
    const string FROM_EMAIL      = 'from_email';
    const string FROM_NAME       = 'from_name';
    const string EMAIL_TEMPLATE  = 'email_template';

    public abstract function type(): MarketingCampaignType;

    public function configValidation(): array
    {
        return [
            'email_template_id'     => ['required', 'integer', 'exists:' . EmailTemplate::TABLE . ',' . EmailTemplate::FIELD_ID],
            'sent_at'               => ['exclude_if:campaign.status,sent', 'required', 'date'],
            'sender_domain'         => ['required', 'string'],
            'sender_local'          => ['required', 'string'],
            'sender_name'           => ['required', 'string'],
            'sending_strategy'      => ['nullable', Rule::enum(SendingStrategy::class)],
            'sending_strategy_data' => ['nullable', 'array'],
        ];

    }

    public function prefixedConfigValidation(string $prefix): array
    {
        $validation = $this->configValidation();
        $prefixed = [];
        foreach ($validation as $key => $value) {
            $prefixed[$prefix . '.' . $key] = $value;
        }

        return $prefixed;
    }

    public function prepareConfiguration(array $configuration, array $filters): array
    {
        return $configuration;
    }

    public function initialise(
        MarketingCampaign $campaign,
        array             $filters,
        int               $authorId,
    ): void
    {
        InitialiseMarketingCampaign::dispatch(
            $campaign->{MarketingCampaign::FIELD_ID},
            $campaign->{MarketingCampaign::FIELD_NAME},
            $filters,
            $authorId,
        );
    }

    public function getSendingConfiguration(MarketingCampaign $campaign): array
    {
        $configuration = new Fluent($campaign->{MarketingCampaign::FIELD_CONFIGURATION} ?? []);

        $senderLocal = $configuration->get('sender_local');
        $senderDomain = $configuration->get('sender_domain');

        if (!empty($senderLocal) && !empty($senderDomain)) {
            $fromEmail = $senderLocal . '@' . $senderDomain;
        } else {
            throw new InvalidArgumentException("Unable to build from email for marketing campaign id: $campaign->id");
        }

        $fromName = $configuration->get('sender_name', $fromEmail);

        $emailTemplate = EmailTemplate::query()->findOrFail($configuration->get('email_template_id'));

        $templateReplacerService = app(EmailTemplateShortcodeReplacerService::class);

        $usedShortcodes = $templateReplacerService->getUsedShortcodes($emailTemplate);

        return [
            self::USED_SHORTCODES => $usedShortcodes,
            self::FROM_EMAIL      => $fromEmail,
            self::FROM_NAME       => $fromName,
            self::EMAIL_TEMPLATE  => $emailTemplate,
        ];
    }

    public function prepareMarketingCampaignConsumerToSend(
        MarketingCampaignConsumer $campaignConsumer,
        array                     $sendingConfiguration,
    ): mixed
    {
        $consumer = $campaignConsumer->consumer;

        try {
            $useCase = EmailMarketingShortcodeUseCase::fromMarketingCampaignConsumer(marketingCampaignConsumer: $campaignConsumer);
            $compiledShortcodes = $useCase->compile($sendingConfiguration[self::USED_SHORTCODES])->toArray();

            [$validTarget, $message, $context] = $this->validateOutgoingEmail(
                $compiledShortcodes,
            );
        } catch (Error $e) {
            $validTarget = false;
            $message = $e->getMessage();
            $context = [
                'consumer'  => $consumer->toArray(),
                'marketing' => $campaignConsumer->toArray(),
            ];
        }

        if ($validTarget) {
            /** @var EmailTemplate $emailTemplate */
            $emailTemplate = Arr::get($sendingConfiguration, self::EMAIL_TEMPLATE);

            return new OutgoingEmailDTO(
                toEmail: app(EmailAddressService::class)->formatEmailAddress(email: $consumer->email),
                fromEmail: Arr::get($sendingConfiguration, self::FROM_EMAIL),
                fromUsername: Arr::get($sendingConfiguration, self::FROM_NAME),
                subject: $emailTemplate->subject,
                body: $emailTemplate->content,
                shortcodes: $compiledShortcodes,
                relationModel: $campaignConsumer::class,
                relationId: $campaignConsumer->id,
                emailMeta: [OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY => $campaignConsumer->id],
                emailTemplateId: $emailTemplate->id,
            );
        } else {
            MarketingLogService::log(
                message: $message,
                namespace: MarketingLogType::MARKETING_INTERNAL_SEND,
                level: LogLevel::ERROR,
                context: [
                    'marketing_campaign_id' => $campaignConsumer->marketing_campaign_id,
                    'consumer_id'           => $consumer->id,
                    'validation_issue'      => $context,
                ],
                relations: [
                    $campaignConsumer,
                    $campaignConsumer->marketingCampaign,
                ]
            );

            return null;
        }

    }

    /**
     * @param MarketingCampaign $campaign
     * @param OutgoingEmailDTO $dto
     * @param Carbon $delay
     * @return void
     */
    public function dispatch(MarketingCampaign $campaign, mixed $dto, Carbon $delay): void
    {
        SendMarketingEmail::dispatch(
            $campaign->id,
            $dto,
            $dto->getEmailTemplateId(),
            $dto->getFromEmail(),
            $dto->getFromEmail(),
        )->delay($delay);
    }

    public function send(MarketingCampaign $campaign): void
    {
        app(MarketingCampaignSendingService::class)->prepareSend(campaign: $campaign);

        app(MarketingCampaignRepository::class)->updateMarketingCampaign(
            marketingCampaign: $campaign,
            status: MarketingCampaignStatus::SENT,
            sentAt: now(),
        );
    }

    /**
     * @param array $shortcodes
     * @return array
     */
    public function validateOutgoingEmail(
        array $shortcodes,
    ): array
    {
        $valid = true;
        $message = 'Outgoing Email Valid';
        $context = [];

        $anyUnmatchedShortcodes = in_array(null, $shortcodes, true);

        if ($anyUnmatchedShortcodes) {
            $valid = false;

            $invalidShortcodes = collect($shortcodes)->filter(function ($value, $key) {
                return empty($value);
            })->toArray();

            $context = $invalidShortcodes;

            $message = "Used shortcodes with no replacement value found";
        }

        return [
            $valid,
            $message,
            $context,
        ];
    }
}
