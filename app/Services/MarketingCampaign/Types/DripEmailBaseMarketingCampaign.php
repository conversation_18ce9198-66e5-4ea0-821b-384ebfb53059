<?php

namespace App\Services\MarketingCampaign\Types;

use App\Enums\GlobalConfigurationKey;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\MarketingCampaign\Sending\MarketingCampaignSendingService;
use Exception;
use Illuminate\Support\Str;

class DripEmailBaseMarketingCampaign extends BaseMarketingCampaignType
{
    public function configValidation(): array
    {
        return [
            'sender_domain'     => ['required', 'string'],
            'sender_local'      => ['required', 'string'],
            'sent_at'           => ['nullable', 'date'],
            'send_time'         => ['required', 'array'],
            'span_type'         => ['required', 'string', 'in:day,week,month,year'],
            'span_value'        => ['required', 'integer', 'min:1'],
            'email_template_id' => ['required', 'integer', 'exists:' . EmailTemplate::TABLE . ',' . EmailTemplate::FIELD_ID],
        ];
    }

    public function type(): MarketingCampaignType
    {
        return MarketingCampaignType::DRIP_EMAIL;
    }

    public function prepareConfiguration(array $configuration, array $filters): array
    {
        if (!isset($configuration['sender_local']) || !isset($configuration['sender_domain'])) {
            $globalConfigurationRepository = app(GlobalConfigurationRepository::class);

            $marketingConfig = $globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::MARKETING)?->toArray()['data'];

            $fromEmail        = $marketingConfig[GlobalConfigurationMarketingField::FROM_EMAIL->value] ?? null;

            if (empty($fromEmail)) {
                throw new Exception('From email address global config not set.');
            }

            $configuration['sender_local'] = Str::of($fromEmail)->before('@');
            $configuration['sender_domain'] = Str::of($fromEmail)->after('@');
        }

        $configuration['filters'] = $filters;

        $configuration['sent_at'] =  $configuration['sent_at'] ?: now();

        return $configuration;
    }

    public function send(MarketingCampaign $campaign): void
    {
        app(MarketingCampaignSendingService::class)->prepareSend(campaign: $campaign);

        //todo: temporary measure while we have drip campaigns in sent status
        app(MarketingCampaignRepository::class)->updateMarketingCampaign(
            marketingCampaign: $campaign,
            status: MarketingCampaignStatus::ACTIVE,
        );
    }

    public function initialise(
        MarketingCampaign $campaign,
        array $filters,
        int $authorId
    ): void
    {
        $repository = app(MarketingCampaignRepository::class);

        $repository->updateMarketingCampaign(
            marketingCampaign: $campaign,
            authorId: $authorId,
            status: MarketingCampaignStatus::ACTIVE,
            externalReference: Str::uuid()->toString(),
            externalCode: Str::uuid()->toString(),
        );
    }
}
