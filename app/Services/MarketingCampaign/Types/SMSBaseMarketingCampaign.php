<?php

namespace App\Services\MarketingCampaign\Types;

use App\DTO\MarketingCampaign\MarketingSMS;
use App\DTO\SMS;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Jobs\MarketingCampaign\SendMarketingSMS;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\PhoneNumber\PhoneNumberService;
use App\Services\Shortcode\ShortcodeImplementation\EmailMarketingShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Fluent;

class SMSBaseMarketingCampaign extends BaseMarketingCampaignType
{
    const string MESSAGING_SERVICE = 'messaging_service';
    const string USED_SHORTCODES   = 'used_shortcodes';
    const string BASE_WEBHOOK_URL  = 'base_webhook_url';
    const string MESSAGE           = 'message';

    public function configValidation(): array
    {
        return [
            'sent_at'                 => ['exclude_if:campaign.status,sent', 'required', 'date'],
            'message'                 => ['required', 'string'],
            'send_time.start.hours'   => ['required', 'integer', 'min:8', 'max:20'], //tcpa requirement (allows till 9pm but provide margin for job execution)
            'send_time.start.minutes' => ['required', 'integer', 'min:0', 'max:60'],
            'send_time.start.seconds' => ['required', 'integer', 'min:0', 'max:60'],
            'send_time.end.hours'     => ['required', 'integer', 'min:8', 'max:20'], //tcpa requirement (allows till 9pm but provide margin for job execution)
            'send_time.end.minutes'   => ['required', 'integer', 'min:0', 'max:60'],
            'send_time.end.seconds'   => ['required', 'integer', 'min:0', 'max:60'],
        ];
    }

    public function type(): MarketingCampaignType
    {
        return MarketingCampaignType::SMS;
    }

    /**
     * @throws Exception
     */
    public function getSendingConfiguration(MarketingCampaign $campaign): array
    {
        $configuration = new Fluent($campaign->{MarketingCampaign::FIELD_CONFIGURATION} ?? []);

        $message = $configuration->get('message');

        $shortcodeService = app(ShortcodeReplacerService::class);

        $usedShortcodes = $shortcodeService->getAllShortcodes(rawText: $message);

        $globalConfigurationRepository = app(GlobalConfigurationRepository::class);
        $marketingConfig = $globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::MARKETING)?->toArray()['data'];

        $marketingMessagingServiceID = $marketingConfig[GlobalConfigurationMarketingField::SMS_SERVICE_ID->value] ?? null;
        $smsWebhookRoute = $marketingConfig[GlobalConfigurationMarketingField::SMS_WEBHOOK_URL->value] ?? null;
        if (empty($marketingMessagingServiceID)) {
            throw new Exception('SMS Service ID global config not set.');
        }

        return [
            self::MESSAGE           => $message,
            self::MESSAGING_SERVICE => $marketingMessagingServiceID,
            self::USED_SHORTCODES   => $usedShortcodes,
            self::BASE_WEBHOOK_URL  => $smsWebhookRoute,
        ];
    }

    public function prepareMarketingCampaignConsumerToSend(
        MarketingCampaignConsumer $campaignConsumer,
        array                     $sendingConfiguration
    ): mixed
    {
        $consumer = $campaignConsumer->consumer;
        $phoneNumberService = app(PhoneNumberService::class);

        $formattedPhone = $phoneNumberService->formatPhoneNumber(phone: $consumer->phone);
        $useCase = EmailMarketingShortcodeUseCase::fromMarketingCampaignConsumer($campaignConsumer);
        $baseWebhook = Arr::get($sendingConfiguration, self::BASE_WEBHOOK_URL);
        $shortcodeMap = $useCase->compile(Arr::get($sendingConfiguration, self::USED_SHORTCODES))->toArray();
        $message = Arr::get($sendingConfiguration, self::MESSAGE);

        $invalidMessage = '';

        try {
            //todo: give more descript reason for invalid phone
            $valid = $phoneNumberService->validateConsumerPhone(consumer: $consumer);

            if (!$valid) {
                $invalidMessage = 'Invalid phone number';
            }

            $message = app(ShortcodeReplacerService::class)
                ->process(
                    rawText: $message,
                    shortcodeMap: $shortcodeMap
                );
        } catch (Exception $exception) {
            $valid = false;
            $invalidMessage = $exception->getMessage();
        }

        if ($valid) {
            return new MarketingSMS(
                toPhone: $formattedPhone,
                from: Arr::get($sendingConfiguration, self::MESSAGING_SERVICE),
                message: $message,
                marketingConsumerId: $campaignConsumer->id,
                fromType: SMS::FROM_TYPE_SERVICE,
                meta: [
                    'StatusCallback' => "$baseWebhook?mcc_id=$campaignConsumer->id",
                    'shortenUrls' => true,
                ],
            );
        } else {
            MarketingLogService::log(
                message: $invalidMessage,
                namespace: MarketingLogType::MARKETING_INTERNAL_SEND,
                level: LogLevel::ERROR,
                context: [
                    'marketing_campaign_id' => $campaignConsumer->marketing_campaign_id,
                    'consumer_id'           => $consumer->id,
                ],
                relations: [
                    $campaignConsumer,
                    $campaignConsumer->marketingCampaign,
                ]
            );

            return null;
        }
    }

    /**
     * @param MarketingCampaign $campaign
     * @param MarketingSMS $dto
     * @param Carbon $delay
     * @return void
     */
    public function dispatch(MarketingCampaign $campaign, mixed $dto, Carbon $delay): void
    {
        SendMarketingSMS::dispatch(
            $dto
        )->delay($delay);
    }
}
