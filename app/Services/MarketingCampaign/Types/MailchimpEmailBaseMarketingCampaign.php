<?php

namespace App\Services\MarketingCampaign\Types;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\MarketingCampaign;
use App\Services\MarketingCampaign\MarketingLogService;

class MailchimpEmailBaseMarketingCampaign extends BaseMarketingCampaignType
{
    public function configValidation(): array
    {
        return [];
    }

    public function type(): MarketingCampaignType
    {
        return MarketingCampaignType::MAILCHIMP_EMAIL;
    }

    public function send(MarketingCampaign $campaign): void
    {
        MarketingLogService::log(
            message: 'Attempted to send mailchimp email',
            namespace: MarketingLogType::MARKETING_CAMPAIGN_SENT,
            level: LogLevel::ERROR,
            relations: [$campaign],
        );
    }
}
