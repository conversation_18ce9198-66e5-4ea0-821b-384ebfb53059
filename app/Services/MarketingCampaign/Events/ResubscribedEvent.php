<?php

namespace App\Services\MarketingCampaign\Events;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingLogService;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class ResubscribedEvent extends MarketingCampaignConsumerMarketingEvent
{
    public function __construct(
        int $marketingCampaignConsumerId,
    )
    {
        parent::__construct($marketingCampaignConsumerId);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    function trigger(): void
    {
        $service = app(MarketingCampaignConsumerRepository::class);

        $mcc = $service->get(id: $this->marketingCampaignConsumerId);

        MarketingLogService::log(
            message: 'Consumer Subscribed',
            namespace: MarketingLogType::WEBHOOK,
            level: LogLevel::ALERT,
            context: [],
            relations: [$mcc, $mcc->marketingCampaign],
        );
    }
}
