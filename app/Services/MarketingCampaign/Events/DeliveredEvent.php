<?php

namespace App\Services\MarketingCampaign\Events;

use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class DeliveredEvent extends MarketingCampaignConsumerMarketingEvent
{
    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    function trigger(): void
    {
        $service = app(MarketingCampaignConsumerRepository::class);

        $mcc = $service->get(id: $this->marketingCampaignConsumerId);

        $service->updateMarketingCampaignConsumer(
            campaignConsumer: $mcc,
            status: MarketingCampaignConsumerStatus::DELIVERED,
            deliveredAt: now(),
        );
    }
}
