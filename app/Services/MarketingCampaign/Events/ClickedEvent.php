<?php

namespace App\Services\MarketingCampaign\Events;

use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;

class ClickedEvent extends MarketingCampaignConsumerMarketingEvent
{

    function trigger(): void
    {
        $service = app(MarketingCampaignConsumerRepository::class);

        $mcc = $service->get(id: $this->marketingCampaignConsumerId);

        $service->updateMarketingCampaignConsumer(
            campaignConsumer: $mcc,
            clickedAt: now(),
        );
    }
}
