<?php

namespace App\Services\MarketingCampaign\Events;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingLogService;

class ComplaintEvent extends MarketingCampaignConsumerMarketingEvent
{

    function trigger(): void
    {
        $service = app(MarketingCampaignConsumerRepository::class);

        $mcc = $service->get(id: $this->marketingCampaignConsumerId);

        MarketingLogService::log(
            message: 'Consumer Complained',
            namespace: MarketingLogType::WEBHOOK,
            level: LogLevel::ALERT,
            context: [],
            relations: [$mcc, $mcc->marketingCampaign],
        );
    }
}
