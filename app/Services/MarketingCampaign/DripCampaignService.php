<?php

namespace App\Services\MarketingCampaign;

use App\Builders\Odin\ConsumerSearchBuilder;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Filterables\Consumer\ConsumerFilterableService;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Carbon\Carbon;

class DripCampaignService
{
    public function __construct(
        protected ConsumerFilterableService $filterableService,
        protected MarketingCampaignService $marketingCampaignService,
    )
    {
    }

    public function getEstimate(
        array $filters,
        Carbon $startAt,
        int $sendAfterDays,
    ): Collection
    {
        $filters['consumer-date-range'] = [
            'from' => $startAt->clone()->subDays($sendAfterDays),
            'to' => $startAt->clone()->subDays($sendAfterDays)->addWeek(),
        ];

        $query = ConsumerSearchBuilder::query()
            ->excludeCloned(true)
            ->excludeConsumerProductStatus(ConsumerProduct::STATUS_CANCELLED)
            ->getDripCampaignEstimate();

        return $this->filterableService->runQuery($filters, $query)->get();
    }

    public function getTargets(
        array $filters,
        Carbon $startRange,
        Carbon $endRange,
        int $marketingCampaignId,
    ): Collection|\Illuminate\Support\Collection
    {
        $filters['consumer-date-range'] = [
            'from' => $startRange,
            'to' => $endRange,
        ];

        return $this->marketingCampaignService->initialiseMarketingCampaignConsumers(
            ['filters' => $filters],
            $marketingCampaignId,
        );
    }

    /**
     * @param string $span
     * @param int $number
     * @return int
     * @throws Exception
     */
    public function processAnniversary(
        string $span,
        int $number,
    ): int
    {
        return match ($span) {
            'day' => $number,
            'week' => $number * 7,
            'month' => $number * 30,
            'year' => $number * 365,
            default => throw new Exception('Span not implemented'),
        };
    }

    /**
     * @throws Exception
     */
    public function getTargetDay(
        string $span,
        int $number,
    ): Carbon
    {
        $anniversary = $this->processAnniversary($span, $number);

        return now()->subDays($anniversary);
    }

}
