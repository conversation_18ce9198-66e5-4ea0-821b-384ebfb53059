<?php

namespace App\Services\MarketingCampaign;

use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\GlobalConfigurationKey;
use App\Enums\GlobalConfigurationMailchimpField;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\HelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use InvalidArgumentException;

class MarketingCampaignConsumerService
{
    public function __construct(
        protected MarketingCampaignConsumerRepository $repository,
        protected MarketingCampaignService $marketingCampaignService,
        protected GlobalConfigurationRepository $globalConfigurationRepository,
    )
    {
    }

    /**
     * @param int|null $marketingCampaignId
     * @param string|null $name
     * @param MarketingCampaignConsumerStatus|null $status
     * @return Builder
     */
    public function listMarketingCampaigns(
        ?int $marketingCampaignId,
        ?string $name = null,
        ?MarketingCampaignConsumerStatus $status = null,
        ?bool $revalidated = null,
    ): Builder
    {
        return $this->repository->listMarketingCampaignConsumers(
            marketingCampaignId: $marketingCampaignId,
            consumerName: $name,
            status: $status,
            revalidated: $revalidated
        );
    }

    /**
     * @param string $consumerReference
     * @param string|null $externalReference
     * @return MarketingCampaignConsumer
     */
    public function retrieveOrCreateMarketingConsumer(
        string $consumerReference,
        ?string $externalReference = null,
    ): MarketingCampaignConsumer
    {
        if (empty($externalReference)) {
            $marketingCampaignConsumer = $this->repository->getMarketingCampaignConsumerByConsumerReference($consumerReference);
        }

        if (!empty($externalReference)) {
            $marketingCampaign = MarketingCampaign::query()->where(MarketingCampaign::FIELD_EXTERNAL_REFERENCE, $externalReference)->first();

            if (empty($marketingCampaign)) {
                $marketingCampaignConsumer = $this->initialiseUnknownCampaignAndConsumer($consumerReference, $externalReference);
            } else {
                try {
                    $marketingCampaignConsumer = $this->repository->getByConsumerExternalMarketingReference(
                        $consumerReference,
                        $marketingCampaign->{MarketingCampaign::FIELD_EXTERNAL_REFERENCE}
                    );
                } catch (ModelNotFoundException $exception) {
                    $marketingCampaignConsumer = $this->createMarketingCampaignConsumer(
                        $marketingCampaign->{MarketingCampaign::FIELD_ID},
                        $consumerReference,
                    );
                }
            }
        }

        return $marketingCampaignConsumer;
    }

    /**
     * @param string $consumerReference
     * @param string $externalReference
     * @return MarketingCampaignConsumer
     */
    public function initialiseUnknownCampaignAndConsumer(string $consumerReference, string $externalReference): MarketingCampaignConsumer
    {
        $marketingCampaign = $this->marketingCampaignService->initialiseUnknownMarketingCampaign($externalReference);

        return $this->createMarketingCampaignConsumer(
            $marketingCampaign->{MarketingCampaign::FIELD_ID},
            $consumerReference,
        );
    }

    /**
     * @param string $marketingCampaignId
     * @param string $consumerReference
     * @param string|null $externalReference
     * @return MarketingCampaignConsumer
     */
    public function createMarketingCampaignConsumer(
        string $marketingCampaignId,
        string $consumerReference,
        ?string $externalReference = null,
    ): MarketingCampaignConsumer
    {
        return $this->repository->createMarketingCampaignConsumer(
            $marketingCampaignId,
            $consumerReference,
            $externalReference,
        );
    }

    /**
     * @param MarketingCampaignConsumer $marketingCampaignConsumer
     * @return string
     */
    public function getMarketingCampaignConsumerCallbackUrl(MarketingCampaignConsumer $marketingCampaignConsumer): string
    {
        $mailchimpConfigPayload = $this->globalConfigurationRepository->getConfigurationPayload(
            GlobalConfigurationKey::MARKETING
        )?->toArray() ?? [];

        $baseCallbackUrl = fluent(
            $mailchimpConfigPayload
        )->get('data.'.GlobalConfigurationMarketingField::CALLBACK_URL->value);

        if (empty($baseCallbackUrl)) {
            throw new InvalidArgumentException("Unable to retrieve base callback url, may need to be set in the global config");
        }

        $consumerReference = $marketingCampaignConsumer->{MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE};

        $marketingCampaign = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN};

        $marketingCampaignExternalReference = $marketingCampaign->{MarketingCampaign::FIELD_EXTERNAL_REFERENCE};

        if (empty($marketingCampaignExternalReference)) {
            throw new InvalidArgumentException("No Marketing Campaign External Reference for Marketing Campaign ID: $marketingCampaign->id");
        }

        return "$baseCallbackUrl/$consumerReference?campaign=$marketingCampaignExternalReference";
    }

    public function findMarketingCampaignConsumerByPhone(string $phone): ?MarketingCampaignConsumer
    {
        $formattedPhone = HelperService::formatUSPhoneNumber($phone);

        return MarketingCampaignConsumer::query()
            ->select([MarketingCampaignConsumer::TABLE .'.*'])
            ->join(Consumer::TABLE,
                MarketingCampaignConsumer::TABLE .'.'. MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE,
                '=',
                Consumer::TABLE .'.'. Consumer::FIELD_REFERENCE,
            )
            ->join(MarketingCampaign::TABLE,
                MarketingCampaignConsumer::TABLE .'.'. MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID,
                '=',
                MarketingCampaign::TABLE .'.'. MarketingCampaign::FIELD_ID
            )
            ->whereIn(MarketingCampaign::TABLE .'.'. MarketingCampaign::FIELD_TYPE, [MarketingCampaignType::SMS, MarketingCampaignType::DRIP_SMS])
            ->whereNotNull([
                MarketingCampaignConsumer::TABLE .'.'. MarketingCampaignConsumer::FIELD_SENT_AT,
                MarketingCampaignConsumer::TABLE .'.'. MarketingCampaignConsumer::FIELD_DELIVERED_AT
            ])->where(function (Builder $builder) use ($formattedPhone) {
                $builder->where(Consumer::TABLE .'.'. Consumer::FIELD_PHONE,'like', "%$formattedPhone%")
                    ->orWhere(Consumer::TABLE .'.'. Consumer::FIELD_FORMATTED_PHONE,'like', "%$formattedPhone%");
            })
            ->orderByDesc(MarketingCampaignConsumer::TABLE .'.'.MarketingCampaignConsumer::FIELD_ID)
            ->first();
    }
}

