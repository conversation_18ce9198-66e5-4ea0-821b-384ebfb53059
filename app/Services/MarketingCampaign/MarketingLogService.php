<?php

namespace App\Services\MarketingCampaign;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogRelationType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\BaseModel;
use App\Models\MarketingLog;
use App\Repositories\MarketingCampaign\MarketingLogRelationRepository;
use App\Repositories\MarketingCampaign\MarketingLogRepository;
use Illuminate\Database\Eloquent\Builder;

class MarketingLogService
{
    public function __construct(
        protected MarketingLogRepository $repository,
    )
    {
    }

    /**
     * @param array<LogLevel>|null $levels
     * @param string|null $message
     * @param int|null $relationId
     * @param MarketingLogRelationType|null $relationType
     * @return Builder
     */
    public function listLogs(
        ?array  $levels = null,
        ?string $message = null,
        ?int $relationId = null,
        ?MarketingLogRelationType $relationType = null,
    ): Builder
    {
        return $this->repository->listLogs(
            message: $message,
            levels: $levels,
            relationId: $relationId,
            relationType: $relationType
        );
    }

    /**
     * @param int $logId
     * @return MarketingLog
     */
    public function getLog(
        int $logId
    ): MarketingLog
    {
        return $this->repository->getLog(
            logId: $logId
        );
    }

    /**
     * @param string $message
     * @param MarketingLogType $namespace
     * @param LogLevel $level
     * @param string|null $stackTrace
     * @param array|null $context
     * @param array $relations
     * @return void
     */
    public static function log(
        string           $message,
        MarketingLogType $namespace,
        LogLevel         $level,
        ?string          $stackTrace = null,
        ?array           $context = null,
        array            $relations = [],
    ): void
    {
        $log = MarketingLogRepository::log(
            message: $message,
            namespace: $namespace,
            level: $level,
            stackTrace: $stackTrace,
            context: $context,
        );

        collect($relations)->each(function ($relation) use ($log) {
            $relationType = null;
            $relationId = null;

            if ($relation instanceof BaseModel) {
                $relationType = $relation::class;
                $relationId = $relation->id;
            }

            if (is_array($relation)) {
                $relationType = $relation['class'];
                $relationId = $relation['id'];
            }

            if (empty($relationType) || empty($relationId)) {
                logger()->error("failed to retrieve type/id for log relation");
                return;
            }

            MarketingLogRelationRepository::create(
                marketingLogId: $log->id,
                relationType: $relationType,
                relationId: $relationId,
            );
        });
    }
}

