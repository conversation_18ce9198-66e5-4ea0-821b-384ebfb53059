<?php

namespace App\Services\MarketingCampaign\CallbackPayload;

use App\ConsumerProcessing\Services\ConsumerProductAllocationService;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\DTO\DTOContract;
use App\Enums\ConsumerProductChannel;
use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\OptInCompanyRepository;
use App\Repositories\RecycledLeads\RecycledLeadsRepository;
use App\Services\ConsumerRevalidationService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;

abstract class CallbackPayloadType implements DTOContract
{
    const string TYPE = 'type';
    const string DATA = 'data';
    const int AUTO_SELL_MAX = 2;
    public abstract function getType(): MarketingCampaignCallbackType;

    public static abstract function getInputs(): array;

    public static abstract function fromMarketingCampaign(MarketingCampaign $campaign): self;

    /**
     * @param MarketingCampaignConsumer $marketingCampaignConsumer
     * @return array
     */
    public abstract function render(MarketingCampaignConsumer $marketingCampaignConsumer): array;

    /**
     * override this function to modify how we revalidate consumers based on the callback type used
     *
     * @param MarketingCampaignConsumer $marketingCampaignConsumer
     * @param array|null $responseData
     * @return void
     * @throws BindingResolutionException
     */
    public function revalidate(
        MarketingCampaignConsumer $marketingCampaignConsumer,
        ?array $responseData = [],
    ): void
    {
        $companyIds = Arr::get($responseData, 'company_ids', []);
        $campaignIds = Arr::get($responseData, 'campaign_ids', []);

        /** @var Consumer $consumer */
        $consumer = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_CONSUMER};

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        /** @var RecycledLeadsRepository $recycledLeadsRepository */
        $recycledLeadsRepository = app(RecycledLeadsRepository::class);

        /** @var ConsumerRevalidationService $consumerRevalidationService */
        $consumerRevalidationService = app(ConsumerRevalidationService::class);

        /** @var OptInCompanyRepository $optInCompanyRepository */
        $optInCompanyRepository = app(OptInCompanyRepository::class);

        $newConsumerProduct = $recycledLeadsRepository->cloneConsumerProduct(
            consumerProduct: $consumerProduct,
            channel: ConsumerProductChannel::REVALIDATED_FROM_EMAIL_CAMPAIGN
        );

        $optInCompanyRepository->addOptInCompanies(
            $newConsumerProduct,
            collect($companyIds)
        );

        $newConsumer = $newConsumerProduct->consumer;

        $newConsumer->update([
            Consumer::FIELD_CLASSIFICATION => empty($newConsumer->phone)
                ? Consumer::CLASSIFICATION_EMAIL_ONLY
                : Consumer::CLASSIFICATION_VERIFIED_VIA_REVALIDATION_EMAIL
        ]);

        $newConsumerProduct->contact_requests = max(count($companyIds), $newConsumerProduct->contact_requests);

        $newConsumerProduct->save();

        $marketingCampaignConsumer->cloned_consumer_product_id = $newConsumerProduct->id;

        $marketingCampaignConsumer->save();

        if (empty($campaignIds) || empty($newConsumer->phone)) {
            $consumerRevalidationService->addInInitialQueue(
                consumer: $newConsumer,
            );

            return;
        }

        /** @var ProductAssignmentRepository $productAssignmentRepository */
        $productAssignmentRepository = app(ProductAssignmentRepository::class);

        $excludedCompanyIds = $productAssignmentRepository->getAllAssignmentsForConsumer($consumer)
            ->pluck(ProductAssignment::FIELD_COMPANY_ID)->values()->toArray();

        /** @var ConsumerProjectProcessingService $consumerProjectProcessingService */
        $consumerProjectProcessingService = app(ConsumerProjectProcessingService::class);

        $consumerProject = $consumerProjectProcessingService->prepareConsumerProject(
            consumer: $newConsumerProduct->consumer,
            address: $newConsumerProduct->address,
            excludedCompanies: $excludedCompanyIds
        );

        $potentialCampaigns = $consumerProjectProcessingService->getAvailableCampaigns($consumerProject);

        $validatedPotentialCampaigns = $potentialCampaigns->filter(fn(CompanyCampaign $campaign) => in_array($campaign->id, $campaignIds));

        $proposedAssignments = collect();

        if ($validatedPotentialCampaigns->isNotEmpty()) {
            $assignmentStrategyContract = app(MultiProductAssignmentStrategyContract::class);

            $proposedAssignments = $assignmentStrategyContract->calculate(
                $consumerProject,
                $validatedPotentialCampaigns,
                $consumerProjectProcessingService->getPotentialProductTypes($consumerProject),
                []
            )->filter(function(ProposedProductAssignment $proposedProductAssignment) use ($excludedCompanyIds)  {
                return !$proposedProductAssignment->isExistingAssignment && !in_array($proposedProductAssignment->companyId, $excludedCompanyIds);
            });
        }

        $validatedCampaignIds = $proposedAssignments->map(function (ProposedProductAssignment $proposedProductAssignment) {
            return $proposedProductAssignment->campaignId;
        })->values()->toArray();

        if (!empty($validatedCampaignIds)) {
            $consumerProductAllocationService = app(ConsumerProductAllocationService::class);

            $consumerProductAllocationService->allocateConsumerProduct(
                consumerProduct: $newConsumerProduct,
                campaignIds: $validatedCampaignIds,
                saleType: SaleTypes::mapAllocationsToVerifiedType(count($validatedCampaignIds)),
                qualityTier: QualityTier::STANDARD
            );
        } else {
            $consumerRevalidationService->addInInitialQueue(
                consumer: $newConsumer,
            );
        }

        $optInCompanyRepository->addOptInCompanies(
            $newConsumerProduct,
            collect($companyIds)
        );
    }

    public function toProperArray(): ?array
    {
        return [
            self::TYPE => $this->getType()->value,
            self::DATA => $this->toArray()
        ];
    }
}
