<?php

namespace App\Services\MarketingCampaign\CallbackPayload;

use App\ConsumerProcessing\Services\ConsumerProductAllocationService;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\ConsumerProductChannel;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\RecycledLeads\RecycledLeadsRepository;
use App\Services\ConsumerRevalidationService;
use App\Services\MarketingCampaign\MarketingLogService;
use Illuminate\Support\Arr;

class SolarValidatePayload extends CallbackPayloadType
{
    const string QUESTIONS              = 'questions';
    const string QUESTION_INSTALLED     = 'installed';
    const string QUESTION_ELECTRIC_COST = 'electric_cost';
    const string QUESTION_QUOTES        = 'quotes';
    const string QUESTION_ADDRESS       = 'address';

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [];
    }

    public function getType(): MarketingCampaignCallbackType
    {
        return MarketingCampaignCallbackType::SOLAR_VALIDATE;
    }

    public static function getInputs(): array
    {
        return [];
    }

    public static function fromMarketingCampaign(MarketingCampaign $campaign): self
    {
        return new self();
    }

    /**
     * @inheritDoc
     */
    public function render(MarketingCampaignConsumer $marketingCampaignConsumer): array
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $marketingCampaignConsumer->consumer->consumerProducts()->first();
        $electricCost = Arr::get($consumerProduct->consumerProductData->payload, SolarConfigurableFields::ELECTRIC_COST->value);
        $contactRequests = $consumerProduct->contact_requests;
        $address = $consumerProduct->address;

        return [
            self::QUESTIONS => [
                [
                    'label'       => 'What is your most recent utility bill?',
                    'key'         => self::QUESTION_ELECTRIC_COST,
                    'placeholder' => $electricCost ?? "300",
                    'type'        => 'integer',
                    'options'     => [
                        'min'    => 50,
                        'max'    => 600,
                        'step'   => 10,
                        'prefix' => '$',
                    ],
                ],
                [
                    'label'       => 'How many prices from solar installers would you like?',
                    'key'         => self::QUESTION_QUOTES,
                    'placeholder' => $contactRequests,
                    'type'        => 'options',
                    'options'     => [
                        ['name' => '1', 'value' => 1],
                        ['name' => '2', 'value' => 2],
                        ['name' => '3', 'value' => 3],
                        ['name' => '4', 'value' => 4],
                    ],
                ],
                [
                    'label'       => 'Do you currently have a solar system installed?',
                    'key'         => self::QUESTION_INSTALLED,
                    'placeholder' => false,
                    'type'        => 'boolean',
                ],
                [
                    'label'       => 'Is this still your current address?',
                    'key'         => self::QUESTION_ADDRESS,
                    'placeholder' => [
                        'same_address' => true,
                        'address_1'    => $address->address_1,
                        'address_2'    => $address->address_2,
                        'city'         => $address->city,
                        'state'        => $address->state,
                        'zip_code'     => $address->zip_code,
                        'country'      => $address->country,
                    ],
                    'type'        => 'address',
                    'options'     => []
                ],
            ]
        ];
    }

    public static function fromArray(array $array): \App\DTO\DTOContract
    {
        return new self();
    }

    public function revalidate(MarketingCampaignConsumer $marketingCampaignConsumer, ?array $responseData = []): void
    {
        /** @var Consumer $consumer */
        $consumer = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_CONSUMER};

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        /** @var RecycledLeadsRepository $recycledLeadsRepository */
        $recycledLeadsRepository = app(RecycledLeadsRepository::class);

        /** @var ConsumerRevalidationService $consumerRevalidationService */
        $consumerRevalidationService = app(ConsumerRevalidationService::class);

        $installedSolar = Arr::get($responseData, self::QUESTION_INSTALLED);

        if ($installedSolar === false) {
            $newConsumerProduct = $recycledLeadsRepository->cloneConsumerProduct(
                consumerProduct: $consumerProduct,
                channel: ConsumerProductChannel::REVALIDATED_FROM_EMAIL_CAMPAIGN
            );

            $consumerProductData = $newConsumerProduct->consumerProductData;

            $consumerProductData->payload = [
                ...$consumerProductData->payload,
                SolarConfigurableFields::ELECTRIC_COST->value => (string)Arr::get($responseData, self::QUESTION_ELECTRIC_COST)
            ];

            $consumerProductData->save();

            $newConsumerProduct->contact_requests = (int)Arr::get($responseData, self::QUESTION_QUOTES, self::AUTO_SELL_MAX);
            $newConsumerProduct->save();

            $addressData = Arr::get($responseData, self::QUESTION_ADDRESS);

            $sameAddress = Arr::get($addressData, 'same_address', true);

            if (!$sameAddress) {
                $addressRepository = app(AddressRepository::class);

                $zipCodeSetRepository = app(ZipCodeSetRepository::class);

                $address = $addressRepository->createAddressFromAttributes(
                    data: [
                        Address::FIELD_ADDRESS_1 => Arr::get($addressData, 'address_1'),
                        Address::FIELD_ADDRESS_2 => Arr::get($addressData, 'address_2'),
                        Address::FIELD_CITY      => Arr::get($addressData, 'city'),
                        Address::FIELD_STATE     => Arr::get($addressData, 'state'),
                        Address::FIELD_ZIP_CODE  => Arr::get($addressData, 'zip_code'),
                        Address::FIELD_COUNTRY   => Arr::get($addressData, 'country'),
                        Address::FIELD_LATITUDE  => Arr::get($addressData, 'lat'),
                        Address::FIELD_LONGITUDE => Arr::get($addressData, 'long'),
                        Address::FIELD_PLACE_ID  => Arr::get($addressData, 'place_id'),
                        Address::FIELD_UTC       => $zipCodeSetRepository->getUtc(zipCode: Arr::get($addressData, 'zip_code')),
                        Address::FIELD_IMPORTED  => false,
                    ]);

                $newConsumerProduct->address_id = $address->id;
                $newConsumerProduct->save();
            }

            $marketingCampaignConsumer->cloned_consumer_product_id = $newConsumerProduct->id;

            $marketingCampaignConsumer->save();

            $newConsumer = $newConsumerProduct->consumer;

            $newConsumer->update([
                Consumer::FIELD_CLASSIFICATION => empty($newConsumer->phone)
                    ? Consumer::CLASSIFICATION_EMAIL_ONLY
                    : Consumer::CLASSIFICATION_VERIFIED_VIA_REVALIDATION_EMAIL
            ]);

            if ($sameAddress) {
                $productAssignmentRepository = app(ProductAssignmentRepository::class);

                $excludedCompanyIds = $productAssignmentRepository->getAllAssignmentsForConsumerIncludingClones(
                    consumer: $consumer,
                    delivered: true,
                )->pluck(ProductAssignment::FIELD_COMPANY_ID)->values()->toArray();

                /** @var ConsumerProjectProcessingService $consumerProjectProcessingService */
                $consumerProjectProcessingService = app(ConsumerProjectProcessingService::class);

                $consumerProject = $consumerProjectProcessingService->prepareConsumerProject(
                    consumer: $newConsumerProduct->consumer,
                    address: $newConsumerProduct->address,
                    excludedCompanies: $excludedCompanyIds
                );

                $potentialCampaigns = $consumerProjectProcessingService->getAvailableCampaigns($consumerProject);

                $proposedAssignments = collect([]);

                if ($potentialCampaigns->isNotEmpty()) {
                    $assignmentStrategyContract = app(MultiProductAssignmentStrategyContract::class);

                    $proposedAssignments = $assignmentStrategyContract->calculate(
                        $consumerProject,
                        $potentialCampaigns,
                        $consumerProjectProcessingService->getPotentialProductTypes($consumerProject),
                        []
                    )->filter(function(ProposedProductAssignment $proposedProductAssignment) use ($excludedCompanyIds)  {
                        return !$proposedProductAssignment->isExistingAssignment && !in_array($proposedProductAssignment->companyId, $excludedCompanyIds);
                    });
                }

                $validatedCampaignIds = $proposedAssignments->map(function (ProposedProductAssignment $proposedProductAssignment) {
                    return $proposedProductAssignment->campaignId;
                })->values()->toArray();


                if (!empty($validatedCampaignIds)) {
                    $consumerProductAllocationService = app(ConsumerProductAllocationService::class);

                    MarketingLogService::log(
                        message: 'Allocating Directly, consumer revalidated + campaigns found :)',
                        namespace: MarketingLogType::CONSUMER_RESPONSE,
                        level: LogLevel::NOTICE,
                        context: [
                            'responseData' => $responseData,
                            'validatedCampaignIds' => $validatedCampaignIds,
                        ],
                        relations: [$marketingCampaignConsumer]
                    );

                    $consumerProductAllocationService->allocateConsumerProduct(
                        consumerProduct: $newConsumerProduct,
                        campaignIds: $validatedCampaignIds,
                        saleType: SaleTypes::mapAllocationsToVerifiedType(count($validatedCampaignIds)),
                        qualityTier: QualityTier::STANDARD
                    );
                } else {
                    MarketingLogService::log(
                        message: 'Gone to Pending Review Queue, no campaigns found :(',
                        namespace: MarketingLogType::CONSUMER_RESPONSE,
                        level: LogLevel::NOTICE,
                        context: [
                            'responseData' => $responseData,
                        ],
                        relations: [$marketingCampaignConsumer]
                    );

                    $consumerRevalidationService->addInInitialQueue(
                        consumer: $newConsumer,
                    );
                }

            } else {
                MarketingLogService::log(
                    message: 'Gone to Pending Review Queue, new Address',
                    namespace: MarketingLogType::CONSUMER_RESPONSE,
                    level: LogLevel::NOTICE,
                    context: [
                        'responseData' => $responseData,
                        'old_address' => $consumerProduct->address->toArray(),
                    ],
                    relations: [$marketingCampaignConsumer]
                );

                $consumerRevalidationService->addInInitialQueue(
                    consumer: $newConsumer,
                );
            }
        } else {
            MarketingLogService::log(
                message: 'Consumer has solar installed',
                namespace: MarketingLogType::CONSUMER_RESPONSE,
                level: LogLevel::NOTICE,
                context: [
                    'responseData' => $responseData,
                ],
                relations: [$marketingCampaignConsumer]
            );
        }
    }
}
