<?php

namespace App\Services\MarketingCampaign\CallbackPayload;

use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;

class BaseCallbackPayload extends CallbackPayloadType
{

    public function getType(): MarketingCampaignCallbackType
    {
        return MarketingCampaignCallbackType::BASE;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [];
    }

    public static function fromArray(array $array): self
    {
        return new self();
    }

    public static function fromMarketingCampaign(MarketingCampaign $campaign): self
    {
        return new self();
    }

    public function toProperArray(): ?array
    {
        return null;
    }

    public static function getInputs(): array
    {
        return [];
    }

    /**
     * @param MarketingCampaignConsumer $marketingCampaignConsumer
     * @return array
     */
    public function render(MarketingCampaignConsumer $marketingCampaignConsumer): array
    {
        return [];
    }
}
