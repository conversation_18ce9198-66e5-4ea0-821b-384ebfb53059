<?php

namespace App\Services\MarketingCampaign\CallbackPayload;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\GlobalConfigurationKey;
use App\Enums\GlobalConfigurationMarketingCallbackField;
use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Factories\Workflows\WorkflowPayloadFactory;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Services\Shortcode\ShortcodeImplementation\EmailMarketingShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use App\Workflows\WorkflowEvent;
use Exception;
use Illuminate\Support\Arr;

class SimpleSingleSlideCallbackPayload extends CallbackPayloadType
{
    const string HEADING    = 'heading';
    const string SUBHEADING = 'subheading';

    public function __construct(
        protected string  $heading,
        protected ?string $subheading = null,
    )
    {}

    public function getType(): MarketingCampaignCallbackType
    {
        return MarketingCampaignCallbackType::SIMPLE_SINGLE_SLIDE;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::HEADING    => $this->heading,
            self::SUBHEADING => $this->subheading,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            heading: Arr::get($array, self::HEADING),
            subheading: Arr::get($array, self::SUBHEADING),
        );
    }

    public static function fromMarketingCampaign(MarketingCampaign $campaign): self
    {
        $payload = $campaign->{MarketingCampaign::FIELD_CALLBACK_PAYLOAD}[self::DATA];

        return new self(
          heading: $payload[self::HEADING],
          subheading: $payload[self::SUBHEADING],
        );
    }

    public static function getInputs(): array
    {
        /** @var GlobalConfigurationRepository $globalConfigurationService */
        $globalConfigurationService = app()->make(GlobalConfigurationRepository::class);

        $payload = $globalConfigurationService->getConfigurationPayload(GlobalConfigurationKey::MARKETING_CALLBACK_PLACEHOLDERS)?->toArray()['data'];

        $heading = $payload[GlobalConfigurationMarketingCallbackField::SIMPLE_SINGLE_SLIDE_HEADING->value] ?? null;
        $subheading = $payload[GlobalConfigurationMarketingCallbackField::SIMPLE_SINGLE_SLIDE_SUBHEADING->value] ?? null;

        return [
            [
                'field' => self::HEADING,
                'type' => 'text',
                'label' => 'Heading',
                ...($heading ? ['placeholder' => $heading] : [])
            ],
            [
                'field' => self::SUBHEADING,
                'type' => 'text',
                'label' => 'Subheading',
                ...($subheading ? ['placeholder' => $subheading] : [])
            ],
        ];
    }

    /**
     * @param MarketingCampaignConsumer $marketingCampaignConsumer
     * @return array
     * @throws Exception
     */
    public function render(MarketingCampaignConsumer $marketingCampaignConsumer): array
    {
        /** @var ShortcodeReplacerService $replacerService */
        $replacerService = app()->make(ShortcodeReplacerService::class);

        $usedShortcodes = collect($replacerService->getAllShortcodes($this->heading))
            ->merge($replacerService->getAllShortcodes($this->subheading))
            ->unique()
            ->values()
            ->toArray();

        $useCase = EmailMarketingShortcodeUseCase::fromMarketingCampaignConsumer($marketingCampaignConsumer);

        $compiled = $useCase->compile($usedShortcodes)->toArray();

        $processedHeading = $replacerService->process($this->heading,$compiled);

        $processedSubheading    = !empty($this->subheading) ? $replacerService->process($this->subheading, $compiled) : null;

        return [
            self::HEADING => $processedHeading,
            self::SUBHEADING => $processedSubheading,
        ];
    }
}