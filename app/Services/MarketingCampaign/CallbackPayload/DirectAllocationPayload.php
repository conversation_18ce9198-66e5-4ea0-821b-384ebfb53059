<?php

namespace App\Services\MarketingCampaign\CallbackPayload;

use App\Enums\ConsumerProductChannel;
use App\Enums\GlobalConfigurationKey;
use App\Enums\GlobalConfigurationMarketingCallbackField;
use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\RecycledLeads\RecycledLeadsRepository;
use App\Services\ConsumerRevalidationService;
use App\Services\Shortcode\ShortcodeImplementation\EmailMarketingShortcodeUseCase;
use App\Services\Shortcode\ShortcodeReplacerService;
use Exception;
use Illuminate\Support\Arr;

class DirectAllocationPayload extends CallbackPayloadType
{
    const string HEADING = 'heading';
    const string BODY    = 'body';

    public function __construct(
        protected string  $heading,
        protected ?string $body = null,
    )
    {
    }

    public function getType(): MarketingCampaignCallbackType
    {
        return MarketingCampaignCallbackType::DIRECT_ALLOCATION;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::HEADING => $this->heading,
            self::BODY    => $this->body,
        ];
    }

    public static function fromArray(?array $array): self
    {
        if (!$array) {
            return new self(
                heading: '',
                body   : '',
            );
        }

        return new self(
            heading: Arr::get($array, self::HEADING),
            body   : Arr::get($array, self::BODY),
        );
    }

    public static function fromMarketingCampaign(MarketingCampaign $campaign): self
    {
        $payload = $campaign->{MarketingCampaign::FIELD_CALLBACK_PAYLOAD}[self::DATA];

        return new self(
            heading: $payload[self::HEADING],
            body   : $payload[self::BODY],
        );
    }

    public static function getInputs(): array
    {
        /** @var GlobalConfigurationRepository $globalConfigurationService */
        $globalConfigurationService = app()->make(GlobalConfigurationRepository::class);

        $payload = $globalConfigurationService->getConfigurationPayload(GlobalConfigurationKey::MARKETING_CALLBACK_PLACEHOLDERS)?->toArray()['data'];

        $heading = $payload[GlobalConfigurationMarketingCallbackField::DIRECT_ALLOCATION_HEADING->value] ?? null;
        $body = $payload[GlobalConfigurationMarketingCallbackField::DIRECT_ALLOCATION_BODY->value] ?? null;

        return [
            [
                'field' => self::HEADING,
                'type' => 'text',
                'label' => 'Heading',
                ...($heading ? ['placeholder' => $heading] : [])
            ],
            [
                'field' => self::BODY,
                'type' => 'textarea',
                'label' => 'Body',
                ...($body ? ['placeholder' => $body] : [])
            ],
        ];
    }

    /**
     * @throws Exception
     */
    public function render(MarketingCampaignConsumer $marketingCampaignConsumer): array
    {
        /** @var ShortcodeReplacerService $replacerService */
        $replacerService = app()->make(ShortcodeReplacerService::class);

        $usedShortcodes = collect($replacerService->getAllShortcodes($this->heading))
            ->merge($replacerService->getAllShortcodes($this->body))
            ->unique()
            ->values()
            ->toArray();

        $useCase = EmailMarketingShortcodeUseCase::fromMarketingCampaignConsumer($marketingCampaignConsumer);

        $compiled = $useCase->compile($usedShortcodes)->toArray();

        $processedHeading = $replacerService->process($this->heading,$compiled);

        $processedBody    = !empty($this->body) ? $replacerService->process($this->body, $compiled) : null;

        return [
            self::HEADING => $processedHeading,
            self::BODY    => $processedBody,
        ];
    }

    public function revalidate(MarketingCampaignConsumer $marketingCampaignConsumer, ?array $responseData = []): void
    {
        /** @var Consumer $consumer */
        $consumer = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_CONSUMER};

        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        /** @var RecycledLeadsRepository $recycledLeadsRepository */
        $recycledLeadsRepository = app(RecycledLeadsRepository::class);

        /** @var ConsumerRevalidationService $consumerRevalidationService */
        $consumerRevalidationService = app(ConsumerRevalidationService::class);

        $newConsumerProduct = $recycledLeadsRepository->cloneConsumerProduct(
            consumerProduct: $consumerProduct,
            channel: ConsumerProductChannel::REVALIDATED_FROM_EMAIL_CAMPAIGN
        );

        $newConsumerProduct->contact_requests = self::AUTO_SELL_MAX;
        $newConsumerProduct->save();

        $marketingCampaignConsumer->cloned_consumer_product_id = $newConsumerProduct->id;

        $marketingCampaignConsumer->save();

        $newConsumer = $newConsumerProduct->consumer;

        $newConsumer->update([
            Consumer::FIELD_CLASSIFICATION => empty($newConsumer->phone)
                ? Consumer::CLASSIFICATION_EMAIL_ONLY
                : Consumer::CLASSIFICATION_VERIFIED_VIA_REVALIDATION_EMAIL
        ]);

        $consumerRevalidationService->addInInitialQueue(
            consumer: $newConsumer,
        );
    }
}
