<?php

namespace App\Services;

use Illuminate\Support\Number;

class CurrencyService
{
    /**
     * TODO - Replace and Remove
     * @param float $amount
     * @param string $toCurrency
     * @return string
     */
    public function formatToCurrency(float $amount, string $toCurrency = 'USD'): string
    {
        return Number::currency($amount, $toCurrency);
    }

    /**
     * @param float $amount
     * @param string $toCurrency
     * @return string
     */
    public static function toCurrency(float $amount, string $toCurrency = 'USD'): string
    {
        return Number::currency($amount, $toCurrency);
    }

    /**
     * @param float $amount
     * @param string $toCurrency
     * @return string
     */
    public static function fromAtomicToCurrency(float $amount, string $toCurrency = 'USD'): string
    {
        return self::toCurrency($amount / 100, $toCurrency);
    }
}
