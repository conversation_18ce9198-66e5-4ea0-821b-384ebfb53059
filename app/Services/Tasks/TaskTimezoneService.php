<?php

namespace App\Services\Tasks;

use App\Enums\SupportedTimezones;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Sales\Task;
use App\Models\User;
use App\Services\OutreachCadence\TimeZoneHelperService;

class TaskTimezoneService
{
    public function __construct(protected TimeZoneHelperService $timeZoneHelperService) {}

    /**
     * <PERSON>les calculating the timezone for a given task.
     *
     * @param Task $task
     * @return SupportedTimezones
     */
    public function calculateTimezoneForTask(Task $task): SupportedTimezones
    {
        return $task->manual ? $this->calculateForManualTask($task) : $this->calculateForSystemTask($task);
    }

    /**
     * <PERSON>les calculating timezone for a manually created task.
     *
     * Currently, manually created tasks can only be made for companies or will default to the assigned users' timezone.
     * Using this knowledge, we can skip checking the workflow for the potential cases, and go straight to company/user.
     *
     * @param Task $task
     * @return SupportedTimezones
     */
    protected function calculateForManualTask(Task $task): SupportedTimezones
    {
        if (array_key_exists('company_id', $task->payload ?? []) && is_numeric($task->payload['company_id']))
            return $this->calculateTimezoneForCompany($task->payload['company_id']) ?? $this->calculateTimezoneForAssignedUser($task->assigned_user_id);

        return $this->calculateTimezoneForAssignedUser($task->assigned_user_id);
    }

    /**
     * Handles calculating timezone for a system generated task.
     *
     * As system generated tasks can be for a number of workflow events, we'll test for multiple cases here.
     * Starting with if the workflow payload contains a company id, followed by a campaign id followed by a lead id and
     * then falling back to the assigned users' timezone. Further cases can be added in the future.
     *
     * @param Task $task
     * @return SupportedTimezones
     */
    protected function calculateForSystemTask(Task $task): SupportedTimezones
    {
        $event = $task->runningWorkflow?->payload?->event;

        if($event && $event->has('company_id') && is_numeric($event->get('company_id'))) {
            $timezone = $this->calculateTimezoneForCompany($event->get('company_id'));

            if($timezone)
                return $timezone;
        }

        if($event && $event->has('campaign_id') && is_numeric($event->get('campaign_id'))) {
            $timezone = $this->calculateTimezoneForCampaign($event->get('campaign_id'));

            if($timezone)
                return $timezone;
        }

        if($event && $event->has('lead_id') && is_numeric($event->get('lead_id'))) {
            $timezone = $this->calculateTimezoneForConsumer($event->get('lead_id'));

            if($timezone)
                return $timezone;
        }

        return $this->calculateTimezoneForAssignedUser($task->assigned_user_id);
    }

    /**
     * Handles calculating the timezone for a given company. This will use the company's default address, or first
     * address if there's no default. Otherwise, we return null.
     *
     * @param int|null $companyId
     * @return SupportedTimezones|null
     */
    protected function calculateTimezoneForCompany(?int $companyId): ?SupportedTimezones
    {
        if($companyId === null) return null;

        /** @var Company|null $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $companyId)->first();

        if(!$company)
            return null;

        $zipcode = $company->locations()->first()?->address?->zip_code;

        if(!$zipcode)
            return null;

        return SupportedTimezones::fromOffsetHour(
            $this->timeZoneHelperService->getTimezoneOffsetDataFromZipcode($zipcode)["standard_utc_offset"]
        );
    }

    /**
     * Handles calculating the timezone for a given campaign. This will lookup the company from the campaign, and then
     * use the company logic.
     *
     * @param int|null $campaignId
     * @return SupportedTimezones|null
     * @see self::calculateTimezoneForCompany()
     */
    protected function calculateTimezoneForCampaign(?int $campaignId): ?SupportedTimezones
    {
        if($campaignId === null) return null;

        /** @var LeadCampaign|null $campaign */
        $campaign = LeadCampaign::query()->find($campaignId);

        return $campaign ? $this->calculateTimezoneForCompany($campaign->company_id) : null;
    }

    /**
     * Calculates the timezone for a given consumer based on the address they have provided.
     *
     * @param int|null $consumerId
     * @return SupportedTimezones|null
     */
    protected function calculateTimezoneForConsumer(?int $consumerId): ?SupportedTimezones
    {
        if($consumerId === null) return null;

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $consumerId)->first();

        if(!$consumer)
            return null;

        return SupportedTimezones::fromOffsetHour(
            $this->timeZoneHelperService->getTimezoneOffsetDataFromZipcode($consumer->legacyLead->address->zipcode)["standard_utc_offset"]
        );
    }

    /**
     * Calculates the timezone for a given user that the task is assigned to. This uses their personal settings.
     *
     * @param int $userId
     * @return SupportedTimezones
     */
    protected function calculateTimezoneForAssignedUser(int $userId): SupportedTimezones
    {
        /** @var User|null $user */
        $user = User::query()->find($userId);

        return $user?->timezone ?? SupportedTimezones::EASTERN;
    }
}
