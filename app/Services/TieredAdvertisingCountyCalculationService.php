<?php

namespace App\Services;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Reports\CountyCoverageReport\CountyCoverageReportColumnEnum;
use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Odin\IndustryConfiguration;
use App\Models\ReportData;
use App\Models\TieredAdvertisingCampaign;
use App\Models\TieredAdvertisingConfiguration;
use App\Models\TieredAdvertisingCounty;
use App\Models\TieredAdvertisingInstance;
use App\Models\USZipCode;
use App\Services\Advertising\TieredAdvertisingService;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class TieredAdvertisingCountyCalculationService
{
    /**
     * @param TieredAdvertisingService $tieredAdsService
     */
    public function __construct(protected TieredAdvertisingService $tieredAdsService)
    {}

    /**
     * Calculates tiered advertising counties for all platforms and industries, if limits are given only stores for given platform and industry
     * @param string $platformLimit
     * @param string $industryLimit
     * @param int|null $instanceIdLimit
     * @return void
     */
    public function calculateTieredAdvertisingCounties(string $platformLimit = '', string $industryLimit = '', ?int $instanceIdLimit = null): void
    {
        $estimatedRevenuePerLead = $this->getEstimatedRevenuePerLead();
        $googleAverageRevenuePerSmsVerified = $this->getSevenDayAverageGoogleSmsVerifiedLeads();
        $tieredAdvertisingCampaigns = $this->getTieredAdvertisingCampaigns();
        $platformRoas = $this->getPlatformRoas();

        $countyIds = $this->getCountyIds();
        $industryIds = $this->getIndustryIds($industryLimit);
        $instanceIds = $this->getInstanceIds($instanceIdLimit);
        $advertisingPlatforms = $this->getAdvertisingPlatforms($platformLimit);
        $tieredAdvertisingCountyData = [];

        foreach ($countyIds as $countyId) {
            foreach ($industryIds as $industryId) {
                $erpl = $estimatedRevenuePerLead[$countyId][$industryId][0]->{EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE} ?? 0;
                $arpl = $googleAverageRevenuePerSmsVerified[$countyId][$industryId][0]->{CountyCoverageReportColumnEnum::REVENUE_PER_SOLD_LEAD->value} ?? 0;
                $grpl = $googleAverageRevenuePerSmsVerified[$countyId][$industryId][0]->{CountyCoverageReportColumnEnum::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD->value} ?? 0;
                $negativeZips = $estimatedRevenuePerLead[$countyId][$industryId][0]->{EstimatedRevenuePerLeadByLocation::FIELD_NEGATIVE_ZIP_CODES} ?? 0;

                $mixedRPL = 0;
                // Mixed RPL Calculation
                // - If we have an ERPL, a GRPL, and an ARPL, use average of the three
                // - If we have an ERPL and an ARPL but no GRPL, use average of ERPL and ARPL
                // - If we have an ERPL and a GRPL but no ARPL, use average of ERPL and GRPL - should not be possible without ARPL calculation error (leaving here as fallback)
                // - If we only have an ERPL, use ERPL
                // - If we don't have an ERPL, mixed RPL is 0 - no current coverage
                if ($erpl > 0 && $arpl > 0 && $grpl > 0) {
                    $mixedRPL = ($erpl + $arpl + $grpl) / 3;
                } else if ($erpl > 0 && $arpl > 0) {
                    $mixedRPL = ($erpl + $arpl) / 2;
                } else if ($erpl > 0 && $grpl > 0) {
                    $mixedRPL = ($erpl + $grpl) / 2;
                } else if ($erpl > 0) {
                    $mixedRPL = $erpl;
                }

                foreach ($advertisingPlatforms as $advertisingPlatform) {
                    // Get instances for this platform / industry
                    $platformInstanceIds = $instanceIds[$advertisingPlatform->value] ?? [];
                    $platformInstanceIds = $platformInstanceIds[$industryId] ?? [];

                    // If no instances, use default instance with null id
                    if (count($platformInstanceIds) === 0)
                        $platformInstanceIds[] = null;

                    foreach ($platformInstanceIds as $instanceId) {
                        $roas = $platformRoas[$advertisingPlatform->value][$industryId][$instanceId] ?? TieredAdvertisingService::DEFAULT_ROAS;
                        if (!$roas)
                            $roas = TieredAdvertisingService::DEFAULT_ROAS;

                        // Target CPA is RPL / ROAS
                        $tcpaBid = $mixedRPL / $roas;

                        // Store how we computed tcpa bid
                        $data = [
                            'erpl'     => round($erpl, 2),
                            'arpl'     => round($arpl, 2),
                            'grpl'     => round($grpl, 2),
                            'mixedRPL' => round($mixedRPL, 2),
                            'roas'     => $roas,
                        ];

                        // Compute tier for this ad platform
                        $availableCampaigns = $tieredAdvertisingCampaigns[$industryId][$advertisingPlatform->value][$instanceId] ?? collect();

                        // If no campaigns available, continue
                        if ($availableCampaigns->count() === 0) {
                            continue;
                        }

                        // These will be null if county does not fit into campaign bucket match
                        $tieredAdsCampaignId = null;
                        $tier = null;

                        // Match county's calculated TCPA to campaign by bounding information
                        foreach ($availableCampaigns as $campaign) {
                            if (!$campaign->{TieredAdvertisingCampaign::FIELD_UPPER_BOUND}) {
                                // Check top campaign with no upper bound
                                if ($tcpaBid > $campaign->{TieredAdvertisingCampaign::FIELD_LOWER_BOUND}) {
                                    $tieredAdsCampaignId = $campaign->{TieredAdvertisingCampaign::FIELD_ID};
                                    $tier = $campaign->{TieredAdvertisingCampaign::FIELD_TIER};
                                    break;
                                }
                            } else {
                                // Check all campaigns with upper and lower bounds
                                if ($tcpaBid >= $campaign->{TieredAdvertisingCampaign::FIELD_LOWER_BOUND} &&
                                    $tcpaBid <= $campaign->{TieredAdvertisingCampaign::FIELD_UPPER_BOUND}) {
                                    $tieredAdsCampaignId = $campaign->{TieredAdvertisingCampaign::FIELD_ID};
                                    $tier = $campaign->{TieredAdvertisingCampaign::FIELD_TIER};
                                    break;
                                }
                            }
                        }

                        if ($tieredAdsCampaignId) {
                            // Add to new entries for tiered advertising counties table
                            $tieredAdvertisingCountyData[] = [
                                TieredAdvertisingCounty::FIELD_LOCATION_ID                    => $countyId,
                                TieredAdvertisingCounty::FIELD_INDUSTRY_ID                    => $industryId,
                                TieredAdvertisingCounty::FIELD_PLATFORM                       => $advertisingPlatform->value,
                                TieredAdvertisingCounty::FIELD_TCPA_BID                       => round($tcpaBid, 2),
                                TieredAdvertisingCounty::FIELD_TIER                           => $tier,
                                TieredAdvertisingCounty::FIELD_TIERED_ADVERTISING_CAMPAIGN_ID => $tieredAdsCampaignId,
                                TieredAdvertisingCounty::FIELD_NEGATIVE_ZIP_CODES             => json_encode($negativeZips),
                                TieredAdvertisingCounty::FIELD_DATA                           => json_encode($data),
                                TieredAdvertisingCounty::CREATED_AT                           => now(),
                            ];
                        }
                    }
                }
            }
        }

        $deleteQuery = TieredAdvertisingCounty::query();

        if ($platformLimit !== '')
            $deleteQuery = $deleteQuery->where(TieredAdvertisingCounty::FIELD_PLATFORM, $platformLimit);

        if ($industryLimit !== '') {
            $industryId = IndustryEnum::fromSlug($industryLimit)->model()->{Industry::FIELD_ID};
            $deleteQuery = $deleteQuery->where(TieredAdvertisingCounty::FIELD_INDUSTRY_ID, $industryId);
        }

        DB::transaction(function () use ($tieredAdvertisingCountyData, $deleteQuery) {
            // Clear county links to campaign tiers
            $deleteQuery->delete();

            foreach (array_chunk($tieredAdvertisingCountyData, 500) as $chunk) {
                TieredAdvertisingCounty::insert($chunk);
            }
        });

        // Update tiered ads campaign coverage population after re-allocating counties
        $this->updateAdvertisingCampaignPopulations($platformLimit, $industryLimit, $instanceIdLimit);
    }

    /**
     * Returns the live estimated revenue per lead for each county that is calculated every few minutes
     * @return Collection
     */
    public function getEstimatedRevenuePerLead(): Collection
    {
        return EstimatedRevenuePerLeadByLocation::query()
            ->join(Location::TABLE, fn (JoinClause $join) =>
                $join->on(Location::TABLE.'.'.Location::ID, '=', EstimatedRevenuePerLeadByLocation::TABLE.'.'.EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID)
                    ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_COUNTY)
            )->get([
                EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID,
                EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE,
                EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID,
                EstimatedRevenuePerLeadByLocation::FIELD_NEGATIVE_ZIP_CODES,
            ])->groupBy([
                EstimatedRevenuePerLeadByLocation::FIELD_LOCATION_ID,
                EstimatedRevenuePerLeadByLocation::FIELD_INDUSTRY_ID,
            ]);
    }

    /**
     * Seven day average of google revenue per sms verified lead
     * @return Collection
     */
    public function getSevenDayAverageGoogleSmsVerifiedLeads(): Collection
    {
        return ReportData::query()
            ->where(ReportData::FIELD_REPORT, ReportData::REPORT_COUNTY_COVERAGE)
            ->where(ReportData::FIELD_RELATION_TYPE, ReportData::RELATION_TYPE_COUNTY_LOCATION)
            ->get([
                ReportData::FIELD_RELATION_ID,
                ReportData::FIELD_DATA.'->'.CountyCoverageReportColumnEnum::INDUSTRY_ID.' as industry_id',
                ReportData::FIELD_DATA.'->'.CountyCoverageReportColumnEnum::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD->value.' as '.CountyCoverageReportColumnEnum::GOOGLE_REVENUE_PER_SMS_VERIFIED_LEAD->value,
                ReportData::FIELD_DATA.'->'.CountyCoverageReportColumnEnum::REVENUE_PER_SOLD_LEAD->value.' as '.CountyCoverageReportColumnEnum::REVENUE_PER_SOLD_LEAD->value,
            ])->groupBy([
                ReportData::FIELD_RELATION_ID,
                'industry_id',
            ]);
    }

    /**
     * @return array
     */
    public function getPlatformRoas(): array
    {
        $configs = TieredAdvertisingConfiguration::query()
            ->get([
                TieredAdvertisingConfiguration::FIELD_PLATFORM,
                TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID,
                TieredAdvertisingConfiguration::FIELD_INSTANCE_ID,
                TieredAdvertisingConfiguration::FIELD_ROAS,
            ])->groupBy([
                TieredAdvertisingConfiguration::FIELD_PLATFORM,
                TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID,
                TieredAdvertisingConfiguration::FIELD_INSTANCE_ID,
            ]);

        $platformRoas = [];
        foreach ($configs as $platform => $configPlatform) {
            foreach ($configPlatform as $industry => $configIndustry) {
                foreach ($configIndustry as $instance => $configInstance) {
                    $platformRoas[$platform][$industry][$instance] = $configInstance->first()->{TieredAdvertisingConfiguration::FIELD_ROAS};
                }
            }
        }
        return $platformRoas;
    }

    /**
     * Returns industry ids with an active future campaign, only given industry if limit is not empty
     *
     * @param string $industryLimit
     * @return Collection
     */
    public function getIndustryIds(string $industryLimit): Collection
    {
        if ($industryLimit === '') {
            return Industry::query()
                ->join(IndustryConfiguration::TABLE, IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_INDUSTRY_ID, '=', Industry::TABLE.'.'.Industry::FIELD_ID)
                ->where(IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
                ->get([
                    Industry::TABLE.'.'.Industry::FIELD_ID,
                ])->pluck(Industry::FIELD_ID);
        } else {
            return collect([IndustryEnum::fromSlug($industryLimit)->model()->{Industry::FIELD_ID}]);
        }
    }

    /**
     * Gets the instance ids grouped by platform and industry, can be limited to a single instance by passing id
     *
     * @param int|null $instanceId
     * @return array
     */
    public function getInstanceIds(?int $instanceId = null): array
    {
        // Get all instances for industry and service
        $instanceIdsQuery = TieredAdvertisingInstance::query();

        // Limit to single instance if passed
        if ($instanceId)
            $instanceIdsQuery = $instanceIdsQuery->where(TieredAdvertisingInstance::FIELD_ID, $instanceId);

        $instances = $instanceIdsQuery->get([
            TieredAdvertisingInstance::FIELD_ID,
            TieredAdvertisingInstance::FIELD_INDUSTRY_ID,
            TieredAdvertisingInstance::FIELD_PLATFORM,
        ])->groupBy([
            TieredAdvertisingInstance::FIELD_PLATFORM,
            TieredAdvertisingInstance::FIELD_INDUSTRY_ID,
        ]);

        $instanceIds = [];
        foreach ($instances as $platform => $instancePlatform) {
            foreach ($instancePlatform as $industry => $instanceIndustry) {
                $instanceIds[$platform][$industry] = $instanceIndustry->pluck(TieredAdvertisingInstance::FIELD_ID);
            }
        }
        return $instanceIds;
    }

    /**
     * Returns all platforms if no limit given, otherwise only returns limited platform
     *
     * @param string $platformLimit
     * @return array
     */
    public function getAdvertisingPlatforms(string $platformLimit): array
    {
        if ($platformLimit === '') {
            return AdvertisingPlatform::cases();
        } else {
            return [AdvertisingPlatform::from($platformLimit)];
        }
    }

    /**
     * Returns all the tiered advertising campaigns with bounds information for county assignment
     * @return Collection
     */
    public function getTieredAdvertisingCampaigns(): Collection
    {
        return TieredAdvertisingCampaign::query()
            ->get([
                TieredAdvertisingCampaign::FIELD_ID,
                TieredAdvertisingCampaign::FIELD_INDUSTRY_ID,
                TieredAdvertisingCampaign::FIELD_PLATFORM,
                TieredAdvertisingCampaign::FIELD_TCPA_BID,
                TieredAdvertisingCampaign::FIELD_LOWER_BOUND,
                TieredAdvertisingCampaign::FIELD_UPPER_BOUND,
                TieredAdvertisingCampaign::FIELD_TIER,
                TieredAdvertisingCampaign::FIELD_INSTANCE_ID,
            ])
            ->groupBy([
                TieredAdvertisingCampaign::FIELD_INDUSTRY_ID,
                TieredAdvertisingCampaign::FIELD_PLATFORM,
                TieredAdvertisingCampaign::FIELD_INSTANCE_ID,
            ]);
    }

    /**
     * Returns ids of location models for counties
     * @return Collection
     */
    public function getCountyIds(): Collection
    {
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->get([Location::ID])->pluck(Location::ID);
    }

    /**
     * Compute population covered by each tiered ads campaign and store in the tiered ads campaign table
     *
     * @param string $platformLimit
     * @param string $industryLimit
     * @param int|null $instanceIdLimit
     * @return void
     */
    public function updateAdvertisingCampaignPopulations(string $platformLimit, string $industryLimit, ?int $instanceIdLimit = null): void
    {
        // Update population column in tiered advertising campaigns
        $populationsQuery = TieredAdvertisingCampaign::query()
            ->leftJoin(TieredAdvertisingCounty::TABLE, TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_TIERED_ADVERTISING_CAMPAIGN_ID, '=', TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_ID)
            ->leftJoin(Location::TABLE, fn (JoinClause $join) =>
            $join->on(Location::TABLE.'.'.Location::ID, '=', TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_LOCATION_ID)
                ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_COUNTY)
            )
            ->leftJoin(Location::TABLE.' as l_zip', fn (JoinClause $join) =>
            $join->on('l_zip.'.Location::STATE_ABBREVIATION, '=', Location::TABLE.'.'.Location::STATE_ABBREVIATION)
                ->on('l_zip.'.Location::COUNTY_KEY, '=', Location::TABLE.'.'.Location::COUNTY_KEY)
                ->where('l_zip.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            )
            ->leftJoin(USZipCode::TABLE, fn (JoinClause $join) =>
            $join->on(USZipCode::TABLE.'.'.USZipCode::FIELD_ZIP_CODE, '=', 'l_zip.'.Location::ZIP_CODE)
                ->where(USZipCode::FIELD_CITY_TYPE, 'D')
            )
            ->groupBy(TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_ID);

        if ($platformLimit !== '')
            $populationsQuery = $populationsQuery->where(TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_PLATFORM, $platformLimit);

        if ($industryLimit !== '') {
            $industryId = IndustryEnum::fromSlug($industryLimit)->model()->{Industry::FIELD_ID};
            $populationsQuery = $populationsQuery->where(TieredAdvertisingCounty::TABLE.'.'.TieredAdvertisingCounty::FIELD_INDUSTRY_ID, $industryId);
        }

        $populations = $populationsQuery->get([
                TieredAdvertisingCampaign::TABLE.'.'.TieredAdvertisingCampaign::FIELD_ID,
                DB::raw('SUM('.USZipCode::TABLE.'.'.USZipCode::FIELD_POPULATION.') as '.TieredAdvertisingCampaign::FIELD_COVERED_POPULATION),
            ]);

        // Update covered populations in tiered ads campaigns table
        foreach ($populations as $population) {
            TieredAdvertisingCampaign::where(TieredAdvertisingCampaign::FIELD_ID, $population->{TieredAdvertisingCampaign::FIELD_ID})->update([
                TieredAdvertisingCampaign::FIELD_COVERED_POPULATION => $population->{TieredAdvertisingCampaign::FIELD_COVERED_POPULATION},
            ]);
        }
    }
}
