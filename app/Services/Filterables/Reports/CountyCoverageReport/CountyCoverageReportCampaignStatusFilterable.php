<?php

namespace App\Services\Filterables\Reports\CountyCoverageReport;

use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CountyCoverageReportCampaignStatusFilterable extends MultiSelectFilterableOption
{
    const string ID = 'leads-report-campaign-status';

    public function __construct()
    {
        $this->model = CompanyCampaign::class;
        $this->name = "Campaign Status";
        $this->id = self::ID;
        $this->withRelations = null;

        $campaignOptions = CampaignStatus::getAsKeyValueSelectArray();
        if (array_key_exists('Paused Permanently', $campaignOptions)) {
            $campaignOptions['Off (Excluded by Default)'] = $campaignOptions['Paused Permanently'];
            unset($campaignOptions['Paused Permanently']);
        }
        if (array_key_exists('Paused Temporarily', $campaignOptions)) {
            $campaignOptions['Paused Temporarily (Excluded by Default)'] = $campaignOptions['Paused Temporarily'];
            unset($campaignOptions['Paused Temporarily']);
        }

        $this->options = $campaignOptions;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
