<?php

namespace App\Services\Filterables\Reports\CountyCoverageReport;

use App\Models\Campaigns\CompanyCampaign;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CountyCoverageReportEmptyLocationsFilterable extends MultiSelectFilterableOption
{
    const string ID = 'leads-report-empty-locations';
    const string EXCLUDE_KEY = 'exclude';

    public function __construct()
    {
        $this->model = CompanyCampaign::class;
        $this->name = "Empty Locations";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = ['Exclude' => self::EXCLUDE_KEY];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
