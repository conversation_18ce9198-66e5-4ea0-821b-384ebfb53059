<?php

namespace App\Services\Filterables\Reports\CountyCoverageReport;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Consumer;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class CountyCoverageReportFilterableService extends BaseFilterableService
{
    const string FILTERABLE_CATEGORY = 'county-coverage-report';

    protected array $filters = [
        CountyCoverageReportLocationFilterable::class,
        CountyCoverageReportCampaignStatusFilterable::class,
        CountyCoverageReportCampaignBudgetFilterable::class,
        CountyCoverageReportEmptyLocationsFilterable::class,
        CountyCoverageReportCampaignTargetingFilterable::class,
    ];

    protected string $baseModel = CompanyCampaign::class;

    /**
     * @param array $results
     * @param Builder|null $baseQuery
     * @return Builder
     */
    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? Consumer::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }
}
