<?php

namespace App\Services\Filterables\Reports\CountyCoverageReport;

use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CountyCoverageReportCampaignTargetingFilterable extends MultiSelectFilterableOption
{
    const string ID     = 'leads-report-campaign-targeting';
    const int ZIP_CODE  = 1;
    const int COUNTY    = 0;
    public function __construct()
    {
        $this->model = CompanyCampaign::class;
        $this->name = "Campaign Targeting";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = [
            'Zip Code Targeted' => self::ZIP_CODE,
            'County Level'      => self::COUNTY,
        ];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
