<?php

namespace App\Services\Filterables\Reports\CountyCoverageReport;

use App\Enums\Operator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\CheckboxListDualOperatorFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CountyCoverageReportCampaignBudgetFilterable extends CheckboxListDualOperatorFilterableOption
{
    const string ID = 'company-campaign-budget';
    const KEY_NO_LIMIT = 'no_limit';
    const KEY_VOLUME = 'volume';
    const KEY_COST = 'cost';

    public function __construct()
    {
        $this->model = CompanyCampaign::class;
        $this->name = "Campaign Budget";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = [
            'noLimit' => [
                'key' => self::KEY_NO_LIMIT,
                'label' => 'No Limit',
                'selected' => false,
            ],
            'volume' => [
                'key' => self::KEY_VOLUME,
                'label' => 'Volume',
                'selected' => false,
                'operator' => Operator::GREATER_THAN_OR_EQUAL_TO->value,
                'value' => null,
                'units' => 'leads per day'
            ],
            'cost' => [
                'key' => self::KEY_COST,
                'label' => 'Cost',
                'selected' => false,
                'operator' => Operator::GREATER_THAN_OR_EQUAL_TO->value,
                'value' => null,
                'units' => 'cost per day'
            ],
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    /**
     * @throws Exception
     */
    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
