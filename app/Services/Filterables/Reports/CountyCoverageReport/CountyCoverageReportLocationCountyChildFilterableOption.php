<?php

namespace App\Services\Filterables\Reports\CountyCoverageReport;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\Consumer;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\Company\CompanyOfficeLocationFilterable;
use App\Services\Filterables\Consumer\ConsumerLocationFilterable;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CountyCoverageReportLocationCountyChildFilterableOption extends MultiSelectFilterableOption
{
    protected array $options = [];
    const string NAME = 'County';

    public function __construct()
    {
        $this->model         = Consumer::class;
        $this->name          = self::NAME;
        $this->id            = CountyCoverageReportLocationFilterable::CHILD_FILTERABLE_COUNTY_ID;
        $this->withRelations = null;

        $this->options = [];
    }

    /**
     * @param array $filterByKeys
     * @return array|null
     */
    public function updateOptions(array $filterByKeys): ?array
    {
        $newOptions = Location::query()
            ->select(Location::STATE, Location::COUNTY, Location::COUNTY_KEY)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->whereIn(Location::STATE, $filterByKeys)
            ->get()
            ->reduce(function (array $output, Location $county) {
                $output[$county->county] = $county->county;
                return $output;
            }, []);

        $this->options = $newOptions;

        return $newOptions;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}

