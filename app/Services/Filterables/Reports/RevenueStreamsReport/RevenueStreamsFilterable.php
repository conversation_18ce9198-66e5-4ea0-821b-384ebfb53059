<?php

namespace App\Services\Filterables\Reports\RevenueStreamsReport;

use App\Models\RevenueStream;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class RevenueStreamsFilterable extends MultiSelectFilterableOption
{
    const string ID     = 'revenue-streams';

    public function __construct()
    {
        $this->model = RevenueStream::class;
        $this->name = "Revenue Streams";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = RevenueStream::query()
            ->get([RevenueStream::FIELD_NAME, RevenueStream::FIELD_ID])
            ->pluck(RevenueStream::FIELD_ID, RevenueStream::FIELD_NAME)
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
