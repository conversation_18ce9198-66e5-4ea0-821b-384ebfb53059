<?php

namespace App\Services\Filterables\Reports\RevenueStreamsReport;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Consumer;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class RevenueStreamsReportFilterableService extends BaseFilterableService
{
    const string FILTERABLE_CATEGORY = 'revenue-streams-report';

    protected array $filters = [
        RevenueStreamsFilterable::class,
        RevenueStreamsIndustryFilterable::class,
        RevenueStreamsPlatformFilterable::class,
        RevenueStreamsAdvertiserFilterable::class,
    ];

    protected string $baseModel = CompanyCampaign::class;

    /**
     * @param array $results
     * @param Builder|null $baseQuery
     * @return Builder
     */
    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? Consumer::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }
}
