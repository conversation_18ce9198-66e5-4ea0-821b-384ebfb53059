<?php

namespace App\Services\Filterables\Reports\RevenueStreamsReport;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\RevenueStream;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class RevenueStreamsPlatformFilterable extends MultiSelectFilterableOption
{
    const string ID     = 'platform';

    public function __construct()
    {
        $this->model = RevenueStream::class;
        $this->name = "Platform";
        $this->id = self::ID;
        $this->withRelations = null;

        $platforms = AdvertisingPlatform::getAsKeyValueSelectArray();
        $platforms['Organic'] = 'organic';

        $this->options = $platforms;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
