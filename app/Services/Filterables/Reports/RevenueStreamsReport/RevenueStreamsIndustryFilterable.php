<?php

namespace App\Services\Filterables\Reports\RevenueStreamsReport;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\RevenueStream;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class RevenueStreamsIndustryFilterable extends MultiSelectFilterableOption
{
    const string ID     = 'industry';

    public function __construct()
    {
        $this->model = RevenueStream::class;
        $this->name = "Industry";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = Industry::query()
            ->join(IndustryConfiguration::TABLE, IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_INDUSTRY_ID, '=', Industry::TABLE.'.'.Industry::FIELD_ID)
            ->where(IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
            ->get([Industry::TABLE.'.'.Industry::FIELD_ID, Industry::TABLE.'.'.Industry::FIELD_NAME])
            ->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
