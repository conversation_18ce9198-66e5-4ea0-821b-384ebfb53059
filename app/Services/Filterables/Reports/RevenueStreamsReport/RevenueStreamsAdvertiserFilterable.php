<?php

namespace App\Services\Filterables\Reports\RevenueStreamsReport;

use App\Enums\Advertising\Advertiser;
use App\Models\RevenueStream;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class RevenueStreamsAdvertiserFilterable extends MultiSelectFilterableOption
{
    const string ID     = 'advertiser';

    public function __construct()
    {
        $this->model = RevenueStream::class;
        $this->name = "Advertiser";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = Advertiser::getAsNameIntArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
