<?php

namespace App\Services\Filterables;

abstract class DateRangeFilterableOption extends BaseFilterableOption
{
    const DATE_RANGE_FROM           = 'from';
    const DATE_RANGE_TO             = 'to';
    const DATE_RANGE_MIN            = 'min';
    const DATE_RANGE_MAX            = 'max';
    const DATE_RANGE_DEFAULT        = 'default';
    const DATE_RANGE_PRESETS        = 'presets';
    const DATE_RANGE_PRESET         = 'preset';

    protected FilterableType $type = FilterableType::DATE_RANGE;

}
