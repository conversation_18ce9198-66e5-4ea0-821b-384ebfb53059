<?php

namespace App\Services\Filterables;

/**
 * Filterable class for a multi-select with child components
 * Child components will be show at the top of the parent as nested filters
 *
 */
abstract class MultiSelectWithChildrenFilterableOption extends BaseFilterableOption
{
    protected FilterableType $type = FilterableType::MULTI_SELECT_WITH_CHILDREN;

    /** @var BaseFilterableOption[] */
    protected array $children = [];

    /**
     * Whether to display all selected inputs from all components on the filter pill
     * or only the final child/parent containing input data
     * @var bool $filterPillDisplayLastChild
     */
    protected bool $filterPillDisplayLastChild = false;

    /**
     * Whether skip parent verification when displaying the active filter or not
     * @var bool $bypassParentCheckForActiveFilterDisplay
     */
    protected bool $bypassParentCheckForActiveFilterDisplay = false;

    public function getFilterableDisplayData(): array
    {
        $childDisplayData = [];
        foreach ($this->children as $childFilterable) {
            $childDisplayData[$childFilterable->getId()] = $childFilterable->getFilterableDisplayData();
        }

        return [
            ...parent::getFilterableDisplayData(),
            'children'                   => $childDisplayData,
            'filterPillDisplayLastChild' => $this->filterPillDisplayLastChild,
            'bypassParentCheckForActiveFilterDisplay' => $this->bypassParentCheckForActiveFilterDisplay,
        ];
    }
}
