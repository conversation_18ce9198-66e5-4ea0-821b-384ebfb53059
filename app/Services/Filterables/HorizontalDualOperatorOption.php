<?php

namespace App\Services\Filterables;

use App\Enums\Logical;
use App\Enums\Operator;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;

abstract class HorizontalDualOperatorOption extends BaseFilterableOption
{
    const FIRST_INPUT = 'first_value';
    const SECOND_INPUT = 'second_value';
    const INPUT_MIN = 'min';
    const INPUT_MAX = 'max';
    const FIRST_OPERATOR = 'first_operator';
    const SECOND_OPERATOR = 'second_operator';
    const LOGICAL = 'logical';
    const ACTIVE = 'active';

    protected FilterableType $type = FilterableType::HORIZONTAL_DUAL_OPERATOR;

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $active = data_get($value, self::ACTIVE, false);

        if (!$active) {
            return $builder;
        }

        try {
            list($firstOperator, $secondOperator, $firstInput, $secondInput, $logical) = $this->validateValue($value);
        } catch (Exception $e) {
            $filterName = $this->name;

            throw new Exception("Issue w/ $filterName: ".$e->getMessage());
        }

        return $value
            ? $this->getQuery($builder, $firstOperator, $secondOperator, $firstInput, $secondInput, $logical)
            : $builder;
    }

    /**
     * @param  mixed  $value
     * @return array
     * @throws Exception
     */
    protected function validateValue(mixed $value): array
    {
        if (!is_array($value)) {
            throw new Exception('Value must be an array');
        }

        if (isset($this->options)) {
            $min = data_get($this->options, self::INPUT_MIN);
            $max = data_get($this->options, self::INPUT_MAX);

            if (is_numeric($min) && is_numeric($max)) {
                $inputRule = "between:$min,$max";
            } elseif (is_numeric($min)) {
                $inputRule = "gte:$min";
            } elseif (is_numeric($max)) {
                $inputRule = "lte:$max";
            } else {
                $inputRule = 'numeric';
            }
        } else {
            $inputRule = '';
        }

        $validated = Validator::make($value, [
            self::FIRST_OPERATOR => ['required', new Enum(Operator::class)],
            self::SECOND_OPERATOR => ['sometimes', new Enum(Operator::class), 'nullable'],
            self::FIRST_INPUT => ['required', 'numeric', $inputRule],
            self::SECOND_INPUT => ['sometimes', 'numeric', 'nullable', $inputRule],
            self::LOGICAL => ['sometimes', 'nullable', new Enum(Logical::class)],
        ])->validate();

        if (Logical::tryFrom(data_get($validated, self::LOGICAL))) {
            Validator::make($value, [
                self::SECOND_OPERATOR => ['required', new Enum(Operator::class)],
                self::SECOND_INPUT => ['required', 'numeric', $inputRule],
            ])->validate();
        }

        $firstOperator = data_get($validated, self::FIRST_OPERATOR);
        $secondOperator = data_get($validated, self::SECOND_OPERATOR);
        $firstInput = data_get($validated, self::FIRST_INPUT);
        $secondInput = data_get($validated, self::SECOND_INPUT);
        $logical = data_get($validated, self::LOGICAL);

        $firstOperator = Operator::tryFrom($firstOperator);
        $secondOperator = Operator::tryFrom($secondOperator);
        $firstInput = (int) $firstInput;
        $secondInput = (int) $secondInput;
        $logical = Logical::tryFrom($logical);

        return [
            $firstOperator,
            $secondOperator,
            $firstInput,
            $secondInput,
            $logical,
        ];
    }

    abstract protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical,
    ): Builder;

    /**
     * @param  Builder  $builder
     * @param  string  $column
     * @param  Operator  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  int  $firstInput
     * @param  int|null  $secondInput
     * @param  Logical|null  $logical
     * @return Builder
     * @throws Exception
     */
    protected function defaultQueryLogic(
        Builder $builder,
        string $column,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical
    ): Builder {
        try {
            $firstOperatorSql = Operator::sqlOperator($firstOperator);
        } catch (Exception $e) {
            throw new Exception("Issue w/ first operator: ".$e->getMessage());
        }


        $builder->having($column, $firstOperatorSql, $firstInput);

        if ($logical && $secondOperator instanceof Operator && gettype($secondInput) == 'integer') {
            try {
                $secondOperatorSql = Operator::sqlOperator($secondOperator);
            } catch (Exception $e) {
                throw new Exception("Issue w/ second operator: ".$e->getMessage());
            }

            if ($logical === Logical::AND) {
                $builder->having($column, $secondOperatorSql, $secondInput);
            } else {
                $builder->orHaving($column, $secondOperatorSql, $secondInput);
            }
        }

        return $builder;
    }
}
