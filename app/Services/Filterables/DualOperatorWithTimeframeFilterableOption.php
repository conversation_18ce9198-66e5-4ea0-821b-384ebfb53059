<?php

namespace App\Services\Filterables;

use App\Enums\Logical;
use App\Enums\Operator;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;

abstract class DualOperatorWithTimeframeFilterableOption extends BaseFilterableOption
{
    const FIRST_INPUT = 'first_input';
    const SECOND_INPUT = 'second_input';
    const FIRST_OPERATOR = 'first_operator';
    const SECOND_OPERATOR = 'second_operator';
    const FIRST_TIMEFRAME_ACTIVE = 'first_timeframe_active';
    const SECOND_TIMEFRAME_ACTIVE = 'second_timeframe_active';
    const FIRST_FROM_DATE = 'first_from_date';
    const FIRST_TO_DATE = 'first_to_date';
    const SECOND_FROM_DATE = 'second_from_date';
    const SECOND_TO_DATE = 'second_to_date';
    const LOGICAL = 'logical';

    protected FilterableType $type = FilterableType::DUAL_OPERATOR_WITH_TIME_FRAME;

    public function __construct(
        protected $secondSegmentIsOptional = true,
    ) {

    }

    /**
     * @param  mixed  $value
     * @return array
     * @throws Exception
     */
    protected function validateValue(mixed $value): array
    {
        if (!is_array($value)) {
            throw new Exception('Value must be an array');
        }

        Validator::make($value, [
            self::FIRST_OPERATOR => ['required', new Enum(Operator::class)],
            self::FIRST_INPUT => 'required|numeric',
            self::FIRST_TIMEFRAME_ACTIVE => 'sometimes|boolean',
            self::SECOND_TIMEFRAME_ACTIVE => 'sometimes|boolean',
        ])->validate();

        if ($this->secondSegmentIsOptional) {
            Validator::make($value, [
                self::LOGICAL => ['nullable', new Enum(Logical::class)],
                self::SECOND_OPERATOR => ['nullable', new Enum(Operator::class)],
                self::SECOND_INPUT => 'nullable|numeric',
            ])->validate();
        } else {
            Validator::make($value, [
                self::LOGICAL => ['required', new Enum(Logical::class)],
                self::SECOND_OPERATOR => ['required', new Enum(Operator::class)],
                self::SECOND_INPUT => 'required|numeric',
            ])->validate();
        }

        $validated = $value;

        if (data_get($validated, self::FIRST_TIMEFRAME_ACTIVE)) {
            Validator::make($value, [
                self::FIRST_FROM_DATE => 'required|date',
                self::FIRST_TO_DATE => 'required|date',
            ])->validate();
        } else {
            Validator::make($value, [
                self::FIRST_FROM_DATE => 'sometimes|nullable|date',
                self::FIRST_TO_DATE => 'sometimes|nullable|date',
            ])->validate();
        }


        if (data_get($validated, self::SECOND_TIMEFRAME_ACTIVE)) {
            Validator::make($value, [
                self::SECOND_FROM_DATE => 'required|date',
                self::SECOND_TO_DATE => 'required|date',
            ])->validate();
        } else {
            Validator::make($value, [
                self::SECOND_FROM_DATE => 'sometimes|nullable|date',
                self::SECOND_TO_DATE => 'sometimes|nullable|date',
            ])->validate();
        }

        $firstOperator = Operator::tryFrom(data_get($validated, self::FIRST_OPERATOR));
        $secondOperator = Operator::tryFrom(data_get($validated, self::SECOND_OPERATOR));
        $firstInput = (int) data_get($validated, self::FIRST_INPUT);
        $secondInput = (int) data_get($validated, self::SECOND_INPUT);
        $logical = Logical::tryFrom(data_get($validated, self::LOGICAL));
        $firstTimeframeActive = (bool) data_get($validated, self::FIRST_TIMEFRAME_ACTIVE);
        $secondTimeframeActive = (bool) data_get($validated, self::SECOND_TIMEFRAME_ACTIVE);
        $firstFromDate = Carbon::make(data_get($validated, self::FIRST_FROM_DATE));
        $firstToDate = Carbon::make(data_get($validated, self::FIRST_TO_DATE));
        $secondFromDate = Carbon::make(data_get($validated, self::SECOND_FROM_DATE));
        $secondToDate = Carbon::make(data_get($validated, self::SECOND_TO_DATE));

        return [
            $firstOperator,
            $secondOperator,
            $firstInput,
            $secondInput,
            $logical,
            $firstTimeframeActive,
            $secondTimeframeActive,
            $firstFromDate,
            $firstToDate,
            $secondFromDate,
            $secondToDate,
        ];
    }
}
