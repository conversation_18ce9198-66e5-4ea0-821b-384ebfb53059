<?php

namespace App\Services\Filterables\Base;

use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class BinaryChoiceFilterable extends SelectFilterableOption
{
    public function __construct(string $id, string $name, string $model)
    {
        $this->model = $model;
        $this->name = $name;
        $this->id = $id;
        $this->withRelations = null;

        $this->options = [
            'Yes'  => '1',
            'No' => '0',
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
