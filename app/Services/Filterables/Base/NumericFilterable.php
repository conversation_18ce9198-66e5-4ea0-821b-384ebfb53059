<?php

namespace App\Services\Filterables\Base;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Services\Filterables\DualOperatorFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class NumericFilterable extends DualOperatorFilterableOption
{
    public function __construct(string $id, string $name, string $model, int $min = 0)
    {
        $this->model = $model;
        $this->name = $name;
        $this->id = $id;
        $this->withRelations = null;

        $this->options = [
            'min' => $min,
        ];
    }

    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical
    ): Builder {
        return $builder;
    }
}
