<?php

namespace App\Services\Filterables;

use App\Enums\Operator;
use Exception;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;

abstract class CheckboxListDualOperatorFilterableOption extends BaseFilterableOption
{
    const OPTION_SELECTED = 'selected';
    const OPTION_OPERATOR = 'operator';
    const OPTION_VALUE = 'value';
    const OPTION_KEY = 'key';

    protected FilterableType $type = FilterableType::CHECKBOX_LIST_DUAL_OPERATOR;

    protected function isActive(mixed $value): bool
    {
        if (!is_array($value)) {
            return false;
        }

        return collect($value)->filter(fn($item) => data_get($item, self::OPTION_SELECTED, true))->count() > 0;
    }

    /**
     * @param  mixed  $value
     * @return array
     * @throws Exception
     */
    protected function validateValue(mixed $value): array
    {
        if (!is_array($value)) {
            throw new Exception('Value must be an array');
        }

        foreach ($value as $key => $item) {
            $validator = Validator::make($item, [
                self::OPTION_SELECTED => ['required', 'boolean'],
                self::OPTION_OPERATOR => ['sometimes', new Enum(Operator::class), 'nullable'],
                self::OPTION_VALUE => 'sometimes|numeric|nullable',
                self::OPTION_KEY => 'sometimes|string',
            ]);

            if ($validator->fails()) {
                $optionName = data_get($item, self::OPTION_KEY, $key);

                throw new Exception("Issue with $optionName: ".$validator->errors()->first());
            }
        }


        return $value;
    }
}
