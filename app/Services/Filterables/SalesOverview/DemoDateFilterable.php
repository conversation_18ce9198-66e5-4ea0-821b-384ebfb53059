<?php

namespace App\Services\Filterables\SalesOverview;

use App\Models\Calendar\Demo;
use App\Services\Filterables\BaseDateRangeFilterableOption;

class DemoDateFilterable extends BaseDateRangeFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model = Demo::class;
        $this->id = 'demo-date-range';
        $this->minFromDate = null;

        parent::__construct();
    }

    protected function setTableAndColumn(): void
    {
        $this->tableAndColumn = Demo::TABLE .'.'. Demo::CREATED_AT;
    }
}
