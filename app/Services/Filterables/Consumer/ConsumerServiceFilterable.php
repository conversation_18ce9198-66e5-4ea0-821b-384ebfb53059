<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerServiceFilterable extends MultiSelectFilterableOption
{

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Service";
        $this->id = 'consumer-service';

        $this->options = IndustryService::all()
            ->mapWithKeys(fn(IndustryService $industryService) => [
                $industryService->industry->name . ' - ' . $industryService->name => $industryService->id
            ])
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $value)
            : $builder;
    }

}
