<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use Illuminate\Database\Eloquent\Builder;

class ConsumerMarketingCampaignIdFilterable extends InputFilterableOption
{
    protected string  $model            = Consumer::class;
    protected string  $name             = 'Marketing Campaign Id';
    protected string  $id               = 'marketing-campaign-id';
    protected ?string $inputLabel       = 'Marketing Campaign Id';
    protected ?string $inputPlaceholder = 'Marketing Campaign Id';
    protected string  $inputType        = 'number';

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (empty($value)) {
            return $builder;
        }

        $builder->whereHas(Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD, function ($query) use ($value) {
            return $query->where(ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID, $value);
        });

        return $builder;
    }
}
