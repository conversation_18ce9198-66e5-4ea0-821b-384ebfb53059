<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Eloquent\Builder;

class ConsumerContactRequestFilterable extends InputFilterableOption
{
    protected string  $model            = Consumer::class;
    protected string  $name             = 'Contact request number';
    protected string  $id               = 'contact-request-number';
    protected ?string $inputLabel       = 'Contact request number';
    protected ?string $inputPlaceholder = 'Contact request number';

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (empty($value)) {
            return $builder;
        }

        $builder->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONTACT_REQUESTS, $value);

        return $builder;
    }
}
