<?php

namespace App\Services\Filterables\Consumer;

use App\Enums\PermissionType;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Filterables\BaseDateRangeFilterableOption;
use App\Models\User;
use Illuminate\Contracts\Auth\Authenticatable;
use Spatie\Permission\Models\Permission;

class ConsumerDateFilterable extends BaseDateRangeFilterableOption
{
    public function __construct(User|Authenticatable|null $authUser = null)
    {
        $this->model = Consumer::class;
        $this->id = 'consumer-date-range';

        $this->minFromDate = empty($authUser)
            || (Permission::where('name', PermissionType::CONSUMER_PRODUCT_VIEW_LONGER_THAN_90_DAYS->value)->exists()
                &&  ($authUser?->hasPermissionTo(PermissionType::CONSUMER_PRODUCT_VIEW_LONGER_THAN_90_DAYS->value)))
            ? null
            : now()->subMonths(3)->subDays(2);

        $this->maxFromDate = now()->addHours(12);
        $this->defaultFromDate = now()->subDay();
        $this->minToDate = $this->minFromDate;
        $this->maxToDate = now()->addHours(12);
        $this->defaultToDate = now();

        parent::__construct();
    }

    protected function setTableAndColumn(): void
    {
        $this->tableAndColumn = ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT;
    }
}
