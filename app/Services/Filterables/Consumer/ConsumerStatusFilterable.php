<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Enums\Odin\Product as ProductEnum;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerStatusFilterable extends MultiSelectFilterableOption
{
    const string LEAD_UNSOLD_STRING        = 'Lead Unsold';

    protected array $options = [
        ConsumerProduct::STATUS_TEXT[ConsumerProduct::STATUS_INITIAL]               => ConsumerProduct::STATUS_INITIAL,
        ConsumerProduct::STATUS_TEXT[ConsumerProduct::STATUS_PENDING_REVIEW]        => ConsumerProduct::STATUS_PENDING_REVIEW,
        ConsumerProduct::STATUS_TEXT[ConsumerProduct::STATUS_UNDER_REVIEW]          => ConsumerProduct::STATUS_UNDER_REVIEW,
        ConsumerProduct::STATUS_TEXT[ConsumerProduct::STATUS_PENDING_ALLOCATION]    => ConsumerProduct::STATUS_PENDING_ALLOCATION,
        ConsumerProduct::STATUS_TEXT[ConsumerProduct::STATUS_ALLOCATED]             => ConsumerProduct::STATUS_ALLOCATED,
        ConsumerProduct::STATUS_TEXT[ConsumerProduct::STATUS_CANCELLED]             => ConsumerProduct::STATUS_CANCELLED,
        ConsumerStatusFilterable::LEAD_UNSOLD_STRING                                => ConsumerProduct::STATUS_UNSOLD,
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Status";
        $this->id = 'consumer-status';
        $this->withRelations = null;
    }

    /**
     * Builds a query for Lead unsold distinction
     *
     * @param Builder $builder
     * @param array $values
     * @param array $productNames
     * @return Builder
     */
    private function unsoldQueryBuilder(Builder $builder, array $values, array $productNames): Builder
    {
        return $builder->where(function (Builder $query) use ($values, $productNames) {
            $query->where(function (Builder $inner_query) use ($values, $productNames) {
                $inner_query->whereIn(Product::TABLE . '.' . Product::FIELD_NAME, $productNames)
                            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_UNSOLD);
            })->orWhereIn(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS, $values);
        });
    }

    /**
     * Consumer status filter query creation
     *
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!$value) return $builder;

        if (in_array(ConsumerProduct::STATUS_UNSOLD, $value)){
            unset($value[array_search(ConsumerProduct::STATUS_UNSOLD,$value)]);
            return $this->unsoldQueryBuilder($builder, $value, [ProductEnum::LEAD->value]);
        }

        return $builder->whereIn(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_STATUS, $value);
    }

}
