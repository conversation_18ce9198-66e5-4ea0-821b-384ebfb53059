<?php

namespace App\Services\Filterables\Consumer;

use App\Services\Filterables\BaseFilterableOption;
use App\Services\Filterables\FilterableType;


abstract class InputFilterableOption extends BaseFilterableOption
{
    protected FilterableType $type = FilterableType::INPUT;

    protected string  $inputType        = 'text';
    protected ?string $inputLabel       = null;
    protected ?string $inputPlaceholder = null;

    /**
     * @return array
     */
    public function getFilterableDisplayData(): array
    {
        return [
            ...parent::getFilterableDisplayData(),
            'input_type'        => $this->inputType,
            'input_label'       => $this->inputLabel,
            'input_placeholder' => $this->inputPlaceholder,
        ];
    }
}
