<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\Website;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerOriginFilterable extends MultiSelectFilterableOption
{

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Origin";
        $this->id = 'consumer-origin';
        $this->withRelations = null;

        $this->options = Website::all()
            ->mapWithKeys(fn(Website $website) => [$website->name => $website->id])
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Website::TABLE .'.'. Website::FIELD_ID, $value)
            : $builder;
    }

}
