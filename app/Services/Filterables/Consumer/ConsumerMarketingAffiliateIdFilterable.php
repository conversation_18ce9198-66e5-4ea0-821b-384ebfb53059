<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use Illuminate\Database\Eloquent\Builder;

class ConsumerMarketingAffiliateIdFilterable extends InputFilterableOption
{
    protected string  $model            = Consumer::class;
    protected string  $name             = 'Marketing Affiliate id';
    protected string  $id               = 'marketing-affiliate-id';
    protected ?string $inputLabel       = 'Marketing Affiliate id';
    protected ?string $inputPlaceholder = 'Marketing Affiliate id';
    protected string  $inputType        = 'number';

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (empty($value)) {
            return $builder;
        }

        $builder->whereHas(Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD, function ($query) use ($value) {
            return $query->where(ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID, $value);
        });

        return $builder;
    }
}
