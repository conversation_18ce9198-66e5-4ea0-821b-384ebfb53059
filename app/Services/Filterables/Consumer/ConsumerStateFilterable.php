<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerStateFilterable extends MultiSelectFilterableOption
{

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "State";
        $this->id = 'consumer-state';
        $this->withRelations = null;

        $this->options = Location::query()
            ->select(Location::STATE, Location::STATE_ABBREVIATION)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get()
            ->mapWithKeys(fn(Location $state) => [$state->state => $state->state_abbr])
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Address::TABLE .'.'. Address::FIELD_STATE, $value)
            : $builder;
    }

}
