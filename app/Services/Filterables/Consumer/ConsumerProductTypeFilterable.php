<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\Product;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerProductTypeFilterable extends MultiSelectFilterableOption
{

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Product Type";
        $this->id = 'consumer-product';
        $this->withRelations = null;

        $this->options = Product::query()
            ->whereNot(Product::FIELD_NAME, \App\Enums\Odin\Product::APPOINTMENT->value)
            ->get()
            ->mapWithKeys(fn(Product $product) => [$product->name => $product->id])
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Product::TABLE .'.'. Product::FIELD_ID, $value)
            : $builder;
    }

}
