<?php

namespace App\Services\Filterables\Consumer;

use App\Models\AvailableBudget;
use App\Models\AvailableCompanyByLocation;
use App\Models\Odin\Address;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class ConsumerHasAvailableBudgetFilterable extends SelectFilterableOption
{
    const int LEGACY = 1;
    const int DEFAULT = 2;
    const int WITH_UNSOLD_LEGS = 3;

    protected array $options = [
        'Legacy Has Available Budget' => self::LEGACY,
        'Default' => self::DEFAULT,
        'With Unsold Legs' => self::WITH_UNSOLD_LEGS,
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Has Available Budget";
        $this->id = 'has-available-budget';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if ($value === self::LEGACY) {
            return $builder
                ->join(DatabaseHelperService::database().'.'.AvailableBudget::TABLE, function($join) {
                    $join
                        ->on(
                            DatabaseHelperService::database().'.'.AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_LOCATION_ID,
                            '=',
                            DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_ZIP_CODE_LOCATION_ID
                        )
                        ->on(
                            DatabaseHelperService::database().'.'.AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_INDUSTRY_ID,
                            '=',
                            DatabaseHelperService::database().'.'.IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID
                        );
                })
                ->selectRaw(implode(',', [
                    AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_AVAILABLE_CAMPAIGN_COUNT,
                    AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS,
                    AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME,
                    AvailableBudget::TABLE.'.'.AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT,
                ]));
        } elseif ($value === self::DEFAULT || $value === self::WITH_UNSOLD_LEGS) {
            if ($value === self::WITH_UNSOLD_LEGS) {
                $builder
                    ->addSelect(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONTACT_REQUESTS)
                    ->havingRaw('COUNT('.ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID .') < '.ConsumerProduct::TABLE.'.' . ConsumerProduct::FIELD_CONTACT_REQUESTS);
            }

            return $builder
                ->join(AvailableCompanyByLocation::TABLE, function (JoinClause $join) {
                    $join->on(
                        Address::TABLE .'.'. Address::FIELD_ZIP_CODE_LOCATION_ID,
                        '=',
                        AvailableCompanyByLocation::TABLE .'.'. AvailableCompanyByLocation::FIELD_LOCATION_ID
                    );
                })
                ->leftJoin(ProductAssignment::TABLE . ' as pa2', fn(JoinClause $join) =>
                    $join->on('pa2.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID)
                        ->on('pa2.' . ProductAssignment::FIELD_COMPANY_ID, '=', AvailableCompanyByLocation::TABLE .'.'. AvailableCompanyByLocation::FIELD_COMPANY_ID)
                )->whereNull('pa2.' . ProductAssignment::FIELD_COMPANY_ID)
                ->join(CompanyService::TABLE, function (JoinClause $join) {
                    $join->on(
                        CompanyService::TABLE . '.' . CompanyService::FIELD_COMPANY_ID,
                        '=',
                        AvailableCompanyByLocation::TABLE . '.' . AvailableCompanyByLocation::FIELD_COMPANY_ID
                    )->on(
                        ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID,
                        '=',
                        CompanyService::TABLE . '.' . CompanyService::FIELD_INDUSTRY_SERVICE_ID
                    );
                });
        } else {
            return $builder;
        }
    }

}
