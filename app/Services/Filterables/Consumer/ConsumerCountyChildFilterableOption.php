<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerCountyChildFilterableOption extends MultiSelectFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model         = Consumer::class;
        $this->name          = "County";
        $this->id            = ConsumerLocationFilterable::CHILD_FILTERABLE_COUNTY_ID;
        $this->withRelations = null;

        $this->options = [];
    }

    public function updateOptions(array $filterByKeys): ?array
    {
        $newOptions = Location::query()
            ->select(Location::STATE_ABBREVIATION, Location::COUNTY, Location::COUNTY_KEY)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->whereIn(Location::STATE_ABBREVIATION, $filterByKeys)
            ->get()
            ->reduce(function (array $output, Location $county) {
                $output[$county->county] = $county->county_key;
                return $output;
            }, []);

        $this->options = $newOptions;

        return $newOptions;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $this->joinLocationIfRequired($builder);
        return $value
            ? $builder->whereIn(Location::TABLE .'.'. Location::COUNTY_KEY, $value)
            : $builder;
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinLocationIfRequired(Builder &$query): void
    {
        if (!DatabaseHelperService::joinExists($query->getQuery(), Location::TABLE)) {
            $query->join(
                Location::TABLE,
                Location::TABLE . '.' . Location::ZIP_CODE,
                '=',
                Address::TABLE . '.' . Address::FIELD_ZIP_CODE
            );
        }
    }
}

