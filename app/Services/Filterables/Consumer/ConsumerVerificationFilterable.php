<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerVerificationFilterable extends MultiSelectFilterableOption
{

    protected array $options = [
        Consumer::CLASSIFICATION_TEXT[Consumer::CLASSIFICATION_EMAIL_ONLY]                          => Consumer::CLASSIFICATION_EMAIL_ONLY,
        Consumer::CLASSIFICATION_TEXT[Consumer::CLASSIFICATION_VERIFIED_EMAIL_ONLY]                 => Consumer::CLASSIFICATION_VERIFIED_EMAIL_ONLY,
        Consumer::CLASSIFICATION_TEXT[Consumer::CLA<PERSON><PERSON>ICATION_UNVERIFIED_PHONE]                    => Consumer::CLASSIFICATION_UNVERIFIED_PHONE,
        Consumer::CLASSIFICATION_TEXT[Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS]              => Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS,
        Consumer::CLASSIFICATION_TEXT[Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL]             => Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL,
        Consumer::CLASSIFICATION_TEXT[Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING]  => Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING,
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Verification";
        $this->id = 'consumer-verification';
        $this->withRelations = null;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Consumer::TABLE .'.'. Consumer::FIELD_CLASSIFICATION, $value)
            : $builder;
    }

}
