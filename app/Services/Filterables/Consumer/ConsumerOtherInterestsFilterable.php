<?php

namespace App\Services\Filterables\Consumer;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class ConsumerOtherInterestsFilterable extends SelectFilterableOption
{

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = 'Has Other Interests';
        $this->id = 'other-interests';

        $this->options = Industry::all()
            ->mapWithKeys(fn(Industry $industry) => [$industry->name => $industry->slug])
            ->toArray();
    }

    #[\Override]
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if ($value === null) {
            return $builder;
        }

        $this->joinConsumerProductData($builder);

        if ($value) {
            $builder->where(ConsumerProductData::VIRTUAL_FIELD_OTHER_INTERESTS ,'like', "%$value%");
        }

        return $builder;
    }

    /**
     * @param Builder $builder
     *
     * @return void
     */
    protected function joinConsumerProductData(Builder $builder): void
    {
        if ($this->shouldJoin($builder, ConsumerProductData::TABLE)) {
            $builder->join(
                ConsumerProductData::TABLE,
                ConsumerProductData::TABLE . '.' . ConsumerProductData::FIELD_ID,
                '=',
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID
            );
        }
    }
}
