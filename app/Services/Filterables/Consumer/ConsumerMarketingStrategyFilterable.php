<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProductTracking;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerMarketingStrategyFilterable extends SelectFilterableOption
{
    const string OPTION_PAID    = 'Paid';
    const string OPTION_ORGANIC = 'Organic';

    protected array $options = [
        self::OPTION_PAID    => 'Paid',
        self::OPTION_ORGANIC => 'Organic',
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Marketing Strategy";
        $this->id = 'marketing-strategies';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!in_array($value, [self::OPTION_PAID, self::OPTION_ORGANIC])) {
            return $builder;
        }

        $not = $value === self::OPTION_ORGANIC;

        $builder->whereExists(function ($builder) {
            $builder
                ->whereNotNull(ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::AD_TRACK_TYPE)
                ->whereNotNull(ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::AD_TRACK_CODE);
        }, 'and', $not);

        return $builder;
    }
}
