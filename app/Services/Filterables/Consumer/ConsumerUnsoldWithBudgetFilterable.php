<?php

namespace App\Services\Filterables\Consumer;

use App\Models\AvailableBudget;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

/**
 * TODO: this is a workaround, industry-based single-select until we can join
 *  available_budgets properly - it's currently using 'roofer' from legacy in industry_type
 *  so we can't join on industry-slug. There is already a to-do to update this to 'roofing'
 *
 * Once available_budgets has been updated (either with roofer => roofing, or replacing them all with industry_id)
 *  we can turn this into a MultiSelectFilterableOption with any flags we want specific to unsold products searches
 * The industry constraint would then just be controlled by the Industry filter as usual
 */
class ConsumerUnsoldWithBudgetFilterable extends SelectFilterableOption
{
//    const string LABEL_UNSOLD_WITH_BUDGET = 'Unsold with budget';
//    const string LABEL_ORDER_BY_BUDGET    = 'Order by most budget';
//    const string LABEL_INCLUDE_UNDERSOLD  = 'Include undersold';
    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Unsold Products";
        $this->id = 'consumer-unsold-products';

        $this->options = Industry::all()
            ->mapWithKeys(fn(Industry $industry) => [$industry->name => $industry->id])
            ->toArray();
//        $this->options = [
//            self::LABEL_UNSOLD_WITH_BUDGET => 1,
//            self::LABEL_ORDER_BY_BUDGET    => 2,
//            self::LABEL_INCLUDE_UNDERSOLD  => 3,
//        ];
    }

    /**
     * @inheritDoc
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $industrySlug = Industry::query()->find($value)?->slug;
        if (!$industrySlug) {
            return $builder;
        }
        else {
            return $this->getBaseUnsoldWithBudgetQuery($builder, $value, $industrySlug)
                ->addSelect([
                    AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_AVAILABLE_CAMPAIGN_COUNT,
                    AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT,
                    AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS,
                    AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME,
                ]);
        }
    }

    /**
     * @param Builder $query
     * @param mixed $value
     * @param string $industrySlug
     * @return Builder
     */
    protected function getBaseUnsoldWithBudgetQuery(Builder $query, mixed $value, string $industrySlug): Builder
    {
        // workaround until available_budgets table is updated
        $industrySlug = $industrySlug === 'roofing'
            ? 'roofer'
            : $industrySlug;

        if ($this->shouldJoin($query, Location::TABLE))
            $query->join(Location::TABLE, Location::TABLE .'.'. Location::ZIP_CODE, '=', Address::TABLE .'.'. Address::FIELD_ZIP_CODE);

        return $query->join(AvailableBudget::TABLE, fn(JoinClause $join) =>
            $join->on(AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_LOCATION_ID, '=', Location::TABLE .'.'. Location::ID)
                ->where(AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_INDUSTRY_TYPE, $industrySlug)
            )->where(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $value)
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONTACT_REQUESTS, '>', 0)
            ->whereNull(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID)
            ->where(fn(Builder $query) =>
                $query->orWhere(AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_UNLIMITED_BUDGET_COUNT, '>', 0)
                    ->orWhere(AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_BUDGET_AVAILABLE_VOLUME, '>', 0)
                    ->orWhere(AvailableBudget::TABLE .'.'. AvailableBudget::FIELD_BUDGET_AVAILABLE_DOLLARS, '>', 0)
            );
    }
}
