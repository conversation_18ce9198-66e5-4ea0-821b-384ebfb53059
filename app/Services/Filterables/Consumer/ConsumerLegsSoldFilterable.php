<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\Website;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ConsumerLegsSoldFilterable extends MultiSelectFilterableOption
{
    protected bool $sortOptions = false;

    protected array $options = [
        'Zero'  => 0,
        'One'   => 1,
        'Two'   => 2,
        'Three' => 3,
        'Four'  => 4,
    ];

    public function __construct()
    {
        $this->model         = Consumer::class;
        $this->name          = "Legs Sold";
        $this->id            = 'legs-sold';
        $this->withRelations = null;

    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (empty($value)) {
            return $builder;
        }

        $hasZero = in_array(0, $value);
        $otherValues = array_filter($value, fn($v) => $v !== 0);

        $builder->where(function ($query) use ($hasZero, $otherValues) {
            // Handle zero values - consumers with no legs sold
            if ($hasZero) {
                $query->orWhereNotExists(function ($subQuery) {
                    $subQuery
                        ->select(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
                        ->from(ProductAssignment::TABLE)
                        ->whereColumn(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
                        ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                        ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true);
                });
            }

            // Handle other values - consumers with specific counts of legs sold
            if (!empty($otherValues)) {
                $stringValues = "(" . implode(', ', $otherValues) . ")";
                $query->orWhereExists(function ($subQuery) use ($stringValues) {
                    $subQuery
                        ->select(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
                        ->from(ProductAssignment::TABLE)
                        ->whereColumn(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
                        ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                        ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                        ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
                        ->havingRaw('COUNT(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID . ') IN ' . $stringValues);
                });
            }
        });

        return $builder;
    }

}
