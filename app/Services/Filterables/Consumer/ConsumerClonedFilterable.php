<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerClonedFilterable extends SelectFilterableOption
{
    const string OPTION_CLONED   = 'True';
    const string OPTION_ORIGINAL = 'False';

    protected array $options = [
        self::OPTION_CLONED   => 'True',
        self::OPTION_ORIGINAL => 'False',
    ];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Is Cloned";
        $this->id = 'cloned';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!in_array($value, [self::OPTION_CLONED, self::OPTION_ORIGINAL])) {
            return $builder;
        }

        if ($value === self::OPTION_CLONED) {
            $builder->whereNotNull(Consumer::TABLE .'.'. Consumer::FIELD_CLONED_FROM_ID);
        } else {
            $builder->whereNull(Consumer::TABLE .'.'. Consumer::FIELD_CLONED_FROM_ID);
        }

        return $builder;
    }
}
