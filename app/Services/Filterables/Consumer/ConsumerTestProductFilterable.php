<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\TestProduct;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ConsumerTestProductFilterable extends SelectFilterableOption
{
    protected array $options = [
        'True'  => true,
        'False' => false,
    ];

    public function __construct()
    {
        $this->model    = Consumer::class;
        $this->name     = "Test Products";
        $this->id       = 'test-product';
    }

    /**
     * Query the existence of a test product
     *
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (is_null($value)) return $builder;

        return $value
            ? $builder->whereExists(function ($query) {
                $this->getTestProductRelationQuery($query);
            })
            : $builder->whereNotExists(function ($query) {
                $this->getTestProductRelationQuery($query);
            });
    }

    /**
     * Query related test products
     *
     * @param \Illuminate\Database\Query\Builder $query
     * @return \Illuminate\Database\Query\Builder
     */
    protected function getTestProductRelationQuery(\Illuminate\Database\Query\Builder $query): \Illuminate\Database\Query\Builder
    {
        return $query->select(DB::raw(1))
            ->from(TestProduct::TABLE)
            ->whereColumn(TestProduct::TABLE . '.' . TestProduct::FIELD_PRODUCT_ID, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID);
    }

}
