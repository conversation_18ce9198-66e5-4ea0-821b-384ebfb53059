<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class ConsumerIndustryFilterable extends MultiSelectFilterableOption
{

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Industry";
        $this->id = 'consumer-industry';
        $this->withRelations = null;

        $this->options = Industry::all()
            ->mapWithKeys(fn(Industry $industry) => [$industry->name => $industry->id])
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $value)
            : $builder;
    }

}
