<?php

namespace App\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class ConsumerFilterableService extends BaseFilterableService
{
    const FILTERABLE_CATEGORY = 'consumer-search';

    protected array $filters = [
        ConsumerStatusFilterable::class,
        ConsumerIndustryFilterable::class,
        ConsumerServiceFilterable::class,
        ConsumerProductTypeFilterable::class,
        ConsumerVerificationFilterable::class,
        ConsumerLocationFilterable::class,
        ConsumerOriginFilterable::class,
        ConsumerDateFilterable::class,
        ConsumerMarketingStrategyFilterable::class,
        ConsumerGoodToSellFilterable::class,
        ConsumerTestProductFilterable::class,
        ConsumerOtherInterestsFilterable::class,
        ConsumerLegsSoldFilterable::class,
        ConsumerMarketingAffiliateIdFilterable::class,
        ConsumerMarketingCampaignIdFilterable::class,
        ConsumerContactRequestFilterable::class,
        ConsumerUnsoldWithBudgetFilterable::class,
        ConsumerDeliveredFilterable::class,
        ConsumerHasAvailableBudgetFilterable::class,
        ConsumerClonedFilterable::class,
        ConsumerHasSecondaryServicesFilterable::class,
    ];

    protected string $baseModel = Consumer::class;

    /**
     * @param array $results
     * @param Builder|null $baseQuery
     * @return Builder
     */
    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? Consumer::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }

}
