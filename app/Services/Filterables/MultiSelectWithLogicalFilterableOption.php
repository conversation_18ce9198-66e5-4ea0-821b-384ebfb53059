<?php

namespace App\Services\Filterables;

use App\Enums\Logical;
use Exception;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;

abstract class MultiSelectWithLogicalFilterableOption extends BaseFilterableOption
{
    const OPTION_OPTIONS = 'options';
    const OPTION_LOGICAL = 'logical';

    protected FilterableType $type = FilterableType::MULTI_SELECT_WITH_LOGICAL;

    protected function getShow(): bool
    {
        $options = data_get($this->getOptions(), self::OPTION_OPTIONS, []);

        if (!is_array($options)) {
            throw new Exception('Options must be an array');
        }

        return count($options) > 0;
    }

    protected function isActive(mixed $value): bool
    {
        if (!is_array($value)) {
            return false;
        }

        $options = data_get($value, 'options', []);
        $logical = data_get($value, 'logical');
        $logical = Logical::tryFrom($logical);

        return $logical && count($options) > 0;
    }

    /**
     * @param  mixed  $value
     * @return array
     * @throws Exception
     */
    protected function validateValue(mixed $value): array
    {
        if (!is_array($value)) {
            throw new Exception('Value must be an array');
        }

        Validator::make($value, [
            self::OPTION_LOGICAL => ['required', new Enum(Logical::class)],
            self::OPTION_OPTIONS => 'required|array',
        ])->validate();

        return [
            Logical::tryFrom(data_get($value, self::OPTION_LOGICAL)),
            data_get($value, self::OPTION_OPTIONS, []),
        ];
    }
}
