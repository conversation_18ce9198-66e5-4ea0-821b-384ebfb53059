<?php

namespace App\Services\Filterables\Company;

use App\Enums\Company\CompanyCampaignStatus;
use App\Models\Odin\Company;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CampaignStatusFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Campaign Status";
        $this->id = 'company-status';
        $this->withRelations = null;

        $this->options = CompanyCampaignStatus::getAsKeyValueSelectArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Company::TABLE.'.'.Company::FIELD_CAMPAIGN_STATUS, $value)
            : $builder;
    }
}
