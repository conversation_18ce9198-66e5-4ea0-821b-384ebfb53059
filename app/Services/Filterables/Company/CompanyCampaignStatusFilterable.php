<?php

namespace App\Services\Filterables\Company;

use App\Enums\CampaignStatus;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyCampaignStatusFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Campaign Status";
        $this->id = 'company-campaign-status';
        $this->withRelations = null;

        $this->options = CampaignStatus::getAsKeyValueSelectArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        $builder->whereExists(function (\Illuminate\Database\Query\Builder $builder) use ($value) {
            $this->connectCompanyToLeadCampaign($builder);

            $campaignStatusColumn = DatabaseHelperService::readOnlyDatabase().".".LeadCampaign::TABLE.".".LeadCampaign::STATUS;

            $campaignReactivateDateColumn = DatabaseHelperService::readOnlyDatabase().".".LeadCampaign::TABLE.".".LeadCampaign::REACTIVATE_DATE;

            $builder->where(function ($query) use ($campaignStatusColumn, $campaignReactivateDateColumn, $value) {
                if (in_array(CampaignStatus::ACTIVE->value, $value)) {
                    $query->orWhere($campaignStatusColumn, '=',
                        LeadCampaign::STATUS_ACTIVE);
                }

                if (in_array(CampaignStatus::PAUSED->value, $value)) {
                    $query
                        ->orWhere(function ($query) use ($campaignStatusColumn, $campaignReactivateDateColumn) {
                            $query
                                ->where($campaignStatusColumn,
                                    LeadCampaign::STATUS_INACTIVE)
                                ->whereNotNull($campaignReactivateDateColumn);
                        });
                }

                if (in_array(CampaignStatus::OFF->value, $value)) {
                    $query
                        ->orWhere(function ($query) use ($campaignStatusColumn, $campaignReactivateDateColumn) {
                            $query->where($campaignStatusColumn,
                                LeadCampaign::STATUS_INACTIVE)
                                ->whereNull($campaignReactivateDateColumn);
                        });
                }
            });
        });

        return $builder;
    }

    private function connectCompanyToLeadCampaign(\Illuminate\Database\Query\Builder $query): void
    {
        $query->fromRaw(
            '`'.DatabaseHelperService::readOnlyDatabase()."`.`".LeadCampaign::TABLE.'`',
        );

        $query->whereColumn(
            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID,
            Company::TABLE.'.'.Company::FIELD_LEGACY_ID);

        $query->whereNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.".".LeadCampaign::DELETED_AT);
    }
}
