<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Models\Odin\Company;
use App\Repositories\CompetitorRepository;
use App\Services\CompanyCompetitorService;
use App\Services\Filterables\MultiSelectWithLogicalFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class CompanyPurchasingFromCompetitorFilterable extends MultiSelectWithLogicalFilterableOption
{
    public function __construct(
        protected CompanyCompetitorService $companyCompetitorService,
        protected CompetitorRepository $competitorRepository
    ) {
        $this->model = Company::class;
        $this->name = "Purchasing From Competitor";
        $this->id = 'company-purchasing-from-competitor';
        $this->withRelations = null;

        $this->options = [
            'logical' => Logical::AND->value,
            'options' => $this->competitorRepository->getAllCompetitorsAsKeyValueSelectArray(),
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!$this->isActive($value)) {
            return $builder;
        }

        try {
            list($logical, $options) = $this->validateValue($value);
        } catch (Exception $e) {
            $filterName = $this->name;

            throw new Exception("Issue w/ $filterName: ".$e->getMessage());
        }

        if (!is_array($value)) {
            return $builder;
        }

        $competitorsArray = $this->competitorRepository->getAllCompetitorsAsValueArray();

        Validator::validate($value, [
            'logical' => ['required', Rule::enum(Logical::class)],
            'options' => ['array', 'required'],
            'options.*' => Rule::in($competitorsArray),
        ]);

        if ($logical === Logical::AND) {
            $this->companyCompetitorService->queryIfCompanyHasAllCompetitors($builder, $options);
        } else {
            $this->companyCompetitorService->queryIfCompanyHasOneOrMoreOfCompetitors($builder, $options);
        }

        return $builder;
    }
}
