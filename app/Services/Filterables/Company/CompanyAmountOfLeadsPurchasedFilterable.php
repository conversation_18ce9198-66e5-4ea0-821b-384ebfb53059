<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\DualOperatorWithTimeframeFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Expression;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Carbon;

class CompanyAmountOfLeadsPurchasedFilterable extends DualOperatorWithTimeframeFilterableOption
{
    const COLUMN_LEAD_COST_ONE = 'lead_cost_one';
    const COLUMN_LEAD_COST_TWO = 'lead_cost_two';

    public function __construct()
    {
        parent::__construct();
        $this->model = Company::class;
        $this->name = "Amount of Leads Purchased";
        $this->id = 'company-leads-purchased';
        $this->withRelations = null;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        try {
            list(
                $firstOperator,
                $secondOperator,
                $firstInput,
                $secondInput,
                $logical,
                $firstTimeframeActive,
                $secondTimeframeActive,
                $firstFromDate,
                $firstToDate,
                $secondFromDate,
                $secondToDate,
                ) = $this->validateValue($value);
        } catch (Exception) {
            return $builder;
        }

        $this->getJoin(
            $builder,
            $firstTimeframeActive,
            $secondTimeframeActive,
            $firstFromDate,
            $firstToDate,
            $secondFromDate,
            $secondToDate,
            $logical
        );

        $this->getLeadCostColumns(
            $builder,
            $firstTimeframeActive,
            $secondTimeframeActive,
            $firstFromDate,
            $firstToDate,
            $secondFromDate,
            $secondToDate
        );

        $builder->groupBy(
            Company::TABLE.'.'.Company::FIELD_ID,
            Company::TABLE.'.'.Company::FIELD_NAME,
            Company::TABLE.'.'.Company::FIELD_ENTITY_NAME,
            Company::TABLE.'.'.Company::FIELD_ADMIN_STATUS,
            Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS,
            Company::TABLE.'.'.Company::FIELD_SALES_STATUS,
            Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
        );

        $this->filterByLeadCosts(
            $builder,
            $firstOperator,
            $secondOperator,
            $firstInput,
            $secondInput,
            $logical
        );

        return $builder;
    }

    /**
     * Get the join between the companies table and the legacy leads table.
     *
     * @param  Builder  $builder
     * @param  bool  $firstTimeframeActive
     * @param  bool  $secondTimeframeActive
     * @param  Carbon|null  $firstFromDate
     * @param  Carbon|null  $firstToDate
     * @param  Carbon|null  $secondFromDate
     * @param  Carbon|null  $secondToDate
     * @param  Logical|null  $logical
     * @return void
     */
    private function getJoin(
        Builder $builder,
        bool $firstTimeframeActive,
        bool $secondTimeframeActive,
        ?Carbon $firstFromDate,
        ?Carbon $firstToDate,
        ?Carbon $secondFromDate,
        ?Carbon $secondToDate,
        ?Logical $logical
    ): void {
        $builder->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.' as '.EloquentQuoteCompany::TABLE,
            function (JoinClause $join) use (
                $firstTimeframeActive,
                $secondTimeframeActive,
                $firstFromDate,
                $firstToDate,
                $secondFromDate,
                $secondToDate,
                $logical
            ) {
                $join->on(Company::TABLE.".".Company::FIELD_LEGACY_ID, '=',
                    EloquentQuoteCompany::TABLE.".".EloquentQuoteCompany::COMPANY_ID);

                $days = config('models.EloquentQuoteCompany.range_by_delivered_timestamp_in_days');
                $join->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                    '>=', now()->subDays($days)->timestamp);
            });

        $builder->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, '=', 1);

        $builder->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, '=', 1);

        if ($firstTimeframeActive) {
            $builder->whereBetween(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY,
                [$firstFromDate->timestamp, $firstToDate->timestamp]);
        }

        if ($logical instanceof Logical && $secondTimeframeActive) {
            $builder->orWhereBetween(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY,
                [$secondFromDate->timestamp, $secondToDate->timestamp]);
        }
    }

    /**
     * Get the lead cost columns depending on how many date ranges are set.
     *
     * @param  Builder  $builder
     * @param  bool  $firstTimeframeActive
     * @param  bool  $secondTimeframeActive
     * @param  Carbon|null  $firstFromDate
     * @param  Carbon|null  $firstToDate
     * @param  Carbon|null  $secondFromDate
     * @param  Carbon|null  $secondToDate
     * @return void
     */
    private function getLeadCostColumns(
        Builder $builder,
        bool $firstTimeframeActive,
        bool $secondTimeframeActive,
        ?Carbon $firstFromDate,
        ?Carbon $firstToDate,
        ?Carbon $secondFromDate,
        ?Carbon $secondToDate
    ): void {
        $legacyLeadTableName = EloquentQuoteCompany::TABLE;

        $timestampOfInitialDeliveryFieldName = EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY;

        $leadCostOneColumn = self::COLUMN_LEAD_COST_ONE;
        $leadCostTwoColumn = self::COLUMN_LEAD_COST_TWO;

        $selectOneWithDateRange = "SUM(CASE WHEN `$legacyLeadTableName`.`$timestampOfInitialDeliveryFieldName` BETWEEN ? AND ? THEN `$legacyLeadTableName`.`cost` ELSE 0 END) AS `$leadCostOneColumn`";

        $selectTwoWithDateRange = "SUM(CASE WHEN `$legacyLeadTableName`.`$timestampOfInitialDeliveryFieldName` BETWEEN ? AND ? THEN `$legacyLeadTableName`.`cost` ELSE 0 END) AS `$leadCostTwoColumn`";

        $selectOneWithoutDateRange = "SUM(`$legacyLeadTableName`.`cost`) AS `$leadCostOneColumn`";

        if ($firstTimeframeActive && $secondTimeframeActive) {
            $builder->selectRaw("$selectOneWithDateRange, $selectTwoWithDateRange", [
                $firstFromDate->timestamp,
                $firstToDate->timestamp,
                $secondFromDate->timestamp,
                $secondToDate->timestamp,
            ]);
        } elseif ($firstTimeframeActive) {
            $builder->selectRaw("$selectOneWithDateRange", [
                $firstFromDate->timestamp,
                $firstToDate->timestamp,
            ]);
        } elseif ($secondTimeframeActive) {
            $builder->selectRaw("$selectOneWithDateRange", [
                $secondFromDate->timestamp,
                $secondToDate->timestamp,
            ]);
        } else {
            $builder->selectRaw("$selectOneWithoutDateRange");
        }
    }

    /**
     * Filter by the lead cost columns if they exist.
     *
     * @param  Builder  $builder
     * @param  Operator  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  int  $firstValue
     * @param  int|null  $secondValue
     * @param  Logical|null  $logical
     * @return void
     */
    private function filterByLeadCosts(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstValue,
        ?int $secondValue,
        ?Logical $logical
    ): void {
        $columnsList = $builder->getQuery()->columns;

        $expression = null;

        foreach ($columnsList as $column) {
            if ($column instanceof Expression) {
                $expression = $column;
            }
        }

        if (!$expression instanceof Expression) {
            return;
        }

        $grammar = $builder->getQuery()->getGrammar();

        $string = $expression->getValue($grammar);

        $hasLeadCostOneColumn = str_contains($string, self::COLUMN_LEAD_COST_ONE);
        $hasLeadCostTwoColumn = str_contains($string, self::COLUMN_LEAD_COST_TWO);

        $leadCostOneColumn = self::COLUMN_LEAD_COST_ONE;
        $leadCostTwoColumn = self::COLUMN_LEAD_COST_TWO;

        if (!$hasLeadCostOneColumn) {
            return;
        }

        if (!$hasLeadCostTwoColumn) {
            $leadCostTwoColumn = $leadCostOneColumn;
        }

        try {
            $firstOperatorSql = Operator::sqlOperator($firstOperator);
        } catch (Exception) {
            return;
        }

        $builder->having($leadCostOneColumn, $firstOperatorSql, $firstValue);

        if ($logical instanceof Logical) {
            try {
                $secondOperatorSql = Operator::sqlOperator($secondOperator);
            } catch (Exception) {
                return;
            }

            if ($logical === Logical::OR) {
                $builder->orHaving($leadCostTwoColumn, $secondOperatorSql, $secondValue);
            } else {
                $builder->having($leadCostTwoColumn, $secondOperatorSql, $secondValue);
            }
        }
    }
}
