<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Services\Filterables\DateRangeFilterableOption;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Exception;
use Illuminate\Support\Facades\DB;

class CompanyLastLeadPurchasedDateRangeFilterable extends DateRangeFilterableOption
{
    const string LAST_LEADS_QUERY       = 'last_leads_query';
    const string LAST_LEAD_DELIVERED    = 'last_lead_delivered';

    protected array $options = [];

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = 'Last Lead Purchased Date';
        $this->id = 'company-last-lead-purchased-date-range';
        $this->clearable = true;

        $this->options = [
            self::DATE_RANGE_PRESETS => [
                'Previous Week'     => [ self::DATE_RANGE_FROM => now()->subWeek()->subWeek(), self::DATE_RANGE_TO => now()->subWeek() ],
                'Previous Month'    => [ self::DATE_RANGE_FROM => now()->startOfMonth()->subMonth(), self::DATE_RANGE_TO => now()->startOfMonth() ],
            ],
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $startDate  = isset($value[self::DATE_RANGE_FROM]) ? new Carbon($value[self::DATE_RANGE_FROM]) : null;
        $endDate    = isset($value[self::DATE_RANGE_TO]) ? new Carbon($value[self::DATE_RANGE_TO]): null;

        if(!is_null($startDate) && !is_null($endDate))
        {
            $lastLeadQuery = ProductAssignment::query()
                ->select([
                    ProductAssignment::FIELD_COMPANY_ID,
                    DB::raw('MAX('.ProductAssignment::CREATED_AT.') AS '.self::LAST_LEAD_DELIVERED ),
                ])
                ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                ->where(ProductAssignment::FIELD_DELIVERED, true)
                ->groupBy(ProductAssignment::FIELD_COMPANY_ID);

            $builder->joinSub($lastLeadQuery, self::LAST_LEADS_QUERY , function ($join) use ($startDate, $endDate){
                $join->on(Company::TABLE.'.'.Company::FIELD_ID, '=', self::LAST_LEADS_QUERY .'.'.ProductAssignment::FIELD_COMPANY_ID)
                    ->where(self::LAST_LEADS_QUERY.'.'.self::LAST_LEAD_DELIVERED, '>=', $startDate)
                    ->where(self::LAST_LEADS_QUERY.'.'.self::LAST_LEAD_DELIVERED, '<=', $endDate);
            });
        }

        return $builder;
    }
}
