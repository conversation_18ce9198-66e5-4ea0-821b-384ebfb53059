<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Services\Filterables\MultiSelectFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Spatie\Permission\Models\Role;

class CompanyMissingRoleAssignmentsFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = 'Missing Role Assignments';
        $this->id = 'company-missing-role-assignments';
        $this->withRelations = null;

        $this->options = [
            'No Account Managers' => 'account-manager',
            'No Business Development Managers' => 'business-development-manager',
            'No Sales Development Representatives' => 'sales-development-representative',
            'No Customer Success Managers' => 'customer-success-manager',
            'No Onboarding Managers' => 'onboarding-manager',
        ];
    }

    /**
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder
            ->when(
                is_array($value) && filled($value),
                function (Builder $query) use ($value) {
                    $value = collect($value)->filter(fn ($option) => in_array($option, $this->options, true))->toArray();

                    $query->whereDoesntHave('userRelationships', function (Builder $query) use ($value) {
                        $query->whereHas('role', function (Builder $query) use ($value) {
                            $query->whereIn('name', $value);
                        });
                    });
                }
            );
    }
}
