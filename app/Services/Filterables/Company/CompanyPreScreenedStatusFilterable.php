<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Services\Filterables\SelectFilterableOption;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class CompanyPreScreenedStatusFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Pre-Screened Status";
        $this->id = 'company-pre-screened-status';
        $this->withRelations = null;

        $this->options = [
            'True'  => 'true',
            'False' => 'false',
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if(is_null($value)) return $builder;
        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);

        return $value
            ? $builder->whereDate(Company::FIELD_PRESCREENED_AT, '>',
                    Carbon::now()->subYear()->toDateString())
            : $builder->whereDate(Company::FIELD_PRESCREENED_AT, '<=',
                Carbon::now()->subYear()->toDateString());
    }

}