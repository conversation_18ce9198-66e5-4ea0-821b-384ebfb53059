<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Services\Filterables\MultiSelectWithLogicalFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyIndustryFilterable extends MultiSelectWithLogicalFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Industry";
        $this->id = 'company-industry';
        $this->withRelations = null;

        $this->options = [
            'logical' => Logical::AND->value,
            'options' => Industry::getAsKeyValueSelectArray(),
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!$this->isActive($value)) {
            return $builder;
        }

        try {
            list($logical, $options) = $this->validateValue($value);
        } catch (Exception $e) {
            $filterName = $this->name;

            throw new Exception("Issue w/ $filterName: ".$e->getMessage());
        }

        /**
         * @var Logical $logical
         */
        if ($logical === Logical::OR) {
            $builder->whereHas(Company::RELATION_INDUSTRIES, function (Builder $query) use ($options) {
                $query->whereIn(Industry::TABLE.'.'.Industry::FIELD_ID, $options);
            });
        } else {
            $builder->whereHas(Company::RELATION_INDUSTRIES, function (Builder $query) use ($options) {
                $query->whereIn(Industry::TABLE.'.'.Industry::FIELD_ID, $options);
            }, '=', count($options));
        }

        return $builder;
    }
}
