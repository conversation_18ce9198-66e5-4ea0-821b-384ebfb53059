<?php

namespace App\Services\Filterables\Company;

use App\Enums\CompanyConsolidatedStatus;
use App\Models\Odin\Company;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyConsolidatedStatusFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Can Receive Leads";
        $this->id = 'consolidated-status';
        $this->withRelations = null;

        $this->options = [
            'Yes' => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value,
            'No' => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS->value
        ];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return !is_null($value)
            ? $builder->where(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, $value)
            : $builder;
    }
}
