<?php

namespace App\Services\Filterables\Company;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyHasBiddingDisabledCampaignsFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Has Bidding Disabled Campaigns";
        $this->id = 'company-has-bidding-disabled-campaigns';
        $this->withRelations = null;

        $this->options = [
            'Yes' => 'yes',
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if ($value) {
            return $builder->whereHas(Company::RELATION_FUTURE_CAMPAIGNS, function (Builder $query) {
                $query->where(CompanyCampaign::FIELD_BIDDING_DISABLED, true)
                    ->whereNull(CompanyCampaign::FIELD_DELETED_AT);
            });
        } else {
            return $builder;
        }
    }
}
