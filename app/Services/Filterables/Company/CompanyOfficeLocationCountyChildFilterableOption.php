<?php

namespace App\Services\Filterables\Company;

use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\Consumer;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\Consumer\ConsumerLocationFilterable;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyOfficeLocationCountyChildFilterableOption extends MultiSelectFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model         = Company::class;
        $this->name          = "County";
        $this->id            = CompanyOfficeLocationFilterable::CHILD_FILTERABLE_COUNTY_ID;
        $this->withRelations = null;

        $this->options = [];
    }

    public function updateOptions(array $filterByKeys): ?array
    {
        $newOptions = Location::query()
            ->select(Location::STATE, Location::COUNTY, Location::COUNTY_KEY)
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->whereIn(Location::STATE, $filterByKeys)
            ->get()
            ->reduce(function (array $output, Location $county) {
                $output[$county->county] = $county->county_key;
                return $output;
            }, []);

        $this->options = $newOptions;

        return $newOptions;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!$value) return $builder;

        $zipcodes = Location::query()
            ->select([Location::ZIP_CODE])
            ->whereIn(Location::COUNTY_KEY, $value)
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->groupBy(Location::ZIP_CODE)
            ->get()
            ->pluck(Location::ZIP_CODE);

        $builder->whereHas(Company::RELATION_LOCATIONS, function (Builder $builder) use ($zipcodes) {
            $builder->whereHas(CompanyLocation::RELATION_ADDRESS, function (Builder $builder) use ($zipcodes) {
                $builder->whereIn(Address::FIELD_ZIP_CODE, $zipcodes);
            });
        });

        return $builder;
    }
}

