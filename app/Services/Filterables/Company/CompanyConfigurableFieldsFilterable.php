<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\GlobalCompanyField;
use App\Services\Filterables\Base\BinaryChoiceFilterable;
use App\Services\Filterables\Base\NumericFilterable;
use App\Services\Filterables\MultiSelectWithChildrenFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyConfigurableFieldsFilterable extends MultiSelectWithChildrenFilterableOption
{
    protected bool $bypassParentCheckForActiveFilterDisplay = true;

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Company Configurable Fields";
        $this->id = 'company-configurable-field';
        $this->withRelations = null;
        $this->children = $this->loadChildren();
    }

    private function loadChildren(): array
    {
        $fields = GlobalCompanyField::query()
            ->select([
                GlobalCompanyField::TABLE . '.' . GlobalCompanyField::FIELD_ID,
                GlobalCompanyField::TABLE . '.' . GlobalCompanyField::FIELD_NAME,
                GlobalCompanyField::TABLE . '.' . GlobalCompanyField::FIELD_KEY,
                ConfigurableFieldType::TABLE . '.' . ConfigurableFieldType::FIELD_TYPE,
            ])
            ->join(
                ConfigurableFieldType::TABLE,
                GlobalCompanyField::TABLE . '.' . GlobalCompanyField::FIELD_TYPE,
                ConfigurableFieldType::TABLE . '.' . ConfigurableFieldType::FIELD_ID
            )
            ->whereIn(ConfigurableFieldType::TABLE . '.' . ConfigurableFieldType::FIELD_TYPE, [
                ConfigurableFieldType::TYPE_BOOLEAN,
                ConfigurableFieldType::TYPE_INTEGER,
                ConfigurableFieldType::TYPE_FLOAT
            ])
            ->get();

        $children = [];

        foreach ($fields as $field) {
            $key = $field->key;
            $name = $field->name;
            $type = $field->type;

            if (in_array($type, [ConfigurableFieldType::TYPE_FLOAT, ConfigurableFieldType::TYPE_INTEGER])) {
                $children[] = new NumericFilterable($key, $name, Company::class);
            }
            if ($type === ConfigurableFieldType::TYPE_BOOLEAN) {
                $children[] = new BinaryChoiceFilterable($key, $name, Company::class);
            }
        }

        return $children;
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $filtered = collect($value)->filter(function ($item) {
            // Remove null, empty arrays and strings
            if ($item === null || (is_array($item) && empty($item)) || (is_string($item) && empty($item))) {
                return false;
            }

            return true;
        });


        if ($filtered->isEmpty()) {
            return $builder;
        }

        foreach ($filtered as $fieldKey => $value) {
            $builder->whereHas(Company::RELATION_DATA, function ($query) use ($fieldKey, $value) {
                $jsonField = CompanyData::FIELD_PAYLOAD . '->' . $fieldKey;

                if (!is_array($value)) {
                    $query->where($jsonField, $value);
                } else {
                    $query->where(
                        $jsonField,
                        Operator::sqlOperator($value['first_operator']),
                        $value['first_input']
                    );

                    if (isset($value['second_input']) && isset($value['second_operator'])) {
                        if ($value['logical'] === Logical::OR->value) {
                            $query->orWhere(
                                $jsonField,
                                Operator::sqlOperator($value['second_operator']),
                                $value['second_input']
                            );
                        } else if ($value['logical'] === Logical::AND->value) {
                            $query->where(
                                $jsonField,
                                Operator::sqlOperator($value['second_operator']),
                                $value['second_input']
                            );
                        }
                    }
                }
            });
        }

        return $builder;
    }
}
