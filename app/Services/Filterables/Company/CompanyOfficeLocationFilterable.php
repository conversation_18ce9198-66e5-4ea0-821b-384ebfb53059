<?php

namespace App\Services\Filterables\Company;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Services\Filterables\MultiSelectWithChildrenFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyOfficeLocationFilterable extends MultiSelectWithChildrenFilterableOption
{
    const CHILD_FILTERABLE_COUNTY_ID    = 'company-office-county';

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Office Location";
        $this->id = 'company-office-location';
        $this->withRelations = null;
        $this->filterPillDisplayLastChild = true;

        $this->options = Location::query()
            ->select(Location::STATE, Location::STATE_ABBREVIATION)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get()
            ->mapWithKeys(fn(Location $state) => [$state->state => $state->state])
            ->toArray();

        $this->children = [
            self::CHILD_FILTERABLE_COUNTY_ID    => new CompanyOfficeLocationCountyChildFilterableOption(),
        ];
    }


    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!$value) {
            return $builder;
        }

        $value = $value ?? [];

        $stateValues = $value[$this->getId()] ?? [];
        $countyValues = $value[self::CHILD_FILTERABLE_COUNTY_ID] ?? [];

        $updatedCountyKeys = $this->children[self::CHILD_FILTERABLE_COUNTY_ID]->updateOptions($stateValues);
        $this->validateChildInputs($countyValues, $updatedCountyKeys);

        $this->setUpdatedOptions(true);

        if($stateValues) {
            $stateValues = StateAbbreviation::fromStateNames($stateValues);
            $builder->whereHas(Company::RELATION_LOCATIONS, function (Builder $builder) use ($stateValues) {
                $builder->whereHas(CompanyLocation::RELATION_ADDRESS, function (Builder $builder) use ($stateValues) {
                    $builder->whereIn(Address::FIELD_STATE, $stateValues);
                });
            });
        }

        if ($countyValues)
            $this->children[self::CHILD_FILTERABLE_COUNTY_ID]->runQuery($builder, $countyValues);

        return $builder;
    }

    /**
     * Validate child options to handle user de-selecting parent regions
     * @param array $inputValues
     * @param array $validOptions
     * @return void
     */
    protected function validateChildInputs(array &$inputValues, array $validOptions): void
    {
        $validValues = array_values($validOptions);
        $inputValues = array_filter($inputValues, fn($value) => in_array($value, $validValues));
    }
}
