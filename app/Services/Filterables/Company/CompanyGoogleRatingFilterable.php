<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Services\Filterables\DualOperatorFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyGoogleRatingFilterable extends DualOperatorFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Google Rating";
        $this->id = 'company-google-rating';
        $this->withRelations = null;

        $this->options = [
            'min' => 1,
            'max' => 5,
        ];
    }

    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical
    ): Builder {
        $builder->whereHas(Company::RELATION_DATA, function ($has) use (
            $firstInput,
            $firstOperator,
            $logical,
            $secondInput,
            $secondOperator,
        ) {
            $sqlOperator = Operator::sqlOperator($firstOperator);

            $has->whereNotNull(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->google_rating")
                ->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->google_rating",
                    $sqlOperator,
                    $firstInput
                );

            if ($logical instanceof Logical && $secondOperator instanceof Operator && gettype($secondInput) == 'integer') {
                $sqlOperator = Operator::sqlOperator($secondOperator);

                $has->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->google_rating",
                    $sqlOperator,
                    $secondInput
                );
            }
        });

        return $builder;
    }
}
