<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Services\Filterables\SelectFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyQrTop500Filterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = 'QR Top 500';
        $this->id = 'company-qr-top-500';
        $this->withRelations = null;

        $this->options = [
            'Yes' => 'true',
            'No' => 'false',
        ];
    }

    /**
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (is_null($value)) {
            return $builder;
        }

        if (is_string($value)) {
            $value = filter_var($value, FILTER_VALIDATE_BOOL);
        }

        return $builder->where(function (Builder $query) use ($value) {
            $query->whereHas('data', fn (Builder $query) => $query->where('qr_top_500_company', $value));

            if (! $value) {
                $query->orWhereDoesntHave('data');
            }
        });
    }
}
