<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyDecisionMakerFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Decision Maker";
        $this->id = 'company-decision-maker';

        $this->options = [
            'Active Decision Maker' => true,
            'No Decision Maker'     => false,
        ];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if ($value === true) {
            $builder->whereHas(Company::RELATION_USERS, fn(Builder $query) =>
                $query->where(CompanyUser::FIELD_IS_DECISION_MAKER, true)
            );
        }
        else if ($value === false) {
            $builder->whereDoesntHave(Company::RELATION_USERS, fn(Builder $query) =>
                $query->where(CompanyUser::FIELD_IS_DECISION_MAKER, true)
            );
        }

        return $builder;
    }
}
