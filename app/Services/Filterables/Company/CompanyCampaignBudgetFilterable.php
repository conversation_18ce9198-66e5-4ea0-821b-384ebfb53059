<?php

namespace App\Services\Filterables\Company;

use App\Enums\Operator;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\CheckboxListDualOperatorFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyCampaignBudgetFilterable extends CheckboxListDualOperatorFilterableOption
{
    const KEY_NO_LIMIT = 'no_limit';
    const KEY_VOLUME = 'volume';
    const KEY_COST = 'cost';

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Campaign Budget";
        $this->id = 'company-campaign-budget';
        $this->withRelations = null;

        $this->options = [
            'noLimit' => [
                'key' => self::KEY_NO_LIMIT,
                'label' => 'No Limit',
                'selected' => false,
            ],
            'volume' => [
                'key' => self::KEY_VOLUME,
                'label' => 'Volume',
                'selected' => false,
                'operator' => Operator::GREATER_THAN_OR_EQUAL_TO->value,
                'value' => null,
                'units' => 'leads per day'
            ],
            'cost' => [
                'key' => self::KEY_COST,
                'label' => 'Cost',
                'selected' => false,
                'operator' => Operator::GREATER_THAN_OR_EQUAL_TO->value,
                'value' => null,
                'units' => 'cost per day'
            ],
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    /**
     * @throws Exception
     */
    private function getQuery(Builder $builder, mixed $value): Builder
    {
        if (!$this->isActive($value)) {
            return $builder;
        }

        try {
            $options = $this->validateValue($value);
        } catch (Exception $e) {
            throw new Exception('Invalid value given for '.$this->name.': '.$e->getMessage());
        }

        foreach ($options as $option) {
            $key = data_get($option, 'key');

            switch ($key) {
                case self::KEY_NO_LIMIT:
                    $selected = data_get($option, 'selected');

                    if (!$selected) {
                        break;
                    }

                    $this->filterByNoLimit($builder, data_get($option, 'selected'));
                    break;
                case self::KEY_VOLUME:
                    $selected = data_get($option, 'selected');

                    if (!$selected) {
                        break;
                    }

                    $operator = Operator::tryFrom(data_get($option, 'operator'));

                    $value = data_get($option, 'value');

                    if (!$operator) {
                        throw new Exception('Invalid operator given for volume.');
                    }

                    if (!$value) {
                        throw new Exception('Invalid value given for volume.');
                    }

                    $this->filterByVolume($builder, data_get($option, 'selected'), $operator,
                        $value);
                    break;
                case self::KEY_COST:
                    $selected = data_get($option, 'selected');

                    if (!$selected) {
                        break;
                    }

                    $operator = Operator::tryFrom(data_get($option, 'operator'));

                    $value = data_get($option, 'value');

                    if (!$operator) {
                        throw new Exception('Invalid operator given for cost.');
                    }

                    if (!$value) {
                        throw new Exception('Invalid value given for cost.');
                    }

                    $this->filterByCost($builder, data_get($option, 'selected'), $operator,
                        $value);
                    break;
                default:
                    break;
            }
        }

        return $builder;
    }

    private function filterByNoLimit(Builder $builder, bool $selected): void
    {
        if (!$selected) {
            return;
        }

        $builder->orWhere(function (Builder $query) use ($selected) {
            $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS, function (Builder $query) {
                $query->whereHas(ProductCampaign::RELATION_BUDGETS, function (Builder $query) {
                    $query->where(ProductCampaignBudget::FIELD_VALUE_TYPE,
                        ProductCampaignBudget::VALUE_TYPE_NO_LIMIT);
                });
            });

            $query->orWhereExists(function (\Illuminate\Database\Query\Builder $query) {
                $this->connectCompanyToLeadCampaign($query);

                $query->whereNotNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID);

                $query->where(function (
                    Builder|\Illuminate\Database\Query\Builder $query,
                ) {
                    $query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                        $query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                            $query->whereNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD)
                                ->orWhere(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD,
                                    '=',
                                    0);
                        });

                        $query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                            $query->whereNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND)->orWhere(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND,
                                '=', 0);
                        });
                    });
                });
            });
        });
    }

    /**
     * @param  \Illuminate\Database\Query\Builder  $query
     *
     * @return void
     */
    private function connectCompanyToLeadCampaign(\Illuminate\Database\Query\Builder $query): void
    {
        $query->fromRaw(
            '`'.DatabaseHelperService::readOnlyDatabase()."`.`".LeadCampaign::TABLE.'`',
        );

        $query->whereColumn(
            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID,
            Company::TABLE.'.'.Company::FIELD_LEGACY_ID);

        $query->whereNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.".".LeadCampaign::DELETED_AT);
    }

    private function filterByVolume(Builder $builder, bool $selected, Operator $operator, int $value): void
    {
        if (!$selected) {
            return;
        }

        $builder->orWhere(function ($query) use ($operator, $value) {
            $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS,
                function (Builder $query) use ($operator, $value) {
                    $query->whereHas(ProductCampaign::RELATION_BUDGETS,
                        function (Builder $query) use ($operator, $value) {
                            $query->where(function (Builder $query) use ($operator, $value) {
                                try {
                                    $sqlOperator = Operator::sqlOperator($operator);
                                } catch (Exception $e) {
                                    throw new Exception('Invalid volume operator'.$e->getMessage());
                                }

                                $query->orWhere(ProductCampaignBudget::FIELD_VALUE_TYPE,
                                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS)->where(ProductCampaignBudget::FIELD_VALUE,
                                    $sqlOperator, $value);
                            });
                        });
                });

            $query->orWhereExists(function (\Illuminate\Database\Query\Builder $query) use ($operator, $value) {
                $this->connectCompanyToLeadCampaign($query);

                $query->whereNotNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID);

                $query->where(function (
                    Builder|\Illuminate\Database\Query\Builder $query,
                ) use ($operator, $value) {
                    try {
                        $sqlOperator = Operator::sqlOperator($operator);
                    } catch (Exception $e) {
                        throw new Exception('Invalid volume operator'.$e->getMessage());
                    }

                    $query->orWhere(function (
                        Builder|\Illuminate\Database\Query\Builder $query,
                    ) use (
                        $sqlOperator,
                        $value,
                    ) {
                        $query->where(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD,
                            $sqlOperator,
                            $value);
                    });
                });
            });
        });
    }

    private function filterByCost(Builder $builder, bool $selected, Operator $operator, int $value)
    {
        if (!$selected) {
            return;
        }

        $builder->orWhere(function ($query) use ($operator, $value) {
            $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS,
                function (Builder $query) use ($operator, $value) {
                    $query->whereHas(ProductCampaign::RELATION_BUDGETS,
                        function (Builder $query) use ($operator, $value) {
                            $query->where(function (Builder $query) use ($operator, $value) {
                                try {
                                    $sqlOperator = Operator::sqlOperator($operator);
                                } catch (Exception $e) {
                                    throw new Exception('Invalid volume operator'.$e->getMessage());
                                }

                                $query->orWhere(ProductCampaignBudget::FIELD_VALUE_TYPE,
                                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND)->where(ProductCampaignBudget::FIELD_VALUE,
                                    $sqlOperator, $value);
                            });
                        });
                });

            $query->orWhereExists(function (\Illuminate\Database\Query\Builder $query) use ($operator, $value) {
                $this->connectCompanyToLeadCampaign($query);

                $query->whereNotNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID);

                $query->where(function (
                    Builder|\Illuminate\Database\Query\Builder $query,
                ) use ($operator, $value) {
                    try {
                        $sqlOperator = Operator::sqlOperator($operator);
                    } catch (Exception $e) {
                        throw new Exception('Invalid volume operator'.$e->getMessage());
                    }

                    $query->orWhere(function (
                        Builder|\Illuminate\Database\Query\Builder $query,
                    ) use (
                        $sqlOperator,
                        $value,
                    ) {
                        $query->where(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND,
                            $sqlOperator,
                            $value
                        );
                    });
                });
            });
        });
    }
}
