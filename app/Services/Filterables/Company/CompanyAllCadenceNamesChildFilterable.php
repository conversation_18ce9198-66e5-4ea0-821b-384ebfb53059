<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyAllCadenceNamesChildFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model         = Company::class;
        $this->name          = "All";
        $this->id            = CompanyCadenceNameFilterable::CHILD_FILTER_ALL_CADENCE_NAMES;
        $this->withRelations = null;

        $this->options = [];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }

    public function updateOptions(array $filterByKeys): ?array
    {
        $this->options = $filterByKeys;

        return null;
    }
}
