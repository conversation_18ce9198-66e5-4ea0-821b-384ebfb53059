<?php

namespace App\Services\Filterables\Company;

use App\Enums\CompanySalesStatus;
use App\Models\Odin\Company;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanySalesStatusFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Sales Status";
        $this->id = 'company-sales-status';
        $this->withRelations = null;

        $this->options = CompanySalesStatus::getAsKeyValueSelectArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Company::TABLE.'.'.Company::FIELD_SALES_STATUS, $value)
            : $builder;
    }
}
