<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

trait CompanyRejectionPercentageTrait
{
    protected int $productId;

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $manual = data_get($value, self::MANUAL_REJECTION);

        $crm = data_get($value, self::CRM_REJECTION);

        $overall = data_get($value, self::OVERALL_REJECTION);

        if (is_array($manual)) {
            $this->filter($builder, $manual, ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE);
        }

        if (is_array($crm)) {
            $this->filter($builder, $crm, ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE);
        }

        if (is_array($overall)) {
            $this->filter($builder, $overall, ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE);
        }

        return $builder;
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @param  string  $field
     * @return void
     */
    private function filter(Builder $builder, mixed $value, string $field,): void
    {
        $productId = $this->productId;

        $firstInput = data_get($value, 'first_input');
        $firstOperator = data_get($value, 'first_operator');
        $logical = data_get($value, 'logical');
        $secondInput = data_get($value, 'second_input');
        $secondOperator = data_get($value, 'second_operator');

        $firstOperator = Operator::tryFrom($firstOperator);
        $logical = Logical::tryFrom($logical);
        $secondOperator = Operator::tryFrom($secondOperator);

        if (!is_numeric($firstInput) || !($firstOperator instanceof Operator)) {
            return;
        }

        /**
         * @param  \Illuminate\Database\Query\Builder  $query
         * @param  int  $firstInput
         * @param  Operator  $firstOperator
         * @param  Logical|null  $logical
         * @param  int|null  $secondInput
         * @param  Operator|null  $secondOperator
         * @return void
         * @throws Exception
         */
        $filter = function (
            \Illuminate\Database\Query\Builder $query,
            int $firstInput,
            Operator $firstOperator,
            Logical|null $logical,
            int|null $secondInput,
            Operator|null $secondOperator,
        ) {
            $firstOperatorSql = Operator::sqlOperator($firstOperator);

            $query->having('avg_rejection_percentage', $firstOperatorSql,
                $firstInput);

            if ($logical instanceof Logical && $secondOperator instanceof Operator && is_numeric($secondInput)) {

                $secondOperatorSql = Operator::sqlOperator($secondOperator);

                if ($logical === Logical::AND) {
                    $query->having('avg_rejection_percentage', $secondOperatorSql,
                        $secondInput);
                } else {
                    $query->orHaving('avg_rejection_percentage', $secondOperatorSql,
                        $secondInput);
                }
            }
        };

        $builder->whereExists(function (\Illuminate\Database\Query\Builder $query) use (
            $productId,
            $firstInput,
            $firstOperator,
            $logical,
            $secondInput,
            $secondOperator,
            $filter,
            $field,
        ) {
            $query->from(ComputedRejectionStatistic::TABLE);

            $query->whereColumn(Company::TABLE.'.'.Company::FIELD_ID,
                ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_COMPANY_ID);

            $query->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, '=',
                $productId);

            $query->groupBy(Company::TABLE.'.'.Company::FIELD_ID);

            $query->select(DB::raw('avg(`'.ComputedRejectionStatistic::TABLE.'`.`'.$field.'`) as avg_rejection_percentage'));

            $filter($query, $firstInput, $firstOperator, $logical, $secondInput, $secondOperator);
        });
    }
}
