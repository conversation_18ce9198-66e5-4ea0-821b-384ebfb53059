<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\DualOperatorFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyConsumerRatingFilterable extends DualOperatorFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Consumer Rating";
        $this->id = 'company-consumer-rating';
        $this->withRelations = null;

        $this->options = [
            'min' => 1,
            'max' => 5,
        ];
    }

    /**
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical
    ): Builder {
        $builder->join(
            DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE,
            DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::COMPANY_ID,
            '=',
            DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
        );

        $builder->where(
            DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::BAYESIAN_ALL_TIME,
            Operator::sqlOperator($firstOperator),
            $firstInput
        );

        if ($logical === Logical::AND && $secondOperator instanceof Operator && gettype($secondInput) == 'integer') {
            $builder->where(
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::BAYESIAN_ALL_TIME,
                Operator::sqlOperator($secondOperator),
                $secondInput
            );
        } else {
            $builder->orWhere(
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::BAYESIAN_ALL_TIME,
                Operator::sqlOperator($secondOperator),
                $secondInput
            );
        }

        return $builder;
    }
}
