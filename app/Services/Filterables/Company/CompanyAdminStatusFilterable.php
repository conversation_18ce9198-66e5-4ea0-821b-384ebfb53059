<?php

namespace App\Services\Filterables\Company;

use App\Enums\Company\CompanyAdminStatus;
use App\Models\Odin\Company;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyAdminStatusFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Admin Status";
        $this->id = 'admin-status';
        $this->withRelations = null;

        $this->options = CompanyAdminStatus::getAsKeyValueSelectArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Company::TABLE.'.'.Company::FIELD_ADMIN_STATUS, $value)
            : $builder;
    }
}
