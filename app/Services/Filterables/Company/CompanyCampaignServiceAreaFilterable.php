<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Enums\Odin\StateAbbreviation;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\MultiSelectWithLogicalFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CompanyCampaignServiceAreaFilterable extends MultiSelectWithLogicalFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Campaign Service Area";
        $this->id = 'company-campaign-service-area';
        $this->withRelations = null;

        $this->options = [
            'logical' => Logical::AND->value,
            'options' => StateAbbreviation::getAsKeyValueSelectArray(),
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    private function getQuery(Builder $builder, mixed $value): Builder
    {
        if (!$this->isActive($value)) {
            return $builder;
        }

        try {
            list($logical, $options) = $this->validateValue($value);
        } catch (Exception $e) {
            $filterName = $this->name;

            throw new Exception("Issue w/ $filterName: ".$e->getMessage());
        }

        $builder->whereExists(function (\Illuminate\Database\Query\Builder $query) use ($options, $logical) {
            $query
                ->select(DB::raw(1))
                ->from(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE,
                    'legacy_lead_campaigns')
                ->whereColumn('legacy_lead_campaigns.'.LeadCampaign::COMPANY_ID,
                    Company::TABLE.'.'.Company::FIELD_LEGACY_ID)
                ->whereIn('legacy_lead_campaigns.id',
                    function (\Illuminate\Database\Query\Builder $queryTwo) use ($options, $logical) {
                        $queryTwo
                            ->select('legacy_lead_campaign_locations.'.LeadCampaignLocation::LEAD_CAMPAIGN_ID)
                            ->from(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLocation::TABLE,
                                'legacy_lead_campaign_locations')
                            ->whereIn('legacy_lead_campaign_locations.'.LeadCampaignLocation::LOCATION_ID,
                                function (\Illuminate\Database\Query\Builder $queryThree) use ($options, $logical) {
                                    $queryThree
                                        ->select('legacy_locations.'.Location::ID)
                                        ->from(Location::TABLE,
                                            'legacy_locations')
                                        ->whereIn('legacy_locations.'.Location::STATE_ABBREVIATION,
                                            $options);
                                });

                        if ($logical === Logical::AND) {
                            $column = 'legacy_lead_campaign_locations.'.LeadCampaignLocation::LEAD_CAMPAIGN_ID;

                            $rawCountString = 'COUNT('.$column.') = '.count($options);

                            $queryTwo->groupBy($column)
                                ->havingRaw($rawCountString);
                        }
                    });
        });

        return $builder;
    }
}
