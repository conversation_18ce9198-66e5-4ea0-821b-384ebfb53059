<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Models\User;
use App\Services\Filterables\MultiSelectFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyAccountManagerFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model         = Company::class;
        $this->name          = "Account Manager";
        $this->id            = 'company-account-manager';
        $this->withRelations = null;

        $this->options = User::accountManagerRole()
            ->orderBy('name')
            ->pluck('id', 'name')
            ->toArray();
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder
            ->when(
                is_array($value) && filled($value),
                fn (Builder $query) => $query->whereHas('accountManager',
                    fn(Builder $query) => $query->whereIn('users.id', $value)
                )
            )
            ->when(
                is_numeric($value) && filled($value),
                fn (Builder $query) => $query->whereHas('accountManager',
                    fn(Builder $query) => $query->where('users.id', $value)
                )
            );
    }
}
