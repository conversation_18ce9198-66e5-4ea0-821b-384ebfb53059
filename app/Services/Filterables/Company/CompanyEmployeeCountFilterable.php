<?php

namespace App\Services\Filterables\Company;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Services\Filterables\DualOperatorFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyEmployeeCountFilterable extends DualOperatorFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Employee Count";
        $this->id = 'company-employee-count';
        $this->withRelations = null;

        $this->options = [
            'min' => 0,
        ];
    }

    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical
    ): Builder {
        $builder->whereHas(Company::RELATION_DATA, function ($has) use (
            $firstInput,
            $firstOperator,
            $logical,
            $secondInput,
            $secondOperator,
        ) {
            $sqlOperator = Operator::sqlOperator($firstOperator);

            $has->whereNotNull(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->employee_count")
                ->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->employee_count",
                    $sqlOperator,
                    $firstInput
                );

            if ($logical instanceof Logical && $secondOperator instanceof Operator && gettype($secondInput) == 'integer') {
                $sqlOperator = Operator::sqlOperator($secondOperator);

                $has->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->employee_count",
                    $sqlOperator,
                    $secondInput
                );
            }
        });

        return $builder;
    }
}
