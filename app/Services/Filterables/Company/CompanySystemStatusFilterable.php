<?php

namespace App\Services\Filterables\Company;

use App\Enums\Company\CompanySystemStatus;
use App\Models\Odin\Company;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanySystemStatusFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "System Status";
        $this->id = 'system-status';
        $this->withRelations = null;

        $this->options = CompanySystemStatus::getAsKeyValueSelectArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(Company::TABLE.'.'.Company::FIELD_SYSTEM_STATUS, $value)
            : $builder;
    }
}
