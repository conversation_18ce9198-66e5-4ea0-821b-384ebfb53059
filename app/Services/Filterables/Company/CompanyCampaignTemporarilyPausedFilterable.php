<?php

namespace App\Services\Filterables\Company;

use App\Enums\Campaigns\CampaignStatus as CompanyCampaignStatus;
use App\Enums\Odin\ProductCampaignStatusType;
use App\Enums\Operator;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ProductCampaign;
use App\Services\Filterables\CheckboxListDualOperatorFilterableOption;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyCampaignTemporarilyPausedFilterable extends CheckboxListDualOperatorFilterableOption
{
    const string PERMANENT_PAUSED_CAMPAIGNS = 'permanent_paused_campaigns';
    const string TEMPORALLY_PAUSED_FOR      = 'Temporally paused for';

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Paused Campaigns";
        $this->id = 'paused-campaigns';
        $this->withRelations = null;

        $this->options = [
            self::PERMANENT_PAUSED_CAMPAIGNS => [
                'key'      => self::PERMANENT_PAUSED_CAMPAIGNS,
                'label'    => 'Permanent',
                'selected' => false,
            ],
            self::TEMPORALLY_PAUSED_FOR      => [
                'key'      => self::TEMPORALLY_PAUSED_FOR,
                'label'    => 'Temporally paused for',
                'selected' => false,
                'operator' => Operator::GREATER_THAN_OR_EQUAL_TO->value,
                'value'    => null,
                'units'    => 'months'
            ],
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    /**
     * @throws Exception
     */
    private function getQuery(Builder $builder, mixed $value): Builder
    {
        if (!$this->isActive($value)) {
            return $builder;
        }

        try {
            $options = $this->validateValue($value);
        } catch (Exception $e) {
            throw new Exception('Invalid value given for ' . $this->name . ': ' . $e->getMessage());
        }

        foreach ($options as $option) {
            $key = data_get($option, 'key');

            switch ($key) {
                case self::PERMANENT_PAUSED_CAMPAIGNS:
                    $selected = data_get($option, 'selected');

                    if (!$selected) {
                        break;
                    }

                    $this->filterPermanentPausedCampaigns($builder, data_get($option, 'selected'));
                    break;
                case self::TEMPORALLY_PAUSED_FOR:
                    $selected = data_get($option, 'selected');

                    if (!$selected) {
                        break;
                    }

                    $operator = Operator::tryFrom(data_get($option, 'operator'));

                    $value = data_get($option, 'value');

                    if (!$operator) {
                        throw new Exception('Invalid operator given for volume.');
                    }

                    if (!$value) {
                        throw new Exception('Invalid value given for volume.');
                    }

                    $this->filterByCampaignsWithingGap($builder, $operator, $value);
                    break;
                default:
                    break;
            }
        }

        return $builder;
    }

    /**
     * @param Builder $builder
     * @param bool $selected
     * @return void
     */
    private function filterPermanentPausedCampaigns(Builder $builder, bool $selected): void
    {
        if (!$selected) {
            return;
        }

        $builder->where(function (Builder $query) use ($selected) {
            $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS, function (Builder $query) {
                $query->where(ProductCampaign::FIELD_STATUS, ProductCampaignStatusType::PERMANENT);
            })->orWhereHas(Company::RELATION_FUTURE_CAMPAIGNS, function ($query) {
                $query->where(CompanyCampaign::FIELD_STATUS, CompanyCampaignStatus::PAUSED_PERMANENTLY);
            });
        });
    }

    /**
     * @param Builder $builder
     * @param Operator $operator
     * @param mixed $value
     * @return void
     * @throws Exception
     */
    private function filterByCampaignsWithingGap(Builder $builder, Operator $operator, mixed $value): void
    {
        if (empty($value)) {
            return;
        }

        $sqlOperator = $operator->sqlOperator($operator->value);
        $targetDate = Carbon::now()->addMonths($value);

        $builder->where(function (Builder $query) use ($sqlOperator, $targetDate) {
            $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS, function (Builder $query) use ($sqlOperator, $targetDate) {
                $query->where(ProductCampaign::FIELD_STATUS, ProductCampaignStatusType::TEMPORARY)
                    ->where(ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP, '>', now()->timestamp)
                    ->where(ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP, $sqlOperator, $targetDate->timestamp);
            })->orWhereHas(Company::RELATION_FUTURE_CAMPAIGNS, function (Builder $query) use ($sqlOperator, $targetDate) {
                $query->where(CompanyCampaign::FIELD_STATUS, CompanyCampaignStatus::PAUSED_TEMPORARILY)
                    ->whereHas(CompanyCampaign::RELATION_REACTIVATION, function (Builder $query) use ($sqlOperator, $targetDate) {
                        $query->where(CampaignReactivation::FIELD_REACTIVATE_AT, $sqlOperator, $targetDate)
                            ->where(CampaignReactivation::FIELD_REACTIVATE_AT, '>', now());
                    });
            });
        });
    }
}
