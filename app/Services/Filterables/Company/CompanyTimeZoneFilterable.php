<?php

namespace App\Services\Filterables\Company;

use App\Enums\SupportedTimezones;
use App\Models\Legacy\EloquentZipCode;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Services\Filterables\MultiSelectFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class CompanyTimeZoneFilterable extends MultiSelectFilterableOption
{
    public function __construct(
        protected ZipCodeSetRepository $zipCodeSetRepository,
    )
    {
        $this->model = Company::class;
        $this->name = "Company Time Zone";
        $this->id = 'company-time-zone';
        $this->withRelations = null;

        $this->options = SupportedTimezones::getAsKeyValueSelectArray();
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (empty($value)) {
            return $builder;
        }

        $zipcodes = EloquentZipCode::query()->whereIn(EloquentZipCode::FIELD_UTC, $value)->pluck(EloquentZipCode::FIELD_ZIP_CODE)->toArray();

        $builder->whereHas(Company::RELATION_LOCATIONS, function ($builder) use ($value, $zipcodes) {
            $builder->whereHas(CompanyLocation::RELATION_ADDRESS, function ($builder) use ($value, $zipcodes) {
                $builder->whereIn(Address::FIELD_UTC, $value)
                    ->orWhereIn(Address::FIELD_ZIP_CODE, $zipcodes);
            });
        });

        return $builder;
    }
}
