<?php

namespace App\Services\Filterables\Company;

use App\Enums\Cadence;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Odin\Company;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyCadenceStatusFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Cadence Status";
        $this->id = 'company-cadence-status';
        $this->withRelations = null;

        $this->options = Cadence::getAsKeyValueSelectArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        if ($value === Cadence::NOT_ASSIGNED->value) {
            $builder->whereDoesntHave(Company::RELATION_CADENCE_ROUTINES);
        } elseif ($value === Cadence::NEVER_ASSIGNED->value) {
            $builder->whereDoesntHave(Company::RELATION_ALL_CADENCE_ROUTINES);
        } else {
            $builder->whereHas(Company::RELATION_CADENCE_ROUTINES, function (Builder $query) use ($value) {

                if ($value === Cadence::ONGOING->value) {
                    $query->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_PENDING);
                }

                if ($value === Cadence::COMPLETED->value) {
                    $query->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_CONCLUDED);
                }
            });
        }

        return $builder;
    }
}
