<?php

namespace App\Services\Filterables\Company;

use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Odin\Company;
use App\Models\User;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\MultiSelectWithChildrenFilterableOption;
use Dflydev\DotAccessData\Data;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use function logger;

class CompanyCadenceNameFilterable extends MultiSelectWithChildrenFilterableOption
{
    const CHILD_FILTER_ALL_CADENCE_NAMES = 'company-cadence-name-all';

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Cadence Name";
        $this->id = 'company-cadence-name';
        $this->withRelations = null;

        $this->options = [];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        /** @var Collection $combinedValues */
        $combinedValues = collect($value)->reduce(function($output, array $subArray) {
            $output->push(...$subArray);
            return $output;
        }, collect());
        $flatArray = $combinedValues->unique()->toArray();

        if (!$flatArray) return $builder;

        if (!DatabaseHelperService::joinExists($builder->getQuery(), CompanyCadenceRoutine::TABLE)) {
            $builder->join(CompanyCadenceRoutine::TABLE, CompanyCadenceRoutine::TABLE .'.'. CompanyCadenceRoutine::FIELD_COMPANY_ID, '=', Company::TABLE .'.'. Company::FIELD_ID);
        }
        if (!DatabaseHelperService::joinExists($builder->getQuery(), CadenceRoutine::TABLE)) {
            $builder->join(CadenceRoutine::TABLE, CadenceRoutine::TABLE .'.'. CadenceRoutine::FIELD_ID, '=', CompanyCadenceRoutine::TABLE .'.'. CompanyCadenceRoutine::FIELD_CADENCE_ROUTINE_ID);
        }

        return $builder->whereIn(CadenceRoutine::TABLE .'.'. CadenceRoutine::FIELD_ID, $flatArray);
    }

    public function getFilterableDisplayData(): array
    {
        $this->updateCadenceOptions();

        $childDisplayData = [];
        foreach ($this->children as $childFilterable) {
            $childDisplayData[$childFilterable->getId()] = $childFilterable->getFilterableDisplayData();
        }

        return [
            ...parent::getFilterableDisplayData(),
            'children'                   => $childDisplayData,
            'filterPillDisplayLastChild' => $this->filterPillDisplayLastChild,
        ];
    }

    public function updateCadenceOptions(): void
    {
        /** @var User $user */
        $user = Auth::user();

        $allRoutines = CadenceRoutine::query()
            ->select([CadenceRoutine::FIELD_ID, CadenceRoutine::FIELD_USER_ID, CadenceRoutine::FIELD_NAME])
            ->get();

        $userRoutines = $user
            ? $allRoutines->filter(fn(CadenceRoutine $routine) => $routine->user_id === $user?->id)
                ->mapWithKeys(fn(CadenceRoutine $routine) => [$routine->name => $routine->id])
            : collect();

        $allRoutines = $allRoutines->mapWithKeys(fn(CadenceRoutine $routine) => [$routine->name => $routine->id]);

        if ($userRoutines->count()) {
            $this->children = [
                self::CHILD_FILTER_ALL_CADENCE_NAMES => new CompanyAllCadenceNamesChildFilterable(),
            ];

            $this->options = $userRoutines->toArray();
            $this->children[self::CHILD_FILTER_ALL_CADENCE_NAMES]->updateOptions($allRoutines->toArray());
        }
        else {
            $this->options = $allRoutines->toArray();
        }
    }
}
