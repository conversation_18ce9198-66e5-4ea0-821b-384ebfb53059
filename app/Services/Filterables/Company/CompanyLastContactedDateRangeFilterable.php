<?php

namespace App\Services\Filterables\Company;

use App\Enums\ActivityType;
use App\Models\ActivityFeed;
use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Text;
use App\Services\Filterables\DateRangeFilterableOption;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Exception;
use Illuminate\Database\Eloquent\Model;

class CompanyLastContactedDateRangeFilterable extends DateRangeFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = 'Last Contacted Date';
        $this->id = 'company-last-contacted-date-range';
        $this->clearable = true;

        $this->options = [
            self::DATE_RANGE_PRESETS => [
                'Last 24 Hours'         => [ self::DATE_RANGE_FROM => now()->subDay(), self::DATE_RANGE_TO => now() ],
                'Last Week'             => [ self::DATE_RANGE_FROM => now()->subWeek(), self::DATE_RANGE_TO => now() ],
                'Last Month'            => [ self::DATE_RANGE_FROM => now()->subMonth(), self::DATE_RANGE_TO => now() ],
                'This Calendar Month'   => [ self::DATE_RANGE_FROM => now()->startOfMonth(), self::DATE_RANGE_TO => now() ],
            ],
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $startDate  = isset($value[self::DATE_RANGE_FROM]) ? new Carbon($value[self::DATE_RANGE_FROM]) : null;
        $endDate    = isset($value[self::DATE_RANGE_TO]) ? new Carbon($value[self::DATE_RANGE_TO]): null;

        if(!is_null($startDate) && !is_null($endDate))
        {
            $builder->whereHas(Company::RELATION_ACTIVITY_FEEDS, function (Builder $query) use ($startDate, $endDate) {
                $query->whereHasMorph(ActivityFeed::RELATION_ACTIVITY_ITEM, ActivityType::CALL->value, function (Builder $query
                ) use ($startDate, $endDate) {
                    return $query->when($startDate, function ($q, $startDate) {
                        $q->where(Call::TABLE . '.' . Call::FIELD_CALL_END, '>=', $startDate);
                    })->when($endDate, function ($q, $endDate) {
                        $q->where(Call::TABLE . '.' . Call::FIELD_CALL_END, '<=', $endDate);
                    });
                });
                $query->orwhereHasMorph(ActivityFeed::RELATION_ACTIVITY_ITEM, ActivityType::TEXT->value, function (Builder $query
                ) use ($startDate, $endDate) {
                    return $query->when($startDate, function ($q, $startDate) {
                        $q->where(Text::TABLE . '.' . Model::UPDATED_AT, '>=', $startDate);
                    })->when($endDate, function ($q, $endDate) {
                        $q->where(Text::TABLE . '.' . Model::UPDATED_AT, '<=', $endDate);
                    });
                });
            });
        }

        return $builder;
    }
}