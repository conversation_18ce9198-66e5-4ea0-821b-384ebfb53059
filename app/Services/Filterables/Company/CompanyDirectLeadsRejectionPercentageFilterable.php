<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Repositories\Odin\ProductRepository;
use App\Services\Filterables\Base\NumericFilterable;
use App\Services\Filterables\MultiSelectWithChildrenFilterableOption;

class CompanyDirectLeadsRejectionPercentageFilterable extends MultiSelectWithChildrenFilterableOption
{
    use CompanyRejectionPercentageTrait;

    const string MANUAL_REJECTION = 'company-direct-leads-rejection-percentage-manual';

    const string CRM_REJECTION = 'company-direct-leads-rejection-percentage-crm';

    const string OVERALL_REJECTION = 'company-direct-leads-rejection-percentage-overall';

    protected bool $bypassParentCheckForActiveFilterDisplay = true;

    public function __construct( protected ProductRepository $productRepository)
    {
        $this->model = Company::class;
        $this->name = 'Direct Leads Rejections';
        $this->id = 'company-direct-leads-rejection-percentage';
        $this->withRelations = null;
        $this->children = $this->loadChildren();
        $this->productId = $this->productRepository->getDirectLeadProductId();
    }

    /**
     * @return array
     */
    private function loadChildren(): array
    {
        return [
            new NumericFilterable(self::MANUAL_REJECTION, 'Manual Rejection %', Company::class),
            new NumericFilterable(self::CRM_REJECTION, 'CRM Rejection %', Company::class),
            new NumericFilterable(self::OVERALL_REJECTION, 'Overall Rejection %', Company::class),
        ];
    }
}
