<?php

namespace App\Services\Filterables\Company;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyNeverExceedsBudgetFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Never Exceeds Budget";
        $this->id = 'company-never-exceeds-budget';
        $this->withRelations = null;

        $this->options = [
            'True' => 'true',
            'False' => 'false',
        ];
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $value = (bool) $value;

        return $value
            ? $builder->whereHas(Company::RELATION_LEGACY_COMPANY, function (Builder $builder) use ($value) {
                $builder->from(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE,
                    EloquentCompany::TABLE);

                $builder->where(EloquentCompany::TABLE.'.'.EloquentCompany::NEVER_EXCEED_BUDGET,
                    $value);
            })
            : $builder;
    }
}
