<?php

namespace App\Services\Filterables\Company;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\CompanyMetric;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\DualOperatorFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CompanyEstimatedMonthlyPPCSpendFilterable extends DualOperatorFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Estimated Monthly Ad Spend";
        $this->id = 'estimated-monthly-ad-spend';
        $this->withRelations = null;

        $this->options = [
            'min' => 1,
        ];
    }

    /**
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical
    ): Builder {

        $sqlOperator = Operator::sqlOperator($firstOperator);

        $builder->whereNotNull(CompanyMetric::TABLE.'.'.CompanyMetric::FIELD_REQUEST_RESPONSE."->monthlySpend")
            ->where(
                CompanyMetric::TABLE.'.'.CompanyMetric::FIELD_REQUEST_RESPONSE."->monthlySpend",
                $sqlOperator,
                $firstInput
            );

        if ($logical instanceof Logical && $secondOperator instanceof Operator && gettype($secondInput) == 'integer') {
            $sqlOperator = Operator::sqlOperator($secondOperator);

            $builder->where(
                CompanyMetric::TABLE.'.'.CompanyMetric::FIELD_REQUEST_RESPONSE."->monthlySpend",
                $sqlOperator,
                $secondInput
            );
        }

        return $builder;
    }
}
