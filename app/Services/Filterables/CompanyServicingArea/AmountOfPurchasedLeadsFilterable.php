<?php

namespace App\Services\Filterables\CompanyServicingArea;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\HorizontalDualOperatorWithDropdownOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class AmountOfPurchasedLeadsFilterable extends HorizontalDualOperatorWithDropdownOption
{
    public function __construct()
    {
        $this->model = EloquentCompany::class;
        $this->name = "Amount of Leads Purchased";
        $this->id = 'company-servicing-area-amount-of-leads-purchased';
        $this->withRelations = null;

        $this->dropdownOptions = [
            [
                'id' => 'allTime',
                'name' => 'All Time',
            ],
            [
                'id' => 'last30Days',
                'name' => 'Last 30 Days',
            ],
            [
                'id' => 'last60Days',
                'name' => 'Last 60 Days',
            ],
            [
                'id' => 'last90Days',
                'name' => 'Last 90 Days',
            ],
            [
                'id' => 'lastSixMonths',
                'name' => 'Last Six Months',
            ],
            [
                'id' => 'lastYear',
                'name' => 'Last Year',
            ],
            [
                'lastTwoYears',
                'name' => 'Last Two Years',
            ]
        ];

        $this->options = [
            'dropdown' => $this->dropdownOptions,
            'default' => 'last60Days',
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  Operator  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  int  $firstInput
     * @param  int|null  $secondInput
     * @param  Logical|null  $logical
     * @param  string  $option
     * @return Builder
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical,
        string $option,
    ): Builder {
        $column = 'lead_cost';

        $getLeadCost = function (Builder $builder, string $option) use ($column) {
            $builder->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.' as '.EloquentQuoteCompany::TABLE,
                function (JoinClause $join) use (
                    $option
                ) {
                    $join->on(Company::TABLE.".".Company::FIELD_LEGACY_ID, '=',
                        EloquentQuoteCompany::TABLE.".".EloquentQuoteCompany::COMPANY_ID);

                    $days = config('models.EloquentQuoteCompany.range_by_delivered_timestamp_in_days');
                    $join->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                        '>=', now()->subDays($days)->timestamp);
                });

            $builder->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, '=', 1);

            $builder->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, '=', 1);

            $timestamps = match ($option) {
                'allTime' => [
                    now()->subYears(100)->timestamp,
                    now()->timestamp,
                ],
                'last30Days' => [
                    now()->subDays(30)->timestamp,
                    now()->timestamp,
                ],
                'last60Days' => [
                    now()->subDays(60)->timestamp,
                    now()->timestamp,
                ],
                'last90Days' => [
                    now()->subDays(90)->timestamp,
                    now()->timestamp,
                ],
                'lastSixMonths' => [
                    now()->subMonths(6)->timestamp,
                    now()->timestamp,
                ],
                'lastYear' => [
                    now()->subYear()->timestamp,
                    now()->timestamp,
                ],
                'lastTwoYears' => [
                    now()->subYears(2)->timestamp,
                    now()->timestamp,
                ],
                default => throw new Exception("Invalid option: $option"),
            };

            $builder->where(function (Builder $builder) use ($timestamps) {
                $builder->whereBetween(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY,
                    $timestamps);
            });

            $legacyLeadTableName = EloquentQuoteCompany::TABLE;

            $timestampOfInitialDeliveryFieldName = EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY;

            $leadCostSelect = "SUM(CASE WHEN `$legacyLeadTableName`.`$timestampOfInitialDeliveryFieldName` BETWEEN ? AND ? THEN `$legacyLeadTableName`.`cost` ELSE 0 END) AS `$column`";

            $builder->selectRaw($leadCostSelect, $timestamps);
        };

        $getLeadCost($builder, $option);

        return $this->defaultQueryLogic($builder, $column, $firstOperator, $secondOperator, $firstInput, $secondInput,
            $logical);
    }
}
