<?php

namespace App\Services\Filterables\CompanyServicingArea;

use App\Models\Legacy\EloquentCompany;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class CompanyServicingAreaFilterableService extends BaseFilterableService
{
    const FILTERABLE_CATEGORY = 'company-servicing-area';

    protected array $filters = [
        StatusFilterable::class,
        SalesStatusFilterable::class,
        ContactsCountFilterable::class,
        AmountOfPurchasedLeadsFilterable::class,
        EstimatedRevenueFilterable::class,
        EmployeeCountFilterable::class,
        GoogleReviewsFilterable::class,
        GoogleRatingFilterable::class,
    ];

    protected string $baseModel = EloquentCompany::class;

    /**
     * @param  array  $results
     * @param  Builder|null  $baseQuery
     * @return Builder
     */
    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? EloquentCompany::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }
}
