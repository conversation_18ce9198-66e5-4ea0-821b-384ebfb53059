<?php

namespace App\Services\Filterables\CompanyServicingArea;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\EloquentCompany;
use App\Services\Filterables\HorizontalDualOperatorOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class EmployeeCountFilterable extends HorizontalDualOperatorOption
{
    public function __construct()
    {
        $this->model = EloquentCompany::class;
        $this->name = "Employee Count";
        $this->id = 'company-servicing-area-employee-count';
        $this->withRelations = null;
    }

    /**
     * @param  Builder  $builder
     * @param  Operator  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  int  $firstInput
     * @param  int|null  $secondInput
     * @param  Logical|null  $logical
     * @return Builder
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical,
    ): Builder {
        $column = 'employee_count';

        return $this->defaultQueryLogic($builder, $column, $firstOperator, $secondOperator, $firstInput, $secondInput, $logical);
    }
}
