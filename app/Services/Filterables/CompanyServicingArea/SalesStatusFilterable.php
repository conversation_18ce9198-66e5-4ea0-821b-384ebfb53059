<?php

namespace App\Services\Filterables\CompanyServicingArea;

use App\Enums\CompanySalesStatus;
use App\Enums\IsOrIsNot;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\HorizontalIsOrIsNotMultiSelectOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class SalesStatusFilterable extends HorizontalIsOrIsNotMultiSelectOption
{
    public function __construct()
    {
        $this->model = EloquentCompany::class;
        $this->name = "Sales Status";
        $this->id = 'company-servicing-area-sales-status';
        $this->withRelations = null;
        $this->options = CompanySalesStatus::asSelectArray();
    }

    /**
     * @param  Builder  $builder
     * @param  IsOrIsNot  $isOrIsNot
     * @param  array  $selectedOptions
     * @return Builder
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        IsOrIsNot $isOrIsNot,
        array $selectedOptions,
    ): Builder {
        $column = 'sales_status';

        $builder->whereHas(EloquentCompany::RELATION_MI_COMPANY, function (Builder $builder) use ($column, $isOrIsNot, $selectedOptions) {
            if ($isOrIsNot === IsOrIsNot::IS) {
                $builder->from(DatabaseHelperService::database().'.'.Company::TABLE)->whereIn($column, $selectedOptions);
            } else {
                $builder->from(DatabaseHelperService::database().'.'.Company::TABLE)->whereNotIn($column, $selectedOptions);
            }
        });

        return $builder;
    }
}
