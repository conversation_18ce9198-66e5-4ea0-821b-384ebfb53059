<?php

namespace App\Services\Filterables\CompanyServicingArea;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\EloquentCompany;
use App\Services\Filterables\HorizontalDualOperatorOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class GoogleRatingFilterable extends HorizontalDualOperatorOption
{
    public function __construct()
    {
        $this->model = EloquentCompany::class;
        $this->name = "Google Rating";
        $this->id = 'company-servicing-area-google-rating';
        $this->withRelations = null;
        $this->options = [
            self::INPUT_MIN => 1,
            self::INPUT_MAX => 5,
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  Operator  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  int  $firstInput
     * @param  int|null  $secondInput
     * @param  Logical|null  $logical
     * @return Builder
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical,
    ): Builder {
        $column = 'google_rating';

        return $this->defaultQueryLogic($builder, $column, $firstOperator, $secondOperator, $firstInput, $secondInput,
            $logical);
    }
}
