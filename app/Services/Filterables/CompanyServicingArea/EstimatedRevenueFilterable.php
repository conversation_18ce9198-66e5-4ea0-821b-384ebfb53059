<?php

namespace App\Services\Filterables\CompanyServicingArea;

use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\EloquentCompany;
use App\Services\Filterables\HorizontalDualOperatorOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class EstimatedRevenueFilterable extends HorizontalDualOperatorOption
{
    public function __construct()
    {
        $this->model = EloquentCompany::class;
        $this->name = "Estimated Revenue";
        $this->id = 'company-servicing-area-estimated-revenue';
        $this->withRelations = null;
    }

    /**
     * @param  Builder  $builder
     * @param  Operator  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  int  $firstInput
     * @param  int|null  $secondInput
     * @param  Logical|null  $logical
     * @return Builder
     * @throws Exception
     */
    protected function getQuery(
        Builder $builder,
        Operator $firstOperator,
        ?Operator $secondOperator,
        int $firstInput,
        ?int $secondInput,
        ?Logical $logical,
    ): Builder {
        $column = 'estimated_revenue';

        return $this->defaultQueryLogic($builder, $column, $firstOperator, $secondOperator, $firstInput, $secondInput, $logical);
    }
}
