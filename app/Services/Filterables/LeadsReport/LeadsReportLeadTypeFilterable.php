<?php

namespace App\Services\Filterables\LeadsReport;

use App\Models\Odin\ConsumerProduct;
use App\Services\Filterables\MultiSelectFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportLeadTypeFilterable extends MultiSelectFilterableOption
{
    const string ID = 'leads-type';

    const string SECONDARY_SERVICE = 'secondary_service';
    const string EMAIL_MARKETING = 'email_marketing';
    const string PING_POST = 'ping_post';
    const string AFFILIATE = 'affiliate';

    public function __construct()
    {
        $this->model = ConsumerProduct::class;
        $this->name = "Lead Type";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = [
            'Secondary Service' => self::SECONDARY_SERVICE,
            'Email Marketing'   => self::EMAIL_MARKETING,
            'Ping Post'         => self::PING_POST,
            'Affiliate'         => self::AFFILIATE,
        ];
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
