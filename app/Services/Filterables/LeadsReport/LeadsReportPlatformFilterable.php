<?php

namespace App\Services\Filterables\LeadsReport;

use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\Odin\Consumer;
use App\Models\Odin\Product;
use App\Services\Filterables\MultiSelectFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportPlatformFilterable extends MultiSelectFilterableOption
{
    const string ID = 'leads-platform';

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Platform";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = AdvertisingPlatform::getAsKeyValueSelectArray();
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
