<?php

namespace App\Services\Filterables\LeadsReport;

use App\Enums\Campaigns\CampaignStatus;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportCampaignStatusFilterable extends MultiSelectFilterableOption
{
    const string ID = 'leads-report-campaign-status';

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Campaign Status";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = CampaignStatus::getAsKeyValueSelectArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $this->getQuery($builder, $value)
            : $builder;
    }

    private function getQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
