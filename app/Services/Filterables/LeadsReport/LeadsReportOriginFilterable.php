<?php

namespace App\Services\Filterables\LeadsReport;

use App\Models\Odin\Consumer;
use App\Models\Odin\Website;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportOriginFilterable extends MultiSelectFilterableOption
{
    const string ID = 'consumer-origin';

    protected array $options = [];

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = "Origin";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = Website::query()
            ->whereNotNull(Website::FIELD_ABBREVIATION)
            ->where(Website::FIELD_ABBREVIATION, '!=', '')
            ->get()
            ->mapWithKeys(fn(Website $website) => [$website->name => $website->id])
            ->toArray();
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }

}
