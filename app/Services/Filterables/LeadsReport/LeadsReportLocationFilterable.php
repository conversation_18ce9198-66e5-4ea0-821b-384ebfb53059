<?php

namespace App\Services\Filterables\LeadsReport;

use App\Models\Legacy\Location;
use App\Models\Odin\Consumer;
use App\Services\Filterables\MultiSelectWithChildrenFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportLocationFilterable extends MultiSelectWithChildrenFilterableOption
{
    const string ID     = 'leads-state';
    const string NAME   = 'State';
    const string CHILD_FILTERABLE_COUNTY_ID = 'leads-county';

    protected bool $bypassParentCheckForActiveFilterDisplay = false;

    public function __construct()
    {
        $this->model = Consumer::class;
        $this->name = self::NAME;
        $this->id = self::ID;
        $this->withRelations = null;
        $this->filterPillDisplayLastChild = false;

        $this->options = Location::query()
            ->select(Location::STATE, Location::STATE_ABBREVIATION)
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get()
            ->mapWithKeys(fn(Location $state) => [$state->state => $state->state])
            ->toArray();

        $this->children = [
            self::CHILD_FILTERABLE_COUNTY_ID    => new LeadsReportLocationCountyChildFilterableOption(),
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (!$value) {
            return $builder;
        }

        $value = $value ?? [];

        $stateValues = $value[$this->getId()] ?? [];
        $countyValues = $value[self::CHILD_FILTERABLE_COUNTY_ID] ?? [];

        $updatedCountyKeys = $this->children[self::CHILD_FILTERABLE_COUNTY_ID]->updateOptions($stateValues);
        $this->validateChildInputs($countyValues, $updatedCountyKeys);

        $this->setUpdatedOptions(true);

        if ($countyValues)
            $this->children[self::CHILD_FILTERABLE_COUNTY_ID]->runQuery($builder, $countyValues);

        return $builder;
    }

    /**
     * Validate child options to handle user de-selecting parent regions
     * @param array $inputValues
     * @param array $validOptions
     * @return void
     */
    protected function validateChildInputs(array &$inputValues, array $validOptions): void
    {
        $validValues = array_values($validOptions);
        $inputValues = array_filter($inputValues, fn($value) => in_array($value, $validValues));
    }
}
