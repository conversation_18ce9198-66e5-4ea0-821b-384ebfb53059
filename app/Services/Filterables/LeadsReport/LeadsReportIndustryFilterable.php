<?php

namespace App\Services\Filterables\LeadsReport;

use App\Enums\Logical;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Services\Filterables\MultiSelectFilterableOption;
use App\Services\Filterables\MultiSelectWithLogicalFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class LeadsReportIndustryFilterable extends MultiSelectFilterableOption
{
    const string ID = 'leads-industry';

    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Industry";
        $this->id = self::ID;
        $this->withRelations = null;

        $this->options = Industry::query()
            ->join(IndustryConfiguration::TABLE, IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_INDUSTRY_ID, '=', Industry::TABLE.'.'.Industry::FIELD_ID)
            ->where(IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
            ->get([Industry::TABLE.'.'.Industry::FIELD_ID, Industry::TABLE.'.'.Industry::FIELD_NAME])
            ->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)
            ->toArray();
    }

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $builder;
    }
}
