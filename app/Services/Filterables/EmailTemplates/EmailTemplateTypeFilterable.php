<?php

namespace App\Services\Filterables\EmailTemplates;

use App\Enums\EmailTemplateType;
use App\Models\EmailTemplate;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class EmailTemplateTypeFilterable extends MultiSelectFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model = EmailTemplate::class;
        $this->name = "Template Type";
        $this->id = 'email-template-type';
        $this->withRelations = null;

        $this->options = EmailTemplateType::getAsNameKeyPair();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(EmailTemplate::FIELD_TYPE, $value)
            : $builder;
    }

}