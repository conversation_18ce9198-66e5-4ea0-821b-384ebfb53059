<?php

namespace App\Services\Filterables\EmailTemplates;

use App\Models\EmailTemplate;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class EmailTemplatePersonalFilterable extends SelectFilterableOption
{
    protected array $options = [
        'Personal Only' => true
    ];

    public function __construct()
    {
        $this->model = EmailTemplate::class;
        $this->name = "Personal Templates";
        $this->id = 'personal';
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->where(EmailTemplate::FIELD_PERSONAL, $value)
                ->where(EmailTemplate::FIELD_OWNER_USER_ID, Auth::id())
            : $builder;
    }

}