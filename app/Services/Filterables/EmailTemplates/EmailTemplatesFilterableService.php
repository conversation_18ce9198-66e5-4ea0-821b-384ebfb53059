<?php

namespace App\Services\Filterables\EmailTemplates;

use App\Models\EmailTemplate;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class EmailTemplatesFilterableService extends BaseFilterableService
{
    const string FILTERABLE_CATEGORY = 'email-templates-search';

    protected array $filters = [
        EmailTemplateIndustryFilterable::class,
        EmailTemplatePersonalFilterable::class,
        EmailTemplateTypeFilterable::class,
    ];

    protected string $baseModel = EmailTemplate::class;

    /**
     * @param array $results
     * @param Builder|null $baseQuery
     * @return Builder
     */
    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? EmailTemplate::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }

}