<?php

namespace App\Services\Filterables\EmailTemplates;

use App\Models\EmailTemplate;
use App\Models\Odin\Consumer;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\Filterables\MultiSelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;

class EmailTemplateIndustryFilterable extends MultiSelectFilterableOption
{
    protected array $options = [];

    public function __construct()
    {
        $this->model = EmailTemplate::class;
        $this->name = "Industry";
        $this->id = 'email-template-industry';
        $this->withRelations = null;

        $this->options = Industry::all()
            ->mapWithKeys(fn(Industry $industry) => [$industry->name => $industry->id])
            ->toArray();
    }

    public function runQuery(Builder $builder, mixed $value): Builder
    {
        return $value
            ? $builder->whereIn(EmailTemplate::FIELD_INDUSTRY_ID, $value)
            : $builder;
    }

}