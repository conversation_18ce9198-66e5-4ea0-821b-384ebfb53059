<?php

namespace App\Services\Filterables;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

abstract class BaseFilterableOption
{
    /**
     * Model class this operates on
     * @var string
     */
    protected string $model;

    /**
     * The display name in the Filter drop down
     * @var string
     */

    protected string $name;
    /**
     * Type to let the frontend know how to render this filter option
     * @var FilterableType
     */
    protected FilterableType $type;

    /**
     * Identifier to be used by frontend
     * @var string
     */
    protected string $id;

    /**
     * Key/value options for the frontend
     * @var array
     */
    protected array $options;

    /**
     * @var bool
     */
    protected bool $clearable = true;

    protected bool $hasUpdatedOptionsFlag = false;
    protected bool $sortOptions = true;

    /**
     * Supply model class name if builder method requires relations to be eager loaded
     * @var ?array
     */
    protected ?array $withRelations;

    public function getModel(): string
    {
        return $this->model;
    }

    public function getType(): string
    {
        return $this->type->value;
    }

    public function getName(): string
    {
        return $this->name;
    }

    /**
     * Whether this filter can be cleared/reset from the frontend
     * @return bool
     */
    public function getClearable(): bool
    {
        return $this->clearable;
    }

    //TODO: Figure out how this will work - e.g. compile the joins from each filter before running?
    public function getRelations(): ?array
    {
        return $this->withRelations;
    }

    public function getOptions(): array
    {
        return $this->options ?? [];
    }

    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @return bool Whether this filter should be shown in the frontend
     */
    protected function getShow(): bool
    {
        return true;
    }

    protected function hasUpdatedOptions(): bool
    {
        return $this->hasUpdatedOptionsFlag;
    }


    /**
     * Pass in the Query Builder and add this filter's query
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public abstract function runQuery(Builder $builder, mixed $value): Builder;

    /**
     * Fetch the possible values / types for the frontend
     * @return array
     */
    public function getFilterableDisplayData(): array
    {
        $options = collect($this->getOptions());

        return [
            'type'      => $this->type->value,
            'name'      => $this->name,
            'id'        => $this->getId(),
            'clearable' => $this->getClearable(),
            'options'   => $this->sortOptions ? $options->sortKeys()->toArray() : $options->toArray(),
            'show'      => $this->getShow(),
        ];
    }

    public function getClass(): string
    {
        return get_class($this);
    }

    /**
     *  Optional method for FilterableOptions with child components to update available options according to last query
     * @param array $filterByKeys
     * @return array|null
     */
    public function updateOptions(array $filterByKeys): ?array
    {
        return null;
    }

    /**
     * For updating dependent components in WithChildren FilterableOptions
     * @return array|null
     */
    public function getUpdatedOptions(): ?array
    {
        return $this->hasUpdatedOptions()
            ? $this->getFilterableDisplayData()
            : null;
    }

    /**
     * @param bool $value
     * @return void
     */
    public function setUpdatedOptions(bool $value): void
    {
        $this->hasUpdatedOptionsFlag = $value;
    }

    /**
     * @param  Builder  $builder
     * @param  string  $column
     * @return bool
     */
    protected function columnExistsInSql(Builder $builder, string $column): bool
    {
        return str_contains($builder->toSql(), $column);
    }

    /**
     * @param  Builder  $builder
     * @param  string  $column
     * @return void
     * @throws Exception
     */
    protected function handleColumnDoesntExist(Builder $builder, string $column): void
    {
        if ($this->columnExistsInSql($builder, $column)) {
            return;
        }

        throw new Exception("Column $column does not exist in query");
    }

    /**
     * @param Builder $builder
     * @param string $tableName
     * @return bool
     */
    protected function shouldJoin(Builder $builder, string $tableName): bool
    {
        foreach ($builder->getQuery()->joins as $join) {
            /** @var JoinClause $join */
            if ($join->table === $tableName) {
                return false;
            }
        }

        return true;
    }
}
