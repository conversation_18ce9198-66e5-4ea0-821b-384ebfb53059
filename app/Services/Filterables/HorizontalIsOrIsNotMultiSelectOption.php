<?php

namespace App\Services\Filterables;

use App\Enums\IsOrIsNot;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;

abstract class HorizontalIsOrIsNotMultiSelectOption extends BaseFilterableOption
{
    const IS_OR_IS_NOT = 'is_or_is_not';
    const SELECTED_OPTIONS = 'selected_options';
    const ACTIVE = 'active';

    protected FilterableType $type = FilterableType::HORIZONTAL_IS_OR_IS_NOT_MULTI_SELECT;

    /**
     * @param  Builder  $builder
     * @param  mixed  $value
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        $active = data_get($value, self::ACTIVE, false);

        if (!$active) {
            return $builder;
        }

        try {
            list($isOrIsNot, $selectedOptions) = $this->validateValue($value);
        } catch (Exception $e) {
            $filterName = $this->name;

            throw new Exception("Issue w/ $filterName: ".$e->getMessage());
        }

        return $value
            ? $this->getQuery($builder, $isOrIsNot, $selectedOptions)
            : $builder;
    }

    /**
     * @param  mixed  $value
     * @return array
     * @throws Exception
     */
    protected function validateValue(mixed $value): array
    {
        $validator = Validator::make($value, [
            self::IS_OR_IS_NOT => ['required', new Enum(IsOrIsNot::class)],
            self::SELECTED_OPTIONS => 'required|array|min:1',
            self::SELECTED_OPTIONS.'.*' => ['required'],
        ]);

        if ($validator->fails()) {
            throw new Exception($validator->errors()->first());
        }

        $validated = $validator->validated();

        $isOrIsNot = IsOrIsNot::tryFrom(data_get($validated, self::IS_OR_IS_NOT));
        $selectedOptions = data_get($validated, self::SELECTED_OPTIONS);

        return [
            $isOrIsNot,
            $selectedOptions,
        ];
    }

    abstract protected function getQuery(
        Builder $builder,
        IsOrIsNot $isOrIsNot,
        array $selectedOptions,
    ): Builder;
}
