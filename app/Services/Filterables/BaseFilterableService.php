<?php

namespace App\Services\Filterables;

use Illuminate\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Builder;
use App\Models\User;

abstract class BaseFilterableService
{
    const string KEY_FILTERS        = 'filters';
    const string KEY_FILTER_OPTIONS = 'filter_options';
    const string KEY_PRESETS        = 'presets';
    const string KEY_FILTER_UPDATES = 'filter_updates';

    /**
     * Array of BaseFilterOptions available in this Filter set
     * @var BaseFilterableOption[]
     */
    protected array $filters = [];

    /** @var BaseFilterableOption[] */
    private array $filterInstances = [];

    protected string $baseModel;

    public function __construct(User|Authenticatable|null $authUser = null)
    {
        foreach ($this->filters as $filter) {
            $newFilter = app($filter, [
                'authUser' => $authUser,
            ]);

            if (is_a($newFilter, BaseFilterableOption::class)) {
                $this->filterInstances[$newFilter->getId()] = $newFilter;
            }
            else {
                $name = $filter::class;
                logger()->warning("Class '$name' cannot be used as a FilterableOption.");
            }
        }
    }

    /**
     * Receive the Filter inputs from a controller, and optionally receive an existing Query Builder
     *  e.g. a Builder already narrowed by search input.
     * This method should then pass the Builder on to runFilterQueries()
     *
     * @param array $results
     * @param Builder|null $baseQuery
     * @return Builder
     */
    abstract public function runQuery(array $results, ?Builder $baseQuery): Builder;

    /**
     * For use with FilterableOption classes with dependant Filters
     * Run after ->runQuery() to gather any updated options for Filterables
     * @return ?array
     */
    public function getFilterOptionUpdates(): ?array
    {
        $output = [];
        foreach ($this->filterInstances as $filter) {
            $updates = $filter->getUpdatedOptions();
            if ($updates) $output[$filter->getId()] = $updates;
        }

        return $output ?: null;
    }

    /**
     * Returns an array of display data for the front end to use to render the filter options
     *
     * @return array
     */
    public function getDisplayData(): array
    {
        return collect($this->filterInstances)
            ->map(fn (BaseFilterableOption $filter) => $filter->getFilterableDisplayData())
            ->sortBy('name')
            ->values()
            ->toArray();
    }

    /**
     * Runs the base Builder through the valid Filters passed in as inputs
     *
     * @param Builder $query
     * @param array $inputs
     * @return Builder
     */
    protected function runFilterQueries(Builder $query, array $inputs): Builder
    {
        //TODO: table joins from model relations, or use a base Query for the Service with join already in place

        foreach ($inputs as $id => $inputValue) {
            if (key_exists($id, $this->filterInstances)) {
                $currentFilter = $this->filterInstances[$id];

                if ($currentFilter->getModel() === $this->baseModel) {
                    $currentFilter->runQuery($query, $inputValue);
                }
                else {
                    logger()->warning("Filter id '$id' does not operate on this service's model '$this->baseModel', filter ignored.");
                }
            }
        }

        return $query;
    }
}
