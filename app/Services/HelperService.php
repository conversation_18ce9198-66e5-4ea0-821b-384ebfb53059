<?php

namespace App\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class HelperService
{
    public static function removeNonNumbers(string $number): string
    {
        return preg_replace('/[^0-9]/', '', $number);
    }

    /**
     * This function strips out non-numeric characters and removes a leading 1 as US area codes cannot start with 1
     * If the number is not exactly 10 characters we return null as it is not a valid US phone
     *
     * @param string $phone
     * @return string|null
     */
    public static function formatUSPhoneNumber(string $phone): ?string
    {
        $phone = preg_replace("/[^0-9]/","", $phone); // strip out non numeric characters
        $phone = preg_replace("/^1/","", $phone); // remove 1 if first character
        return strlen($phone) === 10 ? $phone : null; // valid US formatted phone must be 10 digits
    }

    /**
     * @param string $email
     * @return bool
     */
    public function isEmailValid(string $email): bool
    {
        return !!preg_match("/^([a-z0-9\+_\-]+)(\.[a-z0-9\+_\-]+)*@([a-z0-9\-]+\.)+[a-z]{2,6}$/ix", $email);
    }

    /**
     * @param Builder $query
     * @return string
     */
    public function getQueryWithBindings(Builder $query): string
    {
        return Str::replaceArray('?', $query->getBindings(), $query->toSql());
    }

    public static function formatDateWithTimezone($date): string {
        return ($date !=null) ? $date->format('Y-m-d H:i:s')." ".strtoupper($date->timezone->getAbbreviatedName()) : '';
    }

    /**
     * This function gets the ip from a request
     *
     * @param Request $request
     * @return string
     */
    function getIpFromRequest(Request $request): string
    {
        return $request->header('X-CLIENT-IP', count($request->ips()) > 1 ? $request->ips()[1] : $request->ip());
    }
}
