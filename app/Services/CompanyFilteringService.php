<?php

namespace App\Services;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\CompanyUserRelationship;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyExternalReview;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Services\Filterables\CompanyServicingArea\CompanyServicingAreaFilterableService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;

class CompanyFilteringService
{
    const ALL_CONTACTS_COUNT = 'contacts_count';

    const CONTACTS_WITH_EMAIL_COUNT = 'contacts_with_email_count';

    const SALES_STATUS_ID = 'sales_status_id';

    const CONTACTS_WITH_PHONE_COUNT = 'contacts_with_phone_count';

    const GOOGLE_RATING = 'google_rating';

    const EMPLOYEE_COUNT = 'employee_count';

    const GOOGLE_REVIEW_COUNT = 'google_review_count';

    const ESTIMATED_REVENUE = 'estimated_revenue';

    const REVENUE_IN_THOUSANDS = 'revenue_in_thousands';

    const FILTER_BY_LEAD_PURCHASED_AMOUNT_ACTIVE = 'filterByLeadPurchasedAmountActive';

    const FILTER_BY_LEAD_PURCHASED_AMOUNT_TIMESTAMP = 'filterByLeadPurchasedAmountTimestamp';

    const FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_VALUE = 'filterByLeadPurchasedAmountFirstValue';

    const FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_VALUE = 'filterByLeadPurchasedAmountSecondValue';

    const FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_OPERATOR = 'filterByLeadPurchasedAmountFirstOperator';

    const FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_OPERATOR = 'filterByLeadPurchasedAmountSecondOperator';

    const FILTER_BY_LEAD_PURCHASED_AMOUNT_LOGICAL = 'filterByLeadPurchasedAmountLogical';

    const LEAD_COST = 'lead_cost';

    const LEAD_COUNT = 'lead_count';

    const CAN_UPDATE_SALES_DATA = 'can_update_sales_data';

    const CONTACTS_WITH_NEITHER_COUNT = 'contacts_with_neither_phone_or_email_count';

    const CONTACTS_WITH_BOTH_COUNT = 'contacts_with_both_phone_and_email_count';

    protected Request $request;

    protected QuoteCompanyRepository $quoteCompanyRepository;

    public function __construct(Request $request, protected CompanyServicingAreaFilterableService $companyServicingAreaFilterableService)
    {
        $this->request = $request;
        $this->quoteCompanyRepository = resolve(QuoteCompanyRepository::class);
    }

    /**
     * Parses the amount of purchased leads filter variables from the request.
     *
     * @return array{
     *     filterByLeadPurchasedAmountActive:bool,
     *     filterByLeadPurchasedAmountTimestamp:numeric,
     *     filterByLeadPurchasedAmountFirstValue:numeric,
     *     filterByLeadPurchasedAmountSecondValue:numeric|null,
     *     filterByLeadPurchasedAmountFirstOperator:Operator,
     *     filterByLeadPurchasedAmountSecondOperator:Operator|null,
     *     filterByLeadPurchasedAmountLogical:Logical|null,
     * }
     */
    public function getAmountOfPurchasedLeadsFilterRequestVariables(): array
    {
        $isActive = filter_var($this->request->get(self::FILTER_BY_LEAD_PURCHASED_AMOUNT_ACTIVE), FILTER_VALIDATE_BOOL) ?? false;

        $logicals = [null, Logical::AND->value, Logical::OR->value];

        $operators = [Operator::EQUAL_TO->value, Operator::GREATER_THAN->value, Operator::LESS_THAN->value, Operator::GREATER_THAN_OR_EQUAL_TO->value, Operator::LESS_THAN_OR_EQUAL_TO->value];

        if ($isActive) {
            $this->request->validate([
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_TIMESTAMP => ['integer', 'required'],
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_VALUE => ['integer', 'required'],
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_VALUE => ['integer', 'nullable'],
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_OPERATOR => [Rule::in($operators), 'required'],
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_OPERATOR => [Rule::in($operators), 'nullable'],
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_LOGICAL => [Rule::in($logicals), 'nullable'],
            ]);
        }

        $result = $this->request->toArray();

        $timestamp = $this->request->get(self::FILTER_BY_LEAD_PURCHASED_AMOUNT_TIMESTAMP);
        $firstValue = $this->request->get(self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_VALUE);
        $secondValue = $this->request->get(self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_VALUE);
        $firstOperator = $this->request->get(self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_OPERATOR);
        $secondOperator = $this->request->get(self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_OPERATOR);
        $logical = $this->request->get(self::FILTER_BY_LEAD_PURCHASED_AMOUNT_LOGICAL);

        $result[self::FILTER_BY_LEAD_PURCHASED_AMOUNT_ACTIVE] = $isActive;
        $result[self::FILTER_BY_LEAD_PURCHASED_AMOUNT_TIMESTAMP] = self::castInt($timestamp);
        $result[self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_VALUE] = self::castInt($firstValue);
        $result[self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_VALUE] = self::castInt($secondValue);
        $result[self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_OPERATOR] = Operator::tryFrom($firstOperator);
        $result[self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_OPERATOR] = Operator::tryFrom($secondOperator);
        $result[self::FILTER_BY_LEAD_PURCHASED_AMOUNT_LOGICAL] = Logical::tryFrom($logical);

        return $result;
    }

    private static function castInt($numeric): ?int
    {
        return is_numeric($numeric) ? intval($numeric) : null;
    }

    /**
     * @throws Exception
     */
    private function setupQuery(Builder $query, ?int $leadsPurchasedCountTimestamp = null, array $requestVariables = [], bool $useLegacyLeadsPurchasedFilter = true): void
    {
        $query->with([
            EloquentCompany::RELATION_MI_COMPANY,
            EloquentCompany::RELATION_MI_COMPANY.'.'.Company::RELATION_DATA,
            EloquentCompany::RELATION_MI_COMPANY.'.'.Company::RELATION_LOCATIONS,
            EloquentCompany::RELATION_MI_COMPANY.'.'.Company::RELATION_USERS,
            EloquentCompany::RELATION_MI_COMPANY.'.'.Company::RELATION_USERS.'.'.CompanyUser::RELATION_OFFICE_PHONE_CALLS,
            EloquentCompany::RELATION_MI_COMPANY.'.'.Company::RELATION_USERS.'.'.CompanyUser::RELATION_CELL_PHONE_CALLS,
            EloquentCompany::RELATION_MI_COMPANY.'.'.Company::RELATION_LOCATIONS.'.'.CompanyLocation::RELATION_EXTERNAL_REVIEWS,
            EloquentCompany::RELATION_MI_COMPANY.'.'.Company::RELATION_LOCATIONS.'.'.CompanyLocation::RELATION_ADDRESS,
        ]);

        if (gettype($leadsPurchasedCountTimestamp) == 'integer') {
            $this->quoteCompanyRepository->applyLeadCostAndLeadCount($query, $leadsPurchasedCountTimestamp);
        }

        $query->leftJoin(
            DatabaseHelperService::database().'.'.Company::TABLE.' as '.Company::TABLE,
            Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
            '=',
            EloquentCompany::TABLE.'.'.EloquentCompany::ID
        );

        $query->leftJoin(
            DatabaseHelperService::database().'.'.CompanyUser::TABLE.' as '.CompanyUser::TABLE,
            CompanyUser::TABLE.'.'.CompanyUser::FIELD_COMPANY_ID,
            '=',
            Company::TABLE.'.'.Company::FIELD_ID
        );

        $query->leftJoin(DatabaseHelperService::database().'.'.SuccessManagerClient::TABLE.' as '.SuccessManagerClient::TABLE,
            SuccessManagerClient::TABLE.'.'.SuccessManagerClient::FIELD_COMPANY_REFERENCE, '=',
            Company::TABLE.'.'.Company::FIELD_REFERENCE);

        $query->leftJoin(DatabaseHelperService::database().'.'.SuccessManager::TABLE.' as '.SuccessManager::TABLE,
            SuccessManager::TABLE.'.'.SuccessManager::FIELD_ID, '=',
            SuccessManagerClient::TABLE.'.'.SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID);

        $query->leftJoin(DatabaseHelperService::database().'.'.CompanyData::TABLE.' as '.CompanyData::TABLE,
            CompanyData::TABLE.'.'.CompanyData::FIELD_COMPANY_ID, '=',
            Company::TABLE.'.'.Company::FIELD_ID);

        $companyUserEmailExistsSql = 'COUNT(DISTINCT IF('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_EMAIL.' IS NOT NULL AND '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_EMAIL.'<> "", '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID.', NULL)) as '.self::CONTACTS_WITH_EMAIL_COUNT;

        $companyUserPhoneExistsSql = 'COUNT(DISTINCT IF(('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_CELL_PHONE.' IS NOT NULL AND '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_CELL_PHONE.'<> "") OR ('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_OFFICE_PHONE.' IS NOT NULL AND '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_OFFICE_PHONE.'<> ""), '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID.', NULL)) as '.self::CONTACTS_WITH_PHONE_COUNT;

        $companyUserNeitherPhoneOrEmailExistsSql = 'COUNT(DISTINCT IF(('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_CELL_PHONE.' IS NULL OR '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_CELL_PHONE.'= "") AND ('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_OFFICE_PHONE.' IS NULL OR '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_OFFICE_PHONE.'= "") AND ('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_EMAIL.' IS NULL OR '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_EMAIL.'= ""), '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID.', NULL)) as '.self::CONTACTS_WITH_NEITHER_COUNT;

        $companyUserBothPhoneAndEmailExistsSql = 'COUNT(DISTINCT IF(('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_CELL_PHONE.' IS NOT NULL AND '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_CELL_PHONE.'<> "") AND ('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_OFFICE_PHONE.' IS NOT NULL AND '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_OFFICE_PHONE.'<> "") AND ('.CompanyUser::TABLE.'.'.CompanyUser::FIELD_EMAIL.' IS NOT NULL AND '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_EMAIL.'<> ""), '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID.', NULL)) as '.self::CONTACTS_WITH_BOTH_COUNT;

        $select = [
            EloquentCompany::TABLE.'.*',
            DB::raw('COUNT(DISTINCT '.CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID.') as '.self::ALL_CONTACTS_COUNT),
            DB::raw($companyUserEmailExistsSql),
            DB::raw($companyUserPhoneExistsSql),
            DB::raw($companyUserNeitherPhoneOrEmailExistsSql),
            DB::raw($companyUserBothPhoneAndEmailExistsSql),
        ];

        $query->addSelect($select);

        $estimatedRevenueSelectSql = 'IF('.CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD.' IS NOT NULL AND '.CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD.'<> "", JSON_EXTRACT('.CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD.', "$.revenue_in_thousands"), NULL) / 1000 as '.self::ESTIMATED_REVENUE;

        $query->selectRaw($estimatedRevenueSelectSql);

        $googleRatingSelectSql = 'IF('.CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD.' IS NOT NULL AND '.CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD.'<> "", JSON_EXTRACT('.CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD.', "$.google_rating"), NULL) as '.self::GOOGLE_RATING;

        $query->selectRaw($googleRatingSelectSql);

        $legacyCompanyId = EloquentCompany::TABLE.'.'.EloquentCompany::ID;

        $query->groupBy($legacyCompanyId, DatabaseHelperService::database().'.company_data.payload');

        $filterByLeadPurchasedAmount = static function (array $requestVariables, Builder $query) {
            $active = data_get($requestVariables,
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_ACTIVE);
            $firstValue = data_get($requestVariables,
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_VALUE);
            $firstOperator = data_get($requestVariables,
                self::FILTER_BY_LEAD_PURCHASED_AMOUNT_FIRST_OPERATOR);

            if ($active && gettype($firstValue) === 'integer' && $firstOperator instanceof Operator) {
                $query->leftJoin(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.' as '.EloquentQuoteCompany::TABLE,
                    function (JoinClause $joinClause) {
                        $joinClause->on(
                            EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID,
                            '=',
                            EloquentCompany::TABLE.'.'.EloquentCompany::ID
                        );

                        $timestamp = config('models.EloquentQuoteCompany.range_by_delivered_timestamp_in_days');

                        $joinClause->where(
                            EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                            '>=',
                            now()->subDays($timestamp)
                        );
                    }
                );

                $query->addSelect([
                    DB::raw('SUM('.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COST.') as '.self::LEAD_COST),
                ]);

                try {
                    $sqlFirstOperator = Operator::sqlOperator($firstOperator);
                } catch (Exception) {
                    return;
                }

                $query->having(self::LEAD_COST,
                    $sqlFirstOperator,
                    $firstValue
                );

                $timestamp = data_get($requestVariables, self::FILTER_BY_LEAD_PURCHASED_AMOUNT_TIMESTAMP);

                $query->whereBetween(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, [
                    $timestamp,
                    now()->timestamp,
                ]);

                $logical = data_get($requestVariables, self::FILTER_BY_LEAD_PURCHASED_AMOUNT_LOGICAL);
                $secondOperator = data_get($requestVariables, self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_OPERATOR);
                $secondValue = data_get($requestVariables, self::FILTER_BY_LEAD_PURCHASED_AMOUNT_SECOND_VALUE);

                if ($logical instanceof Logical && $secondOperator instanceof Operator && gettype($secondValue) === 'integer') {
                    try {
                        $sqlSecondOperator = Operator::sqlOperator($secondOperator);
                    } catch (Exception) {
                        return;
                    }

                    if ($logical === Logical::AND) {
                        $query->having(self::LEAD_COST,
                            $sqlSecondOperator,
                            $secondValue
                        );
                    } else {
                        $query->orHaving(self::LEAD_COST,
                            $sqlSecondOperator,
                            $secondValue
                        );
                    }
                }
            }
        };

        if ($useLegacyLeadsPurchasedFilter) {
            $filterByLeadPurchasedAmount($requestVariables, $query);
        }
    }

    /**
     * Appends the necessary attributes for each company in the collection.
     */
    public function appendAttributes(Builder $query, $requestVariables, ?int $leadsPurchasedCountTimestamp = null, ?Request $request = null, ?int $pagination = null): Collection|LengthAwarePaginator
    {
        $companyUserRelationships = CompanyUserRelationship::with('company')
            ->join('companies', 'companies.id', '=', 'company_user_relationships.company_id')
            ->where('company_user_relationships.user_id', auth()->id())
            ->select(['companies.legacy_id', 'company_user_relationships.*'])
            ->get();

        $accountManagerRoleId = Role::findByName('account-manager')->id;

        $customerSuccessManagerRoleId = Role::findByName('customer-success-manager')->id;

        $filters = $request?->get('filters');

        if (gettype($filters) === 'string') {
            $filters = json_decode($filters, true);
        }

        try {
            $this->setupQuery($query, $leadsPurchasedCountTimestamp, $requestVariables, (bool) $filters);
        } catch (Exception) {
            return collect();
        }

        if ($filters) {
            $this->companyServicingAreaFilterableService->runQuery($filters, $query);
        }

        $transform = function (EloquentCompany $legacyCompany) use ($companyUserRelationships, $accountManagerRoleId, $customerSuccessManagerRoleId) {
            if (! is_numeric($legacyCompany[self::LEAD_COST])) {
                $legacyCompany[self::LEAD_COST] = 0;
            }

            if (! is_numeric($legacyCompany[self::LEAD_COUNT])) {
                $legacyCompany[self::LEAD_COUNT] = 0;
            }

            $companyDataPayload = $legacyCompany->miCompany?->data?->payload;

            $checkArray = is_array($companyDataPayload) ? $companyDataPayload : null;

            $companyDataPayload = is_string($companyDataPayload) ?
                json_decode($companyDataPayload, true, 512, JSON_THROW_ON_ERROR) : $checkArray;

            $revenueInThousands = $this->standardizeRevenueInThousands($companyDataPayload);

            $estimatedRevenue = $this->standardizeEstimatedRevenue($revenueInThousands);

            $googleReviewsCount = $this->standardizeGoogleReviewCount($legacyCompany);

            $employeeCount = $this->standardizeEmployeeCount($companyDataPayload);

            $googleRating = $this->standardizeGoogleRating($legacyCompany);

            $legacyCompany[self::REVENUE_IN_THOUSANDS] = data_get($legacyCompany, self::REVENUE_IN_THOUSANDS) ?? $revenueInThousands;
            $legacyCompany[self::ESTIMATED_REVENUE] = data_get($legacyCompany, self::ESTIMATED_REVENUE) ?? $estimatedRevenue;
            $legacyCompany[self::GOOGLE_REVIEW_COUNT] = data_get($legacyCompany, self::GOOGLE_REVIEW_COUNT) ?? $googleReviewsCount;
            $legacyCompany[self::EMPLOYEE_COUNT] = data_get($legacyCompany, self::EMPLOYEE_COUNT) ?? $employeeCount;
            $legacyCompany[self::GOOGLE_RATING] = data_get($legacyCompany, self::GOOGLE_RATING) ?? $googleRating;
            $legacyCompany[Company::FIELD_STATUS] = CompanyConsolidatedStatus::label($legacyCompany->miCompany?->consolidated_status);
            $legacyCompany[Company::FIELD_SALES_STATUS] = CompanySalesStatus::label($legacyCompany->miCompany?->sales_status);
            $legacyCompany[self::SALES_STATUS_ID] = $legacyCompany->miCompany?->sales_status ?? 0;
            $legacyCompany[self::ALL_CONTACTS_COUNT] = data_get($legacyCompany, self::ALL_CONTACTS_COUNT);
            $legacyCompany[self::CONTACTS_WITH_EMAIL_COUNT] = data_get($legacyCompany, self::CONTACTS_WITH_EMAIL_COUNT);
            $legacyCompany[self::CONTACTS_WITH_PHONE_COUNT] = data_get($legacyCompany, self::CONTACTS_WITH_PHONE_COUNT);
            $legacyCompany[self::CAN_UPDATE_SALES_DATA] = $this->canUpdateSalesData($legacyCompany, $companyUserRelationships, $accountManagerRoleId, $customerSuccessManagerRoleId);

            return $legacyCompany;
        };

        if ($pagination) {
            return $query->paginate($pagination)->through($transform);
        } else {
            $collection = $query->get();

            return $collection->map($transform);
        }
    }

    private function canUpdateSalesData(EloquentCompany $legacyCompany, Collection $companyUserRelationships, int $accountManagerRoleId, int $customerSuccessManagerRoleId): bool
    {
        return (bool) $companyUserRelationships->filter(fn (CompanyUserRelationship $companyUserRelationship) => $companyUserRelationship->company->legacy_id === $legacyCompany->companyid && ($companyUserRelationship->role_id === $accountManagerRoleId || $companyUserRelationship->role_id === $customerSuccessManagerRoleId))->first();
    }

    /**
     * Returns the estimated revenue (in thousands) for a company.
     */
    private function standardizeRevenueInThousands($companyDataPayload): ?float
    {
        if (is_array($companyDataPayload)) {
            $revenueInThousands = array_key_exists(self::REVENUE_IN_THOUSANDS, $companyDataPayload) ? $companyDataPayload[self::REVENUE_IN_THOUSANDS] : null;
        } else {
            $revenueInThousands = null;
        }

        return $revenueInThousands;
    }

    /**
     * Returns the estimated revenue for a company.
     */
    private function standardizeEstimatedRevenue(?float $revenueInThousands): ?float
    {
        if ($revenueInThousands) {
            $estimatedRevenue = $revenueInThousands * 1000;
        } else {
            $estimatedRevenue = null;
        }

        return $estimatedRevenue;
    }

    /**
     * Returns the total aggregated google review count for a company.
     */
    private function standardizeGoogleReviewCount(EloquentCompany $company): ?int
    {
        $result = 0;

        $locations = $company->miCompany?->locations;

        if (is_iterable($locations) && count($locations) > 0) {
            foreach ($locations as $location) {
                if (is_null($location?->externalReviews->count())) {
                    $result = null;
                } else {
                    $result += $location->externalReviews->sum(CompanyExternalReview::FIELD_AGG_COUNT);
                }
            }
        } else {
            $result = null;
        }

        return $result;
    }

    /**
     * Returns the total number of employees for a company.
     */
    private function standardizeEmployeeCount($companyDataPayload): ?int
    {
        return
            is_array($companyDataPayload) && array_key_exists(self::EMPLOYEE_COUNT, $companyDataPayload)
                ? $companyDataPayload[self::EMPLOYEE_COUNT]
                : null;
    }

    /**
     * Returns the aggregated average google rating for all locations of a company.
     */
    private function standardizeGoogleRating(EloquentCompany $company): ?float
    {
        $result = null;

        $locations = $company->miCompany?->locations;

        if (is_iterable($locations) && count($locations) > 0) {
            $total = 0;
            $count = 0;

            foreach ($locations as $location) {
                $externalReviews = $location?->externalReviews;

                $reviewAggValueTimesAggCount = 0;

                $reviewAggCount = 0;

                if (is_iterable($externalReviews)) {
                    foreach ($externalReviews as $externalReview) {
                        $aggCount = $externalReview?->agg_count;
                        $aggValue = $externalReview?->agg_value;

                        if (is_numeric($aggCount) && is_numeric($aggValue)) {
                            $reviewAggValueTimesAggCount += $aggCount * $aggValue;
                        }

                        if (is_numeric($aggCount)) {
                            $reviewAggCount += $aggCount;
                        }
                    }
                }

                $total += $reviewAggValueTimesAggCount;
                $count += $reviewAggCount;
            }

            if ($count > 0 && $total > 0) {
                $result = round($total / $count, 2);
            }
        }

        return $result;
    }
}
