<?php

namespace App\Services;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use App\Models\Odin\CompanyReview;

class CompanyReviewsService
{
    /**
     * @param bool $paginate
     * @param int $perPage
     * @return LengthAwarePaginator|Collection
     */
    public function all(bool $paginate = false, int $perPage = 10): LengthAwarePaginator|Collection
    {
        $query = CompanyReview::query()
                ->with([
                    CompanyReview::RELATION_COMPANY,
                    CompanyReview::RELATION_DATA,
                    CompanyReview::RELATION_CONSUMER,
                    CompanyReview::RELATION_RESPONSES
                ]);

        if($paginate
        && $perPage > 0) {
            return $query->paginate($perPage);
        }

        return $query->get();
    }

    /**
     * @param int $id
     * @return CompanyReview
     */
    public function getById(int $id): CompanyReview
    {
        return CompanyReview::findOrFail($id);
    }

    /**
     * Handles updating a review, by it's id.
     *
     * @param int $id
     * @param bool|null $emailValidated
     * @param bool|null $phoneValidated
     * @param int|null $status
     * @return bool
     */
    public function update(
        int $id,
        ?bool $emailValidated,
        ?bool $phoneValidated,
        ?int $status
    ): bool
    {
        $review = $this->getById($id);

        if($status !== null)
            $review->{CompanyReview::FIELD_STATUS} = $status;

        if($emailValidated !== null)
            $review->{CompanyReview::FIELD_EMAIL_VALIDATED} = $emailValidated;

        if($phoneValidated !== null)
            $review->{CompanyReview::FIELD_PHONE_VALIDATED} = $phoneValidated;

        return $review->save();
    }
}
