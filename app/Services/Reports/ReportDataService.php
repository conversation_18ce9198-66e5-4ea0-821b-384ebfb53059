<?php

namespace App\Services\Reports;

use App\Contracts\Services\Reports\ReportDataServiceInterface;

class ReportDataService
{

    protected array $dailyReportServices = [
        CountyCoverageReportService::class,
    ];

    public function __construct() {}

    /**
     * @return void
     */
    public function calculateDailyReportData(): void
    {
        foreach ($this->dailyReportServices as $service) {
            $newService = app($service);

            if (is_a($newService, ReportDataServiceInterface::class)) {
                $newService->calculateReportData();
            }
            else {
                $name = $newService::class;
                logger()->warning("Class '$name' cannot be used as a daily report service.");
            }
        }
    }
}
