<?php

namespace App\Services\Reports;

use App\Models\RevenueStream;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\Reports\RevenueStreamsReport\RevenueStreamsAdvertiserFilterable;
use App\Services\Filterables\Reports\RevenueStreamsReport\RevenueStreamsFilterable;
use App\Services\Filterables\Reports\RevenueStreamsReport\RevenueStreamsIndustryFilterable;
use App\Services\Filterables\Reports\RevenueStreamsReport\RevenueStreamsPlatformFilterable;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Revolt\EventLoop\Driver\EvDriver;

class RevenueStreamsReportService
{
    /**
     * @return Collection
     */
    public function getRevenueStreams(): Collection
    {
        return RevenueStream::query()
            ->select([
                RevenueStream::TABLE.'.'.RevenueStream::FIELD_ID,
                RevenueStream::TABLE.'.'.RevenueStream::FIELD_SLUG,
                RevenueStream::TABLE.'.'.RevenueStream::FIELD_ADVERTISER,
                RevenueStream::TABLE.'.'.RevenueStream::FIELD_PLATFORM,
                RevenueStream::TABLE.'.'.RevenueStream::FIELD_INDUSTRY_ID,
            ])
            ->get();
    }

    /**
     * @param array|null $dateRange
     * @param array|null $filters
     * @return Collection
     */
    public function getRevenueStreamReportData(
        ?array $dateRange = [],
        mixed $filters = null,
    ): Collection
    {
        [$dateFrom, $dateTo] = $dateRange;
        $startCarbon = Carbon::parse($dateFrom);
        $endCarbon = Carbon::parse($dateTo);
        $startDay = $startCarbon->format('Y-m-d');
        $endDay = $endCarbon->format('Y-m-d');
        $start = $startCarbon->format('Y-m-d')." 07:00:00";
        $end = $endCarbon->format('Y-m-d')." 07:00:00";

        // Filters
        $revenueStreamFilterIds = $filters?->{RevenueStreamsFilterable::ID} ?? null;
        $platformFilterSlugs = $filters?->{RevenueStreamsPlatformFilterable::ID} ?? null;
        $advertiserFilterValues = $filters?->{RevenueStreamsAdvertiserFilterable::ID} ?? null;
        $industryFilterIds = $filters?->{RevenueStreamsIndustryFilterable::ID} ?? null;

        $revenueStreams = RevenueStream::query()
            ->orderBy(RevenueStream::FIELD_PRIORITY, 'DESC')
            ->get();

        $baseDateQuery = "
            WITH RECURSIVE date_range (dt) AS (
                SELECT DATE('$startDay')
                UNION ALL
                SELECT dt + INTERVAL 1 DAY
                FROM date_range
                WHERE dt + INTERVAL 1 DAY <= '$endDay'
            )
            SELECT dt FROM date_range
        ";

        $baseQuery = DB::table(DB::raw("($baseDateQuery) AS dates"))
            ->crossJoin(RevenueStream::TABLE)
            ->select([
                'dates.dt as day',
                RevenueStream::FIELD_SLUG.' as stream',
            ]);

        if ($revenueStreamFilterIds)
            $baseQuery->whereIn(RevenueStream::TABLE.'.'.RevenueStream::FIELD_ID, $revenueStreamFilterIds);

        if ($platformFilterSlugs)
            $baseQuery->whereIn(RevenueStream::TABLE.'.'.RevenueStream::FIELD_PLATFORM, $platformFilterSlugs);

        if ($advertiserFilterValues)
            $baseQuery->whereIn(RevenueStream::TABLE.'.'.RevenueStream::FIELD_ADVERTISER, $advertiserFilterValues);

        if ($industryFilterIds)
            $baseQuery->whereIn(RevenueStream::TABLE.'.'.RevenueStream::FIELD_INDUSTRY_ID, $industryFilterIds);


        // Subquery: sold
        $soldSub = DB::table('consumer_products as cp')
            ->leftJoin('product_assignments as pa', 'pa.consumer_product_id', '=', 'cp.id')
            ->leftJoin('product_rejections as pr', 'pr.product_assignment_id', '=', 'pa.id')
            ->where('cp.created_at', '>=', $start)
            ->where(DB::raw('DATE_SUB(cp.created_at, INTERVAL 1 DAY)'), '<', $end)
            ->whereNull('pr.id')
            ->where('pa.chargeable', true)
            ->where('pa.delivered', true)
            ->groupBy('cp.id')
            ->select([
                'cp.id as cp_id',
                DB::raw('SUM(pa.cost) as rev')
            ]);

        $cpSelectString = "CASE";
        foreach ($revenueStreams as $revStream) {
            if ($revStream->{RevenueStream::FIELD_CP_CASE})
                $cpSelectString = $cpSelectString." WHEN ".$revStream->{RevenueStream::FIELD_CP_CASE}." THEN '".$revStream->{RevenueStream::FIELD_SLUG}."'";
        }
        $cpSelectString = $cpSelectString." ELSE 'unknown' END as stream";

        // Subquery: streams
        $streamsSub = DB::table('consumer_products as cp')
            ->join('consumers as c', 'c.id', '=', 'cp.consumer_id')
            ->join('service_products as sp', 'sp.id', '=', 'cp.service_product_id')
            ->join('industry_services as is2', 'is2.id', '=', 'sp.industry_service_id')
            ->join('industries as i', 'i.id', '=', 'is2.industry_id')
            ->leftJoin('consumer_product_tracking as cpt', 'cpt.id', '=', 'cp.consumer_product_tracking_id')
            ->leftJoin('consumer_product_affiliate_records as cpar', 'cpar.id', '=', 'cp.consumer_product_affiliate_record_id')
            ->leftJoin('ping_post_affiliate_leads as ppal', 'ppal.consumer_product_id', '=', 'cp.id')
            ->leftJoin('marketing_campaign_consumers as mcc', 'mcc.cloned_consumer_product_id', '=', 'cp.id')
            ->leftJoin('websites as w', 'w.id', '=', 'c.website_id')
            ->where('cp.created_at', '>=', $start)
            ->where(DB::raw('DATE_SUB(cp.created_at, INTERVAL 1 DAY)'), '<', $end)
            ->select([
                'cp.id as cp_id',
                DB::raw($cpSelectString),
            ]);

        // Subquery: cp_data
        $cpDataSub = DB::table('consumer_products as cp')
            ->joinSub($streamsSub, 'streams', function ($join) {
                $join->on('streams.cp_id', '=', 'cp.id');
            })
            ->leftJoinSub($soldSub, 'sold', function ($join) {
                $join->on('sold.cp_id', '=', 'cp.id');
            })
            ->groupBy(['streams.stream', 'day'])
            ->select([
                'streams.stream',
                DB::raw('DATE(DATE_SUB(cp.created_at, INTERVAL 7 HOUR)) as day'),
                DB::raw('COUNT(DISTINCT cp.id) as total_leads'),
                DB::raw('COUNT(DISTINCT CASE WHEN cp.good_to_sell = true THEN cp.id END) as gts_leads'),
                DB::raw('COUNT(DISTINCT CASE WHEN sold.rev > 0 THEN cp.id END) as sold_leads'),
                DB::raw('COALESCE(SUM(sold.rev), 0) as revenue'),
                DB::raw('MAX(cp.id) as example_cp_id'),
            ]);

        $costSelectString = "CASE";
        foreach ($revenueStreams as $revStream) {
            if ($revStream->{RevenueStream::FIELD_DAC_CASE})
                $costSelectString = $costSelectString." WHEN ".$revStream->{RevenueStream::FIELD_DAC_CASE}." THEN '".$revStream->{RevenueStream::FIELD_SLUG}."'";
        }
        $costSelectString = $costSelectString." ELSE 'unknown' END as stream";

        // Subquery: costs
        $costsSub = DB::table('daily_ad_costs as dac')
            ->join(DatabaseHelperService::readOnlyDatabase().'.locations as l', 'l.id', '=', 'dac.location_id')
            ->join('industries as i', 'i.id', '=', 'dac.industry_id')
            ->where('l.type', 'co')
            ->where('dac.date', '>=', DB::raw("DATE('$start')"))
            ->where('dac.date', '<=', DB::raw("DATE('$end')"))
            ->groupBy(['stream','day'])
            ->select([
                DB::raw($costSelectString),
                DB::raw('dac.date as day'),
                DB::raw('SUM(dac.cost) as cost'),
                DB::raw('GROUP_CONCAT(DISTINCT dac.advertiser) as advertiser'),
                DB::raw('GROUP_CONCAT(DISTINCT dac.industry_id) as industry')
            ]);

        // Final query: Join cp_data with costs
        $finalQuery = DB::query()
            ->fromSub($baseQuery, 'base')
            ->leftJoinSub($cpDataSub, 'cp_data', function ($join) {
                $join->on('cp_data.stream', '=', 'base.stream')
                    ->on('cp_data.day', '=', 'base.day');
            })
            ->leftJoinSub($costsSub, 'costs', function ($join) {
                $join->on('costs.stream', '=', 'base.stream')
                    ->on('costs.day', '=', 'base.day');
            })
            ->addSelect('base.day')
            ->addSelect('base.stream')
            ->addSelect(DB::raw('COALESCE(cp_data.total_leads, 0) as total_leads'))
            ->addSelect(DB::raw('COALESCE(cp_data.gts_leads, 0) as gts_leads'))
            ->addSelect(DB::raw('COALESCE(cp_data.sold_leads, 0) as sold_leads'))
            ->addSelect(DB::raw('COALESCE(cp_data.revenue, 0) as revenue'))
            ->addSelect(DB::raw('COALESCE(costs.cost, 0) as cost'))
            ->addSelect(DB::raw('COALESCE(cp_data.revenue, 0) - COALESCE(costs.cost, 0) as gross_profit'))
            ->addSelect(DB::raw('CASE WHEN costs.cost > 0 THEN cp_data.revenue / costs.cost END as roas'))
            ->addSelect('cp_data.example_cp_id')
            ->orderBy('base.stream');

        $data = $finalQuery->get();

        // Combine data
        $combined = $data->map(function ($stream) {
            return [
                'stream' => $stream->stream,
                'day' => $stream->day,
                'total_leads' => $stream->total_leads,
                'gts_leads' => $stream->gts_leads,
                'sold_leads' => $stream->sold_leads,
                'revenue' => $stream->revenue,
                'cost' => round($stream->cost, 2),
                'gross_profit' => round($stream->gross_profit,2),
                'roas' => round($stream->roas, 4),
                'example_cp_id' => $stream->example_cp_id,
            ];
        });

        return $combined->values();
    }
}
