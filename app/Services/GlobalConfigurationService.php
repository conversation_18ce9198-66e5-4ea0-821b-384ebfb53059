<?php

namespace App\Services;

use App\Enums\GlobalConfigurationKey;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;

class GlobalConfigurationService
{
    public function __construct(protected GlobalConfigurationRepository $repository)
    {

    }

    /**
     * @param GlobalConfigurationKey $key
     * @return array
     */
    public function getConfigData(GlobalConfigurationKey $key): array
    {
        return json_decode($this->repository->getConfigurationPayload(
            $key
        )?->data ?? '', true) ?? [];
    }

    /**
     * @param $configuration
     * @return array
     */
    public static function prepareConfigurationResponse($configuration): array {

        return [
            'id' => $configuration->id,
            'configuration_key' => $configuration->configuration_key,
            'configuration_payload' => $configuration->configuration_payload,
            'updated_by' => $configuration->updatedBy->name ?? '',
            'created_at' => HelperService::formatDateWithTimezone($configuration->created_at),
            'updated_at' => HelperService::formatDateWithTimezone($configuration->updated_at),
            'deleted_at' => HelperService::formatDateWithTimezone($configuration->deleted_at)
        ];
    }
}
