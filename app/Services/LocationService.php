<?php

namespace App\Services;

use App\Models\Legacy\Location;
use App\Repositories\LocationRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class LocationService
{
    public function __construct(
        protected LocationRepository $locationRepository
    )
    {

    }

    /**
     * @param Collection $countyData
     * @return array
     */
    protected function groupCountiesByState(Collection $countyData): array
    {
        $grouped = [];

        foreach ($countyData as $item) {
            $key = $item->{Location::STATE_ABBREVIATION};

            if (!isset($grouped[$key])) {
                $grouped[$key] = [
                    'name'     => $item->{Location::STATE},
                    'abbrev'   => $item->{Location::STATE_ABBREVIATION},
                    'counties' => []
                ];
            }

            $grouped[$key]['counties'][] = [
                'name' => $item->{Location::COUNTY},
                'key'  => $item->{Location::COUNTY_KEY}
            ];
        }

        return collect($grouped)->map(function ($state) {
            $state['counties'] = collect($state['counties'])
                ->sortBy('name')
                ->values()
                ->toArray();

            return $state;
        })->values()->toArray();
    }

    /**
     * @return array
     */
    public function getStateWithCounties(): array
    {
        $countyData = $this->locationRepository->getCounties();

        return $this->groupCountiesByState($countyData);
    }
}
