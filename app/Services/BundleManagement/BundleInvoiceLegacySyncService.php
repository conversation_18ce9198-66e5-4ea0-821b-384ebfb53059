<?php

namespace App\Services\BundleManagement;

use App\Services\Legacy\APIConsumer;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Exception;
use Illuminate\Support\Facades\Http;

class BundleInvoiceLegacySyncService extends APIConsumer
{

    /**
     * @return PendingRequest
     * @throws Exception
     */
    private function getBaseHttpClient(): PendingRequest
    {
        $token = $this->authenticate();
        return Http::withToken($token);
    }

    /**
     * @param string $route
     * @param array|null $data
     * @param int|null $timeout
     * @param array|null $headers
     * @return Response
     * @throws Exception
     */
    public function post(string $route, ?array $data = null, ?int $timeout = null, ?array $headers = []): Response
    {
        $baseClient = $this->getBaseHttpClient()->withHeaders($headers);
        return $baseClient->post($this->baseURL . trim(trim($route), '/'), $data ?? []);
    }

}
