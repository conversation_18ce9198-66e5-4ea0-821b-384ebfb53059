<?php

namespace App\Services\BundleManagement\v2;

use App\Aggregates\CreditAggregateRoot;
use App\DTO\Billing\InvoiceItemDTO;
use App\DTO\Billing\InvoicePayload;
use App\Enums\Billing\CreditType;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceItemTypes;
use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\PaymentMethodServices;
use App\Enums\BundleInvoiceStatus;
use App\Enums\CommonTag;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\Billing\BillingProfileRepository;
use App\Repositories\BundleManagement\BundleInvoiceHistoryRepository;
use App\Services\Billing\BillingProfile\BillingProfileService;
use App\Services\Billing\CreditService;
use App\Services\Billing\InvoiceService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class BundleInvoiceService
{

    public function __construct(
        protected BundleInvoiceHistoryRepository $bundleInvoiceHistoryRepository,
        protected InvoiceService $invoiceService,
        protected CreditService $creditService,
        protected BillingProfileRepository $billingProfileRepository,
        protected BillingProfileService $billingProfileService
    )
    {

    }

    /**
     * @param InvoiceStates $status
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return true
     * @throws Exception
     */
    public function handleInvoiceTransitionRequest(InvoiceStates $status, BundleInvoice $bundleInvoice, ?User $user = null): true
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::query()
            ->where(Invoice::FIELD_ID, $bundleInvoice->{BundleInvoice::FIELD_PAYABLE_INVOICE_ID})
            ->firstOrFail();

        $this->invoiceService->requestInvoiceStatusUpdate(
            invoice  : $invoice,
            newStatus: $status->value,
            author   : $user ? InvoiceEventAuthorTypes::USER : InvoiceEventAuthorTypes::SYSTEM,
            authorId : $user?->id,
        );

        return true;
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return bool
     */
    public function handleApproveCredits(BundleInvoice $bundleInvoice, ?User $user = null): bool
    {
        $authorType = $user ? InvoiceEventAuthorTypes::USER : InvoiceEventAuthorTypes::SYSTEM;
        $authorId = $user?->{User::FIELD_ID};

        $this->applyCreditsToCompany(
            bundleInvoice: $bundleInvoice,
            authorType   : $authorType,
            authorId     : $authorId,
        );

        $bundleInvoice->update([
            BundleInvoice::FIELD_STATUS      => BundleInvoiceStatus::COMPLETE,
            BundleInvoice::FIELD_APPROVED_AT => now(),
            BundleInvoice::FIELD_APPROVED_BY => $user?->{User::FIELD_ID},
        ]);

        return false;
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return bool
     */
    public function handleDenyCredits(BundleInvoice $bundleInvoice, ?User $user = null): bool
    {
        $bundleInvoice->update([
            BundleInvoice::FIELD_STATUS    => BundleInvoiceStatus::COMPLETE,
            BundleInvoice::FIELD_DENIED_AT => now(),
            BundleInvoice::FIELD_DENIED_BY => $user?->{User::FIELD_ID},
        ]);

        return false;
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param string $transition
     * @param User|null $user
     * @param array $options
     * @return bool
     * @throws Exception
     */
    public function requestInvoiceTransition(
        BundleInvoice $bundleInvoice,
        string $transition,
        ?User $user = null,
        array $options = []
    ): bool
    {
        $isStatusDependant = match ($transition) {
            BundleInvoice::TRANSITION_ISSUE   => $this->createPayableLegacyInvoice($bundleInvoice, $user),
            BundleInvoice::TRANSITION_PAID    => $this->handleInvoiceTransitionRequest(InvoiceStates::PAID, $bundleInvoice, $user),
            BundleInvoice::TRANSITION_APPROVE => $this->handleApproveCredits($bundleInvoice, $user),
            BundleInvoice::TRANSITION_DENY    => $this->handleDenyCredits($bundleInvoice, $user),
            BundleInvoice::TRANSITION_CANCEL  => $this->handleInvoiceTransitionRequest(InvoiceStates::VOIDED, $bundleInvoice, $user),
            BundleInvoice::TRANSITION_FAIL    => $this->handleInvoiceTransitionRequest(InvoiceStates::FAILED, $bundleInvoice, $user, $options),
        };

        if ($isStatusDependant) {
            $bundleInvoice->update([
                BundleInvoice::FIELD_STATUS => BundleInvoiceStatus::PENDING->value
            ]);
        }

        return true;
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return bool
     * @throws Exception
     */
    public function createPayableLegacyInvoice(BundleInvoice $bundleInvoice, ?User $user = null): bool
    {
        $atomicPrice = $bundleInvoice->{BundleInvoice::FIELD_COST} * 100;

        $invoiceItem = new InvoiceItemDTO(
            billable_id  : $bundleInvoice->{BundleInvoice::FIELD_ID},
            billable_type: InvoiceItemTypes::BUNDLE->value, // Change it to receive the enum instead of string to make less confusion
            unit_price   : $atomicPrice,
            quantity     : 1,
            description  : $bundleInvoice->{BundleInvoice::RELATION_BUNDLE}->{Bundle::FIELD_TITLE} ?? '',
        );

        $billingProfile = $this->billingProfileRepository->getBillingProfilesQuery(
            companyId    : $bundleInvoice->{BundleInvoice::FIELD_COMPANY_ID},
            paymentMethod: PaymentMethodServices::STRIPE->value
        )
            ->first();

        if (!$billingProfile) {
            $billingProfile = $this->billingProfileService->createProfileWithDefaultConfiguration(
                paymentMethod: PaymentMethodServices::STRIPE,
                companyId    : $bundleInvoice->{BundleInvoice::FIELD_COMPANY_ID},
            );
        }

        $invoicePayload = new InvoicePayload(
            companyId       : $bundleInvoice->{BundleInvoice::RELATION_COMPANY}->{Company::FIELD_ID},
            dueDate         : now()->addDays($billingProfile->{BillingProfile::FIELD_DUE_IN_DAYS}),
            note            : $bundleInvoice->{BundleInvoice::FIELD_NOTE} ?? '',
            status          : InvoiceStates::ISSUED->value,
            billingProfileId: $billingProfile->{BillingProfile::FIELD_ID},
            items           : collect([$invoiceItem]),
            tags            : collect(CommonTag::BUNDLE->value)
        );

        try {
            // TODO - Rename createUpdateInvoice to saveInvoice
            $invoiceUuid = $this->invoiceService->createUpdateInvoice(
                invoicePayload: $invoicePayload,
                authorType    : $user ? InvoiceEventAuthorTypes::USER : InvoiceEventAuthorTypes::SYSTEM,
                authorId      : $user?->{User::FIELD_ID}
            );

            $bundleInvoice->update([
                BundleInvoice::FIELD_PAYABLE_INVOICE_ID => Invoice::findByUuid($invoiceUuid)->id,
            ]);

            $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
                $bundleInvoice->{BundleInvoice::FIELD_ID},
                sprintf(
                    'Create payable invoice with ID %s',
                    $bundleInvoice->{BundleInvoice::FIELD_PAYABLE_INVOICE_ID},
                )
            );

            return true;

        } catch (Exception $exception) {
            $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
                $bundleInvoice->{BundleInvoice::FIELD_ID},
                'Failed to create payable invoice: ' . $exception->getMessage()
            );

            throw $exception;
        }
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return bool
     * @throws BindingResolutionException
     */
    public function applyCreditsToCompany(
        BundleInvoice $bundleInvoice,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null,
    ): bool
    {
        $bundle = $bundleInvoice->{BundleInvoice::RELATION_BUNDLE};

        $billingProfileIds = Arr::get($bundle->payload, 'billing_profile_ids', []);

        $invoice = Invoice::query()->findOrFail($bundleInvoice->{BundleInvoice::FIELD_PAYABLE_INVOICE_ID});

        $atomicTotalCredit = $bundle->{Bundle::FIELD_COST} * 100;
        $atomicTotalSignup = ($bundle->{Bundle::FIELD_CREDIT} - $bundle->{Bundle::FIELD_COST}) * 100;

        $creditAggregateRoot = CreditAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        if ($atomicTotalCredit > 0) {
            $creditAggregateRoot->addCredit(
                uuid            : Str::uuid()->toString(),
                companyReference: $invoice->{Invoice::RELATION_COMPANY}->{Company::FIELD_REFERENCE},
                amount          : $atomicTotalCredit,
                type            : CreditType::CREDIT->value,
                authorType      : $authorType->value,
                authorId        : $authorId,
                notes           : 'Credit automatically applied through invoice ' . $invoice->{Invoice::FIELD_ID},
                billingProfileIds: $billingProfileIds
            );
        }

        if ($atomicTotalSignup > 0) {
            $creditAggregateRoot->addCredit(
                uuid            : Str::uuid()->toString(),
                companyReference: $invoice->{Invoice::RELATION_COMPANY}->{Company::FIELD_REFERENCE},
                amount          : $atomicTotalSignup,
                type            : CreditType::SIGNUP_BONUS->value,
                authorType      : $authorType->value,
                authorId        : $authorId,
                notes           : 'Signup Bonus automatically applied through invoice ' . $invoice->{Invoice::FIELD_ID},
                billingProfileIds: $billingProfileIds
            );
        }

        $creditAggregateRoot->persist();

        return true;
    }
}
