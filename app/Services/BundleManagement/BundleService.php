<?php

namespace App\Services\BundleManagement;

use App\Http\Controllers\API\CompanyRegistration\CompanyRegistrationController;
use App\Http\Requests\Bundles\PurchaseBundleRequest;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Odin\Company;
use App\Repositories\BundleManagement\BundleInvoiceRepository;
use App\Repositories\BundleManagement\BundleRepository;
use App\Repositories\Odin\CompanyRepository;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

class BundleService
{

    public function __construct(
        protected BundleRepository $bundleRepository,
        protected BundleInvoiceRepository $bundleInvoiceRepository,
        protected CompanyRepository $companyRepository,
    ){}


    /**
     * Tries to find a company by a provided reference field.
     * If one is found a bundle invoice is created for that company.
     *
     * @param array $data
     * @param Bundle $bundle
     * @return array
     */
    public function processPurchasedBundle(array $data, Bundle $bundle): array
    {
        try {
            $company = $this->companyRepository->findByReferenceOrFail($data[PurchaseBundleRequest::REQUEST_COMPANY_REFERENCE]);
            $invoice = $this->bundleInvoiceRepository->createNewInvoice($bundle->id, $company->id);

            return [
                'status' => true,
                'message' => 'Successfully created with ID: ' . $invoice->{BundleInvoice::FIELD_ID}
            ];
        } catch (Exception $exception) {
            return [
                'status' => false,
                'message' => $exception->getMessage()
            ];
        }
    }

}
