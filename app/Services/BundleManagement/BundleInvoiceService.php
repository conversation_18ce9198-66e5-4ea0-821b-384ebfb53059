<?php

namespace App\Services\BundleManagement;

use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Legacy\EloquentCreditLog;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\BundleManagement\BundleInvoiceHistoryRepository;
use Exception;
use Spatie\FlareClient\Http\Exceptions\BadResponse;

class BundleInvoiceService
{

    public function __construct(
        protected BundleInvoiceLegacySyncService $legacySyncService,
        protected BundleInvoiceHistoryRepository $bundleInvoiceHistoryRepository
    ){}

    /**
     * @param BundleInvoice $bundleInvoice
     * @param User|null $user
     * @return bool
     * @throws BadResponse
     */
    public function createPayableLegacyInvoice(BundleInvoice $bundleInvoice, ?User $user = null): bool
    {
        $bundleId = $bundleInvoice->{BundleInvoice::FIELD_BUNDLE_ID};
        $companyId = $bundleInvoice->{BundleInvoice::RELATION_COMPANY}->{Company::FIELD_LEGACY_ID};
        $userId = $user instanceof User ? $user->{User::FIELD_ID} : 1; // Default to system user
        $headers = [
            'Idempotency-Key' => base64_encode($userId . $bundleId . $companyId)
        ];
        $data = [
            'companyid' => $companyId,
            'invoice_amount' => $bundleInvoice->{BundleInvoice::FIELD_COST},
            'credit_amount' => $bundleInvoice->{BundleInvoice::FIELD_CREDIT},
            'invoice_description' => $bundleInvoice->{BundleInvoice::RELATION_BUNDLE}->{Bundle::FIELD_TITLE} ?? '',
            'invoice_note' => $bundleInvoice->{BundleInvoice::FIELD_NOTE} ?? '',
            'discount_description' => ''
        ];


        /**
         * Try to create the payable invoice and attach the response data to the BundleInvoice.
         * On success create a BundleHistoryRecord with the details.
         * On failure, catch it and create a BundleHistoryRecord, then continue to bubble the exception.
         */
        try {
            $res = $this->legacySyncService->post(
                "invoice/create",
                $data,
                null,
                $headers
            );

            [
                'invoice_id' => $invoiceId,
                'invoice_url' => $invoiceUrl
            ] = $res->json();

            if (!$invoiceId) {
                throw new BadResponse('Response is missing a required invoice_id parameter.');
            }
            if (!$invoiceUrl) {
                throw new BadResponse('Response is missing a required invoice_url parameter.');
            }

            $bundleInvoice->update([
                BundleInvoice::FIELD_PAYABLE_INVOICE_ID => $invoiceId,
                BundleInvoice::FIELD_PAYABLE_INVOICE_URL => $invoiceUrl,
            ]);

            $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
                $bundleInvoice->{BundleInvoice::FIELD_ID},
                sprintf(
                    'Create payable invoice with ID %s and URL %s',
                    $bundleInvoice->{BundleInvoice::FIELD_PAYABLE_INVOICE_ID},
                    $bundleInvoice->{BundleInvoice::FIELD_PAYABLE_INVOICE_URL}
                )
            );

            return true;

        } catch (Exception $exception) {
            $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
                $bundleInvoice->{BundleInvoice::FIELD_ID},
                'Failed to create payable invoice: ' . $exception->getMessage()
            );

            throw $exception;
        }
    }


    /**
     * @param BundleInvoice $bundleInvoice
     * @return bool
     * @throws BadResponse
     */
    public function applyLeadCredit(BundleInvoice $bundleInvoice): bool
    {
        $companyId = $bundleInvoice->{BundleInvoice::RELATION_COMPANY}->{Company::FIELD_LEGACY_ID};
        $data = [
            'invoiceid'           => $bundleInvoice->{BundleInvoice::FIELD_PAYABLE_INVOICE_ID},
            'companyid'           => $companyId,
            'credit_amount'       => $bundleInvoice->{BundleInvoice::FIELD_COST},
            'signup_bonus_amount' => $bundleInvoice->{BundleInvoice::FIELD_CREDIT} - $bundleInvoice->{BundleInvoice::FIELD_COST},
        ];

        /**
         * Try to create the payable invoice and attach the response data to the BundleInvoice.
         * On success create a BundleHistoryRecord with the details.
         * On failure, catch it and create a BundleHistoryRecord, then continue to bubble the exception.
         */
        try {
            if (!$companyId) {
                throw new BadResponse('Company is missing a legacy ID.');
            }

            if ($this->hasCreditApplied($companyId, $bundleInvoice->payable_invoice_id)) {
                throw new BadResponse('Credit has already been applied for this bundle invoice.');
            }

            $res = $this->legacySyncService->post("invoice/apply-lead-credit", $data);

            [
                "company_id"            => $companyId,
                "credit_amount_applied" => $creditAmountApplied
            ] = $res->json();

            if (!$companyId) {
                throw new BadResponse('Response is missing a required company_id parameter.');
            }
            if (!$creditAmountApplied) {
                throw new BadResponse('Response is missing a required credit_amount_applied parameter.');
            }

            $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
                $bundleInvoice->{BundleInvoice::FIELD_ID},
                sprintf(
                    '%s credit applied to the company with id: %s',
                    $companyId,
                    $creditAmountApplied
                )
            );

            return true;

        } catch (Exception $exception) {
            $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
                $bundleInvoice->{BundleInvoice::FIELD_ID},
                'Failed to apply credit to company account: ' . $exception->getMessage()
            );

            throw $exception;
        }
    }

    /**
     * @param int $companyId
     * @param int $invoiceId
     *
     * @return bool
     */
    public function hasCreditApplied(int $companyId, int $invoiceId): bool
    {
        return !!EloquentCreditLog::query()
            ->where(EloquentCreditLog::COMPANY_ID, $companyId)
            ->where(EloquentCreditLog::INVOICE_ID, $invoiceId)
            ->where(EloquentCreditLog::STATUS, EloquentCreditLog::STATUS_ISSUED)
            ->first();
    }

}
