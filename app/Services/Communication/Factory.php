<?php

namespace App\Services\Communication;

use App\Contracts\Services\Communication\CommunicationContract;
use App\Services\LeadProcessing\LeadCommunicationService;
use Twilio\Exceptions\ConfigurationException;

class Factory
{
    /**
     * Handles making an instance of LeadCommunicationContract
     *
     * @param string $driver
     * @return CommunicationContract|null
     * @throws ConfigurationException
     */
    static function make(string $driver): ?CommunicationContract
    {
        switch ($driver) {
            case "twilio":
                return new TwilioCommunicationService(
                    config('services.twilio.sid'),
                    config('services.twilio.token'),
                    config('services.twilio.ml_sid')
                );
            case "dummy": return new DummyCommunicationService();
            default:
                return null;
        }
    }
}
