<?php

namespace App\Services\Communication;

use App\Contracts\Services\Communication\CommunicationContract;
use App\Enums\RoleType;
use App\Models\Call;
use App\Models\Role;
use App\Models\TextMediaAsset;
use App\Services\NotificationService;
use Illuminate\Support\Str;
use Twilio\Exceptions\TwilioException;
use Twilio\Jwt\ClientToken;
use Twilio\Rest\Api\V2010\Account\IncomingPhoneNumberInstance;
use Twilio\Rest\Client;
use Twilio\Security\RequestValidator;

class TwilioCommunicationService implements CommunicationContract
{
    const REQUEST_TO_NUMBER                    = 'To';
    const REQUEST_FROM_NUMBER                  = 'From';
    const REQUEST_REFERENCE                    = 'MessageSid';
    const REQUEST_BODY                         = 'Body';
    const string REQUEST_NUM_MEDIA             = 'NumMedia';
    const string REQUEST_MEDIA_CONTENT_TYPE    = 'MediaContentType';
    const string REQUEST_MEDIA_URL             = 'MediaUrl';

    // If the twilio phone number friendly name starts with this, it will be ignored when looking for available user phone numbers
    const FRIENDLY_NAME_SYSTEM_RESERVED_PREFIX = 'system_reserved';

    const SMS_SUBJECT = 'SMS Sending Failed';
    const SMS_BODY = 'Failed to send SMS to ';

    protected Client $client;

    /**
     * @param string $sid
     * @param string $token
     * @param string $mlSid
     * @throws \Twilio\Exceptions\ConfigurationException
     */
    public function __construct(
        protected string $sid,
        protected string $token,
        protected string $mlSid
    )
    {
        $this->client = new Client($sid, $token);
    }

    /**
     * @param string $from
     * @param string $to
     * @param string $body
     * @param string|null $fromType
     * @inheritDoc
     * @throws TwilioException
     */
    public function sendSMS(
        string $from,
        string $to,
        string $body,
        ?string $fromType = self::FROM_TYPE_PHONE,
        ?array $meta = [],
    ): string
    {
        try {
            $options = match ($fromType) {
                self::FROM_TYPE_SERVICE => [
                    'body' => $body,
                    'messagingServiceSid' => $from,
                ],
                default  => [
                    'body' => $body,
                    'from' => $from,
                ]
            };

            $options = array_merge($options, $meta ?: []);

            return $this->client->messages->create($to, $options)->sid;
        } catch(\Exception $e) {

            $subject = self::SMS_SUBJECT;
            $notificationBody = self::SMS_BODY . "$to. Error: " . $e->getMessage();

            /** @var NotificationService $notificationService */
            $notificationService = app(NotificationService::class);
            $roleId = Role::where(ROLE::FIELD_NAME, RoleType::ALERT_PROCESSOR->value)->value(ROLE::FIELD_ID);

            if ($roleId) {
                $notificationService->createNotificationForRole(
                    $roleId,
                    null,
                    $subject,
                    $notificationBody,
                    null
                );
            }

            throw $e;
        }
    }

    /**
     * Handles validating that an incoming webhook request was sent by twilio.
     *
     * @param string $header
     * @param array  $postVars
     * @return bool
     */
    public function validateIncomingWebhook(string $header, array $postVars): bool
    {
        $validator = new RequestValidator($this->token);

        return $validator->validate($header, Str::replace("http://", "https://", request()->fullUrl()), $postVars);
    }

    /**
     * Handles an incoming SMS webhook.
     *
     * @param array $data
     * @return void
     * @throws \Throwable
     */
    public function handleIncomingSMS(array $data): void
    {
        /** @var CommunicationService $service */
        $service = app()->make(CommunicationService::class);

        if(array_key_exists(self::REQUEST_NUM_MEDIA, $data) && $data[self::REQUEST_NUM_MEDIA] > 0)
            $mediaArray = $this->getMediaFromDataPayLoad($data);

        $service->receiveInboundSMS(
            $this->getServiceName(),
            $data[self::REQUEST_REFERENCE],
            $data[self::REQUEST_TO_NUMBER],
            Str::replace('+1', '', $data[self::REQUEST_FROM_NUMBER]),
            $data[self::REQUEST_BODY] ?? '',
            $mediaArray ?? null,
        );

    }

    /**
     * Strips US country code.
     *
     * @param string|null $number
     * @return string
     */
    protected function formatNumber(?string $number): string
    {
        return Str::replace("+1", "", $number ?? "");
    }

    /**
     * Returns the available numbers for the authenticated twilio
     * account.
     *
     * @return array
     */
    public function getAvailableNumbers(): array
    {
        $incoming_phone_numbers = $this->client->incomingPhoneNumbers
            ->read([], 1000);

        return collect($incoming_phone_numbers)
            ->filter(function(IncomingPhoneNumberInstance $phoneNumberRecord){
                return !str_starts_with($phoneNumberRecord->friendlyName, self::FRIENDLY_NAME_SYSTEM_RESERVED_PREFIX);
            })
            ->mapWithKeys(fn($item) => [$item->sid => ["number" => $item->phoneNumber, 'name' => $item->friendlyName]])
            ->toArray();
    }

    /**
     * Handles generating the respective client token.
     *
     * @param array $numbers
     * @return string
     */
    public function retrieveWebPhoneToken(array $numbers = []): string
    {
        $token = new ClientToken($this->sid, $this->token);
        $token->allowClientOutgoing($this->mlSid);

        foreach($numbers as $number) {
            // Remove non number characters to avoid twilio error:
            // Only alphanumeric characters allowed in client name
            $sanitizedNumber = preg_replace("/[^0-9]/", "", $number);
            $token->allowClientIncoming('incoming_' . $sanitizedNumber);
        }

        return $token->generateToken();
    }

    /**
     * @inheritDoc
     */
    public function getServiceName(): string
    {
        return Call::EXTERNAL_TYPE_TWILIO;
    }

    protected function getMediaFromDataPayLoad(array $data): array
    {
        $mediaResponse = [];
        for($i = 0; $i < $data[self::REQUEST_NUM_MEDIA]; $i++) {
            $mediaResponse[] = [
                TextMediaAsset::FIELD_URL   => $data[self::REQUEST_MEDIA_URL.$i],
                TextMediaAsset::FIELD_TYPE  => $data[self::REQUEST_MEDIA_CONTENT_TYPE.$i]
            ];
        }

        return $mediaResponse;
    }

}
