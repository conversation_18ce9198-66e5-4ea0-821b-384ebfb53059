<?php

namespace App\Services\Communication;

use App\Contracts\Services\Communication\CommunicationContract;
use Illuminate\Support\Str;

class DummyCommunicationService implements CommunicationContract
{

    /**
     * @inheritDoc
     */
    public function sendSMS(
        string $from,
        string $to,
        string $body,
        ?string $fromType = self::FROM_TYPE_PHONE,
        ?array $meta = [],
    ): string
    {
        logger()->info(
            "Dummy SMS Sent: " . json_encode([
                'from' => $from,
                'to' => $to,
                'body' => $body,
                'fromType' => $fromType,
                'meta' => $meta,
            ]));

        return Str::uuid()->toString();
    }

    /**
     * @inheritDoc
     */
    public function getAvailableNumbers(): array
    {
        return [];
    }

    public function retrieveWebPhoneToken(array $numbers = []): string
    {
        return Str::uuid()->toString();
    }

    /**
     * @inheritDoc
     */
    public function getServiceName(): string
    {
        return 'dummy';
    }
}
