<?php

namespace App\Services;
use App\Repositories\CompanyCampaignContactDeliveryLogRepository;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class CompanyCampaignContactDeliveryLogService
{
    public function __construct(
        protected CompanyCampaignContactDeliveryLogRepository $companyCampaignContactDeliveryLogRepository
    )
    {}

    /**
     * @param int $perPage
     * @param int $page
     * @param int|null $companyId
     * @param bool|null $succeeded
     * @param string|null $campaign
     * @param int|null $consumerProductId
     * @param array|null $dateRange
     * @param int|null $invoiceId
     *
     * @return LengthAwarePaginator
     */
    public function listContactDeliveryLogs(
        int     $perPage,
        int     $page,
        ?int    $companyId = null,
        ?bool   $succeeded = null,
        ?string $campaign = null,
        ?int    $consumerProductId  = null,
        ?array  $dateRange = null,
        ?int    $invoiceId = null,

    ): LengthAwarePaginator
    {
        return $this->companyCampaignContactDeliveryLogRepository->listContactDeliveryLogs(
            perPage             :    $perPage,
            page                :       $page,
            companyId           :  $companyId,
            succeeded           :  $succeeded,
            campaign            :   $campaign,
            consumerProductId   : $consumerProductId,
            dateRange           : $dateRange,
            invoiceId           : $invoiceId
        );
    }

    public function listAllContactDeliveryLogs(
        ?int    $limit = 500,
        ?int    $companyId = null,
        ?bool   $succeeded = null,
        ?string $campaign = null,
        ?int    $consumerProductId  = null,
        ?array  $dateRange = null,
        ?int    $invoiceId = null,
    ): Collection
    {
        return $this->companyCampaignContactDeliveryLogRepository->getBaseQuery(
            companyId: $companyId,
            succeeded: $succeeded,
            campaign: $campaign,
            consumerProductId: $consumerProductId,
            dateRange: $dateRange,
            invoiceId: $invoiceId
        )->limit($limit)->get();
    }
}

