<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class SlackDirectMessageService
{
    /**
     * @throws ConnectionException
     */
    public static function sendMessage(string $message, int $userId): bool
    {
        /** @var User $user */
        $user        = User::find($userId);
        $slackUserId = $user->slack_username;
        if (empty($slackUserId))
            return false;

        $authToken = config('services.slack.admin_system_bot_auth_token');
        if (empty($authToken))
            return false;

        $request   = Http::withToken($authToken)->post('https://slack.com/api/conversations.open', ['users' => $slackUserId]);
        $channelId = $request->json()['channel']['id'];

        Http::withToken($authToken)->post('https://slack.com/api/chat.postMessage', [
            'channel'       => $channelId,
            'markdown_text' => $message,
        ]);

        return true;
    }

    /**
     * @param string $channelId
     * @param string $message
     * @param string|null $slackAuthToken
     *
     * @return bool
     * @throws ConnectionException
     */
    public static function sendMessageToChannel(string $channelId, string $message, ?string $slackAuthToken = null): bool
    {
        $authToken = $slackAuthToken ?? config('services.slack.admin_system_bot_auth_token');

        if (!$authToken) {
            return false;
        }

        $response = Http::withToken($authToken)->post('https://slack.com/api/chat.postMessage', [
            'channel'       => $channelId,
            'markdown_text' => $message,
        ]);

        return $response->json()['ok'] ?? false;
    }
}
