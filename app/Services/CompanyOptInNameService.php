<?php

namespace App\Services;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyOptInName;
use App\Models\Odin\Company;

class CompanyOptInNameService
{

    /**
     * @param CompanyCampaign $campaign
     * @return CompanyOptInName|null
     */
    public static function getOptInNameByCampaign(CompanyCampaign $campaign): ?CompanyOptInName
    {
        return $campaign->activeOptInName ?? self::getOptInNameByCompany($campaign->company);
    }

    /**
     * @param Company $company
     * @return CompanyOptInName|null
     */
    public static function getOptInNameByCompany(Company $company): ?CompanyOptInName
    {
        return $company->activeOptInName;
    }
}
