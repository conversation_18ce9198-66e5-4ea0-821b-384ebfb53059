<?php

namespace App\Services\Search\Searchables;

use App\Contracts\Search\SearchableContract;
use App\DataModels\Search\SearchResultDataModel;
use App\Enums\PermissionType;
use App\Models\Sales\Task;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class TaskSearchableService implements SearchableContract
{

    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(PermissionType::TASK->value);
    }

    public function getCategory(): string
    {
        return 'Tasks';
    }

    public function search(string $query): Collection
    {
        return Task::query()
            ->where(Task::FIELD_COMPLETED, Task::TASK_NOT_COMPLETED)
            ->where(Task::FIELD_ASSIGNED_USER_ID, Auth::id())
            ->where(fn(Builder $dataQuery) => $dataQuery->where(Task::FIELD_SUBJECT, 'LIKE', "%{$query}%"))
            ->orderBy(Task::FIELD_ID, 'DESC')
            ->limit(5)
            ->get()
            ->map(fn(Task $task) => new SearchResultDataModel(
                $task->id,
                $task->subject,
                route("task-queue", ["tasks" => $task->id]),
                false
            ));
    }
}
