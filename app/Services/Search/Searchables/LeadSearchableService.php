<?php

namespace App\Services\Search\Searchables;

use App\Contracts\Search\SearchableContract;
use App\DataModels\Search\SearchResultDataModel;
use App\Enums\PermissionType;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class LeadSearchableService implements SearchableContract
{
    /**
     * @inheritDoc
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::LEAD_PROCESSING->value);
    }

    /**
     * @inheritDoc
     */
    public function getCategory(): string
    {
        return 'Leads';
    }

    /**
     * @inheritDoc
     */
    public function search(string $query): Collection
    {
        return EloquentQuote::query()
            ->with(EloquentQuote::RELATION_ADDRESS)
            ->where(EloquentQuote::TIMESTAMP_ADDED, '>=', Carbon::now()->subMonths(3)->timestamp)
            ->where(fn(Builder $dataQuery) => $dataQuery->where(EloquentQuote::ID, 'LIKE', "%{$query}%")
                ->orWhere(DB::raw('concat(' . EloquentQuote::FIRST_NAME . '," ",' . EloquentQuote::LAST_NAME . ')'), 'LIKE', "%{$query}%"))
            ->orWhere(EloquentQuote::USER_EMAIL, 'LIKE', "{$query}%")
            ->orWhereHas(EloquentQuote::RELATION_ADDRESS,
                fn(Builder $builder) => $builder
                    ->where(EloquentAddress::ADDRESS1, 'LIKE', "%{$query}%")
                    ->orWhere(EloquentAddress::ADDRESS2, 'LIKE', "%{$query}%")
                    ->orWhere(EloquentAddress::CITY, 'LIKE', "{$query}%")
                    ->orWhere(DB::raw('REGEXP_REPLACE(TRIM('.EloquentAddress::PHONE.'), "^\+1|^1|[^0-9]*", "")'), $query)
            )
            ->orderBy(EloquentQuote::ID, 'DESC')
            ->limit(5)
            ->get()
            ->map(fn(EloquentQuote $lead) => new SearchResultDataModel(
                $lead->quoteid,
                "Lead {$lead->quoteid} ({$lead->firstname} {$lead->lastname})",
                route("lead-processing", ["lead_id" => $lead->quoteid]),
                false
            ));
    }
}
