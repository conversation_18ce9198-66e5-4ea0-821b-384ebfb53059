<?php

namespace App\Services\Search;

use App\Contracts\Search\SearchableContract;
use App\DataModels\Search\SearchCategoryDataModel;
use App\DataModels\Search\SearchResultDataModel;
use Illuminate\Support\Collection;

class SearchService
{
    /**
     * @param Collection<SearchableContract> $searchables
     */
    public function __construct(protected Collection $searchables) {}

    /**
     * @param string $query
     * @return Collection<Collection<SearchResultDataModel>>
     */
    public function search(string $query): Collection
    {
        return $this->searchables
            ->map(function(SearchableContract $searchable) use ($query) {
                return ($searchable->authorize()) ? new SearchCategoryDataModel($searchable->getCategory(), $searchable->search($query)) : ['category' => $searchable->getCategory(), 'items' => []];
        });
    }
}
