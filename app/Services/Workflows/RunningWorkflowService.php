<?php

namespace App\Services\Workflows;

use App\Models\CompletedWorkflow;
use App\Models\RunningWorkflow;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class RunningWorkflowService
{
    /**
     * @param RunningWorkflow $runningWorkflow
     *
     * @return bool|null
     */
    public function completeWorkflow(RunningWorkflow $runningWorkflow): ?bool
    {
        CompletedWorkflow::query()->create([
            CompletedWorkflow::FIELD_WORKFLOW_ID         => $runningWorkflow->workflow_id,
            CompletedWorkflow::FIELD_RUNNING_WORKFLOW_ID => $runningWorkflow->id,
            CompletedWorkflow::FIELD_PAYLOAD             => $runningWorkflow->payload,
        ]);

        return $runningWorkflow->delete();
    }

    /**
     * @param  Collection  $workflowsToComplete
     * @return void
     */
    public function completeWorkflows(Collection $workflowsToComplete): void
    {
        if ($workflowsToComplete->count() === 0) {
            return;
        }

        $date = Carbon::now();

        CompletedWorkflow::insert(
            $workflowsToComplete->map(function (RunningWorkflow $runningWorkflow) use($date) {
                return [
                    CompletedWorkflow::FIELD_WORKFLOW_ID         => $runningWorkflow->workflow_id,
                    CompletedWorkflow::FIELD_RUNNING_WORKFLOW_ID => $runningWorkflow->id,
                    CompletedWorkflow::FIELD_PAYLOAD             => $runningWorkflow->payload->toJson(),
                    CompletedWorkflow::CREATED_AT                => $date,
                    CompletedWorkflow::UPDATED_AT                => $date
                ];
            })->toArray()
        );

        RunningWorkflow::query()->whereIn(RunningWorkflow::FIELD_ID, $workflowsToComplete->pluck('id'))->delete();
    }
}
