<?php

namespace App\Services\Workflows;

use App\Enums\EventCategory;
use App\Enums\RunningWorkflowStatus;
use App\Factories\Workflows\RunningWorkflowFactory;
use App\Factories\Workflows\WorkflowEventFactory;
use App\Factories\Workflows\WorkflowPayloadFactory;
use App\Jobs\Workflows\RunWorkflowPipeline;
use App\Models\WorkflowEvent;
use App\Repositories\PubSubEventRepository;
use Illuminate\Foundation\Bus\DispatchesJobs;

class WorkflowEventService
{
    use DispatchesJobs;

    public function __construct(protected PubSubEventRepository $pubSubEventRepository, protected WorkflowEventFactory $workflowEventFactory){}

    /**
     * Handle event
     *
     * @param string $type
     * @param string $event
     * @param array $data
     *
     * @return void
     */
    public function handle(string $type, string $event, array $data): void
    {
        $eventCategory = EventCategory::tryFrom($type);
        $eventsForCategory = $eventCategory
            ? $this->pubSubEventRepository->getEventsForCategory($eventCategory)
            : null;

        if (!$event || !$eventsForCategory || !in_array($event, $eventsForCategory)) {
            return;
        }

        /** @var WorkflowEvent|null $workflowEvent */
        $workflowEvent = WorkflowEvent::query()
            ->where(WorkflowEvent::FIELD_EVENT_CATEGORY, $type)
            ->where(WorkflowEvent::FIELD_EVENT_NAME, $event)
            ->first();

        if (!$workflowEvent) {
            return;
        }

        $payload = WorkflowPayloadFactory::createFromPubSubEventPayload($data);

        foreach ($workflowEvent->workflows as $workflow) {
            if($workflow->entry_action_id !== null) {
                $runningWorkFlow = RunningWorkflowFactory::create($workflow->id, $payload, RunningWorkflowStatus::INITIAL, $workflow->entry_action_id);

                $this->dispatch(new RunWorkflowPipeline($runningWorkFlow));
            }
        }
    }
}
