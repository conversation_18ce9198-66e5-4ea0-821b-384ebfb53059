<?php

namespace App\Services\Workflows;

use App\Contracts\Workflows\HasTargetContract;
use App\Enums\Target;
use App\Services\NotificationService;
use App\Workflows\Actions\AddNotificationAction;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use Exception;

class LegacyCRMEntryService
{
    /**
     * @param NotificationService $notificationService
     * @param WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory
     */
    public function __construct(protected NotificationService $notificationService, protected WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory) {}

    /**
     * Add legacy CRM entry
     *
     * @param AddNotificationAction $addNotificationAction
     *
     * @return void
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function createCRMEntry(AddNotificationAction $addNotificationAction): void
    {
        $target = $addNotificationAction->getTargets()->first(fn(HasTargetContract $target) => $target->getTargetType() === Target::COMPANY);

        if (!$target) {
            return;
        }

        $this->notificationService->createNotificationForCompany(
            $target->getTarget(),
            $addNotificationAction->getNotifier(),
            $this->getMessage($addNotificationAction),
            $addNotificationAction->getChannel()->toNotificationMapping()
        );
    }

    /**
     * Ensures that the notification goes through the shortcode service.
     *
     * @param AddNotificationAction $action
     * @return string
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     * @throws Exception
     */
    protected function getMessage(AddNotificationAction $action): string
    {
        return ($this->workflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER))->handle($action->getMessage(), $action->getPayload());
    }
}
