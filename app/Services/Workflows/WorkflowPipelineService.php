<?php

namespace App\Services\Workflows;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\ActionContract;
use App\Contracts\Workflows\PausesWorkflow;
use App\Enums\RunningWorkflowStatus;
use App\Models\CompletedWorkflow;
use App\Models\RunningWorkflow;
use App\Models\Workflow;
use App\Models\WorkflowAction;
use Illuminate\Contracts\Container\BindingResolutionException;

class WorkflowPipelineService
{
    /**
     * Constructor for the workflow pipeline service.
     *
     * @param WorkflowProcessingService $processingService
     */
    public function __construct(protected WorkflowProcessingService $processingService) { }

    /**
     * Handles the execution of a workflow.
     *
     * @param RunningWorkflow $runningWorkflow
     * @return void
     * @throws BindingResolutionException
     */
    public function execute(RunningWorkflow $runningWorkflow): void
    {
        $currentAction = match ($runningWorkflow->status) {
            RunningWorkflowStatus::HALTED => $this->getNextAction($runningWorkflow, $this->getCurrentAction($runningWorkflow)),
            default                       => $this->getCurrentAction($runningWorkflow)
        };

        while (true) {
            $shouldContinue = $this->handleAction($runningWorkflow, $currentAction);

            // In case the current action is missing/null, we can simply skip looking for the sibling/next action here.
            $currentAction = $currentAction
                ? $this->getNextAction($runningWorkflow, $currentAction)
                : null;

            $this->persist(
                $runningWorkflow,
                $currentAction,
                $shouldContinue ? RunningWorkflowStatus::RUNNING : RunningWorkflowStatus::HALTED
            );

            if (!$currentAction && $shouldContinue) {
                $this->completeWorkflow($runningWorkflow);
                break;
            }

            if (!$shouldContinue)
                break;
        }
    }

    /**
     * Returns the current action for a given running workflow.
     *
     * @param RunningWorkflow $workflow
     * @return WorkflowAction|null
     */
    protected function getCurrentAction(RunningWorkflow $workflow): ?WorkflowAction
    {
        return $workflow->currentAction;
    }

    /**
     * Returns the next action for a running workflow.
     *
     * @param RunningWorkflow $workflow
     * @param WorkflowAction $action
     * @return WorkflowAction|null
     * @throws BindingResolutionException
     */
    protected function getNextAction(RunningWorkflow $workflow, WorkflowAction $action): ?WorkflowAction
    {
        return $this->getActionClass($workflow, $action)?->getNextAction() ?? null;
    }

    /**
     * Returns the action class for a workflow action.
     *
     * @param RunningWorkflow $workflow
     * @param WorkflowAction $action
     * @return Action|null
     * @throws BindingResolutionException
     */
    protected function getActionClass(RunningWorkflow $workflow, WorkflowAction $action): ?Action
    {
        $actionClass = $action->action_type->getActionClass();
        if ($actionClass) {
            /** @var Action $class */
            $class = app()->make(
                $actionClass,
                ["workflowAction" => $action, "payload" => $workflow->payload, "runningWorkflowId" => $workflow->id]
            );

            return $class;
        }

        return null;
    }

    /**
     * Handles running an action.
     *
     * @param RunningWorkflow $workflow
     * @param WorkflowAction|null $action
     * @return bool
     * @throws BindingResolutionException
     */
    protected function handleAction(RunningWorkflow &$workflow, ?WorkflowAction $action): bool
    {
        // In case that there was an action that halted the workflow previously
        // and there's no further work, we can safely return true here.
        if ($action == null)
            return true;

        $actionHandler = $this->getActionClass($workflow, $action);
        if (!$actionHandler)
            return false;

        $workflow->payload = $this->processingService->process($actionHandler);

        return !($actionHandler instanceof PausesWorkflow);
    }

    /**
     * Saves the running workflow.
     *
     * @param RunningWorkflow $workflow
     * @param WorkflowAction|null $currentAction
     * @param RunningWorkflowStatus $status
     * @return void
     */
    protected function persist(RunningWorkflow $workflow, ?WorkflowAction $currentAction, RunningWorkflowStatus $status): void
    {
        $workflow->status            = $status;
        $workflow->current_action_id = $status === RunningWorkflowStatus::HALTED ? $workflow->current_action_id : $currentAction?->id;
        $workflow->save();
    }

    /**
     * Handles the completion of a running workflow.
     *
     * @param RunningWorkflow $runningWorkflow
     * @return void
     */
    protected function completeWorkflow(RunningWorkflow $runningWorkflow): void
    {
        CompletedWorkflow::query()->create([
            CompletedWorkflow::FIELD_WORKFLOW_ID         => $runningWorkflow->workflow_id,
            CompletedWorkflow::FIELD_RUNNING_WORKFLOW_ID => $runningWorkflow->id,
            CompletedWorkflow::FIELD_PAYLOAD             => $runningWorkflow->payload,
        ]);

        $runningWorkflow->delete();
    }
}
