<?php

namespace App\Services\Twilio;

use App\Contracts\PhoneVerificationServiceInterface;
use LogicException;
use Twilio\Exceptions\TwilioException;

class TwilioVerificationService implements PhoneVerificationServiceInterface
{
    /** @var string */
    protected string $phoneNumber;

    /** @var TwilioVerify */
    protected TwilioVerify $verificationService;

    public function __construct(
        TwilioVerify $verificationService
    )
    {
        $this->verificationService = $verificationService;
    }

    /**
     * @param string $key
     * @return bool
     * @throws TwilioException
     */
    public function sendCode(string $key): bool
    {
        if (!$this->phoneNumber) {
            throw new LogicException("A phone number must be set on the SMS Verification Service, before attempting to send a SMS. Use SMSVerificationService::setPhoneNumber(\$number) first.");
        }

        $this->verificationService->sendVerificationCode($this->phoneNumber);

        return true;
    }

    /**
     * @param string $key
     * @param string $code
     * @return bool
     * @throws TwilioException
     */
    public function verifyCode(string $key, string $code): bool
    {
        if (!$this->phoneNumber) {
            throw new LogicException("A phone number must be set on the SMS Verification Service, before attempting to verify a number. Use SMSVerificationService::setPhoneNumber(\$number) first.");
        }

        return $this->verificationService->verifySMSCode($this->phoneNumber, $code);
    }

    /**
     * Handles setting the phone number to verify.
     *
     * @param string $number
     * @return self
     */
    public function setPhoneNumber(string $number): self
    {
        $this->phoneNumber = $number;

        return $this;
    }

}
