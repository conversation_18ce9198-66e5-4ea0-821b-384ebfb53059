<?php

namespace App\Services\Twilio;

class TwilioPhoneNumberFormattingService
{
    /** @var string */
    protected string $countryCode = '1';
    /** @var string */
    protected string $areaCode;
    /** @var string */
    protected string $nextThree;
    /** @var string */
    protected string $lastFour;
    /** @var boolean */
    protected bool $isValidNumber = true;

    /**
     * @param string $phoneNumber
     */
    public function __construct(string $phoneNumber)
    {
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);
        if (strlen($phoneNumber) > 10) {
            $this->countryCode = substr($phoneNumber, 0, strlen($phoneNumber) - 10);
            $this->areaCode = substr($phoneNumber, -10, 3);
            $this->nextThree = substr($phoneNumber, -7, 3);
            $this->lastFour = substr($phoneNumber, -4, 4);
        } else if (strlen($phoneNumber) == 10) {
            $this->areaCode = substr($phoneNumber, 0, 3);
            $this->nextThree = substr($phoneNumber, 3, 3);
            $this->lastFour = substr($phoneNumber, 6, 4);
        } else {
            $this->isValidNumber = false;
        }
    }

    /**
     * @return string|null
     */
    public function fullNumberWithCountryCode(): ?string
    {
        return $this->isValidNumber ?
            '+' . $this->countryCode . $this->areaCode . $this->nextThree . $this->lastFour :
            null;
    }

    /**
     * @return string|null
     */
    public function prettyNumber(): ?string
    {
        return $this->isValidNumber ?
            '+' . $this->countryCode . ' (' . $this->areaCode . ') ' . $this->nextThree . '-' . $this->lastFour :
            null;
    }

    /**
     * @return bool
     */
    public function isValid(): bool
    {
        return $this->isValidNumber;
    }
}
