<?php

namespace App\Services\Twilio;

use App\Enums\CompanyUserVerificationMethod;
use Twilio\Exceptions\ConfigurationException;
use Twilio\Exceptions\TwilioException;
use Twilio\Rest\Client;

class TwilioVerify
{
    const string SMS_VERIFICATION_APPROVED_STATUS = 'approved';

    /** @var Client */
    protected Client $client;

    /** @var string */
    protected string $verifyServiceSid;

    /**
     * @param string $applicationSid
     * @param string $applicationToken
     * @param string $verifyServiceSid
     * @throws ConfigurationException
     */
    public function __construct(
        string $applicationSid,
        string $applicationToken,
        string $verifyServiceSid,
    )
    {
        $this->verifyServiceSid = $verifyServiceSid;
        $this->client = new Client($applicationSid, $applicationToken);
    }

    /**
     * @param string $phoneNumber
     *
     * @return void
     * @throws TwilioException
     */
    public function sendVerificationCode(string $phoneNumber): void
    {
        $phone = new TwilioPhoneNumberFormattingService($phoneNumber);
        if ($phone->isValid()) {
            $this->client->verify->v2
                ->services($this->verifyServiceSid)
                ->verifications
                ->create($phone->fullNumberWithCountryCode(), CompanyUserVerificationMethod::SMS->value);
        }
    }

    /**
     * @param string $number
     * @param string $code
     * @return bool
     * @throws TwilioException
     */
    public function verifySMSCode(string $number, string $code): bool
    {
        $phone = new TwilioPhoneNumberFormattingService($number);

        if (!$phone->isValid())
            return false;

        $verification = $this->client->verify->v2
            ->services($this->verifyServiceSid)
            ->verificationChecks
            ->create([
                'to'    => $phone->fullNumberWithCountryCode(),
                'code'  => $code
            ]);

        return $verification->status === self::SMS_VERIFICATION_APPROVED_STATUS;
    }
}
