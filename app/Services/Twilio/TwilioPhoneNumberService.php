<?php

namespace App\Services\Twilio;

use App\Enums\PhoneType;
use App\Models\Phone;
use Illuminate\Support\Str;
use Twilio\Exceptions\ConfigurationException;
use Twilio\Exceptions\TwilioException;
use Twilio\Rest\Client;

class TwilioPhoneNumberService
{
    protected Client $client;

    /**
     * @throws ConfigurationException
     */
    public function __construct()
    {

    }

    /**
     * <PERSON><PERSON> acquiring a new number through twilio.
     *
     * @param string|null $region
     * @param PhoneType $type
     * @param string $name
     * @return Phone
     * @throws TwilioException
     */
    public function acquireNumber(
        ?string   $region = null,
        PhoneType $type = PhoneType::OTHER,
        string    $name = "system_bought",
        array     $configuration = [],
    ): Phone
    {
        $twilioClient = new Client(config('services.twilio.sid'), config('services.twilio.token'));

        $available = $twilioClient->availablePhoneNumbers("US")
                         ->local
                         ->read(["region" => $region], 1)[0] ?? null;

        if (!$available)
            throw new \RuntimeException("No phone numbers available.");

        $bought = $twilioClient
            ->incomingPhoneNumbers
            ->create([
                "phoneNumber"  => $available->phoneNumber,
                // TODO: THIS
                // "statusCallback" => route("twilio-general-status"),
                // "statusCallbackEvent" => ["initiated", "ringing", "answered", "completed"],
                // "voiceFallbackUrl" => route("twilio-general-voice"),
                // "recordingStatusCallback" => route("twilio-general-recording-status"),
                // "voiceUrl" => route("twilio-general-voice"),
                'friendlyName' => $name,
                ...$configuration
            ]);

        $phone = new Phone();
        $phone->phone = $this->formatPhone($available->phoneNumber);
        $phone->status = Phone::STATUS_ACTIVE;
        $phone->friendly_name = $name;
        $phone->region = $region;
        $phone->type = $type;
        $phone->external_reference = $bought->sid;
        $phone->save();

        return $phone;
    }

    /**
     * @param string $sid
     *
     * @return bool
     * @throws ConfigurationException
     * @throws TwilioException
     */
    public function releasePhone(string $sid): bool
    {
        $twilioClient = new Client(config('services.twilio.sid'), config('services.twilio.token'));

        return $twilioClient->incomingPhoneNumbers($sid)->delete();
    }

    /**
     * Returns the formatted phone number.
     *
     * @param string $number
     * @return string
     */
    protected function formatPhone(string $number): string
    {
        return Str::replace("+1", "", $number);
    }
}
