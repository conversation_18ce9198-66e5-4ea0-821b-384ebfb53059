<?php

namespace App\Services\Shortcode\ShortcodeImplementation;

use App\Models\CompanyUserRelationship;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\Shortcode\ShortcodeSets\BDMShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\CompanyUserShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\FixrShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\MissedProductsShortcodeSet;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class MissedProductsShortcodeUseCase extends ShortcodeUseCase
{
    public function __construct(
        protected Company $company,
        protected CompanyUser $companyUser,
        protected OpportunityNotificationConfig $config,
    )
    {
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    public static function getShortcodeSets(): Collection
    {
        return collect([
            BDMShortcodeSet::DEFAULT_KEY            => new BDMShortcodeSet(),
            MissedProductsShortcodeSet::DEFAULT_KEY => new MissedProductsShortcodeSet(),
            FixrShortcodeSet::DEFAULT_KEY           => new FixrShortcodeSet(),
            CompanyUserShortcodeSet::DEFAULT_KEY    => new CompanyUserShortcodeSet(),
        ]);
    }

    /**
     * @inheritdoc
     */
    public function getMappedData(): Collection
    {
        return collect([
            BDMShortcodeSet::DEFAULT_KEY            => $this->getBDM(),
            MissedProductsShortcodeSet::DEFAULT_KEY => $this->getMissedProductPayload(),
            FixrShortcodeSet::DEFAULT_KEY           => $this->companyUser,
            CompanyUserShortcodeSet::DEFAULT_KEY    => $this->companyUser,
        ]);
    }

    /**
     * @return array{
     *      company_user: CompanyUser,
     *      company: Company,
     *      config: OpportunityNotificationConfig
     *  }
     */
    private function getMissedProductPayload(): array
    {
        return [
            'company_user' => $this->companyUser,
            'company'      => $this->company,
            'config'       => $this->config,
        ];
    }

    /**
     * @return User
     */
    private function getBDM(): User
    {
        $target = $this->company->businessDevelopmentManager;
        if (!$target) {
            $mostRecentRelation = CompanyUserRelationship::query()
                ->where(CompanyUserRelationship::FIELD_COMPANY_ID, $this->company->id)
                ->whereNull(
                    CompanyUserRelationship::TABLE . '.' . CompanyUserRelationship::FIELD_DELETED_AT,
                )
                ->orderBy(CompanyUserRelationship::UPDATED_AT, 'desc')
                ->first()
                ?->user_id;
            $target = User::query()->find($mostRecentRelation)
                ?? Auth::user()
                //TODO: define default managers somewhere to prevent email errors? There is no logged in user when this runs from a job
                ?? CompanyUserRelationship::query()
                    ->whereNull(
                        CompanyUserRelationship::TABLE . '.' . CompanyUserRelationship::FIELD_DELETED_AT,
                    )
                    ->first()
                    ->user;
        }

        return $target;
    }
}
