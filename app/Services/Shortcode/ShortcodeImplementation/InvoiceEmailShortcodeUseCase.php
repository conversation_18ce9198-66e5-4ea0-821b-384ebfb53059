<?php

namespace App\Services\Shortcode\ShortcodeImplementation;

use App\Models\Billing\Invoice;
use App\Services\Shortcode\ShortcodeSets\CompanyShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\InvoiceShortcodeSet;
use Illuminate\Support\Collection;

class InvoiceEmailShortcodeUseCase extends ShortcodeUseCase
{
    const string INVOICE = 'invoice';
    const string COMPANY = 'company';

    public function __construct(
        public Invoice $invoice,
    )
    {
        parent::__construct();
    }

    /**
     * @return Collection
     */
    public static function getShortcodeSets(): Collection
    {
        return collect([
            self::INVOICE => new InvoiceShortcodeSet(),
            self::COMPANY => new CompanyShortcodeSet()
        ]);
    }

    /**
     * @return Collection
     */
    public function getMappedData(): Collection
    {
        return collect([
            self::INVOICE => $this->invoice,
            self::COMPANY => $this->invoice->company,
        ]);
    }
}
