<?php

namespace App\Services\Shortcode\ShortcodeImplementation;

use App\Models\Odin\CompanyUser;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use App\Services\Shortcode\ShortcodeSets\BDMShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\CompanyUserShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\ProspectShortcodeSet;
use Illuminate\Support\Collection;

class PersonalEmailTemplateShortcodeUseCase extends ShortcodeUseCase
{
    const string COMPANY_USER = 'company_user';
    const string USER = 'user';

    public function __construct(protected CompanyUser $companyUser, protected User $user)
    {
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    public static function getShortcodeSets(): Collection
    {
        return collect([
            self::COMPANY_USER => new CompanyUserShortcodeSet(),
            self::USER => new BDMShortcodeSet(),
        ]);
    }

    public function getMappedData(): Collection
    {
        return collect([
            self::COMPANY_USER => $this->companyUser,
            self::USER => $this->user,
        ]);
    }
}
