<?php

namespace App\Services\Shortcode\ShortcodeImplementation;

use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use App\Services\Shortcode\ShortcodeSets\BDMShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\ProspectShortcodeSet;
use Illuminate\Support\Collection;

class ProspectShortcodeUseCase extends ShortcodeUseCase
{
    const string PROSPECT = 'prospect';
    const string BDM = 'BDM';

    public function __construct(protected NewBuyerProspect $newBuyerProspect, protected User $user)
    {
        parent::__construct();
    }

    /**
     * @inheritdoc
     */
    public static function getShortcodeSets(): Collection
    {
        return collect([
            self::PROSPECT => new ProspectShortcodeSet(),
            self::BDM => new BDMShortcodeSet(),
        ]);
    }

    public function getMappedData(): Collection
    {
        return collect([
            self::PROSPECT => $this->newBuyerProspect,
            self::BDM => $this->user
        ]);
    }
}
