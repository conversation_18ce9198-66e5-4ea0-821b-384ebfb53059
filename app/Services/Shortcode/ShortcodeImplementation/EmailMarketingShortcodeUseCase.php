<?php

namespace App\Services\Shortcode\ShortcodeImplementation;

use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\Shortcode\ShortcodeSets\AddressShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\ConsumerProductShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\ConsumerShortcodeSet;
use App\Services\Shortcode\ShortcodeSets\MarketingCampaignConsumerShortcodeSet;
use Faker\Factory;
use Illuminate\Support\Collection;

class EmailMarketingShortcodeUseCase extends ShortcodeUseCase
{
    const string CONSUMER                    = 'consumer';
    const string CONSUMER_ADDRESS            = 'consumer_address';
    const string CONSUMER_PRODUCT            = 'consumer_product';
    const string MARKETING_CAMPAIGN_CONSUMER = 'marketing_campaign_consumer';

    private function __construct(
        public Consumer                  $consumer,
        public Address                   $consumerAddress,
        public ConsumerProduct           $consumerProduct,
        public MarketingCampaignConsumer $marketingCampaignConsumer
    )
    {
        parent::__construct();
    }

    public static function fromMarketingCampaignConsumer(MarketingCampaignCOnsumer $marketingCampaignConsumer): EmailMarketingShortcodeUseCase
    {
        $consumerProduct = $marketingCampaignConsumer->consumer->consumerProducts()->first();

        return new self(
            consumer                 : $marketingCampaignConsumer->consumer,
            consumerAddress          : $consumerProduct->address,
            consumerProduct          : $consumerProduct,
            marketingCampaignConsumer: $marketingCampaignConsumer,
        );
    }

    public static function mock()
    {
        $marketingCampaignConsumer = MarketingCampaignConsumer::factory()->make();

        $marketingCampaignConsumer->setRelation(MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN,
            MarketingCampaign::factory()->make()
        );

        $faker = Factory::create();

        $consumer = new Consumer([
            Consumer::FIELD_REFERENCE => $faker->uuid,
            Consumer::FIELD_EMAIL => $faker->email,
            Consumer::FIELD_PHONE => $faker->phoneNumber,
            Consumer::FIELD_FORMATTED_PHONE => $faker->phoneNumber,
            Consumer::FIELD_FIRST_NAME => $faker->firstName,
            Consumer::FIELD_LAST_NAME => $faker->lastName,
            Consumer::FIELD_STATUS => $faker->randomElement(Consumer::STATUSES),
            Consumer::FIELD_CLASSIFICATION => $faker->randomElement(Consumer::CLASSIFICATIONS),
            Consumer::FIELD_STATUS_REASON => $faker->words(3, true),
            Consumer::FIELD_MAX_CONTACT_REQUESTS => $faker->numberBetween(1,4),
        ]);

        $consumerProduct = new ConsumerProduct([
            ConsumerProduct::FIELD_GOOD_TO_SELL => $faker->boolean,
            ConsumerProduct::FIELD_STATUS => $faker->randomElement(ConsumerProduct::STATUSES),
            ConsumerProduct::FIELD_CONTACT_REQUESTS => $faker->numberBetween(1,4),
        ]);

        $consumerProductData = ConsumerProductData::factory()->make();

        $consumerProduct->setRelation(
            ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
            $consumerProductData
        );

        $industryService = new IndustryService([
            IndustryService::FIELD_NAME => $faker->name,
            IndustryService::FIELD_SLUG => $faker->slug,
            IndustryService::FIELD_SHOW_ON_WEBSITE => $faker->boolean,
        ]);

        $consumerProduct->setRelation(ConsumerProduct::RELATION_INDUSTRY_SERVICE, $industryService);

        $industry = Industry::factory()->make();

        $industryService->setRelation(IndustryService::RELATION_INDUSTRY, $industry);

        $address = new Address([
            Address::FIELD_ADDRESS_1 => $faker->streetAddress(),
            Address::FIELD_ADDRESS_2 => $faker->streetAddress(),
            Address::FIELD_CITY      => $faker->city(),
            Address::FIELD_STATE     => $faker->word(),
            Address::FIELD_ZIP_CODE  => $faker->postcode,
            Address::FIELD_COUNTRY   => 'US',
            Address::FIELD_COUNTY => $faker->word(),
            Address::FIELD_LATITUDE  => $faker->latitude(),
            Address::FIELD_LONGITUDE => $faker->longitude(),
            Address::FIELD_UTC       => '-'.$faker->numberBetween(4, 10),
            Address::FIELD_PLACE_ID => $faker->uuid()
        ]);

        $consumerProduct->setRelation(ConsumerProduct::RELATION_ADDRESS, $address);

        return new self(
            consumer                 : $consumer,
            consumerAddress          : $address,
            consumerProduct          : $consumerProduct,
            marketingCampaignConsumer: $marketingCampaignConsumer,
        );
    }

    public static function getShortcodeSets(): Collection
    {
        return collect([
            self::CONSUMER                    => new ConsumerShortcodeSet(),
            self::CONSUMER_PRODUCT            => new ConsumerProductShortcodeSet(),
            self::MARKETING_CAMPAIGN_CONSUMER => new MarketingCampaignConsumerShortcodeSet(),
            self::CONSUMER_ADDRESS            => new AddressShortcodeSet(),
        ]);
    }

    public function getMappedData(): Collection
    {
        return collect([
            self::CONSUMER                    => $this->consumer,
            self::CONSUMER_PRODUCT            => $this->consumerProduct,
            self::MARKETING_CAMPAIGN_CONSUMER => $this->marketingCampaignConsumer,
            self::CONSUMER_ADDRESS            => $this->consumerAddress,
        ]);
    }
}
