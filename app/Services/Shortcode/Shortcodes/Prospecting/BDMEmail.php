<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class BDMEmail implements GenericShortcodeContract
{

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Email';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'email';
    }

    /**
     * @param User $input
     *
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return $input->email;
    }
}
