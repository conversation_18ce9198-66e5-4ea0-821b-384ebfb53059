<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\User;

class BDMNameEmailLink extends BDMEmailLink
{
    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Name with Email Link';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'name-email-link';
    }

    /**
     * @param User $input
     *
     * @return mixed
     */
    public function getValue(mixed $input): string
    {
        return $this->makeEmailLink($input->email, $input->name);
    }
}
