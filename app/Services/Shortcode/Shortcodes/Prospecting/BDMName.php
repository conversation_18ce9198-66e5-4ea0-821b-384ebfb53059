<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\User;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class BDMName implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Name';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'name';
    }

    /**
     * @param User $input
     *
     * @return mixed
     */
    public function getValue(mixed $input): string
    {
        return $input->name;
    }
}
