<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectEmailService;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class DomainNameShortcode implements GenericShortcodeContract
{

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Domain Name';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'domain_name';
    }

    /**
     * @param NewBuyerProspect $input
     *
     * @return mixed
     */
    public function getValue(mixed $input): string
    {
        /** @var ProspectEmailService $service */
        $service = app(ProspectEmailService::class);
        [$siteName, $url, $calculatorUrl, $industry] = $service->getWebsiteDetails($input);

        return $siteName;
    }
}
