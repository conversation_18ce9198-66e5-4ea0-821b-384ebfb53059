<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectEmailService;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class IndustryShortcode implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Industry';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'industry';
    }

    /**
     * @param NewBuyerProspect $input
     *
     * @return mixed
     */
    public function getValue(mixed $input): mixed
    {
        /** @var ProspectEmailService $service */
        $service = app(ProspectEmailService::class);
        [$siteName, $url, $calculatorUrl, $industry] = $service->getWebsiteDetails($input);

        return $industry;
    }
}
