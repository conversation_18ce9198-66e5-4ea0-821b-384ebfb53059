<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\User;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class BDMPhone implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Phone';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'phone';
    }

    /**
     * @param User $input
     *
     * @return mixed
     */
    public function getValue(mixed $input): string
    {
        return $input->phones->first()?->phone ?? '+****************';
    }
}
