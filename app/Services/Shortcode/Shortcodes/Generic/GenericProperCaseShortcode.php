<?php

namespace App\Services\Shortcode\Shortcodes\Generic;

use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

abstract class GenericProperCaseShortcode implements GenericShortcodeContract
{
    /**
     * @param string[]|string $input
     *
     * @return mixed
     */
    protected function properCase(mixed $input): string
    {
        return str(match(gettype($input)) {
            'string' => $input,
            'array'  => implode(' ', array_reduce($input, fn($out, $v) => trim($v) ? [...$out, trim($v)] : $out, [])),
            default  => '',
        })->headline();
    }
}