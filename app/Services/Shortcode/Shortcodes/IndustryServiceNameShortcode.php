<?php

namespace App\Services\Shortcode\Shortcodes;

use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use InvalidArgumentException;

class IndustryServiceNameShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Industry Service Name";
    }

    public function getKey(): string
    {
        return "industry_service_name";
    }

    /**
     * @param IndustryService|ConsumerProduct $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        $class = $input::class;
        return match ($class) {
            IndustryService::class => $input->name,
            ConsumerProduct::class => $input->industryService->name,
            default => throw new InvalidArgumentException("Shortcode getValue expected IndustryService or ConsumerProduct, received: $class")
        };

    }
}