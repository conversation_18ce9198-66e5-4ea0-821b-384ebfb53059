<?php

namespace App\Services\Shortcode\Shortcodes\CompanyUser;

use App\Models\Odin\CompanyUser;
use App\Services\Shortcode\Shortcodes\Generic\GenericProperCaseShortcode;

class CompanyUserNameShortcode extends GenericProperCaseShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'name';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return "Name";
    }

    /**
     * @param CompanyUser $input
     * @return mixed
     */
    public function getValue(mixed $input): mixed
    {
        return $this->properCase([
            $input->first_name,
            $input->last_name,
        ]);
    }
}