<?php

namespace App\Services\Shortcode\Shortcodes;

use App\Models\Odin\Address;

class AddressStreetShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Street Address";
    }

    public function getKey(): string
    {
        return "address_street";
    }

    /**
     * @param Address $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return $input->getFullStreetAddress();
    }
}
