<?php

namespace App\Services\Shortcode\Shortcodes\MissedProducts;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\MissedProducts\MissedProductService;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Facades\View;

class MissedProductBlockActiveCompanyShortcode implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "product-block-active-company";
    }

    /**
     * @param array{
     *      company_user: CompanyUser,
     *      company: Company,
     *      config: OpportunityNotificationConfig
     *  }$input
     */
    public function getValue(mixed $input): string
    {
        return $this->buildMarkdownProductsView(
            $input['company'],
            $input['config'],
        );
    }

    public function getLabel(): string
    {
        return "Missed Products Block - Active Company";
    }

    /**
     * @param Company $company
     * @param OpportunityNotificationConfig $opportunityNotificationConfig
     * @return mixed
     */
    protected function buildMarkdownProductsView(Company $company, OpportunityNotificationConfig $opportunityNotificationConfig): string
    {
        /** @var MissedProductService $missedProductService */
        $missedProductService = app(MissedProductService::class);
        $missedProductData = $missedProductService->getExistingCompanyMissedProductsSummary($company, $opportunityNotificationConfig);

        if (empty($missedProductData)) return '';

        return View::make('emails.opportunitynotifications.display-leads-missed-reasons', [
            'missedProductData'   => $missedProductData,
        ])->render();
    }
}