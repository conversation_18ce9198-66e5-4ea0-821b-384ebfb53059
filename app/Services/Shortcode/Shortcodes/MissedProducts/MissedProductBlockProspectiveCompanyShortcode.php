<?php

namespace App\Services\Shortcode\Shortcodes\MissedProducts;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\MissedProducts\MissedProductService;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Facades\View;

class MissedProductBlockProspectiveCompanyShortcode implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "product-block-prospective-company";
    }

    /**
     * @param array{
     *      company_user: CompanyUser,
     *      company: Company,
     *      config: OpportunityNotificationConfig
     *  }$input
     */
    public function getValue(mixed $input): string
    {
        return $this->buildMarkdownProductsView(
            $input['company'],
            $input['company_user'],
            $input['config'],
        );
    }

    public function getLabel(): string
    {
        return "Missed Products Block - Prospective Company";
    }


    /**
     * @param Company $company
     * @param CompanyUser $user
     * @param OpportunityNotificationConfig $opportunityNotificationConfig
     * @return mixed
     */
    protected function buildMarkdownProductsView(Company $company, CompanyUser $user, OpportunityNotificationConfig $opportunityNotificationConfig): string
    {
        /** @var MissedProductService $missedProductService */
        $missedProductService = app(MissedProductService::class);
        $missedProductData = $missedProductService->getNewCompanyMissedProductsPreview($company);

        if (empty($missedProductData))
            return '';

        return View::make('emails.opportunitynotifications.display-leads-single-detail', [
            'missedProductCategories'   => $missedProductData,
            'totalLeadsAvailable'       => collect($missedProductData)->sum('total_available'),
            "bookDemoUrl"               => $this->getBookDemoUrl($company, $user, $opportunityNotificationConfig)
        ])->render();
    }

    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     * @param OpportunityNotificationConfig $opportunityConfig
     * @return string
     */
    protected function getBookDemoUrl(Company $company, CompanyUser $companyUser, OpportunityNotificationConfig $opportunityConfig): string
    {
        $baseUrl = url()->query(config('app.fixr_domain') . '/company-registration', [
            'contact_name'    => $companyUser->completeName(),
            'email'           => $companyUser->email,
            'phone'           => $companyUser->cell_phone ?? $companyUser->office_phone,
            'company_name'    => $company->name,
            'company_website' => $company->website,
            'utm_source'      => 'missed_products',
            'utm_medium'      => 'email',
            'utm_campaign'    => $opportunityConfig->uuid,
        ]);

        return "$baseUrl#book-demo";
    }
}