<?php

namespace App\Services\Shortcode\Shortcodes\MissedProducts;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Encryption\Encrypter;
use <PERSON><PERSON>\UrlSigner\Laravel\Facades\UrlSigner;

class MissedProductUnsubscribeLinkShortcode implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "unsubscribe-link";
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return "Unsubscribe Link";
    }

    /**
     * @param array{
     *      company_user: CompanyUser,
     *      company: Company,
     *      config: OpportunityNotificationConfig
     *  }$input
     */
    public function getValue(mixed $input): string
    {
        $url = $this->getUnsubscribeUrl($input['company_user']);

        return "<a href='$url'>Unsubscribe</a>";
    }

    /**
     * Returns the unsubscribed url.
     *
     * @param CompanyUser $companyUser
     * @return string
     */
    protected function getUnsubscribeUrl(CompanyUser $companyUser): string
    {
        $encrypter = new Encrypter(
            config('app.url_signer_signature_key'),
            config('app.cipher'),
        );

        $url = sprintf(
            '%s/claim-leads/unsubscribe?%s',
            config('mail.external_links.fixr'),
            http_build_query([
                'cr' => $encrypter->encrypt($companyUser->reference),
            ])
        );

        return UrlSigner::sign($url, now()->addCenturies(1));
    }
}