<?php

namespace App\Services\Shortcode\Shortcodes;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class ModelFieldShortcode implements GenericShortcodeContract
{
    public function __construct(
        protected string $field,
    )
    {
    }

    public function getLabel(): string
    {
        return Str::headline($this->field);
    }

    public function getKey(): string
    {
        return $this->field;
    }

    /**
     * @param Model $input
     * @return mixed
     */
    public function getValue(mixed $input): mixed
    {
        return $input->{$this->field};
    }
}