<?php

namespace App\Services\Shortcode\Shortcodes\Invoice;

use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Services\Billing\InvoicePdfService\InvoicePdfDataBuilderService;
use App\Services\Billing\InvoicePdfService\InvoicePdfTemplateService;
use App\Services\Billing\InvoiceTemplateRenderer\InvoiceFoundationComponent;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Arr;
use phpDocumentor\GraphViz\Exception;

class InvoicePaymentInfoShortcode implements GenericShortcodeContract
{

    protected InvoicePdfTemplateService    $invoicePdfTemplateService;
    protected InvoicePdfDataBuilderService $invoicePdfDataBuilderService;

    public function __construct()
    {
        $this->invoicePdfTemplateService = app()->make(InvoicePdfTemplateService::class);
        $this->invoicePdfDataBuilderService = app()->make(InvoicePdfDataBuilderService::class);
    }

    public function getLabel(): string
    {
        return "Payment Info";
    }

    public function getKey(): string
    {
        return "payment_info";
    }

    /**
     * @param Invoice $input
     * @return string
     * @throws Exception
     */
    public function getValue(mixed $input): string
    {
        $billingProfile = $input->{Invoice::RELATION_BILLING_PROFILE};

        $func = match ($billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD}) {
            PaymentMethodServices::MANUAL => function () use ($input) {
                $formattedDueDate = (new InvoiceDueDateShortcode())->getValue($input);

                $invoiceTemplate = $invoiceTemplate ?? $this->invoicePdfTemplateService->getInvoiceTemplate($input);
                /** @var InvoiceFoundationComponent $componentsProps */
                $componentsProps = $this->invoicePdfTemplateService->getComponentsProps($invoiceTemplate);
                $data = $this->invoicePdfDataBuilderService->getBankAccountInfo(
                    invoiceFoundationComponent: $componentsProps
                );

                $routingNumber = Arr::get($data, 'routing_number');
                $accountNumber = Arr::get($data, 'account_number');
                $accountName = Arr::get($data, 'account_name');

                return "<p>Please arrange payment prior to {$formattedDueDate} via the ACH details below:</p>
                        <ul>
                            <li><p>Account name: $accountName</p></li>
                            <li><p>Account number: $accountNumber</p></li>
                            <li><p>Routing number: $routingNumber</p></li>
                        </ul>";
            },
            PaymentMethodServices::STRIPE => function () use ($input) {
                $formattedIssueDate = (new InvoiceIssuedDateShortcode())->getValue($input);

                return "<p>
                  This invoice will be automatically charged to your nominated
                  credit card within your Fixr Dashboard and will occur on or
                  after {$formattedIssueDate}.
                </p>
                <p>
                  You can pay this invoice manually before then, in your Fixr
                  Dashboard - Billing and Payments.
                </p>";
            },
            default                       => throw new Exception('Payment Method not found.'),
        };

        return $func();
    }

}
