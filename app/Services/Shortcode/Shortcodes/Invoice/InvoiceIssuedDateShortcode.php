<?php

namespace App\Services\Shortcode\Shortcodes\Invoice;

use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class InvoiceIssuedDateShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Issued Date";
    }

    public function getKey(): string
    {
        return "issued_date";
    }

    /**
     * @param Invoice $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return CarbonHelper::parse($input->{Invoice::FIELD_ISSUE_AT})->toFormat(
            format: CarbonHelper::FORMAT_DATE
        );
    }
}
