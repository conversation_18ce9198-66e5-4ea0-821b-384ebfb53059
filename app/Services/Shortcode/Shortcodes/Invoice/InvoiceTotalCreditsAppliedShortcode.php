<?php

namespace App\Services\Shortcode\Shortcodes\Invoice;

use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Number;

class InvoiceTotalCreditsAppliedShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Total Credits Applied";
    }

    public function getKey(): string
    {
        return "total_credits_applied";
    }

    /**
     * @param Invoice $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return Number::currency($input->getTotalCreditsApplied() / 100);
    }
}
