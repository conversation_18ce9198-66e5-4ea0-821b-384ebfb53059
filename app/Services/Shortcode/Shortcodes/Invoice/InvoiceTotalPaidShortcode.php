<?php

namespace App\Services\Shortcode\Shortcodes\Invoice;

use App\Models\Billing\Invoice;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Number;

class InvoiceTotalPaidShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Total Paid";
    }

    public function getKey(): string
    {
        return "total_paid";
    }

    /**
     * @param Invoice $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return Number::currency($input->getTotalPaid() / 100);
    }
}
