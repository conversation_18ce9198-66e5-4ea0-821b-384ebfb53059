<?php

namespace App\Services\Shortcode\Shortcodes\Invoice;

use App\Models\Billing\Invoice;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Number;

class InvoiceTotalIssuedShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Total Issued";
    }

    public function getKey(): string
    {
        return "total_issued";
    }

    /**
     * @param Invoice $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return Number::currency($input->getTotalIssuable() / 100);
    }
}
