<?php

namespace App\Services\Shortcode\Shortcodes;

use App\Models\Odin\Address;

class AddressFullShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Full Address";
    }

    public function getKey(): string
    {
        return "address_full";
    }

    /**
     * @param Address $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return $input->getFullAddress();
    }
}