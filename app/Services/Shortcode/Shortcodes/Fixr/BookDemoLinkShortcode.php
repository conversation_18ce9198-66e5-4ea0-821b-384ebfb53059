<?php

namespace App\Services\Shortcode\Shortcodes\Fixr;

use App\Enums\OpportunityNotifications\OpportunityNotificationConfigType;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class BookDemoLinkShortcode implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'book-a-demo';
    }

    /**
     * @param CompanyUser $input
     *
     * @inheritDoc
     */
    public function getValue(mixed $input): string
    {
        $company = $input->company;
        $demoUrl = $this->getBookDemoUrl($company, $input, OpportunityNotificationConfig::getBDMQueueConfig());

        return $input
            ? $this->makeButtonHTML($demoUrl)
            : '';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return "Book a Demo Link";
    }

    /**
     * @param string $url
     * @param string $title
     * @return string
     */
    protected function makeButtonHTML(string $url, string $title = "Book A Demo"): string
    {
        return '
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="" width="100%">
                <tbody>
                <tr>
                    <td align="center" vertical-align="middle" style="font-size:0px;padding:10px 25px;word-break:break-word;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:separate;width:210px;line-height:100%;">
                            <tbody>
                            <tr>
                                <td align="center" bgcolor="#00A3FF" role="presentation" style="border:none;border-radius:3px;cursor:auto;mso-padding-alt:10px 25px;background:#00A3FF;" valign="middle">
                                    <a
                                            href="'.$url.'"
                                            style="display:inline-block;width:160px;background:#00A3FF;color:#ffffff;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;font-weight:normal;line-height:120%;margin:0;text-decoration:none;text-transform:none;padding:10px 25px;mso-padding-alt:0px;border-radius:3px;"
                                            target="_blank"
                                    >
                                        '.$title.'
                                    </a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>';
    }

    /**
     * @param Company $company
     * @param CompanyUser $companyUser
     * @param OpportunityNotificationConfig $opportunityConfig
     * @return string
     */
    protected function getBookDemoUrl(Company $company, CompanyUser $companyUser, OpportunityNotificationConfig $opportunityConfig): string
    {
        $baseUrl = url()->query(config('app.fixr_domain') . '/company-registration', [
            'contact_name'    => $companyUser->completeName(),
            'email'           => $companyUser->email,
            'phone'           => $companyUser->cell_phone ?? $companyUser->office_phone,
            'company_name'    => $company->name,
            'company_website' => $company->website,
            'utm_source'      => 'missed_products',
            'utm_medium'      => 'email',
            'utm_campaign'    => $opportunityConfig?->uuid ?? '',
            'meeting_url'     => $this->getMeetingURL($company, $opportunityConfig),
        ]);

        return "$baseUrl#book-demo";
    }

    /**
     * If this has come from the BDM queue config, feed the BDM's calendly URL to Fixr
     * Fixr will default back to the shared account manager Calendly if no URL, or it is invalid
     *
     * @param Company $company
     * @param OpportunityNotificationConfig $config
     * @return string|null
     */
    protected function getMeetingURL(Company $company, OpportunityNotificationConfig $config): ?string
    {
        if ($config->type === OpportunityNotificationConfigType::BDM_COMPANIES) {
            $url = $company->businessDevelopmentManager?->meeting_url ?? '';
            if (preg_match("/^https?:\/\/calendly\.com\/\w+/i", $url))
                return $url;
        }

        return null;
    }
}