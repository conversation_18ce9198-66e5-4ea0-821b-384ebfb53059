<?php

namespace App\Services\Shortcode\Shortcodes\Fixr;

use App\Models\Odin\CompanyUser;
use App\Services\Dashboard\DashboardLoginTokenService;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class DashboardLinkShortcode implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'dashboard-link';
    }

    /**
     * @param CompanyUser $input
     * @inheritDoc
     */
    public function getValue(mixed $input): string
    {
        return $input
            ? $this->makeButtonHTML($this->buildDashboardURL($input), 'My Dashboard')
            : '';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return "Fixr Dashboard Link";
    }

    /**
     * @param string $url
     * @param string $title
     * @return string
     */
    private function makeButtonHTML(string $url, string $title = "Go To Dashboard"): string
    {
        return '
            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="" width="100%">
                <tbody>
                <tr>
                    <td align="center" vertical-align="middle" style="font-size:0px;padding:10px 25px;word-break:break-word;">
                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:separate;width:210px;line-height:100%;">
                            <tbody>
                            <tr>
                                <td align="center" bgcolor="#00A3FF" role="presentation" style="border:none;border-radius:3px;cursor:auto;mso-padding-alt:10px 25px;background:#00A3FF;" valign="middle">
                                    <a
                                            href="'.$url.'"
                                            style="display:inline-block;width:160px;background:#00A3FF;color:#ffffff;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;font-weight:normal;line-height:120%;margin:0;text-decoration:none;text-transform:none;padding:10px 25px;mso-padding-alt:0px;border-radius:3px;"
                                            target="_blank"
                                    >
                                        '.$title.'
                                    </a>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>';
    }

    /**
     * Users who can log in to dashboard get a token link
     * Other users get the standard login page
     * @param CompanyUser $user
     * @return string
     */
    private function buildDashboardURL(CompanyUser $user): string
    {
        $baseUrl = config('app.dashboard.fixr_url');

        if ($user->can_log_in && $user->password) {
            $token = $this->getLoginToken($user);

            return "$baseUrl/login-with-token?token=$token";
        }

        return "$baseUrl/login";
    }

    /**
     * Get a working-day login token for the user
     * @param CompanyUser $user
     * @return string
     */
    private function getLoginToken(CompanyUser $user): string
    {
        /** @var DashboardLoginTokenService $tokenService */
        $tokenService = app(DashboardLoginTokenService::class);
        $expiry = 8 * 60;

        return $tokenService->generateToken($user, null, $expiry);
    }
}