<?php

namespace App\Services\Shortcode\Shortcodes;

use App\Models\MarketingCampaignConsumer;
use App\Services\MarketingCampaign\MarketingCampaignConsumerService;
use Illuminate\Contracts\Container\BindingResolutionException;

class MarketingCampaignConsumerCallbackUrlShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Callback Url";
    }

    public function getKey(): string
    {
        return "callback_url";
    }

    /**
     * @param MarketingCampaignConsumer $input
     * @return string
     * @throws BindingResolutionException
     */
    public function getValue(mixed $input): string
    {
        /** @var MarketingCampaignConsumerService $marketingCampaignConsumerService */
        $marketingCampaignConsumerService = app()->make(MarketingCampaignConsumerService::class);

        return $marketingCampaignConsumerService->getMarketingCampaignConsumerCallbackUrl($input);
    }
}
