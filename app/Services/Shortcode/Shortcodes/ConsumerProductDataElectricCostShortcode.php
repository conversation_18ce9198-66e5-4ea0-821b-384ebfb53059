<?php

namespace App\Services\Shortcode\Shortcodes;

use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use Illuminate\Support\Arr;
use InvalidArgumentException;

class ConsumerProductDataElectricCostShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Electric Cost";
    }

    public function getKey(): string
    {
        return "electric_cost";
    }

    /**
     * @param ConsumerProductData|ConsumerProduct $input
     * @return string|null
     */
    public function getValue(mixed $input): ?string
    {
        $class = $input::class;

        return match ($class) {
            ConsumerProductData::class => Arr::get($input->payload, SolarConfigurableFields::ELECTRIC_COST->value),
            ConsumerProduct::class => Arr::get($input->consumerProductData->payload, SolarConfigurableFields::ELECTRIC_COST->value),
            default => throw new InvalidArgumentException("getValue expected ConsumerProductData or ConsumerProduct models, $class recieved")
        };
    }
}