<?php

namespace App\Services\Shortcode\Shortcodes;

use App\Models\MarketingCampaignConsumer;
use App\Services\MarketingCampaign\MarketingCampaignConsumerService;
use Illuminate\Contracts\Container\BindingResolutionException;

class DynamicMarketingCampaignConsumerCallbackUrlShortcode extends MarketingCampaignConsumerCallbackUrlShortcode
{
    public function getLabel(): string
    {
        return "Dynamic Callback Url";
    }

    public function getKey(): string
    {
        return "dynamic_callback_url";
    }
}
