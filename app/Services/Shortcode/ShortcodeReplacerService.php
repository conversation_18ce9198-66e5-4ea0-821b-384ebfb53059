<?php

namespace App\Services\Shortcode;

use App\Exceptions\Shortcode\ShortcodeReplacementFailed;
use Exception;

class ShortcodeReplacerService
{
    protected string $openChar  = '{';
    protected string $closeChar = '}';

    public function __construct(
    )
    {
    }

    /**
     * @param string $rawText
     * @param array $shortcodeMap
     * @return string
     * @throws Exception
     */
    public function process(string $rawText, array $shortcodeMap): string
    {
        $result = $rawText;

        //serialises any models along with their relations
        $shortcodeMap    = collect($shortcodeMap)->toArray();

        $data = $this->parseToArray($shortcodeMap);

        foreach ($this->getAllShortcodes($rawText) as $shortcode) {
            [$value, $fieldFound] = getValueCaseInsensitive(
                property: $shortcode,
                context : $data,
            );

            if ($value !== null) {
                foreach ($this->getPossibleRegexPatterns($shortcode) as $pattern) {
                    $escapedValue = strtr($value, ['\\' => '\\\\', '$' => '\\$']);
                    $result = preg_replace($pattern, $escapedValue, $result);
                }
            }
        }

        $this->validate($rawText, $shortcodeMap);

        return $result;
    }

    /**
     * @param array $shortcodeMap
     * @return array
     */
    protected function parseToArray(array $shortcodeMap): array
    {
        return $this->recursivelyMap(
            newData: [],
            value  : $shortcodeMap,
        );
    }

    /**
     * Cycles through all shortcodes given and flattens any nesting out with dot notion.
     * Shortcode array eg ['consumer' => ['first_name' => 'John']] = ['consumer.first_name => 'John']
     *
     * @param array $newData
     * @param mixed $value
     * @param string $key
     * @return array
     */
    protected function recursivelyMap(array $newData, mixed $value, string $key = ''): array
    {
        if (gettype($value) === 'array') {
            foreach ($value as $newKey => $newValue) {
                $newData = $this->recursivelyMap(
                    newData: $newData,
                    value  : $newValue,
                    key    : collect([$key, $newKey])->filter()->join('.')
                );
            }
        } else {
            $newData[$key] = $value;
        }

        return $newData;
    }

    /**
     * @param string $rawText
     * @param array $shortcodeMap
     * @return ShortcodeReplacerService
     * @throws Exception
     */
    public function validate(string $rawText, array $shortcodeMap): self
    {
        $shortcodes = $this->getAllShortcodes($rawText);

        $data = $this->parseToArray($shortcodeMap);

        $unmatched = array_filter($shortcodes, function ($shortcode) use ($data) {
            [$value, $fieldFound] = getValueCaseInsensitive(
                property: $shortcode,
                context : $data,
            );

            return $value === null;
        });

        if (!empty($unmatched)) {
            throw new ShortcodeReplacementFailed('Not all shortcodes matched. Unmatched shortcodes: ' . json_encode($unmatched));
        }

        return $this;
    }

    /**
     * Need to check both urlencoded and normal patterns to make replacement more reliable
     *
     * @param string $search
     * @return array
     */
    protected function getPossibleRegexPatterns(string $search): array
    {
        return [
            '/(' . $this->openChar . $search . $this->closeChar . ')/',
            '/(' . urlencode($this->openChar) . $search . urlencode($this->closeChar) . ')/',
        ];
    }

    /**
     * @param string $rawText
     * @return array
     */
    public function getAllShortcodes(string $rawText): array
    {
        $patterns = $this->getPossibleRegexPatterns('([a-zA-Z0-9_.-]+)');

        $shortcodes = collect();

        foreach ($patterns as $pattern) {
            preg_match_all($pattern, $rawText, $matches);

            $shortcodes = $shortcodes->merge(array_unique($matches[2]));
        }
        return $shortcodes->unique()->values()->toArray();
    }
}
