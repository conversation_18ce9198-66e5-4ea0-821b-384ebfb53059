<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\EmailRecipient\EmailRecipientEmailShortcode;
use App\Services\Shortcode\Shortcodes\EmailRecipient\EmailRecipientNameShortcode;
use Illuminate\Support\Collection;

class EmailRecipientShortcodeSet extends ShortcodeSet
{
    public function listShortcodes(): Collection
    {
        return collect([
            new EmailRecipientNameShortcode(),
            new EmailRecipientEmailShortcode(),
        ]);
    }
}
