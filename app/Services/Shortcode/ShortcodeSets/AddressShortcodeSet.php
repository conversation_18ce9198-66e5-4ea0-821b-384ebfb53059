<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Models\Odin\Address;
use App\Services\Shortcode\Shortcodes\AddressFullShortcode;
use App\Services\Shortcode\Shortcodes\AddressStreetShortcode;
use Illuminate\Support\Collection;

class AddressShortcodeSet extends ShortcodeSet
{
    protected array $modelFields = [
        Address::FIELD_ZIP_CODE,
        Address::FIELD_COUNTY,
        Address::FIELD_STATE,
        Address::FIELD_CITY,
    ];

    /**
     * @inheritDoc
     */
    public function listShortcodes(): Collection
    {
        return collect([
            new AddressFullShortcode(),
            new AddressStreetShortcode(),
        ]);
    }
}
