<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\Prospecting\CalculatorUrlShortcode;
use App\Services\Shortcode\Shortcodes\Prospecting\DecisionMakerFirstNameShortcode;
use App\Services\Shortcode\Shortcodes\Prospecting\DomainNameShortcode;
use App\Services\Shortcode\Shortcodes\Prospecting\IndustryShortcode;
use App\Services\Shortcode\Shortcodes\Prospecting\UrlShortcode;
use Illuminate\Support\Collection;

class ProspectShortcodeSet extends ShortcodeSet
{
    public function listShortcodes(): Collection
    {
       return collect([
           new DecisionMakerFirstNameShortcode(),
           new DomainNameShortcode(),
           new CalculatorUrlShortcode(),
           new IndustryShortcode(),
           new UrlShortcode()
       ]);
    }
}
