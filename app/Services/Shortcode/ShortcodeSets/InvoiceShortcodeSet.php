<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Models\Billing\Invoice;
use App\Services\Shortcode\Shortcodes\Invoice\InvoiceDueDateShortcode;
use App\Services\Shortcode\Shortcodes\Invoice\InvoiceIssuedDateShortcode;
use App\Services\Shortcode\Shortcodes\Invoice\InvoicePaymentInfoShortcode;
use App\Services\Shortcode\Shortcodes\Invoice\InvoiceTotalCreditsAppliedShortcode;
use App\Services\Shortcode\Shortcodes\Invoice\InvoiceTotalIssuedShortcode;
use App\Services\Shortcode\Shortcodes\Invoice\InvoiceTotalPaidShortcode;
use App\Services\Shortcode\Shortcodes\Invoice\InvoiceTotalShortcode;
use Illuminate\Support\Collection;

class InvoiceShortcodeSet extends ShortcodeSet
{
    protected array $modelFields = [
        Invoice::FIELD_ID
    ];

    public function listShortcodes(): Collection
    {
        return collect([
            new InvoiceIssuedDateShortcode(),
            new InvoiceDueDateShortcode(),
            new InvoiceTotalCreditsAppliedShortcode(),
            new InvoiceTotalIssuedShortcode(),
            new InvoiceTotalPaidShortcode(),
            new InvoiceTotalShortcode(),
            new InvoicePaymentInfoShortcode(),
        ]);
    }
}
