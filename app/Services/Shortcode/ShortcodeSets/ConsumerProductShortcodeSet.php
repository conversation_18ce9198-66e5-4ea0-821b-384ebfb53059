<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\ConsumerProductDataElectricCostShortcode;
use App\Services\Shortcode\Shortcodes\IndustryNameShortcode;
use App\Services\Shortcode\Shortcodes\IndustryServiceNameShortcode;
use Illuminate\Support\Collection;

class ConsumerProductShortcodeSet extends ShortcodeSet
{
    /**
     * @inheritDoc
     */
    public function listShortcodes(): Collection
    {
        return collect([
            new IndustryServiceNameShortcode(),
            new IndustryNameShortcode(),
            new ConsumerProductDataElectricCostShortcode(),
        ]);
    }
}
