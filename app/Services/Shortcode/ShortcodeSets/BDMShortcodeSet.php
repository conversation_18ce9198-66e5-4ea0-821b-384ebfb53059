<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\Prospecting\BDMEmail;
use App\Services\Shortcode\Shortcodes\Prospecting\BDMEmailLink;
use App\Services\Shortcode\Shortcodes\Prospecting\BDMName;
use App\Services\Shortcode\Shortcodes\Prospecting\BDMNameEmailLink;
use App\Services\Shortcode\Shortcodes\Prospecting\BDMPhone;
use Illuminate\Support\Collection;

class BDMShortcodeSet extends ShortcodeSet
{
    const string DEFAULT_KEY = 'bdm';

    //todo Change the naming to a more generic a User shortcode set
    //this has nothing to do with a BDM, its all generic user data
    public function listShortcodes(): Collection
    {
        return collect([
            new BDMEmail(),
            new BDMName(),
            new BDMPhone(),
            new BDMEmailLink(),
            new BDMNameEmailLink(),
        ]);
    }
}
