<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\DynamicMarketingCampaignConsumerCallbackUrlShortcode;
use App\Services\Shortcode\Shortcodes\MarketingCampaignConsumerCallbackUrlShortcode;
use Illuminate\Support\Collection;

class MarketingCampaignConsumerShortcodeSet extends ShortcodeSet
{
    /**
     * @inheritDoc
     */
    public function listShortcodes(): Collection
    {
        return collect([
            new MarketingCampaignConsumerCallbackUrlShortcode(),
            new DynamicMarketingCampaignConsumerCallbackUrlShortcode(),
        ]);
    }
}
