<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\MissedProducts\MissedProductBlockActiveCompanyShortcode;
use App\Services\Shortcode\Shortcodes\MissedProducts\MissedProductBlockProspectiveCompanyShortcode;
use App\Services\Shortcode\Shortcodes\MissedProducts\MissedProductUnsubscribeLinkShortcode;
use Illuminate\Support\Collection;

class MissedProductsShortcodeSet extends ShortcodeSet
{
    const string DEFAULT_KEY = 'missed-products';

    /**
     * @inheritdoc
     */
    public function listShortcodes(): Collection
    {
        return collect([
            new MissedProductBlockActiveCompanyShortcode(),
            new MissedProductBlockProspectiveCompanyShortcode(),
            new MissedProductUnsubscribeLinkShortcode(),
        ]);
    }
}