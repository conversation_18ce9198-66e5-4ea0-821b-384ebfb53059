<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Collection;

interface ShortcodeSetContract
{
    /**
     * Returns a collection of shortcodes that have a shared source of data for replacement
     *
     * @see ConsumerShortcodeSet for example
     *
     * @return Collection<GenericShortcodeContract>
     */
    public function listShortcodes(): Collection;

    /**
     * @param mixed $value Takes the shared object or class required
     *  to implement all shortcodes in the bundle e.g a consumer modal
     *
     * @return array with all shortcode keys as keys and values to replace the keys with as values
     */
    public function compile(mixed $value): array;

}
