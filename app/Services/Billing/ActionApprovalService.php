<?php

namespace App\Services\Billing;

use App\Aggregates\ActionApprovalAggregateRoot;
use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovalStatus;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\ActionApproval;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\Billing\ActionApprovalRepository;
use App\Services\Billing\ReviewableAction\ReviewableAction;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Throwable;

class ActionApprovalService
{
    public function __construct(
        protected ActionApprovalRepository $actionApprovalRepository
    )
    {

    }

    /**
     * TODO - improve
     * @param ActionApproval $invoiceActionRequest
     * @return string|int
     */
    protected function getReference(ActionApproval $invoiceActionRequest): string|int
    {
        return match ($invoiceActionRequest->approvable_type) {
            Invoice::class        => Invoice::query()->where(Invoice::FIELD_ID, $invoiceActionRequest->approvable_id)->first()->{Invoice::FIELD_UUID},
            Company::class        => Company::query()->where(Company::FIELD_ID, $invoiceActionRequest->approvable_id)->first()->{Company::FIELD_REFERENCE},
            BillingProfile::class => $invoiceActionRequest->approvable_id,
            default               => Str::uuid()->toString()
        };
    }

    /**
     * @param ActionApproval $invoiceActionRequest
     * @param int $reviewedByUserId
     * @param ApprovalStatus $status
     * @param string|null $reason
     * @return void
     * @throws Exception
     */
    public function reviewAction(
        ActionApproval $invoiceActionRequest,
        int $reviewedByUserId,
        ApprovalStatus $status,
        ?string $reason = null,
    ): void
    {
        if ($invoiceActionRequest->status !== ApprovalStatus::PENDING) {
            throw new Exception('This request has already been reviewed');
        }

        $reference = $this->getReference($invoiceActionRequest);

        ActionApprovalAggregateRoot::retrieve($reference)
            ->reviewAction(
                actionApprovalUuid: $invoiceActionRequest->{ActionApproval::FIELD_UUID},
                modelId           : $invoiceActionRequest->approvable_id,
                modelClass        : $invoiceActionRequest->approvable_type,
                authorId          : $reviewedByUserId,
                status            : $status->value,
                reason            : $reason,
            )->persist();
    }

    /**
     * @param ApprovableActionType $actionType
     * @param ApprovalStatus $status
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     */
    public function executeAction(
        ApprovableActionType $actionType,
        ApprovalStatus $status,
        array $arguments = []
    ): void
    {
        /** @var ReviewableAction $action */
        $action = app()->make($actionType->getActionClass());

        try {
            switch ($status) {
                case ApprovalStatus::APPROVED;
                    $action->onApproval($arguments);
                    break;
                case ApprovalStatus::REJECTED;
                    $action->onRefusal($arguments);
                    break;
                case ApprovalStatus::PENDING:
                    return;
            }
        } catch (Throwable $exception) {
            BillingLogService::logException(
                exception: $exception,
                namespace: 'action_approval_service',
                context  : [
                    "actionType" => $actionType,
                    "status"     => $status,
                    "arguments"  => $arguments,
                ]
            );
            $action->onError($exception);
        }
    }

    /**
     * @param string $referenceId
     * @param int $modelId
     * @param ApprovableActionRelationTypes $type
     * @param ApprovableActionType $action
     * @param int|null $requestedBy
     * @param bool $bypassApproval
     * @param array $arguments
     * @param bool $enforceSingleApprovalPending
     * @param string|null $note
     * @return void
     * @throws BindingResolutionException
     */
    public function requestActionExecution(
        string $referenceId,
        int $modelId,
        ApprovableActionRelationTypes $type,
        ApprovableActionType $action,
        ?int $requestedBy,
        bool $bypassApproval = false,
        array $arguments = [],
        bool $enforceSingleApprovalPending = true,
        ?string $note = null,
    ): void
    {
        if ($bypassApproval) {
            $this->executeAction(
                actionType: $action,
                status    : ApprovalStatus::APPROVED,
                arguments : $arguments,
            );
        } else {
            $modelClass = $type->getModelClass();

            if ($enforceSingleApprovalPending) {
                $exists = ActionApproval::query()
                    ->where(ActionApproval::FIELD_APPROVABLE_ID, $modelId)
                    ->where(ActionApproval::FIELD_APPROVABLE_TYPE, $modelClass)
                    ->where(ActionApproval::FIELD_STATUS, ApprovalStatus::PENDING->value)
                    ->exists();

                if ($exists) {
                    throw new Exception('Action not requested because there is a pending request');
                }
            }

            ActionApprovalAggregateRoot::retrieve($referenceId)
                ->requestAction(
                    actionApprovalUuid: Str::uuid()->toString(),
                    modelId           : $modelId,
                    modelClass        : $modelClass,
                    actionRequested   : $action->value,
                    authorType        : $requestedBy ? InvoiceEventAuthorTypes::USER->value : InvoiceEventAuthorTypes::SYSTEM->value,
                    authorId          : $requestedBy,
                    arguments         : $arguments,
                    note              : $note
                )->persist();
        }
    }

    /**
     * @param int|null $reviewedBy
     * @param int|null $requestedBy
     * @param string|null $status
     * @param array $relatedTypes
     * @param array $relatedIds
     * @param array $orderBy
     * @param int|null $companyId
     * @param array $requestedActionTypes
     * @return Builder
     */
    public function getListQuery(
        ?int $reviewedBy = null,
        ?int $requestedBy = null,
        ?string $status = null,
        array $relatedTypes = [],
        array $relatedIds = [],
        array $orderBy = [],
        ?int $companyId = null,
        array $requestedActionTypes = []
    ): Builder
    {
        return $this->actionApprovalRepository->getListQuery(
            reviewedBy          : $reviewedBy,
            requestedBy         : $requestedBy,
            status              : $status,
            relatedTypes        : $relatedTypes,
            relatedIds          : $relatedIds,
            orderBy             : $orderBy,
            companyId           : $companyId,
            requestedActionTypes: $requestedActionTypes
        );
    }

    /**
     * @param int $id
     * @return ActionApproval
     */
    public function getInvoiceActionRequest(
        int $id,
    ): ActionApproval
    {
        return $this->actionApprovalRepository->getInvoiceActionRequest($id);
    }

    /**
     * @param ActionApproval $actionApproval
     * @param User $user
     * @return void
     * @throws Exception
     */
    public function cancel(
        ActionApproval $actionApproval,
        User $user
    ): void
    {
        if ($actionApproval->requested_by !== $user->id) {
            throw new Exception("Only the owner can cancel this action");
        }

        if ($actionApproval->status !== ApprovalStatus::PENDING) {
            throw new Exception("Only pending actions can be cancelled");
        }

        ActionApprovalAggregateRoot::retrieve($this->getReference($actionApproval))
            ->cancel(
                actionApprovalUuid: $actionApproval->uuid,
                modelId           : $actionApproval->approvable_id,
                modelClass        : $actionApproval->approvable_type,
                actionRequested   : $actionApproval->requested_action->value,
                authorType        : InvoiceEventAuthorTypes::USER->value,
                authorId          : $user->id,
                arguments         : $actionApproval->getActionArguments(),
                note              : $actionApproval->{ActionApproval::FIELD_NOTE}
            )->persist();
    }
}



