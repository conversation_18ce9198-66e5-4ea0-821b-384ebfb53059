<?php

namespace App\Services\Billing\InvoiceTransactions;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionType;
use App\Models\Billing\InvoiceTransaction;
use App\Repositories\Billing\InvoiceTransactionRepository;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;

class InvoiceTransactionService
{
    public function __construct(protected InvoiceTransactionRepository $repository)
    {
    }

    /**
     * @param int $page
     * @param int $perPage
     * @param array|null $types
     * @param array|null $scenarios
     * @param string|null $externalReference
     * @param int|null $invoiceId
     * @param int|null $minimumValue
     * @param int|null $maximumValue
     * @return LengthAwarePaginator
     */
    public function listInvoiceTransactions(
        int $page,
        int $perPage,
        ?array $types = null,
        ?array $scenarios = null,
        ?string $externalReference = null,
        ?int $invoiceId = null,
        ?int $minimumValue = null,
        ?int $maximumValue = null,
    ): LengthAwarePaginator
    {
        return $this->repository->listInvoiceTransactions(
            page             : $page,
            perPage          : $perPage,
            types            : $types,
            scenarios        : $scenarios,
            externalReference: $externalReference,
            invoiceId        : $invoiceId,
            minimumValue     : $minimumValue,
            maximumValue     : $maximumValue,
        );
    }

    /**
     * @return array
     */
    public function getInvoiceTransactionFilters(): array
    {
        $transactionScenarios = collect(InvoiceTransactionScenario::cases())
            ->map(function (InvoiceTransactionScenario $type) {
                return [
                    'id'   => $type->value,
                    'name' => $type->getTitle()
                ];
            });

        $transactionTypes = collect(InvoiceTransactionType::cases())->map(function (InvoiceTransactionType $type) {
            return [
                'id'   => $type->value,
                'name' => $type->getTitle()
            ];
        })->push(
            [
                'id'   => 'voided',
                'name' => 'Voided'
            ],
            [
                'id'   => 'deleted',
                'name' => 'Deleted'
            ]);


        return [
            'transaction_types'     => $transactionTypes,
            'transaction_scenarios' => $transactionScenarios,
        ];
    }

    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param string $externalReference
     * @param int $amount
     * @param string $currency
     * @param string $type
     * @param string $origin
     * @param array|null $payload
     * @param string|null $scenario
     * @param string|null $scope
     * @param string|null $date
     * @return void
     * @throws BindingResolutionException
     */
    public function createInvoiceTransaction(
        string $uuid,
        string $invoiceUuid,
        string $externalReference,
        int $amount,
        string $currency,
        string $type,
        string $origin,
        ?array $payload = [],
        ?string $scenario = null,
        ?string $scope = null,
        ?string $date = null
    ): void
    {
        $root = InvoiceAggregateRoot::retrieve($invoiceUuid);

        $root->transaction->createInvoiceTransaction(
            uuid             : $uuid,
            invoiceUuid      : $invoiceUuid,
            externalReference: $externalReference,
            amount           : $amount,
            currency         : $currency,
            type             : $type,
            origin           : $origin,
            payload          : $payload,
            scenario         : $scenario,
            scope            : $scope,
            date             : $date,
        );

        $root->persist();
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceTransactionType|null $type
     * @param array|null $relations
     * @param string|null $externalReference
     * @param InvoiceTransactionScenario|null $scenario
     * @return Collection<InvoiceTransaction>
     * @throws Exception
     */
    public function getInvoiceTransactions(
        string $invoiceUuid,
        ?InvoiceTransactionType $type = null,
        ?array $relations = [],
        ?string $externalReference = null,
        ?InvoiceTransactionScenario $scenario = null
    ): Collection
    {
        return $this->repository->getInvoiceTransactions(
            invoiceUuid      : $invoiceUuid,
            type             : $type,
            relations        : $relations,
            externalReference: $externalReference,
            scenario         : $scenario,
        );
    }
}
