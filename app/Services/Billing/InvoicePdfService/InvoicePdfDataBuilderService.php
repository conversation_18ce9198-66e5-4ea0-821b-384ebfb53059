<?php

namespace App\Services\Billing\InvoicePdfService;

use App\DTO\Billing\InvoicePdf\SummaryItem;
use App\DTO\Billing\InvoicePdf\Table;
use App\DTO\Billing\InvoicePdf\TableTotals;
use App\DTO\Billing\InvoicePdf\TableHeader;
use App\Enums\GlobalConfigurationKey;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CreditType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\ProductAssignment;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Services\Billing\InvoiceTemplateRenderer\InvoiceFoundationComponent;
use App\Services\CurrencyService;
use App\Services\MoneyFactory;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use function PHPUnit\Framework\isEmpty;

class InvoicePdfDataBuilderService
{
    protected MoneyFactory $moneyFactory;
    const string DATE_FORMAT = 'M d, Y';
    const string TIME_ZONE   = 'America/Denver';
    const bool   SHOW_TAX    = false;

    public function __construct(
        protected CurrencyService $currencyService,
        protected GlobalConfigurationRepository $globalConfigurationRepository,
    )
    {
    }


    /**
     * @param array<InvoiceItem> $invoiceItems
     * @return Collection
     */
    public function formatProductAssignments(array $invoiceItems): Collection
    {
        $headers = collect([
            TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'details',
                TableHeader::FIELD_TITLE => 'Details',
            ]),
            TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'date',
                TableHeader::FIELD_TITLE => 'Delivered At',
            ]),
            self::SHOW_TAX ? TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'tax.formatted',
                TableHeader::FIELD_TITLE => 'Tax',
            ]) : null,
            TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'price.formatted',
                TableHeader::FIELD_TITLE => 'Price',
            ]),
        ])->filter();

        $itemGroupedByCampaign = [];
        $industryGroupedByCampaign = [];

        foreach ($invoiceItems as $item) {
            /** @var ProductAssignment $related */
            $related = $item->billable;

            $related->load([
                'budget.budgetContainer.campaign' => function ($query) {
                    $query->withTrashed();
                }
            ]);

            $groupName = $related->budget?->budgetContainer?->campaign?->name ?? 'Unknown Campaign';
            $industry = $related->consumerProduct?->serviceProduct?->service?->industry->name ?? 'Unknown Industry';

            if (!isset($itemGroupedByCampaign[$groupName])) {
                $itemGroupedByCampaign[$groupName] = [];
            }

            if (!isset($industryGroupedByCampaign[$groupName])) {
                $industryGroupedByCampaign[$groupName] = [];
            }

            $itemGroupedByCampaign[$groupName][] = $item;
            $industryGroupedByCampaign[$groupName][] = $industry;
        }

        return collect($itemGroupedByCampaign)
            ->map(fn($val, $key) => $this->formatInvoiceItemsDataForTableView(
                title          : $key,
                invoiceItems   : $val,
                incomingHeaders: $headers,
                tableMeta      : collect($industryGroupedByCampaign[$key])->unique()->implode(', ')
            ));
    }

    /**
     * @param array $groupedInvoiceItems
     * @return Collection
     */
    protected function getLineItemTables(array $groupedInvoiceItems): Collection
    {
        $lineItemTables = collect();

        $standardHeaders = collect([
            TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'details',
                TableHeader::FIELD_TITLE => 'Details',
            ]),
            TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'date',
                TableHeader::FIELD_TITLE => 'Date',
            ]),
            self::SHOW_TAX ? TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'tax.formatted',
                TableHeader::FIELD_TITLE => 'Tax',
            ]) : null,
            TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'quantity',
                TableHeader::FIELD_TITLE => 'Quantity',
            ]),
            TableHeader::fromArray([
                TableHeader::FIELD_FIELD => 'price.formatted',
                TableHeader::FIELD_TITLE => 'Price',
            ]),
        ])->filter();

        foreach ($groupedInvoiceItems as $group => $invoiceItems) {
            $res = match ($group) {
                CreditType::class        => $this->formatInvoiceItemsDataForTableView('Credits', $invoiceItems, $standardHeaders),
                'manual'                 => $this->formatInvoiceItemsDataForTableView('Manual Line Items', $invoiceItems, $standardHeaders),
                ProductAssignment::class => $this->formatProductAssignments($invoiceItems),
                default                  => $this->formatInvoiceItemsDataForTableView('Others', $invoiceItems, $standardHeaders),
            };

            if ($res instanceof Table) {
                $lineItemTables->add($res);
            } else {
                $lineItemTables = $lineItemTables->merge($res->values())->values();
            }
        }

        // Sort table by title alphabetically
        return $lineItemTables->sortBy(fn (Table $table) => $table->getTitle());
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceFoundationComponent $invoiceFoundationComponent
     * @return mixed
     */
    public function formatInvoiceDataToView(Invoice $invoice, InvoiceFoundationComponent $invoiceFoundationComponent): array
    {
        $this->moneyFactory = new MoneyFactory($invoiceFoundationComponent->getCurrency());

        $groupedInvoiceItems = $this->groupInvoiceItemsByBillableType($invoice->{Invoice::RELATION_INVOICE_ITEMS});

        $billedCompany = $this->getBilledCompanyData($invoice->{Invoice::RELATION_COMPANY});

        $lineItemTables = $this->getLineItemTables($groupedInvoiceItems);

        $summary = $this->formatDataToSummary($lineItemTables, $invoice);

        $billingAccountData = $this->getBankAccountInfo($invoiceFoundationComponent);

        return collect([
            'billedCompany'               => $billedCompany,
            'lineItemTables'              => $lineItemTables,
            'summary'                     => $summary,
            'note'                        => $invoice->notes,
            'billing_account'             => $billingAccountData,
            'billingProfilePaymentMethod' => $invoice->{Invoice::RELATION_BILLING_PROFILE}->{BillingProfile::FIELD_PAYMENT_METHOD}
        ])->toArray();
    }

    /**
     * @param InvoiceFoundationComponent $invoiceFoundationComponent
     * @return array|null
     */
    public function getBankAccountInfo(InvoiceFoundationComponent $invoiceFoundationComponent): ?array
    {
        $billingAccount = GlobalConfigurationKey::tryFrom($invoiceFoundationComponent->getBillingAccount());

        $accountConfig = $this->globalConfigurationRepository->getConfigurationPayload($billingAccount)?->toArray() ?? [];

        return $billingAccount ? Arr::get($accountConfig, 'data') : null;
    }

    /**
     * @param string $title
     * @param array $invoiceItems
     * @param Collection|null $incomingHeaders
     * @param string|null $tableMeta
     * @return Table
     */
    public function formatInvoiceItemsDataForTableView(
        string $title,
        array $invoiceItems,
        ?Collection $incomingHeaders = new Collection(),
        ?string $tableMeta = null
    ): Table
    {
        $aggregateTax = 0;
        $aggregateSubTotal = 0;
        $aggregateTotal = 0;

        $rows = collect($invoiceItems)->map(function (InvoiceItem $item) use (
            &$aggregateTax,
            &$aggregateSubTotal,
            &$aggregateTotal,
        ) {

            $tax = $item->{InvoiceItem::FIELD_TAX} ?? 0;

            $aggregateTax += $tax;
            $aggregateSubTotal += $item->totalPrice();
            $aggregateTotal += self::SHOW_TAX ? $item->totalPrice() + $tax : $item->totalPrice();

            return collect([
                'id'       => $item->{InvoiceItem::FIELD_ID},
                'details'  => $item->{InvoiceItem::FIELD_DESCRIPTION},
                'date'     => $item->billable_type === ProductAssignment::class
                    ? Carbon::parse($item->billable?->delivered_at)->setTimezone(self::TIME_ZONE)->format(self::DATE_FORMAT)
                    : Carbon::parse($item->{InvoiceItem::FIELD_CREATED_AT})->setTimezone(self::TIME_ZONE)->format(self::DATE_FORMAT),
                'quantity' => $item->{InvoiceItem::FIELD_QUANTITY},
                'tax'      => self::SHOW_TAX ? $this->moneyFactory->make($tax) : 0,
                'price'    => $this->moneyFactory->make($item->{InvoiceItem::FIELD_UNIT_PRICE}),
            ]);
        });

        $totals = new TableTotals(
            total   : $this->moneyFactory->make($aggregateTotal),
            subtotal: $this->moneyFactory->make($aggregateSubTotal),
            tax     : self::SHOW_TAX ? $this->moneyFactory->make($aggregateTax) : null,
        );

        return new Table(
            title  : $title,
            rows   : $rows,
            headers: $incomingHeaders,
            totals : $totals,
            meta   : $tableMeta,
        );
    }

    /**
     * todo: retrieve actual custom totals (credits applied etc)
     * @return Collection
     */
    public function getCustomTotals(): Collection
    {
        $customTotals = collect();


        return $customTotals->push();
    }

    /**
     * @param Company $company
     * @return array
     */
    public function getBilledCompanyData(Company $company): array
    {
        /** @var Address $address */
        $address = $company
            ->locations()
            ->where(CompanyLocation::FIELD_IS_PRIMARY, true)
            ->first()
            ?->{CompanyLocation::RELATION_ADDRESS};

        return [
            'company_name'        => $company->{Company::FIELD_NAME},
            'address_street_name' => $address?->{Address::FIELD_ADDRESS_1},
            'address_postcode'    => $address?->{Address::FIELD_ZIP_CODE},
            'full_address'        => $address?->getFullAddress(),
        ];
    }

    /**
     * @param Collection<Table> $data
     * @param Invoice $invoice
     * @return Table
     */
    public function formatDataToSummary(Collection $data, Invoice $invoice): Table
    {
        $table = Table::fromArray([
            Table::FIELD_TITLE   => 'Summary',
            Table::FIELD_HEADERS => collect([
                [
                    'field' => 'name',
                    'title' => 'Name'
                ],
                [
                    'field' => 'details',
                    'title' => 'details'
                ],
                [
                    'field' => 'count',
                    'title' => 'count'
                ],
                [
                    'field' => 'subtotal.formatted',
                    'title' => 'subtotal'
                ],
                self::SHOW_TAX ? [
                    'field' => 'tax.formatted',
                    'title' => 'tax'
                ] : null,
                [
                    'field' => 'total.formatted',
                    'title' => 'total'
                ],
            ])->filter()->toarray()
        ]);

        $aggregateTax = 0;
        $aggregateSubTotal = 0;
        $aggregateTotal = 0;

        foreach ($data as $item) {
            $subtotal = $item->getTotals()->getSubtotal();
            $tax = $item->getTotals()->getTax();
            $total = $item->getTotals()->getTotal();
            $count = $item->getRows()->count();

            $aggregateTax += $tax?->getAmount() ?? 0;
            $aggregateSubTotal += $subtotal->getAmount();
            $aggregateTotal += $total->getAmount();

            $table->addRow(collect([
                'name'     => $item->getTitle(),
                'count'    => $count,
                'details'  => $item->getMeta(),
                'subtotal' => $subtotal,
                'tax'      => self::SHOW_TAX ? $tax : 0,
                'total'    => $total,
            ]));
        }

        $customTotals = collect();

        $totalCreditsApplied = $invoice->getTotalCreditsApplied();

        if ($totalCreditsApplied > 0) {
            $customTotals = collect([
                new SummaryItem(
                    'Credits',
                    $this->moneyFactory->make($totalCreditsApplied)
                ),
                new SummaryItem(
                    'Outstanding',
                    $this->moneyFactory->make($invoice->getTotalIssuable())
                )
            ]);
        }

        $totals = new TableTotals(
            total       : $this->moneyFactory->make($invoice->getInvoiceItemsTotal()),
            subtotal    : $this->moneyFactory->make($invoice->getInvoiceItemsTotal()),
            tax         : self::SHOW_TAX ? $this->moneyFactory->make($aggregateTax) : null,
            customTotals: $customTotals
        );

        $table->setTotals($totals);

        return $table;
    }


    /**
     * @param Collection<InvoiceItem> $invoiceItems
     * @return array
     */
    public function groupInvoiceItemsByBillableType(Collection $invoiceItems): array
    {
        $grouped = [];

        foreach ($invoiceItems as $item) {
            $type = $item->{InvoiceItem::FIELD_BILLABLE_TYPE};

            if (!isset($grouped[$type])) {
                $grouped[$type] = [];
            }

            $grouped[$type][] = $item;
        }

        return $grouped;
    }
}



