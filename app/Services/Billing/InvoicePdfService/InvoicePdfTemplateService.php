<?php

namespace App\Services\Billing\InvoicePdfService;

use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Billing\InvoiceTemplate;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Services\Billing\InvoiceTemplateRenderer\BaseComponent;
use App\Services\Billing\InvoiceTemplateRenderer\InvoiceFoundationComponent;
use Exception;
use Illuminate\Support\Arr;

class InvoicePdfTemplateService
{

    /**
     * @param Invoice $invoice
     * @return InvoiceTemplate
     * @throws Exception
     */
    public function getInvoiceTemplate(Invoice $invoice): InvoiceTemplate
    {
        $invoiceTemplate = $invoice->{Invoice::RELATION_BILLING_PROFILE}->{BillingProfile::RELATION_INVOICE_TEMPLATE};

        if (empty($invoiceTemplate)) {
            $relation = $this->discoverRelations($invoice);
            if (count($relation) > 0) {
                [$class, $id] = $relation;
                $invoiceTemplate = InvoiceTemplate::query()
                    ->where(InvoiceTemplate::FIELD_MODEL_TYPE, $class)
                    ->where(InvoiceTemplate::FIELD_MODEL_ID, $id)
                    ->first();
            }
        }

        /** @var InvoiceTemplate $invoiceTemplate */
        $invoiceTemplate = $invoiceTemplate ?: InvoiceTemplate::query()
            ->where(InvoiceTemplate::FIELD_IS_GLOBAL, true)
            ->first();

        if (empty($invoiceTemplate)) {
            throw new Exception('Invoice template not found');
        }

        return $invoiceTemplate;
    }

    /**
     * @param InvoiceTemplate|null $invoiceTemplate
     * @return BaseComponent
     */
    public function getComponentsProps(?InvoiceTemplate $invoiceTemplate = null): BaseComponent
    {
        // Add support for other components ?
        return InvoiceFoundationComponent::make($invoiceTemplate->{InvoiceTemplate::FIELD_PROPS});
    }

    /**
     * Loop through all invoice items and check if all belong to the industry, service etc and load the template related to it
     * @param Invoice $invoice
     * @return array
     */
    public function discoverRelations(Invoice $invoice): array
    {
        $relations = [];

        // TODO - Improve, add support for other billable entities
        // Use tags?
        foreach ($invoice->{Invoice::RELATION_INVOICE_ITEMS} as $item) {
            if ($item->{InvoiceItem::FIELD_BILLABLE_TYPE} === ProductAssignment::class) {
                $billable = $item->billable;

                $industryServiceSlug = $billable
                    ?->{ProductAssignment::RELATION_CONSUMER_PRODUCT}
                    ?->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}
                    ?->{IndustryService::FIELD_SLUG};

                $industrySlug = $billable
                    ?->{ProductAssignment::RELATION_CONSUMER_PRODUCT}
                    ?->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}
                    ?->{IndustryService::RELATION_INDUSTRY}
                    ?->{Industry::FIELD_SLUG};

                if (!isset($relations[$industrySlug])) {
                    $relations['industry'] = [];
                }

                if (!isset($relations[$industryServiceSlug])) {
                    $relations['industry_service'] = [];
                }

                $relations['industry'][] = $industrySlug;
                $relations['industry_service'][] = $industryServiceSlug;
            }
        }

        // This is the order of priority to load templates
        if ($this->areAllItemsSame(Arr::get($relations, 'industry', []))) {
            return [
                Industry::class,
                $relations['industry'][0]
            ];
        }

        if ($this->areAllItemsSame(Arr::get($relations, 'industry_service', []))) {
            return [
                IndustryService::class,
                $relations['industry_service'][0]
            ];
        }

        return [];
    }

    /**
     * @param array $array
     * @return bool
     */
    public function areAllItemsSame(array $array): bool
    {
        if (count($array) === 0) {
            return false;
        }

        $firstItem = reset($array);

        foreach ($array as $item) {
            if ($item !== $firstItem) {
                return false;
            }
        }

        return true;
    }
}



