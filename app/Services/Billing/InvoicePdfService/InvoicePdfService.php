<?php

namespace App\Services\Billing\InvoicePdfService;

use App\Exceptions\Billing\PdfGenerationFailedException;
use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTemplate;
use App\Models\Odin\Company;
use App\Services\Billing\InvoiceTemplateRenderer\InvoiceFoundationComponent;
use App\Services\Billing\PdfBuilderService;
use Carbon\Carbon;
use Exception;
use Illuminate\Validation\ValidationException;

class InvoicePdfService
{
    const string INVOICE_CONTENT_VIEW = 'billing.invoice-content';
    const string INVOICE_FOOTER_VIEW  = 'billing.invoice-footer';
    const string INVOICE_HEADER_VIEW  = 'billing.invoice-header';
    const string HASH_ALGORITHM       = 'xxH3';

    public function __construct(
        protected PdfBuilderService $pdfBuilderService,
        protected InvoicePdfDataBuilderService $invoicePdfDataBuilderService,
        protected InvoicePdfTemplateService $invoicePdfTemplateService
    )
    {

    }

    /**
     * @param Invoice $invoice
     * @return string
     * @throws PdfGenerationFailedException
     */
    public function createPdf(
        Invoice $invoice
    ): string
    {
        return $this->mountPdfHtml($invoice)
            ->toBucket(
                $this->buildStoragePath($invoice),
                config('services.google.storage.buckets.invoices')
            );
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceTemplate|null $invoiceTemplate
     * @return PdfBuilderService
     * @throws Exception
     */
    public function mountPdfHtml(Invoice $invoice, ?InvoiceTemplate $invoiceTemplate = null): PdfBuilderService
    {
        $invoiceTemplate = $invoiceTemplate ?? $this->invoicePdfTemplateService->getInvoiceTemplate($invoice);
        /** @var InvoiceFoundationComponent $componentsProps */
        $componentsProps = $this->invoicePdfTemplateService->getComponentsProps($invoiceTemplate);
        $viewData = $this->invoicePdfDataBuilderService->formatInvoiceDataToView($invoice, $componentsProps);

        $filename = $this->buildPdfFilename($viewData);

        return $this->pdfBuilderService
            ->setContentView(self::INVOICE_CONTENT_VIEW, collect([
                'viewData'        => $viewData,
                'componentsProps' => $componentsProps,
            ]))
            ->setHeaderView(self::INVOICE_HEADER_VIEW, collect([
                'invoiceId'       => $invoice->{Invoice::FIELD_ID},
                'issueDate'       => CarbonHelper::parseWithTimezone($invoice->{Invoice::FIELD_ISSUE_AT})->format('d M Y'),
                'dueDate'         => CarbonHelper::parseWithTimezone($invoice->{Invoice::FIELD_DUE_AT})->format('d M Y'),
                'componentsProps' => $componentsProps
            ]))
            ->setFooterView(self::INVOICE_FOOTER_VIEW)
            ->setFilename($filename)
            ->setMarginsInPixels(200, 0, 75, 0);
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceTemplate|null $invoiceTemplate
     * @return string
     * @throws ValidationException|Exception
     */
    public function previewContentHtml(Invoice $invoice, ?InvoiceTemplate $invoiceTemplate = null): string
    {
        return $this->mountPdfHtml($invoice, $invoiceTemplate)->getContentHtml();
    }

    /**
     * @param Invoice $invoice
     * @return string
     */
    protected function buildStoragePath(Invoice $invoice): string
    {
        return '/companies/'
            . $invoice->{Invoice::RELATION_COMPANY}->{Company::FIELD_REFERENCE}
            . '/invoices';
    }

    /**
     * @param array $data
     * @return string
     */
    protected function buildPdfFilename(array $data): string
    {
        return hash(self::HASH_ALGORITHM, json_encode([
                ...$data,
                '_generated_at' => now()->toISOString()
            ])) . '.pdf';
    }

    /**
     * @param string $str
     * @return array
     */
    public function extractAndRemoveFirstAndLastWords(string $str): array
    {
        preg_match('/^\w+/', $str, $firstWordMatch);
        preg_match('/\w+$/', $str, $lastWordMatch);

        $firstWord = $firstWordMatch ? $firstWordMatch[0] : null;
        $lastWord = $lastWordMatch ? $lastWordMatch[0] : null;

        $modifiedStr = $str;

        if ($firstWord) {
            $modifiedStr = trim(str_replace($firstWord, '', $modifiedStr));
        }

        if ($lastWord) {
            $lastWordIndex = strrpos($modifiedStr, $lastWord);
            if ($lastWordIndex !== false) {
                $modifiedStr = trim(substr($modifiedStr, 0, $lastWordIndex));
            }
        }

        return ['firstWord' => $firstWord, 'lastWord' => $lastWord, 'modifiedStr' => $modifiedStr];
    }
}
