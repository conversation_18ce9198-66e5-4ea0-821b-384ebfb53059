<?php

namespace App\Services\Billing\InvoiceStatusListener;

use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Services\Billing\InvoiceStatusListener\InvoiceItemInvoiceStatusListenerFactory\InvoiceItemInvoiceStatusListenerFactory;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class InvoiceStatusChangeController
{
    const string STEP_ON     = 'on';
    const string STEP_BEFORE = 'before';

    protected Collection $listeners;

    public function __construct()
    {
        $this->listeners = collect([
            InvoiceStatusEmitterListener::class,
            InvoiceStatusListener::class,
        ]);
    }

    /**
     * @param InvoiceStates $state
     * @param Invoice $invoice
     * @return void
     * @throws BindingResolutionException
     */
    public function on(InvoiceStates $state, Invoice $invoice): void
    {
        $this->notifyListeners($state, $invoice, self::STEP_ON);
        $this->notifyItems($state, $invoice, self::STEP_ON);
    }


    /**
     * @param InvoiceStates $state
     * @param Invoice $invoice
     * @return void
     * @throws BindingResolutionException
     */
    public function before(InvoiceStates $state, Invoice $invoice): void
    {
        $this->notifyListeners($state, $invoice, self::STEP_BEFORE);
        $this->notifyItems($state, $invoice, self::STEP_BEFORE);
    }

    /**
     * @param InvoiceStates $state
     * @param Invoice $invoice
     * @param string $step
     * @return void
     * @throws BindingResolutionException
     */
    protected function notifyListeners(
        InvoiceStates $state,
        Invoice $invoice,
        string $step
    ): void
    {
        foreach ($this->listeners as $listener) {
            $instance = app()->makeWith(
                $listener,
                [
                    "invoice" => $invoice,
                ]
            );

            $this->notifyListener($instance, $state, $step);
        }
    }

    /**
     * @param BaseInvoiceStatusListener $listener
     * @param InvoiceStates $state
     * @param string $step
     * @return void
     */
    protected function notifyListener(
        BaseInvoiceStatusListener $listener,
        Invoicestates $state,
        string $step
    ): void
    {
        $methodName = $this->getListenerMethodName($state, $step);

        if ($methodName && method_exists($listener, $methodName)) {
            $listener->$methodName();
        }
    }

    /**
     * @param InvoiceStates $state
     * @param string $methodPrefix
     * @return string|null
     */
    private function getListenerMethodName(Invoicestates $state, string $methodPrefix): ?string
    {
        return match ($state) {
            InvoiceStates::PAID       => $methodPrefix . 'Paid',
            InvoiceStates::ISSUED     => $methodPrefix . 'Issued',
            InvoiceStates::FAILED     => $methodPrefix . 'Failed',
            InvoiceStates::COLLECTION => $methodPrefix . 'Collections',
            InvoiceStates::CHARGEBACK => $methodPrefix . 'Chargeback',
            InvoiceStates::REFUNDED   => $methodPrefix . 'Refunded',
            default                   => null
        };
    }


    /**
     * @param InvoiceStates $state
     * @param Invoice $invoice
     * @param string $step
     * @return void
     * @throws BindingResolutionException
     */
    protected function notifyItems(
        InvoiceStates $state,
        Invoice $invoice,
        string $step
    ): void
    {
        foreach ($invoice->{Invoice::RELATION_INVOICE_ITEMS} as $invoiceItem) {
            $listener = InvoiceItemInvoiceStatusListenerFactory::make(
                invoice    : $invoice,
                invoiceItem: $invoiceItem,
            );

            if (empty($listener)) {
                return;
            }

            $this->notifyListener($listener, $state, $step);
        }
    }
}
