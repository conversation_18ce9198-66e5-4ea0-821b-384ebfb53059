<?php

namespace App\Services\Billing\InvoiceStatusListener;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Bundle;
use App\Services\Billing\InvoicePaymentService;
use Exception;

class InvoiceStatusListener extends BaseInvoiceStatusListener
{
    /**
     * @return void
     * @throws Exception
     */
    public function onIssued(): void
    {
        $billingProfile = $this->invoice->{Invoice::RELATION_BILLING_PROFILE};
        $invoiceOutstanding = $this->invoice->getTotalOutstanding();

        /** @var InvoicePaymentService $invoicePaymentService */
        $invoicePaymentService = app()->make(InvoicePaymentService::class);

        $shouldAutoProcess = !$this->invoice->isBundleInvoice()
            && $billingProfile->process_auto
            && $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD}->value === PaymentMethodServices::STRIPE->value
            && $invoiceOutstanding > 0;

        if ($shouldAutoProcess) {
            $invoicePaymentService->payInvoice(
                invoice   : $this->invoice,
                amount    : $invoiceOutstanding,
                authorType: InvoiceEventAuthorTypes::SYSTEM,
            );
        }
    }
}
