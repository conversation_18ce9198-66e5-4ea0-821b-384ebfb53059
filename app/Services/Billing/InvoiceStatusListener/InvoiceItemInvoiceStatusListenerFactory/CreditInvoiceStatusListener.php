<?php

namespace App\Services\Billing\InvoiceStatusListener\InvoiceItemInvoiceStatusListenerFactory;

use App\Aggregates\CreditAggregateRoot;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\Billing\CreditType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Odin\Company;
use App\Services\Billing\InvoiceStatusListener\BaseInvoiceStatusListener;
use Illuminate\Support\Str;

class CreditInvoiceStatusListener extends BaseInvoiceStatusListener
{
    /**
     * @return void
     */
    public function onPaid(): void
    {
        $credit = $this->invoiceItem->{InvoiceItem::RELATION_BILLABLE};

        CreditAggregateRoot::retrieve($this->invoice->{Invoice::FIELD_UUID})
            ->addCredit(
                uuid            : Str::uuid()->toString(),
                companyReference: $this->invoice->{Invoice::RELATION_COMPANY}->{Company::FIELD_REFERENCE},
                amount          : $this->invoiceItem->totalPrice(),
                type            : $credit->{CreditType::FIELD_SLUG},
                authorType      : InvoiceEventAuthorTypes::SYSTEM->value,
                notes           : 'Credit automatically applied through invoice ' . $this->invoice->{Invoice::FIELD_ID}
            )->persist();
    }
}
