<?php

namespace App\Services\Billing\InvoiceStatusListener\InvoiceItemInvoiceStatusListenerFactory;

use App\Enums\BundleInvoiceStatus;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Repositories\BundleManagement\BundleInvoiceHistoryRepository;
use App\Services\Billing\InvoiceStatusListener\BaseInvoiceStatusListener;
use App\Services\BundleManagement\v2\BundleInvoiceService;

class BundleInvoiceStatusListener extends BaseInvoiceStatusListener
{

    public function __construct(
        Invoice $invoice,
        InvoiceItem $invoiceItem,
        protected BundleInvoiceHistoryRepository $bundleInvoiceHistoryRepository,
        protected BundleInvoiceService $bundleInvoiceService,
    )
    {
        parent::__construct($invoice, $invoiceItem);
    }

    /**
     * @param int $invoiceId
     * @param BundleInvoiceStatus $status
     * @return void
     */
    protected function updateBundleInvoiceStatus(int $invoiceId, BundleInvoiceStatus $status): void
    {
        $bundleInvoice = BundleInvoice::query()
            ->where(BundleInvoice::FIELD_PAYABLE_INVOICE_ID, $invoiceId)
            ->firstOrFail();

        $oldStatus = $bundleInvoice->{BundleInvoice::FIELD_STATUS};

        $bundleInvoice->update([
            BundleInvoice::FIELD_STATUS => $status->value
        ]);

        $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
            $bundleInvoice->{BundleInvoice::FIELD_ID},
            sprintf(
                "Status updated from %s to %s",
                $oldStatus->getStatusString(),
                $status->getStatusString(),
            )
        );
    }

    /**
     * @return void
     */
    public function onPaid(): void
    {
        $this->updateBundleInvoiceStatus(
            $this->invoice->id,
            BundleInvoiceStatus::PAID
        );

        $bundleInvoice = BundleInvoice::query()
            ->findOrFail($this->invoiceItem->billable_id);

        if ($bundleInvoice->{BundleInvoice::RELATION_BUNDLE}->{Bundle::FIELD_AUTO_APPLY_CREDITS}) {
            $this->bundleInvoiceService->handleApproveCredits(
                bundleInvoice: $bundleInvoice,
            );
        }
    }


    /**
     * @return void
     */
    public function onIssued(): void
    {
        $this->updateBundleInvoiceStatus(
            $this->invoice->id,
            BundleInvoiceStatus::ISSUED
        );
    }


    /**
     * @return void
     */
    public function onFailed(): void
    {
        $this->updateBundleInvoiceStatus(
            $this->invoice->id,
            BundleInvoiceStatus::CANCELLED
        );
    }
}
