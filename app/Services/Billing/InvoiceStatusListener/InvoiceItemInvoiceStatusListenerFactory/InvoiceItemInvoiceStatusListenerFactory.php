<?php

namespace App\Services\Billing\InvoiceStatusListener\InvoiceItemInvoiceStatusListenerFactory;

use App\Models\Billing\CreditType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Bundle;
use App\Services\Billing\InvoiceStatusListener\BaseInvoiceStatusListener;
use Illuminate\Contracts\Container\BindingResolutionException;

class InvoiceItemInvoiceStatusListenerFactory
{
    /**
     * @param Invoice $invoice
     * @param InvoiceItem $invoiceItem
     * @return BaseInvoiceStatusListener|null
     * @throws BindingResolutionException
     */
    static function make(Invoice $invoice, InvoiceItem $invoiceItem): ?BaseInvoiceStatusListener
    {
        return match ($invoiceItem->{InvoiceItem::FIELD_BILLABLE_TYPE}) {
            CreditType::class => new CreditInvoiceStatusListener(
                invoice    : $invoice,
                invoiceItem: $invoiceItem
            ),
            Bundle::class     => app()->makeWith(
                BundleInvoiceStatusListener::class,
                [
                    "invoice"     => $invoice,
                    "invoiceItem" => $invoiceItem
                ]
            ),
            default           => null,
        };
    }
}
