<?php

namespace App\Services\Billing\InvoiceStatusListener;

use App\Events\Billing\LaravelEvents\InvoiceFailed;
use App\Events\Billing\LaravelEvents\InvoiceIssued;
use App\Events\Billing\LaravelEvents\InvoicePaid;

class InvoiceStatusEmitterListener extends BaseInvoiceStatusListener
{
    /**
     * @return void
     */
    public function onIssued(): void
    {
        InvoiceIssued::dispatch($this->invoice->uuid);
    }

    /**
     * @return void
     */
    public function onFailed(): void
    {
        InvoiceFailed::dispatch($this->invoice->uuid);
    }

    /**
     * @return void
     */
    public function onPaid(): void
    {
        InvoicePaid::dispatch($this->invoice->uuid);
    }
}
