<?php

namespace App\Services\Billing\InvoiceStatusListener;

use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;

abstract class BaseInvoiceStatusListener
{
    /**
     * @param Invoice $invoice
     * @param InvoiceItem|null $invoiceItem
     */
    public function __construct(protected Invoice $invoice, protected ?InvoiceItem $invoiceItem = null)
    {

    }

    /**
     * @return void
     */
    public function onPaid(): void
    {

    }

    /**
     * @return void
     */
    public function onIssued(): void
    {

    }


    /**
     * @return void
     */
    public function onFailed(): void
    {

    }

    /**
     * @return void
     */
    public function onCollections(): void
    {

    }

    /**
     * @return void
     */
    public function onVoided(): void
    {

    }

    /**
     * @return void
     */
    public function onDeleted(): void
    {

    }

    /**
     * @return void
     */
    public function onRefunded(): void
    {

    }

    /**
     * @return void
     */
    public function onChargeback(): void
    {

    }

    /**
     * @return void
     */
    public function beforePaid(): void
    {

    }

    /**
     * @return void
     */
    public function beforeIssued(): void
    {

    }


    /**
     * @return void
     */
    public function beforeFailed(): void
    {

    }

    /**
     * @return void
     */
    public function beforeCollections(): void
    {

    }

    /**
     * @return void
     */
    public function beforeVoided(): void
    {

    }

    /**
     * @return void
     */
    public function beforeDeleted(): void
    {

    }

    /**
     * @return void
     */
    public function beforeRefunded(): void
    {

    }

    /**
     * @return void
     */
    public function beforeChargeback(): void
    {

    }
}
