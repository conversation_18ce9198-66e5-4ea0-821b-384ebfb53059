<?php

namespace App\Services\Billing;

use App\Exceptions\Billing\PdfGenerationFailedException;
use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Spa<PERSON>\Browsershot\Browsershot;
use Spatie\LaravelPdf\Enums\Unit;
use Spatie\LaravelPdf\Facades\Pdf;
use Spatie\LaravelPdf\PdfBuilder;

class PdfBuilderService
{
    /**
     * @var string
     */
    protected string $contentView;
    /**
     * @var Collection
     */
    protected Collection $contentData;
    /**
     * @var array
     */
    protected array $margins = [];
    /**
     * @var string
     */
    protected string $headerView;
    /**
     * @var Collection
     */
    protected Collection $headerData;
    /**
     * @var string
     */
    protected string $footerView;
    /**
     * @var Collection
     */
    protected Collection $footerData;
    /**
     * @var string
     */
    protected string $filename;

    /**
     * @var string
     */
    private string $localFilepath;

    /**
     * @param GoogleCloudStorageService $googleCloudStorageService
     */
    public function __construct(
        protected GoogleCloudStorageService $googleCloudStorageService
    )
    {
        $this->headerData = new Collection();
        $this->footerData = new Collection();
        $this->contentData = new Collection();

    }

    /**
     * @param string $contentView
     * @param Collection $contentData
     * @return $this
     */
    public function setContentView(string $contentView, Collection $contentData = new Collection()): PdfBuilderService
    {
        $this->contentView = $contentView;
        $this->contentData = $contentData;
        return $this;
    }

    /**
     * @param string $footerView
     * @param Collection $footerData
     * @return $this
     */
    public function setFooterView(string $footerView, Collection $footerData = new Collection()): PdfBuilderService
    {
        $this->footerView = $footerView;
        $this->footerData = $footerData;
        return $this;
    }

    /**
     * @param string $headerView
     * @param Collection $headerData
     * @return $this
     */
    public function setHeaderView(string $headerView, Collection $headerData = new Collection()): PdfBuilderService
    {
        $this->headerView = $headerView;
        $this->headerData = $headerData;
        return $this;
    }

    /**
     * @param string $filename
     * @return $this
     */
    public function setFilename(string $filename): PdfBuilderService
    {
        $this->filename = $filename;
        return $this;
    }

    /**
     * @param string $path
     * @return string
     */
    public function toPath(string $path): string
    {
        $filepath = $this->getFilepath($path);

        $localFilepath = storage_path($filepath);
        $localFolder = dirname($localFilepath);

        if (!file_exists($localFolder)) {
            mkdir($localFolder, 0755, true);
        }

        $this->mountPdf()->save($localFilepath);

        $this->localFilepath = $localFilepath;

        return $filepath;
    }

    /**
     * @param string $path
     * @return string
     */
    protected function getFilepath(string $path): string
    {
        $path = Str::finish($path, '/');

        return $path . $this->filename;
    }

    /**
     * @return PdfBuilder
     */
    protected function mountPdf(): PdfBuilder
    {
        [$top, $right, $bottom, $left] = $this->margins;

        /** @var PdfBuilder */
        return Pdf::html($this->getContentHtml())
            ->headerHtml($this->getHeaderHtml())
            ->footerHtml($this->getFooterHtml())
            ->margins($top ?? 0, $right ?? 0, $bottom ?? 0, $left ?? 0, Unit::Pixel)
            ->withBrowsershot(function (Browsershot $browsershot) {
                $errors = $browsershot->setOption('args', ['--disable-web-security', '--no-sandbox'])->pageErrors();
                $failedRequests = $browsershot->failedRequests();
                $consoleMessages = $browsershot->consoleMessages();

                $hasConsoleErrors = collect($consoleMessages)
                    ->some(fn($item) => Arr::get($item, 'type') === 'error');

                if (!empty($errors) || !empty($failedRequests) || $hasConsoleErrors) {
                    throw new PdfGenerationFailedException(
                        failedRequests: $failedRequests,
                        errors        : $errors,
                        consoleMessages : $consoleMessages
                    );
                }
            });
    }

    /**
     *
     * @param string|null $bucket
     * @param string $path
     * @return string
     * @throws PdfGenerationFailedException|Exception
     */
    public function toBucket(string $path, ?string $bucket = null): string
    {
        $base64 = $this->mountPdf()->base64();

        $filepath = $this->getFilepath($path);

        $this->googleCloudStorageService->setCurrentBucket($bucket);
        $this->googleCloudStorageService->upload($filepath, base64_decode($base64));

        return $filepath;
    }


    /**
     * @return string
     */
    public function getFilename(): string
    {
        return $this->filename;
    }


    /**
     * @return string
     */
    public function getHeaderHtml(): string
    {
        if (empty($this->headerView)) {
            return '';
        }

        return view($this->headerView, $this->headerData->toArray())->render();
    }

    /**
     * @return string
     */
    public function getFooterHtml(): string
    {
        if (empty($this->footerView)) {
            return '';
        }

        return view($this->footerView, $this->footerData->toArray())->render();
    }


    /**
     * @return string
     */
    public function getContentHtml(): string
    {
        if (empty($this->contentView)) {
            return '';
        }

        return view($this->contentView, $this->contentData->toArray())->render();
    }

    /**
     * @return string
     */
    public function getAllHtml(): string
    {
        return implode(PHP_EOL, [
            $this->getHeaderHtml(),
            $this->getContentHtml(),
            $this->getFooterHtml(),
        ]);
    }

    /**
     * @param float|null $top
     * @param float|null $right
     * @param float|null $bottom
     * @param float|null $left
     * @return $this
     */
    public function setMarginsInPixels(
        ?float $top = 0,
        ?float $right = 0,
        ?float $bottom = 0,
        ?float $left = 0,
    ): self
    {
        $this->margins = [
            $top,
            $right,
            $bottom,
            $left,
        ];

        return $this;
    }
}
