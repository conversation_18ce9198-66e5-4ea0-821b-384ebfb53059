<?php

namespace App\Services\Billing;

use App\Aggregates\CreditAggregateRoot;
use App\DTO\Billing\Credit\CreditTypePayload;
use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Http\Requests\Billing\Credit\AddCreditRequest;
use App\Models\Billing\Credit;
use App\Models\Billing\CreditType;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\Credit\CreditRepository;
use App\Repositories\Credit\CreditTypeRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class CreditService
{
    const string NAME        = 'name';
    const string DESCRIPTION = 'description';
    const string ACTIVE      = 'active';
    const string ID          = 'id';

    public function __construct(
        protected CreditTypeRepository $creditTypeRepository,
        protected CreditRepository $creditRepository,
        protected BillingActionApprovalService $billingActionApprovalService
    )
    {

    }

    /**
     * @param string $uuid
     * @param string $companyReference
     * @param float $amount
     * @param string $type
     * @param string $authorType
     * @param int|null $authorId
     * @param array $billingProfileIds
     * @return void
     * @throws BindingResolutionException
     */
    public function addCredit(
        string $uuid,
        string $companyReference,
        float $amount,
        string $type,
        string $authorType,
        ?int $authorId = null,
        array $billingProfileIds = []
    ): void
    {
        CreditAggregateRoot::retrieve($companyReference)
            ->addCredit(
                uuid             : $uuid,
                companyReference : $companyReference,
                amount           : $amount,
                type             : $type,
                authorType       : $authorType,
                authorId         : $authorId,
                billingProfileIds: $billingProfileIds
            )->persist();
    }

    /**
     * @throws Exception
     */
    public function requestAddCredit(
        string $uuid,
        string $companyReference,
        int $companyId,
        int $amount,
        string $type,
        string $authorType,
        ?int $authorId = null,
        ?string $note = null,
        ?array $billingProfileIds = []
    ): void
    {
        $creditType = $this->getCreditTypeBySlug($type);

        $this->billingActionApprovalService->requestActionExecution(
            referenceId                 : $companyReference,
            modelId                     : $companyId,
            type                        : ApprovableActionRelationTypes::COMPANY,
            action                      : ApprovableActionType::APPLY_CREDIT_TO_COMPANY,
            requesterType               : InvoiceEventAuthorTypes::USER,
            requesterId                 : $authorId,
            arguments                   : [
                "uuid"              => $uuid,
                "amount"            => $amount,
                "type"              => $type,
                "name"              => $creditType->name,
                "authorType"        => $authorType,
                "authorId"          => $authorId,
                "companyReference"  => $companyReference,
                "billingProfileIds" => $billingProfileIds
            ],
            enforceSingleApprovalPending: false,
            note                        : $note,
        );
    }

    /**
     * @param Company $company
     * @param User $user
     * @param array $validated
     * @return void
     */
    public function deductCredit(Company $company, User $user, array $validated): void
    {
        CreditAggregateRoot::retrieve(Str::uuid())
            ->deductCreditType(
                company   : $company,
                amount    : Arr::get($validated, AddCreditRequest::FIELD_VALUE),
                type      : Arr::get($validated, AddCreditRequest::FIELD_TYPE),
                authorType: InvoiceEventAuthorTypes::USER->value,
                authorId  : $user->{User::FIELD_ID},
            )->persist();
    }

    /**
     * Retrieves the remaining balances of all credit types for a given company.
     * @param int $companyId
     * @param int|null $billingProfileId
     * @return Collection
     */
    public function getCreditBalances(
        int $companyId,
        ?int $billingProfileId = null,
    ): Collection
    {
        return $this->creditRepository->getAvailableActiveCredits(
            companyId       : $companyId,
            billingProfileId: $billingProfileId,
        );
    }


    /**
     * Retrieves the remaining balances of all credit types for a given company.
     * @param int $companyId
     * @param string|null $type
     * @return int
     */
    public function getCreditBalance(
        int $companyId,
        ?string $type = null
    ): int
    {
        return $this->creditRepository->getAvailableActiveCredits(
            companyId: $companyId,
            type     : $type
        )->first()?->balance ?? 0;
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getCompanyCredits(int $companyId): Collection
    {
        return $this->creditRepository->getCompanyCredits($companyId);
    }

    /**
     * @param int $companyId
     * @param int $creditId
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return bool
     * @throws BindingResolutionException
     */
    public function expireCredit(
        int $companyId,
        int $creditId,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null,
    ): bool
    {
        $company = Company::query()->findOrFail($companyId);
        $credit = Credit::query()->findOrFail($creditId);

        $this->billingActionApprovalService->requestActionExecution(
            referenceId                 : $company->{Company::FIELD_REFERENCE},
            modelId                     : $companyId,
            type                        : ApprovableActionRelationTypes::COMPANY,
            action                      : ApprovableActionType::EXPIRE_COMPANY_CREDIT,
            requesterType               : $authorType,
            requesterId                 : $authorId,
            arguments                   : [
                "creditType"           => $credit->credit_type,
                "creditId"             => $creditId,
                "authorType"           => $authorType->value,
                "authorId"             => $authorId,
                "newExpiryDate"        => now()->toISOString(),
                "oldExpiryDate"        => $credit->expires_at,
                "creditInitialValue"   => $credit->initial_value,
                "creditRemainingValue" => $credit->remaining_value,
            ],
            enforceSingleApprovalPending: false,
        );

        return true;
    }

    /**
     * @param Credit $credit
     * @return bool
     */
    public function isCreditExpired(Credit $credit): bool
    {
        return Carbon::now()->gte($credit->{Credit::FIELD_EXPIRES_AT});
    }

    /**
     * @param int $companyId
     * @param int $creditId
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @param string|null $newDate
     * @param int $extensionPeriod
     * @return bool
     * @throws BindingResolutionException
     */
    public function extendCredit(
        int $companyId,
        int $creditId,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null,
        ?string $newDate = null,
        int $extensionPeriod = 7
    ): bool
    {
        if (empty($newDate)) {
            $newDate = Carbon::now()->addDays($extensionPeriod);
        }

        $company = Company::query()->findOrFail($companyId);
        $credit = Credit::query()->findOrFail($creditId);

        $this->billingActionApprovalService->requestActionExecution(
            referenceId                 : $company->{Company::FIELD_REFERENCE},
            modelId                     : $companyId,
            type                        : ApprovableActionRelationTypes::COMPANY,
            action                      : ApprovableActionType::EXTEND_COMPANY_CREDIT,
            requesterType               : $authorType,
            requesterId                 : $authorId,
            arguments                   : [
                "extensionPeriod"      => $extensionPeriod,
                "newExpiryDate"        => $newDate,
                "oldExpiryDate"        => $credit->expires_at,
                "creditType"           => $credit->credit_type,
                "creditInitialValue"   => $credit->initial_value,
                "creditRemainingValue" => $credit->remaining_value,
                "creditId"             => $creditId,
                "authorType"           => $authorType->value,
                "authorId"             => $authorId,
            ],
            enforceSingleApprovalPending: false,
        );

        return true;
    }

    /**
     * @param array $filters
     * @return Collection
     */
    public function getCreditTypes(
        ?bool $isCash = null
    ): Collection
    {
        return $this->creditTypeRepository->getCreditTypes(
            isCash: $isCash
        );
    }

    /**
     * @param string $slug
     * @return ?CreditType
     */
    public function getCreditTypeBySlug(string $slug): ?CreditType
    {
        return $this->creditTypeRepository->getCreditTypeBySlug($slug);
    }

    /**
     * @param CreditTypePayload $payload
     * @return CreditType
     */
    public function createCreditType(CreditTypePayload $payload): CreditType
    {
        return $this->creditTypeRepository->updateOrCreate(
            name             : $payload->getName(),
            slug             : $payload->getSlug(),
            description      : $payload->getDescription(),
            line_item_text   : $payload->getLineItemText(),
            expires_in_days  : $payload->getExpiry() ? $payload->getexpiresInDays() : null,
            consumption_order: $payload->getConsumptionOrder(),
            cash             : $payload->getCash(),
            active           : $payload->getActive(),
            id               : $payload->getId() ?? null,
        );
    }

    /**
     * @param Collection<CreditTypePayload> $creditTypePayloads
     * @return Collection
     */
    public function updateOrCreateCreditTypes(Collection $creditTypePayloads): Collection
    {
        foreach ($creditTypePayloads as $creditType) {
            $creditType->setExpiry(!$creditType->getExpiresInDays());
            $this->createCreditType($creditType);
        }

        return $this->creditTypeRepository->getCreditTypes();
    }
}
