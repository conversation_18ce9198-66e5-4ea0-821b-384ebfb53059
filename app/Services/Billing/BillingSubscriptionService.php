<?php

namespace App\Services\Billing;

use App\Models\Billing\BillingSubscription;
use Poliander\Cron\CronExpression;

class BillingSubscriptionService
{
    /**
     * @param array $billingSubscriptionAttributes
     * @return BillingSubscription|null
     */
    public function createBillingSubscription(array $billingSubscriptionAttributes): ?BillingSubscription
    {
            // generate frequency cron string
            // prevent collision with other billingSubscriptions for campaign
    }
}
