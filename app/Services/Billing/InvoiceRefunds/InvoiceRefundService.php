<?php

namespace App\Services\Billing\InvoiceRefunds;

use App\DTO\Billing\Refund\InvoiceRefundChargeDTO;
use App\DTO\Billing\Refund\InvoiceRefundDataDTO;
use App\DTO\Billing\Refund\InvoiceRefundDTO;
use App\DTO\Billing\Refund\InvoiceRefundItemDTO;
use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceTransactionType;
use App\Enums\Billing\Refunds\RefundReason;
use App\Enums\InvoiceRefundStatus;
use App\Exceptions\ValidatorException;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Billing\InvoiceRefund;
use App\Models\Billing\InvoiceRefundItem;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Billing\InvoiceTransaction;
use App\Repositories\Billing\InvoiceItemRepository;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundItemsRepository;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundRepository;
use App\Repositories\Billing\InvoiceRepository;
use App\Services\Billing\BillingActionApprovalService;
use App\Services\Billing\InvoiceItemService;
use App\Services\Billing\InvoicePaymentService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class InvoiceRefundService
{
    public function __construct(
        protected InvoiceRefundRepository $refundRepository,
        protected InvoiceRefundItemsRepository $refundItemsRepository,
        protected InvoiceRepository $invoiceRepository,
        protected InvoiceItemService $invoiceItemService,
        protected InvoiceItemRepository $invoiceItemRepository,
        protected InvoiceTransactionService $invoiceTransactionService,
        protected BillingActionApprovalService $billingActionApprovalService,
        protected InvoicePaymentService $invoicePaymentService,
    )
    {
    }

    /**
     * @param Invoice $invoice
     * @param float|null $customAmount
     * @param array|null $refundItemIds
     * @param string|null $reason
     * @param string|null $invoiceRefundUuid
     * @return InvoiceRefundDTO
     * @throws Exception
     */
    public function createInvoiceRefundDTO(
        Invoice $invoice,
        ?float $customAmount = 0,
        ?array $refundItemIds = [],
        ?string $reason = RefundReason::OTHER->value,
        ?string $invoiceRefundUuid = null,
    ): InvoiceRefundDTO
    {
        $invoiceRefundUuid = $invoiceRefundUuid ?: Str::uuid();

        $total = 0;

        $refundItems = collect();

        if ($customAmount != 0) {
            $total += $customAmount;

            $refundItems->push(new InvoiceRefundItemDTO(
                id             : null,
                invoiceItemId  : null,
                invoiceRefundId: null,
                value          : $customAmount,
            ));
        }

        if ($refundItemIds != []) {
            /** @var Collection<InvoiceItem> $invoiceItems */
            $invoiceItems = $invoice->{Invoice::RELATION_INVOICE_ITEMS};

            /** @var Collection<InvoiceItem> $itemsToRefund */
            $itemsToRefund = $invoiceItems
                ->filter(fn(InvoiceItem $invoiceItem) => in_array($invoiceItem->{InvoiceItem::FIELD_ID}, $refundItemIds));

            foreach ($itemsToRefund as $refundItem) {
                $total += $refundItem->totalItemPrice();

                $refundItems->push(new InvoiceRefundItemDTO(
                    id             : null,
                    invoiceItemId  : $refundItem->id,
                    invoiceRefundId: null,
                    value          : $refundItem->totalItemPrice(),
                ));
            }
        }

        $invoiceRefundCharges = $this->createInvoiceRefundCharges(
            invoice      : $invoice,
            customAmount : $customAmount,
            refundItemIds: $refundItemIds,
        );

        return new InvoiceRefundDTO(
            id                            : null,
            uuid                          : $invoiceRefundUuid,
            invoice_id                    : $invoice->id,
            total                         : $total,
            status                        : InvoiceRefundStatus::PENDING,
            reason                        : $reason,
            invoiceRefundItemsCollection  : $refundItems,
            invoiceRefundChargesCollection: $invoiceRefundCharges,
        );
    }

    /**
     * @param Invoice $invoice
     * @param float $customAmount
     * @param array $refundItems
     * @return void
     * @throws ValidatorException
     */
    public function validateRefundRequest(
        Invoice $invoice,
        float $customAmount,
        array $refundItems
    ): void
    {
        $refundItems = array_filter($refundItems);

        if (empty($refundItems) && empty($customAmount)) {
            throw new Exception('Cannot request a refund for nothing ¯\(°_o)/¯  ');
        }

        $refundRequestValue = $this->getRefundRequestValue(
            customAmount : $customAmount,
            refundItemIds: $refundItems
        );
        $refundable = $this->getInvoiceAmountRefundable($invoice);
        $message = null;

        if ($refundRequestValue > $refundable) {
            $message = 'Invalid refund request: Cannot request refund on invoice ' . $invoice->id . ' as request value exceeds refundable.';
        }

        if (!empty($message)) {
            logger()->error($message);
            throw ValidationException::withMessages([
                'message' => $message
            ]);
        }
    }

    /**
     * @throws Exception
     */
    public function createInvoiceRefundCharges(
        Invoice $invoice,
        ?float $customAmount = 0,
        ?array $refundItemIds = [],
    ): SupportCollection
    {
        $refundAmount = $this->getRefundRequestValue($customAmount, $refundItemIds);

        $invoicePayments = $this->invoiceTransactionService->getInvoiceTransactions(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::PAYMENT,
            relations  : [InvoiceTransaction::RELATION_REFUNDS]
        );

        $remainingRefundable = $this->calculateRemainingRefundable($invoicePayments);

        return $this->proposeInvoiceRefundCharges($remainingRefundable, $refundAmount);
    }

    /**
     * @param Collection $invoiceTransactions
     * @return SupportCollection
     */
    protected function calculateRemainingRefundable(Collection $invoiceTransactions)
    {
        return $invoiceTransactions
            ->filter(fn(InvoiceTransaction $transaction) => $transaction->totalRefundable() > 0)
            ->mapWithKeys(fn(InvoiceTransaction $transaction) => [
                $transaction->id => [
                    'internal_id' => $transaction->id,
                    'external_id' => $transaction->external_reference,
                    'refundable'  => $transaction->totalRefundable()
                ]
            ]);
    }

    /**
     * @param SupportCollection $refundable
     * @param float $refundAmount
     * @return SupportCollection
     * @throws Exception
     */
    protected function proposeInvoiceRefundCharges(SupportCollection $refundable, float $refundAmount): SupportCollection
    {
        $invoiceRefundChargesCollection = collect();

        foreach ($refundable as $value) {
            if (empty($refundAmount)) break;

            $refundChargeAmount = $refundAmount;

            if ($refundAmount >= $value['refundable']) {//refund the whole payment
                $refundChargeAmount = $value['refundable'];
            }

            $invoiceRefundChargesCollection->push(new InvoiceRefundChargeDTO(
                id                 : null,
                uuid               : Str::uuid(),
                amount             : $refundChargeAmount,
                requestStatus      : null,
                invoiceRefundId    : null,
                refundedPaymentId  : $value['internal_id'],
                refundTransactionId: null,
            ));

            $refundAmount = $refundAmount - $refundChargeAmount;
        }

        if (!empty($refundAmount)) throw new Exception("Could not refund full amount");

        return $invoiceRefundChargesCollection;
    }

    /**
     * @param float $customAmount
     * @param array $refundItemIds
     * @return int
     */
    protected function getRefundRequestValue(
        float $customAmount,
        array $refundItemIds
    ): int
    {
        $invoiceItems = $this->invoiceItemRepository->getInvoiceItems($refundItemIds);

        return $customAmount + $invoiceItems->sum(function (InvoiceItem $item) {
                return $item->totalItemPrice();
            });
    }

    /**
     * @param Invoice $invoice
     * @return int
     */
    protected function getInvoiceAmountRefundable(Invoice $invoice): int
    {
        return $invoice->lastSnapshot()->{InvoiceSnapshot::FIELD_TOTAL_PAID};
    }

    /**
     * @param int $invoiceId
     * @return Collection<InvoiceRefund>|array
     */
    protected function getExistingRefundRequests(int $invoiceId): Collection|array
    {
        return $this->refundRepository->getInvoiceRefunds($invoiceId);
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceRefundDTO $refundObject
     * @param string|null $authorType
     * @param int|null $authorId
     * @return void
     * @throws BindingResolutionException
     */
    public function issueRefund(
        Invoice $invoice,
        InvoiceRefundDTO $refundObject,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId,
    ): void
    {
        $this->billingActionApprovalService->requestActionExecution(
            referenceId  : $invoice->{Invoice::FIELD_UUID},
            modelId      : $invoice->{Invoice::FIELD_ID},
            type         : ApprovableActionRelationTypes::INVOICES,
            action       : ApprovableActionType::ISSUE_INVOICE_REFUND,
            requesterType: $authorType,
            requesterId  : $authorId,
            arguments    : [
                "invoiceUuid"  => $invoice->{Invoice::FIELD_UUID},
                "refundObject" => $refundObject->toArray(),
                "authorType"   => $authorType->value,
                "authorId"     => $authorId,
            ],
        );

    }

    /**
     * @param int $page
     * @param int $perPage
     * @param array|null $refundStatus
     * @param int|null $invoiceId
     * @param int|null $minimumValue
     * @param int|null $maximumValue
     * @return LengthAwarePaginator
     */
    public function listInvoiceRefunds(
        int $page,
        int $perPage,
        ?array $refundStatus = null,
        ?int $invoiceId = null,
        ?int $minimumValue = null,
        ?int $maximumValue = null,
    ): LengthAwarePaginator
    {
        return $this->refundRepository->listInvoiceRefunds(
            page        : $page,
            perPage     : $perPage,
            refundStatus: $refundStatus,
            invoiceId   : $invoiceId,
            minimumValue: $minimumValue,
            maximumValue: $maximumValue,
        );
    }

    /**
     * @param Invoice $invoice
     * @return SupportCollection
     */
    public function getInvoiceRefundData(
        Invoice $invoice
    ): SupportCollection
    {
        $data = collect();

        $invoiceRefunds = $this->refundRepository->getInvoiceRefunds(invoiceId: $invoice->id);

        foreach ($invoiceRefunds as $invoiceRefund) {
            $invoiceItemsRefunded = $this->getRefundedInvoiceItems($invoiceRefund);

            $refundedItems = $invoice->itemsToDTO($invoiceItemsRefunded);

            $customAmount = $this->getCustomRefundAmount($invoiceRefund);

            $data->push(new InvoiceRefundDataDTO(
                id           : $invoiceRefund->id,
                items        : $refundedItems,
                custom_amount: $customAmount,
                reason       : $invoiceRefund->reason,
                refunded_at  : $invoiceRefund->created_at,
                uuid         : $invoiceRefund->{InvoiceRefund::FIELD_UUID},
            ));
        }

        return $data;
    }

    /**
     * @param InvoiceRefund $invoiceRefund
     * @return SupportCollection<InvoiceItem>
     */
    public function getRefundedInvoiceItems(InvoiceRefund $invoiceRefund): SupportCollection
    {
        return $invoiceRefund->refundItems->map(function (InvoiceRefundItem $item) {
            return $item->invoiceItem;
        })->flatten()->filter()->values();
    }

    /**
     * @param InvoiceRefund $invoiceRefund
     * @return int|mixed
     */
    public function getCustomRefundAmount(InvoiceRefund $invoiceRefund): mixed
    {
        return $invoiceRefund->refundItems()
            ->whereNull(InvoiceRefundItem::FIELD_INVOICE_ITEM_ID)
            ->sum(InvoiceRefundItem::FIELD_VALUE);
    }

    /**
     * @param Invoice $invoice
     * @return SupportCollection
     */
    public function getRefundableItems(Invoice $invoice): SupportCollection
    {
        $refundable = $invoice->items()->whereDoesntHave(InvoiceItem::RELATION_REFUND_ITEM)->get();

        return $invoice->itemsToDTO($refundable);
    }

    /**
     * @param Invoice $invoice
     * @param array $refundItems
     * @param string $refundReason
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @param int|null $customAmount
     * @param string|null $invoiceRefundUuid
     * @return void
     * @throws BindingResolutionException
     * @throws ValidatorException
     */
    public function validateAndIssueRefundRequest(
        Invoice $invoice,
        array $refundItems,
        string $refundReason,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null,
        ?int $customAmount = 0,
        string $invoiceRefundUuid = null
    ): void
    {
        $invoiceRefundUuid = $invoiceRefundUuid ?: Str::uuid()->toString();

        $this->validateRefundRequest(
            invoice     : $invoice,
            customAmount: $customAmount,
            refundItems : $refundItems
        );

        $invoiceRefund = $this->createInvoiceRefundDTO(
            invoice          : $invoice,
            customAmount     : $customAmount,
            refundItemIds    : $refundItems,
            reason           : $refundReason,
            invoiceRefundUuid: $invoiceRefundUuid
        );

        $this->issueRefund(
            invoice     : $invoice,
            refundObject: $invoiceRefund,
            authorType  : $authorType,
            authorId    : $authorId
        );
    }

}
