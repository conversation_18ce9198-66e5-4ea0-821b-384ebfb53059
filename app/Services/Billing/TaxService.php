<?php

namespace App\Services\Billing;

use App\DTO\Billing\AddressData;
use App\DTO\Billing\InvoiceItemDTO;
use App\Models\Billing\InvoiceItem as InvoiceItemModel;
use App\Jobs\CalculateInvoiceTaxJob;
use App\Models\Billing\Invoice;
use App\Models\Odin\Address;


class TaxService
{
    /**
     * @param Invoice $invoice
     * @param int|null $authorId
     * @return void
     */
    public function calculateTax(
        Invoice $invoice,
        ?int $authorId = null
    ): void
    {
        //todo: invoice should relate to billing policy not company
        /** @var Address $companyAddress */
        $companyAddress = $invoice->company->locations->first()->address;

        $invoiceItems = $invoice->{Invoice::RELATION_INVOICE_ITEMS}
            ->map(fn(InvoiceItemModel $item) => new InvoiceItemDTO(
                invoice_item_id: $item->id,
                invoice_id     : $item->invoice_id,
                billable_id    : $item->billable_id,
                billable_type  : $item->billable_type,
                unit_price     : $item->unit_price,
                quantity       : $item->quantity,
                description    : $item->description,
            )
            )->all();

        $addressDTO = new AddressData(
            line_one : $companyAddress->address_1,
            city     : $companyAddress->city,
            state    : $companyAddress->state,
            post_code: $companyAddress->zip_code,
            country  : $companyAddress->country
        );

        CalculateInvoiceTaxJob::dispatch($addressDTO, $invoiceItems, $invoice->uuid, $authorId);
    }

}
