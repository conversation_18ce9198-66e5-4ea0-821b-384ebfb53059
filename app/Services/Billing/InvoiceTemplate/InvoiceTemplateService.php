<?php

namespace App\Services\Billing\InvoiceTemplate;

use App\Enums\Billing\InvoiceTemplateModelType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTemplate;
use App\Repositories\Billing\InvoiceTemplateRepository;
use App\Services\Billing\InvoicePdfService\InvoicePdfService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\ValidationException;


class InvoiceTemplateService
{
    public function __construct(
        protected InvoiceTemplateRepository $invoiceTemplateRepository,
        protected InvoicePdfService $invoicePdfService,
    )
    {

    }

    /**
     * @return Builder
     */
    public function getListInvoiceTemplatesQuery(): Builder
    {
        return $this->invoiceTemplateRepository->getListInvoiceTemplatesQuery();
    }

    /**
     * @param string $name
     * @param bool $isGlobal
     * @param array $props
     * @param int $createdById
     * @param InvoiceTemplateModelType|null $modelType
     * @param string|null $modelId
     * @return InvoiceTemplate
     * @throws Exception
     */
    public function createInvoiceTemplate(
        string $name,
        bool $isGlobal,
        array $props,
        int $createdById,
        ?InvoiceTemplateModelType $modelType = null,
        ?string $modelId = null,
    ): InvoiceTemplate
    {
        if ($isGlobal && InvoiceTemplate::query()->where(InvoiceTemplate::FIELD_IS_GLOBAL, true)->count() > 0) {
            throw ValidationException::withMessages([
                'message' => 'Only one global invoice template can be created.'
            ]);
        }

        return $this->invoiceTemplateRepository->createInvoiceTemplate(
            name       : $name,
            isGlobal   : $isGlobal,
            props      : $props,
            createdById: $createdById,
            modelType  : $modelType?->getClass(),
            modelId    : $modelId,
        );
    }


    /**
     * @param int $id
     * @param string $name
     * @param bool $isGlobal
     * @param array $props
     * @param InvoiceTemplateModelType|null $modelType
     * @param string|null $modelId
     * @return InvoiceTemplate
     * @throws Exception
     */
    public function updateInvoiceTemplate(
        int $id,
        string $name,
        bool $isGlobal,
        array $props,
        ?InvoiceTemplateModelType $modelType = null,
        ?string $modelId = null,
    ): InvoiceTemplate
    {
        return $this->invoiceTemplateRepository->updateInvoiceTemplate(
            id       : $id,
            name     : $name,
            isGlobal : $isGlobal,
            props    : $props,
            modelType: $modelType?->getClass(),
            modelId  : $modelId,
        );
    }


    /**
     * @param InvoiceTemplate $invoiceTemplate
     * @return bool
     */
    public function deleteInvoiceTemplate(
        InvoiceTemplate $invoiceTemplate
    ): bool
    {
        return $invoiceTemplate->delete();
    }


    /**
     * @throws ValidationException
     */
    public function previewPdfTemplateHtml(InvoiceTemplate $invoiceTemplate): string
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::query()->inRandomOrder()->first();

        return $this->invoicePdfService->previewContentHtml($invoice, $invoiceTemplate);
    }
}



