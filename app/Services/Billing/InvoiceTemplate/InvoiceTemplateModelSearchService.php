<?php

namespace App\Services\Billing\InvoiceTemplate;

use App\DTO\SelectOption;
use App\Enums\Billing\InvoiceTemplateModelType;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;


class InvoiceTemplateModelSearchService
{
    const string PROJECTED_ID   = 'id';
    const string PROJECTED_NAME = 'name';


    /**
     * @param string $query
     * @return Builder
     */
    protected function searchIndustries(string $query): Builder
    {
        return Industry::query()
            ->select([
                Industry::FIELD_ID . ' as ' . self::PROJECTED_ID,
                Industry::FIELD_NAME . ' as ' . self::PROJECTED_NAME,
            ])
            ->where(Industry::FIELD_NAME, 'LIKE', '%' . $query . '%')
            ->orWhere(Industry::FIELD_SLUG, 'LIKE', '%' . $query . '%');
    }

    /**
     * @param string $query
     * @return Builder
     */
    protected function searchIndustryServices(string $query): Builder
    {
        return IndustryService::query()
            ->select(
                IndustryService::TABLE . '.' . IndustryService::FIELD_ID . ' as ' . self::PROJECTED_ID,
                DB::raw('CONCAT(' . Industry::TABLE . '.' . Industry::FIELD_NAME . ', " - ", ' . IndustryService::TABLE . '.' . IndustryService::FIELD_NAME . ') as ' . self::PROJECTED_NAME)
            )
            ->join(Industry::TABLE, Industry::TABLE . '.' . Industry::FIELD_ID, IndustryService::TABLE . '.' . IndustryService::FIELD_INDUSTRY_ID)
            ->having('name', 'like', '%' . $query . '%')
            ->orderBy(Industry::TABLE . '.' . Industry::FIELD_ID);
    }

    /**
     * @param InvoiceTemplateModelType $modelType
     * @param string $query
     * @return SelectOption[]
     */
    public function searchModels(
        InvoiceTemplateModelType $modelType,
        string $query
    ): array
    {
        $dbQuery = match ($modelType) {
            InvoiceTemplateModelType::INDUSTRY         => $this->searchIndustries($query),
            InvoiceTemplateModelType::INDUSTRY_SERVICE => $this->searchIndustryServices($query),
            default                                    => null
        };

        if (empty($dbQuery)) {
            return [];
        };

        return $dbQuery->limit(10)
            ->get()
            ->map(fn($row) => SelectOption::fromArray([
                SelectOption::FIELD_ID   => $row->{self::PROJECTED_ID},
                SelectOption::FIELD_NAME => $row->{self::PROJECTED_NAME}
            ]))->all();
    }
}



