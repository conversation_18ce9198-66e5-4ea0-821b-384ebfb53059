<?php

namespace App\Services\Billing;

use App\Repositories\Billing\InvoiceDispute\InvoiceDisputeRepository;
use Illuminate\Database\Eloquent\Builder;

class InvoiceDisputeService
{
    public function __construct(
        protected InvoiceDisputeRepository $invoiceDisputeRepository,
    )
    {

    }

    /**
     * @param int|null $invoiceId
     * @param array|null $status
     * @param int|null $companyId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return Builder
     */
    public function list(
        ?int $invoiceId = null,
        ?array $status = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ): Builder
    {
        return $this->invoiceDisputeRepository->list(
            invoiceId: $invoiceId,
            status   : $status,
            companyId: $companyId,
            dateFrom : $dateFrom,
            dateTo   : $dateTo,
        );
    }
}



