<?php

namespace App\Services\Billing\PaymentGateway\Events;

class ChargeRefundUpdated extends PaymentGatewayEvent
{
    public function __construct(
        public string $externalRefundId,
        public string $externalChargeId,
        public string $currency,
        public int $amount,
        public ?string $invoiceRefundChargeUuid = null,
        public ?string $invoiceUuid = null,
    )
    {
        parent::__construct($externalRefundId);
    }
}
