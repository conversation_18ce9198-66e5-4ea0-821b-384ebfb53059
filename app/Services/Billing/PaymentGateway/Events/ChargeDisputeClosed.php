<?php

namespace App\Services\Billing\PaymentGateway\Events;

class ChargeDisputeClosed extends PaymentGatewayEvent
{
    public function __construct(
        string $externalTransactionId,
        public string $chargeId,
        public string $status,
        public int $amount,
        public string $currency,
        public string $source,
        public string $reason,
    )
    {
        parent::__construct($externalTransactionId);
    }
}
