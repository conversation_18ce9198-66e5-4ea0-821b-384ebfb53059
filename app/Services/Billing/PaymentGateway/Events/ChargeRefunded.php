<?php

namespace App\Services\Billing\PaymentGateway\Events;

class ChargeRefunded extends PaymentGatewayEvent
{
    public function __construct(
        string $externalTransactionId,
        public int $amount,
        public string $currency,
        public int $amountRefunded,
        public int $amountCaptured,
        public bool $fullyRefunded,
        public string $source,
        public ?string $reason = null,
        public ?string $invoiceUuid = null,
    )
    {
        parent::__construct($externalTransactionId);
    }
}
