<?php

namespace App\Services\Billing\PaymentGateway\Events;

class ChargeDisputeCreated extends PaymentGatewayEvent
{
    public function __construct(
        string $externalTransactionId,
        public string $chargeId,
        public int $amount,
        public string $reason,
        public string $status,
        public string $currency,
        public string $source,
    )
    {
        parent::__construct($externalTransactionId);
    }
}
