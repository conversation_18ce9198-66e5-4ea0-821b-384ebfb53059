<?php

namespace App\Services\Billing\PaymentGateway\Events;

class ChargeSucceeded extends PaymentGatewayEvent
{
    public function __construct(
        string $externalTransactionId,
        public string $invoiceUuid,
        public int $amount,
        public string $currency,
        public string $source,
        public string $date,
        public ?string $invoicePaymentChargeUuid = null,
        public ?string $externalPaymentMethodId = null,
    )
    {
        parent::__construct($externalTransactionId);
    }
}
