<?php

namespace App\Services\Billing\PaymentGateway\Events;

class ChargeFailed extends PaymentGatewayEvent
{
    public function __construct(
        string $externalTransactionId,
        public string $invoiceUuid,
        public string $errorMessage,
        public int $amount,
        public string $currency,
        public string $source,
    )
    {
        parent::__construct($externalTransactionId);
    }
}
