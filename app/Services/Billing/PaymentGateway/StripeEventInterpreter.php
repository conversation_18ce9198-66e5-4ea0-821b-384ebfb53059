<?php

namespace App\Services\Billing\PaymentGateway;

use App\Contracts\Billing\PaymentGatewayEventInterpreterContract;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\PaymentMethodServices;
use App\Repositories\Billing\InvoiceTransactionRepository;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\PaymentGateway\Events\ChargeFailed;
use App\Services\Billing\PaymentGateway\Events\ChargeDisputeClosed;
use App\Services\Billing\PaymentGateway\Events\ChargeDisputeCreated;
use App\Services\Billing\PaymentGateway\Events\ChargeRefunded;
use App\Services\Billing\PaymentGateway\Events\ChargeRefundUpdated;
use App\Services\Billing\PaymentGateway\Events\ChargeSucceeded;
use App\Services\Billing\PaymentGateway\Events\PaymentGatewayEvent;
use Exception;
use Illuminate\Http\Request;
use Stripe\Event;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Webhook;

class StripeEventInterpreter implements PaymentGatewayEventInterpreterContract
{
    const string EVENT_TYPE_CHARGE_FAILED          = 'charge.failed';
    const string EVENT_TYPE_CHARGE_SUCCEEDED       = 'charge.succeeded';
    const string EVENT_TYPE_CHARGE_DISPUTE_CREATED = 'charge.dispute.created';
    const string EVENT_TYPE_CHARGE_DISPUTE_CLOSED  = 'charge.dispute.closed';
    const string EVENT_TYPE_CHARGE_REFUNDED        = 'charge.refunded';
    const string EVENT_TYPE_CHARGE_REFUND_UPDATED  = 'charge.refund.updated';
    const string HEADER_STRIPE_SIGNATURE           = 'stripe-signature';

    protected string $signingSecret;
    const string EVENT_SOURCE = PaymentMethodServices::STRIPE->value;

    public function __construct(
        protected InvoiceTransactionRepository $invoiceTransactionRepository
    )
    {
        $this->signingSecret = config('services.payment_gateways.stripe.webhook_signing_secret');
    }

    /**
     * Check if request has uuid or charge id exists. If it meets the criteria, it's a v2 event
     * @param string|null $chargeId
     * @param string|null $invoiceUuid
     * @return false
     */
    protected function isV2(
        ?string $chargeId = null,
        ?string $invoiceUuid = null,
    ): bool
    {
        $hasInvoiceUuid = filled($invoiceUuid);

        if ($hasInvoiceUuid) {
            return true;
        }

        if (empty($chargeId)) {
            return false;
        }

        return (bool)$this->invoiceTransactionRepository->getInvoiceTransactionByExternalId(
            externalTransactionId: $chargeId
        );
    }

    /**
     * @param Request $request
     * @return PaymentGatewayEvent|null
     * @throws SignatureVerificationException
     */
    public function interpret(Request $request): ?PaymentGatewayEvent
    {
        $content = $request->getContent();
        $signature = $request->header(self::HEADER_STRIPE_SIGNATURE);

        $incomingEvent = $this->buildEvent($content, $signature);

        if (!$this->isV2(
            chargeId   : $incomingEvent->data->object->charge ?: $incomingEvent->data->object->id,
            invoiceUuid: $incomingEvent->data->object->metadata?->invoiceUuid,
        )) {
            BillingLogService::log(
                message  : 'Legacy event ignored by A20',
                namespace: 'payment_gateway_event_interpreter',
                context  : $request->all(),
            );

            return null;
        }

        $parsedEvent = $this->parseEvent($incomingEvent);

        if (empty($parsedEvent)) {
            BillingLogService::log(
                message  : 'Unknown payment gateway event',
                level    : BillingLogLevel::WARNING,
                namespace: 'payment_gateway_event_interpreter',
                context  : $request->all(),
            );
        }

        return $parsedEvent;
    }

    /**
     * @param Event $event
     * @return PaymentGatewayEvent|null
     */
    protected function parseEvent(Event $event): ?PaymentGatewayEvent
    {
        $externalTransactionId = $event->data->object->id;
        $invoiceUuid = $event->data->object->metadata->invoiceUuid;

        return match ($event->type) {
            self::EVENT_TYPE_CHARGE_FAILED          => new ChargeFailed(
                externalTransactionId: $externalTransactionId,
                invoiceUuid          : $invoiceUuid,
                errorMessage         : $event->data->object->failure_message,
                amount               : $event->data->object->amount,
                currency             : $event->data->object->currency,
                source               : self::EVENT_SOURCE,
            ),
            self::EVENT_TYPE_CHARGE_SUCCEEDED       => new ChargeSucceeded(
                externalTransactionId   : $externalTransactionId,
                invoiceUuid             : $invoiceUuid,
                amount                  : $event->data->object->amount,
                currency                : $event->data->object->currency,
                source                  : self::EVENT_SOURCE,
                date                    : now()->toISOString(),
                invoicePaymentChargeUuid: $event->data->object->metadata->invoicePaymentChargeUuid,
                externalPaymentMethodId : $event->data->object->payment_method
            ),
            self::EVENT_TYPE_CHARGE_DISPUTE_CREATED => new ChargeDisputeCreated(
                externalTransactionId: $externalTransactionId,
                chargeId             : $event->data->object->charge,
                amount               : $event->data->object->amount,
                reason               : $event->data->object->reason,
                status               : $event->data->object->status,
                currency             : $event->data->object->currency,
                source               : self::EVENT_SOURCE,
            ),
            self::EVENT_TYPE_CHARGE_DISPUTE_CLOSED  => new ChargeDisputeClosed(
                externalTransactionId: $externalTransactionId,
                chargeId             : $event->data->object->charge,
                status               : $event->data->object->status,
                amount               : $event->data->object->amount,
                currency             : $event->data->object->currency,
                source               : self::EVENT_SOURCE,
                reason               : $event->data->object->reason
            ),
            self::EVENT_TYPE_CHARGE_REFUNDED        => new ChargeRefunded(
                externalTransactionId: $externalTransactionId,
                amount               : $event->data->object->amount,
                currency             : $event->data->object->currency,
                amountRefunded       : $event->data->object->amount_refunded,
                amountCaptured       : $event->data->object->amount_captured,
                fullyRefunded        : $event->data->object->refunded,
                source               : self::EVENT_SOURCE,
                reason               : $event->data->object->reason,
                invoiceUuid          : $invoiceUuid
            ),
            self::EVENT_TYPE_CHARGE_REFUND_UPDATED  => new ChargeRefundUpdated(
                externalRefundId       : $event->data->object->id,
                externalChargeId       : $event->data->object->charge,
                currency               : $event->data->object->currency,
                amount                 : $event->data->object->amount,
                invoiceRefundChargeUuid: $event->data->object->metadata?->invoiceRefundChargeUuid,
                invoiceUuid            : $event->data->object->metadata?->invoiceUuid,
            ),
            default                                 => null,
        };
    }

    /**
     * @param string $payload
     * @param string $signature
     * @return Event
     * @throws SignatureVerificationException
     * @throws Exception
     */
    protected function buildEvent(string $payload, string $signature): Event
    {
        if (empty($payload)) {
            throw new Exception("Stripe webhook payload is empty");
        } else if (empty($signature)) {
            throw new Exception("Stripe webhook signature is empty");
        } else if (empty($this->signingSecret)) {
            throw new Exception("Stripe webhook signing secret is empty");
        }

        return Webhook::constructEvent(
            $payload,
            $signature,
            $this->signingSecret
        );
    }
}
