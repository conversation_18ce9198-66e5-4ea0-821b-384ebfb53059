<?php

namespace App\Services\Billing;

use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\RoleType;
use App\Models\Billing\InvoiceItem;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\User;
use Illuminate\Contracts\Container\BindingResolutionException;

class BillingActionApprovalService
{
    /**
     * @param ActionApprovalService $actionApprovalService
     */
    public function __construct(
        protected ActionApprovalService $actionApprovalService
    )
    {

    }

    /**
     * @param InvoiceEventAuthorTypes $requesterType
     * @param int|null $requesterId
     * @return bool
     */
    protected function isRequesterFinanceOwner(
        InvoiceEventAuthorTypes $requesterType,
        ?int $requesterId = null,
    ): bool
    {
        return $requesterType === InvoiceEventAuthorTypes::USER
            && filled($requesterId)
            && User::query()
                ->where(User::FIELD_ID, $requesterId)
                ->first()
                ?->hasAnyRole([
                    RoleType::FINANCE_OWNER->value
                ]);
    }

    /**
     * @param InvoiceEventAuthorTypes $requesterType
     * @param int|null $requesterId
     * @param int|null $modelId
     * @param ApprovableActionRelationTypes|null $type
     * @return bool
     */
    protected function shouldBypassApproval(
        InvoiceEventAuthorTypes $requesterType,
        ?int $requesterId = null,
        ?int $modelId = null,
        ?ApprovableActionRelationTypes $type = null,
    ): bool
    {
        $isFinancialOwner = $this->isRequesterFinanceOwner(
            requesterType: $requesterType,
            requesterId  : $requesterId,
        );

        $isSystem = $requesterType === InvoiceEventAuthorTypes::SYSTEM;

        if ($isFinancialOwner || $isSystem) {
            return true;
        }

        if ($type === ApprovableActionRelationTypes::INVOICES && filled($modelId)) {
            $invoiceItems = InvoiceItem::query()
                ->where(InvoiceItem::FIELD_INVOICE_ID, $modelId)
                ->get();

            $unique = $invoiceItems->count() === 1;

            if (!$unique) {
                return false;
            }

            $bundleInvoiceItem = $invoiceItems->first(fn(InvoiceItem $item) => $item->{InvoiceItem::FIELD_BILLABLE_TYPE} === Bundle::class);

            if ($bundleInvoiceItem) {
                $bundleInvoice = BundleInvoice::query()
                    ->with(BundleInvoice::RELATION_BUNDLE)
                    ->findOrFail($bundleInvoiceItem->{InvoiceItem::FIELD_BILLABLE_ID});

                return $bundleInvoice->bundle->is_auto_approved;
            }
        }

        return false;
    }

    /**
     * @param string $referenceId
     * @param int $modelId
     * @param ApprovableActionRelationTypes $type
     * @param ApprovableActionType $action
     * @param InvoiceEventAuthorTypes $requesterType
     * @param int|null $requesterId
     * @param array $arguments
     * @param bool $enforceSingleApprovalPending
     * @param string|null $note
     * @return void
     * @throws BindingResolutionException
     */
    public function requestActionExecution(
        string $referenceId,
        int $modelId,
        ApprovableActionRelationTypes $type,
        ApprovableActionType $action,
        InvoiceEventAuthorTypes $requesterType,
        ?int $requesterId = null,
        array $arguments = [],
        bool $enforceSingleApprovalPending = true,
        ?string $note = null,
    ): void
    {
        $bypassApproval = $this->shouldBypassApproval(
            requesterType: $requesterType,
            requesterId  : $requesterId,
            modelId      : $modelId,
            type         : $type
        );

        $this->actionApprovalService->requestActionExecution(
            referenceId                 : $referenceId,
            modelId                     : $modelId,
            type                        : $type,
            action                      : $action,
            requestedBy                 : $requesterId,
            bypassApproval              : $bypassApproval,
            arguments                   : $arguments,
            enforceSingleApprovalPending: $enforceSingleApprovalPending,
            note                        : $note
        );
    }
}



