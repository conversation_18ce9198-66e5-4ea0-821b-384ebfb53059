<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\InvoiceBalanceReportBuilder;
use App\Enums\Billing\InvoiceReportsGrouping;
use Illuminate\Database\Eloquent\Builder;
use \Illuminate\Database\Query\Builder as QueryBuilder;

class InvoiceBalanceReportService
{
    /**
     * Wrap the query in a sub query, so we can perform a sort
     * after the data is grouped
     *
     * @param InvoiceReportsGrouping $groupedBy
     * @param int|null $onboardingManagerUserId
     * @param int|null $accountManagerUserId
     * @param int|null $businessDevelopmentManagerUserId
     * @param array|null $industryIds
     * @param int|null $companyId
     * @param array|null $sortBy
     *
     * @return Builder|QueryBuilder
     */
    public function getReport(
        InvoiceReportsGrouping $groupedBy,
        ?int $onboardingManagerUserId = null,
        ?int $accountManagerUserId = null,
        ?int $businessDevelopmentManagerUserId = null,
        ?array $industryIds = [],
        ?int $companyId = null,
        ?array $sortBy = null
    ): Builder|QueryBuilder
    {
        return InvoiceBalanceReportBuilder::query()
            ->forCompanyId($companyId)
            ->forCompanyUserRelationship(
                onboardingManagerUserId: $onboardingManagerUserId,
                accountManagerUserId: $accountManagerUserId,
                businessDevelopmentManagerUserId: $businessDevelopmentManagerUserId,
            )
            ->forIndustry($industryIds)
            ->groupBy($groupedBy->value)
            ->getQuery($sortBy ?? []);
    }
}



