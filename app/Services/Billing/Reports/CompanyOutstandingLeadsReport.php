<?php

namespace App\Services\Billing\Reports;

use App\Models\Billing\BillingProfile;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ProductAssignmentRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;


class CompanyOutstandingLeadsReport
{
    protected array $sortColumnsMap = [
        'total_unrejectable'   => 'total_unrejectable',
        'total_to_be_invoiced' => 'total_to_be_invoiced',
        'max_delivered_at'     => 'max_delivered_at',
        'min_delivered_at'     => 'min_delivered_at',
        'company_id'           => 'company_id',
        'company_name'         => 'company_name',
    ];

    public function __construct(protected Builder $query)
    {

    }

    public function sortBy(array $sortBy): static
    {
        $fieldDirections = collect($sortBy)
            ->map(fn($item) => explode(':', $item));

        foreach ($fieldDirections as [$field, $direction]) {
            $colField = Arr::get($this->sortColumnsMap, $field);

            if (filled($colField)) {
                $this->query->orderBy($colField, $direction);
            }
        }

        return $this;
    }

    /**
     * @param Collection $items
     * @return Collection
     */
    protected function addBillingProfileData(Collection $items): Collection
    {
        $uniqueCompanyIds = $items->pluck('company_id')->unique();

        $billingProfiles = BillingProfile::query()
            ->whereIn(BillingProfile::TABLE . '.' . BillingProfile::FIELD_COMPANY_ID, $uniqueCompanyIds)
            ->whereNull(BillingProfile::TABLE . '.' . BillingProfile::FIELD_DELETED_AT)
            ->get()
            ->mapToGroups(fn($item) => [
                $item->company_id => $item
            ]);

        return $items->map(function ($item) use ($billingProfiles) {
            $item->billing_profiles = $billingProfiles->get($item->company_id);
            return $item;
        });
    }

    /**
     * @param int $page
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function paginate(int $page, int $perPage): LengthAwarePaginator
    {
        $paginated = $this->query->paginate(
            perPage: $perPage,
            page   : $page
        );
        $items = $this->addBillingProfileData(collect($paginated->items()));

        return new LengthAwarePaginator(
            items      : $items,
            total      : $paginated->total(),
            perPage    : $paginated->perPage(),
            currentPage: $paginated->currentPage(),
        );
    }

    /**
     * @return self
     * @throws BindingResolutionException
     */
    public static function query(): self
    {
        /** @var ProductAssignmentRepository $productAssignmentRepository */
        $productAssignmentRepository = app()->make(ProductAssignmentRepository::class);

        $query = $productAssignmentRepository->getChargeableUninvoicedProductAssignmentsQuery(
            excludeRejectable: false
        )
            ->select([
                    DB::raw('SUM(CASE WHEN product_assignments.rejection_expiry < CURRENT_TIME() THEN product_assignments.cost ELSE 0 END) AS total_unrejectable'),
                    DB::raw('SUM(product_assignments.cost) AS total_to_be_invoiced'),
                    DB::raw('MAX(product_assignments.delivered_at) AS max_delivered_at'),
                    DB::raw('MIN(product_assignments.delivered_at) AS min_delivered_at'),
                    Company::TABLE . '.' . Company::FIELD_ID . ' AS company_id',
                    Company::TABLE . '.' . Company::FIELD_NAME . ' AS company_name',
                ]
            )
            ->join(
                Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID
            )
            ->groupBy(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID);

        return new self($query);
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): static
    {
        if (filled($companyId)) {
            $this->query->where(Company::TABLE . '.' . Company::FIELD_ID, $companyId);
        }
        return $this;
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        return $this->query;
    }
}




