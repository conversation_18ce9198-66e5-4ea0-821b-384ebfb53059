<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\OverdueInvoicesReportBuilder;
use App\Enums\Billing\InvoiceReportsGrouping;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class OverdueInvoicesReportService
{
    public function getReportQuery(
        ?User $user = null,
        ?string $date = null,
        ?array $sortBy = [],
        ?string $paymentStatus = null,
        ?int $companyId = null,
        ?int $invoiceId = null,
        ?string $paymentMethod = null,
        ?string $reference = null
    ): Builder
    {
        return OverdueInvoicesReportBuilder::query(
            date     : $date,
            reference: $reference
        )
            ->joinCompany()
            ->filterByRole($user)
            ->sortBy($sortBy)
            ->forCompanyId($companyId)
            ->forInvoiceId($invoiceId)
            ->forPaymentStatus($paymentStatus)
            ->forPaymentMethod($paymentMethod)
            ->getQuery();
    }
}




