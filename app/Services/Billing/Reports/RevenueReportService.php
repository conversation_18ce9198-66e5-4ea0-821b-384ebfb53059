<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\RevenueReportBuilder;
use App\Enums\SortOrder;
use App\Models\Billing\InvoiceTransaction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Collection;

class RevenueReportService
{
    /**
     * @param array|null $dateRange
     * @param int|null $companyId
     * @param array|null $scenarios
     * @param array|null $types
     * @param array|null $sortBy
     * @return array
     */
    public function getRevenueReport(
        ?array $dateRange = [],
        ?int $companyId = null,
        ?array $scenarios = null,
        ?array $types = null,
        ?array $sortBy = []
    ): array
    {
        $sortBy = filled($sortBy) ? $sortBy : [InvoiceTransaction::FIELD_DATE . ':' . SortOrder::DESC->value];

        [$dateFrom, $dateTo] = $dateRange;

        $query = RevenueReportBuilder::query()
            ->forDateRange(
                dateFrom: $dateFrom,
                dateTo  : $dateTo,
            )
            ->forCompanyId($companyId)
            ->forScenarios($scenarios)
            ->forTypes($types)
            ->sortBy($sortBy)
            ->getQuery();

        $totals = $this->calculateAggregates($query);

        return [
            'query'      => $query,
            'aggregates' => $totals,
        ];
    }

    /**
     * @param Builder|QueryBuilder $query
     * @return Collection
     */
    private function calculateAggregates(Builder|QueryBuilder $query): Collection
    {
        return $query->get()
            ->groupBy([InvoiceTransaction::FIELD_TYPE, InvoiceTransaction::FIELD_SCENARIO])
            ->mapWithKeys(function ($groupedByType, $type) {
                $totals = collect([$type => 0.0]);

                foreach ($groupedByType as $scenario => $data) {
                    $amountSum = $data->sum('amount') / 100;
                    $totals[$type] += $amountSum;

                    if (!empty($scenario)) {
                        $typeScenarioKey = $type . '_' . $scenario;
                        $totals[$typeScenarioKey] = ($totals[$typeScenarioKey] ?? 0) + $amountSum;
                    }
                }

                return $totals;
            });
    }

}



