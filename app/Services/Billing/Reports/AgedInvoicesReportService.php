<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\AgedInvoicesBuilder;
use App\Enums\Billing\InvoiceReportsGrouping;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class AgedInvoicesReportService
{
    /**
     * @param InvoiceReportsGrouping $groupedBy
     * @param User|null $user
     * @param int|null $onboardingManagerUserId
     * @param int|null $accountManagerUserId
     * @param int|null $businessDevelopmentManagerUserId
     * @param array|null $industryIds
     * @param int|null $companyId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @param array|null $sortBy
     *
     * @return Builder
     */
    public function getReport(
        string $groupedBy,
        ?User $user = null,
        ?int $onboardingManagerUserId = null,
        ?int $accountManagerUserId = null,
        ?int $businessDevelopmentManagerUserId = null,
        ?array $industryIds = [],
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?array $sortBy = []
    ): Builder
    {
        return AgedInvoicesBuilder::query(dateFrom: $dateFrom, dateTo: $dateTo)
            ->forCompanyUserRelationship(
                onboardingManagerUserId: $onboardingManagerUserId,
                accountManagerUserId: $accountManagerUserId,
                businessDevelopmentManagerUserId: $businessDevelopmentManagerUserId,
            )
            ->forCompanyId(companyId: $companyId)
            ->forIndustry(industryId: $industryIds)
            ->forDateRange(
                dateFrom: $dateFrom,
                dateTo  : $dateTo
            )
            ->filterByRole(
                user: $user
            )
            ->groupBy($groupedBy)
            ->sortBy($sortBy)
            ->getQuery();
    }
}



