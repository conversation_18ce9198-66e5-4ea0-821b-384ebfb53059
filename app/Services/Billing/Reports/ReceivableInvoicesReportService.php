<?php

namespace App\Services\Billing\Reports;

use App\Builders\Billing\Report\ReceivableInvoicesReportBuilder;
use App\Enums\Billing\InvoiceReportsGrouping;
use App\Enums\SortOrder;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\User;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ReceivableInvoicesReportService
{
    /**
     * @param InvoiceReportsGrouping $grouping
     * @param User|null $user
     * @param int|null $companyId
     * @param string|null $date
     * @param int|null $onboardingManagerUserId
     * @param int|null $accountManagerUserId
     * @param int|null $businessDevelopmentManagerUserId
     * @param string|null $invoiceStatus
     * @param array|null $sortBy
     * @param array|null $industryIds
     *
     * @return array
     * @throws Exception
     */
    public function getReceivableInvoicesReport(
        string $grouping,
        ?User $user = null,
        ?int $companyId = null,
        ?string $date = null,
        ?int $onboardingManagerUserId = null,
        ?int $accountManagerUserId = null,
        ?int $businessDevelopmentManagerUserId = null,
        ?string $invoiceStatus = null,
        ?array $sortBy = null,
        ?array $industryIds = [],
    ): array
    {
        $sortBy = filled($sortBy) ? $sortBy : [
            InvoiceSnapshot::FIELD_DATE . ':' . SortOrder::DESC->value
        ];

        $query = ReceivableInvoicesReportBuilder::query(
            date    : $date,
        )
            ->forInvoiceStatus($invoiceStatus)
            ->forCompanyId($companyId)
            ->forDateRange(
                dateTo: $date,
            )
            ->forCompanyUserRelationship(
                onboardingManagerUserId         : $onboardingManagerUserId,
                accountManagerUserId            : $accountManagerUserId,
                businessDevelopmentManagerUserId: $businessDevelopmentManagerUserId
            )
            ->forIndustry(
                industryId: $industryIds
            )
            ->filterByRole(
                user: $user
            )
            ->sortBy($sortBy);

        $totalOutstandingInCents = $query
            ->getQuery()
            ->newQuery()
            ->sum(DB::raw('
                CASE
                    WHEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ' > 0
                    THEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . '
                    WHEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK . ' > 0
                    THEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK . '
                    ELSE 0
                END
            '));

        return [
            'query'                      => $query->groupBy($grouping)->getQuery(),
            'total_outstanding_in_cents' => $totalOutstandingInCents,
        ];
    }

    /**
     * @param Builder $query
     * @param array $sortBy
     * @param array $sortColumns
     * @return Builder
     */
    protected function applySort(Builder $query, array $sortBy, array $sortColumns = []): Builder
    {
        $fieldDirections = collect($sortBy)
            ->map(fn($item) => explode(':', $item));

        foreach ($fieldDirections as [$field, $direction]) {
            $colField = Arr::get($sortColumns, $field, $field);
            $query->orderBy($colField, $direction);
        }

        return $query;
    }
}



