<?php

namespace App\Services\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\DTO\Billing\InvoiceItemDTO;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceItemTypes;
use App\Models\Billing\InvoiceItem;
use Illuminate\Support\Collection;

class InvoiceItemService
{
    /**
     * @param string $invoiceUuid
     * @param InvoiceItemDTO[] $invoiceItems
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     */
    public function addInvoiceItems(
        string $invoiceUuid,
        array $invoiceItems,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId,
    ): void
    {
        foreach ($invoiceItems as $newItem) {
            $this->addInvoiceItem($invoiceUuid, $newItem, $authorType, $authorId);
        }
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceItemDTO $item
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     */
    public function addInvoiceItem(string $invoiceUuid, InvoiceItemDTO $item, InvoiceEventAuthorTypes $authorType, ?int $authorId): void
    {
        InvoiceAggregateRoot::retrieve($invoiceUuid)
            ->addInvoiceItem(
                invoiceUuid : $invoiceUuid,
                quantity    : $item->getQuantity(),
                unitPrice   : $item->getUnitPrice(),
                billableType: $item->getBillableType(),
                description : $item->getDescription(),
                authorType  : $authorType->value,
                billableId  : $item->getBillableId(),
                authorId    : $authorId,
            )->persist();
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceItem[] $invoiceItems
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     */
    public function deleteInvoiceItems(
        string $invoiceUuid,
        array $invoiceItems,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId,
    ): void
    {
        foreach ($invoiceItems as $deletedItem) {
            $this->deleteInvoiceItem(
                invoiceUuid: $invoiceUuid,
                invoiceItem: $deletedItem,
                authorType : $authorType,
                authorId   : $authorId,
            );
        }
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceItem $invoiceItem
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     */
    public function deleteInvoiceItem(
        string $invoiceUuid,
        InvoiceItem $invoiceItem,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId,
    ): void
    {
        InvoiceAggregateRoot::retrieve($invoiceUuid)
            ->deleteInvoiceItem(
                invoiceItemId: $invoiceItem->id,
                authorType   : $authorType->value,
                authorId     : $authorId,
            )->persist();
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceItem[] $existingItems
     * @param Collection<InvoiceItemDTO> $newItems
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     */
    public function updateInvoiceItems(
        string $invoiceUuid,
        array $existingItems,
        Collection $newItems,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId,
    ): void
    {
        //existing items that might have changed
        foreach ($existingItems as $item) {
            /** @var InvoiceItemDTO $incomingItem */
            $incomingItem = $newItems->first(fn(InvoiceItemDTO $invoiceItem) => $invoiceItem->getInvoiceItemId() === $item->{InvoiceItem::FIELD_ID});

            $this->updateInvoiceItem(
                invoiceUuid : $invoiceUuid,
                existingItem: $item,
                newItem     : $incomingItem,
                authorType  : $authorType,
                authorId    : $authorId
            );
        }
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceItem $existingItem
     * @param InvoiceItemDTO $newItem
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     */
    public function updateInvoiceItem(
        string                  $invoiceUuid,
        InvoiceItem             $existingItem,
        InvoiceItemDTO          $newItem,
        InvoiceEventAuthorTypes $authorType,
        ?int                    $authorId,
    ): void
    {
        $existingItem->fill([
            InvoiceItem::FIELD_DESCRIPTION => $newItem->getDescription(),
            InvoiceItem::FIELD_QUANTITY    => $newItem->getQuantity(),
            InvoiceItem::FIELD_UNIT_PRICE  => $newItem->getUnitPrice(),
        ]);

        $changed = $existingItem->getDirty();

        $changes = [];

        foreach ($changed as $key => $value) {
            $original = $existingItem->getOriginal($key);
            if ($original !== $value) {
                $changes[] = [
                    'key' => $key,
                    'old' => $original,
                    'new' => $value,
                ];
            }
        }

        if (!collect($changes)->isEmpty()) {
            InvoiceAggregateRoot::retrieve($invoiceUuid)->updateInvoiceItem(
                invoiceItemId: $existingItem->id,
                changes      : $changes,
                authorType   : $authorType->value,
                authorId     : $authorId,
            )->persist();
        }
    }

    /**
     * TODO - Remove this function and always work with Collection of dto
     * @param Collection $invoiceItems
     * @return Collection
     */
    public function formatInvoiceItems(Collection $invoiceItems): Collection
    {
        return $invoiceItems
            ->map(function (array|InvoiceItemDTO $invoiceItem) {
                if ($invoiceItem instanceof InvoiceItemDTO) {
                    return $invoiceItem;
                }

                return InvoiceItemDTO::fromArray($invoiceItem);
            });
    }

    /**
     * @param string $invoiceUuid
     * @param Collection<InvoiceItem> $existingItems
     * @param Collection $receivedItems
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     */
    public function syncInvoiceItems(
        string $invoiceUuid,
        Collection $existingItems,
        Collection $receivedItems,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId,
    ): void
    {
        /** @var Collection<InvoiceItemDTO> $incomingInvoiceItems */
        $incomingInvoiceItems = $this->formatInvoiceItems($receivedItems);

        $existingIds = $existingItems->pluck('id');
        $incomingIds = $incomingInvoiceItems->map(fn(InvoiceItemDTO $item) => $item->getInvoiceItemId());

        /** @var InvoiceItem[] $deleted */
        $deleted = $existingItems->filter(fn(InvoiceItem $invoiceItem) => !in_array($invoiceItem->id, $incomingIds->all()))->all();
        /** @var InvoiceItemDTO[] $added */
        $added = $incomingInvoiceItems->filter(fn(InvoiceItemDTO $item) => empty($item->getInvoiceItemId()))->all();

        $intersectingIds = $existingIds->intersect($incomingIds);

        $incomingUpdated = $incomingInvoiceItems->filter(fn(InvoiceItemDTO $item) => !empty($item->getInvoiceItemId()));
        /** @var InvoiceItem[] $existingUpdated */
        $existingUpdated = $existingItems->filter(fn(InvoiceItem $invoiceItem) => in_array($invoiceItem->id, $intersectingIds->all()))->all();

        //new items
        $this->addInvoiceItems(
            invoiceUuid : $invoiceUuid,
            invoiceItems: $added,
            authorType  : $authorType,
            authorId    : $authorId,
        );

        //deleted items
        $this->deleteInvoiceItems(
            invoiceUuid : $invoiceUuid,
            invoiceItems: $deleted,
            authorType  : $authorType,
            authorId    : $authorId,
        );

        //existing items that might have changed
        $this->updateInvoiceItems(
            invoiceUuid  : $invoiceUuid,
            existingItems: $existingUpdated,
            newItems     : $incomingUpdated,
            authorType   : $authorType,
            authorId     : $authorId,
        );
    }

    public function getInvoiceItemsByInvoiceIds(array $invoiceIds): Collection
    {
        return InvoiceItem::query()->whereIn(InvoiceItem::FIELD_INVOICE_ID, $invoiceIds)->get();
    }

    public function groupInvoiceItemsByType(Collection $invoiceItems): Collection
    {
        $grouped = [];

        foreach (InvoiceItemTypes::cases() as $itemType) {

            if (!isset($grouped[$itemType->value])) {
                $grouped[$itemType->value] = [
                    'total' => 0,
                    'type' => $itemType->value
                ];
            }

            $grouped[$itemType->value]['total'] = $invoiceItems->filter(function ($item) use ($itemType) {
                return InvoiceItemTypes::fromClass($item->{InvoiceItem::FIELD_BILLABLE_TYPE}) === $itemType;
            })->sum(function ($item) {
                return $item->{InvoiceItem::FIELD_QUANTITY} * $item->{InvoiceItem::FIELD_UNIT_PRICE};
            });
        }

        return collect($grouped)->values();
    }
}
