<?php

namespace App\Services\Billing\InvoiceCollections;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\InvoiceCollectionsRecoveryStatus;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceCollections;
use App\Repositories\Billing\InvoiceCollectionsRepository;
use App\Services\Billing\BillingActionApprovalService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class InvoiceCollectionsService
{
    public function __construct(
        protected BillingActionApprovalService $billingActionApprovalService,
        protected InvoiceCollectionsRepository $invoiceCollectionsRepository
    )
    {

    }

    /**
     * @param Invoice $invoice
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     * @throws BindingResolutionException
     */
    public function issueInvoiceToCollections(
        Invoice $invoice,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null,
    ): void
    {
        $amountCollected = $invoice->getTotalOutstanding();

        if (empty($amountCollected)) {
            throw new Exception('Cannot send paid invoices to collections');
        }

        $this->billingActionApprovalService->requestActionExecution(
            referenceId  : $invoice->{Invoice::FIELD_UUID},
            modelId      : $invoice->{Invoice::FIELD_ID},
            type         : ApprovableActionRelationTypes::INVOICES,
            action       : ApprovableActionType::ISSUE_INVOICE_TO_COLLECTIONS,
            requesterType: $authorType,
            requesterId  : $authorId,
            arguments    : [
                "uuid"            => Str::uuid()->toString(),
                "invoiceUuid"     => $invoice->{Invoice::FIELD_UUID},
                "authorType"      => $authorType->value,
                "sentDate"        => Carbon::now()->toISOString(),
                "recoveryStatus"  => InvoiceCollectionsRecoveryStatus::PENDING->value,
                "amountCollected" => $amountCollected,
                "authorId"        => $authorId,
            ],
        );
    }

    /**
     * @param Invoice $invoice
     * @param string $authorType
     * @param float $amountRecovered
     * @param string $recoveryStatus
     * @param int|null $authorId
     * @return void
     * @throws BindingResolutionException
     */
    public function updateInvoiceCollections(
        Invoice $invoice,
        string $authorType,
        float $amountRecovered,
        string $recoveryStatus,
        ?int $authorId = null,
    ): void
    {
        $invoiceCollections = $invoice->lastCollections();

        if (!$invoiceCollections) {
            throw new Exception('Collections not found');
        }

        if ($amountRecovered > $invoiceCollections->{InvoiceCollections::FIELD_AMOUNT_COLLECTED}) {
            throw new Exception('Cannot recover more than the amount collected');
        }

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        $invoiceAggregateRoot->collection->updateInvoiceCollections(
            uuid           : $invoiceCollections->{InvoiceCollections::FIELD_UUID},
            invoiceUuid    : $invoice->{Invoice::FIELD_UUID},
            authorType     : $authorType,
            recoveryStatus : $recoveryStatus,
            amountRecovered: $amountRecovered,
            amountLost     : $invoiceCollections->{InvoiceCollections::FIELD_AMOUNT_COLLECTED} - $amountRecovered,
            recoveryDate   : Carbon::now()->toISOString(),
            authorId       : $authorId,
        );

        $invoiceAggregateRoot->persist();
    }

    /**
     * @param int|null $invoiceId
     * @param int|null $companyId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @param array|null $status
     * @return Builder
     */
    public function list(
        ?int $invoiceId = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?array $status = null
    ): Builder
    {
        return $this->invoiceCollectionsRepository->list(
            invoiceId: $invoiceId,
            companyId: $companyId,
            dateFrom : $dateFrom,
            dateTo   : $dateTo,
            status   : $status
        );
    }
}
