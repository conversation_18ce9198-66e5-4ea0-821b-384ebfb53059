<?php

namespace App\Services\Billing\ReviewableAction;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Exceptions\Billing\InvoicePaymentOverchargeException;
use App\Models\Billing\Invoice;
use App\Services\Billing\InvoicePaymentService;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Validation\ValidationException;

class MakeInvoicePaymentApprovableAction extends ReviewableAction
{
    public function __construct(
        protected InvoicePaymentService $invoicePaymentService,
    )
    {

    }

    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     * @throws InvoicePaymentOverchargeException
     * @throws ValidationException
     */
    public function onApproval(array $arguments): void
    {
        [
            "invoiceId"        => $invoiceId,
            "amount"           => $amount,
            "authorType"       => $authorType,
            "billingProfileId" => $billingProfileId,
            "authorId"         => $authorId,
            "date"             => $date,
        ] = $arguments;

        $invoice = Invoice::query()->findOrFail($invoiceId);

        $this->invoicePaymentService->payInvoice(
            invoice         : $invoice,
            amount          : $amount,
            authorType      : InvoiceEventAuthorTypes::tryFrom($authorType),
            billingProfileId: $billingProfileId,
            authorId        : $authorId,
            date            : Carbon::parse($date ?? now()->toString())
        );
    }
}
