<?php

namespace App\Services\Billing\ReviewableAction;

use App\Models\Billing\BillingProfile;

class ToggleBillingProfileVisibility extends ReviewableAction
{
    /**
     * @param array $arguments
     * @return void
     */
    public function onApproval(array $arguments)
    {
        [
            'billingProfileId' => $billingProfileId,
            'authorId'         => $authorId,
            'date'             => $date
        ] = $arguments;

        $billingProfile = BillingProfile::query()->findOrFail($billingProfileId);

        $shouldArchive = !$billingProfile->{BillingProfile::FIELD_ARCHIVED_AT};

        $billingProfile->update([
            BillingProfile::FIELD_ARCHIVED_BY => $shouldArchive ? $authorId : null,
            BillingProfile::FIELD_ARCHIVED_AT => $shouldArchive ? $date : null,
        ]);
    }
}
