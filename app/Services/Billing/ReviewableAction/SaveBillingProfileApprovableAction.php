<?php

namespace App\Services\Billing\ReviewableAction;

use App\Enums\Billing\BillingProfileFrequenciesEnum;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Services\Billing\BillingProfile\BillingProfileService;
use Illuminate\Support\Arr;

class SaveBillingProfileApprovableAction extends ReviewableAction
{
    const int DEFAULT_DUE_IN_DAYS = 0;

    public function __construct(protected BillingProfileService $billingProfileService)
    {

    }

    /**
     * @param array $arguments
     * @return void
     */
    public function onApproval(array $arguments): void
    {
        $paymentMethod = Arr::get($arguments, 'payment_method');
        $companyId = Arr::get($arguments, 'company_id');
        $default = Arr::get($arguments, 'default');
        $thresholdInDollars = Arr::get($arguments, 'threshold_in_dollars');
        $frequencyType = Arr::get($arguments, 'frequency_type');
        $contact = Arr::get($arguments, 'contact');
        $createdById = Arr::get($arguments, 'created_by_id');
        $maxAllowedChargeAttempts = Arr::get($arguments, 'max_allowed_charge_attempts');
        $frequencyData = Arr::get($arguments, 'frequency_data');
        $associatedCampaignIds = Arr::get($arguments, 'associated_campaign_ids');
        $createdByType = Arr::get($arguments, 'created_by_type');
        $processAuto = Arr::get($arguments, 'process_auto');
        $billingProfileId = Arr::get($arguments, 'billing_profile_id');
        $updatedById = Arr::get($arguments, 'updated_by_id');
        $paymentMethodId = Arr::get($arguments, 'payment_method_id');
        $dueInDays = Arr::get($arguments, 'due_in_days', self::DEFAULT_DUE_IN_DAYS);
        $invoiceTemplateId = Arr::get($arguments, 'invoice_template_id');
        $name = Arr::get($arguments, 'name');

        if (filled($billingProfileId)) {
            $profile = BillingProfile::query()->findOrFail($billingProfileId);

            $defaultCampaignIds = is_null($associatedCampaignIds)
                ? $profile->campaigns->pluck(BillingProfile::FIELD_ID)->toArray()
                : $associatedCampaignIds;

            $this->billingProfileService->updateBillingProfile(
                billingProfile          : BillingProfile::query()->findOrFail($billingProfileId),
                frequencyType           : $frequencyType ?? $profile->{BillingProfile::FIELD_BILLING_FREQUENCY_CRON},
                frequencyData           : $frequencyData ?? $profile->{BillingProfile::FIELD_CRON_DATA},
                thresholdInDollars      : $thresholdInDollars ?? $profile->{BillingProfile::FIELD_THRESHOLD_IN_DOLLARS},
                maxAllowedChargeAttempts: $maxAllowedChargeAttempts ?? $profile->{BillingProfile::FIELD_MAX_ALLOWED_CHARGE_ATTEMPTS},
                associatedCampaignIds   : $associatedCampaignIds ?? $defaultCampaignIds,
                processAuto             : $processAuto ?? $profile->{BillingProfile::FIELD_PROCESS_AUTO},
                default                 : $default ?? $profile->{BillingProfile::FIELD_DEFAULT},
                updatedById             : $updatedById ?? $profile->{BillingProfile::FIELD_UPDATED_BY_ID},
                paymentMethod           : $paymentMethod ?? $profile->{BillingProfile::FIELD_PAYMENT_METHOD}->value,
                dueInDays               : $dueInDays ?? $profile->{BillingProfile::FIELD_DUE_IN_DAYS} ?? self::DEFAULT_DUE_IN_DAYS,
                paymentMethodId         : array_key_exists('payment_method_id', $arguments)
                    ? $arguments['payment_method_id']
                    : $profile->{BillingProfile::FIELD_PAYMENT_METHOD_ID},

                invoiceTemplateId       : $invoiceTemplateId ?? $profile->{BillingProfile::FIELD_INVOICE_TEMPLATE_ID},
                name                    : $name ?? $profile->{BillingProfile::FIELD_NAME},
            );
        } else {
            $this->billingProfileService->createProfileWithDefaultConfiguration(
                paymentMethod           : PaymentMethodServices::tryFrom($paymentMethod),
                companyId               : $companyId,
                default                 : $default,
                processAuto             : $processAuto,
                thresholdInDollars      : $thresholdInDollars,
                frequency               : BillingProfileFrequenciesEnum::tryFrom($frequencyType),
                contact                 : $contact,
                createdById             : $createdById,
                maxAllowedChargeAttempts: $maxAllowedChargeAttempts,
                frequencyData           : $frequencyData,
                associatedCampaignIds   : $associatedCampaignIds,
                createdByType           : $createdByType,
                paymentMethodId         : $paymentMethodId,
                dueInDays               : $dueInDays ?? self::DEFAULT_DUE_IN_DAYS,
                invoiceTemplateId       : $invoiceTemplateId,
                name                    : $name
            );
        }
    }
}
