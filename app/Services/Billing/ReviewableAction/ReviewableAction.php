<?php

namespace App\Services\Billing\ReviewableAction;

use Throwable;

abstract class ReviewableAction
{
    /**
     * @return void
     */
    public function onApproval(array $arguments)
    {

    }

    /**
     * @return void
     */
    public function onRefusal(array $arguments)
    {

    }


    /**
     * @param Throwable $exception
     * @return void
     */
    public function onError(Throwable $exception): void
    {

    }
}
