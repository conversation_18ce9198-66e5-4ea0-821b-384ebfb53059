<?php

namespace App\Services\Billing\ReviewableAction;

use App\Aggregates\InvoiceAggregateRoot;
use App\Models\Billing\Invoice;
use App\Services\Billing\CreditService;
use Illuminate\Contracts\Container\BindingResolutionException;

class IssueInvoiceToCollections extends ReviewableAction
{
    public function __construct(protected CreditService $creditService)
    {

    }

    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     */
    public function onApproval(array $arguments): void
    {
        [
            "uuid"            => $uuid,
            "invoiceUuid"     => $invoiceUuid,
            "authorType"      => $authorType,
            "sentDate"        => $sentDate,
            "recoveryStatus"  => $recoveryStatus,
            "amountCollected" => $amountCollected,
            "authorId"        => $authorId,
        ] = $arguments;

        $invoice = Invoice::findByUuid($invoiceUuid);

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        $invoiceAggregateRoot->collection->issueInvoiceToCollections(
            uuid           : $uuid,
            invoiceUuid    : $invoice->{Invoice::FIELD_UUID},
            authorType     : $authorType,
            sentDate       : $sentDate,
            recoveryStatus : $recoveryStatus,
            amountCollected: $amountCollected,
            authorId       : $authorId,
        );

        $invoiceAggregateRoot->persist();
    }
}
