<?php

namespace App\Services\Billing\ReviewableAction;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceStates;
use App\Enums\BundleInvoiceStatus;
use App\Exceptions\Billing\InvoicePaymentOverchargeException;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use App\States\Billing\Draft;
use Illuminate\Contracts\Container\BindingResolutionException;

class InvoiceStatusUpdateApprovableAction extends ReviewableAction
{
    public function __construct(
        protected InvoiceService $invoiceService,
        protected InvoiceTransactionService $invoiceTransactionService,
    )
    {

    }

    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     */
    public function onApproval(array $arguments): void
    {
        [
            "invoiceUuid" => $invoiceUuid,
            "newStatus"   => $newStatus,
            "oldStatus"   => $oldStatus,
            "authorType"  => $authorType,
            "authorId"    => $authorId,
        ] = $arguments;

        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($invoiceUuid);

        $this->invoiceService->updateInvoiceStatus(
            invoice   : $invoice,
            newStatus : $newStatus,
            authorType: $authorType,
            authorId  : $authorId,
        );
    }

    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException|InvoicePaymentOverchargeException
     */
    public function onRefusal(array $arguments): void
    {
        ["invoiceUuid" => $invoiceUuid] = $arguments;

        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($invoiceUuid);

        //cancel/void rejected status updates from draft
        if($invoice->status instanceof Draft) {
            $this->invoiceService->updateInvoiceStatus(
                invoice: $invoice,
                newStatus: InvoiceStates::VOIDED->value,
                authorType: InvoiceEventAuthorTypes::SYSTEM->value,
            );
        }

        foreach ($invoice->{Invoice::RELATION_INVOICE_ITEMS} as $item) {
            if ($item->{InvoiceItem::FIELD_BILLABLE_TYPE} === Bundle::class) {
                BundleInvoice::query()
                    ->where(BundleInvoice::FIELD_PAYABLE_INVOICE_ID, $invoice->{Invoice::FIELD_ID})
                    ->where(BundleInvoice::FIELD_BUNDLE_ID, $item->{InvoiceItem::FIELD_BILLABLE_ID})
                    ->update([
                        BundleInvoice::FIELD_STATUS => BundleInvoiceStatus::tryFromInvoiceStatus($invoice->status->status())
                    ]);
            }
        }
    }
}
