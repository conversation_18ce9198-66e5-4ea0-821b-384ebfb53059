<?php

namespace App\Services\Billing\ReviewableAction;

use App\Aggregates\CreditAggregateRoot;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Str;

class ExpireCompanyCreditApprovableAction extends ReviewableAction
{
    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     */
    public function onApproval(array $arguments): void
    {
        [
            "creditId"      => $creditId,
            "authorType"    => $authorType,
            "authorId"      => $authorId,
            "oldExpiryDate" => $oldDate
        ] = $arguments;

        CreditAggregateRoot::retrieve(Str::uuid())
            ->expireCredit(
                creditId  : $creditId,
                authorType: $authorType,
                oldDate   : $oldDate,
                authorId  : $authorId
            )->persist();
    }
}
