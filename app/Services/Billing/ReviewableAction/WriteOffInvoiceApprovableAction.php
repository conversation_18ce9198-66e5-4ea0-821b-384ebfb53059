<?php

namespace App\Services\Billing\ReviewableAction;

use App\Aggregates\InvoiceAggregateRoot;
use App\Models\Billing\Invoice;
use App\Services\Billing\CreditService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Illuminate\Contracts\Container\BindingResolutionException;

class WriteOffInvoiceApprovableAction extends ReviewableAction
{
    public function __construct(
        protected CreditService $creditService,
        protected InvoiceTransactionService $invoiceTransactionService,
    )
    {

    }

    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     */
    public function onApproval(array $arguments): void
    {
        [
            "uuid"        => $uuid,
            "invoiceUuid" => $invoiceUuid,
            "authorType"  => $authorType,
            "date"        => $date,
            "amount"      => $amount,
            "authorId"    => $authorId,
        ] = $arguments;

        $invoice = Invoice::findByUuid($invoiceUuid);

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        $invoiceAggregateRoot->writeOffs->writeOffInvoice(
            uuid       : $uuid,
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            authorType : $authorType,
            date       : $date,
            amount     : $amount,
            authorId   : $authorId,
        );

        $invoiceAggregateRoot->persist();
    }
}
