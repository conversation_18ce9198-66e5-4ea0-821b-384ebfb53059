<?php

namespace App\Services\Billing\ReviewableAction;

use App\Aggregates\CreditAggregateRoot;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Str;

class ExtendCompanyCreditApprovableAction extends ReviewableAction
{
    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     */
    public function onApproval(array $arguments): void
    {
        [
            "creditId"      => $creditId,
            "authorType"    => $authorType,
            "authorId"      => $authorId,
            "newExpiryDate" => $nextCreditExpiryDate
        ] = $arguments;

        CreditAggregateRoot::retrieve(Str::uuid())
            ->extendCredit(
                creditId  : $creditId,
                newDate   : $nextCreditExpiryDate,
                authorType: $authorType,
                authorId  : $authorId,
            )->persist();
    }
}
