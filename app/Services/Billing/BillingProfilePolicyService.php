<?php

namespace App\Services\Billing;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Models\Billing\BillingProfilePolicy;
use App\Repositories\Billing\BillingProfilePolicyRepository;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class BillingProfilePolicyService
{
    public function __construct(protected BillingProfilePolicyRepository $billingProfilePolicyRepository)
    {
    }

    /**
     * @return Collection
     * @throws Exception
     */
    public function listGlobalPolicies(): Collection
    {
        return $this->billingProfilePolicyRepository->listGlobalPolicies();
    }

    /**
     * @param string $eventClass
     * @param string|null $billingProfileId
     * @return Collection
     */
    public function getPoliciesForEventClass(string $eventClass, ?string $billingProfileId = null): Collection
    {
        return $this->billingProfilePolicyRepository->getPoliciesForEventClass($eventClass, $billingProfileId);
    }

    /**
     * @param BillingProfilePolicy $profilePolicy
     * @return bool|null
     */
    public function deleteProfilePolicy(BillingProfilePolicy $profilePolicy): ?bool
    {
        return $profilePolicy->delete();
    }

    /**
     * @param BillingPolicyEventType $eventClass
     * @param BillingPolicyActionType $actionClass
     * @param int|null $sortOrder
     * @param int|null $billingProfileId
     * @param int|null $id
     * @return BillingProfilePolicy
     * @throws Exception
     */
    public function saveProfilePolicy(
        BillingPolicyEventType $eventClass,
        BillingPolicyActionType $actionClass,
        ?int $sortOrder = null,
        ?int $billingProfileId = null,
        ?int $id = null,
    ): BillingProfilePolicy
    {
        if (!isset($sortOrder)) {
            $sortOrder = (BillingProfilePolicy::query()->where(BillingProfilePolicy::FIELD_EVENT_CLASS, $eventClass->getClass())->max('sort_order') ?? 0) + 1;
        }

        return $this->billingProfilePolicyRepository->saveProfilePolicy(
            eventClass      : $eventClass->getClass(),
            actionClass     : $actionClass->getClass(),
            sortOrder       : $sortOrder,
            billingProfileId: $billingProfileId,
            id              : $id,
        );
    }

    /**
     * @param array $policies
     * @return bool
     * @throws Exception
     */
    public function batchUpdateProfilePolicies(array $policies): bool
    {
        foreach ($policies as $index => $policy) {
            $event = BillingPolicyEventType::tryFrom(Arr::get($policy, 'event_slug'));
            $action = BillingPolicyActionType::tryFrom(Arr::get($policy, 'action_slug'));
            $actionData = Arr::get($policy, 'action_data', []);

            $this->billingProfilePolicyRepository->saveProfilePolicy(
                eventClass      : $event->getClass(),
                actionClass     : $action->getClass(),
                sortOrder       : $index,
                billingProfileId: Arr::get($policy, 'billing_profile_id'),
                id              : Arr::get($policy, 'id'),
                actionData      : $actionData,
            );
        }

        return true;
    }
}
