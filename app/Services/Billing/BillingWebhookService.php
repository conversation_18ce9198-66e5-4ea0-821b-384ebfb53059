<?php

namespace App\Services\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceTransactionScope;
use App\Enums\Billing\InvoiceTransactionType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Billing\InvoiceTransaction;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundChargeRepository;
use App\Repositories\Billing\InvoiceTransactionRepository;
use App\Services\Billing\PaymentGateway\Events\ChargeFailed;
use App\Services\Billing\PaymentGateway\Events\ChargeDisputeClosed;
use App\Services\Billing\PaymentGateway\Events\ChargeDisputeCreated;
use App\Services\Billing\PaymentGateway\Events\ChargeRefundUpdated;
use App\Services\Billing\PaymentGateway\Events\ChargeSucceeded;
use App\Services\Billing\PaymentGateway\Events\ChargeRefunded;
use App\Services\Billing\PaymentGateway\Events\PaymentGatewayEvent;
use Exception;
use Illuminate\Support\Str;

class BillingWebhookService
{
    public function __construct(
        protected InvoiceTransactionRepository $invoiceTransactionRepository,
        protected InvoiceRefundChargeRepository $invoiceRefundChargeRepository,
    )
    {
    }

    /**
     * @param InvoiceAggregateRoot $invoiceAggregateRoot
     * @param ChargeRefunded $event
     * @param Invoice|null $invoice
     * @return void
     * @throws Exception
     */
    public function handleChargeRefunded(
        InvoiceAggregateRoot $invoiceAggregateRoot,
        ChargeRefunded $event,
        ?Invoice $invoice = null
    ): void
    {
        if (empty($invoice)) {
            $invoice = $this->invoiceTransactionRepository
                ->getInvoiceByExternalReference($event->externalTransactionId);
        }

        [
            'total_refunded'  => $totalRefunded,
            'refunded_amount' => $netRefund
        ] = $this->invoiceRefundChargeRepository->getNetRefundAmount(
            $event->amountRefunded,
            $event->externalTransactionId,
        );

        BillingLogService::log(
            message: 'Charge refunded',
            context: [
                'totalRefunded' => $totalRefunded,
                'netRefund'     => $netRefund,
                'totalPaid'     => $invoice->lastSnapshot()->{InvoiceSnapshot::FIELD_TOTAL_PAID},
                'event'         => $event
            ]
        );

        $scope = $netRefund >= $invoice->lastSnapshot()->{InvoiceSnapshot::FIELD_TOTAL_PAID}
            ? InvoiceTransactionScope::FULL
            : InvoiceTransactionScope::PARTIAL;

        $invoiceAggregateRoot->refund->refundInvoice(
            invoiceUuid          : $event->invoiceUuid,
            externalTransactionId: $event->externalTransactionId,
            amount               : $netRefund,
            currency             : $event->currency,
            type                 : InvoiceTransactionType::REFUND->value,
            amountCaptured       : $event->amountCaptured,
            fullyRefunded        : $event->fullyRefunded,
            source               : $event->source,
            scope                : $scope->value,
        );
    }

    /**
     * @throws Exception
     */
    public function handleChargeSucceeded(
        InvoiceAggregateRoot $invoiceAggregateRoot,
        ChargeSucceeded $event,
        Invoice $invoice
    ): void
    {
        $totalPaid = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $event->invoiceUuid,
            type       : InvoiceTransactionType::PAYMENT
        );

        $scope = $totalPaid + $event->amount >= $invoice->getTotalIssuable()
            ? InvoiceTransactionScope::FULL
            : InvoiceTransactionScope::PARTIAL;

        $invoiceAggregateRoot->charge->invoiceChargeSuccess(
            externalTransactionId   : $event->externalTransactionId,
            invoiceUuid             : $event->invoiceUuid,
            amount                  : $event->amount,
            currency                : $event->currency,
            source                  : $event->source,
            type                    : InvoiceTransactionType::PAYMENT,
            date                    : $event->date,
            scope                   : $scope,
            invoicePaymentChargeUuid: $event->invoicePaymentChargeUuid,
            externalPaymentMethodId : $event->externalPaymentMethodId,
        );
    }

    /**
     * @param PaymentGatewayEvent $event
     * @return void
     * @throws Exception
     */
    public function handleEvent(PaymentGatewayEvent $event): void
    {
        /** @var ?Invoice $invoice */
        $invoice = null;

        if (isset($event->chargeId)) {
            $charge = $this->invoiceTransactionRepository->getInvoiceTransactionByExternalId($event->chargeId);
            $invoice = $charge->{InvoiceTransaction::RELATION_INVOICE};
        } else if (isset($event->invoiceUuid)) {
            $invoice = Invoice::findByUuid($event->invoiceUuid);
        }

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoice?->{Invoice::FIELD_UUID} ?? Str::uuid());

        match ($event::class) {
            ChargeSucceeded::class      => $this->handleChargeSucceeded($invoiceAggregateRoot, $event, $invoice),
            ChargeFailed::class         => $invoiceAggregateRoot->charge->invoiceChargeFailed(
                externalTransactionId: $event->externalTransactionId,
                invoiceUuid          : $event->invoiceUuid,
                errorMessage         : $event->errorMessage,
                amount               : $event->amount,
                currency             : $event->currency,
                source               : $event->source,
            ),
            ChargeDisputeCreated::class => $invoiceAggregateRoot->charge->openChargeDispute(
                externalTransactionId: $event->externalTransactionId,
                chargeId             : $event->chargeId,
                amount               : $event->amount,
                reason               : $event->reason,
                status               : $event->status,
                currency             : $event->currency,
                source               : $event->source,
            ),
            ChargeDisputeClosed::class  => $invoiceAggregateRoot->charge->closeChargeDispute(
                externalTransactionId: $event->externalTransactionId,
                chargeId             : $event->chargeId,
                status               : $event->status,
                amount               : $event->amount,
                currency             : $event->currency,
                source               : $event->source,
                reason               : $event->reason,
            ),
            ChargeRefunded::class       => $this->handleChargeRefunded($invoiceAggregateRoot, $event, $invoice),
            ChargeRefundUpdated::class  => $invoiceAggregateRoot->refund->updateInvoiceCharge(
                externalRefundId       : $event->externalRefundId,
                externalChargeId       : $event->externalChargeId,
                currency               : $event->currency,
                amount                 : $event->amount,
                invoiceRefundChargeUuid: $event->invoiceRefundChargeUuid,
                invoiceUuid            : $event->invoiceUuid,
            ),
            default                     => throw new Exception('Unknown event ' . json_encode($event))
        };

        $invoiceAggregateRoot->persist();
    }
}



