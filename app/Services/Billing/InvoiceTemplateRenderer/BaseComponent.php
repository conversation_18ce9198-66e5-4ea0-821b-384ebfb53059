<?php

namespace App\Services\Billing\InvoiceTemplateRenderer;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

abstract class BaseComponent implements Arrayable
{
    private const string CAMEL_FORMAT = 'camel';
    private const string SNAKE_FORMAT = 'snake';

    /**
     * @var string
     */
    protected string $id;

    /**
     * Get the component's ID.
     *
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * Get the validation rules for the component.
     *
     * @return array
     */
    public static function getValidationRules(): array
    {
        return [];
    }

    /**
     * Create a new instance of the component and validate the provided data.
     *
     * @param array $data
     * @return BaseComponent
     */
    public static function make(array $data): self
    {
        $instance = new static();

        // TODO - Validate
        $instance->fill($data);

        return $instance;
    }

    /**
     * Validate data
     *
     * @param array $data
     * @return array
     * @throws ValidationException
     */
    public static function validate(array $data): array
    {
        $rules = self::getValidationRules();
        $validator = Validator::make($data, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return $validator->validated();
    }

    /**
     * Fill the component properties with validated data.
     *
     * @param array $data
     * @return void
     */
    protected function fill(array $data): void
    {
        foreach ($data as $key => $value) {
            $this->assignProperty($key, $value);
        }
    }

    /**
     * Assign the value to the property if it exists in any case format.
     *
     * @param string $key
     * @param mixed $value
     * @return void
     */
    protected function assignProperty(string $key, mixed $value): void
    {
        $formats = [self::CAMEL_FORMAT, self::SNAKE_FORMAT];

        foreach ($formats as $format) {
            $formattedKey = $this->convertKey($key, $format);

            $setMethod = 'set' . ucfirst($this->convertKey($key, self::CAMEL_FORMAT));

            if (method_exists($this, $setMethod)) {
                $this->{$setMethod}($value);
            } else if (property_exists($this, $formattedKey)) {
                $this->{$formattedKey} = $value;
                return;
            }
        }
    }

    /**
     * Convert the key to the specified format.
     *
     * @param string $key
     * @param string $format
     * @return string
     */
    protected function convertKey(string $key, string $format): string
    {
        return match ($format) {
            self::CAMEL_FORMAT => lcfirst(str_replace(' ', '', ucwords(str_replace(['-', '_'], ' ', $key)))),
            self::SNAKE_FORMAT => strtolower(preg_replace('/(?<!^)[A-Z]/', '_$0', $key)),
            default            => $key,
        };
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->toViewData();
    }

    /**
     * @return array
     */
    abstract public function toViewData(): array;
}
