<?php

namespace App\Services\Billing;

use App\Aggregates\InvoiceSnapshotAggregateRoot;
use App\DTO\Graphs\BaseGraph;
use App\DTO\Graphs\BaseGraphDataset;
use App\Enums\Billing\GraphGroupByOptions;
use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceCollections;
use App\Models\Billing\InvoiceDispute;
use App\Repositories\Billing\InvoiceSnapshotRepository;
use App\Repositories\Billing\InvoiceTransactionRepository;
use App\Services\Territory\RelationshipManagerService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

/**
 *
 */
class InvoiceSnapshotService
{
    public function __construct(
        protected InvoiceSnapshotRepository $invoiceSnapshotRepository,
        protected InvoiceTransactionRepository $invoiceTransactionRepository,
        protected RelationshipManagerService $relationshipManagerService
    )
    {
    }

    /**
     * @param array $filters
     * @param bool|null $paginate
     * @return Collection|LengthAwarePaginator
     */
    public function getInvoiceSnapshots(array $filters, ?bool $paginate = false): Collection|LengthAwarePaginator
    {
        return $this->invoiceSnapshotRepository->getInvoiceSnapshots(filters: $filters, paginate: $paginate);
    }

    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return BaseGraph
     */
    public function getRevenueComparison(
        Carbon $startDate,
        Carbon $endDate,
    ): BaseGraph
    {
        $previousPeriod = $this->getPreviousTimespan(startDate: $startDate, endDate: $endDate);

        $groupByStep = GraphGroupByOptions::getStep(start: $startDate, end: $endDate);

        $groupedPreviousRevenue = $this->invoiceSnapshotRepository->getInvoiceRevenueGrouped(
            start    : $previousPeriod['startDate'],
            end      : $previousPeriod['endDate'],
            groupedBy: $groupByStep
        )->values();

        $groupedCurrentRevenue = $this->invoiceSnapshotRepository->getInvoiceRevenueGrouped(
            start    : $startDate,
            end      : $endDate,
            groupedBy: $groupByStep
        )->values();

        $datasets = [
            ['label' => 'Current Period', 'data' => $groupedCurrentRevenue],
            ['label' => 'Previous Period', 'data' => $groupedPreviousRevenue],
        ];

        return $this->formatRevenueToGraphDto(datasets: $datasets, groupByStep: $groupByStep);
    }

    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function getPreviousTimespan(Carbon $startDate, Carbon $endDate): array
    {
        $duration = $startDate->diffInDays($endDate);

        $newEndDate = $startDate->copy()->subDay();

        $newStartDate = $newEndDate->copy()->subDays($duration);

        return [
            'startDate' => $newStartDate,
            'endDate'   => $newEndDate,
        ];
    }

    /**
     * @param array $datasets
     * @param GraphGroupByOptions $groupByStep
     * @return BaseGraph
     */
    public function formatRevenueToGraphDto(array $datasets, GraphGroupByOptions $groupByStep): BaseGraph
    {
        $formatted = [];

        $labels = [];

        foreach ($datasets as $dataset) {

            $labels = collect($dataset['data'])->pluck('period')->map(function ($period) use ($groupByStep) {
                return Carbon::parse($period)->format($groupByStep->getCarbonFormat());
            })->toArray();

            $graphDto = new BaseGraphDataset(
                label: $dataset['label'],
                data : collect($dataset['data'])->pluck('total')->toArray()
            );

            $formatted[] = $graphDto;
        }

        return new BaseGraph(
            labels  : $labels,
            datasets: $formatted
        );
    }

    /**
     * @param string $invoiceUuid
     * @param string|null $date
     * @return void
     * @throws BindingResolutionException
     */
    public function createInvoiceSnapshot(
        string $invoiceUuid,
        ?string $date = null
    ): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::query()
            ->where(Invoice::FIELD_UUID, $invoiceUuid)
            ->with(['company.accountManager', 'company.customerSuccessManager', 'company.businessDevelopmentManager'])
            ->first();

        $totalRefunded = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::REFUND
        );

        $totalCollections = $invoice->lastCollections()?->{InvoiceCollections::FIELD_AMOUNT_COLLECTED} ?? 0;

        $totalWrittenOff = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::WRITTEN_OFF,
        );

        $totalCollectionsRecovered = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::COLLECTIONS,
            scenario   : InvoiceTransactionScenario::WON
        );

        $totalCollectionsLost = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::COLLECTIONS,
            scenario   : InvoiceTransactionScenario::LOST
        );

        $totalPaid = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::PAYMENT
        );

        $totalCreditsApplied = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::CREDIT
        );

        $totalPaid = $totalPaid - $totalRefunded;

        $totalOutstanding = $invoice->getTotalIssuable()
            - $totalRefunded
            - $totalPaid
            - $totalCollections
            - $totalWrittenOff;

        $totalChargebackWon = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::DISPUTE,
            scenario   : InvoiceTransactionScenario::WON
        );

        $totalChargebackLost = $this->invoiceTransactionRepository->calculateTypeTotal(
            invoiceUuid: $invoice->{Invoice::FIELD_UUID},
            type       : InvoiceTransactionType::DISPUTE,
            scenario   : InvoiceTransactionScenario::LOST
        );

        $totalChargeback = $invoice->chargebacks()->sum(InvoiceDispute::FIELD_AMOUNT);

        InvoiceSnapshotAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID})
            ->createInvoiceSnapshot(
                uuid                        : Str::uuid(),
                invoiceUuid                 : $invoice->{Invoice::FIELD_UUID},
                companyId                   : $invoice->{Invoice::FIELD_COMPANY_ID},
                status                      : $invoice->status->status(),
                totalValue                  : $invoice->getInvoiceItemsTotal(),
                totalOutstanding            : $totalOutstanding < 0 ? 0 : $totalOutstanding, // When they pay more ??
                totalRefunded               : $totalRefunded,
                totalPaid                   : $totalPaid,
                totalCollections            : $totalCollections,
                totalCollectionsRecovered   : $totalCollectionsRecovered,
                totalCollectionsLost        : $totalCollectionsLost,
                totalCreditsApplied         : $totalCreditsApplied,
                totalChargebackWon          : $totalChargebackWon,
                totalChargebackLost         : $totalChargebackLost,
                totalChargeback             : $totalChargeback,
                totalWrittenOff             : $totalWrittenOff,
                accountManagerId            : $invoice->company?->accountManager?->id,
                successManagerId            : $invoice->company?->customerSuccessManager?->id,
                businessDevelopmentManagerId: $invoice->company?->businessDevelopmentManager?->id,
                date                        : $date
            )->persist();
    }
}
