<?php

namespace App\Services\Billing;

use App\DTO\Billing\InvoicePayload;
use App\Enums\Billing\InvoiceItemTypes;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\ProductAssignment;
use App\Services\Billing\BillingProfile\BillingProfileService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class InvoiceBillingProfileService
{
    public function __construct(
        protected BillingProfileService $billingProfileService,
    )
    {

    }

    /**
     * @param int $companyId
     * @return BillingProfile|null
     */
    public function getDefaultCompanyBillingProfile(int $companyId): ?BillingProfile
    {
        return BillingProfile::query()
            ->where(BillingProfile::FIELD_COMPANY_ID, $companyId)
            ->orderByDesc(BillingProfile::FIELD_DEFAULT)  // Prioritise default profile
            ->orderByRaw(BillingProfile::FIELD_PAYMENT_METHOD . " = ? DESC", [
                PaymentMethodServices::STRIPE->value
            ])
            ->first();
    }

    /**
     * @param Collection $productAssignmentIds
     * @param int $companyId
     * @return Collection
     */
    protected function getAllCampaignIdsFromProductAssignmentIds(
        Collection $productAssignmentIds,
        int $companyId
    ): Collection
    {
        return CompanyCampaign::query()
            ->whereHas(CompanyCampaign::RELATION_BUDGET_CONTAINER, function (Builder $builder) use ($productAssignmentIds) {
                $builder->whereHas(BudgetContainer::RELATION_BUDGETS, function (Builder $builder) use ($productAssignmentIds) {
                    $builder->whereHas(Budget::RELATION_PRODUCT_ASSIGNMENTS, function (Builder $builder) use ($productAssignmentIds) {
                        $builder->whereIn(ProductAssignment::FIELD_ID, $productAssignmentIds->toArray());
                    });
                });
            })
            ->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId)
            ->groupBy(CompanyCampaign::FIELD_ID)
            ->get()
            ->pluck(CompanyCampaign::FIELD_ID);
    }

    /**
     * @param int $campaignId
     * @return Builder
     */
    protected function getBillingProfileForCampaignId(
        int $campaignId
    ): Builder
    {
        return BillingProfile::query()
            ->whereHas(BillingProfile::RELATION_CAMPAIGNS, function (Builder $builder) use ($campaignId) {
                $builder->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID, $campaignId);
            })
            ->whereDoesntHave(BillingProfile::RELATION_CAMPAIGNS)
            ->where(BillingProfile::FIELD_DEFAULT, true);
    }

    /**
     * @param InvoicePayload $invoicePayload
     * @return int
     */
    public function calculateBillingProfileForInvoice(InvoicePayload $invoicePayload): int
    {
        $defaultBillingProfile = $this->getDefaultCompanyBillingProfile($invoicePayload->getCompanyId());

        if (!$defaultBillingProfile) {
            return $this->billingProfileService
                ->createProfileWithDefaultConfiguration(
                    paymentMethod: PaymentMethodServices::STRIPE,
                    companyId    : $invoicePayload->getCompanyId(),
                    default      : true
                )
                ->id;
        }

        $invoiceItemTypes = $invoicePayload->getInvoiceItemTypes();

        // No mix of invoice item types
        if ($invoiceItemTypes->contains(InvoiceItemTypes::PRODUCT) && $invoiceItemTypes->count() === 1) {
            $productAssignmentIds = $invoicePayload->getItems()->pluck('billable_id');

            $campaignIds = $this->getAllCampaignIdsFromProductAssignmentIds(
                productAssignmentIds: $productAssignmentIds,
                companyId           : $invoicePayload->getCompanyId()
            );

            if ($campaignIds->count() === 1) {
                $campaignBillingProfile = $this->getBillingProfileForCampaignId(
                    campaignId: $campaignIds->first()
                )->first();

                $defaultBillingProfile = $campaignBillingProfile ?: $defaultBillingProfile;
            }
        }

        return $defaultBillingProfile->id;
    }
}



