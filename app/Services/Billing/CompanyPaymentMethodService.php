<?php

namespace App\Services\Billing;

use App\Models\Billing\CompanyPaymentMethod;
use Illuminate\Database\Eloquent\Collection;

class CompanyPaymentMethodService
{
    /**
     * @param int $companyId
     * @param string|null $type
     * @return Collection
     */
    public function getCompanyBillingProfiles(
        int $companyId,
        ?string $type = null
    ): Collection
    {
        return CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $companyId)
            ->when(filled($type), function ($query) use ($type) {
                return $query->where(CompanyPaymentMethod::FIELD_TYPE, $type);
            })
            ->get();
    }
}
