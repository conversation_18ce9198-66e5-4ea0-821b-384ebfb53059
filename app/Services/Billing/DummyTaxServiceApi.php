<?php

namespace App\Services\Billing;

use App\Contracts\Services\TaxServiceApiContract;
use App\DTO\Billing\AddressData;
use App\DTO\Billing\InvoiceItemTaxData;
use App\DTO\Billing\InvoiceTaxData;

class DummyTaxServiceApi implements TaxServiceApiContract
{
    const int    TAX_FRACTION = 10;
    const string TAX_COUNTRY  = 'US';
    const string TAX_STATE    = 'CA';
    const string TAX_TYPE     = 'sales_tax';

    public function getInvoiceTax(AddressData $buyerAddressData, array $invoiceItemsDtoArray, string $invoiceUuid): ?InvoiceTaxData
    {

        $invoiceItemTaxDTOArray = [];
        $totalTaxableAmount     = 0.00;
        $totalTax               = 0.00;
        foreach ($invoiceItemsDtoArray as $invoiceItem) {
            $taxedAmount        = $invoiceItem->getUnitPrice() / self::TAX_FRACTION;
            $totalTaxableAmount += $invoiceItem->getUnitPrice();
            $totalTax           += $taxedAmount;

            $invoiceItemDTO = new InvoiceItemTaxData(
                invoiceItemId: $invoiceItem->getInvoiceItemId(),
                amount: $invoiceItem->getUnitPrice(),
                tax: $taxedAmount,
                quantity: $invoiceItem->getQuantity(),
                reference: $invoiceItem->getDescription()
            );

            $invoiceItemTaxDTOArray[] = $invoiceItemDTO;
        }

        return new InvoiceTaxData(
            invoiceUuid: $invoiceUuid,
            taxRate: self::TAX_FRACTION,
            region: self::TAX_STATE,
            country: self::TAX_COUNTRY,
            taxType: self::TAX_TYPE,
            taxableAmount: $totalTaxableAmount,
            totalTaxed: $totalTax,
            invoiceItemTaxData: $invoiceItemTaxDTOArray,
        );
    }
}