<?php

namespace App\Services\Billing;

use App\Builders\Billing\InvoiceBuilder;
use App\Builders\EloquentInvoiceBuilder;
use App\DTO\Billing\InvoiceItemDTO;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Billing\InvoiceRepository;
use App\Services\DatabaseHelperService;
use App\Services\Odin\ProductAssignmentService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyInvoiceService
{
    public function __construct(
        protected ProductAssignmentService $productAssignmentService,
        protected InvoiceRepository $invoiceRepository
    )
    {

    }

    /**
     * @param Company $company
     * @param Carbon|null $deliveredBefore
     * @param array $campaignIds
     * @return Collection<InvoiceItemDTO>
     */
    public function getUninvoicedInvoiceItemsForCompany(
        Company $company,
        ?Carbon $deliveredBefore = null,
        array $campaignIds = []
    ): Collection
    {
        $productAssignments = $this->productAssignmentService->getUninvoicedProductAssignments(
            company        : $company,
            deliveredBefore: $deliveredBefore,
            campaignIds    : $campaignIds,
            relations      : [
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_SERVICE_PRODUCT . '.' . ServiceProduct::RELATION_PRODUCT,
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER
            ]
        );

        return $this->productAssignmentService->transformToInvoiceItems($productAssignments);
    }

    /**
     * @param int $companyId
     * @param string|null $status
     * @param string|null $startDate
     * @param string|null $endDate
     * @param string|null $invoiceId
     * @return Builder
     */
    public function getInvoicesCrossSystem(
        int $companyId,
        ?string $status = null,
        ?string $startDate = null,
        ?string $endDate = null,
        ?string $invoiceId = null,
    ): Builder
    {
        $company = Company::query()->findOrFail($companyId);

        $legacyInvoices = EloquentInvoiceBuilder::query()
            ->forLegacyCompanyId($company->{Company::FIELD_LEGACY_ID})
            ->forStatus($status)
            ->forInvoiceNumber($invoiceId)
            ->forFromDate($startDate)
            ->forToDate($endDate)
            ->getQuery()
            ->select([
                DB::raw('invoiceid as id'),
                DB::raw('FROM_UNIXTIME(timestampadded, "%Y-%m-%d %H:%i:%s") as created_at'),
                'status',
                DB::raw('FROM_UNIXTIME(timestamppaymentdue, "%Y-%m-%d %H:%i:%s") as due_at'),
                DB::raw('"legacy" as source')
            ]);

        $a20Invoices = InvoiceBuilder::query()
            ->select([
                DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_ID . ' as id',
                DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_CREATED_AT . ' as created_at',
                DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_STATUS . ' as status',
                DatabaseHelperService::database() . '.' . Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT . ' as due_at',
                DB::raw('"a20" as source')
            ])
            ->joinCompany()
            ->forCompanyId($company->{Company::FIELD_ID})
            ->betweenDates(
                startDate: $startDate,
                endDate  : $endDate,
            )
            ->inStatuses(
                statuses: $status ? [$status] : null
            )
            ->forInvoiceId(
                invoiceId: $invoiceId
            )
            ->getQuery();

        return $legacyInvoices->unionAll($a20Invoices)
            ->orderBy('created_at', 'desc');
    }

}
