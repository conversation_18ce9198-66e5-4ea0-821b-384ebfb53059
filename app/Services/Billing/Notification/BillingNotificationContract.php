<?php

namespace App\Services\Billing\Notification;

use App\Enums\Billing\InvoiceEvents;
use App\Models\Billing\ActionApproval;
use App\Models\User;
use Illuminate\Support\Collection;

interface BillingNotificationContract
{
    /**
     * @param Collection<User> $recipients
     * @param ActionApproval $actionApproval
     * @param string $subject
     * @param string $message
     * @param InvoiceEvents $eventType
     * @return void
     */
    public function notify(
        Collection $recipients,
        ActionApproval $actionApproval,
        string $subject,
        string $message,
        InvoiceEvents $eventType,
    ): void;

}
