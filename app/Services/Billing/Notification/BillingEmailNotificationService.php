<?php

namespace App\Services\Billing\Notification;

use App\Enums\Billing\InvoiceEvents;
use App\Mail\Billing\ActionRequestedNotification;
use App\Mail\Billing\ActionReviewedNotification;
use App\Models\Billing\ActionApproval;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class BillingEmailNotificationService implements BillingNotificationContract
{
    public function notify(
        Collection                 $recipients,
        ActionApproval             $actionApproval,
        string                     $subject,
        string                     $message,
        InvoiceEvents $eventType,
    ): void
    {
        foreach ($recipients as $recipient) {
            $mailable = match ($eventType) {
                InvoiceEvents::INVOICE_ACTION_REQUESTED => new ActionRequestedNotification(
                    reviewer: $recipient,
                    emailSubject: $subject,
                    message: $message,
                    actionApproval: $actionApproval
                ),
                InvoiceEvents::INVOICE_ACTION_REQUEST_REVIEWED => new ActionReviewedNotification(
                    requester: $recipient,
                    actionApproval: $actionApproval,
                    message: $message,
                    emailSubject: $subject
                ),
                default => throw new \Exception('Unexpected match value'),
            };

            Mail::to($recipient->email)->queue($mailable);
        }
    }
}
