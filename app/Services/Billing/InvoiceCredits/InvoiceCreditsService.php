<?php

namespace App\Services\Billing\InvoiceCredits;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceCredit;
use App\Models\User;
use App\Services\Billing\CreditService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;

class InvoiceCreditsService
{
    public function __construct(
        protected CreditService $creditService
    )
    {

    }

    /**
     * @param Invoice $invoice
     * @return Collection
     */
    public function getInvoiceCredits(Invoice $invoice): Collection
    {
        return $invoice
            ->creditsApplied()
            ->orderByDesc(InvoiceCredit::FIELD_APPLIED_AT)
            ->get();
    }

    /**
     * @param Invoice $invoice
     * @param string $creditType
     * @param int $amount
     * @param User $author
     * @return void
     * @throws Exception
     */
    public function applyCreditToInvoice(
        Invoice $invoice,
        string $creditType,
        int $amount,
        User $author
    ): void
    {
        $creditBalance = $this->creditService->getCreditBalance(
            companyId: $invoice->{Invoice::FIELD_COMPANY_ID},
            type     : $creditType
        );

        if ($amount > $creditBalance) {
            throw ValidationException::withMessages([
                "Company doesn't have enough credits"
            ]);
        }

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        $invoiceAggregateRoot->credit->checkAvailableAndApplyCreditsToInvoice(
            invoice            : $invoice,
            expireDateReference: now(),
            amountToApply      : $amount,
            authorType         : InvoiceEventAuthorTypes::USER->value,
            authorId           : $author->id,
            creditType         : $creditType,
        );

        $invoiceAggregateRoot->pdf->createInvoicePdf($invoice->{Invoice::FIELD_UUID});

        $invoiceAggregateRoot->persist();
    }
}
