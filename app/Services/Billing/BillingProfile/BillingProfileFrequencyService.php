<?php

namespace App\Services\Billing\BillingProfile;

use App\Models\Billing\BillingProfile;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Odin\Company;
use App\Services\Billing\BillingLogService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;

class BillingProfileFrequencyService
{
    /**
     * @param BillingProfile $billingProfile
     * @param Carbon|null $now
     * @return bool
     * @throws Exception
     */
    public function isDue(
        BillingProfile $billingProfile,
        Carbon $now = null
    ): bool
    {
        if (!isset($now)) {
            $now = now()->startOfDay();
        }

        $referenceDate = $this
            ->getReferenceDate($billingProfile)
            ->startOfDay();

        $nextDate = $this->calculateNextDate(
            frequencyType: $billingProfile->{BillingProfile::FIELD_BILLING_FREQUENCY_CRON},
            referenceDate: $referenceDate,
            frequencyData: $billingProfile->{BillingProfile::FIELD_CRON_DATA},
        )->startOfDay();

        $isDue = $nextDate->lte($now);

        BillingLogService::log(
            message    : 'Calculating if billing profile is due date',
            namespace  : 'calculate_eligible_profiles_to_bill_by_frequency',
            relatedType: BillingProfile::class,
            relatedId  : $billingProfile->id,
            context    : [
                "now"            => $now->toString(),
                "reference_date" => $referenceDate->toISOString(),
                "next_date"      => $nextDate->toISOString(),
                "is_due"         => $isDue,
            ],
        );

        return $isDue;
    }

    /**
     * @param BillingProfile $billingProfile
     * @return Carbon
     */
    public function getReferenceDate(
        BillingProfile $billingProfile
    ): Carbon
    {
        if (filled($billingProfile->{BillingProfile::FIELD_LAST_BILLED_AT})) {
            return Carbon::parse($billingProfile->{BillingProfile::FIELD_LAST_BILLED_AT});
        }

        $lastLegacyInvoice = EloquentInvoice::query()
            ->where(
                EloquentInvoice::COMPANY_ID,
                $billingProfile->{BillingProfile::RELATION_COMPANY}->{Company::FIELD_LEGACY_ID}
            )
            ->orderByDesc(EloquentInvoice::TIMESTAMP_ADDED)
            ->first();

        return Carbon::parse($lastLegacyInvoice?->{EloquentInvoice::TIMESTAMP_ADDED} ?? $billingProfile->{BillingProfile::FIELD_CREATED_AT});
    }

    /**
     * @param BillingProfile $billingProfile
     * @param float $totalToBill
     * @return bool
     */
    public function isAboveThreshold(
        BillingProfile $billingProfile,
        float $totalToBill,
    ): bool
    {
        return $billingProfile->threshold_in_dollars < $totalToBill;
    }

    /**
     * @param string $frequencyType
     * @param Carbon $referenceDate
     * @param array $frequencyData
     * @return Carbon
     * @throws Exception
     */
    public function calculateNextDate(
        string $frequencyType,
        Carbon $referenceDate,
        array $frequencyData = [],
    ): Carbon
    {
        return match ($frequencyType) {
            'monthly' => $this->parseMonthly(
                referenceDate: $referenceDate,
                data         : $frequencyData
            ),
            'weekly'  => $this->parseWeekly(
                referenceDate: $referenceDate,
                data         : $frequencyData
            ),
            'daily'   => $this->parseDaily(
                referenceDate: $referenceDate,
                data         : $frequencyData
            ),
            default   => throw new Exception("Unknown frequency type: $frequencyType"),
        };
    }

    /**
     * @param Carbon $referenceDate
     * @param array $data
     * @return Carbon
     * @throws Exception
     */
    public function parseDaily(Carbon $referenceDate, array $data): Carbon
    {
        $days = Arr::get($data, 'day');

        if (empty($days)) {
            throw new Exception('Day should not be empty');
        }

        return $referenceDate->copy()->addDays($days);
    }

    /**
     * @param Carbon $referenceDate
     * @param array $data
     * @return Carbon
     * @throws Exception
     */
    protected function parseWeekly(Carbon $referenceDate, array $data): Carbon
    {
        $weekday = Arr::get($data, 'week_day');

        if (empty($weekday)) {
            throw new Exception('Week day should not be empty');
        }

        $formattedWeekday = ucfirst(strtolower($weekday));

        return $referenceDate->copy()->next($formattedWeekday);
    }

    /**
     * @param Carbon $referenceDate
     * @param array $data
     * @return Carbon
     * @throws Exception
     */
    protected function parseMonthly(
        Carbon $referenceDate,
        array $data
    ): Carbon
    {
        $dayOfMonth = Arr::get($data, 'month_day');

        if (empty($dayOfMonth)) {
            throw new Exception('Month day should not be empty');
        }

        if ($referenceDate->day < $dayOfMonth) {
            $nextDate = Carbon::create($referenceDate->year, $referenceDate->month, $dayOfMonth);
        } else {
            $nextDate = Carbon::create($referenceDate->year, $referenceDate->month, $dayOfMonth)->addMonth();
        }

        return $nextDate;
    }
}
