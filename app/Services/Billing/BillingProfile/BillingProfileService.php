<?php

namespace App\Services\Billing\BillingProfile;

use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\BillingProfileFrequenciesEnum;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Billing\InvoiceTemplate;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Repositories\Billing\BillingProfilePolicyRepository;
use App\Repositories\Billing\BillingProfileRepository;
use App\Services\Billing\BillingActionApprovalService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Validation\ValidationException;

class BillingProfileService
{
    public function __construct(
        protected BillingProfileRepository $billingProfileRepository,
        protected BillingProfilePolicyRepository $billingProfilePolicyRepository,
        protected BillingActionApprovalService $billingActionApprovalService
    )
    {

    }

    /**
     * @param PaymentMethodServices $paymentMethod
     * @param int $companyId
     * @param bool $default
     * @param bool $processAuto
     * @param int $thresholdInDollars
     * @param BillingProfileFrequenciesEnum $frequency
     * @param CompanyUser|null $contact
     * @param int|null $createdById
     * @param int|null $maxAllowedChargeAttempts
     * @param array|null $frequencyData
     * @param array|null $associatedCampaignIds
     * @param string|null $createdByType
     * @param int|null $paymentMethodId
     * @param int|null $dueInDays
     * @param int|null $invoiceTemplateId
     * @param string|null $name
     * @return BillingProfile
     */
    public function createProfileWithDefaultConfiguration(
        PaymentMethodServices $paymentMethod,
        int $companyId,
        bool $default = false,
        bool $processAuto = false,
        int $thresholdInDollars = 400,
        BillingProfileFrequenciesEnum $frequency = BillingProfileFrequenciesEnum::DAILY,
        ?CompanyUser $contact = null,
        ?int $createdById = null,
        ?int $maxAllowedChargeAttempts = 3,
        ?array $frequencyData = null,
        ?array $associatedCampaignIds = null,
        ?string $createdByType = null,
        ?int $paymentMethodId = null,
        ?int $dueInDays = 0,
        ?int $invoiceTemplateId = null,
        ?string $name = null,
    ): BillingProfile
    {
        if (empty($frequencyData)) {
            $frequencyData = $frequency->cronData();
        }

        $campaignIds = is_null($associatedCampaignIds) || $default
            ? CompanyCampaign::query()
                ->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId)
                ->get()
                ->pluck(CompanyCampaign::FIELD_ID)
                ->toArray()
            : $associatedCampaignIds;

        $contact = $contact ?: CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->first();

        if ($paymentMethod === PaymentMethodServices::MANUAL) {
            $exists = CompanyPaymentMethod::query()
                ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $companyId)
                ->where(CompanyPaymentMethod::FIELD_TYPE, PaymentMethodServices::MANUAL)
                ->exists();

            if (!$exists) {
                CompanyPaymentMethod::query()
                    ->create([
                        CompanyPaymentMethod::FIELD_COMPANY_ID    => $companyId,
                        CompanyPaymentMethod::FIELD_TYPE          => PaymentMethodServices::MANUAL,
                        CompanyPaymentMethod::FIELD_ADDED_BY_TYPE => $createdByType ?? InvoiceEventAuthorTypes::SYSTEM->value,
                        CompanyPaymentMethod::FIELD_IS_DEFAULT    => $default,
                        CompanyPaymentMethod::FIELD_ADDED_BY_ID   => $createdById,
                    ]);
            }
        }

        if (!$default) {
            $companyHasDefault = BillingProfile::query()
                ->where(BillingProfile::FIELD_COMPANY_ID, $companyId)
                ->where(BillingProfile::FIELD_DEFAULT, true)
                ->exists();

            $default = $companyHasDefault ? $default : true;
        }

        return $this->createOrUpdate(
            paymentMethod           : $paymentMethod,
            billingFrequencyCron    : $frequency->value,
            cronData                : $frequencyData,
            companyId               : $companyId,
            thresholdInDollars      : $thresholdInDollars,
            maxAllowedChargeAttempts: $maxAllowedChargeAttempts,
            dueInDays               : $dueInDays,
            createdById             : $createdById,
            associatedCampaignIds   : $campaignIds,
            processAuto             : $processAuto,
            default                 : $default,
            paymentMethodId         : $paymentMethodId,
            invoiceTemplateId       : $invoiceTemplateId,
            name                    : $name
        );
    }

    /**
     * @param PaymentMethodServices $paymentMethod
     * @param string $billingFrequencyCron
     * @param array $cronData
     * @param int $companyId
     * @param int $thresholdInDollars
     * @param int $maxAllowedChargeAttempts
     * @param int $dueInDays
     * @param int|null $createdById
     * @param array|null $associatedCampaignIds
     * @param bool|null $processAuto
     * @param string|null $paymentGatewayPaymentMethodCode
     * @param string|null $paymentGatewayClientCode
     * @param bool|null $default
     * @param int|null $id
     * @param int|null $updatedById
     * @param int|null $paymentMethodId
     * @param int|null $invoiceTemplateId
     * @param string|null $name
     * @return BillingProfile
     */
    protected function createOrUpdate(
        PaymentMethodServices $paymentMethod,
        string $billingFrequencyCron,
        array $cronData,
        int $companyId,
        int $thresholdInDollars,
        int $maxAllowedChargeAttempts,
        int $dueInDays,
        ?int $createdById = null,
        ?array $associatedCampaignIds = [],
        ?bool $processAuto = true,
        ?string $paymentGatewayPaymentMethodCode = null,
        ?string $paymentGatewayClientCode = null,
        ?bool $default = false,
        ?int $id = null,
        ?int $updatedById = null,
        ?int $paymentMethodId = null,
        ?int $invoiceTemplateId = null,
        ?string $name = null,
    ): BillingProfile
    {
        if ($paymentMethod === PaymentMethodServices::MANUAL) {
            $paymentMethodId = null;
            $processAuto = false;
        }

        if (filled($id)) {
            $existingBillingProfile = BillingProfile::query()->findOrFail($id);

            if ($paymentMethod !== $existingBillingProfile->paymentMethod && $paymentMethod === PaymentMethodServices::MANUAL) {
                // If they change the payment method to bank, we need to ensure that they have bank as payment method
                $found = CompanyPaymentMethod::query()
                    ->where([
                        CompanyPaymentMethod::FIELD_COMPANY_ID => $companyId,
                        CompanyPaymentMethod::FIELD_TYPE       => PaymentMethodServices::MANUAL->value,
                    ])->exists();

                if (!$found) {
                    CompanyPaymentMethod::query()
                        ->create([
                            CompanyPaymentMethod::FIELD_COMPANY_ID => $companyId,
                            CompanyPaymentMethod::FIELD_TYPE       => PaymentMethodServices::MANUAL->value,
                        ]);
                }
            }
        }

        $billingProfile = $this->billingProfileRepository->createOrUpdate(
            paymentMethod                  : $paymentMethod,
            billingFrequencyCron           : $billingFrequencyCron,
            cronData                       : $cronData,
            companyId                      : $companyId,
            thresholdInDollars             : $thresholdInDollars,
            maxAllowedChargeAttempts       : $maxAllowedChargeAttempts,
            dueInDays                      : $dueInDays,
            createdById                    : $createdById,
            updatedById                    : $updatedById,
            paymentGatewayClientCode       : $paymentGatewayClientCode,
            paymentGatewayPaymentMethodCode: $paymentGatewayPaymentMethodCode,
            processAuto                    : $processAuto,
            default                        : $default,
            id                             : $id,
            paymentMethodId                : $paymentMethodId,
            invoiceTemplateId              : $invoiceTemplateId,
            name                           : $name
        );

        if ($default) {
            BillingProfile::query()
                ->where(BillingProfile::FIELD_COMPANY_ID, $billingProfile->company_id)
                ->whereNot(BillingProfile::FIELD_ID, $billingProfile->id)
                ->update([
                    BillingProfile::FIELD_DEFAULT => false
                ]);
        }

        $campaignIds = empty($associatedCampaignIds) && empty($id)
            ? CompanyCampaign::withTrashed()
                ->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId)
                ->whereDoesntHave(CompanyCampaign::RELATION_BILLING_PROFILES)
                ->get()
                ->pluck(CompanyCampaign::FIELD_ID)
                ->toArray()
            : $associatedCampaignIds;

        $this->billingProfileRepository->syncAssociatedCompanyCampaigns($billingProfile, $campaignIds);

        return $billingProfile;
    }

    /**
     * @param string $paymentMethod
     * @param int $authorId
     * @return bool|null
     */
    public function deleteBillingProfile(string $paymentMethod, int $authorId): ?bool
    {
        return $this->billingProfileRepository->deleteBillingProfile($paymentMethod);
    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @return bool
     */
    public function updateDefaultBillingProfile(string $customerCode, string $paymentMethod): bool
    {
        return $this->billingProfileRepository->updateDefaultBillingProfile($customerCode, $paymentMethod);
    }

    /**
     * @param int $companyId
     * @param int|null $id
     * @param string|null $paymentMethodCode
     * @param string|null $paymentMethod
     * @return BillingProfile|null
     */
    public function getBillingProfile(
        int $companyId,
        ?int $id = null,
        ?string $paymentMethodCode = null,
        ?string $paymentMethod = null,
    ): ?BillingProfile
    {
        return $this->billingProfileRepository->getBillingProfilesQuery(
            id               : $id,
            companyId        : $companyId,
            paymentMethodCode: $paymentMethodCode,
            paymentMethod    : $paymentMethod
        )->first();
    }

    /**
     * @param int|null $id
     * @param int|null $companyId
     * @param string|null $paymentMethodCode
     * @param string|null $paymentMethod
     * @param int|null $campaignId
     * @param int|null $perPage
     * @param int|null $page
     * @param User|null $filterByUserRole
     * @param array|null $ids
     * @return Builder
     */
    public function getBillingProfiles(
        ?int $id = null,
        ?int $companyId = null,
        ?string $paymentMethodCode = null,
        ?string $paymentMethod = null,
        ?string $showArchived = null,
        ?int $campaignId = null,
        ?int $perPage = 10,
        ?int $page = 1,
        ?User $filterByUserRole = null,
        ?array $ids = [],
    ): Builder
    {
        return $this
            ->billingProfileRepository
            ->getBillingProfilesQuery(
                id               : $id,
                companyId        : $companyId,
                paymentMethodCode: $paymentMethodCode,
                paymentMethod    : $paymentMethod,
                campaignId       : $campaignId,
                filterByUserRole : $filterByUserRole,
                showArchived     : $showArchived === 'all' ? null : (bool)$showArchived,
                ids              : $ids
            );
    }

    /**
     * @param BillingProfile $billingProfile
     * @param string $frequencyType
     * @param array $frequencyData
     * @param float $thresholdInDollars
     * @param int $maxAllowedChargeAttempts
     * @param array $associatedCampaignIds
     * @param bool $processAuto
     * @param bool $default
     * @param int $updatedById
     * @param string $paymentMethod
     * @param int $dueInDays
     * @param int|null $paymentMethodId
     * @param int|null $invoiceTemplateId
     * @return bool
     */
    public function updateBillingProfile(
        BillingProfile $billingProfile,
        string $frequencyType,
        array $frequencyData,
        float $thresholdInDollars,
        int $maxAllowedChargeAttempts,
        array $associatedCampaignIds,
        bool $processAuto,
        bool $default,
        int $updatedById,
        string $paymentMethod,
        int $dueInDays,
        ?int $paymentMethodId = null,
        ?int $invoiceTemplateId = null,
        ?string $name = null,
    ): bool
    {
        $this->createOrUpdate(
            paymentMethod                  : PaymentMethodServices::tryFrom($paymentMethod),
            billingFrequencyCron           : $frequencyType,
            cronData                       : $frequencyData,
            companyId                      : $billingProfile->company_id,
            thresholdInDollars             : $thresholdInDollars,
            maxAllowedChargeAttempts       : $maxAllowedChargeAttempts,
            dueInDays                      : $dueInDays,
            createdById                    : $billingProfile->created_by_id,
            associatedCampaignIds          : $associatedCampaignIds,
            processAuto                    : $processAuto,
            paymentGatewayPaymentMethodCode: $billingProfile->payment_gateway_payment_method_code,
            paymentGatewayClientCode       : $billingProfile->payment_gateway_client_code,
            default                        : $default,
            id                             : $billingProfile->id,
            updatedById                    : $updatedById,
            paymentMethodId                : $paymentMethodId,
            invoiceTemplateId              : $invoiceTemplateId,
            name                           : $name
        );

        return true;
    }


    /**
     * @param int $companyId
     * @return Collection
     */
    public function getCompanyPaymentMethods(int $companyId): Collection
    {
        return $this
            ->billingProfileRepository
            ->getBillingProfilesQuery(companyId: $companyId)
            ->groupBy(BillingProfile::FIELD_PAYMENT_METHOD)
            ->get();
    }

    /**
     * @param int $billingProfileId
     * @return void
     */
    public function updateBillingProfileLastBilledAt(
        int $billingProfileId,
    ): void
    {
        BillingProfile::query()
            ->where(BillingProfile::FIELD_ID, $billingProfileId)
            ->update([
                BillingProfile::FIELD_LAST_BILLED_AT => now()
            ]);
    }

    /**
     * @param BillingProfile $billingProfile
     * @param string $frequencyType
     * @param array $frequencyData
     * @param bool $default
     * @param array $campaignIds
     * @param bool $processAuto
     * @param int $thresholdInDollars
     * @param int $maxChargeAttempts
     * @param User $user
     * @param string $paymentMethod
     * @param int|null $paymentMethodId
     * @param int|null $dueInDays
     * @param int|null $invoiceTemplateId
     * @return void
     * @throws BindingResolutionException
     * @throws ValidationException
     */
    public function updateBillingProfileViaApprovals(
        BillingProfile $billingProfile,
        string $frequencyType,
        array $frequencyData,
        bool $default,
        bool $processAuto,
        int $thresholdInDollars,
        int $maxChargeAttempts,
        User $user,
        string $paymentMethod,
        ?array $campaignIds = null,
        ?int $paymentMethodId = null,
        ?int $dueInDays = null,
        ?int $invoiceTemplateId = null,
        ?string $name = null,
    ): void
    {
        if (!$default) {
            $hasDefaultBillingProfile = $this->billingProfileRepository->hasDefaultBillingProfiles(
                billingProfile: $billingProfile
            );

            if (!$hasDefaultBillingProfile) {
                throw ValidationException::withMessages([
                    'message' => "Company must have at least one preferred billing profile.",
                ]);
            }
        }

        if ($default) {
            $campaignIds = CompanyCampaign::query()
                ->select(CompanyCampaign::FIELD_ID)
                ->where(CompanyCampaign::FIELD_COMPANY_ID, $billingProfile->company_id)
                ->get()
                ->pluck(CompanyCampaign::FIELD_ID)
                ->toArray();
        }

        $this->saveBillingProfileViaApprovals(
            actionRelationType: ApprovableActionRelationTypes::BILLING_PROFILES,
            actionType        : ApprovableActionType::UPDATE_BILLING_PROFILE,
            modelId           : $billingProfile->id,
            frequencyType     : $frequencyType,
            frequencyData     : $frequencyData,
            default           : $default,
            processAuto       : $processAuto,
            thresholdInDollars: $thresholdInDollars,
            maxChargeAttempts : $maxChargeAttempts,
            user              : $user,
            customArguments   : [
                'company_id'         => $billingProfile->company_id,
                'company_name'       => $billingProfile->company->{Company::FIELD_NAME},
                'billing_profile_id' => $billingProfile->id,
                'updated_by_id'      => $user->id,
            ],
            paymentMethod     : $paymentMethod,
            campaignIds       : $campaignIds,
            paymentMethodId   : $paymentMethodId,
            invoiceTemplateId : $invoiceTemplateId,
            dueInDays         : $dueInDays,
            name              : $name
        );
    }


    /**
     * @param int $companyId
     * @param PaymentMethodServices $paymentMethod
     * @param string $frequencyType
     * @param array $frequencyData
     * @param bool $default
     * @param array $campaignIds
     * @param bool $processAuto
     * @param int $thresholdInDollars
     * @param int $maxChargeAttempts
     * @param User $user
     * @param int|null $paymentMethodId
     * @param int|null $dueInDays
     * @param int|null $invoiceTemplateId
     * @return void
     * @throws BindingResolutionException
     */
    public function createBillingProfileViaApprovals(
        int $companyId,
        PaymentMethodServices $paymentMethod,
        string $frequencyType,
        array $frequencyData,
        bool $default,
        bool $processAuto,
        int $thresholdInDollars,
        int $maxChargeAttempts,
        User $user,
        ?array $campaignIds = null,
        ?int $paymentMethodId = null,
        ?int $dueInDays = null,
        ?int $invoiceTemplateId = null,
        ?string $name = null
    ): void
    {
        $this->saveBillingProfileViaApprovals(
            actionRelationType: ApprovableActionRelationTypes::COMPANY,
            actionType        : ApprovableActionType::CREATE_BILLING_PROFILE,
            modelId           : $companyId,
            frequencyType     : $frequencyType,
            frequencyData     : $frequencyData,
            default           : $default,
            processAuto       : $processAuto,
            thresholdInDollars: $thresholdInDollars,
            maxChargeAttempts : $maxChargeAttempts,
            user              : $user,
            customArguments   : [
                'company_id'      => $companyId,
                'company_name'    => Company::query()->findOrFail($companyId)->name,
                'created_by_id'   => $user->id,
                'created_by_type' => User::class,
            ],
            paymentMethod     : $paymentMethod->value,
            campaignIds       : $campaignIds,
            paymentMethodId   : $paymentMethodId,
            invoiceTemplateId : $invoiceTemplateId,
            dueInDays         : $dueInDays,
            name              : $name
        );
    }

    /**
     * @param ApprovableActionRelationTypes $actionRelationType
     * @param ApprovableActionType $actionType
     * @param int $modelId
     * @param string $frequencyType
     * @param array $frequencyData
     * @param bool $default
     * @param array $campaignIds
     * @param bool $processAuto
     * @param int $thresholdInDollars
     * @param int $maxChargeAttempts
     * @param User $user
     * @param array $customArguments
     * @param string $paymentMethod
     * @param int|null $paymentMethodId
     * @param int|null $invoiceTemplateId
     * @param int|null $dueInDays
     * @param string|null $name
     * @return void
     * @throws BindingResolutionException
     */
    protected function saveBillingProfileViaApprovals(
        ApprovableActionRelationTypes $actionRelationType,
        ApprovableActionType $actionType,
        int $modelId,
        string $frequencyType,
        array $frequencyData,
        bool $default,
        bool $processAuto,
        int $thresholdInDollars,
        int $maxChargeAttempts,
        User $user,
        array $customArguments,
        string $paymentMethod,
        ?array $campaignIds = null,
        ?int $paymentMethodId = null,
        ?int $invoiceTemplateId = null,
        ?int $dueInDays = null,
        ?string $name = null,
    ): void
    {
        $billingProfile = $actionType === ApprovableActionType::UPDATE_BILLING_PROFILE
            ? BillingProfile::query()->findOrFail($modelId)
            : null;

        $paymentMethodData = [];

        if ($paymentMethodId && (!$billingProfile || $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD_ID} !== $paymentMethodId)) {
            $companyPaymentMethod = CompanyPaymentMethod::query()->find($paymentMethodId);

            if ($companyPaymentMethod) {
                $paymentMethod = $companyPaymentMethod->{CompanyPaymentMethod::FIELD_TYPE};
                $paymentMethodData = [
                    'payment_method_expiry_month' => $companyPaymentMethod->{CompanyPaymentMethod::FIELD_EXPIRY_MONTH},
                    'payment_method_expiry_year'  => $companyPaymentMethod->{CompanyPaymentMethod::FIELD_EXPIRY_YEAR},
                    'payment_method_number'       => $companyPaymentMethod->{CompanyPaymentMethod::FIELD_NUMBER},
                ];
            }
        }

        $invoiceTemplateData = [];

        if ($invoiceTemplateId && (!$billingProfile || $billingProfile->{BillingProfile::FIELD_INVOICE_TEMPLATE_ID} !== $invoiceTemplateId)) {
            $invoiceTemplate = InvoiceTemplate::query()->find($invoiceTemplateId);

            if ($invoiceTemplate) {
                $invoiceTemplateData = [
                    'invoice_template_name' => $invoiceTemplate->{InvoiceTemplate::FIELD_NAME},
                ];
            }
        }

        $changes = [
            'process_auto'                => $processAuto ? 1 : 0,
            'default'                     => $default ? 1 : 0,
            'threshold_in_dollars'        => $thresholdInDollars,
            'frequency_type'              => $frequencyType,
            'max_allowed_charge_attempts' => $maxChargeAttempts,
            'frequency_data'              => $frequencyData,
            'associated_campaign_ids'     => $campaignIds,
            'payment_method_id'           => $paymentMethodId,
            'payment_method'              => $paymentMethod,
            'invoice_template_id'         => $invoiceTemplateId,
            'due_in_days'                 => $dueInDays,
            'name'                        => $name
        ];

        if ($billingProfile) {
            $isChangingPaymentMethod = $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD}->value !== $paymentMethod;

            $billingProfile->fill([
                ...$changes,
                BillingProfile::FIELD_BILLING_FREQUENCY_CRON => $frequencyType,
                BillingProfile::FIELD_CRON_DATA              => $frequencyData,
            ]);

            $dirtyFields = $billingProfile->getDirty();

            $changes = [
                ...$dirtyFields,
                'associated_campaign_ids' => $campaignIds,
                'payment_method' => $isChangingPaymentMethod || Arr::has($dirtyFields, BillingProfile::FIELD_PAYMENT_METHOD_ID) ? $paymentMethod : null,
                'frequency_data' => ($cronData = Arr::get($dirtyFields, BillingProfile::FIELD_CRON_DATA)) ? json_decode($cronData, true) : null,
                'frequency_type' => Arr::get($dirtyFields, BillingProfile::FIELD_BILLING_FREQUENCY_CRON),
            ];
        }

        if (filled($campaignIds)) {
            $campaignNames = CompanyCampaign::withTrashed()
                ->whereIn(CompanyCampaign::FIELD_ID, $campaignIds)
                ->get()
                ->pluck(CompanyCampaign::FIELD_NAME)
                ->toArray();

            $changes = [
                ...$changes,
                'associated_campaign_ids'   => $campaignIds,
                'associated_campaign_names' => $campaignNames
            ];
        }
        $this->billingActionApprovalService->requestActionExecution(
            referenceId                 : $modelId,
            modelId                     : $modelId,
            type                        : $actionRelationType,
            action                      : $actionType,
            requesterType               : InvoiceEventAuthorTypes::USER,
            requesterId                 : $user->id,
            arguments                   : [
                ...$customArguments,
                ...$paymentMethodData,
                ...$invoiceTemplateData,
                ...$changes
            ],
            enforceSingleApprovalPending: false,
        );
    }

    /**
     * @param BillingProfile $profile
     * @param User $user
     * @return void
     * @throws ValidationException
     */
    public function archive(BillingProfile $profile, User $user): void
    {
        if ($profile->{BillingProfile::FIELD_DEFAULT}) {
            throw ValidationException::withMessages([
                'message' => 'Cannot archive default billing profile'
            ]);
        }

        $this->billingActionApprovalService->requestActionExecution(
            referenceId                 : $profile->{BillingProfile::FIELD_ID},
            modelId                     : $profile->{BillingProfile::FIELD_ID},
            type                        : ApprovableActionRelationTypes::BILLING_PROFILES,
            action                      : ApprovableActionType::ARCHIVE_BILLING_PROFILE,
            requesterType               : InvoiceEventAuthorTypes::USER,
            requesterId                 : $user->id,
            arguments                   : [
                "billingProfileId" => $profile->id,
                "authorType"       => InvoiceEventAuthorTypes::USER->value,
                "authorId"         => $user->{User::FIELD_ID},
                "date"             => now()->toIso8601String(),
            ],
            enforceSingleApprovalPending: false,
        );
    }

    /**
     * @param BillingProfile $profile
     * @param User $user
     * @return void
     * @throws BindingResolutionException
     */
    public function restore(BillingProfile $profile, User $user): void
    {
        $this->billingActionApprovalService->requestActionExecution(
            referenceId                 : $profile->{BillingProfile::FIELD_ID},
            modelId                     : $profile->{BillingProfile::FIELD_ID},
            type                        : ApprovableActionRelationTypes::BILLING_PROFILES,
            action                      : ApprovableActionType::RESTORE_BILLING_PROFILE,
            requesterType               : InvoiceEventAuthorTypes::USER,
            requesterId                 : $user->id,
            arguments                   : [
                "billingProfileId" => $profile->id,
                "authorType"       => InvoiceEventAuthorTypes::USER->value,
                "authorId"         => $user->{User::FIELD_ID},
                "date"             => now()->toIso8601String(),
            ],
            enforceSingleApprovalPending: false,
        );
    }
}
