<?php

namespace App\Services\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\PaymentMethodServices;
use App\Exceptions\Billing\InvoicePaymentOverchargeException;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class InvoicePaymentService
{
    /**
     * @param int $invoiceId
     * @return Collection
     */
    public function getInvoicePayments(int $invoiceId): Collection
    {
        return InvoicePayment::query()
            ->where(InvoicePayment::FIELD_INVOICE_ID, $invoiceId)
            ->get();
    }

    /**
     * @param string $invoiceUuid
     * @param string $invoicePaymentUuid
     * @param int $total
     * @return void
     * @throws InvoicePaymentOverchargeException|ValidationException
     */
    public function validatePaymentForChargeAttempt(
        string $invoiceUuid,
        string $invoicePaymentUuid,
        int $total
    ): void
    {
        if ($total <= 0) {
            throw ValidationException::withMessages([
                'message' => 'The payment amount should be greater than 0'
            ]);
        }

        $invoice = Invoice::findByUuid($invoiceUuid);

        if (!$invoice) {
            throw new Exception('Invoice not found');
        }

        $totalOutstanding = $invoice->getTotalOutstanding();

        if (empty($totalOutstanding)) {
            throw ValidationException::withMessages([
                'message' => 'No outstanding to pay'
            ]);
        }

        $isOvercharge = $total > $totalOutstanding;

        if ($isOvercharge) {
            throw new InvoicePaymentOverchargeException(
                invoiceUuid       : $invoiceUuid,
                invoicePaymentUuid: $invoicePaymentUuid
            );
        }

        $invoiceStatus = $invoice->status->status();

        if (!in_array($invoiceStatus, [
            InvoiceStates::FAILED->value,
            InvoiceStates::ISSUED->value,
        ])) {
            throw new Exception("Cannot pay invoice that is not in issued or failed. Invoice id $invoice->id status $invoiceStatus");
        };

        /** @var InvoicePayment $invoicePayment */
        $invoicePayment = InvoicePayment::findByUuid($invoicePaymentUuid);

        if ($invoicePayment && !$invoicePayment->isChargeable()) {
            throw new Exception("Cannot proceed charge attempt for the payment id $invoicePayment->id. Has it been canceled?");
        }

        $totalPaying = InvoicePayment::query()
            ->where(InvoicePayment::FIELD_INVOICE_ID, $invoice->id)
            ->whereNot(InvoicePayment::FIELD_UUID, $invoicePaymentUuid)
            ->whereIn(InvoicePayment::FIELD_STATUS, [
                InvoicePaymentStatus::REQUESTED,
                InvoicePaymentStatus::PENDING,
                InvoicePaymentStatus::CHARGED,
            ])
            ->get()
            ->sum(InvoicePayment::FIELD_TOTAL);

        $totalIssued = $invoice->getTotalIssuable();

        if ($totalPaying + $total > $totalIssued) {
            throw ValidationException::withMessages([
                'message' => "There's a payment being processed"
            ]);
        }

        if ($invoice->{Invoice::RELATION_BILLING_PROFILE}->{BillingProfile::FIELD_COMPANY_ID} !== $invoice->{Invoice::FIELD_COMPANY_ID}) {
            throw ValidationException::withMessages([
                'message' => 'Cannot pay invoice with a billing profile from a different company'
            ]);
        }
    }

    /**
     * @param InvoicePayment $invoicePayment
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     * @throws Exception
     */
    public function cancelInvoicePayment(
        InvoicePayment $invoicePayment,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null
    ): void
    {
        if (!in_array($invoicePayment->status->value, [
            InvoicePaymentStatus::RESCHEDULED->value,
            InvoicePaymentStatus::PENDING->value,
        ])) {
            throw new Exception('Pending or rescheduled payments can be canceled only');
        }

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoicePayment->{InvoicePayment::RELATION_INVOICE}->{Invoice::FIELD_UUID});

        $invoiceAggregateRoot->charge->cancelInvoicePayment(
            invoicePaymentUuid                       : $invoicePayment->{InvoicePayment::FIELD_UUID},
            authorType                               : $authorType->value,
            invoicePaymentAttemptNumber              : $invoicePayment->{InvoicePayment::FIELD_ATTEMPT_NUMBER},
            invoicePaymentMaxAttemptsPerPaymentMethod: $invoicePayment->{InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD},
            invoicePaymentTotal                      : $invoicePayment->{InvoicePayment::FIELD_TOTAL},
            authorId                                 : $authorId
        );

        $invoiceAggregateRoot->persist();
    }

    /**
     * @param Invoice $invoice
     * @param int $amount
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $billingProfileId
     * @param int|null $authorId
     * @param InvoiceAggregateRoot|null $root
     * @param Carbon|null $date
     * @return void
     * @throws BindingResolutionException
     * @throws InvoicePaymentOverchargeException|ValidationException
     */
    public function payInvoice(
        Invoice $invoice,
        int $amount,
        InvoiceEventAuthorTypes $authorType,
        ?int $billingProfileId = null,
        ?int $authorId = null,
        ?InvoiceAggregateRoot $root = null,
        ?Carbon $date = null
    ): void
    {
        $date = $date ?? Carbon::now();

        $billingProfileId = $billingProfileId ?? $invoice->{Invoice::RELATION_BILLING_PROFILE}->{BillingProfile::FIELD_ID};

        $billingProfile = BillingProfile::query()->findOrFail($billingProfileId);

        $invoicePaymentUuid = Str::uuid()->toString();

        $this->validatePaymentForChargeAttempt(
            invoiceUuid       : $invoice->{Invoice::FIELD_UUID},
            invoicePaymentUuid: $invoicePaymentUuid,
            total             : $amount,
        );

        // Force card payments to have the current date
        $date = $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD}->value === PaymentMethodServices::STRIPE->value
            ? now()->toString()
            : $date;

        if (empty($invoice->getTotalOutstanding())) {
            BillingLogService::log(
                message    : "Invoice has no outstanding to pay",
                namespace  : 'invoice_service',
                relatedType: Invoice::class,
                relatedId  : $invoice->id,
                context    : [
                    'snapshot' => $invoice->lastSnapshot()
                ]
            );
            return;
        }

        $shouldPersist = !$root;

        $root = $root ?: InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        $root->charge->requestInvoiceCharge(
            uuid              : $invoicePaymentUuid,
            invoiceUuid       : $invoice->{Invoice::FIELD_UUID},
            total             : $amount,
            maxAttempts       : $invoice->{Invoice::RELATION_BILLING_PROFILE}->{BillingProfile::FIELD_MAX_ALLOWED_CHARGE_ATTEMPTS},
            billingProfileId  : $billingProfileId,
            authorType        : $authorType->value,
            invoicePaymentDate: $date,
            authorId          : $authorId,
        );

        if ($shouldPersist) {
            $root->persist();
        }
    }
}



