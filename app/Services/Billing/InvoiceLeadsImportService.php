<?php

namespace App\Services\Billing;

use App\DTO\Billing\LeadImportDTO;
use App\Enums\LeadImportStatusEnum;
use App\Jobs\Billing\LeadImport\ImportLeadsToGenerateInvoiceViaImport;
use App\Jobs\Billing\LeadImport\SystemRejectProductAssignmentsJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use App\Services\CSVWrapper;
use App\Services\Odin\ProductAssignmentService;
use Exception;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Throwable;

class InvoiceLeadsImportService
{
    const string PRODUCT_ID_HEADER       = 'S3';
    const string ESTIMATED_PAYOUT_HEADER = 'Estimated Payout';
    const string PRODUCT_STATUS_HEADER   = 'Delivery Status';

    public function __construct(
        protected ProductAssignmentService $productAssignmentService,
        protected CSVWrapper $csvWrapper,
        protected InvoiceService $invoiceService,
    )
    {
    }

    /**
     * @param int $companyId
     * @param UploadedFile $file
     * @return array
     * @throws Throwable
     * @throws ValidationException
     * @throws Exception
     */
    public function getImportLeadsData(int $companyId, UploadedFile $file): array
    {
        $company = Company::query()->findOrFail($companyId);
        $rows = $this->csvWrapper->read($file);

        $this->validateCsvData($rows);

        $importLeadData = $this->mapToDTOs($rows);
        $importLeadDataById = $importLeadData->keyBy(fn(LeadImportDTO $DTO) => $DTO->getConsumerProductId());

        $allConsumerProductIds = $importLeadDataById->keys();

        $allProductAssignments = ProductAssignment::query()
            ->with([
                ProductAssignment::RELATION_CONSUMER,
                ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_ADDRESS,
                ProductAssignment::RELATION_BUDGET
                . '.' . Budget::RELATION_BUDGET_CONTAINER
                . '.' . BudgetContainer::RELATION_CAMPAIGN,
            ])
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->whereIn(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $allConsumerProductIds)
            ->get();

        $allFormattedProductAssignments = $this->formatProductAssignments(
            rawProductAssignments: $allProductAssignments,
            importedLeads        : $importLeadDataById
        );

        $deliveredConsumerProductIds = $importLeadData
            ->filter(fn(LeadImportDTO $leadImportDTO) => $leadImportDTO->getStatus() === LeadImportStatusEnum::DELIVERED)
            ->map(fn(LeadImportDTO $leadImportDTO) => $leadImportDTO->getConsumerProductId());

        $deliveredBillableProductAssignments = $this->productAssignmentService->getUninvoicedProductAssignments(
            company           : $company,
            consumerProductIds: $deliveredConsumerProductIds->toArray()
        );

        $billableConsumerProductIds = $deliveredBillableProductAssignments
            ->map(fn(ProductAssignment $productAssignment) => $productAssignment->consumer_product_id);

        $billableFormattedProductAssignments = $allFormattedProductAssignments
            ->filter(fn(array $item) => $billableConsumerProductIds->contains($item['consumer_product_id']));

        $nonBillableFormattedProductAssignments = $allFormattedProductAssignments
            ->filter(fn(array $item) => !$billableConsumerProductIds->contains($item['consumer_product_id']) && $item['status'] === 'import');

        $nonBillableConsumerProductIds = $nonBillableFormattedProductAssignments->map(fn(array $item) => $item['consumer_product_id']);

        $rejectedProducts = $allFormattedProductAssignments->filter(fn(array $item) => $item['status'] === 'reject');

        $rejectedProductConsumerProductIds = $rejectedProducts->pluck('consumer_product_id');

        $billableFormattedProductAssignments = $billableFormattedProductAssignments
            ->filter(fn($item) => !$nonBillableConsumerProductIds->contains($item['consumer_product_id'])
                && !$rejectedProductConsumerProductIds->contains($item['consumer_product_id'])
            );

        $totalImportPrice = $billableFormattedProductAssignments->sum('import_price');
        $totalSystemPrice = $billableFormattedProductAssignments->sum('system_price');
        $totalBillablePrice = $billableFormattedProductAssignments->sum('billable_price');

        return [
            'company_id'        => $companyId,
            'company_name'      => $company->name,
            'total_discrepancy' => $totalBillablePrice - $totalSystemPrice,

            'total_system_price' => $totalSystemPrice,
            'total_import_price' => $totalImportPrice,

            'not_billable'                  => $nonBillableFormattedProductAssignments->sortByDesc('abs_discrepancy')->values(),
            'total_not_billable_in_dollars' => $nonBillableFormattedProductAssignments->sum('billable_price'),

            'billable'                  => $billableFormattedProductAssignments->sortByDesc('abs_discrepancy')->values(),
            'total_billable_in_dollars' => $totalBillablePrice,

            'rejected'                  => $rejectedProducts->sortByDesc('abs_discrepancy')->values(),
            'total_rejected_in_dollars' => $rejectedProducts->sum('billable_price'),
        ];
    }

    /**
     * @param Collection $rawProductAssignments
     * @param Collection $importedLeads
     * @return Collection
     */
    public function formatProductAssignments(Collection $rawProductAssignments, Collection $importedLeads): Collection
    {
        return $rawProductAssignments->map(function (ProductAssignment $productAssignment) use ($importedLeads) {
            /** @var LeadImportDTO $importLead */
            $importLead = $importedLeads->get($productAssignment->consumer_product_id);

            return [
                'status'                => $importLead->getStatus() === LeadImportStatusEnum::DELIVERED ? 'import' : 'reject',
                'system_price'          => $productAssignment->{ProductAssignment::FIELD_COST},
                'import_price'          => $importLead->getPrice(),
                'billable_price'        => $importLead->getPrice(),
                'first_name'            => $productAssignment->{ProductAssignment::RELATION_CONSUMER}->{Consumer::FIELD_FIRST_NAME},
                'last_name'             => $productAssignment->{ProductAssignment::RELATION_CONSUMER}->{Consumer::FIELD_LAST_NAME},
                'consumer_product_id'   => $productAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID},
                'product_assignment_id' => $productAssignment->{ProductAssignment::FIELD_ID},
                'zipcode'               => $productAssignment->{ProductAssignment::RELATION_CONSUMER_PRODUCT}
                    ->{ConsumerProduct::RELATION_ADDRESS}
                    ->{Address::FIELD_ZIP_CODE},
                'campaign'              => $productAssignment->{ProductAssignment::RELATION_BUDGET}
                    ->{Budget::RELATION_BUDGET_CONTAINER}
                    ->{BudgetContainer::RELATION_CAMPAIGN}
                    ->{CompanyCampaign::FIELD_NAME},
                'discrepancy'           => $importLead->getPrice() - $productAssignment->cost,
                'abs_discrepancy'       => abs($importLead->getPrice() - $productAssignment->cost),
            ];
        });
    }

    /**
     * @param int $companyId
     * @param UploadedFile $file
     * @param User $user
     * @return string
     * @throws Throwable
     * @throws ValidationException
     */
    public function importLeads(
        int $companyId,
        UploadedFile $file,
        User $user
    ): string
    {
        $importData = $this->getImportLeadsData($companyId, $file);

        BillingLogService::log(
            message    : 'Importing leads via csv',
            namespace  : 'invoice_leads_import',
            relatedType: Company::class,
            relatedId  : $companyId,
            context    : $importData,
        );

        [
            'billable' => $billableLeads,
            'rejected' => $rejectedLeads,
        ] = $importData;

        $tempFile = null;
        if (filled($billableLeads)) {
            $tempFile = $this->formatAndExportImportSummary(
                originalFileName: $file->getClientOriginalName(),
                billableLeads   : $billableLeads,
            );

            ImportLeadsToGenerateInvoiceViaImport::dispatch(
                $billableLeads->toArray(),
                $companyId,
                $user->id,
            );
        }

        if (filled($rejectedLeads)) {
            SystemRejectProductAssignmentsJob::dispatch(
                $rejectedLeads->map(fn(array $lead) => $lead['product_assignment_id'])->toArray(),
                $companyId,
                'Rejected via CSV'
            );
        }

        return $tempFile;
    }

    /**
     * @param Collection $data
     * @throws ValidationException
     */
    protected function validateCsvData(Collection $data): void
    {
        if ($data->isEmpty()) {
            throw ValidationException::withMessages([
                'message' => 'Data is empty',
            ]);
        }

        $requiredHeaders = [
            self::PRODUCT_ID_HEADER,
            self::PRODUCT_STATUS_HEADER,
            self::ESTIMATED_PAYOUT_HEADER,
        ];

        $incomingHeaders = array_keys($data->first());

        foreach ($requiredHeaders as $header) {
            if (!in_array($header, $incomingHeaders)) {
                throw ValidationException::withMessages([
                    'message' => "Column $header is required and was not found in csv.",
                ]);
            }
        }

        $invalidDeliveredProducts = $data->filter(function ($item) {
            return Arr::get($item, self::PRODUCT_ID_HEADER)
                && intval(Arr::get($item, self::ESTIMATED_PAYOUT_HEADER)) === 0
                && Str::lower(Arr::get($item, self::PRODUCT_STATUS_HEADER)) === 'delivered';
        })->map(fn($item) => Arr::get($item, self::PRODUCT_ID_HEADER));

        if (filled($invalidDeliveredProducts)) {
            throw ValidationException::withMessages([
                'message' => "Invalid delivered products with $0 as cost: " . $invalidDeliveredProducts->join(', '),
            ]);
        }

        $duplicates = $data
            ->groupBy(fn($item) => Arr::get($item, self::PRODUCT_ID_HEADER))
            ->filter(fn($items) => $items->count() > 1);

        if ($duplicates->isNotEmpty()) {
            throw ValidationException::withMessages([
                'message' => 'Duplicate entries found for product IDs: ' . $duplicates->keys()->implode(', ')
            ]);
        }
    }

    /**
     * @param Collection $rows
     * @return Collection
     */
    protected function mapToDTOs(Collection $rows): Collection
    {
        return $rows->map(function (array $row) {
            $cost = Arr::get($row, self::ESTIMATED_PAYOUT_HEADER);
            $productId = Arr::get($row, self::PRODUCT_ID_HEADER);
            $statusRaw = Str::lower(Arr::get($row, self::PRODUCT_STATUS_HEADER));

            if (empty($cost) || empty($productId) || empty($statusRaw)) {
                return null;
            }

            $status = LeadImportStatusEnum::tryFrom($statusRaw);

            return new LeadImportDTO(
                consumerProductId: $productId,
                status           : $status,
                price            : $cost
            );
        })->filter();
    }

    /**
     * @param string $originalFileName
     * @param Collection $billableLeads
     * @return string
     * @throws Throwable
 */
    protected function formatAndExportImportSummary(
        string $originalFileName,
        Collection $billableLeads
    ): string
    {
        $filePath = storage_path("app/$originalFileName");
        $formattedDataToExport = $billableLeads->map(function ($item) {
            return [
                'Product Id'     => $item['consumer_product_id'],
                'First Name'     => $item['first_name'],
                'Last Name'      => $item['last_name'],
                'Internal Price' => $item['system_price'],
                'External Price' => $item['import_price'],
                'Billable Price' => $item['billable_price'],
                'Discrepancy'    => $item['discrepancy'],
                'Campaign'       => $item['campaign'],
                'Zipcode'        => $item['zipcode'],
            ];
        });

        $totals = $formattedDataToExport->reduce(function ($carry, $item) {
            $carry['Internal Price'] = ($carry['Internal Price'] ?? 0) + $item['Internal Price'];
            $carry['External Price'] = ($carry['External Price'] ?? 0) + $item['External Price'];
            $carry['Billable Price'] = ($carry['Billable Price'] ?? 0) + $item['Billable Price'];
            return $carry;
        }, []);

        $totals = array_merge([
            'Product Id' => 'TOTAL',
            'First Name' => '',
            'Last Name'  => '',
        ], $totals);

        $formattedDataToExport = $formattedDataToExport->push($totals);

        (new CSVWrapper())->write(
            path   : $filePath,
            records: $formattedDataToExport->toArray()
        );

        return $filePath;
    }
}
