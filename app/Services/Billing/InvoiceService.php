<?php

namespace App\Services\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\DTO\Billing\InvoicePayload;
use App\DTO\Billing\InvoiceTaxData;
use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\InvoiceTransactionType;
use App\Exceptions\Billing\InvoicePaymentOverchargeException;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoiceTransaction;
use App\Models\Odin\Company;
use App\Repositories\Billing\CompanyBillingRepository;
use App\Repositories\Billing\InvoiceRepository;
use App\Repositories\Billing\InvoiceSnapshotRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\StoredInvoiceEventRepository;
use App\Services\Billing\BillingProfile\BillingProfileService;
use App\Services\Billing\InvoiceCollections\InvoiceCollectionsService;
use App\Services\CloudStorage\GoogleCloudStorageService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Carbon\Carbon;

class InvoiceService
{
    public function __construct(
        protected InvoiceRepository $invoiceRepository,
        protected StoredInvoiceEventRepository $storedEventRepository,
        protected InvoiceItemService $invoiceItemService,
        protected BillingActionApprovalService $billingActionApprovalService,
        protected InvoiceBillingProfileService $invoiceBillingProfileService,
        protected BillingProfileService $billingProfileService,
        protected InvoiceCollectionsService $invoiceCollectionService,
        protected InvoiceWriteOffService $invoiceWriteOffService,
        protected CompanyBillingRepository $companyBillingRepository,
        protected InvoiceSnapshotRepository $invoiceSnapshotRepository,
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected InvoicePaymentService $invoicePaymentService,
    )
    {
    }

    /**
     * @param array $filters
     * @param bool|null $paginate
     * @return LengthAwarePaginator|Collection
     */
    public function getInvoices(array $filters, ?bool $paginate = true): LengthAwarePaginator|Collection
    {
        return $this->invoiceRepository->getInvoices($filters, $paginate);
    }

    /**
     * @param int $invoiceId
     * @return Invoice
     */
    public function getInvoice(int $invoiceId): Invoice
    {
        return $this->invoiceRepository->getInvoice($invoiceId);
    }

    /**
     * TODO - Move to another service
     * @param Invoice $invoice
     * @return Collection
     */
    public function getInvoiceEvents(Invoice $invoice): Collection
    {
        return $this->storedEventRepository->getInvoiceEvents($invoice->{Invoice::FIELD_UUID});
    }

    /**
     * @param InvoicePayload $invoicePayload
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return string
     * @throws BindingResolutionException
     */
    protected function createInvoice(
        InvoicePayload $invoicePayload,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null,
    ): string
    {
        $invoiceUuid = Str::uuid();

        $company = Company::query()->findOrFail($invoicePayload->getCompanyId());

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoiceUuid);

        $invoiceAggregateRoot->createInvoice(
            invoiceUuid     : $invoiceUuid,
            companyReference: $company->{Company::FIELD_REFERENCE},
            dueDate         : Carbon::parse($invoicePayload->getDueDate())->utc(),
            issueDate       : Carbon::parse($invoicePayload->getIssueDate())->utc(),
            authorType      : $authorType->value,
            billingProfileId: $invoicePayload->getBillingProfileId(),
            authorId        : $authorId,
            notes           : $invoicePayload->getNote(),
        );

        $invoiceAggregateRoot->persist();

        return $invoiceUuid;
    }

    /**
     * @param Invoice $invoice
     * @param string $newStatus
     * @param InvoiceEventAuthorTypes $author
     * @param int|null $authorId
     * @return void
     * @throws Exception
     */
    public function requestInvoiceStatusUpdate(
        Invoice $invoice,
        string $newStatus,
        InvoiceEventAuthorTypes $author,
        ?int $authorId = null
    ): void
    {
        if ($newStatus === InvoiceStates::COLLECTION->value) {
            $this->invoiceCollectionService->issueInvoiceToCollections(
                invoice   : $invoice,
                authorType: $author,
                authorId  : $authorId
            );
        } else if ($newStatus === InvoiceStates::WRITTEN_OFF->value) {
            $this->invoiceWriteOffService->writeOffInvoice(
                invoice   : $invoice,
                authorType: $author,
                authorId  : $authorId
            );
        } else {
            $this->billingActionApprovalService->requestActionExecution(
                referenceId  : $invoice->{Invoice::FIELD_UUID},
                modelId      : $invoice->{Invoice::FIELD_ID},
                type         : ApprovableActionRelationTypes::INVOICES,
                action       : ApprovableActionType::UPDATE_INVOICE_STATUS,
                requesterType: $author,
                requesterId  : $authorId,
                arguments    : [
                    "invoiceUuid" => $invoice->{Invoice::FIELD_UUID},
                    "newStatus"   => $newStatus,
                    "oldStatus"   => $invoice->{Invoice::FIELD_STATUS},
                    "authorType"  => $author->value,
                    "authorId"    => $authorId,
                ],
            );
        }
    }

    /**
     * @param InvoiceAggregateRoot $root
     * @param Invoice $invoice
     * @param string $authorType
     * @param int|null $authorId
     * @return void
     * @throws Exception
     */
    protected function issueInvoice(
        InvoiceAggregateRoot $root,
        Invoice $invoice,
        string $authorType,
        ?int $authorId = null,
    ): void
    {
        $hasBeenInvoiced = InvoiceTransaction::query()
            ->where(InvoiceTransaction::FIELD_INVOICE_UUID, $invoice->{Invoice::FIELD_UUID})
            ->where(InvoiceTransaction::FIELD_TYPE, InvoiceTransactionType::ISSUED->value)
            ->where(InvoiceTransaction::FIELD_AMOUNT, $invoice->getInvoiceItemsTotal())
            ->exists();

        if (!$hasBeenInvoiced) {
            $root->transaction->createInvoiceTransaction(
                uuid             : Str::uuid()->toString(),
                invoiceUuid      : $invoice->{Invoice::FIELD_UUID},
                externalReference: Str::uuid()->toString(),
                amount           : $invoice->getInvoiceItemsTotal(),
                currency         : 'usd',
                type             : InvoiceTransactionType::ISSUED->value,
                origin           : 'A20',
                payload          : [
                    'created_at' => $invoice->{Invoice::FIELD_ISSUE_AT},
                ],
                date             : $invoice->{Invoice::FIELD_ISSUE_AT}
            );

            if ($invoice->canApplyCredits()) {
                $root->credit->checkAvailableAndApplyCreditsToInvoice(
                    invoice            : $invoice,
                    expireDateReference: now(),
                    amountToApply      : $invoice->getInvoiceItemsTotal(),
                    authorType         : InvoiceEventAuthorTypes::SYSTEM->value,
                );
            }
        }

        $root->pdf->createInvoicePdf($invoice->{Invoice::FIELD_UUID});
    }

    /**
     * @param Invoice $invoice
     * @param string $newStatus
     * @param string $authorType
     * @param int|null $authorId
     * @return void
     * @throws BindingResolutionException|InvoicePaymentOverchargeException
     */
    public function updateInvoiceStatus(
        Invoice $invoice,
        string $newStatus,
        string $authorType,
        ?int $authorId = null,
        ?string $date = null
    ): void
    {
        $root = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        match ($newStatus) {
            InvoiceStates::ISSUED->value     => $this->issueInvoice(
                root      : $root,
                invoice   : $invoice,
                authorType: $authorType,
                authorId  : $authorId
            ),
            InvoiceStates::PAID->value       => $this->invoicePaymentService->payInvoice(
                invoice   : $invoice,
                amount    : $invoice->getTotalOutstanding(),
                authorType: InvoiceEventAuthorTypes::tryFrom($authorType),
                authorId  : $authorId,
                root      : $root
            ),
            InvoiceStates::COLLECTION->value => $this->invoiceCollectionService->issueInvoiceToCollections(
                invoice   : $invoice,
                authorType: InvoiceEventAuthorTypes::tryFrom($authorType),
                authorId  : $authorId
            ),
            default                          => $root->updateInvoiceStatus(
                invoice   : $invoice,
                newStatus : $newStatus,
                authorType: $authorType,
                authorId  : $authorId,
                date      : $date
            )
        };

        $root->persist();
    }

    /**
     * @param InvoicePayload $invoicePayload
     * @param InvoiceEventAuthorTypes $authorType
     * @param ?int $authorId
     * @return array
     * @throws Exception
     */
    public function createUpdateInvoice(
        InvoicePayload $invoicePayload,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null
    ): array
    {
        $uuid = $invoicePayload->getUuid();
        /** @var ?Invoice $invoice */
        $invoice = $uuid ? Invoice::findByUuid($uuid) : null;

        if (!$invoicePayload->getBillingProfileId()) {
            $billingProfileId = $this->invoiceBillingProfileService->calculateBillingProfileForInvoice($invoicePayload);
            $invoicePayload->setBillingProfileId($billingProfileId);
        }

        if ($invoice) {
            $this->updateInvoice(
                invoice       : $invoice,
                invoicePayload: $invoicePayload,
                authorType    : $authorType,
                authorId      : $authorId,
            );

            $message = "Invoice Status Updated for UUID: ";
        } else {
            $uuid = $this->createInvoice(
                invoicePayload: $invoicePayload,
                authorType    : $authorType,
                authorId      : $authorId,
            );
            $message = "Invoice Created: ";
        }

        /** @var Invoice $invoice */
        $invoice = $invoice ?? Invoice::findByUuid($uuid);

        $invoice->syncTags($invoicePayload->getTags());

        $this->invoiceItemService->syncInvoiceItems(
            invoiceUuid  : $uuid,
            existingItems: $invoice->{Invoice::RELATION_INVOICE_ITEMS} ?? collect(),
            receivedItems: $invoicePayload->getItems(),
            authorType   : $authorType,
            authorId     : $authorId,
        );

        if ($invoice->{Invoice::FIELD_STATUS}->status() !== $invoicePayload->getStatus()) {
            $this->requestInvoiceStatusUpdate(
                invoice  : $invoice,
                newStatus: $invoicePayload->getStatus(),
                author   : $authorType,
                authorId : $authorId
            );
        }

        return [$uuid, $message];
    }

    /**
     * @param Invoice $invoice
     * @param InvoicePayload $invoicePayload
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return bool
     * @throws BindingResolutionException
     */
    protected function updateInvoice(
        Invoice $invoice,
        InvoicePayload $invoicePayload,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null
    ): bool
    {
        $invoice->fill([
            Invoice::FIELD_COMPANY_ID    => $invoicePayload->getCompanyId(),
            Invoice::FIELD_DUE_AT        => $invoicePayload->getDueDate(),
            Invoice::FIELD_ISSUE_AT      => $invoicePayload->getIssueDate(),
            Invoice::FIELD_NOTES         => $invoicePayload->getNote(),
            Invoice::FIELD_CREATED_BY_ID => $authorId,
        ]);

        $changed = $invoice->getDirty();
        $changes = [];
        foreach ($changed as $key => $value) {
            $original = $invoice->getOriginal($key);
            if ($original !== $value) {
                $changes[] = [
                    'key' => $key,
                    'old' => $original,
                    'new' => $value,
                ];
            }
        }

        $root = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

        if (!collect($changes)->isEmpty() && $invoice->{Invoice::FIELD_STATUS}->status() === InvoiceStates::DRAFT->value) {
            $root->updateInvoice(
                invoiceUuid: $invoice->{Invoice::FIELD_UUID},
                changes    : $changes,
                authorType : $authorType->value,
                authorId   : $authorId,
            );
        }

        $root->persist();

        return true;
    }

    /**
     * TODO - Move to another service
     * @param InvoiceTaxData $invoiceTaxData
     * @param int|null $userId
     * @return void
     * @throws BindingResolutionException
     */
    public function applyTaxToInvoice(InvoiceTaxData $invoiceTaxData, ?int $userId = null): void
    {
        InvoiceAggregateRoot::retrieve($invoiceTaxData->getInvoiceUuid())
            ->applyInvoiceTax(
                invoiceTaxData: $invoiceTaxData,
                authorType    : InvoiceEventAuthorTypes::SYSTEM->value,
                authorId      : $userId,
            )->persist();
    }

    /**
     * @return array
     */
    public function getInvoiceFilters(): array
    {
        return InvoiceStates::cases();
    }

    /**
     * @param array $filters
     * @return Collection
     */
    public function getInvoiceCountByType(array $filters): Collection
    {
        return $this->invoiceRepository->getInvoiceCountGroupedByType($filters);
    }

    /**
     * @param Company $company
     * @return array
     */
    public function getCompanyInvoiceSummary(Company $company): array
    {
        $summary = $this->invoiceSnapshotRepository->getCompanyInvoiceSummary($company);

        $toBeInvoiced = $this->productAssignmentRepository
            ->getCompanyUninvoicedProductAssignmentsValue($company, false);

        $toBeInvoicedUnrejectable = $this->productAssignmentRepository
            ->getCompanyUninvoicedProductAssignmentsValue($company);

        $atomicToBeInvoiced = (int)($toBeInvoiced * 100);
        $atomicToBeInvoicedUnrejectable = (int)($toBeInvoicedUnrejectable * 100);

        return [
            'total'          => $summary->{'total_value'},
            'paid'           => $summary->{'total_paid'},
            'unpaid'         => $summary->{'total_outstanding'},
            'to_be_invoiced' => $atomicToBeInvoiced,
            'non_rejectable' => $atomicToBeInvoicedUnrejectable,
        ];
    }

    /**
     * @return string|null
     * @throws BindingResolutionException
     */
    public function generateSignedUrl(Invoice $invoice): ?string
    {
        $googleCloudStorageService = app()->make(GoogleCloudStorageService::class);
        $url = null;

        if (filled($invoice->invoice_url)) {
            try {
                $url = $googleCloudStorageService->generateSignedUrl(config('services.google.storage.buckets.invoices'), $invoice->invoice_url);
            } catch (Exception $exception) {
                BillingLogService::logException(
                    exception: $exception,
                    namespace: 'generate_invoice_pdf_signed_url'
                );
            }
        }

        return $url;
    }
}



