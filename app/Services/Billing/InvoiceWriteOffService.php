<?php

namespace App\Services\Billing;

use App\Enums\ApprovableAction\ApprovableActionRelationTypes;
use App\Enums\Billing\ApprovableActionType;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceWriteOff;
use App\Repositories\Billing\InvoiceWriteOffsRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class InvoiceWriteOffService
{
    public function __construct(
        protected BillingActionApprovalService $billingActionApprovalService,
        protected InvoiceWriteOffsRepository $invoiceWriteOffRepository
    )
    {

    }

    /**
     * @param int|null $invoiceId
     * @param int|null $companyId
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return Builder
     */
    public function listWriteOffs(
        ?int $invoiceId = null,
        ?int $companyId = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
    ): Builder
    {
        return $this->invoiceWriteOffRepository->list(
            invoiceId: $invoiceId,
            companyId: $companyId,
            dateFrom : $dateFrom,
            dateTo   : $dateTo,
        )
            ->orderByDesc(InvoiceWriteOff::FIELD_ID);
    }

    /**
     * @param Invoice $invoice
     * @param InvoiceEventAuthorTypes $authorType
     * @param int|null $authorId
     * @return void
     * @throws BindingResolutionException
     */
    public function writeOffInvoice(
        Invoice $invoice,
        InvoiceEventAuthorTypes $authorType,
        ?int $authorId = null,
    ): void
    {
        $amountCollected = $invoice->getTotalOutstanding();

        if (empty($amountCollected)) {
            throw new Exception('Cannot write off paid invoices to collections');
        }

        $this->billingActionApprovalService->requestActionExecution(
            referenceId  : $invoice->{Invoice::FIELD_UUID},
            modelId      : $invoice->{Invoice::FIELD_ID},
            type         : ApprovableActionRelationTypes::INVOICES,
            action       : ApprovableActionType::WRITE_OFF_INVOICE,
            requesterType: $authorType,
            requesterId  : $authorId,
            arguments    : [
                "uuid"        => Str::uuid()->toString(),
                "invoiceUuid" => $invoice->{Invoice::FIELD_UUID},
                "authorType"  => $authorType->value,
                "date"        => Carbon::now()->toISOString(),
                "amount"      => $amountCollected,
                "authorId"    => $authorId,
            ],
        );
    }
}
