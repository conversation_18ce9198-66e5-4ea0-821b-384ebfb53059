<?php

namespace App\Services\Billing;

use App\Enums\Billing\BillingLogLevel;
use App\Models\Billing\BillingLog;
use Exception;
use Throwable;

class BillingLogService
{
    const string LOG_FILE_CHANNEL   = 'billing';
    const string LOG_STDERR_CHANNEL = 'stderr';
    const array  LOG_CHANNELS       = [
        self::LOG_FILE_CHANNEL,
        //        self::LOG_STDERR_CHANNEL, // TODO - Make this optional
    ];


    // ANSI color codes
    const string COLOR_RESET  = "\033[0m";
    const string COLOR_RED    = "\033[31m";
    const string COLOR_GREEN  = "\033[32m";
    const string COLOR_YELLOW = "\033[33m";
    const string COLOR_CYAN   = "\033[36m";

    /**
     * @param Exception|Throwable $exception
     * @param string|null $namespace
     * @param string|null $relatedType
     * @param string|null $relatedId
     * @param array $context
     * @return void
     */
    public static function logException(
        Exception|Throwable $exception,
        ?string $namespace = null,
        ?string $relatedType = null,
        ?string $relatedId = null,
        array $context = [],
    ): void
    {
        self::log(
            message    : $exception->getMessage(),
            level      : BillingLogLevel::ERROR,
            namespace  : $namespace,
            relatedType: $relatedType,
            relatedId  : $relatedId,
            context    : [
                ...$context,
                'file'  => $exception->getFile(),
                'line'  => $exception->getLine(),
                'code'  => $exception->getCode(),
                'trace' => $exception->getTraceAsString(),
            ],
            trace      : $exception->getTraceAsString(),
        );
    }

    /**
     * TODO - Check log level in env?
     * @param string $message
     * @param BillingLogLevel|null $level
     * @param string|null $namespace
     * @param string|null $relatedType
     * @param int|string|null $relatedId
     * @param array $context
     * @param string|null $trace
     * @return void
     */
    public static function log(
        string $message,
        ?BillingLogLevel $level = BillingLogLevel::INFO,
        ?string $namespace = null,
        ?string $relatedType = null,
        int|string|null $relatedId = null,
        array $context = [],
        ?string $trace = null,
    ): void
    {
        try {
            $clean = json_encode($context, JSON_PARTIAL_OUTPUT_ON_ERROR);

            $log = BillingLog::query()->create([
                BillingLog::FIELD_RELATED_TYPE => $relatedType,
                BillingLog::FIELD_RELATED_ID   => $relatedId,
                BillingLog::FIELD_CONTEXT      => $clean,
                BillingLog::FIELD_NAMESPACE    => $namespace,
                BillingLog::FIELD_MESSAGE      => $message,
                BillingLog::FIELD_TRACE        => $trace,
                BillingLog::FIELD_LEVEL        => $level->value,
            ]);

            self::logToChannels(
                log        : $log,
                message    : $message,
                level      : $level,
                namespace  : $namespace,
                context    : $context,
                relatedType: $relatedType,
                relatedId  : $relatedId,
            );
        } catch (Exception $exception) {
            logger()->error($exception);
        }
    }

    /**
     * @param BillingLog $log
     * @param string $message
     * @param BillingLogLevel|null $level
     * @param string|null $namespace
     * @param array $context
     * @param string|null $relatedType
     * @param int|string|null $relatedId
     * @return void
     */
    protected static function logToChannels(
        BillingLog $log,
        string $message,
        ?BillingLogLevel $level = BillingLogLevel::INFO,
        ?string $namespace = null,
        array $context = [],
        ?string $relatedType = null,
        int|string|null $relatedId = null,
    ): void
    {
        $consolidatedMessage =
            ($level === BillingLogLevel::ERROR ? self::COLOR_RED : self::COLOR_GREEN) . $log->id . self::COLOR_RESET .
            self::COLOR_YELLOW . " [$namespace] " . self::COLOR_RESET .
            " - " . ($level === BillingLogLevel::ERROR ? self::COLOR_RED : self::COLOR_CYAN) . $message . self::COLOR_RESET . "\n";

        try {
            $consolidatedMessage .= json_encode([
                ...$context,
                "related_type" => $relatedType,
                "related_id"   => $relatedId,
            ], JSON_UNESCAPED_UNICODE);
        } catch (Exception $exception) {
            logger()->error($exception->getMessage());
        }

        $consolidatedMessage .= "\n";

        foreach (self::LOG_CHANNELS as $channel) {
            logger()
                ->channel($channel)
                ->log($level->value, $consolidatedMessage);
        }
    }
}
