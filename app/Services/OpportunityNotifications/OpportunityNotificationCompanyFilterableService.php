<?php

namespace App\Services\OpportunityNotifications;

use App\Builders\Odin\Company\CompanySearchBuilder;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\UserPreset;
use App\Services\Filterables\Company\CompanyFilterableService;
use Illuminate\Database\Eloquent\Builder;

class OpportunityNotificationCompanyFilterableService
{
    public function __construct(
        protected CompanyFilterableService $companyFilterableService,
    ) {}

    public function applyCompanyUsersFilters($query)
    {
        $query
            ->whereNotNull(CompanyUser::TABLE . '.' . CompanyUser::FIELD_EMAIL)
            ->whereNotNull(CompanyUser::TABLE . '.' . CompanyUser::FIELD_REFERENCE)
            ->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_REFERENCE, '<>' , '')
            ->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
            ->where(CompanyUser::TABLE . '.' . CompanyUser::FIELD_UNSUBSCRIBED_FROM_PROMOTIONS, false);

        return $query;
    }

    /**
     * @param UserPreset $filter
     * @return Builder
     */
    public function query(UserPreset $filter): Builder
    {
        $query = CompanySearchBuilder::query()
            ->getQuery()
            ->where(function ($query) {
                $this->applyCompanyUsersFilters($query)
                    ->selectRaw('COUNT(' . CompanyUser::TABLE . '.' . CompanyUser::FIELD_ID .')')
                    ->from(CompanyUser::TABLE)
                    ->whereColumn(CompanyUser::TABLE.'.'.CompanyUser::FIELD_COMPANY_ID, Company::TABLE . '.' . Company::FIELD_ID)
                    ->groupBy(CompanyUser::TABLE . '.' . CompanyUser::FIELD_COMPANY_ID);
            }, '>', 0);

        return $this->companyFilterableService->runQuery($filter->{UserPreset::FIELD_VALUE}, $query);
    }
}
