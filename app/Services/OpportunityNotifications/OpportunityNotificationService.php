<?php

namespace App\Services\OpportunityNotifications;

use App\Enums\OpportunityNotifications\OpportunityNotificationDeliveryMethods;
use App\Jobs\OpportunityNotifications\SendCampaignSummaryOpportunityNotificationJob;
use App\Mail\OpportunityNotifications\OpportunityNotificationEmail;
use App\Models\EmailTemplate;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\MissedProducts\OpportunityNotificationConfigRepository;
use App\Repositories\MissedProducts\OpportunityNotificationRepository;
use App\Services\Delivery\Email;
use Carbon\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;

class OpportunityNotificationService
{
    public function __construct(
        protected OpportunityNotificationRepository $notificationRepository,
        protected OpportunityNotificationConfigRepository $configRepository
    ) {}

    /**
     * @param int $companyUserId
     * @param int $opportunityConfigurationId
     * @return string
     */
    public function generateEmailTemplatePreview(int $companyUserId, int $opportunityConfigurationId): string
    {
        $contact = CompanyUser::query()
            ->where(CompanyUser::FIELD_ID, $companyUserId)
            ->firstOrFail();

        $opportunityConfiguration = OpportunityNotificationConfig::query()
            ->where(OpportunityNotificationConfig::FIELD_ID, $opportunityConfigurationId)
            ->firstOrFail();

        $message = App::make(OpportunityNotificationEmail::class, [
            'companyUser' => $contact,
            'template' => $this->getEmailTemplate($opportunityConfiguration),
            'notificationConfig' => $opportunityConfiguration
        ]);

        return $message->render();
    }

    /**
     * Builds and send a notification to send to eligible company users and saves it ot a new OpportunityNotification.
     *
     * @param array $recipientIds
     * @param int $companyId
     * @param int $configId
     * @param bool $sendTest
     * @return bool
     */
    public function sendNotification(
        array $recipientIds,
        int $companyId,
        int $configId,
        bool $sendTest = false
    ): bool {
        $company = Company::query()->findOrFail($companyId);
        $config = OpportunityNotificationConfig::query()->findOrFail($configId);
        $companyUsers = CompanyUser::query()->findMany($recipientIds);

        foreach ($companyUsers as $user) {
            $toAddress = $user->email;
            $message = App::make(OpportunityNotificationEmail::class, [
                'companyUser' => $user,
                'template' => $this->getEmailTemplate($config),
                'notificationConfig' => $config
            ]);

            logger()->debug("Missed Product email sending disabled, target email address was: " . $toAddress);
//            Email::send($toAddress, $message, $sendTest); // The Email class will override the 'to' addresses if in test mode

            if (config('app.outgoing_communication.test_mode') === true || $sendTest === true) {
                $testEmail = config('app.outgoing_communication.test_email');
                $toAddress = !is_array($testEmail) ? $testEmail: implode(', ', $testEmail);
            }

            $this->notificationRepository->createNotification(
                $toAddress,
                $message->render(),
                OpportunityNotificationDeliveryMethods::EMAIL->value,
                $company->{Company::FIELD_ID},
                Carbon::now(),
                $config->{OpportunityNotificationConfig::FIELD_ID}
            );

            // Only send once when it's a test sending
            if ($sendTest)
                return true;
        }

        return true;
    }

    /**
     * @param OpportunityNotificationConfig $config
     * @return EmailTemplate
     */
    protected function getEmailTemplate(OpportunityNotificationConfig $config): EmailTemplate
    {
        /** @var EmailTemplate */
        return EmailTemplate::query()
            ->where(EmailTemplate::FIELD_TYPE, $config->type->getEmailTemplate())
            ->where(EmailTemplate::FIELD_INDUSTRY_ID, null)
            ->firstOrFail();
    }

    /**
     * @param string $companyUserRef
     * @return bool
     */
    public function unsubscribeUser(string $companyUserRef): bool
    {
        $contact = CompanyUser::query()
            ->where(CompanyUser::FIELD_REFERENCE, $companyUserRef)
            ->firstOrFail();

        return $contact->update([
            CompanyUser::FIELD_UNSUBSCRIBED_FROM_PROMOTIONS => true,
        ]);
    }

    /**
     * @param Company $company
     * @param bool $preferDecisionMakerOnly
     * @return Collection
     */
    public function getAvailableEmailRecipients(Company $company, bool $preferDecisionMakerOnly = false): Collection
    {
        /** @var OpportunityNotificationCompanyFilterableService $opportunityNotificationCompanyFilterableService */
        $opportunityNotificationCompanyFilterableService = app(OpportunityNotificationCompanyFilterableService::class);

        $query = $opportunityNotificationCompanyFilterableService
            ->applyCompanyUsersFilters($company->users())
            ->select([
                CompanyUser::FIELD_ID,
                CompanyUser::FIELD_EMAIL,
                CompanyUser::FIELD_IS_DECISION_MAKER
            ]);

        $decisionMakers = $query->clone()
            ->where(CompanyUser::FIELD_IS_DECISION_MAKER, true);

        if ($preferDecisionMakerOnly && $decisionMakers->count())
            return $decisionMakers->get();

        return $query
            ->orderBy(CompanyUser::FIELD_IS_DECISION_MAKER, 'desc')
            ->get()
            ->unique(CompanyUser::FIELD_EMAIL);
    }

    /**
     * @param Company $company
     * @param OpportunityNotificationConfig $config
     * @return bool
     */
    public function companyCanBeNotified(Company $company, OpportunityNotificationConfig $config): bool
    {
        $lastNotifiedAt = OpportunityNotification::query()
            ->where(OpportunityNotification::FIELD_COMPANY_ID, $company->id)
            ->latest()
            ->first()
            ?->sent_at
            ?? null;

        if (!$lastNotifiedAt)
            return true;

        $cooldownHours = 24 * ($config->maximum_send_frequency ?: SendCampaignSummaryOpportunityNotificationJob::DEFAULT_NOTIFICATION_COOLDOWN_IN_DAYS);

        $sendTime = Carbon::createFromFormat('H:i:s', $config->send_time);

        // The daily processing job runs early in the morning, add some leeway hours to account for send time vs processing time lapse
        return (int) $sendTime->diffInHours($lastNotifiedAt, true) >= ($cooldownHours - 8);
    }
}
