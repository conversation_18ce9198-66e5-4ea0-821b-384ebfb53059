<?php

namespace App\Services\TestLead;

use App\Enums\CompanyCampaignSource;
use App\Enums\TestProducts\ProductAuthorEnum;
use App\Jobs\CreateAndDeliverTestProductJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaign;
use App\Repositories\Odin\TestLeadApiRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class TestLeadService
{
    const FIELD_PRODUCT_AUTHOR = 'product_author';

    public function __construct(
        protected TestLeadApiRepository $testLeadApiRepository
    )
    {}

    /**
     * @param array $validated
     * @return LengthAwarePaginator|Collection
     */
    public function getTestProducts(array $validated): LengthAwarePaginator|Collection
    {
        if (isset($validated[self::FIELD_PRODUCT_AUTHOR])) {
            if (in_array(ProductAuthorEnum::ALL_PRODUCTS->value, $validated[self::FIELD_PRODUCT_AUTHOR])) {
                unset($validated[self::FIELD_PRODUCT_AUTHOR]);
            }
        }

        return $this->testLeadApiRepository->getTestProducts($validated);
    }


    /**
     * @param Company $company
     * @param int $campaignId
     * @param CompanyCampaignSource $campaignSource
     * @param int|null $userId
     * @param Carbon|null $revealAt
     * @return void
     * @throws Exception
     */
    public function createMultiCampaignTestLead(
        Company $company,
        int $campaignId,
        CompanyCampaignSource $campaignSource,
        int $userId = null,
        Carbon $revealAt = null
    ): void
    {
        $campaign = null;
        $industryService = null;

        switch ($campaignSource) {
            case CompanyCampaignSource::PRODUCT_CAMPAIGN:
                $campaign = ProductCampaign::query()
                    ->where(ProductCampaign::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
                    ->where(ProductCampaign::FIELD_ID, $campaignId)
                    ->firstOrFail();

                $industryService = $campaign->service ?? IndustryService::query()->firstOrFail();
                break;
            case CompanyCampaignSource::LEAD_CAMPAIGN:
                $campaign = LeadCampaign::query()
                    ->where(LeadCampaign::ID, $campaignId)
                    ->firstOrFail();
                //we need to infer the industry service from the company because leadCampaign doesn't store it
                $industryService = $company->services()->first() ?? IndustryService::query()->firstOrFail();
                break;
            case CompanyCampaignSource::COMPANY_CAMPAIGN:
                /** @var CompanyCampaign $campaign */
                $campaign = CompanyCampaign::query()
                    ->where(CompanyCampaign::FIELD_ID, $campaignId)
                    ->where(CompanyCampaign::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
                    ->firstOrFail();
                //we need to infer the industry service from the company because leadCampaign doesn't store it
                $industryService = $campaign->service;
                break;
            default:
                throw new Exception('Campaign source not found');
        }

        $revealAt = $revealAt ?: now()->addDay(); //todo: reveal at

        CreateAndDeliverTestProductJob::dispatch($company, $industryService, $revealAt, $userId, $campaign);
    }


}
