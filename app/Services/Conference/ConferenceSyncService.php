<?php

namespace App\Services\Conference;

use App\DTO\Meet\ConferenceDTO;
use App\Models\Conference\Conference;
use App\Repositories\Conference\ConferenceRepository;
use Carbon\Carbon;
use Google\Service\Exception;

class ConferenceSyncService
{
    public function __construct(
        protected ConferenceParticipantSyncService $conferenceParticipantSyncService,
        protected ConferenceTranscriptSyncService $conferenceTranscriptSyncService,
        protected ConferenceRecordingSyncService $conferenceRecordingSyncService,
        protected ConferenceRepository $conferenceRepository
    )
    {

    }


    /**
     * @param int $userId
     * @param int $calendarEventId
     * @param ConferenceDTO $conference
     * @return void
     */
    public function importConference(
        int $userId,
        int $calendarEventId,
        ConferenceDTO $conference
    ): void
    {
        $storedConference = $this->syncConference(
            calendarEventId: $calendarEventId,
            userId         : $userId,
            conferenceData : $conference
        );

        $storedParticipants = $this->conferenceParticipantSyncService->syncParticipants(
            conferenceId: $storedConference->id,
            participants: $conference->getParticipants(),
        );

        $storedTranscripts = $this->conferenceTranscriptSyncService->syncTranscripts(
            conference        : $storedConference,
            transcripts       : $conference->getTranscripts(),
            storedParticipants: $storedParticipants
        );

        $this->conferenceRecordingSyncService->syncRecordings(
            conferenceId: $storedConference->id,
            recordings  : $conference->getRecordings(),
        );
    }

    /**
     * @param int $calendarEventId
     * @param int $userId
     * @param ConferenceDTO $conferenceData
     * @return Conference
     */
    private function syncConference(
        int $calendarEventId,
        int $userId,
        ConferenceDTO $conferenceData
    ): Conference
    {
        $durationInSeconds = 0;

        if ($conferenceData->getEndTime() && $conferenceData->getStartTime()) {
            $durationInSeconds = Carbon::parse($conferenceData->getEndTime())
                ->diffInSeconds($conferenceData->getStartTime());
        }

        return $this->conferenceRepository->updateOrCreate(
            calendarEventId  : $calendarEventId,
            userId           : $userId,
            externalId       : $conferenceData->getName(),
            startTime        : $conferenceData->getStartTime(),
            endTime          : $conferenceData->getEndTime(),
            expireTime       : $conferenceData->getExpireTime(),
            durationInSeconds: $durationInSeconds,
        );
    }
}
