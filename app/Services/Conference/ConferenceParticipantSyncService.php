<?php

namespace App\Services\Conference;

use App\DTO\Meet\ConferenceParticipantDTO;
use App\Models\Conference\ConferenceParticipant;
use App\Repositories\Conference\ConferenceParticipantRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ConferenceParticipantSyncService
{
    public function __construct(
        protected ConferenceParticipantRepository $conferenceParticipantRepository,
    )
    {

    }

    /**
     * @param int $conferenceId
     * @param ConferenceParticipantDTO $participantData
     * @return ConferenceParticipant
     */
    public function syncParticipant(
        int $conferenceId,
        ConferenceParticipantDTO $participantData
    ): ConferenceParticipant
    {
        $durationInSeconds = 0;

        if ($participantData->getLatestEndTime() && $participantData->getEarliestStartTime()) {
            $durationInSeconds = Carbon::parse($participantData->getLatestEndTime())
                ->diffInSeconds($participantData->getEarliestStartTime());
        }

        return $this->conferenceParticipantRepository->updateOrCreate(
            conferenceId     : $conferenceId,
            externalId       : $participantData->getName(),
            name             : $participantData->getUserDisplayName(),
            earliestStartTime: $participantData->getEarliestStartTime(),
            latestEndTime    : $participantData->getLatestEndTime(),
            durationInSeconds: $durationInSeconds,
        );
    }

    /**
     * @param int $conferenceId
     * @param Collection<ConferenceParticipantDTO> $participants
     * @return Collection
     */
    public function syncParticipants(
        int $conferenceId,
        Collection $participants
    ): Collection
    {
        $result = collect();
        foreach ($participants as $participant) {
            $storedParticipant = $this->syncParticipant(
                conferenceId   : $conferenceId,
                participantData: $participant,
            );

            $result->push($storedParticipant);
        }

        return $result;
    }
}
