<?php

namespace App\Services\Conference;

use App\DTO\Meet\ConferenceRecordingDTO;
use App\Models\Conference\ConferenceRecording;
use App\Repositories\Conference\ConferenceRecordingRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ConferenceRecordingSyncService
{
    public function __construct(
        protected ConferenceRecordingRepository $conferenceRecordingRepository,
    )
    {

    }

    /**
     * @param int $conferenceId
     * @param ConferenceRecordingDTO $recordingData
     * @return ConferenceRecording
     */
    public function syncRecording(
        int $conferenceId,
        ConferenceRecordingDTO $recordingData
    ): ConferenceRecording
    {
        $durationInSeconds = 0;

        if ($recordingData->getEndTime() && $recordingData->getStartTime()) {
            $durationInSeconds = Carbon::parse($recordingData->getEndTime())
                ->diffInSeconds($recordingData->getStartTime());
        }

        return $this->conferenceRecordingRepository->updateOrCreate(
            conferenceId             : $conferenceId,
            externalId               : $recordingData->getName(),
            externalDestinationFileId: $recordingData->getDriveDestinationFile(),
            startTime                : $recordingData->getStartTime(),
            endTime                  : $recordingData->getEndTime(),
            durationInSeconds        : $durationInSeconds,
        );
    }

    /**
     * @param int $conferenceId
     * @param Collection<ConferenceRecordingDTO> $recordings
     * @return Collection
     */
    public function syncRecordings(
        int $conferenceId,
        Collection $recordings,
    ): Collection
    {
        $result = collect();
        /** @var  $recording */
        foreach ($recordings as $recording) {
            $storedRecording = $this->syncRecording(
                conferenceId : $conferenceId,
                recordingData: $recording
            );

            $result->push($storedRecording);
        }

        return $result;
    }
}
