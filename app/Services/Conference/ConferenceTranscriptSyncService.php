<?php

namespace App\Services\Conference;

use App\DTO\Meet\ConferenceTranscriptDTO;
use App\DTO\Meet\ConferenceTranscriptEntryDTO;
use App\Enums\GoogleServiceType;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Conference\ConferenceTranscript;
use App\Models\GoogleUserToken;
use App\Repositories\Conference\ConferenceTranscriptEntryRepository;
use App\Repositories\Conference\ConferenceTranscriptRepository;
use App\Services\Calendar\Google\GoogleDriveProvider;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;

class ConferenceTranscriptSyncService
{
    public function __construct(
        protected ConferenceTranscriptEntryRepository $conferenceTranscriptEntryRepository,
        protected ConferenceTranscriptRepository $conferenceTranscriptRepository,
    )
    {

    }


    /**
     * @param int $storedTranscriptId
     * @param Collection<ConferenceTranscriptEntryDTO> $entries
     * @param Collection<ConferenceParticipant> $storedParticipants
     * @return void
     */
    private function syncTranscriptEntries(
        int $storedTranscriptId,
        Collection $entries,
        Collection $storedParticipants
    ): void
    {
        foreach ($entries as $entry) {
            try {
                $conferenceParticipantId = $storedParticipants
                    ->first(fn(ConferenceParticipant $item) => $item->external_id === $entry->getParticipant())
                    ?->{ConferenceParticipant::FIELD_ID};

                $this->conferenceTranscriptEntryRepository->updateOrCreate(
                    conferenceTranscriptId : $storedTranscriptId,
                    conferenceParticipantId: $conferenceParticipantId,
                    externalId             : $entry->getName(),
                    externalParticipantId  : $entry->getParticipant(),
                    startTime              : $entry->getStartTime(),
                    endTime                : $entry->getEndTime(),
                    text                   : $entry->getText(),
                );
            } catch (Exception $exception) {
                logger()->error($exception);
            }
        }
    }

    /**
     * @param Conference $conference
     * @param ConferenceTranscriptDTO $transcriptData
     * @param Collection $storedParticipants
     * @return ConferenceTranscript
     */
    public function syncTranscript(
        Conference $conference,
        ConferenceTranscriptDTO $transcriptData,
        Collection $storedParticipants
    ): ConferenceTranscript
    {
        $durationInSeconds = 0;

        if ($transcriptData->getEndTime() && $transcriptData->getStartTime()) {
            $durationInSeconds = Carbon::parse($transcriptData->getEndTime())
                ->diffInSeconds($transcriptData->getStartTime());
        }

        $storedTranscript = $this->conferenceTranscriptRepository->updateOrCreate(
            conferenceId              : $conference->id,
            externalId                : $transcriptData->getName(),
            endTime                   : $transcriptData->getEndTime(),
            startTime                 : $transcriptData->getStartTime(),
            durationInSeconds         : $durationInSeconds,
            docsDestinationDocumentId : $transcriptData->getDocsDestinationDocumentId(),
            docsDestinationDocumentUrl: $transcriptData->getDocsDestinationDocumentUrl(),
        );

        $this->syncTranscriptEntries(
            storedTranscriptId: $storedTranscript->id,
            entries           : $transcriptData->getEntries(),
            storedParticipants: $storedParticipants,
        );

        if ($transcriptData->getDocsDestinationDocumentId()) {
            try {
                $this->removeTranscript(
                    conference          : $conference,
                    externalTranscriptId: $transcriptData->getDocsDestinationDocumentId()
                );
            } catch (Exception $exception) {
                logger()->error($exception);
            }
        }

        return $storedTranscript;
    }

    /**
     * @param Conference $conference
     * @param string $externalTranscriptId
     * @return void
     * @throws Exception
     */
    private function removeTranscript(
        Conference $conference,
        string $externalTranscriptId
    ): void
    {
        $transcriptOwnerDriveToken = GoogleUserToken::query()
            ->where(GoogleUserToken::FIELD_SERVICE, GoogleServiceType::DRIVE->value)
            ->where(GoogleUserToken::FIELD_USER_ID, $conference->user_id)
            ->first();

        if ($transcriptOwnerDriveToken) {
            $googleDriveProvider = new GoogleDriveProvider($transcriptOwnerDriveToken);

            $googleDriveProvider->deleteFile($externalTranscriptId);
        }

    }

    /**
     * @param Conference $conference
     * @param Collection<ConferenceTranscriptDTO> $transcripts
     * @param Collection $storedParticipants
     * @return Collection
     */
    public function syncTranscripts(
        Conference $conference,
        Collection $transcripts,
        Collection $storedParticipants,
    ): Collection
    {
        $result = collect();

        foreach ($transcripts as $transcript) {
            $storedTranscript = $this->syncTranscript(
                conference        : $conference,
                transcriptData    : $transcript,
                storedParticipants: $storedParticipants
            );

            $result->push($storedTranscript);
        }

        return $result;
    }
}
