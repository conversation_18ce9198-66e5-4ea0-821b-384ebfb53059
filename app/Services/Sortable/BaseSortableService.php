<?php

namespace App\Services\Sortable;

use Illuminate\Database\Eloquent\Builder;

abstract class BaseSortableService
{
    protected array $options = [];

    public function __construct(
        protected array $fields,
    ) {

    }

    public function getQuery(Builder $existingQuery): Builder
    {
        foreach ($this->fields as $field) {
            $fieldId = data_get($field, 'id');

            $fieldDirection = data_get($field, 'direction');

            /**
             * @var BaseSortableOption $optionAlignedWithField
             */
            $optionAlignedWithField = collect($this->options)->filter(function ($option) use ($fieldId) {
                $option = app($option);

                /**
                 * @var BaseSortableOption $option
                 */
                return $option->getId() === $fieldId;
            })->first();

            if ($optionAlignedWithField) {
                $optionAlignedWithField = app($optionAlignedWithField);

                $optionAlignedWithField?->appendToQuery($existingQuery, $fieldDirection);
            }
        }

        return $existingQuery;
    }
}
