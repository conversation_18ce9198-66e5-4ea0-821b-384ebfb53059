<?php

namespace App\Services\Sortable\Company;

use App\Models\Cadence\CadenceRoutine;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyCadenceNameSortableOption extends BaseSortableOption
{
    protected string $id = 'company-cadence-name';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        if (!DatabaseHelperService::joinExists($existingQuery->getQuery(), CompanyCadenceRoutine::TABLE)) {
            $existingQuery->leftJoin(CompanyCadenceRoutine::TABLE, CompanyCadenceRoutine::TABLE .'.'. CompanyCadenceRoutine::FIELD_COMPANY_ID, '=', Company::TABLE .'.'. Company::FIELD_ID);
        }
        if (!DatabaseHelperService::joinExists($existingQuery->getQuery(), CadenceRoutine::TABLE)) {
            $existingQuery->leftJoin(CadenceRoutine::TABLE, CadenceRoutine::TABLE .'.'. CadenceRoutine::FIELD_ID, '=', CompanyCadenceRoutine::TABLE .'.'. CompanyCadenceRoutine::FIELD_CADENCE_ROUTINE_ID);
        }

        $existingQuery->orderBy(CadenceRoutine::TABLE .'.'. CadenceRoutine::FIELD_NAME, $direction);
    }
}
