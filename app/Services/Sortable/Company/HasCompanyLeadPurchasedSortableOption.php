<?php

namespace App\Services\Sortable\Company;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\Company\CompanyAmountOfLeadsPurchasedFilterable;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Expression;
use Illuminate\Database\Query\JoinClause;

trait HasCompanyLeadPurchasedSortableOption
{
    /**
     * @param  Builder  $builder
     * @param  bool  $leadCostTwoSorted
     * @return Builder
     */
    private function buildQuery(Builder $builder, bool $leadCostTwoSorted = false): Builder
    {
        $columns = $builder->getQuery()->columns;

        $leadCostOneAlreadyCalled = false;

        $leadCostTwoAlreadyCalled = false;

        /**
         * @var Expression|string $column
         */
        foreach ($columns as $column) {
            if ($column instanceof Expression) {
                $grammar = $builder->getQuery()->getGrammar();

                $column = $column->getValue($grammar);
            }

            if (str_contains($column, CompanyAmountOfLeadsPurchasedFilterable::COLUMN_LEAD_COST_ONE)) {
                $leadCostOneAlreadyCalled = true;
            }

            if (str_contains($column, CompanyAmountOfLeadsPurchasedFilterable::COLUMN_LEAD_COST_TWO)) {
                $leadCostTwoAlreadyCalled = true;
            }
        }

        if ($leadCostTwoSorted && $leadCostTwoAlreadyCalled) {
            return $builder;
        }

        if ($leadCostOneAlreadyCalled) {
            return $builder;
        }

        $builder->leftJoin(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.' as '.EloquentQuoteCompany::TABLE,
            function (JoinClause $join) {
                $join->on(Company::TABLE.".".Company::FIELD_LEGACY_ID, '=',
                    EloquentQuoteCompany::TABLE.".".EloquentQuoteCompany::COMPANY_ID);

                $days = config('models.EloquentQuoteCompany.range_by_delivered_timestamp_in_days');
                $join->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                    '>=', now()->subDays($days)->timestamp);
            });

        $builder->where(function ($query) {
            $query->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, '=',
                1);

            $query->orWhereNull(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE);
        });

        $builder->where(function ($query) {
            $query->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, '=',
                1);

            $query->orWhereNull(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED);
        });

        $builder->where(function ($query) {
            $query->whereBetween(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, [
                now()->subDays(30)->timestamp,
                now()->timestamp
            ]);

            $query->orWhereNull(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY);
        });

        $legacyLeadTableName = EloquentQuoteCompany::TABLE;

        $leadCostOneColumn = CompanyAmountOfLeadsPurchasedFilterable::COLUMN_LEAD_COST_ONE;

        $select = "SUM(`$legacyLeadTableName`.`cost`) AS `$leadCostOneColumn`";

        $builder->selectRaw($select);

        $builder->groupBy(
            Company::TABLE.".".Company::FIELD_ID,
            Company::TABLE.".".Company::FIELD_NAME,
            Company::TABLE.".".Company::FIELD_ENTITY_NAME,
            Company::TABLE.".".Company::FIELD_ADMIN_STATUS,
            Company::TABLE.".".Company::FIELD_SALES_STATUS,
            Company::TABLE.".".Company::FIELD_CONSOLIDATED_STATUS,
            Company::TABLE.".".Company::FIELD_LEGACY_ID,
            CompanyData::TABLE.".".CompanyData::FIELD_PAYLOAD,
        );

        return $builder;
    }
}
