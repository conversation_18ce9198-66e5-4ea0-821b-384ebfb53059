<?php

namespace App\Services\Sortable\Company;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CompanyLastTimeLeadPurchasedSortableOption extends BaseSortableOption
{
    protected string $id = 'last-time-lead-purchased';

    public function appendToQuery(Builder|Company $existingQuery, string $direction = 'asc'): void
    {
        $latestLeadPurchasedField = 'latest_lead_purchased';
        $latestLeadPurchasedTable = 'latest_leads';

        $mostRecentLeadQuery = EloquentQuoteCompany::query()
            ->select(EloquentQuoteCompany::COMPANY_ID, DB::raw('MAX('.EloquentQuoteCompany::TIMESTAMP_ADDED.') as '.$latestLeadPurchasedField))
            ->from(EloquentQuoteCompany::TABLE)
            ->where(EloquentQuoteCompany::CHARGEABLE, true)
            ->where(EloquentQuoteCompany::DELIVERED, true)
            ->groupBy(EloquentQuoteCompany::COMPANY_ID);

        $existingQuery
            ->leftJoinSub($mostRecentLeadQuery, $latestLeadPurchasedTable, function ($join) use ($latestLeadPurchasedTable) {
                $join->on(Company::TABLE.'.'.Company::FIELD_LEGACY_ID, '=', $latestLeadPurchasedTable.'.'.EloquentQuoteCompany::COMPANY_ID);
            });

        $existingQuery->orderBy($latestLeadPurchasedField, $direction);
    }
}
