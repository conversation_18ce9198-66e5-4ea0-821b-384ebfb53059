<?php

namespace App\Services\Sortable\Company;

use App\Services\Sortable\BaseSortableService;

class CompanySortableService extends BaseSortableService
{
    protected array $options = [
        CompanyIdSortableOption::class,
        CompanyNameSortableOption::class,
        CompanyStatusSortableOption::class,
        CompanySalesStatusSortableOption::class,
        CompanyGoogleReviewCountSortableOption::class,
        CompanyEstimatedRevenueSortableOption::class,
        CompanyLastTimeContactedSortableOption::class,
        CompanyServicesSortableOption::class,
        CompanyStateSortableOption::class,
        CompanyCampaignsSortableOption::class,
        CompanyLeadRejectionPercentageManualSortableOption::class,
        CompanyLeadRejectionPercentageCrmSortableOption::class,
        CompanyLeadRejectionPercentageOverallSortableOption::class,
        CompanyAppointmentRejectionPercentageManualSortableOption::class,
        CompanyAppointmentRejectionPercentageCrmSortableOption::class,
        CompanyAppointmentRejectionPercentageOverallSortableOption::class,
        CompanyCadenceNameSortableOption::class,
        CompanyLeadPurchasedOneSortableOption::class,
        CompanyLeadPurchasedTwoSortableOption::class,
        CompanyGoogleRatingSortableOption::class,
        CompanyEstimatedMonthlyAdSpendSortableOption::class,
        CompanyLastTimeLeadPurchasedSortableOption::class,
        CompanyLifetimeRevenueSortableOption::class,
    ];
}
