<?php

namespace App\Services\Sortable\Company;

use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyStateSortableOption extends BaseSortableOption
{
    protected string $id = 'state';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->join(CompanyLocation::TABLE, CompanyLocation::TABLE .'.'. CompanyLocation::FIELD_COMPANY_ID, '=', Company::TABLE .'.'. Company::FIELD_ID)
            ->join(Address::TABLE, Address::TABLE .'.'. Address::FIELD_ID, '=', CompanyLocation::FIELD_ADDRESS_ID)
            ->orderBy(Address::TABLE .'.'. Address::FIELD_STATE, $direction);
    }
}
