<?php

namespace App\Services\Sortable\Company;

use App\Services\Filterables\Company\CompanyAmountOfLeadsPurchasedFilterable;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyLeadPurchasedOneSortableOption extends BaseSortableOption
{
    use HasCompanyLeadPurchasedSortableOption;

    protected string $id = 'lead-purchased-1';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $this->buildQuery($existingQuery);

        $existingQuery->orderBy(CompanyAmountOfLeadsPurchasedFilterable::COLUMN_LEAD_COST_ONE,
            $direction);
    }
}
