<?php

namespace App\Services\Sortable\Company;

use App\Jobs\CalculateAndStoreGoogleRatingsJob;
use App\Models\Odin\CompanyData;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyGoogleRatingSortableOption extends BaseSortableOption
{
    protected string $id = 'google-rating';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->orderByRaw("CAST(JSON_EXTRACT(".CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD.", '$.".CalculateAndStoreGoogleRatingsJob::GOOGLE_RATING."') as double) $direction");
    }
}
