<?php

namespace App\Services\Sortable\Company;

use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyAppointmentRejectionPercentageOverallSortableOption extends BaseSortableOption
{
    use CompanyRejectionPercentageSortableTrait;

    protected string $id = 'overall-appointment-rejection-percentage';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $this->getAverageOverallAppointmentRejectionPercentageField($existingQuery);

        $existingQuery->orderBy('average_overall_appointment_rejection_percentage', $direction);
    }
}
