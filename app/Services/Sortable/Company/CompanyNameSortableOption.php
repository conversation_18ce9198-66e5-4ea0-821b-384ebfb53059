<?php

namespace App\Services\Sortable\Company;

use App\Models\Odin\Company;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyNameSortableOption extends BaseSortableOption
{
    protected string $id = 'name';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->orderBy(Company::TABLE.'.'.Company::FIELD_NAME, $direction);
    }
}
