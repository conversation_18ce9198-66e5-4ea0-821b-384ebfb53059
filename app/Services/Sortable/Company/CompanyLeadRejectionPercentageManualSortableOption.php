<?php

namespace App\Services\Sortable\Company;

use App\Models\ComputedRejectionStatistic;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyLeadRejectionPercentageManualSortableOption extends BaseSortableOption
{
    use CompanyRejectionPercentageSortableTrait;

    protected string $id = 'manual-lead-rejection-percentage';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $this->getAverageManualLeadRejectionPercentageField($existingQuery);

        $existingQuery->orderBy('average_manual_lead_rejection_percentage', $direction);
    }
}
