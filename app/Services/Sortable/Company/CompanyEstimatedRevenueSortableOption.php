<?php

namespace App\Services\Sortable\Company;

use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\CompanyData;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyEstimatedRevenueSortableOption extends BaseSortableOption
{
    protected string $id = 'estimated-revenue';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->orderByRaw("CAST(JSON_EXTRACT(" . CompanyData::TABLE .'.'. CompanyData::FIELD_PAYLOAD . ", '$." . SolarConfigurableFields::REVENUE_IN_THOUSANDS->value . "') as signed) $direction");
    }
}
