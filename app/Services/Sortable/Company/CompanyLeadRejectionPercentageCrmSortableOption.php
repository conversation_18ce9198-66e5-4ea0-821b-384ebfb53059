<?php

namespace App\Services\Sortable\Company;

use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyLeadRejectionPercentageCrmSortableOption extends BaseSortableOption
{
    use CompanyRejectionPercentageSortableTrait;

    protected string $id = 'crm-lead-rejection-percentage';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $this->getAverageCrmLeadRejectionPercentageField($existingQuery);

        $existingQuery->orderBy('average_crm_lead_rejection_percentage', $direction);
    }
}
