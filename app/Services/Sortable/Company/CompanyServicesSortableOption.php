<?php

namespace App\Services\Sortable\Company;

use App\Models\Odin\Company;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyServicesSortableOption extends BaseSortableOption
{
    protected string $id = 'services';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->withCount(Company::RELATION_SERVICES)
            ->orderBy('services_count', $direction);
    }
}
