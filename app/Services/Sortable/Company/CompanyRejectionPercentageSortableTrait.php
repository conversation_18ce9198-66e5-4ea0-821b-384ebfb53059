<?php

namespace App\Services\Sortable\Company;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

trait CompanyRejectionPercentageSortableTrait
{
    /**
     * @param  Builder  $existingQuery
     * @return void
     */
    protected function getAverageManualLeadRejectionPercentageField(Builder $existingQuery): void
    {
        $this->joinComputedRejectionStatistic($existingQuery);

        $productId = $this->getLeadProductId();

        $fieldName = "average_manual_lead_rejection_percentage";

        $existingQuery->addSelect([
            DB::raw("avg(IF(product_id <> $productId, 0, (`manual_rejection_percentage`))) as '$fieldName'")
        ]);
    }

    /**
     * @param  Builder  $existingQuery
     * @return void
     */
    protected function joinComputedRejectionStatistic(Builder $existingQuery): void
    {
        $existingQuery->groupBy(Company::TABLE.'.'.Company::FIELD_ID);

        $joinAlreadyExists = collect($existingQuery->getQuery()->joins)
            ->filter(function ($join) {
                return $join->table === ComputedRejectionStatistic::TABLE;
            })
            ->isNotEmpty();

        if (!$joinAlreadyExists) {
            $existingQuery->join(ComputedRejectionStatistic::TABLE,
                ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_COMPANY_ID, '=',
                Company::TABLE.'.'.Company::FIELD_ID);
        }
    }

    /**
     * @return int|mixed
     */
    protected function getLeadProductId(): mixed
    {
        return Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::LEAD->value)
            ->first()
            ->id;
    }

    /**
     * @param  Builder  $existingQuery
     * @return void
     */
    protected function getAverageCrmLeadRejectionPercentageField(Builder $existingQuery): void
    {
        $this->joinComputedRejectionStatistic($existingQuery);

        $productId = $this->getLeadProductId();

        $fieldName = "average_crm_lead_rejection_percentage";

        $existingQuery->addSelect([
            DB::raw("avg(IF(product_id <> $productId, 0, (`crm_rejection_percentage`))) as '$fieldName'")
        ]);
    }

    /**
     * @param  Builder  $existingQuery
     * @return void
     */
    protected function getAverageOverallLeadRejectionPercentageField(Builder $existingQuery): void
    {
        $this->joinComputedRejectionStatistic($existingQuery);

        $productId = $this->getLeadProductId();

        $fieldName = "average_overall_lead_rejection_percentage";

        $existingQuery->addSelect([
            DB::raw("avg(IF(product_id <> $productId, 0, (`overall_rejection_percentage`))) as '$fieldName'")
        ]);
    }

    /**
     * @param  Builder  $existingQuery
     * @return void
     */
    protected function getAverageManualAppointmentRejectionPercentageField(Builder $existingQuery): void
    {
        $this->joinComputedRejectionStatistic($existingQuery);

        $productId = $this->getAppointmentProductId();

        $fieldName = "average_manual_appointment_rejection_percentage";

        $existingQuery->addSelect([
            DB::raw("avg(IF(product_id <> $productId, 0, (`manual_rejection_percentage`))) as '$fieldName'")
        ]);
    }

    /**
     * @return int|mixed
     */
    protected function getAppointmentProductId(): mixed
    {
        return Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value)
            ->first()
            ->id;
    }

    /**
     * @param  Builder  $existingQuery
     * @return void
     */
    protected function getAverageCrmAppointmentRejectionPercentageField(Builder $existingQuery): void
    {
        $this->joinComputedRejectionStatistic($existingQuery);

        $productId = $this->getAppointmentProductId();

        $fieldName = "average_crm_appointment_rejection_percentage";

        $existingQuery->addSelect([
            DB::raw("avg(IF(product_id <> $productId, 0, (`crm_rejection_percentage`))) as '$fieldName'")
        ]);
    }

    /**
     * @param  Builder  $existingQuery
     * @return void
     */
    protected function getAverageOverallAppointmentRejectionPercentageField(Builder $existingQuery): void
    {
        $this->joinComputedRejectionStatistic($existingQuery);

        $productId = $this->getAppointmentProductId();

        $fieldName = "average_overall_appointment_rejection_percentage";

        $existingQuery->addSelect([
            DB::raw("avg(IF(product_id <> $productId, 0, (`overall_rejection_percentage`))) as '$fieldName'")
        ]);
    }
}
