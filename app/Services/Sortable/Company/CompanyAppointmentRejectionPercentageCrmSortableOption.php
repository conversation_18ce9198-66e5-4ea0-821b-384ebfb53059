<?php

namespace App\Services\Sortable\Company;

use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyAppointmentRejectionPercentageCrmSortableOption extends BaseSortableOption
{
    use CompanyRejectionPercentageSortableTrait;

    protected string $id = 'crm-appointment-rejection-percentage';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $this->getAverageCrmAppointmentRejectionPercentageField($existingQuery);

        $existingQuery->orderBy('average_crm_appointment_rejection_percentage', $direction);
    }
}
