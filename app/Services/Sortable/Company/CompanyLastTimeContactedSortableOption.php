<?php

namespace App\Services\Sortable\Company;

use App\Models\ActivityFeed;
use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Text;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class CompanyLastTimeContactedSortableOption extends BaseSortableOption
{
    protected string $id = 'last-time-contacted';

    public function appendToQuery(Builder|Company $existingQuery, string $direction = 'asc'): void
    {
        $existingQuery
            ->leftJoin(ActivityFeed::TABLE, function (JoinClause $joinClause) {
                $joinClause->on(
                    Company::TABLE.'.'.Company::FIELD_ID,
                    '=',
                    ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_COMPANY_ID
                );
            });

        $existingQuery->leftJoin(Call::TABLE, function (Join<PERSON>lause $joinClause) {
            $joinClause->on(
                ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_ID,
                '=',
                Call::TABLE.'.'.Call::FIELD_ID,
            );

            $joinClause->where(
                ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_TYPE,
                '=',
                'call'
            );
        });

        $existingQuery->leftJoin(Text::TABLE, function (JoinClause $joinClause) {
            $joinClause->on(
                ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_ID,
                '=',
                Text::TABLE.'.'.Text::FIELD_ID,
            );

            $joinClause->where(
                ActivityFeed::TABLE.'.'.ActivityFeed::FIELD_ITEM_TYPE,
                '=',
                'text'
            );
        });

        $existingQuery->addSelect([
            DB::raw('MAX(IFNULL('.Call::TABLE.'.'.Call::FIELD_CALL_END.', '.Text::TABLE.'.'.Text::FIELD_CREATED_AT.')) as last_time_contacted'),
        ]);

        $existingQuery->groupBy(Company::TABLE.'.'.Company::FIELD_ID);

        $existingQuery->orderBy('last_time_contacted', $direction);
    }
}
