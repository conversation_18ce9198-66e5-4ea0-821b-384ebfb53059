<?php

namespace App\Services\Sortable\Company;

use App\Jobs\CalculateAndStoreGoogleRatingsJob;
use App\Models\CompanyMetric;
use App\Models\Odin\CompanyData;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyEstimatedMonthlyAdSpendSortableOption extends BaseSortableOption
{
    protected string $id = 'estimated-monthly-ad-spend';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->orderByRaw("CAST(JSON_EXTRACT(".CompanyMetric::TABLE.'.'.CompanyMetric::FIELD_REQUEST_RESPONSE.", '$.monthlySpend') as double) $direction");
    }
}
