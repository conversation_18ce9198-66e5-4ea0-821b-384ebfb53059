<?php

namespace App\Services\Sortable\Company;

use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyCampaignsSortableOption extends BaseSortableOption
{
    protected string $id = 'campaigns';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->withCount([
            Company::RELATION_CAMPAIGNS,
            Company::RELATION_CAMPAIGNS . " as active_campaigns" => fn(Builder $query) =>
                $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE),
        ])->orderBy("active_campaigns", $direction);
    }
}
