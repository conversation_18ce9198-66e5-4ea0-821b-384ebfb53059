<?php

namespace App\Services;

use App\Http\Requests\Odin\StoreCompanyLocationRequest;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Repositories\Legacy\CompanyAddressRepository;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Services\Google\GeocodingService;
use App\Transformers\Legacy\CompanyLocationToLegacyAddressTransformer;
use Illuminate\Http\Client\RequestException;
use Throwable;

class CompanyLocationService
{

    public function __construct(
        protected CompanyRepository $companyRepository,
        protected CompanyLocationRepository $locationRepository,
        protected AddressRepository $addressRepository,
        protected CompanyLocationToLegacyAddressTransformer $legacyAddressTransformer,
        protected CompanyAddressRepository $legacyAddressRepository,
        protected GeocodingService $geocodingService
    ) { }

    /**
     * @param Company $company
     * @param StoreCompanyLocationRequest $request
     *
     * @return CompanyLocation|null
     * @throws RequestException
     */
    public function createCompanyLocation(Company $company, StoreCompanyLocationRequest $request): ?CompanyLocation
    {
        $addressData = $request->safe()->only(CompanyLocation::RELATION_ADDRESS)[CompanyLocation::RELATION_ADDRESS] ?? [];
        // Default to US in the absence of any country selector
        $addressData[Address::FIELD_COUNTRY] = $addressData[Address::FIELD_COUNTRY] ?? 'US';
        $locationData = $request->safe()->except(CompanyLocation::RELATION_ADDRESS);
        $addressData = $this->addLatLngToAddress($addressData);

        $newAddress = $this->addressRepository->createAddressFromAttributes($addressData);
        $newLocation = $this->locationRepository->updateOrCreateCompanyLocation([
            ...$locationData,
            CompanyLocation::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
            CompanyLocation::FIELD_ADDRESS_ID => $newAddress->{Address::FIELD_ID}
        ]);

        if ($newLocation->{CompanyLocation::FIELD_ID}) {
            try {
                $legacyAddressData = $this->legacyAddressTransformer->transformCompanyLocationToLegacyAddress($newLocation);
                if (!($this->legacyAddressRepository->createCompanyAddress($company->{Company::FIELD_REFERENCE}, $legacyAddressData)['status'] ?? false)) {
                    $this->handleLegacySyncError("Failed to create legacy CompanyLocation on integration-api, Odin ID $newLocation->id.");
                }
            }
            catch(Throwable $err) {
                $this->handleLegacySyncError("Failed to create legacy CompanyLocation on integration-api, Odin ID $newLocation->id.\n\t".$err->getMessage());
            }
        }

        return $newLocation;
    }

    /**
     * @param Company $company
     * @param CompanyLocation $companyLocation
     * @param StoreCompanyLocationRequest $request
     *
     * @return CompanyLocation|null
     * @throws RequestException
     */
    public function updateCompanyLocation(Company $company, CompanyLocation $companyLocation, StoreCompanyLocationRequest $request): ?CompanyLocation
    {
        $addressData = $request->safe()->only(CompanyLocation::RELATION_ADDRESS)[CompanyLocation::RELATION_ADDRESS] ?? [];
        $locationData = $request->safe()->except(CompanyLocation::RELATION_ADDRESS);
        $addressData = $this->addLatLngToAddress($addressData);

        $address = $companyLocation->{CompanyLocation::RELATION_ADDRESS};
        if (!$address) {
            $newAddress = $this->addressRepository->createAddressFromAttributes($addressData);
            $locationData[CompanyLocation::FIELD_ADDRESS_ID] = $newAddress->{Address::FIELD_ID};
        }
        else {
            $this->addressRepository->updateAddress($address, $addressData);
        }

        $updatedLocation = $this->locationRepository->updateOrCreateCompanyLocation([
            ...$locationData,
            CompanyLocation::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
            CompanyLocation::FIELD_ADDRESS_ID => $companyLocation->{CompanyLocation::FIELD_ADDRESS_ID}
        ]);

        if ($updatedLocation->{CompanyLocation::FIELD_ID}) {
            try {
                $legacyAddressData = $this->legacyAddressTransformer->transformCompanyLocationToLegacyAddress($companyLocation->refresh());
                $legacySuccess = $this->legacyAddressRepository->updateCompanyAddressDetails($company->{Company::FIELD_REFERENCE}, $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_LEGACY_ID}, $legacyAddressData)['status'] ?? false;
                if (!$legacySuccess) $this->handleLegacySyncError("Failed to update legacy CompanyLocation on integration-api, Odin ID $updatedLocation->id.");
            }
            catch(Throwable $err) {
                $this->handleLegacySyncError("Failed to update legacy CompanyLocation on integration-api, Odin ID $updatedLocation->id.\n\t".$err->getMessage());
            }
        }

        return $updatedLocation;
    }

    /**
     * @param int $companyId
     * @param int $companyLocationId
     * @return bool
     */
    public function deleteCompanyLocation(int $companyId, int $companyLocationId): bool
    {
        $location = $this->locationRepository->findOrFail($companyLocationId);
        $company = $this->companyRepository->findOrFail($companyId);
        if ($location->{CompanyLocation::FIELD_COMPANY_ID} !== $company->{Company::FIELD_ID}) {
            return false;
        }
        else {
            $legacyAddressId = $location->{CompanyLocation::RELATION_ADDRESS}?->{Address::FIELD_LEGACY_ID};
            $companyReference = $company->{Company::FIELD_REFERENCE};
            try {
                $legacySuccess = $this->legacyAddressRepository->deleteCompanyAddress($companyReference, $legacyAddressId);
                if (!$legacySuccess) $this->handleLegacySyncError("Failed to update legacy CompanyLocation on integration-api, Odin ID $location->id.");
            }
            catch(Throwable $err) {
                $this->handleLegacySyncError("Failed to update legacy CompanyLocation on integration-api, Odin ID $location->id.\n\t".$err->getMessage());
            }

            return !!$location->delete();
        }
    }

    /**
     * @param int $companyId
     * @param int $companyLocationId
     * @return bool
     */
    public function makeCompanyLocationPrimary(int $companyId, int $companyLocationId): bool
    {
        $company = $this->companyRepository->findOrFail($companyId);
        $company->locations()->findOrFail($companyLocationId);

        $company->locations->each(fn(CompanyLocation $location) => $location->update([CompanyLocation::FIELD_IS_PRIMARY => $location->id === $companyLocationId ]));
        return true;
    }

    /**
     * @param array $addressData
     * @param bool $throw
     *
     * @return array
     * @throws RequestException
     */
    public function addLatLngToAddress(array $addressData, bool $throw = true): array
    {
        list($lat, $lng) = $this->geocodingService->getLatLngFromAddress((new Address())->fill($addressData), $throw);

        $addressData[Address::FIELD_LATITUDE] = $lat;
        $addressData[Address::FIELD_LONGITUDE] = $lng;

        return $addressData;
    }

    private function handleLegacySyncError(string $message): void
    {
        logger()->error($message);
    }

}
