<?php

namespace App\Services;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\IndustryServiceSlug;
use App\Models\ContractorProfile\ContractorProfile;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyService;
use App\Models\Odin\IndustryService;
use App\Repositories\ContractorProfileRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ContractorProfileService
{
    public function __construct(protected ContractorProfileRepository $repository) {}

    public function shouldDisplay(Company $company): bool
    {
        return $company->contractorProfile->status ?? false;
    }

    /**
     * @param int $companyId
     * @return bool
     */
    public function toggleDisplayProfile(int $companyId): bool
    {
        $profile         = $this->getContractorProfile($companyId);
        $profile->status = !$profile->status;
        $profile->save();
        return $profile->status;
    }

    /**
     * @param int $companyId
     * @return ContractorProfile
     */
    private function getContractorProfile(int $companyId): ContractorProfile
    {
        return $this->repository->firstOrCreate($companyId);
    }

    /**
     * @param int $companyId
     * @param string $timezone
     * @param array $times
     * @return void
     */
    public function updateBusinessHours(int $companyId, string $timezone, array $times): void
    {
        $this->getContractorProfile($companyId)->update([
            ContractorProfile::FIELD_BUSINESS_HOURS_TIMEZONE => $timezone,
            ContractorProfile::FIELD_BUSINESS_HOURS          => $times,
        ]);
    }

    /**
     * Temporary function - can be deleted once run
     * SRF profile visibility will now depend on the contractor profile status, so once the feature is released
     * we'll enable all current solar companies by default
     *
     * @return void
     */
    public static function enableAllSolarCompanyProfiles(): void
    {
        $query = Company::query()
            ->select(Company::TABLE . '.' . Company::FIELD_ID)
            ->leftJoin(CompanyService::TABLE, CompanyService::TABLE . '.' . CompanyService::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->leftJoin(IndustryService::TABLE, IndustryService::TABLE . '.' . IndustryService::FIELD_ID, '=', CompanyService::TABLE . '.' . CompanyService::FIELD_INDUSTRY_SERVICE_ID)
            ->leftJoin(ContractorProfile::TABLE, ContractorProfile::TABLE . '.' . ContractorProfile::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->where(IndustryService::TABLE . '.' . IndustryService::FIELD_SLUG, '=', IndustryServiceSlug::SOLAR_INSTALLATION->value)
            ->whereNull(ContractorProfile::TABLE . '.' . ContractorProfile::FIELD_ID);

        dump('Enabling ' . $query->count());

        $query->chunk(1000, function (Collection $companies) {
            $inserts = $companies->unique(Company::FIELD_ID)->map(fn(Company $company) => [
                ContractorProfile::FIELD_COMPANY_ID => $company->id,
                ContractorProfile::FIELD_STATUS     => true,
            ])->toArray();
            ContractorProfile::insert($inserts);
        });
    }
}
