<?php

namespace App\Services\Alert;

use App\Enums\Alert\AlertType;
use App\Enums\Campaigns\CampaignStatus;
use App\Jobs\Emails\SendGenericEmailJob;
use App\Mail\CampaignUsageThreshold;
use App\Models\Alert;
use App\Models\AlertRecipient;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use App\Services\Campaigns\CompanyCampaignService;
use App\Services\Communication\TwilioCommunicationService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Mail;

class CampaignAlertService
{
    const string NOTIFICATION_DATE_FORMAT = 'F j, Y';

    /**
     * @param CompanyCampaign $companyCampaign
     *
     * @return void
     */
    public function notifyCampaignUsageThreshold(CompanyCampaign $companyCampaign): void
    {
        $this->getAlertsForCampaign(AlertType::CAMPAIGN_THRESHOLD, $companyCampaign)
            ->each(fn(Alert $alert) => $this->notifyIfThresholdReached($alert, $companyCampaign));
    }

    /**
     * @param Alert $alert
     * @param CompanyCampaign $companyCampaign
     *
     * @return void
     */
    protected function notifyIfThresholdReached(Alert $alert, CompanyCampaign $companyCampaign): void
    {
        $threshold = $alert->getPayloadByKey(Alert::PAYLOAD_KEY_CAMPAIGN_USAGE_THRESHOLD);

        if (!$threshold) {
            return;
        }

        // Reset alert data at the start of each month (temporary hardcoded logic until UI implementation)
        if ($alert->last_alerted_at?->isBefore(now()->startOfMonth())) {
            $alert->last_alerted_at = null;
            $alert->payload = [...$alert->payload, 'last_alerted_threshold' => null];
            $alert->save();
        }

        $campaignSpend = $this->getCampaignSpend($companyCampaign);
        $nextThreshold = $alert->getPayloadByKey('last_alerted_threshold', 0) + $threshold;

        if ($campaignSpend < $nextThreshold) {
            return;
        }

        //recalculate next threshold based on campaign usage
        $lastAlertedThreshold = (int) floor($campaignSpend/$threshold) * $threshold;

        $alert->last_alerted_at = now();
        $alert->payload = [...$alert->payload, 'last_alerted_threshold' => $lastAlertedThreshold];
        $alert->save();

        $reactivationDate = now()->addMonth()->startOfMonth();
        $paused = $this->pauseCampaignIfEnabled($companyCampaign, $reactivationDate);

        $alert->recipients
            ->each(function (AlertRecipient $recipient) use ($companyCampaign, $lastAlertedThreshold, $campaignSpend, $paused, $reactivationDate) {
                switch ($recipient->channel) {
                    case AlertRecipient::CHANNEL_EMAIL:
                        $this->sendEmailAlert(
                            companyCampaign: $companyCampaign,
                            recipient: $recipient,
                            lastAlertedThreshold: $lastAlertedThreshold,
                            campaignSpend: $campaignSpend,
                            reactivationDate: $paused ? $reactivationDate : null
                        );
                        break;

                    case AlertRecipient::CHANNEL_SMS:
                        $this->sendSMSAlert(
                            companyCampaign: $companyCampaign,
                            recipient: $recipient,
                            lastAlertedThreshold: $lastAlertedThreshold,
                            campaignSpend: $campaignSpend,
                            reactivationDate: $paused ? $reactivationDate : null
                        );
                }
            });

        $this->notifyCompanyManagers(
            companyCampaign: $companyCampaign,
            lastAlertedThreshold: $lastAlertedThreshold,
            campaignSpend: $campaignSpend,
            reactivationDate: $paused ? $reactivationDate : null
        );
    }

    protected function pauseCampaignIfEnabled(CompanyCampaign $companyCampaign, Carbon $reactivationDate): bool
    {
        if ($companyCampaign->status !== CampaignStatus::ACTIVE) {
            return false;
        }

        if (!$companyCampaign->company->configuration?->pause_campaign_on_threshold_exceeded) {
            return false;
        }

        return app(CompanyCampaignService::class)->pauseCampaign(
            campaign: $companyCampaign,
            newStatus: CampaignStatus::PAUSED_TEMPORARILY,
            oldStatus: $companyCampaign->status,
            reason: 'Campaign usage threshold exceeded',
            reactivateAt: $reactivationDate
        );
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param int $lastAlertedThreshold
     * @param int $campaignSpend
     * @param Carbon|null $reactivationDate
     *
     * @return void
     */
    protected function notifyCompanyManagers(CompanyCampaign $companyCampaign, int $lastAlertedThreshold, int $campaignSpend, ?Carbon $reactivationDate = null): void
    {
        collect([
            $companyCampaign->company->businessDevelopmentManager,
            $companyCampaign->company->accountManager
        ])
            ->filter()
            ->each(function (User $user) use ($companyCampaign, $lastAlertedThreshold, $campaignSpend, $reactivationDate) {
                $threshold = number_format($lastAlertedThreshold);
                $spend = number_format($campaignSpend);
                $companyLink =  config('app.url') . "/companies/{$companyCampaign->company->id}";
                $sender = config('app.name');
                $reactivationText = $reactivationDate ? 'The campaign has been paused and will be reactivated at ' . $reactivationDate->format(self::NOTIFICATION_DATE_FORMAT) : '';

                $content = <<<CONTENT
Hi $user->name,

An alert has been sent for campaign $companyCampaign->name of company <a href="$companyLink">{$companyCampaign->company->name}</a>, as it has exceeded its spend threshold of $$threshold (actual usage: $$spend).
$reactivationText

Regards,<br>
$sender
CONTENT;

                SendGenericEmailJob::dispatch(
                    $user->email,
                    'Campaign Usage Threshold',
                    null,
                    $content
                );
            });
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param AlertRecipient $recipient
     * @param int $lastAlertedThreshold
     * @param int $campaignSpend
     * @param Carbon|null $reactivationDate
     *
     * @return void
     */
    protected function sendEmailAlert(
        CompanyCampaign $companyCampaign,
        AlertRecipient $recipient,
        int $lastAlertedThreshold,
        int $campaignSpend,
        ?Carbon $reactivationDate = null
    ): void
    {
        if (!$recipient->notifiable->email) {
            return;
        }

        Mail::to($recipient->notifiable->email)
            ->queue(
                (new CampaignUsageThreshold(
                    fromEmail: '<EMAIL>',
                    fromName: 'Fixr Team',
                    emailData: [
                        'recipientName'  => $recipient->notifiable->first_name ?? $recipient->notifiable->name,
                        'campaignName'   => $companyCampaign->name,
                        'spendThreshold' => number_format($lastAlertedThreshold),
                        'usage'          => number_format($campaignSpend),
                        'reactivationDate' => $reactivationDate?->format(self::NOTIFICATION_DATE_FORMAT)
                    ]
                ))
            );

        $this->logAlert(
            companyCampaign: $companyCampaign,
            recipient: $recipient->notifiable->email,
            lastAlertedThreshold: $lastAlertedThreshold,
            campaignSpend: $campaignSpend,
            paused: !! $reactivationDate
        );
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param AlertRecipient $recipient
     * @param int $lastAlertedThreshold
     * @param int $campaignSpend
     *
     * @return void
     * @throws \Throwable
     */
    protected function sendSMSAlert(
        CompanyCampaign $companyCampaign,
        AlertRecipient $recipient,
        int $lastAlertedThreshold,
        int $campaignSpend,
        ?Carbon $reactivationDate = null
    ): void
    {
        $phone = match ($recipient->notifiable::class) {
            CompanyUser::class => $recipient->notifiable->cell_phone,
            User::class => $recipient->notifiable->primaryPhone()?->phone,
            default => null
        };

        if (!$phone) {
            return;
        }

        /** @var TwilioCommunicationService $service */
        $service = app(TwilioCommunicationService::class);
        $name = $recipient->notifiable->first_name ?? $recipient->notifiable->name;

        try {
            $service->sendSMS(
                from: config('services.twilio.from_phone_number'),
                to: $phone,
                body: sprintf(
                    'Hi %s, Your campaign %s has exceeded its spend threshold of %s (actual usage: %s). %s',
                    $name,
                    $companyCampaign->name,
                    number_format($lastAlertedThreshold),
                    number_format($campaignSpend),
                    $reactivationDate ? 'The campaign has been paused and will be reactivated at ' . $reactivationDate->format(self::NOTIFICATION_DATE_FORMAT) : ''
                )
            );
        } catch (Exception $exception) {
            logger()->error("Failed send Alert SMS to $phone. Error: {$exception->getMessage()}");
            return;
        }

        $this->logAlert(
            companyCampaign: $companyCampaign,
            recipient: $phone,
            lastAlertedThreshold: $lastAlertedThreshold,
            campaignSpend: $campaignSpend,
            paused: !! $reactivationDate
        );
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param string $recipient
     * @param int $lastAlertedThreshold
     * @param int $campaignSpend
     * @param bool $paused
     *
     * @return void
     */
    protected function logAlert(CompanyCampaign $companyCampaign, string $recipient, int $lastAlertedThreshold, int $campaignSpend, bool $paused): void
    {
        activity('campaign_threshold_alert')
            ->performedOn($companyCampaign)
            ->event('alert_sent')
            ->withProperties([
                'attributes' => [
                    'recipient' => $recipient,
                    'campaign_spend' => $campaignSpend,
                    'paused' => $paused
                ],
                'threshold' => $lastAlertedThreshold
            ])
            ->log('campaign-threshold-alert-sent');
    }

    /**
     * @param AlertType $alertType
     * @param CompanyCampaign $companyCampaign
     *
     * @return Collection
     */
    protected function getAlertsForCampaign(AlertType $alertType, CompanyCampaign $companyCampaign): Collection
    {
        return $companyCampaign->alerts()
            ->where(Alert::FIELD_TYPE, $alertType)
            ->where(Alert::FIELD_ACTIVE, true)
            ->get();
    }

    /**
     * @param CompanyCampaign $companyCampaign
     *
     * @return float
     */
    protected function getCampaignSpend(CompanyCampaign $companyCampaign): float
    {
        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->whereIn(ProductAssignment::FIELD_BUDGET_ID, $companyCampaign->budgetContainer->budgets->pluck(Budget::FIELD_ID)->toArray())
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->startOfMonth())
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '<=', now()->endOfMonth())
            ->sum(ProductAssignment::FIELD_COST);
    }
}
