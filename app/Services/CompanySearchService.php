<?php

namespace App\Services;

use App\Builders\Odin\Company\CompanySearchBuilder;
use App\Constants\Company\Search\AmountOfLeadsPurchasedArray;
use App\Constants\Company\Search\FilterRequestParams;
use App\Constants\Company\Search\FilterRequestParams as RequestParams;
use App\DataModels\AmountOfLeadsPurchasedObject;
use App\Enums\Cadence;
use App\Enums\DateFormatOption;
use App\Enums\Logical;
use App\Enums\Operator;
use App\Http\Requests\SearchCompaniesRequest;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Exception;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Validation\ValidationException;

class CompanySearchService
{
    const HAS_ALL_COMPANIES_SELECTED = 'all';

    /**
     * @param CompanySearchBuilder $companySearchBuilder
     * @param string|null $companyName
     * @param array $statusIds
     * @param array $salesStatusIds
     * @param array $stateAbbreviations
     * @param array $accountManagerIds
     * @param array $industryIds
     * @param array $orderBy
     * @param Logical $industryLogical
     * @param array $officeLocationIds
     * @param Logical $officeLocationLogical
     * @param array $successManagerIds
     * @param null|array $amountOfLeadsPurchasedObject
     * @param bool|null $neverExceedsBudget
     * @param Cadence|null $cadence
     * @param array $campaignStatusIds
     * @param bool $campaignBudgetNoLimitSelected
     * @param int|null $campaignBudgetVolumeInput
     * @param Operator|null $campaignBudgetVolumeOperator
     * @param int|null $campaignBudgetCostInput
     * @param Operator|null $campaignBudgetCostOperator
     * @param array $campaignServiceAreas
     * @param Logical $campaignServiceAreasLogical
     * @param int|null $leadRejectionFirstInput
     * @param int|null $leadRejectionSecondInput
     * @param Operator|null $leadRejectionFirstOperator
     * @param Operator|null $leadRejectionSecondOperator
     * @param Logical|null $leadRejectionLogical
     * @param int|null $appointmentRejectionFirstInput
     * @param int|null $appointmentRejectionSecondInput
     * @param Operator|null $appointmentRejectionFirstOperator
     * @param Operator|null $appointmentRejectionSecondOperator
     * @param Logical|null $appointmentRejectionLogical
     * @param int|null $employeeCountFirstInput
     * @param Operator|null $employeeCountFirstOperator
     * @param int|null $employeeCountSecondInput
     * @param Operator|null $employeeCountSecondOperator
     * @param Logical|null $employeeCountLogical
     * @param int|null $estimatedRevenueFirstInput
     * @param Operator|null $estimatedRevenueFirstOperator
     * @param int|null $estimatedRevenueSecondInput
     * @param Operator|null $estimatedRevenueSecondOperator
     * @param Logical|null $estimatedRevenueLogical
     * @param int|null $googleRatingFirstInput
     * @param Operator|null $googleRatingFirstOperator
     * @param int|null $googleRatingSecondInput
     * @param Operator|null $googleRatingSecondOperator
     * @param Logical|null $googleRatingLogical
     * @param int|null $googleReviewCountFirstInput
     * @param Operator|null $googleReviewCountFirstOperator
     * @param int|null $googleReviewCountSecondInput
     * @param Operator|null $googleReviewCountSecondOperator
     * @param Logical|null $googleReviewCountLogical
     * @param int|null $consumerRatingFirstInput
     * @param Operator|null $consumerRatingFirstOperator
     * @param int|null $consumerRatingSecondInput
     * @param Operator|null $consumerRatingSecondOperator
     * @param Logical|null $consumerRatingLogical
     * @param int|null $consumerReviewCountFirstInput
     * @param Operator|null $consumerReviewCountFirstOperator
     * @param int|null $consumerReviewCountSecondInput
     * @param Operator|null $consumerReviewCountSecondOperator
     * @param Logical|null $consumerReviewCountLogical
     */
    public function __construct(
        protected CompanySearchBuilder $companySearchBuilder,
        public ?string $companyName = null,
        public array $statusIds = [],
        public array $salesStatusIds = [],
        public array $stateAbbreviations = [],
        public array $accountManagerIds = [],
        public array $industryIds = [],
        public array $orderBy = [],
        public Logical $industryLogical = Logical::OR,
        public array $officeLocationIds = [],
        public Logical $officeLocationLogical = Logical::OR,
        public array $successManagerIds = [],
        public ?array $amountOfLeadsPurchasedObject = [
            AmountOfLeadsPurchasedArray::ACTIVE => false,
            AmountOfLeadsPurchasedArray::FIRST_VALUE => 0,
            AmountOfLeadsPurchasedArray::SECOND_VALUE => null,
            AmountOfLeadsPurchasedArray::FIRST_OPERATOR => Operator::GREATER_THAN,
            AmountOfLeadsPurchasedArray::SECOND_OPERATOR => null,
            AmountOfLeadsPurchasedArray::LOGICAL => null,
            AmountOfLeadsPurchasedArray::FIRST_DATE_TOGGLED => false,
            AmountOfLeadsPurchasedArray::SECOND_DATE_TOGGLED => false,
            AmountOfLeadsPurchasedArray::FIRST_FROM_DATE => null,
            AmountOfLeadsPurchasedArray::FIRST_TO_DATE => null,
            AmountOfLeadsPurchasedArray::SECOND_FROM_DATE => null,
            AmountOfLeadsPurchasedArray::SECOND_TO_DATE => null,
        ],
        public ?bool $neverExceedsBudget = null,
        public ?Cadence $cadence = null,
        public array $campaignStatusIds = [],
        public ?bool $campaignBudgetNoLimitSelected = null,
        public ?int $campaignBudgetVolumeInput = null,
        public ?Operator $campaignBudgetVolumeOperator = null,
        public ?int $campaignBudgetCostInput = null,
        public ?Operator $campaignBudgetCostOperator = null,
        public array $campaignServiceAreas = [],
        public Logical $campaignServiceAreasLogical = Logical::OR,
        public ?int $leadRejectionFirstInput = null,
        public ?int $leadRejectionSecondInput = null,
        public ?Operator $leadRejectionFirstOperator = null,
        public ?Operator $leadRejectionSecondOperator = null,
        public ?Logical $leadRejectionLogical = null,
        public ?int $appointmentRejectionFirstInput = null,
        public ?int $appointmentRejectionSecondInput = null,
        public ?Operator $appointmentRejectionFirstOperator = null,
        public ?Operator $appointmentRejectionSecondOperator = null,
        public ?Logical $appointmentRejectionLogical = null,
        public ?int $employeeCountFirstInput = null,
        public ?Operator $employeeCountFirstOperator = null,
        public ?int $employeeCountSecondInput = null,
        public ?Operator $employeeCountSecondOperator = null,
        public ?Logical $employeeCountLogical = null,
        public ?int $estimatedRevenueFirstInput = null,
        public ?Operator $estimatedRevenueFirstOperator = null,
        public ?int $estimatedRevenueSecondInput = null,
        public ?Operator $estimatedRevenueSecondOperator = null,
        public ?Logical $estimatedRevenueLogical = null,
        public ?int $googleRatingFirstInput = null,
        public ?Operator $googleRatingFirstOperator = null,
        public ?int $googleRatingSecondInput = null,
        public ?Operator $googleRatingSecondOperator = null,
        public ?Logical $googleRatingLogical = null,
        public ?int $googleReviewCountFirstInput = null,
        public ?Operator $googleReviewCountFirstOperator = null,
        public ?int $googleReviewCountSecondInput = null,
        public ?Operator $googleReviewCountSecondOperator = null,
        public ?Logical $googleReviewCountLogical = null,
        public ?int $consumerRatingFirstInput = null,
        public ?Operator $consumerRatingFirstOperator = null,
        public ?int $consumerRatingSecondInput = null,
        public ?Operator $consumerRatingSecondOperator = null,
        public ?Logical $consumerRatingLogical = null,
        public ?int $consumerReviewCountFirstInput = null,
        public ?Operator $consumerReviewCountFirstOperator = null,
        public ?int $consumerReviewCountSecondInput = null,
        public ?Operator $consumerReviewCountSecondOperator = null,
        public ?Logical $consumerReviewCountLogical = null,
    ) {
    }

    /**
     * @param  SearchCompaniesRequest  $request
     * @return void
     */
    private function setVariables(SearchCompaniesRequest $request): void
    {
        $searchParams = $request->safe()->collect();

        $this->statusIds = $searchParams->get(FilterRequestParams::REQUEST_STATUS_IDS) ?: [];
        $this->salesStatusIds = $searchParams->get(FilterRequestParams::REQUEST_SALES_STATUS_IDS) ?: [];
        $this->stateAbbreviations = $request->get(FilterRequestParams::REQUEST_STATE_ABBREVIATIONS) ?: [];
        $this->accountManagerIds = $request->get(FilterRequestParams::REQUEST_ACCOUNT_MANAGER_IDS) ?: [];
        $this->industryIds = $request->get(FilterRequestParams::REQUEST_INDUSTRY_IDS) ?: [];
        $this->industryLogical = $request->enum(FilterRequestParams::REQUEST_INDUSTRY_LOGICAL,
            Logical::class) ?: Logical::OR;
        $this->officeLocationIds = $request->get(FilterRequestParams::REQUEST_OFFICE_LOCATION_IDS) ?: [];
        $this->officeLocationLogical = $request->enum(FilterRequestParams::REQUEST_OFFICE_LOCATION_LOGICAL,
            Logical::class) ?: Logical::OR;
        $this->companyName = $request->get(FilterRequestParams::REQUEST_COMPANY_NAME);

        $this->amountOfLeadsPurchasedObject = $request->get(FilterRequestParams::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT) ?: [];

        $this->parseAmountOfLeadsPurchasedObject();

        $this->successManagerIds = $request->get(FilterRequestParams::REQUEST_SUCCESS_MANAGER_IDS) ?: [];
        $this->orderBy = $request->get(FilterRequestParams::REQUEST_ORDER_BY) ?: [];

        $this->neverExceedsBudget = $request->get(FilterRequestParams::REQUEST_NEVER_EXCEEDS_BUDGET) ?: null;

        $this->cadence = $request->enum(FilterRequestParams::REQUEST_CADENCE, Cadence::class) ?: null;

        $this->campaignStatusIds = data_get($request->toArray(), RequestParams::REQUEST_CAMPAIGN_STATUS_IDS, []);

        $this->campaignBudgetNoLimitSelected = data_get($request->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_NO_LIMIT_SELECTED);
        $this->campaignBudgetVolumeInput = intval(data_get($request->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_VOLUME_INPUT));
        $this->campaignBudgetVolumeOperator = Operator::tryFrom(data_get($request->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_VOLUME_OPERATOR));
        $this->campaignBudgetCostInput = intval(data_get($request->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_COST_INPUT));
        $this->campaignBudgetCostOperator = Operator::tryFrom(data_get($request->toArray(),
            RequestParams::REQUEST_CAMPAIGN_BUDGET_COST_OPERATOR));

        $this->campaignServiceAreas = data_get($request->toArray(),
            RequestParams::REQUEST_CAMPAIGN_SERVICE_AREAS, []);
        $this->campaignServiceAreasLogical = Logical::tryFrom(data_get($request->toArray(),
            RequestParams::REQUEST_CAMPAIGN_SERVICE_AREAS_LOGICAL, Logical::OR->value));

        $getInput = function (SearchCompaniesRequest $request, string $key): ?int {
            return intval(data_get($request->toArray(), $key)) ?? null;
        };

        $getOperator = function (SearchCompaniesRequest $request, string $key): ?Operator {
            return Operator::tryFrom(data_get($request->toArray(), $key)) ?? null;
        };

        $getLogical = function (SearchCompaniesRequest $request, string $key): ?Logical {
            return Logical::tryFrom(data_get($request->toArray(), $key)) ?? null;
        };

        $this->leadRejectionFirstInput = $getInput($request, RequestParams::REQUEST_LEAD_REJECTION_FIRST_INPUT);
        $this->leadRejectionSecondInput = $getInput($request, RequestParams::REQUEST_LEAD_REJECTION_SECOND_INPUT);
        $this->leadRejectionFirstOperator = $getOperator($request, RequestParams::REQUEST_LEAD_REJECTION_FIRST_OPERATOR);
        $this->leadRejectionSecondOperator = $getOperator($request, RequestParams::REQUEST_LEAD_REJECTION_SECOND_OPERATOR);
        $this->leadRejectionLogical = $getLogical($request, RequestParams::REQUEST_LEAD_REJECTION_LOGICAL);

        $this->appointmentRejectionFirstInput = $getInput($request, RequestParams::REQUEST_APPOINTMENT_REJECTION_FIRST_INPUT);
        $this->appointmentRejectionSecondInput = $getInput($request, RequestParams::REQUEST_APPOINTMENT_REJECTION_SECOND_INPUT);
        $this->appointmentRejectionFirstOperator = $getOperator($request, RequestParams::REQUEST_APPOINTMENT_REJECTION_FIRST_OPERATOR);
        $this->appointmentRejectionSecondOperator = $getOperator($request, RequestParams::REQUEST_APPOINTMENT_REJECTION_SECOND_OPERATOR);
        $this->appointmentRejectionLogical = $getLogical($request, RequestParams::REQUEST_APPOINTMENT_REJECTION_LOGICAL);

        $this->employeeCountFirstInput = $getInput($request, RequestParams::REQUEST_EMPLOYEE_COUNT_FIRST_INPUT);
        $this->employeeCountFirstOperator = $getOperator($request, RequestParams::REQUEST_EMPLOYEE_COUNT_FIRST_OPERATOR);
        $this->employeeCountSecondInput = $getInput($request, RequestParams::REQUEST_EMPLOYEE_COUNT_SECOND_INPUT);
        $this->employeeCountSecondOperator = $getOperator($request, RequestParams::REQUEST_EMPLOYEE_COUNT_SECOND_OPERATOR);
        $this->employeeCountLogical = $getLogical($request, RequestParams::REQUEST_EMPLOYEE_COUNT_LOGICAL);

        $this->estimatedRevenueFirstInput = $getInput($request, RequestParams::REQUEST_ESTIMATED_REVENUE_FIRST_INPUT);
        $this->estimatedRevenueFirstOperator = $getOperator($request, RequestParams::REQUEST_ESTIMATED_REVENUE_FIRST_OPERATOR);
        $this->estimatedRevenueSecondInput = $getInput($request, RequestParams::REQUEST_ESTIMATED_REVENUE_SECOND_INPUT);
        $this->estimatedRevenueSecondOperator = $getOperator($request, RequestParams::REQUEST_ESTIMATED_REVENUE_SECOND_OPERATOR);
        $this->estimatedRevenueLogical = $getLogical($request, RequestParams::REQUEST_ESTIMATED_REVENUE_LOGICAL);

        $this->googleRatingFirstInput = $getInput($request, RequestParams::REQUEST_GOOGLE_RATING_FIRST_INPUT);
        $this->googleRatingFirstOperator = $getOperator($request, RequestParams::REQUEST_GOOGLE_RATING_FIRST_OPERATOR);
        $this->googleRatingSecondInput = $getInput($request, RequestParams::REQUEST_GOOGLE_RATING_SECOND_INPUT);
        $this->googleRatingSecondOperator = $getOperator($request, RequestParams::REQUEST_GOOGLE_RATING_SECOND_OPERATOR);
        $this->googleRatingLogical = $getLogical($request, RequestParams::REQUEST_GOOGLE_RATING_LOGICAL);

        $this->googleReviewCountFirstInput = $getInput($request, RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_FIRST_INPUT);
        $this->googleReviewCountFirstOperator = $getOperator($request, RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_FIRST_OPERATOR);
        $this->googleReviewCountSecondInput = $getInput($request, RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_SECOND_INPUT);
        $this->googleReviewCountSecondOperator = $getOperator($request, RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_SECOND_OPERATOR);
        $this->googleReviewCountLogical = $getLogical($request, RequestParams::REQUEST_GOOGLE_REVIEW_COUNT_LOGICAL);

        $this->consumerRatingFirstInput = $getInput($request, RequestParams::REQUEST_CONSUMER_RATING_FIRST_INPUT);
        $this->consumerRatingFirstOperator = $getOperator($request, RequestParams::REQUEST_CONSUMER_RATING_FIRST_OPERATOR);
        $this->consumerRatingSecondInput = $getInput($request, RequestParams::REQUEST_CONSUMER_RATING_SECOND_INPUT);
        $this->consumerRatingSecondOperator = $getOperator($request, RequestParams::REQUEST_CONSUMER_RATING_SECOND_OPERATOR);
        $this->consumerRatingLogical = $getLogical($request, RequestParams::REQUEST_CONSUMER_RATING_LOGICAL);

        $this->consumerReviewCountFirstInput = $getInput($request, RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_FIRST_INPUT);
        $this->consumerReviewCountFirstOperator = $getOperator($request, RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_FIRST_OPERATOR);
        $this->consumerReviewCountSecondInput = $getInput($request, RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_SECOND_INPUT);
        $this->consumerReviewCountSecondOperator = $getOperator($request, RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_SECOND_OPERATOR);
        $this->consumerReviewCountLogical = $getLogical($request, RequestParams::REQUEST_CONSUMER_REVIEW_COUNT_LOGICAL);
    }

    /**
     * @return void
     */
    private function parseAmountOfLeadsPurchasedObject(): void
    {
        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::ACTIVE] = filter_var(data_get($this->amountOfLeadsPurchasedObject,
            'active'), FILTER_VALIDATE_BOOLEAN);
        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::FIRST_VALUE] = intval(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_FIRST_VALUE));

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::SECOND_VALUE] = intval(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_SECOND_VALUE));

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::FIRST_OPERATOR] = data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_FIRST_OPERATOR);

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::SECOND_OPERATOR] = data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_SECOND_OPERATOR);

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::LOGICAL] = data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_LOGICAL);

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::FIRST_DATE_TOGGLED] = filter_var(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_FIRST_DATE_TOGGLED), FILTER_VALIDATE_BOOLEAN);

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::SECOND_DATE_TOGGLED] = filter_var(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_SECOND_DATE_TOGGLED), FILTER_VALIDATE_BOOLEAN);

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::FIRST_FROM_DATE] = $this->parseTimestamp(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_FIRST_FROM_DATE));

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::FIRST_TO_DATE] = $this->parseTimestamp(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_FIRST_TO_DATE));

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::SECOND_FROM_DATE] = $this->parseTimestamp(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_SECOND_FROM_DATE));

        $this->amountOfLeadsPurchasedObject[AmountOfLeadsPurchasedArray::SECOND_TO_DATE] = $this->parseTimestamp(data_get($this->amountOfLeadsPurchasedObject,
            AmountOfLeadsPurchasedObject::PROPERTY_SECOND_TO_DATE));
    }

    private function parseTimestamp($arg): ?Carbon
    {
        $dateFormat = data_get($this->amountOfLeadsPurchasedObject, AmountOfLeadsPurchasedObject::PROPERTY_DATE_FORMAT);

        $dateFormatIsTimestamp = $dateFormat === DateFormatOption::TIMESTAMP->value;
        $dateFormatIsString = $dateFormat === DateFormatOption::STRING->value;
        $dateFormatIsTimestampMs = $dateFormat === DateFormatOption::TIMESTAMP_MILLISECONDS->value;

        $argIsNotEmptyOrZero = !empty($arg) && $arg !== 0;
        $argIsAlreadyCarbon = $arg instanceof Carbon;

        if ($argIsNotEmptyOrZero) {
            if ($dateFormatIsTimestamp)

                return Carbon::createFromTimestamp($arg);

            if ($dateFormatIsString)

                return Carbon::parse($arg);

            if ($dateFormatIsTimestampMs)

                return Carbon::createFromTimestampMs($arg);

            if ($argIsAlreadyCarbon)

                return $arg;
        }

        return null;
    }

    /**
     * @return CompanySearchBuilder
     * @throws ValidationException
     * @throws Exception
     */
    private function getSearchCompaniesQuery(): CompanySearchBuilder
    {
        $query = $this->companySearchBuilder->appendUsers();

        $setProps = function () use ($query) {
            $query
                ->setCompanyName($this->companyName)
                ->setStatusIds($this->statusIds)
                ->setSalesStatusIds($this->salesStatusIds)
                ->setIndustryLogical($this->industryLogical)
                ->setIndustryIds($this->industryIds)
                ->setOfficeLocationLogical($this->officeLocationLogical)
                ->setOfficeLocationIds($this->officeLocationIds)
                ->setAccountsManagerIds($this->accountManagerIds)
                ->setSuccessManagerIds($this->successManagerIds)
                ->setStateAbbreviations($this->stateAbbreviations)
                ->setCampaignBudget(
                    noLimitSelected: $this->campaignBudgetNoLimitSelected,
                    volumeInput: $this->campaignBudgetVolumeInput,
                    volumeOperator: $this->campaignBudgetVolumeOperator,
                    costInput: $this->campaignBudgetCostInput,
                    costOperator: $this->campaignBudgetCostOperator
                )
                ->setAmountOfLeadsPurchasedObject($this->amountOfLeadsPurchasedObject)
                ->setNeverExceedsBudget($this->neverExceedsBudget)
                ->setCadence($this->cadence)
                ->setCampaignStatusIds($this->campaignStatusIds)
                ->setCampaignServiceAreas($this->campaignServiceAreas, $this->campaignServiceAreasLogical)
                ->setLeadRejection(
                    firstInput: $this->leadRejectionFirstInput,
                    secondInput: $this->leadRejectionSecondInput,
                    firstOperator: $this->leadRejectionFirstOperator,
                    secondOperator: $this->leadRejectionSecondOperator,
                    logical: $this->leadRejectionLogical
                )
                ->setAppointmentRejection(
                    firstInput: $this->appointmentRejectionFirstInput,
                    secondInput: $this->appointmentRejectionSecondInput,
                    firstOperator: $this->appointmentRejectionFirstOperator,
                    secondOperator: $this->appointmentRejectionSecondOperator,
                    logical: $this->appointmentRejectionLogical
                )
                ->setEmployeeCount(
                    firstInput: $this->employeeCountFirstInput,
                    firstOperator: $this->employeeCountFirstOperator,
                    logical: $this->employeeCountLogical,
                    secondInput: $this->employeeCountSecondInput,
                    secondOperator: $this->employeeCountSecondOperator
                )
                ->setEstimatedRevenue(
                    firstInput: $this->estimatedRevenueFirstInput,
                    firstOperator: $this->estimatedRevenueFirstOperator,
                    logical: $this->estimatedRevenueLogical,
                    secondInput: $this->estimatedRevenueSecondInput,
                    secondOperator: $this->estimatedRevenueSecondOperator
                )
                ->setGoogleRating(
                    firstInput: $this->googleRatingFirstInput,
                    firstOperator: $this->googleRatingFirstOperator,
                    logical: $this->googleRatingLogical,
                    secondInput: $this->googleRatingSecondInput,
                    secondOperator: $this->googleRatingSecondOperator
                )
                ->setGoogleReview(
                    firstInput: $this->googleReviewCountFirstInput,
                    firstOperator: $this->googleReviewCountFirstOperator,
                    logical: $this->googleReviewCountLogical,
                    secondInput: $this->googleReviewCountSecondInput,
                    secondOperator: $this->googleReviewCountSecondOperator
                )
                ->setConsumerRating(
                    firstInput: $this->consumerRatingFirstInput,
                    firstOperator: $this->consumerRatingFirstOperator,
                    logical: $this->consumerRatingLogical,
                    secondInput: $this->consumerRatingSecondInput,
                    secondOperator: $this->consumerRatingSecondOperator
                )
                ->setConsumerReviewCount(
                    firstInput: $this->consumerReviewCountFirstInput,
                    firstOperator: $this->consumerReviewCountFirstOperator,
                    logical: $this->consumerReviewCountLogical,
                    secondInput: $this->consumerReviewCountSecondInput,
                    secondOperator: $this->consumerReviewCountSecondOperator
                )
                ->setOrderBy($this->orderBy);

            return $query;
        };

        $setProps();

        return $query->filter();
    }

    /**
     * Retrieve company Ids
     * @param  SearchCompaniesRequest  $request
     * @param  array|null  $companyData
     * @return array
     * @throws ValidationException|Exception
     */
    public function getCompanyIds(SearchCompaniesRequest $request, ?array $companyData): array
    {
        if ($companyData && $companyData[self::HAS_ALL_COMPANIES_SELECTED]) {
            $this->setVariables($request);
            return $this->getSearchCompaniesQuery()->get()->pluck(Company::FIELD_ID)->toArray();
        } else {
            $companies = $companyData['data']['companies'] ?? [];
            return collect($companies)->pluck(Company::FIELD_ID)->toArray();
        }
    }

    /**
     * @param  SearchCompaniesRequest  $request
     * @return array
     * @throws ValidationException|Exception
     */
    public function getAllCompanyIds(SearchCompaniesRequest $request): array
    {
        $request->safe()->collect();
        $this->setVariables($request);
        $searchCompanies = $this->getSearchCompaniesQuery();

        return $searchCompanies->pluck(Company::FIELD_ID)->toArray();
    }
}
