<?php

namespace App\Services\Campaigns\CRMIntegrations\Pipedrive;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\Pipedrive\PipedriveCRMDeliverer;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Pipedrive\versions\v1\Configuration;
use Pipedrive\versions\v1\Model\AddNoteRequest;
use Pipedrive\versions\v1\Model\DealNonStrict;
use Pipedrive\versions\v1\Model\NewDeal;
use Pipedrive\versions\v1\Model\NewPerson;
use Pipedrive\versions\v1\Model\PersonItem;
use Pipedrive\versions\v1\ObjectSerializer;

class PipedriveDummyCRMService extends PipedriveCRMService
{
    const string TEST_DELIVERY_URL = 'pipedrive-delivery';

    protected Configuration $configuration;

    protected string $driver;
    protected ?CompanyCampaign $campaign;

    public function __construct()
    {
        parent::__construct(config('services.crm_delivery.test_secret'));
        $this->driver = config('services.crm_delivery.driver');
    }

    /**
     * @return array[]
     */
    public function getFields(): array
    {
        $fields = $this->getDummyFields();
        $fields[self::FIELD_CATEGORY_OTHER] = $this->getOtherFields();

        return $fields;
    }

    /**
     * @return Collection
     */
    protected function getOtherFields(): Collection
    {
        return collect([
            [
                PipedriveCRMDeliverer::PIPEDRIVE_KEY    => PipedriveCRMDeliverer::PIPEDRIVE_NOTE_FIELD,
                PipedriveCRMDeliverer::PIPEDRIVE_NAME   => 'Deal Note',
                PipedriveCRMDeliverer::PIPEDRIVE_TYPE   => 'text',
            ]
        ]);
    }

    /**
     * @param array $person
     * @return PersonItem|null
     */
    public function addPerson(array $person): ?PersonItem
    {
        return new PersonItem([
            ...$person,
            'id' => 'abc123',
        ]);
    }

    /**
     * @param array $deal
     * @return array
     * @throws ConnectionException
     */
    public function addDeal(array $deal): array
    {
        if ($this->driver === BaseCRMDeliveryService::CRM_DRIVER_TEST) {
            $responseData = Http::withHeaders($this->getTestHeaders())
                ->withBody(json_encode($deal), 'application/json')
                ->post($this->getTestUrl(self::TEST_DELIVERY_URL))
                ->json()
                ['data'] ?? [];
        }
        else {
            $responseData = ['id' => 'dummy123'];
        }

        $dealData = new DealNonStrict($responseData);

        return [
            self::PIPEDRIVE_RESPONSE_STATUS => true,
            self::PIPEDRIVE_RESPONSE_DATA   => $dealData,
        ];
    }

    /**
     * @param AddNoteRequest $note
     * @return void
     */
    public function addNote(AddNoteRequest $note): void
    {
        // not required for dummy service
    }

    /**
     * @return array[]
     */
    protected function getDummyFields(): array
    {
        return [
            self::FIELD_CATEGORY_DEAL    => $this->makePipedriveFieldCollection([
                'test_deal_field_1',
                'test_deal_field_2',
                'test_deal_field_3',
                'test_deal_field_4',
            ]),
            self::FIELD_CATEGORY_PERSON  => $this->makePipedriveFieldCollection([
                'test_person_field_1',
                'test_person_field_2',
                'test_person_field_3',
                'test_person_field_4',
            ]),
        ];
    }

    protected function makePipedriveFieldCollection(array $keysArray): Collection
    {
        $makeField = function(string $key): array
        {
            return [
                'id'         => "$key-id",
                'key'        => $key,
                'name'       => ucwords($key),
                'field_type' => 'text',
            ];
        };

        return collect(array_map(fn($key) => $makeField($key), $keysArray));
    }

    /**
     * @param string|null $append
     * @return string
     */
    private function getTestUrl(?string $append = ''): string
    {
        $base = config('services.crm_delivery.test_url');

        return str_ends_with($base, '/')
            ? "$base$append"
            : "$base/$append";
    }

    /**
     * @return array
     */
    private function getTestHeaders(): array
    {
        return [
            'Authorization' => config('services.crm_delivery.test_secret'),
        ];
    }
}
