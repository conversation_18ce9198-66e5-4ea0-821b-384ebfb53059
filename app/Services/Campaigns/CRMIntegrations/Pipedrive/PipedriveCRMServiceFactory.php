<?php

namespace App\Services\Campaigns\CRMIntegrations\Pipedrive;

use Illuminate\Support\Facades\App;

class PipedriveCRMServiceFactory
{
    public static function make(string $apiKey): PipedriveCRMService
    {
        return (App::environment('production') || config('services.crm_delivery.driver') === 'live')
            ? new PipedriveCRMService($apiKey)
            : new PipedriveDummyCRMService($apiKey);
    }
}
