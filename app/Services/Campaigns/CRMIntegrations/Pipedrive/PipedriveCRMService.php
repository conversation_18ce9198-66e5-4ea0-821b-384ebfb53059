<?php

namespace App\Services\Campaigns\CRMIntegrations\Pipedrive;

use App\Campaigns\Delivery\CRM\Pipedrive\PipedriveCRMDeliverer;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Collection;
use Pipedrive\versions\v1\Api\DealFieldsApi;
use Pipedrive\versions\v1\Api\DealsApi;
use Pipedrive\versions\v1\Api\NotesApi;
use Pipedrive\versions\v1\Api\PersonFieldsApi;
use Pipedrive\versions\v1\Api\PersonsApi;
use Pipedrive\versions\v1\ApiException;
use Pipedrive\versions\v1\Configuration;
use Pipedrive\versions\v1\Model\AddNoteRequest;
use Pipedrive\versions\v1\Model\GetField;
use Pipedrive\versions\v1\Model\PersonItem;

/**
 * TODO: remove pipedrive library dependency and rewrite this service, next time there are any problems.
 *  Had to remove pipedrive library classes to get custom fields to work last time, so the library is now
 *  doing little more than setting the token header and URL, and wrapping the payload in an http client
 */
class PipedriveCRMService
{
    const string FIELD_CATEGORY_PERSON = 'person_fields';
    const string FIELD_CATEGORY_DEAL   = 'deal_fields';
    const string FIELD_CATEGORY_OTHER  = 'other_fields';
    const string FIELD_PERSON_ID       = 'person_id';
    const string FIELD_USER_ID         = 'user_id';

    const string PIPEDRIVE_RESPONSE_DATA    = 'data';
    const string PIPEDRIVE_RESPONSE_STATUS  = 'status';
    const string PIPEDRIVE_RESPONSE_MESSAGE = 'message';

    protected Configuration $configuration;

    public function __construct(
        string $apiKey,
    )
    {
        $this->configuration = new Configuration();
        $this->configuration->setApiKey('x-api-token', $apiKey);
    }

    /**
     * @return array
     * @throws GuzzleException
     * @throws ApiException
     */
    public function getFields(): array
    {
        $dealFieldsApi   = new DealFieldsApi(new Client(), $this->configuration);
        $personFieldsApi = new PersonFieldsApi(new Client(), $this->configuration);

        $dealFields   = $dealFieldsApi->getDealFields()->getData();
        $personFields = $personFieldsApi->getPersonFields()->getData();

        return [
            self::FIELD_CATEGORY_DEAL   => collect($dealFields)->filter(fn(GetField $item) => !in_array($item->getKey(), $this->getReservedDealFieldNames()))->values(),
            self::FIELD_CATEGORY_PERSON => collect($personFields)->filter(fn(GetField $item) => !in_array($item->getKey(), $this->getReservedPersonFieldNames()))->values(),
            self::FIELD_CATEGORY_OTHER  => $this->getOtherFields(),
        ];
    }

    /**
     * @return Collection
     */
    protected function getOtherFields(): Collection
    {
        return collect([
            [
                PipedriveCRMDeliverer::PIPEDRIVE_KEY    => PipedriveCRMDeliverer::PIPEDRIVE_NOTE_FIELD,
                PipedriveCRMDeliverer::PIPEDRIVE_NAME   => 'Deal Note',
                PipedriveCRMDeliverer::PIPEDRIVE_TYPE   => 'text',
            ]
        ]);
    }

    /**
     * @return array
     */
    protected function getReservedPersonFieldNames(): array
    {
        return [
            'add_time',
            'update_time',
            'org_id',
            'owner_id',
            'open_deals_count',
            'visible_to',
            'next_activity_date',
            'last_activity_date',
            'id',
            'won_deals_count',
            'lost_deals_count',
            'closed_deals_count',
            'activities_count',
            'done_activities_count',
            'undone_activities_count',
            'email_messages_count',
            'picture_id',
            'last_incoming_mail_time',
            'last_outgoing_mail_time',
        ];
    }

    /**
     * @return array
     */
    protected function getReservedDealFieldNames(): array
    {
        return [
            'creator_user_id',
            'user_id',
            'value',
            'currency',
            'org_id',
            'pipeline',
            'person_id',
            'stage_id',
            'status',
            'add_time',
            'update_time',
            'stage_change_time',
            'next_activity_date',
            'last_activity_date',
            'won_time',
            'last_incoming_mail_time',
            'last_outgoing_mail_time',
            'lost_time',
            'close_time',
            'lost_reason',
            'visible_to',
            'id',
            'activities_count',
            'done_activities_count',
            'undone_activities_count',
            'email_messages_count',
            'expected_close_date',
            'probability',
            'weighted_value',
            'weighted_value_currency',
            'product_quantity',
            'product_amount',
            'product_name',
            'origin',
            'arr',
            'arr_currency',
            'acv',
            'acv_currency',
            'mrr',
            'mrr_currency',
        ];
    }

    /**
     * @param array $person
     * @return PersonItem|null
     * @throws GuzzleException
     */
    public function addPerson(array $person): ?PersonItem
    {
        $personsApi = new PersonsApi(new Client(), $this->configuration);

        try {
            $response = $personsApi->addPerson($person);
            return $response->getData();
        } catch (Exception $e) {
            logger()->error("Pipedrive Service error: " . $e->getMessage());

            return null;
        }
    }

    /**
     * @param array $deal
     * @return array
     * @throws GuzzleException
     */
    public function addDeal(array $deal): array
    {
        $dealApi = new DealsApi(new Client(), $this->configuration);

        try {
            $response = $dealApi->addDeal($deal);

            return [
                self::PIPEDRIVE_RESPONSE_STATUS => $response->getSuccess(),
                self::PIPEDRIVE_RESPONSE_DATA   => $response->getData(),
            ];
        } catch (Exception $e) {
            logger()->error("Pipedrive Service error: " . $e->getMessage());

            return [
                self::PIPEDRIVE_RESPONSE_STATUS  => false,
                self::PIPEDRIVE_RESPONSE_MESSAGE => $e->getMessage(),
            ];
        }
    }

    /**
     * @param AddNoteRequest $note
     * @return void
     * @throws GuzzleException
     */
    public function addNote(AddNoteRequest $note): void
    {
        $notesApi = new NotesApi(new Client(), $this->configuration);

        try {
            $notesApi->addNote($note);
        }
        catch (Exception $e) {
            logger()->error("Pipedrive Service error: Failed to add note to Deal - " . $e->getMessage());
        }
    }
}
