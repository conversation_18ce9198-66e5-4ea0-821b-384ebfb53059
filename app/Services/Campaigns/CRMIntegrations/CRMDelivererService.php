<?php

namespace App\Services\Campaigns\CRMIntegrations;

use App\Campaigns\Delivery\CRM\BaseInteractableCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use Exception;

class CRMDelivererService
{
    public function __construct() {}

    /**
     * @param string $methodName
     * @param array $payload
     * @param CRMType|null $crmType
     * @param int|null|string $crmDelivererId
     * @param bool $isTemplate
     * @return array
     * @throws Exception
     */
    public function executeInteractableMethod(string $methodName, array $payload, ?CRMType $crmType, mixed $crmDelivererId, ?bool $isTemplate = false): array
    {
        $delivererId = intval($crmDelivererId) ?: null;

        if (!$crmType && !$delivererId)
            throw new Exception("Cannot execute Interactable method without supplying a CRM Type or Deliverer instance");

        if (!$delivererId) {
            $dummyCrmModule = new CompanyCampaignDeliveryModuleCRM();
            $crm = $crmType->getConcrete($dummyCrmModule);
        }
        else {
            /** @var CompanyCampaignDeliveryModuleCRM $crmModel */
            $crmModel = $isTemplate
                ? CompanyCRMTemplate::query()
                    ->findOrFail($delivererId)
                : CompanyCampaignDeliveryModuleCRM::query()
                    ->findOrFail($delivererId);
            $crm = $crmModel->crm_type->getConcrete($crmModel, $crmModel->payload);
        }

        if (!($crm instanceof BaseInteractableCRMDeliverer))
            throw new Exception("Cannot run Interactable methods on standard CRM Integrations.");

        return $crm->runMethod($methodName, $payload);
    }
}
