<?php

namespace App\Services\Campaigns;

use App\Builders\Pricing\BasePricingBuilder;
use App\Builders\Pricing\CustomFloorPricingBuilder;
use App\Enums\LoweredFloorPricePolicy;
use App\Http\Requests\CompanyCampaigns\StoreCustomCampaignFloorPricingRequest;
use App\Jobs\CalculateCompanyCampaignLowBidFlagJob;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\Campaigns\CustomCampaignFloorPriceRepository;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Throwable;

class CustomCampaignFloorPricingService
{
    public function __construct(
        protected CompanyCampaignRepository $companyCampaignRepository,
        protected CustomCampaignFloorPriceRepository $customCampaignFloorPriceRepository,
    ) {}

    /**
     * @param CompanyCampaign $companyCampaign
     * @param int $stateLocationId
     * @return array
     */
    public function getCustomStateFloorPricing(CompanyCampaign $companyCampaign, int $stateLocationId): array
    {
        $serviceProductId = $this->companyCampaignRepository->getServiceProductFromCampaign($companyCampaign)->{ServiceProduct::FIELD_ID};

        $floorPrices = CustomFloorPricingBuilder::query()
            ->forState($stateLocationId)
            ->forServiceProduct($serviceProductId)
            ->forCompanyCampaign($companyCampaign->id)
            ->select([ProductStateFloorPrice::TABLE . '.*'])
            ->aliasPriceColumns()
            ->getQuery()
            ->get();

        /** @var CampaignPricingService $pricingService */
        $pricingService = app(CampaignPricingService::class);

        $activeCountyBids = $pricingService->getCountyKeysWithActiveBids($companyCampaign, $stateLocationId);
        $stateHasBids = count($pricingService->getStateKeysWithActiveBids($companyCampaign, $stateLocationId));
        if ($stateHasBids)
            $activeCountyBids[] = 'state-bids';

        $prices =  $this->transformAndGroupPrices($floorPrices);

        return [$prices, $activeCountyBids];
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @return Collection
     */
    public function getCustomCountyFloorPricing(CompanyCampaign $companyCampaign, int $stateLocationId, int $countyLocationId): Collection
    {
        $serviceProductId = $this->companyCampaignRepository->getServiceProductFromCampaign($companyCampaign)->{ServiceProduct::FIELD_ID};

        $floorPrices = CustomFloorPricingBuilder::query()
            ->forState($stateLocationId)
            ->forCounty($countyLocationId)
            ->withCounties()
            ->forServiceProduct($serviceProductId)
            ->forCompanyCampaign($companyCampaign->id)
            ->select([ProductStateFloorPrice::TABLE . '.*'])
            ->aliasPriceColumns()
            ->getQuery()
            ->get();

        return $this->transformAndGroupPrices($floorPrices, $countyLocationId);
    }

    /**
     * @param Collection $campaigns
     * @param array $updatedPrices
     * @param bool $useCustomFloorPrices
     * @param LoweredFloorPricePolicy|null $loweredFloorPricePolicy
     * @return int
     * @throws Exception
     */
    public function handleCampaignFloorPricesUpdate(Collection $campaigns, array $updatedPrices, bool $useCustomFloorPrices, ?LoweredFloorPricePolicy $loweredFloorPricePolicy = null): int
    {
        /** @var CompanyCampaignService $companyCampaignService */
        $companyCampaignService = app(CompanyCampaignService::class);

        /** @var LoweredCustomFloorPricePolicyService $loweredFloorPriceService */
        $loweredFloorPriceService = $loweredFloorPricePolicy && $loweredFloorPricePolicy !== LoweredFloorPricePolicy::NO_ACTION
            ? app(LoweredCustomFloorPricePolicyService::class)
            : null;

        $results = [];
        foreach($campaigns as $campaign) {
            $validateLocations = $campaigns->count() > 1;
            $setFlag = $companyCampaignService->toggleCustomFloorPricing($campaign, $useCustomFloorPrices);

            $setPrices = !$campaign->uses_custom_floor_prices
                || $this->saveCustomCampaignFloorPrices($campaign, $updatedPrices, $validateLocations);

            $success = $setFlag && $setPrices;

            if ($success) {
                $loweredFloorPriceService?->handlePolicyEffects($loweredFloorPricePolicy, $campaign, $updatedPrices);
                CalculateCompanyCampaignLowBidFlagJob::dispatch($campaign);
            }
            $results[] = $success;
        }

        return count(array_filter($results, fn($v) => $v));
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param array $payload
     * @param bool|null $validateLocations
     * @return bool
     */
    public function saveCustomCampaignFloorPrices(CompanyCampaign $companyCampaign, array $payload, ?bool $validateLocations = false): bool
    {
        $propertyTypeMap = PropertyType::getIdMap();
        $qualityTierMap = QualityTier::getIdMap();
        $saleTypeMap = SaleType::getIdMap();

        // Location validation is currently used for cloning custom prices across a company's campaigns
        $validStateLocations = null;
        $validCountyLocations = null;
        if ($validateLocations) {
            $validLocationIds = $companyCampaign->locationModule->getStateAndCountyLocationIds();
            $validStateLocations = $validLocationIds[CompanyCampaignLocationModule::LOCATIONS_STATE_ID_KEY] ?? [];
            $validCountyLocations = $validLocationIds[CompanyCampaignLocationModule::LOCATIONS_COUNTY_ID_KEY] ?? [];
        }

        DB::beginTransaction();

        try {
            foreach ($payload as $customFloorPrice) {
                if ($customFloorPrice[CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID] ?? null) {
                    // Validate county exists in campaign when bulk updating
                    if ($validateLocations && !in_array($customFloorPrice[CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID], $validCountyLocations)) {
                        continue;
                    }

                    $this->customCampaignFloorPriceRepository->storeCustomCampaignCountyFloorPrice(
                        companyCampaignId: $companyCampaign->id,
                        stateLocationId: $customFloorPrice[CustomCampaignCountyFloorPrice::FIELD_STATE_LOCATION_ID],
                        countyLocationId: $customFloorPrice[CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID],
                        propertyTypeId: $propertyTypeMap[$customFloorPrice[StoreCustomCampaignFloorPricingRequest::REQUEST_PROPERTY_TYPE]] ?? 0,
                        qualityTierId: $qualityTierMap[$customFloorPrice[StoreCustomCampaignFloorPricingRequest::REQUEST_QUALITY_TIER]] ?? 0,
                        saleTypeId: $saleTypeMap[$customFloorPrice[StoreCustomCampaignFloorPricingRequest::REQUEST_SALE_TYPE]] ?? 0,
                        price: $customFloorPrice[CustomCampaignCountyFloorPrice::FIELD_PRICE]
                    );
                } else {
                    // Validate state exists in campaign when bulk updating
                    if ($validateLocations && !in_array($customFloorPrice[CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID], $validStateLocations)) {
                        continue;
                    }

                    $this->customCampaignFloorPriceRepository->storeCustomCampaignStateFloorPrice(
                        companyCampaignId: $companyCampaign->id,
                        stateLocationId: $customFloorPrice[CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID],
                        propertyTypeId: $propertyTypeMap[$customFloorPrice[StoreCustomCampaignFloorPricingRequest::REQUEST_PROPERTY_TYPE]] ?? 0,
                        qualityTierId: $qualityTierMap[$customFloorPrice[StoreCustomCampaignFloorPricingRequest::REQUEST_QUALITY_TIER]] ?? 0,
                        saleTypeId: $saleTypeMap[$customFloorPrice[StoreCustomCampaignFloorPricingRequest::REQUEST_SALE_TYPE]] ?? 0,
                        price: $customFloorPrice[CustomCampaignStateFloorPrice::FIELD_PRICE],
                    );
                }
            }
        }
        catch(Throwable $exception) {
            logger()->error($exception->getMessage());
            DB::rollBack();

            return false;
        }

        DB::commit();
        return true;
    }

    /**
     * @param Collection $floorPrices
     * @param int|null $countyLocationId
     * @return Collection
     */
    protected function transformAndGroupPrices(Collection $floorPrices, ?int $countyLocationId = null): Collection
    {
        $countyLocationKey = $countyLocationId
            ? Location::query()->find($countyLocationId)?->county_key
            : null;

        $transformedPrices = $floorPrices->groupBy([
            fn($price) => $price->propertyType->name,
            fn($price) => $price->stateLocation->state_abbr,
            fn($price) => $price->countyLocation?->county_key ?? $countyLocationKey ?? 'state',
            fn($price) => $price->qualityTier->name,
            fn($price) => $price->saleType->key
        ])->collect();

        foreach ($transformedPrices as $propertyTypes) {
            foreach ($propertyTypes as $states) {
                foreach ($states as $counties) {
                    foreach ($counties as $qualityTiers) {
                        foreach ($qualityTiers as $saleTypeKey => $saleTypes) {
                            $qualityTiers[$saleTypeKey] = [
                                BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE  => $saleTypes[0]->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE} ?? null,
                                BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE => $saleTypes[0]->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE} ?? null,
                                CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE => $saleTypes[0]->{CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE} ?? null,
                                CustomFloorPricingBuilder::ALIAS_CUSTOM_COUNTY_FLOOR_PRICE => $saleTypes[0]->{CustomFloorPricingBuilder::ALIAS_CUSTOM_COUNTY_FLOOR_PRICE} ?? null,
                            ];
                        }
                    }
                }

            }
        }
        return $transformedPrices;
    }
}
