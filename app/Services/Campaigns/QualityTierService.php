<?php

namespace App\Services\Campaigns;

use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAppointment;

class QualityTierService
{
    const string SOLAR_DATA_KEY_PAYLOAD      = 'payload';
    const string SOLAR_DATA_KEY_APPOINTMENTS = 'appointments';

    /**
     * @param ConsumerProject $consumerProject
     * @param ProductEnum $product
     * @return QualityTierEnum
     */
    public function evaluateConsumerProject(ConsumerProject $consumerProject, ProductEnum $product): QualityTierEnum
    {
        return match ($product) {
            ProductEnum::LEAD         => $this->evaluateLead($consumerProject),
            ProductEnum::DIRECT_LEADS => $this->evaluateDirectLead($consumerProject),
            default                   => QualityTierEnum::STANDARD,
        };
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return QualityTierEnum
     */
    protected function evaluateLead(ConsumerProject $consumerProject): QualityTierEnum
    {
        $leadConsumerProduct = $consumerProject->leadConsumerProduct();
        $industry = $leadConsumerProduct?->serviceProduct->service->industry->name ?? '';

        return match ($industry) {
            IndustryEnum::SOLAR->value => $this->evaluateSolarLead($leadConsumerProduct),
            default                    => QualityTierEnum::STANDARD,
        };
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return QualityTierEnum
     */
    protected function evaluateDirectLead(ConsumerProject $consumerProject): QualityTierEnum
    {
        //TODO: unsure if we need different logic for direct leads
        // will also need to update PotentialProductService to send through the correct Product enum
        return $this->evaluateLead($consumerProject);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return QualityTierEnum
     */
    protected function evaluateSolarLead(ConsumerProduct $consumerProduct): QualityTierEnum
    {
        $consumerProductData = $consumerProduct->consumerProductData?->payload ?? [];
        $payload = $consumerProductData[self::SOLAR_DATA_KEY_PAYLOAD] ?? $consumerProductData;

        if ($this->hasPremiumSolarUsage($payload) || $this->hasBestTimeToCall($payload))
            return QualityTierEnum::PREMIUM;
        else
            return QualityTierEnum::STANDARD;
    }

    /**
     * This is the logic currently used on legacy to determine best_time_to_call from synced leads
     * TODO: update this? will need to work with calculator/flow engine output
     * @param array $consumerProductPayload
     * @return bool
     */
    protected function hasBestTimeToCall(array $consumerProductPayload): bool
    {
        $appointments = array_values($consumerProductPayload[self::SOLAR_DATA_KEY_APPOINTMENTS] ?? []);

        if ($appointments)
            return !!$this->getAppointmentTimestamp($appointments[0] ?? []);

        return false;
    }

    /**
     * @param array $consumerProductPayload
     * @return bool
     */
    protected function hasPremiumSolarUsage(array $consumerProductPayload): bool
    {
        $electricUsage = (int) ($consumerProductPayload[SolarConfigurableFields::ELECTRIC_COST->value] ?? -1);
        $premiumUsage  = (int) config('sales.leads.premium_electric_spend') ?? 300;

        return $electricUsage >= $premiumUsage;
    }

    /**
     * @param array $appointment
     * @return string|null
     */
    protected function getAppointmentTimestamp(array $appointment): ?string
    {
        $date = $appointment[ProductAppointment::APPOINTMENT_DATE] ?? null;
        $time = $appointment[ProductAppointment::APPOINTMENT_TIME] ?? null;

        return $date && $time
            ? "$date $time"
            : null;
    }
}
