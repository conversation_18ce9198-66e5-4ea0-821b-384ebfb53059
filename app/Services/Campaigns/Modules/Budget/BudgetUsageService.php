<?php

namespace App\Services\Campaigns\Modules\Budget;

use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Enums\Timezone;
use App\Events\CompanyCampaign\CampaignOverBudgetEvent;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Odin\ProductAssignment;
use App\Repositories\MissedProducts\MissedProductReasonEventRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Carbon\Carbon;

class BudgetUsageService
{
    private ProductAssignmentRepository $assignmentRepository;
    private MissedProductReasonEventRepository $eventRepository;

    public function __construct() {
        $this->assignmentRepository = app(ProductAssignmentRepository::class);
        $this->eventRepository = app(MissedProductReasonEventRepository::class);
    }

    /**
     * @param Budget $budget
     * @return bool
     */
    public function isBudgetExceeded(Budget $budget): bool
    {
        $overBudget = $this->getCurrentUsagePercent($budget) > $this->getOverageThreshold($budget);
        if ($overBudget)
            $this->eventRepository->handleNewCampaignEvent($budget->budgetContainer->campaign, MissedProductReasonEventType::OVER_BUDGET);

        return $overBudget;
    }

    /**
     * Technically this will return false if we are permitted to exceed the budget
     *
     * @param Budget $budget
     * @param float $productCost
     * @param bool $neverExceedBudget
     * @return bool
     */
    public function willBudgetBeExceeded(Budget $budget, float $productCost, bool $neverExceedBudget = false): bool
    {
        if ($this->isBudgetExceeded($budget)) {
            $this->fireEvents($budget);
            return true;
        }

        $willExceedBudget = $neverExceedBudget && ($this->getProjectedUsagePercent($budget, $productCost) > $this->getOverageThreshold($budget));

        if ($willExceedBudget) {
            $this->fireEvents($budget);
            $this->eventRepository->handleNewCampaignEvent($budget->budgetContainer->campaign, MissedProductReasonEventType::OVER_BUDGET);
        }

        return $willExceedBudget;
    }

    /**
     * @param Budget $budget
     * @return float
     */
    public function getCurrentUsage(Budget $budget): float
    {
        $assignments = $this->assignmentRepository->getChargeableAssignments($budget, $this->getUsageStartDate($budget));
        return match ($budget->type) {
            BudgetType::NO_LIMIT => 0,
            BudgetType::TYPE_DAILY_SPEND => $assignments->sum(ProductAssignment::FIELD_COST),
            BudgetType::TYPE_DAILY_UNITS => $assignments->count(),
        };
    }

    /**
     * @param Budget $budget
     * @return float
     */
    public function getCurrentUsagePercent(Budget $budget): float
    {
        if ($budget->type === BudgetType::NO_LIMIT || $budget->value == 0)
            return 0;
        return ($this->getDailyAverage($budget) / $budget->value) * 100;
    }

    /**
     * @param Budget $budget
     * @return float
     */
    public function getOverageThreshold(Budget $budget): float
    {
        return $budget->budgetContainer->campaign->maximum_budget_usage;
    }

    /**
     * @param Budget $budget
     *
     * @return void
     */
    protected function fireEvents(Budget $budget): void
    {
        CampaignOverBudgetEvent::dispatch($budget);
    }

    /**
     * @param Budget $budget
     *
     * @return float
     */
    protected function getDailyAverage(Budget $budget): float
    {
        return $this->getCurrentUsage($budget) / $this->calculateNumberOfDays($budget);
    }

    /**
     * @param Budget $budget
     * @param float $productCost
     * @return float
     */
    private function getProjectedUsagePercent(Budget $budget, float $productCost): float
    {
        if ($budget->value == 0)
            return 0;
        $currentUsage   = $this->getCurrentUsage($budget);
        $projectedUsage = $currentUsage + match ($budget->type) {
                BudgetType::NO_LIMIT => 0,
                BudgetType::TYPE_DAILY_SPEND => $productCost,
                BudgetType::TYPE_DAILY_UNITS => 1,
            };

        return (($projectedUsage / $this->calculateNumberOfDays($budget)) / $budget->value) * 100;
    }

    /**
     * @param Budget $budget
     *
     * @return int
     */
    protected function calculateNumberOfDays(Budget $budget): int
    {
        $numberOfDays = (int) $this->getUsageStartDate($budget)->diffInDays(Carbon::now(), true);

        return max($numberOfDays, 1);
    }

    /**
     * The start date should be 8am in the company's timezone
     *
     * @param Budget $budget
     * @return Carbon
     */
    private function getUsageStartDate(Budget $budget): Carbon
    {
        $startingTimestamp = 0;
        $thirtyDaysAgo = Carbon::today()->subDays(29)->timestamp;
        if ($budget->last_modified_at) {
            $startingTimestamp = $budget->last_modified_at->startOfday()->timestamp;
        }
        return Carbon::createFromTimestamp(max($startingTimestamp, $thirtyDaysAgo));
    }
}
