<?php

namespace App\Services\Campaigns;

use App\Enums\LoweredFloorPricePolicy;
use App\Http\Requests\CompanyCampaigns\StoreCustomCampaignFloorPricingRequest;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\SaleType;
use Exception;
use Illuminate\Support\Facades\DB;

/**
 * Handle any effects on campaigns requested when changing custom floor prices
 */
class LoweredCustomFloorPricePolicyService
{
    public function handlePolicyEffects(
        LoweredFloorPricePolicy $loweredFloorPricePolicy,
        CompanyCampaign         $campaign,
        array                   $updatedPricesArray,
    ): bool
    {
        return match($loweredFloorPricePolicy) {
            LoweredFloorPricePolicy::REMOVE_AFFECTED_CAMPAIGN_BIDS => $this->removeScopedBidsForCampaign($campaign, $updatedPricesArray),
            LoweredFloorPricePolicy::REMOVE_ALL_CAMPAIGN_BIDS      => $this->removeAllBidsForCampaign($campaign),
            default                                                => true,
        };
    }

    /**
     * Removes bids for a Campaign after updating their custom floor prices
     * Allows a company to immediately make use of new prices without needing to manually adjust bids
     *
     * @param CompanyCampaign $campaign
     * @param array $updatedPrices
     * @return bool
     */
    protected function removeScopedBidsForCampaign(CompanyCampaign $campaign, array $updatedPrices): bool
    {
        $moduleId = $campaign->bidPriceModule?->id;
        if (!$moduleId)
            return true;

        $propertyTypeMap = PropertyType::getIdMap();
        $qualityTierMap = QualityTier::getIdMap();
        $saleTypeMap = SaleType::getIdMap();

        $getPriceConstraints = function(array $price) use ($moduleId, $propertyTypeMap, $qualityTierMap, $saleTypeMap) {
            return [
                ProductStateBidPrice::FIELD_STATE_LOCATION_ID => $price[ProductStateBidPrice::FIELD_STATE_LOCATION_ID],
                ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID  => $propertyTypeMap[$price[StoreCustomCampaignFloorPricingRequest::REQUEST_PROPERTY_TYPE]] ?? null,
                ProductStateBidPrice::FIELD_QUALITY_TIER_ID   => $qualityTierMap[$price[StoreCustomCampaignFloorPricingRequest::REQUEST_QUALITY_TIER]] ?? null,
                ProductStateBidPrice::FIELD_SALE_TYPE_ID      => $saleTypeMap[$price[StoreCustomCampaignFloorPricingRequest::REQUEST_SALE_TYPE]] ?? null,
                ProductStateBidPrice::FIELD_MODULE_ID         => $moduleId,
            ];
        };

        DB::beginTransaction();

        try {
            $removeCountyIds = [];
            $removeStateIds = [];

            foreach ($updatedPrices as $price) {
                $countyLocationId = $price[ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID] ?? null;
                if ($countyLocationId) {
                    $id = ProductCountyBidPrice::query()
                        ->where(ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId)
                        ->where($getPriceConstraints($price))
                        ->first()
                        ?->id;
                    if ($id)
                        $removeCountyIds[] = $id;
                } else {
                    $id = ProductStateBidPrice::query()
                        ->where($getPriceConstraints($price))
                        ->first()
                        ?->id;
                    if ($id)
                        $removeStateIds[] = $id;
                }
            }

            ProductStateBidPrice::query()->whereIn(ProductStateBidPrice::FIELD_ID, $removeStateIds)
                ->delete();
            ProductCountyBidPrice::query()->whereIn(ProductCountyBidPrice::FIELD_ID, $removeCountyIds)
                ->delete();
        }
        catch(Exception $e) {
            logger()->error("Error attempting to update bids on Campaign ID $campaign->id - " . $e->getMessage());
            DB::rollBack();

            return false;
        }

        DB::commit();

        return true;
    }

    /**
     * @param CompanyCampaign $campaign
     * @return bool
     */
    protected function removeAllBidsForCampaign(CompanyCampaign $campaign): bool
    {
        DB::beginTransaction();

        try {
            $campaign->bidPriceModule?->countyBids()->delete();
            $campaign->bidPriceModule?->stateBids()->delete();
        }
        catch(Exception $e) {
            logger()->error("Error attempting to update bids on Campaign ID $campaign->id - " . $e->getMessage());
            DB::rollBack();

            return false;
        }

        DB::commit();

        return true;
    }
}
