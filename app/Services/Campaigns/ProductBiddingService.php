<?php

namespace App\Services\Campaigns;

use App\Builders\Pricing\BasePricingBuilder;
use App\Builders\Pricing\CustomFloorPricingBuilder;
use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Jobs\RecordMonitoringLog;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\PropertyTypeRepository;
use App\Repositories\Odin\QualityTierRepository;
use App\Repositories\Odin\SaleTypeRepository;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\Facades\LogBatch;
use Throwable;

class ProductBiddingService
{
    /**
     * @param QualityTierRepository $qualityTierRepository
     * @param SaleTypeRepository $saleTypeRepository
     * @param PropertyTypeRepository $propertyTypeRepository
     * @param CompanyCampaignRepository $companyCampaignRepository
     */
    public function __construct(
        private readonly QualityTierRepository $qualityTierRepository,
        private readonly SaleTypeRepository $saleTypeRepository,
        private readonly PropertyTypeRepository $propertyTypeRepository,
        private readonly CompanyCampaignRepository $companyCampaignRepository,
    )
    {
    }

    /**
     * @param CompanyCampaign $campaign
     * @param CompanyCampaignBidPriceModule $companyCampaignBidPriceModule
     * @param array $payload
     * @return bool
     */
    public function saveCampaignBids(
        CompanyCampaign $campaign,
        CompanyCampaignBidPriceModule $companyCampaignBidPriceModule,
        array $payload,
    ): bool
    {
        $stateLocationBids = [];
        $countyLocationBids = [];

        foreach($payload[ProductBiddingModule::PAYLOAD_LOCATION_BIDS] as $saleTypeKey => $locationBids) {
            foreach($locationBids as $saleTypeBid) {
                if ($saleTypeBid[ProductBiddingModule::PAYLOAD_IS_STATE_BID]) {
                    $stateLocationBids[$saleTypeKey] = $stateLocationBids[$saleTypeKey] ?? [];
                    $stateLocationBids[$saleTypeKey][] = $saleTypeBid;
                }
                else {
                    $countyLocationBids[$saleTypeKey] = $countyLocationBids[$saleTypeKey] ?? [];
                    $countyLocationBids[$saleTypeKey][] = $saleTypeBid;
                }
            }
        }

        try {
            LogBatch::startBatch();
            $this->handleStateLocationBids($stateLocationBids, $campaign);
            $this->handleCountyLocationBids($countyLocationBids, $campaign);
            $this->cleanUpRedundantBids($campaign);
            LogBatch::endBatch();
        }
        catch(Throwable $e) {
            logger()->error($e->getMessage());
            LogBatch::endBatch();

            RecordMonitoringLog::dispatch(
                "Product bidding err: ".$e->getMessage(),
                [
                    "campaign_reference" => $campaign->{CompanyCampaign::FIELD_REFERENCE}
                ]
            );
        }

        return true;
    }

    /**
     * @param array $stateLocationBids
     * @param CompanyCampaign $campaign
     * @return void
     */
    protected function handleStateLocationBids(array $stateLocationBids, CompanyCampaign $campaign): void
    {
        $qualityTiers  = $this->qualityTierRepository->all();
        $propertyTypes = $this->propertyTypeRepository->all();
        $saleTypes     = $this->saleTypeRepository->all();

        DB::transaction(function() use ($campaign, $stateLocationBids, $qualityTiers, $propertyTypes, $saleTypes) {
            $serviceProductId = $this->companyCampaignRepository->getServiceProductFromCampaign($campaign)->{ServiceProduct::FIELD_ID};

            foreach ($stateLocationBids as $saleTypeKey => $locationBids) {
                $saleTypeId = $this->saleTypeRepository->getSalesTypeByKey($saleTypeKey)?->id ?? $saleTypes[$saleTypeKey] ?? null;
                foreach ($locationBids as $saleTypeBid) {
                    ProductStateBidPrice::query()
                        ->updateOrCreate([
                            ProductStateBidPrice::FIELD_MODULE_ID           => $campaign->bidPriceModule->id,
                            ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID  => $serviceProductId,
                            ProductStateBidPrice::FIELD_QUALITY_TIER_ID     => $qualityTiers[$saleTypeBid[ProductBiddingModule::PAYLOAD_QUALITY_TIER]],
                            ProductStateBidPrice::FIELD_SALE_TYPE_ID        => $saleTypeId,
                            ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID    => $propertyTypes[$saleTypeBid[ProductBiddingModule::PAYLOAD_PROPERTY_TYPE]],
                            ProductStateBidPrice::FIELD_STATE_LOCATION_ID   => $saleTypeBid[ProductBiddingModule::PAYLOAD_STATE_LOCATION_ID],
                        ], [
                            ProductStateBidPrice::FIELD_PRICE => $saleTypeBid[ProductBiddingModule::PAYLOAD_BID],
                        ]);
                }
            }
        });
    }

    /**
     * @param array $countyLocationBids
     * @param CompanyCampaign $campaign
     * @return void
     */
    protected function handleCountyLocationBids(array $countyLocationBids, CompanyCampaign $campaign): void
    {
        $qualityTiers  = $this->qualityTierRepository->all();
        $propertyTypes = $this->propertyTypeRepository->all();
        $saleTypes     = $this->saleTypeRepository->all();

        DB::transaction(function() use ($campaign, $countyLocationBids, $qualityTiers, $propertyTypes, $saleTypes) {
            $serviceProductId = $this->companyCampaignRepository->getServiceProductFromCampaign($campaign)->{ServiceProduct::FIELD_ID};

            foreach ($countyLocationBids as $saleTypeKey => $locationBids) {
                $saleTypeId = $this->saleTypeRepository->getSalesTypeByKey($saleTypeKey)?->id ?? $saleTypes[$saleTypeKey] ?? null;
                foreach ($locationBids as $saleTypeBid) {
                    ProductCountyBidPrice::query()
                        ->updateOrCreate([
                            ProductCountyBidPrice::FIELD_MODULE_ID           => $campaign->bidPriceModule->id,
                            ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID  => $serviceProductId,
                            ProductCountyBidPrice::FIELD_QUALITY_TIER_ID     => $qualityTiers[$saleTypeBid[ProductBiddingModule::PAYLOAD_QUALITY_TIER]],
                            ProductCountyBidPrice::FIELD_SALE_TYPE_ID        => $saleTypeId,
                            ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID    => $propertyTypes[$saleTypeBid[ProductBiddingModule::PAYLOAD_PROPERTY_TYPE]],
                            ProductCountyBidPrice::FIELD_STATE_LOCATION_ID   => $saleTypeBid[ProductBiddingModule::PAYLOAD_STATE_LOCATION_ID],
                            ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID  => $saleTypeBid[ProductBiddingModule::PAYLOAD_LOCATION_ID],
                        ], [
                            ProductCountyBidPrice::FIELD_PRICE => $saleTypeBid[ProductBiddingModule::PAYLOAD_BID],
                        ]);
                }
            }
        });
    }

    /**
     * @param CompanyCampaign $campaign
     * @return void
     */
    protected function cleanUpRedundantBids(CompanyCampaign $campaign): void
    {
        if ($campaign->uses_custom_floor_prices) {
            $this->cleanUpRedundantBidsForCustomPricing($campaign);

            return;
        }

        // Remove State bids where they are equal to or lower than the State floor
        $campaign->bidPriceModule->stateBidsQuery()
            ->withFloorPrices()
            ->getQuery()
            ->whereColumn(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE,
                '<=',
                ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE)
            ->get()
            ->each(fn (ProductStateBidPrice $price) => $price->delete());

        // Remove county bids where they are equal to or lower than the first non-null value from
        //  - parent state bid
        //  - county floor
        //  - state floor
        $campaign->bidPriceModule->countyBidsQuery()
            ->withFloorPrices()
            ->withStatePrices()
            ->getQuery()
            ->whereRaw(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE . ' < ' .
                "COALESCE(" .
                ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE . "," .
                ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE . "," .
                ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE .
                ")")
            ->get()
            ->each(fn (ProductStateBidPrice $price) => $price->delete());
    }

    /**
     * This removes:
     *  - county bids which are less than the first price found from custom county -> custom state -> standard county -> standard state
     *  - state bids which are less than the first price found from custom state -> standard state floor
     * This does not remove county bids which are lower than state bids, this is not currently enforced for Custom Pricing
     * @param CompanyCampaign $campaign
     * @return void
     */
    protected function cleanUpRedundantBidsForCustomPricing(CompanyCampaign $campaign): void
    {
        $campaign->bidPriceModule->stateBidsQuery()
            ->withFloorPrices()
            ->getQuery()
            ->whereRaw(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE . ' < ' .
                "COALESCE(" .
                    CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE . "," .
                    ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE .
                ")"
            )->get()
            ->each(fn (ProductStateBidPrice $price) => $price->delete());

        $campaign->bidPriceModule->countyBidsQuery()
            ->withStatePrices()
            ->withFloorPrices()
            ->getQuery()
            ->whereRaw(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE . ' < ' .
                "COALESCE(" .
                    CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE . "," .
                    CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE . "," .
                    ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE . "," .
                    ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE .
                ")"
            )->get()
            ->each(fn (ProductStateBidPrice $price) => $price->delete());
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param int $countyLocationId
     * @param int $stateLocationId
     * @param int $propertyTypeId
     * @param int $qualityTierId
     * @param int $salesTypeId
     * @return float
     */
    public function getProductBid(
        CompanyCampaign $companyCampaign,
        int $countyLocationId,
        int $stateLocationId,
        int $propertyTypeId,
        int $qualityTierId,
        int $salesTypeId,
    ): float
    {
        $serviceProductId = $this->companyCampaignRepository->getServiceProductFromCampaign($companyCampaign)->{ServiceProduct::FIELD_ID};

        return $this->getHighestBidOrFloorPrice(
            countyLocationId: $countyLocationId,
            stateLocationId: $stateLocationId,
            biddingModuleId: $companyCampaign->bidPriceModule?->id,
            propertyTypeId: $propertyTypeId,
            saleTypeId: $salesTypeId,
            qualityTierId: $qualityTierId,
            serviceProductId: $serviceProductId,
            companyCampaign: $companyCampaign,
        );
    }

    /**
     *
     * @param int $countyLocationId
     * @param int $stateLocationId
     * @param int|null $biddingModuleId
     * @param int $propertyTypeId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $serviceProductId
     * @param CompanyCampaign $companyCampaign
     * @return float
     */
    protected function getHighestBidOrFloorPrice(
        int   $countyLocationId,
        int   $stateLocationId,
        ?int  $biddingModuleId,
        int   $propertyTypeId,
        int   $saleTypeId,
        int   $qualityTierId,
        int   $serviceProductId,
        CompanyCampaign $companyCampaign,
    ): float
    {
        if ($companyCampaign->uses_custom_floor_prices) {
            return $this->getPriceForCustomFloorCampaign(
                stateLocationId: $stateLocationId,
                countyLocationId: $countyLocationId,
                biddingModuleId: $biddingModuleId,
                propertyTypeId: $propertyTypeId,
                saleTypeId: $saleTypeId,
                qualityTierId: $qualityTierId,
                serviceProductId: $serviceProductId,
                companyCampaignId: $companyCampaign->id,
            );
        }

        $prices = BasePricingBuilder::query()
            ->withCounties()
            ->withBids()
            ->select([])
            ->aliasPriceColumns()
            ->forState($stateLocationId)
            ->forCounty($countyLocationId)
            ->forBiddingModule($biddingModuleId)
            ->forPropertyType($propertyTypeId)
            ->forSaleType($saleTypeId)
            ->forQualityTier($qualityTierId)
            ->forServiceProduct($serviceProductId)
            ->getQuery()
            ->first();

        $floor = $prices->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE}
            ?? $prices->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE};

        $bid = max($prices->{BasePricingBuilder::ALIAS_COUNTY_BID_PRICE} ?? 0,
            $prices->{BasePricingBuilder::ALIAS_STATE_BID_PRICE} ?? 0);

        return max(
            $floor,
            $bid,
        );
    }

    /**
     * Calculate the prices for a campaign with custom floor pricing
     * TODO: finalise logic for selecting price
     *
     * Bid price will consider:
     *  - max(custom_county_floor, county_bid)  ... max() required to prevent underselling if custom floor price is raised above a previous bid level
     *  - max(custom_state_floor, state_bid)
     *  - fallback to standard floor pricing
     *
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @param int $biddingModuleId
     * @param int $propertyTypeId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $serviceProductId
     * @param int $companyCampaignId
     * @return float
     */
    private function getPriceForCustomFloorCampaign(
        int $stateLocationId,
        int $countyLocationId,
        int $biddingModuleId,
        int $propertyTypeId,
        int $saleTypeId,
        int $qualityTierId,
        int $serviceProductId,
        int $companyCampaignId,
    ): float
    {
        $prices = CustomFloorPricingBuilder::query()
            ->withCounties()
            ->withBids()
            ->forState($stateLocationId)
            ->forCounty($countyLocationId)
            ->forBiddingModule($biddingModuleId)
            ->forPropertyType($propertyTypeId)
            ->forSaleType($saleTypeId)
            ->forQualityTier($qualityTierId)
            ->forServiceProduct($serviceProductId)
            ->forCompanyCampaign($companyCampaignId)
            ->select([])
            ->aliasPriceColumns()
            ->getQuery()
            ->first();

        return
            max($prices->{BasePricingBuilder::ALIAS_COUNTY_BID_PRICE},
                $prices->{CustomFloorPricingBuilder::ALIAS_CUSTOM_COUNTY_FLOOR_PRICE})
            ?: max($prices->{BasePricingBuilder::ALIAS_STATE_BID_PRICE},
                $prices->{CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE})
            ?: max($prices->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE},
                $prices->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE});
    }
}
