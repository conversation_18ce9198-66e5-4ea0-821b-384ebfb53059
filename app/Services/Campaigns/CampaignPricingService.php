<?php

namespace App\Services\Campaigns;

use App\Builders\Pricing\BasePricingBuilder;
use App\Builders\Pricing\CustomFloorPricingBuilder;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\QualityTier;
use App\Models\SaleType;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\ProductProcessing\FloorPricingRepository;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use App\Enums\Odin\QualityTier as QualityTierEnum;

/**
 * Floor & Bid pricing service for CompanyCampaigns
 */
class CampaignPricingService
{
    const string KEY_FLOOR_PRICE = 'floor_price';
    const string KEY_BID_PRICE   = 'bid_price';

    public function __construct(
        protected CompanyCampaignRepository $companyCampaignRepository,
    ) {}

    /**
     * Handles fetching floor and bid pricing for a County
     * Will return only floor pricing if the Campaign is not provided
     *
     * @param int $serviceProductId
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @param CompanyCampaign|null $campaign
     * @return array
     */
    public function getCountyPricing(
        int $serviceProductId,
        int $stateLocationId,
        int $countyLocationId,
        ?CompanyCampaign $campaign = null,
    ): array
    {
        $customFloorPricing = $campaign?->uses_custom_floor_prices ?? false;
        $moduleId = $campaign?->bidPriceModule?->id;

        $combinedPrices = $customFloorPricing
            ? CustomFloorPricingBuilder::query()
                ->forState($stateLocationId)
                ->forCounty($countyLocationId)
                ->withCounties()
                ->withBids()
                ->forCompanyCampaign($campaign->id)
                ->select([ProductStateFloorPrice::TABLE . '.*'])
                ->aliasPriceColumns()
                ->getQuery()
                ->get()
            : BasePricingBuilder::query()
                ->withCounties()
                ->withBids()
                ->select(ProductStateFloorPrice::TABLE . ".*")
                ->aliasPriceColumns()
                ->forCounty($countyLocationId)
                ->forState($stateLocationId)
                ->forServiceProduct($serviceProductId)
                ->forBiddingModule($moduleId)
                ->forCampaignType($campaign->type)
                ->getQuery()
                ->get();

        return $this->transformAndGroupPricesForBidding($combinedPrices, $countyLocationId, $customFloorPricing)->toArray();
    }

    /**
     *  Handles fetching floor and bid pricing for a State
     *  Will return only floor pricing if the Campaign is not provided
     *
     * @param int $serviceProductId
     * @param int $stateLocationId
     * @param CompanyCampaign|null $campaign
     * @return array
     */
    public function getStatePricing(
        int $serviceProductId,
        int $stateLocationId,
        ?CompanyCampaign $campaign = null,
    ): array
    {
        $customFloorPricing = $campaign?->uses_custom_floor_prices ?? false;
        $moduleId = $campaign?->bidPriceModule?->id;

        $combinedPrices = $customFloorPricing
            ? CustomFloorPricingBuilder::query()
                ->withBids()
                ->forState($stateLocationId)
                ->forCompanyCampaign($campaign->id)
                ->select([ProductStateFloorPrice::TABLE.'.*'])
                ->aliasPriceColumns()
                ->getQuery()
                ->get()
            : BasePricingBuilder::query()
                ->forState($stateLocationId)
                ->forServiceProduct($serviceProductId)
                ->forBiddingModule($moduleId)
                ->forCampaignType($campaign?->type)
                ->withBids()
                ->select([ProductStateFloorPrice::TABLE.'.*'])
                ->aliasPriceColumns()
                ->getQuery()
                ->get();

        $countyKeys = $campaign?->bidPriceModule
            ? $this->getCountyKeysWithActiveBids($campaign, $stateLocationId)
            : [];

        $prices = $this->transformAndGroupPricesForBidding($combinedPrices,null, $customFloorPricing)->toArray();

        return [$prices, $countyKeys];
    }

    /**
     * Transform floor/bid price data for campaign bidding control
     *
     * @param Collection $combinedPrices
     * @param int|null $countyLocationId
     * @param bool|null $usesCustomFloorPricing
     * @return Collection
     */
    protected function transformAndGroupPricesForBidding(Collection $combinedPrices, ?int $countyLocationId = null, ?bool $usesCustomFloorPricing = false): Collection
    {
        $countyLocationKey = $countyLocationId
            ? Location::query()->find($countyLocationId)->county_key
            : null;

        $transformedPrices = $combinedPrices->groupBy([
            fn($price) => $price->serviceProduct->product->name,
            fn($price) => $price->propertyType->name,
            fn($price) => $price->stateLocation->state_abbr,
            fn($price) => $price->countyLocation?->county_key ?? $countyLocationKey ?? 'state',
            fn($price) => $price->qualityTier->name,
            fn($price) => $price->saleType->key
        ])->collect();

        foreach ($transformedPrices as $key1 => &$serviceProduct) {
            foreach ($serviceProduct as $key2 => &$propertyType) {
                foreach ($propertyType as $key3 => &$stateLocation) {
                    foreach ($stateLocation as $key4 => &$countyLocation) {
                        foreach ($countyLocation as $key5 => &$qualityTier) {
                            foreach ($qualityTier as $key6 => &$saleType) {
                                $prices = $usesCustomFloorPricing
                                    ? $this->setCustomFloorAndBidPrices($saleType->first(), !!$countyLocationId)
                                    : $this->setStandardFloorAndBidPrices($saleType->first(), !!$countyLocationId);

                                $transformedPrices->get($key1)->get($key2)->get($key3)->get($key4)->get($key5)
                                    ->put($key6, $prices);
                            }
                        }
                    }
                }
            }
        }

        return $transformedPrices;
    }

    /**
     * Return the floor and bid price for a standard campaign.
     * This will look for the highest value from the state and county floor prices and bids
     *
     * @param ProductStateFloorPrice $price
     * @param bool $countyPrices
     * @return array
     */
    private function setStandardFloorAndBidPrices(ProductStateFloorPrice $price, bool $countyPrices): array
    {
        $countyFloor = $price->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE}
            ?? $price->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE};
        $countyBid = $price->{BasePricingBuilder::ALIAS_COUNTY_BID_PRICE}
            ?? 0;

        return [
            self::KEY_FLOOR_PRICE => $countyPrices
                ? $countyFloor
                : $price->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE},
            self::KEY_BID_PRICE => $countyPrices
                ? max($countyFloor, $countyBid)
                : max($price->{BasePricingBuilder::ALIAS_STATE_BID_PRICE},
                    $price->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE})
        ];
    }

    /**
     * Return the floor and bid prices for a campaign with custom floor pricing enabled
     * Floor price will preference custom_county_floor, then custom_state_floor, then use standard floor logic if neither exist
     * Bid price will consider:
     *  - max(custom_county_floor, county_bid)  ... max() required to prevent underselling if custom floor price is raised above a previous bid level
     *  - max(custom_state_floor, state_bid)
     *  - standard floor pricing
     *
     * @param ProductStateFloorPrice $price
     * @param bool $countyPrices
     * @return array
     */
    private function setCustomFloorAndBidPrices(ProductStateFloorPrice $price, bool $countyPrices): array
    {
        if ($countyPrices) {
            return [
                self::KEY_FLOOR_PRICE => $price->{CustomFloorPricingBuilder::ALIAS_CUSTOM_COUNTY_FLOOR_PRICE}
                    ?: $price->{CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE}
                    ?: max($price->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE},
                        $price->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE}),
                self::KEY_BID_PRICE =>
                    max($price->{BasePricingBuilder::ALIAS_COUNTY_BID_PRICE},
                        $price->{CustomFloorPricingBuilder::ALIAS_CUSTOM_COUNTY_FLOOR_PRICE})
                        ?: max($price->{BasePricingBuilder::ALIAS_STATE_BID_PRICE},
                        $price->{CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE})
                        ?: max($price->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE},
                            $price->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE}),
            ];
        }
        else return [
            self::KEY_FLOOR_PRICE => $price->{CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE}
                ?: $price->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE},
            self::KEY_BID_PRICE =>
                max($price->{BasePricingBuilder::ALIAS_STATE_BID_PRICE},
                    $price->{CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE})
                        ?: $price->{BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE},
        ];
    }

    /**
     * Handles fetching min/max prices for a given array of zip codes
     *
     * @param int $serviceProductId
     * @param array $propertyTypeIds
     * @param array $zipCodeIds
     * @param bool $groupByQualityTier
     * @param bool|null $zipCodeTargeted
     * @return array
     */
    public function getPriceRangeForZipCodes(
        int $serviceProductId,
        array $propertyTypeIds,
        array $zipCodeIds,
        bool $groupByQualityTier = false,
        ?bool $zipCodeTargeted = false,
    ): array
    {
        // Get unique state and county location ids
        [$stateLocationIds, $countyLocationIds] = Location::query()
            ->selectRaw('l2.' . Location::ID . ' as county_id, l3.' . Location::ID . ' as state_id')
            ->distinct('l2.' . Location::ID)
            ->join(Location::TABLE .' as l2', function(JoinClause $join) {
                $join->on(Location::TABLE .'.'. Location::COUNTY_KEY, '=', 'l2.' . Location::COUNTY_KEY)
                    ->on(Location::TABLE .'.'. Location::STATE_ABBREVIATION, '=', 'l2.' . Location::STATE_ABBREVIATION)
                    ->where('l2.' . Location::TYPE, '=', Location::TYPE_COUNTY);
            })->join(Location::TABLE .' as l3', function(JoinClause $join) {
                $join->on('l2.' . Location::STATE_ABBREVIATION, '=', 'l3.' . Location::STATE_ABBREVIATION)
                    ->where('l3.' . Location::TYPE, '=', Location::TYPE_STATE);
            })->where(Location::TABLE .'.'. Location::TYPE, Location::TYPE_ZIP_CODE)
            ->whereIn(Location::TABLE .'.'. Location::ID, $zipCodeIds)
            ->get()
            ->reduce(function(array $output, $location) {
                if (!in_array($location->state_id, $output[0])) $output[0][] = $location->state_id;
                if (!in_array($location->county_id, $output[1])) $output[1][] = $location->county_id;
                return $output;
            }, [[], []]);

        $priceQuery = BasePricingBuilder::query()
            ->withCounties()
            ->aliasPriceColumns()
            ->forStates($stateLocationIds)
            ->forCounties($countyLocationIds)
            ->forPropertyTypes($propertyTypeIds)
            ->forServiceProduct($serviceProductId)
            ->getQuery()
            ->selectRaw(
                'min(' . ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE . ') as min,' .
                'max(' . ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE . ') as county_max,' .
                'max(' . ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE . ') as state_max'
            );

        // appointments are grouped by quality_tier, not by sale_type
        if ($groupByQualityTier) {
            $priceQuery->addSelect(QualityTier::TABLE .'.'. QualityTier::FIELD_NAME .' as key')
                ->join(QualityTier::TABLE, QualityTier::TABLE .'.'. QualityTier::FIELD_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID)
                ->groupBy(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID);
        }
        else {
            $priceQuery->addSelect(SaleType::TABLE .'.'. SaleType::FIELD_KEY)
                ->join(SaleType::TABLE, SaleType::TABLE .'.'. SaleType::FIELD_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID)
                ->groupBy(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID);
        }

        $prices = $priceQuery->get();
        if ($zipCodeTargeted)
            FloorPricingRepository::updatePriceCollectionForZipCodeTargeting($prices, ['min', 'county_max', 'state_max']);

        return $prices->reduce(function(array $output, $price) use (&$groupByQualityTier) {
            $key = $groupByQualityTier
                ? QualityTierEnum::from($price->key)?->getAppointmentBudgetKey() ?? $price->key
                : $price->key;
            $output[$key] = [
                'min'   => $price->min,
                'max'   => max($price->county_max, $price->state_max),
            ];

            return $output;
        }, []);
    }

    /**
     * Gets an array of county keys where the county has an active bid above the floor price
     * Custom floor pricing campaigns will include prices equal to the floor price
     * @param CompanyCampaign $campaign
     * @param int $stateLocationId
     * @return array
     */
    public function getCountyKeysWithActiveBids(CompanyCampaign $campaign, int $stateLocationId): array
    {
        $customFloorPrices = $campaign->uses_custom_floor_prices;
        $comparisonOperator = $customFloorPrices
            ? '>='
            : '>';

        return $campaign->bidPriceModule->countyBidsQuery()
            ->withStatePrices()
            ->withFloorPrices()
            ->forStateLocation($stateLocationId)
            ->getQuery()
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID)
            ->when($customFloorPrices, fn(Builder $query) =>
                $query->whereRaw(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE . " $comparisonOperator " .
                    "COALESCE(" .
                        CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE . "," .
                        CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE . "," .
                        ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE . "," .
                        ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE .
                    ")")
            )->when(!$customFloorPrices, fn(Builder $query) =>
                $query->whereRaw(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE . " $comparisonOperator " .
                    "COALESCE(" .
                        ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE . "," .
                        ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE .
                    ")")
            )->select(Location::TABLE .'.'. Location::COUNTY_KEY)
            ->pluck(Location::TABLE .'.'. Location::COUNTY_KEY)
            ->unique()
            ->values()
            ->toArray();
    }

    /**
     * Gets an array of state keys where the state location has an active bid above the floor price
     * Custom floor pricing campaigns will include prices equal to the floor price
     * @param CompanyCampaign $campaign
     * @param int|null $stateLocationId
     * @return array
     */
    public function getStateKeysWithActiveBids(CompanyCampaign $campaign, ?int $stateLocationId = null): array
    {
        $customFloorPrices = $campaign->uses_custom_floor_prices;
        $comparisonOperator = $customFloorPrices
            ? '>='
            : '>';

        return $campaign->bidPriceModule->stateBidsQuery()
            ->withFloorPrices()
            ->forStateLocation($stateLocationId)
            ->getQuery()
            ->join(Location::TABLE, Location::TABLE .'.'. Location::ID, '=', ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_STATE_LOCATION_ID)
            ->when($customFloorPrices, fn(Builder $query) =>
                $query->whereRaw(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE . " $comparisonOperator " .
                    "COALESCE(" .
                        CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE . "," .
                        ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE .
                    ")")
            )->when(!$customFloorPrices, fn(Builder $query) =>
                $query->whereColumn(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE, $comparisonOperator, ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE)
            )->select(Location::TABLE .'.'. Location::STATE_KEY)
            ->pluck(Location::TABLE .'.'. Location::STATE_KEY)
            ->unique()
            ->values()
            ->toArray();
    }
}
