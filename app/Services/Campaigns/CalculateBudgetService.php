<?php

namespace App\Services\Campaigns;

use App\Models\AvailableBudget;
use App\Models\Campaigns\CompanyCampaign;
use App\Strategies\CampaignBudget\CalculateBudgetFactory;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as BaseCollection;

class CalculateBudgetService
{
    /**
     * @param Collection $campaigns
     * @return array
     * @throws BindingResolutionException
     */
    public function calculateUnverifiedBudgetsForCampaigns(Collection $campaigns): array
    {
        return $this->calculateBudgetsForCampaigns($campaigns, AvailableBudget::BUDGET_TYPE_UNVERIFIED);
    }

    /**
     * @param Collection $campaigns
     * @return array
     * @throws BindingResolutionException
     */
    public function calculateVerifiedBudgetsForCampaigns(Collection $campaigns): array
    {
        return $this->calculateBudgetsForCampaigns($campaigns, AvailableBudget::BUDGET_TYPE_VERIFIED);
    }

    /**
     * @param Collection<CompanyCampaign> $campaigns
     *
     * @return BaseCollection
     * @throws BindingResolutionException
     */
    public function calculateVerifiedBudgetsForFutureCampaign(Collection $campaigns): BaseCollection
    {
        return $this->calculateBudgetsForCampaigns($campaigns, CalculateBudgetFactory::BUDGET_TYPE_FUTURE_CAMPAIGN_VERIFIED);
    }

    /**
     * @param Collection<CompanyCampaign> $campaigns
     *
     * @return array
     * @throws BindingResolutionException
     */
    public function calculateUnverifiedBudgetsForFutureCampaign(Collection $campaigns): array
    {
        return $this->calculateBudgetsForCampaigns($campaigns, CalculateBudgetFactory::BUDGET_TYPE_FUTURE_CAMPAIGN_UNVERIFIED);
    }

    /**
     * @param Collection $campaigns
     * @param string $budgetType
     *
     * @return array|BaseCollection
     * @throws BindingResolutionException
     */
    public function calculateBudgetsForCampaigns(Collection $campaigns, string $budgetType = AvailableBudget::BUDGET_TYPE_VERIFIED): array|BaseCollection
    {
        $service = CalculateBudgetFactory::make($budgetType);
        return $service->calculateBudgetAvailable($campaigns);
    }
}
