<?php

namespace App\Services\Campaigns;

use App\Enums\Campaigns\CRMFieldReplacerKey;
use Illuminate\Support\Collection;

/**
 * TODO: we could replace the hard-coded values by building example Campaign and Product models,
 *  then passing them through the FieldReplacerService.
 */
class CampaignHelpService
{
    /**
     * @return Collection
     */
    public function getCrmFieldReplacerExamples(): Collection
    {
        $crmFields = CRMFieldReplacerKey::getAllKeysAndNames();

        foreach ($crmFields as &$crmField) {
            $crmField['example'] = $this->getFieldReplacerExamples($crmField['value']);
        }

        return collect($crmFields);
    }

    /**
     * @return string
     */
    public function getFieldReplacerInstructions(): string
    {
        return "The fields can be used to configure CRM Integrations. The first column is a description of the data. The second column is what you will need to copy + paste into an integration configuration text box. The third column is an example of the data that will be sent to the CRM when a lead is sent.
            - Example 1:
            \tTo send the full name, '[full_name]' is to be placed inside the integration configuration text box. This will be replaced by the name in the lead. Eg: '<PERSON>'.
            - Example 2:
            \tIf the installer would like the name to be in the format 'Smith, <PERSON>'' then you can enter the following into the integration text box '[last_name], [first_name]' and that will be replaced with the last name and first name when the lead is sent to the CRM.";
    }

    /**
     * @param string $fieldValue
     * @return string|array
     */
    private function getFieldReplacerExamples(string $fieldValue): string|array
    {
        return match($fieldValue) {
            CRMFieldReplacerKey::BRAND->value                      => ['SolarReviews', 'Roofing Calculator', 'Fixr'],
            CRMFieldReplacerKey::DATE->value                       => now()->toDateString(),
            CRMFieldReplacerKey::LEAD_SOURCE->value                => ['SolarEstimate', 'MySolar', 'Fixr', 'Cut My Bill'],
            CRMFieldReplacerKey::LEAD_INDUSTRY->value              => ['Solar', 'Roofing', 'Siding', 'Windows'],
            CRMFieldReplacerKey::LEAD_SERVICE->value               => ['Installation', 'Repair', 'Replacement'],
            CRMFieldReplacerKey::FIRST_NAME->value                 => 'John',
            CRMFieldReplacerKey::LAST_NAME->value                  => 'Smith',
            CRMFieldReplacerKey::FULL_NAME->value                  => 'John Smith',
            CRMFieldReplacerKey::ACCOUNT_NAME->value               => 'John Smith Residence',
            CRMFieldReplacerKey::EMAIL->value                      => '<EMAIL>',
            CRMFieldReplacerKey::FULL_ADDRESS->value               => '123 Fake Street, Unit 1, Los Angeles, CA 90210, US',
            CRMFieldReplacerKey::ADDRESS->value                    => '123 Fake Street, Unit 1',
            CRMFieldReplacerKey::ADDRESS_1->value                  => '123 Fake Street',
            CRMFieldReplacerKey::ADDRESS_2->value                  => 'Unit 1',
            CRMFieldReplacerKey::CITY->value                       => 'Los Angeles',
            CRMFieldReplacerKey::STATE_ABBR->value                 => 'CA',
            CRMFieldReplacerKey::STATE->value                      => 'California',
            CRMFieldReplacerKey::ZIP_CODE->value                   => '90210',
            CRMFieldReplacerKey::COUNTRY_ABBR->value               => 'US',
            CRMFieldReplacerKey::COUNTRY->value                    => 'United States of America',
            CRMFieldReplacerKey::COMBINED_COMMENTS->value          => "I am interested in getting solar but I'd like to talk to someone about the best panel brands to buy.\nContact Time: 9am-11am\nAppointments Requested: 31/10/2017 9am-11am\nSystem Size (KW): 12\nTime Frame: Next few months\nRoof Type: Flat\nRoof Pitch: Slight\nShading: Unshaded 9am to 3pm\nCredit Score: 650 to 680",
            CRMFieldReplacerKey::ELECTRIC_SPEND->value             => '<300',
            CRMFieldReplacerKey::UTILITY_NAME->value               => 'Some Utility Company',
            CRMFieldReplacerKey::LEAD_ID->value                    => '123456',
            CRMFieldReplacerKey::ASSIGNMENT_ID->value              => '123456',
            CRMFieldReplacerKey::UNIVERSAL_LEAD_ID->value          => 'e2f919cb-1037-4f9d-9236-dfbaccb33446',
            CRMFieldReplacerKey::LEAD_PRICE->value                 => '100',
            CRMFieldReplacerKey::NUMBER_OF_QUOTES_REQUESTED->value => ['1', '2', '3', '4'],
            CRMFieldReplacerKey::PHONE->value                      => '(*************',
            CRMFieldReplacerKey::SYSTEM_SIZE->value                => '12',
            CRMFieldReplacerKey::ORIGIN_URL->value                 => 'https://www.solarreviews.com/solar-panels/',
            CRMFieldReplacerKey::CAMPAIGN_NAME->value              => 'CA Solar Campaign',
            CRMFieldReplacerKey::LEAD_SALE_TYPE->value             => ['Exclusive', 'Duo', 'Trio', 'Quad'],
            CRMFieldReplacerKey::LEAD_SALE_COUNT->value            => ['1', '2', '3', '4'],
            CRMFieldReplacerKey::PROPERTY_TYPE->value              => ['Residential', 'Commercial'],
            CRMFieldReplacerKey::LEAD_TYPE->value                  => ['Standard', 'Premium'],
            CRMFieldReplacerKey::COMPANY_NAME->value               => 'My Awesome Solar Company',
            CRMFieldReplacerKey::COMPANY_USER_NAME->value          => 'Leeroy Jenkins',
            CRMFieldReplacerKey::PRODUCT->value                    => ['Lead', 'DirectLead'],
            CRMFieldReplacerKey::SHADE->value                      => ['A little'],
            CRMFieldReplacerKey::BEST_TIME_TO_CALL->value          => '9am-11am, 1pm-3pm',
            CRMFieldReplacerKey::TRUSTED_FORM_URL->value           => "https://cert.trustedform.com/abc123",
            CRMFieldReplacerKey::WATCHDOG_VIDEO_LINK->value        => 'https://watchdog-2.com/videos/123?signature=abc123',
            CRMFieldReplacerKey::OPT_IN_NAME->value                => 'My Solar Company',
            default                                                => '-',
        };
    }
}
