<?php

namespace App\Services\Campaigns;

use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Odin\Product;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Repositories\Odin\DirectLeadsRepository;
use App\Repositories\ProductAppointmentRepository;

class PotentialProductService
{
    const PRODUCT_LEAD        = Product::LEAD->value;
    const PRODUCT_APPOINTMENT = Product::APPOINTMENT->value;

    const QUALITY_TIER_STANDARD = QualityTier::STANDARD->value;
    const QUALITY_TIER_PREMIUM  = QualityTier::PREMIUM->value;
    const QUALITY_TIER_IN_HOME  = QualityTier::IN_HOME->value;
    const QUALITY_TIER_ONLINE   = QualityTier::ONLINE->value;

    const SALES_TYPE_EXCLUSIVE  = SaleTypes::EXCLUSIVE->value;
    const SALES_TYPE_DUO        = SaleTypes::DUO->value;
    const SALES_TYPE_TRIO       = SaleTypes::TRIO->value;
    const SALES_TYPE_QUAD       = SaleTypes::QUAD->value;
    const SALES_TYPE_UNVERIFIED = SaleTypes::UNVERIFIED->value;
    const SALES_TYPE_EMAIL_ONLY = SaleTypes::EMAIL_ONLY->value;

    /** @var array $potentialProducts */
    private array $potentialProducts = [];

    public function __construct(private readonly ProductAppointmentRepository $appointmentRepository) {}

    /**
     * Handles determining exactly which product type variations a given ConsumerProduct may be allocated as
     * This does not consider previous allocations
     *
     * return e.g.
     [
        'lead' => [
            'standard' => [
                'exclusive',
                'duo',
                'trio',
                'quad',
                'email_only',
                'unverified',
            ],
            'premium' => [
                'exclusive',
                'duo',
                'trio',
                'quad',
            ]
    ],
        'appointment' => [
            'in_home' => [
                'exclusive',
                'duo',
            ],
            'online' => [
                'exclusive',
                'duo',
                'trio',
            ]
        ],
    ]
     *
     * @param ConsumerProject $consumerProject
     * @param bool $includeUnverified
     * @param bool $includeEmailOnly
     * @return array
     */
    public function getPotentialProductsForConsumerProject(
        ConsumerProject $consumerProject,
        bool $includeUnverified = false,
        bool $includeEmailOnly = false,
    ): array
    {
        $this->potentialProducts = [];

        // Appointment
        if ($this->meetsBasicAppointmentCriteria($consumerProject)) {

            // In-Home
            if ($this->meetsInHomeAppointmentCriteria($consumerProject)) {
                $salesTypes = $this->getInHomeSalesTypes($consumerProject);
                if (count($salesTypes) > 0)
                    $this->addPotentialProduct(self::PRODUCT_APPOINTMENT, self::QUALITY_TIER_IN_HOME, $salesTypes);
            }

            // Online
            if ($this->meetsOnlineAppointmentCriteria($consumerProject)) {
                $salesTypes = $this->getOnlineSalesTypes($consumerProject);
                if (count($salesTypes) > 0)
                    $this->addPotentialProduct(self::PRODUCT_APPOINTMENT, self::QUALITY_TIER_ONLINE, $salesTypes);
            }

        }

        // Lead
        if ($this->meetsBasicLeadCriteria($consumerProject)) {

            $salesTypes = $this->getLeadSalesTypes($consumerProject, $includeUnverified, $includeUnverified, $consumerProject->unverifiedSaleOnly());

            // Standard
            if ($this->meetsStandardLeadCriteria($consumerProject)) {
                if (count($salesTypes) > 0)
                    $this->addPotentialProduct(self::PRODUCT_LEAD, self::QUALITY_TIER_STANDARD, $salesTypes);
            }

            // Premium
            if ($this->meetsPremiumLeadCriteria($consumerProject)) {
                if (count($salesTypes) > 0)
                    $this->addPotentialProduct(self::PRODUCT_LEAD, self::QUALITY_TIER_PREMIUM, $salesTypes);
            }

        }

        // Direct Leads
        if ($this->meetsBasicDirectLeadsCriteria($consumerProject)) {
            $salesTypes = $this->getLeadSalesTypes($consumerProject, $includeUnverified, $includeUnverified);

            // Standard
            if ($this->meetsStandardLeadCriteria($consumerProject)) {
                if (count($salesTypes) > 0)
                    $this->addPotentialProduct(Product::DIRECT_LEADS->value, self::QUALITY_TIER_STANDARD, $salesTypes);
            }

            // Premium
            if ($this->meetsPremiumLeadCriteria($consumerProject)) {
                if (count($salesTypes) > 0)
                    $this->addPotentialProduct(Product::DIRECT_LEADS->value, self::QUALITY_TIER_PREMIUM, $salesTypes);
            }
        }

        return $this->potentialProducts;
    }

    /**
     * @param ConsumerProject $project
     *
     * @return bool
     */
    protected function meetsBasicDirectLeadsCriteria(ConsumerProject $project): bool
    {
        /** @var DirectLeadsRepository $repository */
        $repository = app(DirectLeadsRepository::class);

        return $repository->isConsumerProductEligibleForDirectLeads($project->leadConsumerProduct()->id);
    }

    /**
     * @param string $product
     * @param string $qualityTier
     * @param $salesTypes
     * @return void
     */
    private function addPotentialProduct(string $product, string $qualityTier, $salesTypes): void
    {
        if (!array_key_exists($product, $this->potentialProducts))
            $this->potentialProducts[$product] = [];

        $this->potentialProducts[$product][$qualityTier] = $salesTypes;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    private function meetsBasicLeadCriteria(ConsumerProject $consumerProject): bool
    {
        return true;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    private function meetsStandardLeadCriteria(ConsumerProject $consumerProject): bool
    {
        return true;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    private function meetsPremiumLeadCriteria(ConsumerProject $consumerProject): bool
    {
        /** @var QualityTierService $qualityTierService */
        $qualityTierService = app(QualityTierService::class);
        $qualityTier = $qualityTierService->evaluateConsumerProject($consumerProject, Product::LEAD);

        return $qualityTier === QualityTier::PREMIUM;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @param bool $includeUnverified
     * @param bool $includeEmailOnly
     * @param bool $unverifiedOnly
     *
     * @return array
     */
    private function getLeadSalesTypes(ConsumerProject $consumerProject, bool $includeUnverified, bool $includeEmailOnly, bool $unverifiedOnly = false): array
    {
        if ($unverifiedOnly) {
            return  [self::SALES_TYPE_UNVERIFIED];
        }

        $salesTypes =  match ($consumerProject->max_contact_requests) {
            1 => [self::SALES_TYPE_EXCLUSIVE],
            2 => [self::SALES_TYPE_EXCLUSIVE, self::SALES_TYPE_DUO],
            3 => [self::SALES_TYPE_EXCLUSIVE, self::SALES_TYPE_DUO, self::SALES_TYPE_TRIO],
            default => [self::SALES_TYPE_EXCLUSIVE, self::SALES_TYPE_DUO, self::SALES_TYPE_TRIO, self::SALES_TYPE_QUAD],
        };

        if($includeUnverified)
            $salesTypes = array_merge([$salesTypes, [self::SALES_TYPE_UNVERIFIED]]);

        if($includeEmailOnly)
            $salesTypes = array_merge([$salesTypes, [self::SALES_TYPE_EMAIL_ONLY]]);

        return $salesTypes;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    // Appointments are currently disabled as a potential product.
    private function meetsBasicAppointmentCriteria(ConsumerProject $consumerProject): bool
    {
        return false;
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    private function meetsInHomeAppointmentCriteria(ConsumerProject $consumerProject): bool
    {
        return $this->appointmentRepository->inHomeAppointmentsExistForConsumerProject($consumerProject);
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return bool
     */
    private function meetsOnlineAppointmentCriteria(ConsumerProject $consumerProject): bool
    {
        return $this->appointmentRepository->onlineAppointmentsExistForConsumerProject($consumerProject);
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return array
     */
    private function getInHomeSalesTypes(ConsumerProject $consumerProject): array
    {
        $inHomeAppointmentsAvailable = $this->appointmentRepository->getInHomeAppointmentsForConsumerProject($consumerProject)->count();
        return match ($inHomeAppointmentsAvailable) {
            1 => [self::SALES_TYPE_EXCLUSIVE],
            2 => [self::SALES_TYPE_EXCLUSIVE, self::SALES_TYPE_DUO],
            default => []
        };
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return array
     */
    private function getOnlineSalesTypes(ConsumerProject $consumerProject): array
    {
        $inHomeAppointmentsAvailable = $this->appointmentRepository->getInOnlineAppointmentsForConsumerProject($consumerProject)->count();
        return match ($inHomeAppointmentsAvailable) {
            1 => [self::SALES_TYPE_EXCLUSIVE],
            2 => [self::SALES_TYPE_EXCLUSIVE, self::SALES_TYPE_DUO],
            3 => [self::SALES_TYPE_EXCLUSIVE, self::SALES_TYPE_DUO, self::SALES_TYPE_TRIO],
            default => []
        };
    }
}
