<?php

namespace App\Services;

use App\Enums\Odin\StateAbbreviation;
use App\Models\Odin\Consumer;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use Exception;

class DelayService
{

    /**
     * @param CarbonTimeZone|null $timezone
     * @return array
     */
    public static function businessHours(?CarbonTimeZone $timezone = null): array
    {
        return [
            'start' => Carbon::parse(tz: $timezone)->setTime(hour: 9, minute: 0),
            'end' => Carbon::parse(tz: $timezone)->setTime(hour: 17, minute: 0),
        ];
    }


    /**
     * @param Consumer $consumer
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param bool $excludeWeekend
     * @return Carbon
     * @throws Exception
     */
    public function getNextConsumerContactTime(
        Consumer $consumer,
        Carbon $startDate,
        Carbon $endDate,
        bool $excludeWeekend = true,
    ): Carbon
    {
        $consumerProduct = $consumer->consumerProducts()->first();

        $timezone = StateAbbreviation::timeZone($consumerProduct->address?->stateLocation?->state_abbr ?? '');

        return $this->calculateEligibleRandomDelay(
            startDate: $startDate,
            endDate: $endDate,
            timeZone: new CarbonTimeZone($timezone),
            excludeWeekend: $excludeWeekend,
        );
    }


    /**
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @param CarbonTimeZone $timeZone
     * @param bool $excludeWeekend
     * @return Carbon
     */
    public function calculateEligibleRandomDelay(
        Carbon $startDate,
        Carbon $endDate,
        CarbonTimeZone $timeZone,
        bool $excludeWeekend = true,
    ): Carbon
    {
        $timezoneStart = Carbon::parse(tz: $timeZone)->setTime(hour: $startDate->hour, minute: $startDate->minute, second: $startDate->second);
        $timezoneEnd = Carbon::parse(tz: $timeZone)->setTime(hour: $endDate->hour, minute: $endDate->minute, second: $endDate->second);

        $delay = rand($timezoneStart->unix(), $timezoneEnd->unix());

        $delayInTimezone = Carbon::parse($delay)->setTimezone($timeZone);

        while(($excludeWeekend && $delayInTimezone->isWeekend()) || $delayInTimezone->isPast()) {
            $delayInTimezone->addDay();
        }

        return $delayInTimezone;
    }

}
