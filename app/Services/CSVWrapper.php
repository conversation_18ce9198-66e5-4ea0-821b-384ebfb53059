<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use League\Csv\Exception;
use League\Csv\Reader;
use League\Csv\Writer;
use League\Csv\Statement;
use Illuminate\Support\Collection;
use SplTempFileObject;

class CSVWrapper
{
    protected string $delimiter = ',';
    protected string $enclosure = '"';
    protected string $escape    = '\\';

    /**
     * @param string $delimiter
     * @return $this
     */
    public function setDelimiter(string $delimiter): self
    {
        $this->delimiter = $delimiter;
        return $this;
    }

    /**
     * @param string $enclosure
     * @return $this
     */
    public function setEnclosure(string $enclosure): self
    {
        $this->enclosure = $enclosure;
        return $this;
    }

    /**
     * @param string $escape
     * @return $this
     */
    public function setEscape(string $escape): self
    {
        $this->escape = $escape;
        return $this;
    }


    /**
     * @param string|UploadedFile $file
     * @param bool $withHeader
     * @return Collection
     * @throws Exception
     */
    public function read(string|UploadedFile $file, bool $withHeader = true): Collection
    {
        if ($file instanceof UploadedFile) {
            $stream = $file->getRealPath();
        } else {
            $stream = $file;
        }

        $csv = Reader::createFromPath($stream, 'r');
        $csv->setDelimiter($this->delimiter);
        $csv->setEnclosure($this->enclosure);
        $csv->setEscape($this->escape);

        if ($withHeader) {
            $csv->setHeaderOffset(0);
            $records = (new Statement())->process($csv);
            return collect(iterator_to_array($records));
        }

        return collect($csv->getRecords());
    }


    /**
     * @param string $path
     * @param array $headers
     * @param array $records
     * @return void
     * @throws Exception
     */
    public function write(
        string $path,
        array $records,
        ?array $headers = []
    ): void
    {
        if (empty($records)) {
            throw new Exception('Cannot export an empty csv');
        }
        if (empty($headers)) {
            $headers = array_keys($records[0]);
        }

        $csv = Writer::createFromPath($path, 'w+');
        $csv->setDelimiter($this->delimiter);
        $csv->setEnclosure($this->enclosure);
        $csv->setEscape($this->escape);

        $csv->insertOne($headers);
        $csv->insertAll($records);
    }

    /**
     * @param array $headers
     * @param array $records
     * @return string
     * @throws Exception
     */
    public function toString(array $headers, array $records): string
    {
        $csv = Writer::createFromFileObject(new SplTempFileObject());
        $csv->setDelimiter($this->delimiter);
        $csv->setEnclosure($this->enclosure);
        $csv->setEscape($this->escape);

        $csv->insertOne($headers);
        $csv->insertAll($records);

        return $csv->getContent();
    }
}
