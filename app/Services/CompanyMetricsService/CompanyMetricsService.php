<?php

namespace App\Services\CompanyMetricsService;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Repositories\CompanyMetricsRepository\CompanyMetricsRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class CompanyMetricsService
{

    public function __construct(
        protected CompanyMetricsRepository $companyMetricsRepository,
    )
    {}

    /**
     * @param int $companyId
     * @param CompanyMetricRequestTypes $type
     * @return CompanyMetric|null
     */
    public function getLatestCompanyMetric(int $companyId, CompanyMetricRequestTypes $type): ?CompanyMetric
    {
        return $this->companyMetricsRepository->getLatestCompanyMetric($companyId, $type);
    }

    /**
     * @param int $companyId
     * @param CompanyMetricRequestTypes $type
     * @return Collection
     */
    public function getCompanyMetrics(int $companyId, CompanyMetricRequestTypes $type): Collection
    {
        return $this->companyMetricsRepository->getCompanyMetrics($companyId, $type);
    }

    /**
     * @param Collection<CompanyMetric> $ppcSpendCollection
     * @return ?float
     */
    public function getAverageCompanyPpcSpend(Collection $ppcSpendCollection): ?float
    {
        return $ppcSpendCollection->avg(CompanyMetric::FIELD_REQUEST_RESPONSE .'.'. 'monthlySpend');
    }

    public function getPermitMetricsForCompany(Company $company, Carbon $startDate, ?Carbon $endDate = null): array
    {
        $metrics = [];

        CompanyMetric::query()
            ->where(CompanyMetric::FIELD_COMPANY_ID, $company->id)
            ->where(CompanyMetric::FIELD_REQUEST_TYPE, CompanyMetric::REQUEST_TYPE_PERMIT_METRICS)
            ->where(CompanyMetric::CREATED_AT, '>=', $startDate)
            ->when($endDate !== null, fn(Builder $query) => $query->where(CompanyMetric::CREATED_AT, '<=', $endDate))
            ->get()->each(function(CompanyMetric $companyMetric) use (&$metrics) {
                if (!$companyMetric->request_response) {
                    return;
                }

                $state = Str::lower(Arr::get($companyMetric->request_response, 'address.state', ''));
                $tagTally = Arr::get($companyMetric->request_response, 'tag_tally');

                if (!$state || !$tagTally) {
                    return;
                }

                foreach ($tagTally as $industry => $permits) {
                    Arr::set($metrics, "$industry.$state", Arr::get($metrics, "$industry.$state", 0) + $permits);
                }
            });

        return $metrics;
    }

}
