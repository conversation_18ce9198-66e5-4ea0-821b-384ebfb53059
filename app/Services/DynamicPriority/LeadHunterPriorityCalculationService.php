<?php

namespace App\Services\DynamicPriority;

use App\Contracts\DynamicPriorityCalculationServiceContract;
use App\Models\Legacy\EloquentQuote;
use App\Models\Sales\Task;
use App\Repositories\Legacy\QuoteRepository;

class LeadHunterPriorityCalculationService implements DynamicPriorityCalculationServiceContract
{
    const UNSOLD_MAX_MULTIPLIER = 10;
    const UNSOLD_MIN            = 0;
    const PRIORITY_MIN          = 1;
    const PRIORITY_MAX          = 100;

    protected int $unsoldDays = 7;

    public function __construct(protected QuoteRepository $quoteRepository)
    {
        $this->unsoldDays = config('app.dynamic_priority_calculation.unsold_days');
    }

    /**
     * @inheritDoc
     */
    public function calculate(Task $task, ?EloquentQuote $lead, ?int $unsoldLeadCount): ?int
    {
        if (!$lead || !$unsoldLeadCount) return null;
        return $this->calculatePriority($unsoldLeadCount);
    }

    /**
     * @param int $unsoldLeads
     *
     * @return int
     */
    protected function calculatePriority(int $unsoldLeads): int
    {
        $unsoldRange   = (self::UNSOLD_MAX_MULTIPLIER * $this->unsoldDays) - self::UNSOLD_MIN;
        $priorityRange = self::PRIORITY_MAX - self::PRIORITY_MIN;

        return ceil(((($unsoldLeads - self::UNSOLD_MIN) * $priorityRange) / $unsoldRange) + self::PRIORITY_MIN);
    }
}
