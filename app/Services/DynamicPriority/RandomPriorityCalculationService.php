<?php

namespace App\Services\DynamicPriority;

use App\Contracts\DynamicPriorityCalculationServiceContract;
use App\Models\Sales\Task;
use App\Models\Legacy\EloquentQuote;

class RandomPriorityCalculationService implements DynamicPriorityCalculationServiceContract
{

    /**
     * @param Task $task
     * @param EloquentQuote|null $lead
     * @param int|null $unsoldLeadCount
     * @inheritDoc
     */
    public function calculate(Task $task, ?EloquentQuote $lead, ?int $unsoldLeadCount): ?int
    {
        return random_int(1, 100);
    }
}
