<?php

namespace App\Services\Calendar\Google;

use App\DTO\Meet\ConferenceDTO;
use App\DTO\Meet\ConferenceParticipantDTO;
use App\DTO\Meet\ConferenceParticipantSessionDTO;
use App\DTO\Meet\ConferenceRecordingDTO;
use App\DTO\Meet\ConferenceTranscriptDTO;
use App\DTO\Meet\ConferenceTranscriptEntryDTO;
use App\Enums\GoogleServiceType;
use App\Models\GoogleUserToken;
use Exception;
use Google\Service\Meet;
use Google\Service\Meet\ConferenceRecord;
use Google\Service\Meet\ParticipantSession;
use Google\Service\Meet\TranscriptEntry;
use Illuminate\Support\Collection;
use \Google\Service\Exception as GoogleException;

class GoogleMeetProvider extends Google<PERSON>rovider
{
    protected array $scopes = [
        Meet::MEETINGS_SPACE_READONLY,
        Meet::MEETINGS_SPACE_CREATED,
    ];

    protected GoogleServiceType $service = GoogleServiceType::MEET;

    protected Meet $meetService;

    /**
     * @throws Exception
     */
    public function __construct(protected ?GoogleUserToken $token = null)
    {
        parent::__construct();
        if (filled($token)) {
            $this->googleClientService->setAccessToken($this->token);
        }
        $this->meetService = $this->googleClientService->getService(Meet::class);
    }

    /**
     * @param string $conferenceCode
     * @return Collection<ConferenceDTO>
     * @throws GoogleException
     */
    public function getConferenceRecords(
        string $conferenceCode
    ): Collection
    {
        $response = $this->meetService->conferenceRecords->listConferenceRecords([
            'filter' => "space.meeting_code:$conferenceCode"
        ]);

        return collect($response->getConferenceRecords())->map(fn(ConferenceRecord $item) => new ConferenceDTO(
            name      : $item->getName(),
            endTime   : $item->getEndTime(),
            expireTime: $item->getExpireTime(),
            space     : $item->getSpace(),
            startTime : $item->getStartTime(),
        ));
    }

    /**
     * @param string $conferenceName
     * @return Collection
     * @throws GoogleException
     */
    public function getConferenceTranscripts(string $conferenceName): Collection
    {
        $result = collect();
        $nextPageToken = null;

        do {
            $response = $this->meetService->conferenceRecords_transcripts->listConferenceRecordsTranscripts(
                parent   : $conferenceName,
                optParams: ['pageToken' => $nextPageToken]
            );

            $transcripts = collect($response->getTranscripts());

            $transcripts->each(function ($transcript) use ($result) {
                $entriesResult = $this->fetchTranscriptEntries($transcript->getName());

                $result->push(new ConferenceTranscriptDTO(
                    name                      : $transcript->getName(),
                    endTime                   : $transcript->getEndTime(),
                    startTime                 : $transcript->getStartTime(),
                    state                     : $transcript->getState(),
                    docsDestinationDocumentId : $transcript->getDocsDestination()?->getDocument(),
                    docsDestinationDocumentUrl: $transcript->getDocsDestination()?->getExportUri(),
                    entries                   : $entriesResult,
                ));
            });

            $nextPageToken = $response->getNextPageToken();
        } while ($nextPageToken);

        return $result;
    }

    /**
     * @param string $transcriptName
     * @return Collection
     * @throws GoogleException
     */
    public function fetchTranscriptEntries(string $transcriptName): Collection
    {
        $entries = collect();
        $nextPageToken = null;

        do {
            $entryResponse = $this->meetService->conferenceRecords_transcripts_entries
                ->listConferenceRecordsTranscriptsEntries(
                    parent   : $transcriptName,
                    optParams: ['pageToken' => $nextPageToken, 'pageSize' => 100]
                );

            $entries = $entries->merge(
                collect($entryResponse->getTranscriptEntries())->map(function (TranscriptEntry $transcriptEntry) {
                    return new ConferenceTranscriptEntryDTO(
                        name        : $transcriptEntry->getName(),
                        participant : $transcriptEntry->getParticipant(),
                        endTime     : $transcriptEntry->getEndTime(),
                        languageCode: $transcriptEntry->getLanguageCode(),
                        startTime   : $transcriptEntry->getStartTime(),
                        text        : $transcriptEntry->getText(),
                    );
                })
            );

            $nextPageToken = $entryResponse->getNextPageToken();

        } while ($nextPageToken);

        return $entries;
    }


    /**
     * @param string $conferenceName
     * @return Collection
     * @throws GoogleException
     */
    public function getConferenceRecordings(string $conferenceName): Collection
    {
        $result = collect();
        $nextPageToken = null;

        do {
            $response = $this->meetService->conferenceRecords_recordings->listConferenceRecordsRecordings(
                parent   : $conferenceName,
                optParams: [
                    'pageToken' => $nextPageToken
                ]
            );

            $recordings = collect($response->getRecordings());
            if ($recordings->isNotEmpty()) {
                $result = $result->merge(
                    $recordings->map(function (Meet\Recording $recording) {
                        return new ConferenceRecordingDTO(
                            name                     : $recording->getName(),
                            endTime                  : $recording->getEndTime(),
                            startTime                : $recording->getStartTime(),
                            state                    : $recording->getState(),
                            driveDestinationFile     : $recording->getDriveDestination()?->getFile(),
                            driveDestinationExportUri: $recording->getDriveDestination()?->getExportUri()
                        );
                    })
                );
            }

            $nextPageToken = $response->getNextPageToken();
        } while ($nextPageToken);

        return $result;
    }

    /**
     * @param string $conferenceName
     * @return Collection
     * @throws GoogleException
     */
    public function getConferenceParticipants(string $conferenceName): Collection
    {
        $result = collect();
        $nextPageToken = null;

        do {
            $response = $this->meetService->conferenceRecords_participants->listConferenceRecordsParticipants(
                parent   : $conferenceName,
                optParams: ['pageToken' => $nextPageToken]
            );

            $participants = collect($response->getParticipants());
            $participants->each(function ($participant) use ($result) {
                $sessionsResult = $this->fetchParticipantSessions($participant->getName());

                $displayName = $participant->getSignedinUser()?->getDisplayName()
                    ?? $participant->getAnonymousUser()?->getDisplayName()
                    ?? $participant->getPhoneUser()?->getDisplayName();

                $result->push(new ConferenceParticipantDTO(
                    name             : $participant->getName(),
                    earliestStartTime: $participant->getEarliestStartTime(),
                    latestEndTime    : $participant->getLatestEndTime(),
                    userDisplayName  : $displayName,
                    sessions         : $sessionsResult
                ));
            });

            $nextPageToken = $response->getNextPageToken();

        } while ($nextPageToken);

        return $result;
    }

    /**
     * @param string $participantName
     * @return Collection
     */
    public function fetchParticipantSessions(string $participantName): Collection
    {
        $sessions = collect();
        $nextPageToken = null;

        do {
            try {
                $sessionResponse = $this->meetService->conferenceRecords_participants_participantSessions
                    ->listConferenceRecordsParticipantsParticipantSessions(
                        parent   : $participantName,
                        optParams: ['pageToken' => $nextPageToken]
                    );

                $sessions = $sessions->merge(
                    collect($sessionResponse->getParticipantSessions())->map(function (ParticipantSession $participantSession) {
                        return new ConferenceParticipantSessionDTO(
                            name     : $participantSession->getName(),
                            endTime  : $participantSession->getEndTime(),
                            startTime: $participantSession->getStartTime(),
                        );
                    })
                );

                $nextPageToken = $sessionResponse->getNextPageToken();
            } catch (Exception $e) {
                logger()->error('Error fetching participant sessions', ['error' => $e->getMessage()]);
                break;
            }
        } while ($nextPageToken);

        return $sessions;
    }
}
