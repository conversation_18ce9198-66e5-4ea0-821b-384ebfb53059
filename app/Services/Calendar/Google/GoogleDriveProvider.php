<?php

namespace App\Services\Calendar\Google;

use App\Enums\GoogleServiceType;
use App\Models\GoogleUserToken;
use Exception;
use Google\Service\Drive;
use \Google\Service\Exception as GoogleException;

class GoogleDriveProvider extends GoogleProvider
{
    protected array $scopes = [
        Drive::DRIVE_FILE,
    ];

    protected GoogleServiceType $service = GoogleServiceType::DRIVE;

    protected Drive $driveService;

    /**
     * @throws Exception
     */
    public function __construct(protected ?GoogleUserToken $token = null)
    {
        parent::__construct();
        if (filled($token)) {
            $this->googleClientService->setAccessToken($this->token);
        }
        $this->driveService = $this->googleClientService->getService(Drive::class);
    }

    /**
     * @param string $fileId
     * @return bool
     * @throws GoogleException
     */
    public function deleteFile(
        string $fileId
    ): bool
    {
        $driveFile = new Drive\DriveFile();

        $driveFile->setTrashed(true);

        $this->driveService->files->update(
            fileId  : $fileId,
            postBody: $driveFile
        );

        return true;
    }
}
