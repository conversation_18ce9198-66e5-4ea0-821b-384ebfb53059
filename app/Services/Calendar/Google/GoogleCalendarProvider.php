<?php

namespace App\Services\Calendar\Google;

use App\DTO\Calendar\CalendarDTO;
use App\DTO\Calendar\CalendarEventAttendeeDTO;
use App\DTO\Calendar\CalendarEventDTO;
use App\DTO\Calendar\CalendarEventsResponse;
use App\Enums\GoogleServiceType;
use App\Jobs\Calendar\SetupUserCalendarEventListener;
use App\Models\GoogleUserToken;
use Carbon\Carbon;
use Exception;
use Google\Service\Calendar;
use Google\Service\Calendar\Channel;
use Google\Service\Exception as GoogleException;

class GoogleCalendarProvider extends GoogleProvider
{
    const string PRIMARY_CALENDAR_ID = 'primary';

    protected array $scopes = [
        Calendar::CALENDAR_READONLY,
        Calendar::CALENDAR_EVENTS_READONLY,
    ];

    protected GoogleServiceType $service = GoogleServiceType::CALENDAR;

    protected Calendar $calendarService;

    protected GoogleRecurrenceRuleParser $googleRecurrenceRuleParser;

    /**
     * @throws Exception
     */
    public function __construct(protected ?GoogleUserToken $token = null)
    {
        parent::__construct();
        if (filled($token)) {
            $this->googleClientService->setAccessToken($this->token);
        }
        $this->calendarService = $this->googleClientService->getService(Calendar::class);
        $this->googleRecurrenceRuleParser = new GoogleRecurrenceRuleParser();
    }

    /**
     * @throws Exception
     */
    public function postUserHasGivenAccess(GoogleUserToken $token): void
    {
        SetupUserCalendarEventListener::dispatchSync($token);
    }

    /**
     * @param string $externalCalendarId
     * @return array
     * @throws GoogleException
     */
    public function setupWatch(
        string $externalCalendarId = self::PRIMARY_CALENDAR_ID
    ): array
    {
        $res = $this->calendarService->events->watch(
            calendarId: $externalCalendarId,
            postBody  : $this->getWebhookChannel()
        );

        return [
            'expires_at'  => Carbon::parse($res->getExpiration() / 1000)->utc()->toISOString(),
            'resource_id' => $res->getResourceId(),
        ];
    }

    /**
     * @return Channel
     */
    protected function getWebhookChannel(): Channel
    {
        $channel = new Channel();

        $channel->setId($this->token->user_id);
        $channel->setType('web_hook');
        $channel->setAddress(route('calendar-event-handler'));

        return $channel;
    }

    /**
     *
     * https://developers.google.com/calendar/api/v3/reference/events/list
     * @param string|null $lastSyncToken
     * @param string|null $pageToken
     * @param string|null $calendarId
     * @param int|null $maxResults
     * @param bool|null $showDeleted
     * @param string|null $timeMin
     * @return CalendarEventsResponse
     * @throws GoogleException
     */
    public function listEvents(
        ?string $lastSyncToken = null,
        ?string $pageToken = null,
        ?string $calendarId = self::PRIMARY_CALENDAR_ID,
        ?int $maxResults = 250,
        ?bool $showDeleted = true,
        ?string $timeMin = null
    ): CalendarEventsResponse
    {
        $response = $this->calendarService->events->listEvents(
            calendarId: $calendarId,
            optParams : [
                'syncToken'   => $lastSyncToken,
                'pageToken'   => $pageToken,
                'maxResults'  => $maxResults,
                'showDeleted' => $showDeleted,
                'timeMin'     => $timeMin
            ]
        );

        $events = collect($response->getItems())->map(fn(Calendar\Event $event) => new CalendarEventDTO(
            id            : $event->getId(),
            summary       : $event->getSummary(),
            description   : $event->getDescription(),
            location      : $event->getLocation(),
            start         : Carbon::parse($event->getStart()->getDateTime() ?? $event->getStart()->getDate())->utc()->toISOString(),
            end           : Carbon::parse($event->getEnd()->getDateTime() ?? $event->getEnd()->getDate())->utc()->toISOString(),
            status        : $event->getStatus(),
            timezone      : $event->getStart()?->getTimeZone() ?? $event->getEnd()?->getTimeZone(),
            attendees     : collect($event->getAttendees())->map(fn(Calendar\EventAttendee $item) => new CalendarEventAttendeeDTO(
                comment       : $item->getComment(),
                displayName   : $item->getDisplayName(),
                email         : $item->getEmail(),
                responseStatus: $item->getResponseStatus(),
                isOrganizer   : $item->getOrganizer(),
            )),
            createdAt     : $event->getCreated(),
            meetingUrl    : $event->getHangoutLink(),
            recurrenceRule: collect($event->getRecurrence())->filter()->first(),
            recurrenceData: $this->googleRecurrenceRuleParser->parse(collect($event->getRecurrence())->filter()->first()),
        ));

        return new CalendarEventsResponse(
            nextSyncToken: $response->getNextSyncToken(),
            nextPageToken: $response->getNextPageToken(),
            timeZone     : $response->getTimeZone(),
            summary      : $response->getSummary(),
            events       : $events
        );
    }

    /**
     * @param string $calendarId
     * @return CalendarDTO
     * @throws GoogleException
     */
    public function getPrimaryCalendar(string $calendarId = self::PRIMARY_CALENDAR_ID): CalendarDTO
    {
        $response = $this->calendarService->calendars->get(
            calendarId: $calendarId
        );

        return new CalendarDTO(
            id         : $response->getId(),
            summary    : $response->getSummary(),
            description: $response->getDescription(),
            timeZone   : $response->getTimeZone(),
        );
    }

    /**
     * @param string $resourceId
     * @return void
     * @throws GoogleException
     */
    public function cancelWatch(string $resourceId): void
    {
        $channel = $this->getWebhookChannel();

        $channel->setResourceId($resourceId);

        $this->calendarService->channels->stop(
            $channel
        );
    }
}
