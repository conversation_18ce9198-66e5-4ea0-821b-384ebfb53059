<?php

namespace App\Services\Calendar\Google;

use App\Enums\GoogleServiceType;
use App\Models\GoogleUserToken;
use Exception;

class GoogleServiceProviderFactory
{
    /**
     * @param GoogleServiceType $type
     * @param GoogleUserToken|null $token
     * @return GoogleProvider
     * @throws Exception
     */
    static function make(
        GoogleServiceType $type,
        ?GoogleUserToken $token = null
    ): GoogleProvider
    {
        return match ($type) {
            GoogleServiceType::CALENDAR => new GoogleCalendarProvider($token),
            GoogleServiceType::MEET     => new GoogleMeetProvider($token),
            GoogleServiceType::DRIVE    => new GoogleDriveProvider($token),
            default                     => throw new Exception("Google service $type->value not supported.")
        };
    }
}
