<?php

namespace App\Services\Calendar\Google;

use App\Enums\GoogleServiceType;
use App\Models\GoogleUserToken;
use App\Services\Google\GoogleClientService;
use Exception;

abstract class GoogleProvider
{
    protected GoogleClientService $googleClientService;

    protected GoogleServiceType $service;
    protected array             $scopes;

    /**
     * @throws Exception
     */
    public function __construct()
    {
        $this->googleClientService = new GoogleClientService();
    }

    /**
     * @param int $userId
     * @return string
     */
    public function generateOAuthConsentScreenUrl(int $userId): string
    {
        return $this->googleClientService->generateOAuthConsentScreenUrl(
            userId : $userId,
            service: $this->service->value,
            scopes : $this->scopes,
        );
    }

    public function postUserHasGivenAccess(GoogleUserToken $token)
    {

    }
}
