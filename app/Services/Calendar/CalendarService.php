<?php

namespace App\Services\Calendar;

use App\DTO\Calendar\CalendarEventAttendeeDTO;
use App\DTO\Calendar\CalendarEventDTO;
use App\Enums\AppFeature;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Enums\GoogleServiceType;
use App\Events\Calendar\CalendarEventSaved;
use App\Exceptions\Calendar\CalendarSyncTokenExpiredException;
use App\Models\Calendar\Calendar;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\UserCalendarListener;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\GoogleUserToken;
use App\Repositories\Calendar\CalendarEventAttendeeRepository;
use App\Repositories\Calendar\CalendarEventRepository;
use App\Repositories\Calendar\CalendarRepository;
use App\Services\AppLogger;
use App\Services\Calendar\Google\GoogleCalendarProvider;
use App\Services\Calendar\Google\GoogleServiceProviderFactory;
use App\Services\ContactIdentification\ContactIdentificationService;
use Carbon\Carbon;
use Exception;
use Google\Service\Exception as GoogleException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CalendarService
{
    protected AppLogger $appLogger;

    public function __construct(
        protected CalendarEventRepository $calendarEventRepository,
        protected CalendarEventAttendeeRepository $calendarEventAttendeeRepository,
        protected ContactIdentificationService $contactIdentificationService
    )
    {
        $this->appLogger = AppLogger::make(
            feature: AppFeature::CALENDAR
        );
    }

    /**
     * @throws Exception
     */
    public function importLatestEvents(
        Calendar $calendar,
        ?Carbon $timeMin = null
    ): void
    {
        try {
            $token = GoogleUserToken::query()
                ->where(GoogleUserToken::FIELD_USER_ID, $calendar->user_id)
                ->where(GoogleUserToken::FIELD_SERVICE, GoogleServiceType::CALENDAR->value)
                ->first();

            if (empty($token)) {
                $message = "Token not found for calendar id $calendar->id";

                $this->appLogger->error(
                    message  : $message,
                    relations: [$calendar]
                );
                throw new Exception(
                    $message
                );
            }

            $calendarProvider = new GoogleCalendarProvider($token);

            $nextPageToken = null;

            $hasRestrictions = (bool)$timeMin?->toIso8601String();

            do {
                // Sync token cannot be used with other request restrictions
                $response = $calendarProvider->listEvents(
                    lastSyncToken: !$hasRestrictions ? $calendar->{Calendar::FIELD_SYNC_TOKEN} : null,
                    pageToken    : $nextPageToken,
                    timeMin      : $timeMin?->toIso8601String()
                );

                foreach ($response->getEvents() as $event) {
                    try {
                        $canImport = $this->checkIfEventIsValidForImport($event);

                        if ($canImport) {
                            $this->syncEvent(
                                calendarId   : $calendar->id,
                                userId       : $calendar->user_id,
                                calendarEvent: $event,
                            );
                        }
                    } catch (Exception $exception) {
                        $this->appLogger->exception(
                            exception: $exception,
                            message  : 'Error listing syncing event',
                            context  : [
                                'external_event_id' => $event->getId()
                            ],
                            relations: [$calendar]
                        );
                    }
                }

                $nextPageToken = $response->getNextPageToken();
            } while ($nextPageToken);

            $calendar->update([
                Calendar::FIELD_SYNC_TOKEN => $response->getNextSyncToken()
            ]);
        } catch (GoogleException $exception) {
            match ($exception->getCode()) {
                410     => throw new CalendarSyncTokenExpiredException(
                    calendarId: $calendar->id,
                    previous  : $exception
                ),
                default => throw $exception
            };
        }
    }

    /**
     * @param CalendarEventDTO $calendarEvent
     * @return bool
     */
    public function checkIfEventIsValidForImport(CalendarEventDTO $calendarEvent): bool
    {
        return $calendarEvent->getAttendees()->isNotEmpty() && filled($calendarEvent->getSummary());
    }

    /**
     * @param int $calendarId
     * @param int $userId
     * @param CalendarEventDTO $calendarEvent
     * @return void
     */
    public function syncEvent(
        int $calendarId,
        int $userId,
        CalendarEventDTO $calendarEvent
    ): void
    {
        $storedEvent = $this->calendarEventRepository->updateOrCreate(
            calendarId    : $calendarId,
            userId        : $userId,
            externalId    : $calendarEvent->getId(),
            title         : $calendarEvent->getSummary(),
            status        : $calendarEvent->getStatus(),
            recurrenceRule: $calendarEvent->getRecurrenceRule(),
            recurrenceData: $calendarEvent->getRecurrenceData()?->toArray(),
            startTime     : $calendarEvent->getStart(),
            conferenceUrl : $calendarEvent->getMeetingUrl(),
            description   : $calendarEvent->getDescription(),
            endTime       : $calendarEvent->getEnd(),
            timezone      : $calendarEvent->getTimezone(),
            location      : $calendarEvent->getLocation()
        );

        $this->syncEventAttendees(
            calendarEvent: $storedEvent,
            attendees    : $calendarEvent->getAttendees()
        );

        CalendarEventSaved::dispatch($storedEvent->id);
    }


    /**
     * @param CalendarEvent $calendarEvent
     * @param Collection<CalendarEventAttendeeDTO> $attendees
     * @return Collection
     */
    public function syncEventAttendees(
        CalendarEvent $calendarEvent,
        Collection $attendees
    ): Collection
    {
        $storedParticipants = collect();

        foreach ($attendees as $attendee) {
            try {
                $contact = $this->contactIdentificationService->createIdentifiedContactAndDispatchJob(
                    identifierValue: $attendee->getEmail(),
                    type           : SearchableFieldType::EMAIL,
                );

                $storedParticipant = $this->calendarEventAttendeeRepository->updateOrCreate(
                    calendarEventId    : $calendarEvent->id,
                    email              : $attendee->getEmail(),
                    status             : $attendee->getResponseStatus(),
                    identifiedContactId: $contact->{IdentifiedContact::FIELD_ID},
                    name               : $attendee->getDisplayName(),
                    isOrganizer        : $attendee->getIsOrganizer(),
                );

                $storedParticipants->push($storedParticipant);
            } catch (Exception $exception) {
                $this->appLogger->exception(
                    exception: $exception,
                    context  : [
                        'attendee_email' => $attendee->getEmail()
                    ],
                    relations: [$calendarEvent],
                );
            }
        }

        return $storedParticipants;
    }

    /**
     * @param GoogleUserToken $token
     * @return void
     * @throws GoogleException|Exception
     */
    public function setupWatch(GoogleUserToken $token): void
    {
        $calendarProvider = GoogleServiceProviderFactory::make($token->service, $token);

        try {
            $userCalendarListener = UserCalendarListener::query()
                ->where(UserCalendarListener::FIELD_USER_ID, $token->user_id)
                ->first();

            if ($userCalendarListener) {
                $calendarProvider->cancelWatch($userCalendarListener->{UserCalendarListener::FIELD_EXTERNAL_RESOURCE_ID});

                UserCalendarListener::query()
                    ->where(UserCalendarListener::FIELD_EXTERNAL_RESOURCE_ID, $userCalendarListener->{UserCalendarListener::FIELD_EXTERNAL_RESOURCE_ID})
                    ->delete();
            }
        } catch (Exception $exception) {
            $this->appLogger->exception(
                exception: $exception,
                message  : 'Failed to remove calendar event listener',
                relations: [$token]
            );
        }

        try {
            $response = $calendarProvider->setupWatch();

            UserCalendarListener::query()->create(
                [
                    UserCalendarListener::FIELD_USER_ID              => $token->user_id,
                    UserCalendarListener::FIELD_EXPIRES_AT           => Arr::get($response, 'expires_at'),
                    UserCalendarListener::FIELD_EXTERNAL_RESOURCE_ID => Arr::get($response, 'resource_id'),
                ]
            );
        } catch (Exception $exception) {
            $this->appLogger->exception(
                exception: $exception,
                message  : 'Failed to setup calendar event listener',
                relations: [$token]
            );
        }

        $calendar = $calendarProvider->getPrimaryCalendar();

        // Skip full sync and get the most recent sync token
        $nextPageToken = null;
        do {
            $response = $calendarProvider->listEvents(
                pageToken: $nextPageToken,
            );

            $nextPageToken = $response->getNextPageToken();
        } while (empty($response->getNextSyncToken()));

        $repo = new CalendarRepository();

        $repo->updateOrCreate(
            userId    : $token->user_id,
            externalId: $calendar->getId(),
            name      : 'Primary',
            timezone  : $calendar->getTimeZone(),
            syncToken : $response->getNextSyncToken(),
        );
    }
}
