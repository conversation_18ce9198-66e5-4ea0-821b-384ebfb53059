<?php

namespace App\Services\Affiliate;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\PayoutStrategy;
use App\Repositories\Affiliate\AffiliateRepository;

class AffiliateService
{
    public function __construct(
        protected AffiliateRepository   $repository,
        protected PayoutStrategyService $payoutStrategyService,
    )
    {
    }

    /**
     * Creates Affiliate and Payment Strategy
     *
     * @param string $name
     * @param PayoutStrategyTypeEnum $type
     * @param int $value
     * @param int|null $authorId
     * @return Affiliate
     */
    public function initialiseAffiliate(
        string                 $name,
        PayoutStrategyTypeEnum $type,
        int                    $value,
        ?int                   $authorId = null,
    ): Affiliate
    {
        $affiliate = $this->repository->create(name: $name);

        $this->payoutStrategyService->create(
            affiliateId: $affiliate->id,
            type: $type,
            value: $value,
            activeFrom: now(),
            authorId: $authorId,
        );

        return $affiliate;
    }

    public function updateAffiliate(
        Affiliate              $affiliate,
        string                 $name,
        PayoutStrategyTypeEnum $type,
        int                    $value,
    ): void
    {
        $this->repository->update(
            affiliate: $affiliate,
            name: $name,
        );

        $strategy = $affiliate->strategy;

        $this->payoutStrategyService->update(
            strategy: $strategy,
            type: $type,
            value: $value,
        );
    }

}
