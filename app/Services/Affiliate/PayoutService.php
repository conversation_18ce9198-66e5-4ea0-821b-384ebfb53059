<?php

namespace App\Services\Affiliate;

use App\Affiliates\PayoutStrategyType;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Affiliates\Payout;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Affiliate\PayoutRepository;

class PayoutService
{
    public function __construct(
        protected PayoutRepository $repository
    )
    {

    }

    /**
     * @param int $atomicValue
     * @param int $payoutStrategyId
     * @param int $affiliateId
     * @param int $consumerProductId
     * @return Payout
     */
    public function updateOrCreate(
        int $atomicValue,
        int $payoutStrategyId,
        int $affiliateId,
        int $consumerProductId,
    ): Payout
    {
        return $this->repository->updateOrCreate(
            atomicValue: $atomicValue,
            payoutStrategyId: $payoutStrategyId,
            affiliateId: $affiliateId,
            consumerProductId: $consumerProductId,
        );
    }

    /**
     * @param PayoutStrategy $strategy
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public function calculate(
        PayoutStrategy  $strategy,
        ConsumerProduct $consumerProduct,
    ): void
    {
        $strategyClass = $strategy->type->getClass();

        $atomicPayout = $strategyClass->calculate($consumerProduct, $strategy);

        $this->updateOrCreate(
            atomicValue: $atomicPayout,
            payoutStrategyId: $strategy->id,
            affiliateId: $strategy->affiliate_id,
            consumerProductId: $consumerProduct->id,
        );
    }

    /**
     * @param Payout $payout
     * @return void
     */
    public function refresh(Payout $payout): void
    {
        $this->calculate(
            strategy: $payout->strategy,
            consumerProduct: $payout->consumerProduct,
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public function updateIfExists(ConsumerProduct $consumerProduct): void
    {
        if ($consumerProduct->affiliatePayout()->exists()) {
            $payout = $consumerProduct->affiliatePayout;

            $this->refresh(
                payout: $payout,
            );
        }
    }

}
