<?php

namespace App\Services\Affiliate;

use App\Enums\ActivityLog\ActivityLogDescription;
use App\Enums\ActivityLog\ActivityLogName;
use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\PayoutStrategy;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\Affiliate\PayoutStrategyRepository;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class PayoutStrategyService
{
    public function __construct(
        protected PayoutStrategyRepository $repository,
        protected ActivityLogRepository    $activityLogRepository,
    )
    {
    }

    /**
     * @param int $affiliateId
     * @return PayoutStrategy|null
     */
    public function getActiveStrategyByAffiliateId(int $affiliateId): ?PayoutStrategy
    {
        return $this->repository->getActiveByAffiliateId(affiliateId: $affiliateId);
    }

    /**
     * @param int $affiliateId
     * @param PayoutStrategyTypeEnum $type
     * @param int $value
     * @param Carbon $activeFrom
     * @param Carbon|null $activeTo
     * @param int|null $authorId
     * @return PayoutStrategy
     */
    public function create(
        int                    $affiliateId,
        PayoutStrategyTypeEnum $type,
        int                    $value,
        Carbon                 $activeFrom,
        ?Carbon                $activeTo = null,
        ?int                   $authorId = null,
    ): PayoutStrategy
    {
        return $this->repository->create(
            affiliateId: $affiliateId,
            type: $type,
            value: $value,
            activeFrom: $activeFrom,
            authorId: $authorId,
            activeTo: $activeTo,
        );
    }

    public function update(
        PayoutStrategy         $strategy,
        PayoutStrategyTypeEnum $type,
        int                    $value,
    ): void
    {
        $newAttributes = [
            PayoutStrategy::FIELD_TYPE => $type,
            PayoutStrategy::FIELD_VALUE => $value,
        ];

        $strategy->fill($newAttributes);

        $difference = $strategy->isDirty();

        if ($difference) {
            $dirtyKeys = array_keys($strategy->getDirty());

            $original = $strategy->getRawOriginal();

            $this->activityLogRepository->createActivityLog(
                logName: ActivityLogName::AFFILIATE_PAYOUT_STRATEGY_UPDATE->value,
                description: ActivityLogDescription::USER_UPDATED->value,
                subjectType: $strategy::class,
                subjectId: $strategy->id,
                properties: [
                    'old' => collect($dirtyKeys)->mapWithKeys(fn ($key) => [$key => $original[$key]])->toArray(),
                    'new' => $strategy->getDirty(),
                ],
            );

            $userId = Auth::user()->id;

            $this->create(
                affiliateId: $strategy->affiliate_id,
                type: $strategy->type,
                value: $strategy->value,
                activeFrom: now(),
                authorId: $userId,
            );

            $this->repository->update(
                strategy: $strategy->refresh(),
                activeTo: now(),
            );
        }
    }

}
