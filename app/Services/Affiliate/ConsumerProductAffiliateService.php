<?php

namespace App\Services\Affiliate;

use App\Repositories\ConsumerProductAffiliates\ConsumerProductAffiliatesRepository;
use Illuminate\Database\Eloquent\Builder;

class ConsumerProductAffiliateService
{
    public function __construct(
        protected ConsumerProductAffiliatesRepository $affiliatesRepository
    )
    {
    }

    public function getConsumerProductAffiliateQuery(
        ?int $affiliateId = null,
        ?int $campaignId = null,
        ?int $timestampFrom = null,
        ?int $timestampTo = null,
        ?string $consumerName = null,
    ): Builder
    {
        return $this->affiliatesRepository->getConsumerProductAffiliateQuery(
            affiliateId: $affiliateId,
            campaignId: $campaignId,
            timestampFrom: $timestampFrom,
            timestampTo: $timestampTo,
            consumerName: $consumerName,
        );
    }

}
