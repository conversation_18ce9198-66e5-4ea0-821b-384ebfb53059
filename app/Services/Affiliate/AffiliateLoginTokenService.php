<?php

namespace App\Services\Affiliate;

use App\Enums\ActivityLog\ActivityLogDescription;
use App\Enums\ActivityLog\ActivityLogName;
use App\Models\Affiliates\Affiliate;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use Firebase\JWT\JWT;

class AffiliateLoginTokenService
{
    public function __construct(
        protected ActivityLogRepository $activityLogRepository
    )
    {
    }

    const string JWT_ALGORITHM              = 'HS256';
    const string FIELD_AFFILIATE_NUMERIC_ID = 'affiliate_numeric_id';
    const string FIELD_AFFILIATE_UUID       = 'affiliate_uuid';
    const string FIELD_SHADOW_ID            = 'shadow_id';
    const string FIELD_EXPIRY               = 'expiry';

    public function generateToken(
        Affiliate $affiliate,
        ?User     $shadow = null,
        ?int      $expiryInMinutes = 30,
    ): string
    {
        $this->activityLogRepository->createActivityLog(
            logName: ActivityLogName::AFFILIATE_SHADOW->value,
            description: ActivityLogDescription::USER_REQUESTED->value,
            subjectType: Affiliate::class,
            subjectId: $affiliate->id,
            properties: [
                'shadow_id' => $shadow?->id,
            ],
        );

        $signingKey = config('dashboard.jwt.signing_key');

        $payload = [
            self::FIELD_AFFILIATE_NUMERIC_ID => $affiliate->id,
            self::FIELD_AFFILIATE_UUID       => $affiliate->uuid,
            self::FIELD_SHADOW_ID            => $shadow?->id,
            self::FIELD_EXPIRY               => now()->addMinutes($expiryInMinutes)
        ];

        return JWT::encode($payload, $signingKey, self::JWT_ALGORITHM);
    }
}