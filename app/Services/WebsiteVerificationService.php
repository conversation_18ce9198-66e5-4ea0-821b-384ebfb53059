<?php

namespace App\Services;

use App\Models\Odin\Company;
use Exception;
use Illuminate\Support\Facades\Http;

class WebsiteVerificationService
{
    public function __construct(private string $url)
    {
    }

    /**
     * This function is used to validate a URL based on domain prefix and http protocol
     *
     * @return string|null
     */
    public function validateWebsiteUrl(): string|null
    {
        $url = $this->pingUrl($this->url);
        // When Initial request return null try by prefixing www
        if (!$url && !str_starts_with(parse_url($this->url, PHP_URL_HOST), 'www')) {
            $this->url = 'www' . '.' . parse_url($this->url, PHP_URL_HOST);
            return $this->pingUrl($this->url);
        }
        // Try manually to get https domain otherwise return previous http
        if (parse_url($url, PHP_URL_SCHEME) == 'http') {
            return $this->pingUrl('https://' . parse_url($url, PHP_URL_HOST)) ?: $url;
        }
        return $url;
    }

    /**
     * This function is used to ping a URL
     *
     * @param string $url
     * @return string|null
     */
    private function pingUrl(string $url): string|null
    {
        try {
            $response = Http::timeout(2)->get($url);
            return $response->transferStats->getHandlerStats()['url'];
        } catch (Exception $e) {
            logger()->error("Error Couldn't reached url: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Verify Company website
     * @param Company $company
     * @return array
     */
    public function verifyCompanyRecords(Company $company): array
    {
        return [
            Company::FIELD_ID => $company->{Company::FIELD_ID},
            Company::FIELD_WEBSITE_VERIFIED_URL => $this->validateWebsiteUrl(),
            Company::FIELD_WEBSITE_VERIFIED_AT => now()
        ];
    }
}
