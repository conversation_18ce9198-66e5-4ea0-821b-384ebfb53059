<?php

namespace App\Services;

use App\Models\Legacy\Location;
use App\Repositories\Legacy\CompanyRepository;
use App\Repositories\LocationRepository;
use App\Repositories\TaskRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class NonPurchasingServiceAreaService
{
    protected TaskRepository $taskRepository;

    protected CompanyRepository $companyRepository;

    protected CompanyFilteringService $companyFilteringService;

    protected LocationRepository $locationRepository;

    public function __construct(
        TaskRepository $taskRepository,
        CompanyRepository $companyRepository,
        CompanyFilteringService $companyFilteringService,
        LocationRepository $locationRepository
    ) {
        $this->taskRepository = $taskRepository;
        $this->companyRepository = $companyRepository;
        $this->companyFilteringService = $companyFilteringService;
        $this->locationRepository = $locationRepository;
    }

    public function getCompanyCountInCounty(string $countyKey, string $stateKey, string $industry): ?int
    {
        try {
            $countyLocation = $this->locationRepository->getCounty($stateKey, $countyKey);
            if ($countyLocation instanceof Location) {
                $query = $this->companyRepository->getCompaniesAgainstNonPurchasingLocationQuery($countyLocation, $industry, true);

                return $query->count();
            }
        } catch (ModelNotFoundException $exception) {
            throw new ModelNotFoundException("There was an issue with building the query to get non-purchasing location companies. Exception Message: {$exception->getMessage()}");
        }

        return null;
    }

    public function getCompaniesInCounty(string $countyKey, string $stateKey, string $industry, bool $filterByAccountManager,
        Request $request, ?int $pagination = null): Collection|LengthAwarePaginator
    {
        try {
            $countyLocation = $this->locationRepository->getCounty($stateKey, $countyKey);
            if ($countyLocation instanceof Location) {
                $query = $this->companyRepository->getCompaniesAgainstNonPurchasingLocationQuery($countyLocation, $industry, $filterByAccountManager);

                return $this->getCompaniesWithAttributes($query, $request, $pagination);
            }
        } catch (ModelNotFoundException $exception) {
            throw new ModelNotFoundException("There was an issue with building the query to get non-purchasing location companies. Exception Message: {$exception->getMessage()}");
        }

        return collect();
    }

    private function getCompaniesWithAttributes(Builder $query, Request $request, ?int $pagination = null): Collection|LengthAwarePaginator
    {
        $requestVariables = $this->companyFilteringService->getAmountOfPurchasedLeadsFilterRequestVariables();

        return $this->companyFilteringService->appendAttributes($query, $requestVariables, null, $request, $pagination);
    }
}
