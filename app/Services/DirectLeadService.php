<?php

namespace App\Services;

use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\OptInCompany;
use Exception;
use Illuminate\Support\Collection;

class DirectLeadService
{

    /**
     * @param ConsumerProduct $consumerProduct
     * @return bool
     */
    public static function shouldBeHandledAsDirectLead(ConsumerProduct $consumerProduct): bool
    {
        $industryId = $consumerProduct->industryService?->industry_id;
        if(!$industryId)
            return false;

        $directLeadIndustryIds = config('sales.direct_lead_enabled_industry_ids');
        return in_array($industryId, $directLeadIndustryIds);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Collection $campaigns
     * @return Collection
     */
    public static function getCampaignsWithOptIns(ConsumerProduct $consumerProduct, Collection $campaigns): Collection
    {
        $optedInCampaignIds = self::getOptInCampaignIds($consumerProduct);
        return $campaigns->filter(fn($campaign) => in_array($campaign->id, $optedInCampaignIds));
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Collection<ProposedProductAssignment> $proposedAssignments
     * @return void
     * @throws Exception
     */
    public static function assertProposedAssignmentsHaveOptIns(ConsumerProduct $consumerProduct, Collection $proposedAssignments): void
    {
        $optedInCampaignIds = self::getOptInCampaignIds($consumerProduct);
        $proposedAssignments->each(function(ProposedProductAssignment $assignment)use($optedInCampaignIds){
            if(!in_array($assignment->campaignId, $optedInCampaignIds))
                throw new Exception("Attempt to allocate without explicit campaign opt-in");
        });
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return array
     */
    private static function getOptInCampaignIds(ConsumerProduct $consumerProduct): array
    {
        return $consumerProduct->optInCompanies->map(fn(OptInCompany $optInCompany) => $optInCompany->company_campaign_id)->toArray();
    }

    /**
     * @param int $consumerProductId
     * @param int $budgetId
     * @return int|null
     */
    public static function getCompanyOptInByBudget(int $consumerProductId, int $budgetId): ?int
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::find($consumerProductId);
        $consumerOptIns = $consumerProduct->optInCompanies;
        if($consumerOptIns->count() === 0)
            return null;

        /** @var Budget $budget */
        $budget = Budget::find($budgetId);
        $campaignId = $budget->budgetContainer->company_campaign_id;

        $optInCompany = $consumerOptIns->filter(fn(OptInCompany $optIn) => $optIn->company_campaign_id === $campaignId);
        if($optInCompany->count() > 0){
            return $optInCompany->first()->id;
        }else{
            return null;
        }
    }
}
