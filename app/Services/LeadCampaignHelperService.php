<?php

namespace App\Services;

use App\Enums\Odin\Product;
use App\Models\Legacy\LeadCampaign;
use App\Repositories\HistoricalCompanyRejectionPercentageRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use Carbon\Carbon;

class LeadCampaignHelperService
{
    /** @var HistoricalCompanyRejectionPercentageRepository $rejectionRepository */
    protected HistoricalCompanyRejectionPercentageRepository $rejectionRepository;

    public function __construct(HistoricalCompanyRejectionPercentageRepository $rejectionRepository)
    {
        $this->rejectionRepository = $rejectionRepository;
    }

    /**
     * Rejection percentage and budget usage calculations have a starting timestamp of either 30 days
     * ago or the Last Budget Altering Event (last_modified_lead_limit), depending on which is more recent.
     * Only campaign specific calculations will look for the Last Budget Altering Event. When the scope is not limited
     * to a single campaign we use 30 days ago
     *
     * @param LeadCampaign|null $leadCampaign
     * @param bool $considerRejection
     * @return int
     */
    public function getStartingTimestamp(LeadCampaign $leadCampaign = null, bool $considerRejection = false): int
    {
        $startingTimestamp = 0;

        $thirtyDaysAgo = Carbon::today()->subDays(29)->timestamp;

        if ($leadCampaign && $leadCampaign->last_modified_lead_limit) {
            $startingTimestamp = $leadCampaign->last_modified_lead_limit->timestamp;
        }

        if($considerRejection) {
            $latestEligibilityAfterRejection = $this->rejectionRepository->getLatestEligibilityTimestamp($leadCampaign->company->companyid, Product::LEAD);
            $startingTimestamp = $latestEligibilityAfterRejection ? max($startingTimestamp, $latestEligibilityAfterRejection) : $startingTimestamp;
        }

        return max($startingTimestamp, $thirtyDaysAgo);
    }

    /**
     * This will give either 30 days ago or the Last Budget Altering Event, whichever is more recent
     *
     * @param LeadCampaign $leadCampaign
     * @param bool $omitRejectionPeriod
     * @return int
     */
    public function getStartingTimestampForCampaign(LeadCampaign $leadCampaign, bool $omitRejectionPeriod = false): int
    {
        return $this->getStartingTimestamp($leadCampaign, $omitRejectionPeriod);
    }

    /**
     * @param float|int $num1
     * @param float|int $num2
     * @param bool $castInt
     * @param bool $castFloat
     * @return float
     */
    private function preventDivByZero(float|int $num1, float|int $num2, bool $castInt = false, bool $castFloat = true): float
    {
        if ($num2 == 0) {
            $num2 = 1;
        }
        if ($castFloat) {
            $result = doubleval($num1) / doubleval($num2);
        } else {
            $result = $num1 / $num2;
        }
        return $castInt ? ceil($result) : round((float) $result, 2);
    }
}
