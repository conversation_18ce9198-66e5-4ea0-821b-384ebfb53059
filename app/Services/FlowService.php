<?php

namespace App\Services;

use App\Services\Workflows\FirestoreProxyService;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class FlowService
{
    public function __construct(
        protected FirestoreProxyService $firestoreProxyService,
    )
    {

    }

    /**
     * @return mixed
     * @throws ValidationException
     */
    public function getFlowOptions(): array
    {
        $response = $this->firestoreProxyService->get('/revisions')->json();

        if (!Arr::get($response, 'data.status')) {
            throw ValidationException::withMessages([
                'message' => 'Failed to retrieve flows.'
            ]);
        }

        return collect(Arr::get($response, 'data.revision_meta'))
            ->map(fn($data) => $this->mapData($data))
            ->values()
            ->toArray();
    }

    /**
     * @param array $data
     * @return array
     */
    public function mapData(array $data): array
    {
        return [
            'id'            => Arr::get($data, 'id'),
            'name'          => Arr::get($data, 'name'),
            'prod_revision' => Arr::get($data, 'versions.production'),
            'revisions'     => collect(Arr::get($data, 'revisions', []))->map(fn($array) => $array['id']),
        ];
    }
}
