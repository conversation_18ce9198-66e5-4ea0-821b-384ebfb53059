<?php

namespace App\Services\Prospects;

use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Support\Facades\DB;

class ProspectingQueuePrioritizationService
{

    /**
     * @return void
     */
    public static function prioritizeQueue(): void
    {
        $availableProspects = NewBuyerProspect::query()->where(NewBuyerProspect::FIELD_USER_ID, 0)
            ->where(NewBuyerProspect::FIELD_STATUS, ProspectStatus::INITIAL)->get();

        $availableProspects = $availableProspects
            ->sortByDesc(fn(NewBuyerProspect $prospect) => intval($prospect['source_data']['recent_lead_count']));

        $ordinalValue = 1;
        DB::beginTransaction();
        /** @var NewBuyerProspect $prospect */
        foreach ($availableProspects as $prospect) {

            if ($prospect->source === ProspectSource::REGISTRATION) {
                $prospect->ordinal_value = 0;
            } else {
                $prospect->ordinal_value = $ordinalValue++;
            }

            $prospect->save();
        }
        DB::commit();

        self::dropDuplicateProspects();
    }

    /**
     * Drop all dupes to the bottom of the queue if any of the respective dupes have a status other than 'initial'
     *
     * @return void
     */
    private static function dropDuplicateProspects(): void
    {
        $nonInitialDupeWebsites = NewBuyerProspect::query()
            ->select(NewBuyerProspect::FIELD_COMPANY_WEBSITE)
            ->whereNotNull(NewBuyerProspect::FIELD_COMPANY_WEBSITE)
            ->whereNot(NewBuyerProspect::FIELD_COMPANY_WEBSITE, '')
            ->groupBy(NewBuyerProspect::FIELD_COMPANY_WEBSITE)
            ->havingRaw('count(*) > 1 and sum(if(status = "initial", 0, 1)) > 0')
            ->pluck(NewBuyerProspect::FIELD_COMPANY_WEBSITE)->toArray();

        NewBuyerProspect::query()
            ->whereIn(NewBuyerProspect::FIELD_COMPANY_WEBSITE, $nonInitialDupeWebsites)
            ->update([NewBuyerProspect::FIELD_ORDINAL_VALUE => null]);
    }
}
