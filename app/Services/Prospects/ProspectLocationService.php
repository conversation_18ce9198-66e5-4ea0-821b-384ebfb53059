<?php

namespace App\Services\Prospects;

use App\Enums\Prospects\ProspectStatus;
use App\Models\Legacy\Location;
use App\Models\Prospects\NewBuyerProspect;
use Exception;
use Illuminate\Support\Collection;

class ProspectLocationService
{

    /**
     * This is a one-off script to populate the state and city fields on existing prospects
     *
     * @return void
     */
    public static function populateMissingLocationData(): void
    {
        NewBuyerProspect::query()->where(NewBuyerProspect::FIELD_STATUS, ProspectStatus::INITIAL)
            ->where(NewBuyerProspect::FIELD_USER_ID, 0)
            ->whereNull(NewBuyerProspect::FIELD_ADDRESS_STATE_ABBR)
            ->whereNull(NewBuyerProspect::FIELD_ADDRESS_CITY_KEY)
            ->chunkById(500, function (Collection $prospects) {
                $prospects->each(function (NewBuyerProspect $prospect) {
                    self::populateCityAndState($prospect);
                });
            });
    }

    /**
     * @param NewBuyerProspect $prospect
     * @return void
     */
    private static function populateCityAndState(NewBuyerProspect $prospect): void
    {
        try {
            $zipCode = $prospect->source_data['zip_codes'][0];
        } catch (Exception $e) {
            return;
        }

        $location = Location::query()->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::ZIP_CODE, $zipCode)->first();

        if (!$location) {
            return;
        }

        $prospect->address_state_abbr = $location->state_abbr;
        $prospect->address_city_key   = $location->city_key;
        $prospect->save();
    }

}
