<?php

namespace App\Services\Prospects;

use App\Models\Prospects\CloserDemo;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\CalendlyService;
use Carbon\Carbon;

class CloserDemoService
{

    public static function create(int $userId, string $calendlyEventUrl, NewBuyerProspect $prospect): CloserDemo
    {
        $demo = CloserDemo::create([
            CloserDemo::FIELD_USER_ID               => $userId,
            CloserDemo::FIELD_NEW_BUYER_PROSPECT_ID => $prospect->id,
            CloserDemo::FIELD_COMPANY_ID            => $prospect->company_id,
            CloserDemo::FIELD_CALENDLY_EVENT_URL    => $calendlyEventUrl,
        ]);

        $calendlyPayload = CalendlyService::getEventPayload($calendlyEventUrl, $userId);
        if (!$calendlyPayload)
            return $demo;

        $demo->update([
            CloserDemo::FIELD_DEMO_AT          => Carbon::createFromTimeString($calendlyPayload['start_time']),
            CloserDemo::FIELD_CALENDLY_PAYLOAD => $calendlyPayload
        ]);

        return $demo;
    }
}
