<?php

namespace App\Services\Prospects;

use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectStatus;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Support\Collection;

class ProspectDuplicateMatchingService
{
    /**
     * @param NewBuyerProspect $prospect
     * @return Collection<Company>
     */
    public static function getAllPotentialMatches(NewBuyerProspect $prospect): Collection
    {
        $websiteMatches = self::findCompaniesWithExactWebsite($prospect->company_website);
        if ($websiteMatches->count() > 0)
            return $websiteMatches;

        $companyPhoneMatches = self::findCompaniesWithExactPhone($prospect->company_phone);
        if ($companyPhoneMatches->count() > 0)
            return $companyPhoneMatches;

        if (empty($prospect->company_website) && empty($prospect->company_phone)) {
            $nameMatches = self::findCompaniesWithSimilarName($prospect->company_name);
            if ($nameMatches->count() > 0)
                return $nameMatches;
        }

        return collect();
    }

    /**
     * @param string|null $website
     * @return Collection<Company>
     */
    public static function findCompaniesWithExactWebsite(?string $website): Collection
    {
        $domain = str($website)->replaceMatches('((http(s*)(:\/\/))|(www.)|(/$))', '')->before('/');

        if (empty($domain)) {
            return collect();
        }

        return Company::query()->whereRaw(
            "SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(SUBSTRING_INDEX(website_verified_url, '/', 3), '://', -1), '/', 1), '?', 1),'www.',-1) like ?",
            ["%$domain%"]
        )->get();
    }

    /**
     * @param string|null $website
     * @return Collection
     */
    private static function findCompaniesWithSimilarWebsite(?string $website): Collection
    {
        if (empty($website))
            return collect();

        $domain = parse_url($website, PHP_URL_HOST);
        return Company::query()->whereRaw('match(website_verified_url)against(?)', [$domain ?? $website])->get();
    }

    /**
     * @param string $name
     * @param int $sampleSize
     * @return Collection
     */
    public static function findCompaniesWithSimilarName(string $name, int $sampleSize = 3): Collection
    {
        if (empty($name))
            return collect();

        $fuzzyName = preg_replace("/[^A-Za-z]/", '||', $name);
        $words     = explode('||', $fuzzyName);
        $words     = array_values(array_filter($words));

        $fuzzySamples = [];
        for ($i = 0; $i < max(count($words) - ($sampleSize - 1), 1); $i++) {
            $fuzzySample = $words[$i];
            for ($j = 1; $j < min($sampleSize, count($words)); $j++) {
                $fuzzySample .= '%' . $words[$i + $j];
            }
            $fuzzySamples[] = '%' . $fuzzySample . '%';
        }

        $query = Company::query()->whereLike(Company::FIELD_NAME, $fuzzySamples[0]);
        for ($i = 1; $i < count($fuzzySamples); $i++) {
            $query->orWhereLike(Company::FIELD_NAME, $fuzzySamples[$i]);
        }

        return $query->get();
    }

    /**
     * @param string $prospectExternalReference
     * @param int $companyId
     * @return void
     */
    public static function flagAsDuplicate(string $prospectExternalReference, int $companyId): void
    {
        $prospect = NewBuyerProspect::query()
            ->where(NewBuyerProspect::FIELD_EXTERNAL_REFERENCE, $prospectExternalReference)
            ->where(NewBuyerProspect::FIELD_COMPANY_ID, 0)
            ->where(NewBuyerProspect::FIELD_STATUS, ProspectStatus::ACTIVE)
            ->first();

        $company = Company::find($companyId);
        if (!$prospect || !$company)
            return;

        $prospect->update([
            NewBuyerProspect::FIELD_COMPANY_ID => $companyId,
            NewBuyerProspect::FIELD_STATUS     => ProspectStatus::CLOSED,
            NewBuyerProspect::FIELD_RESOLUTION => ProspectResolution::DUPLICATE_COMPANY
        ]);

        ProspectConversionService::createCompanyUsers($company, $prospect);
    }

    /**
     * @param string|null $companyPhone
     * @return Collection
     */
    private static function findCompaniesWithExactPhone(?string $companyPhone): Collection
    {
        if (empty($companyPhone))
            return collect();
        $sanitizedCompanyPhone = preg_replace("/[^0-9]/", '', $companyPhone);
        return Company::query()
            ->join(CompanyLocation::TABLE, CompanyLocation::TABLE . '.' . CompanyLocation::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->whereRaw("regexp_replace(company_locations.phone, '\\\\D', '') = ?", $sanitizedCompanyPhone)
            ->get('companies.*')->unique(Company::FIELD_ID);
    }

}
