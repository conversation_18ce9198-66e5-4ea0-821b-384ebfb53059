<?php

namespace App\Services\Prospects;

use App\Models\Odin\IndustryService;
use App\Models\Prospects\NewBuyerProspect;

class ProspectIndustriesService
{

    /**
     * @param NewBuyerProspect $prospect
     * @return array
     */
    public static function getIndustryIds(NewBuyerProspect $prospect): array
    {
        if (empty($prospect->industry_service_ids)){
            return [];
        }

        return IndustryService::query()->whereIn(IndustryService::FIELD_ID, $prospect->industry_service_ids)
            ->get(IndustryService::FIELD_INDUSTRY_ID)->unique(IndustryService::FIELD_INDUSTRY_ID)->pluck('industry_id')
            ->toArray();
    }

    /**
     * @param array $industryIds
     * @return array
     */
    public static function convertIndustryIdsToServiceIds(array $industryIds): array
    {
        $servicesByIndustry = IndustryService::query()->whereIn(IndustryService::FIELD_INDUSTRY_ID, $industryIds)->get()
            ->groupBy(IndustryService::FIELD_INDUSTRY_ID);

        $serviceIds = [];
        foreach ($servicesByIndustry as $industryId => $services) {
            $serviceIds[] = self::getExplicitIndustryService($industryId) ?? min($services->pluck(IndustryService::FIELD_ID)->toArray());
        }

        return $serviceIds;
    }

    /**
     * @param int $industryId
     * @return int|null
     */
    private static function getExplicitIndustryService(int $industryId): ?int
    {
        return match ($industryId) {
            1 => 1,   // solar
            2 => 5,   // roofing
            11 => 21, // windows
            12 => 22, // siding
            10 => 15, // hvac
            14 => 30, // bathroom
            15 => 31, // kitchen
            default => null,
        };
    }
}
