<?php

namespace App\Services\Prospects;

use App\Enums\Prospects\CloserDemoStatus;
use App\Models\Prospects\CloserDemo;
use Illuminate\Support\Collection;

class CloserDemoNotificationService
{
    const int PRIOR_NOTIFICATION_MINUTES = 30;

    /**
     * @return Collection
     */
    public static function getUpcomingDemos(): Collection
    {
        return CloserDemo::query()
            ->where(CloserDemo::FIELD_UPCOMING_NOTIFICATION_SENT, false)
            ->where(CloserDemo::FIELD_STATUS, CloserDemoStatus::BOOKED)
            ->whereDate(CloserDemo::FIELD_DEMO_AT, '>', now())
            ->whereDate(CloserDemo::FIELD_DEMO_AT, '<=', now()->addHour())
            ->get();
    }

    /**
     * @param CloserDemo $demo
     * @return int
     */
    public static function getDelay(CloserDemo $demo): int
    {
        return $demo->demo_at->subMinutes(self::PRIOR_NOTIFICATION_MINUTES)->diffInSeconds(now());
    }

    /**
     * @param CloserDemo $demo
     * @return string
     */
    public static function getUpcomingDemoMessage(CloserDemo $demo): string
    {
        $prospect = $demo->prospect;
        $name     = $prospect->decision_maker_first_name . ' ' . $prospect->decision_maker_last_name;
        $link     = $demo->calendly_payload['location']['join_url'];
        return "\n**Demo meeting in " . self::PRIOR_NOTIFICATION_MINUTES . " minutes**
            `$name @ $prospect->company_name`
            [Join Meeting]($link)
        ";
    }
}
