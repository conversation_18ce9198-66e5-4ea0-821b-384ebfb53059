<?php

namespace App\Services\Prospects;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectStatus;
use App\Jobs\Mailbox\ImportCompanyEmailsJob;
use App\Jobs\Prospects\SyncProspectCallsToCompanyUsers;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\NewBuyerProspect;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyUserRepository;
use Illuminate\Support\Collection;

class ProspectConversionService
{

    /**
     * @param NewBuyerProspect $prospect
     * @param ProspectResolution $resolution
     * @return Company
     */
    public static function convertToCompany(NewBuyerProspect $prospect, ProspectResolution $resolution): Company
    {
        $company = self::createCompany($prospect->company_name, $prospect->company_website);

        $prospect->update([
            NewBuyerProspect::FIELD_STATUS     => ProspectStatus::CLOSED,
            NewBuyerProspect::FIELD_RESOLUTION => $resolution,
            NewBuyerProspect::FIELD_COMPANY_ID => $company->id,
        ]);

        self::createCompanyIndustries($company, $prospect->industry_service_ids);
        self::createCompanyServices($company, $prospect->industry_service_ids);
        self::createCompanyUsers($company, $prospect);

        if ($prospect->address_street && $prospect->address_city_key && $prospect->address_state_abbr)
            self::createCompanyLocation($company, $prospect->address_street, $prospect->address_city_key, $prospect->address_state_abbr, $prospect->company_phone);

        self::syncProspectEmails($company);
        self::syncProspectPhoneCalls($company);

        return $company;
    }

    /**
     * @param NewBuyerProspect $prospect
     * @return bool
     */
    public static function canConvertToCompany(NewBuyerProspect $prospect): bool
    {
        if ($prospect->company_id > 0)
            return false;

        foreach (NewBuyerProspect::FIELDS_REQUIRED_TO_CONVERT as $field) {
            if ($prospect->{$field} === null || $prospect->{$field} === '') {
                return false;
            }
        }
        return true;
    }

    /**
     * @param string $companyName
     * @param string|null $companyWebsite
     * @return Company
     */
    private static function createCompany(string $companyName, ?string $companyWebsite): Company
    {
        return Company::create([
            Company::FIELD_NAME    => $companyName,
            Company::FIELD_WEBSITE => $companyWebsite,
            Company::FIELD_ADMIN_STATUS  => CompanyAdminStatus::ADMIN_LOCKED,
        ]);
    }

    /**
     * @param Company $company
     * @param array $industryServiceIds
     * @return void
     */
    private static function createCompanyIndustries(Company $company, array $industryServiceIds): void
    {
        $industryIds = IndustryService::whereIn(IndustryService::FIELD_ID, $industryServiceIds)
            ->pluck(IndustryService::FIELD_INDUSTRY_ID)->toArray();
        $company->industries()->sync($industryIds);
    }

    /**
     * @param Company $company
     * @param array $industryServiceIds
     * @return void
     */
    private static function createCompanyServices(Company $company, array $industryServiceIds): void
    {
        $company->services()->sync($industryServiceIds);
    }

    /**
     * @param Company $company
     * @param string|null $firstName
     * @param string|null $lastName
     * @param string|null $email
     * @param string|null $phone
     * @param bool $isDecisionMaker
     * @return void
     */
    public static function createCompanyUsers(Company $company, NewBuyerProspect $prospect): void
    {
        $users = collect();

        if ($prospect->decision_maker_first_name && ($prospect->decision_maker_email || $prospect->decision_maker_phone)) {
            $users->add([
                CompanyUser::FIELD_FIRST_NAME        => $prospect->decision_maker_first_name,
                CompanyUser::FIELD_LAST_NAME         => $prospect->decision_maker_last_name,
                CompanyUser::FIELD_EMAIL             => $prospect->decision_maker_email,
                CompanyUser::FIELD_CELL_PHONE        => $prospect->decision_maker_phone,
                CompanyUser::FIELD_IS_DECISION_MAKER => true,
            ]);
        }

        $users->merge($prospect->contacts
                ->reject(fn($contact) => $contact->email === $prospect->decision_maker_email)
                ->transform(fn($contact) => [
                    CompanyUser::FIELD_FIRST_NAME        => $contact->first_name,
                    CompanyUser::FIELD_LAST_NAME         => $contact->last_name,
                    CompanyUser::FIELD_EMAIL             => $contact->email,
                    CompanyUser::FIELD_CELL_PHONE        => $contact->cell_phone,
                    CompanyUser::FIELD_OFFICE_PHONE      => $contact->office_phone,
                    CompanyUser::FIELD_TITLE             => $contact->title,
                    CompanyUser::FIELD_DEPARTMENT        => $contact->department,
            ])
        )->each(fn($user) => app(CompanyUserRepository::class)->createCompanyContact($company, $user));
    }

    /**
     * @param Company $company
     * @param string $street
     * @param string $cityKey
     * @param string $stateAbbr
     * @param string|null $companyPhone
     * @return void
     */
    private static function createCompanyLocation(Company $company, string $street, string $cityKey, string $stateAbbr, ?string $companyPhone): void
    {

        $cityLocation = Location::query()
            ->where(Location::STATE_ABBREVIATION, $stateAbbr)
            ->where(Location::CITY_KEY, $cityKey)
            ->where(Location::TYPE, Location::TYPE_CITY)
            ->first();

        /** @var AddressRepository $addressRepository */
        $addressRepository = app(AddressRepository::class);

        $address = $addressRepository->createAddressFromAttributes([
            Address::FIELD_STATE     => $stateAbbr,
            Address::FIELD_CITY      => $cityLocation->city,
            Address::FIELD_ADDRESS_1 => $street,
        ]);

        $company->locations()->create([
            CompanyLocation::FIELD_ADDRESS_ID => $address->id,
            CompanyLocation::FIELD_NAME       => 'Default',
            CompanyLocation::FIELD_IS_PRIMARY => true,
            CompanyLocation::FIELD_PHONE      => $companyPhone,
        ]);
    }

    /**
     * @param int $userId
     * @return Collection<Company>
     */
    public static function getProspectedCompanies(int $userId): Collection
    {
        return Company::query()
            ->join(NewBuyerProspect::TABLE, NewBuyerProspect::TABLE . '.' . NewBuyerProspect::FIELD_COMPANY_ID, '=', Company::TABLE . '.' . Company::FIELD_ID)
            ->where(NewBuyerProspect::TABLE . '.' . NewBuyerProspect::FIELD_COMPANY_ID, '>', 0)
            ->where(NewBuyerProspect::TABLE . '.' . NewBuyerProspect::FIELD_USER_ID, $userId)
            ->whereNot(NewBuyerProspect::TABLE . '.' . NewBuyerProspect::FIELD_RESOLUTION, ProspectResolution::DUPLICATE_COMPANY)
            ->get(['companies.*']);
    }

    /**
     * @param Company $company
     * @return void
     */
    private static function syncProspectEmails(Company $company): void
    {
        ImportCompanyEmailsJob::dispatch($company->id);
    }

    /**
     * @param Company $company
     * @return void
     */
    private static function syncProspectPhoneCalls(Company $company): void
    {
        SyncProspectCallsToCompanyUsers::dispatch($company->id);
    }
}
