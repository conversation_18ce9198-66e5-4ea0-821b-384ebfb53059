<?php

namespace App\Services\Prospects;

use App\Enums\EmailTemplateType;
use App\Enums\Odin\Industry;
use App\Models\EmailTemplate;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;

class ProspectEmailService
{
    /**
     * @return string
     */
    public function getBDMMarkdownEmailContent(): string
    {
        return <<<MARKDOWN
Hi {prospect.decision_maker_first_name},

It is {BDM.name} here, from [{prospect.domain_name}]({prospect.url}).
We have not dealt with you guys before, but we are a fairly large supplier of {prospect.industry} leads to the industry.

A quick rundown of what makes our leads different:
- All generated internally through paid Google and Meta traffic (and some organic). We don’t buy in affiliate leads, so they are less likely to be duplicates with other lead sources.
- We pin-verify each phone number before creating a lead.
- We ask the consumer how many bids they want for their new roof and then only sell the lead to the number of {prospect.industry} companies that the consumer wants to hear from.

You can go through our call to action [here]({prospect.calculator_url}).

Do you have time for an intro call today or tomorrow?

Kind Regards,

{BDM.name}<br>
Business Development Manager<br>
E: {BDM.email}<br>
W: {prospect.domain_name}<br>
{BDM.phone}
MARKDOWN;
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return array
     */
    public function getWebsiteDetails(NewBuyerProspect $newBuyerProspect): array
    {
        $industries = IndustryService::query()
            ->with(IndustryService::RELATION_INDUSTRY)
            ->whereIn(IndustryService::FIELD_ID, $newBuyerProspect->industry_service_ids)
            ->get()
            ->map(fn(IndustryService $industryService) => $industryService->industry->name)
            ->flip()
            ->toArray();

        if (array_key_exists(Industry::SOLAR->value, $industries)) {
           return ['solarreviews.com', 'https://www.solarreviews.com', 'https://www.solarreviews.com/solar-estimator', strtolower(Industry::SOLAR->value)];
        }
        if (array_key_exists(Industry::ROOFING->value, $industries)) {
            return ['roofingcalculator.com', 'https://roofingcalculator.com', 'https://roofingcalculator.com/roof-replacement-cost-calculator', strtolower(Industry::ROOFING->value)];
        }
        if (array_key_exists(Industry::BATHROOMS->value, $industries)) {
            return ['bathroom-estimate.org', 'https://www.bathroom-estimate.org', 'https://www.bathroom-estimate.org/bathroom-calculator', strtolower(Industry::BATHROOMS->value)];
        }
        if (array_key_exists(Industry::KITCHENS->value, $industries)) {
            return ['kitchen-estimate.org', 'https://www.kitchen-estimate.org', 'https://www.kitchen-estimate.org/kitchen-calculator', strtolower(Industry::KITCHENS->value)];
        }

        return ['fixr.com', 'https://www.fixr.com', 'https://www.fixr.com', strtolower(Industry::SOLAR->value)];
    }

    /**
     * @param User $bdm
     * @param bool $personal
     * @param array $select
     *
     * @return Collection
     */
    public function getEmailTemplatesForBdm(User $bdm, bool $personal = true, array $select = ['*']): Collection
    {
        return EmailTemplate::query()
            ->select($select)
            ->where(EmailTemplate::FIELD_OWNER_USER_ID, $bdm->id)
            ->where(EmailTemplate::FIELD_PERSONAL, $personal)
            ->where(EmailTemplate::FIELD_TYPE, EmailTemplateType::BDM_PROSPECTING_EMAIL->value)
            ->get();
    }
}
