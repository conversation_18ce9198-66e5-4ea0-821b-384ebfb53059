<?php

namespace App\Services;

class GoogleMapsHelperService
{
    /**
     * <PERSON><PERSON> creating a Google Maps link for the given latitude & longitude.
     *
     * @param string|null $latitude
     * @param string|null $longitude
     * @return string|null
     */
    public static function generateGoogleMapsLink(?string $latitude, ?string $longitude): string|null
    {
        if (is_null($latitude)
        || is_null($longitude)
        || !strlen(trim($latitude))
        || !strlen(trim($longitude))) {
            return null;
        }

        return $latitude && $longitude
            ? "https://google.com/maps/?q=$latitude,$longitude&t=h&z=19&hl=en"
            : null;
    }
}
