<?php

namespace App\Services\LeadProcessing;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\Odin\Product;
use App\Enums\RoleType;
use App\Events\LeadProcessing\LeadUndersoldEvent;
use App\Events\LeadProcessing\LeadUnsoldEvent;
use App\Exceptions\LeadProcessingException;
use App\Jobs\DispatchPubSubEvent;
use App\Jobs\RecordMonitoringLog;
use App\Models\AppointmentProcessingAllocation;
use App\Models\LeadProcessingPendingReview;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\LeadProcessingReservedLead;
use App\Models\Legacy\EloquentCompany;
use App\Events\LeadProcessing\LeadAllocatedEvent;
use App\Events\LeadProcessing\LeadReleasedEvent;
use App\Events\LeadProcessing\LeadReservedEvent;
use App\Jobs\AllocateLeadJob;
use App\Jobs\CheckHeartbeatJob;
use App\Jobs\ReviewLeadJob;
use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingConfiguration;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\Industry;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadType;
use App\Models\Notification;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ServiceProduct;
use App\Models\Role;
use App\Repositories\LeadProcessing\LeadProcessingQueueConstraintsRepository;
use App\Repositories\LeadProcessing\LeadProcessingQueueRepository;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Repositories\Legacy\QuoteRepository;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Repositories\MissedProductRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\BestRevenueRecordingService;
use App\Services\Legacy\Campaigns\LeadCampaignAvailabilityService;
use App\Services\Legacy\LeadAggregatorsService;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\MissedProducts\MissedProductService;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\Odin\Campaigns\FutureCampaignAvailabilityService;
use App\Services\Odin\Campaigns\OdinCampaignAvailabilityService;
use App\Services\Odin\ProductProcessing\OdinProcessingService;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Arr;
use JetBrains\PhpStorm\ArrayShape;
use Throwable;

class LeadProcessingService
{
    const SECONDS_IN_MINUTE        = 60;
    const MINUTES_IN_HOUR          = 60;
    const HOURS_IN_DAY             = 24;
    const DELAY_2_HOURS            = self::SECONDS_IN_MINUTE * self::MINUTES_IN_HOUR * 2;
    const DELAY_5_MINUTES          = self::SECONDS_IN_MINUTE * 5;
    const FALLBACK_UTC_OFFSET      = -5;
    const PREMIUM_ELECTRICAL_USAGE = 300;
    const EXPIRY_1_DAY             = self::SECONDS_IN_MINUTE * self::MINUTES_IN_HOUR * self::HOURS_IN_DAY;

    const REQUEST_FIRST_NAME             = 'first_name';
    const REQUEST_LAST_NAME              = 'last_name';
    const REQUEST_EMAIL                  = 'email';
    const REQUEST_OWN_PROPERTY           = 'own_property';
    const REQUEST_COMPANIES_REQUESTED    = 'companies_requested';
    const REQUEST_BEST_TIME_TO_CONTACT   = 'best_time_to_contact';
    const REQUEST_APPOINTMENTS_REQUESTED = 'appointments_requested';
    const REQUEST_ELECTRIC_COST          = 'electric_cost';
    const REQUEST_PHONE                  = 'phone';

    const ELOQUENT_QUOTE_DATA_MAPPING = [
        self::REQUEST_FIRST_NAME             => EloquentQuote::FIRST_NAME,
        self::REQUEST_LAST_NAME              => EloquentQuote::LAST_NAME,
        self::REQUEST_EMAIL                  => EloquentQuote::USER_EMAIL,
        self::REQUEST_OWN_PROPERTY           => EloquentQuote::OWN_PROPERTY,
        self::REQUEST_COMPANIES_REQUESTED    => EloquentQuote::NUMBER_OF_QUOTES,
        self::REQUEST_BEST_TIME_TO_CONTACT   => EloquentQuote::BEST_TIME_TO_CALL,
        self::REQUEST_APPOINTMENTS_REQUESTED => EloquentQuote::BEST_TIME_TO_CALL_OTHER,
        self::REQUEST_ELECTRIC_COST          => EloquentQuote::ELECTRIC_COST
    ];

    const ELOQUENT_ADDRESS_DATA_MAPPING = [
        self::REQUEST_PHONE => EloquentAddress::PHONE,
    ];

    const ALLOCATION_REATTEMPT_LIMIT = 5;

    // Set to false to entirely disable 'Unverified' lead allocations
    const UNVERIFIED_ALLOCATION_STATUS = true;

    const NEXT_DAY_REATTEMPT_TIME      = '14:00'; // 2pm

    /** @var LeadProcessingRepository $repository */
    protected LeadProcessingRepository $repository;

    /** @var Dispatcher $dispatcher */
    protected Dispatcher $dispatcher;

    /** @var LeadProcessingQueueRepository $queueRepository */
    protected LeadProcessingQueueRepository $queueRepository;

    /** @var LeadCampaignAvailabilityService $leadCampaignAvailabilityService */
    protected LeadCampaignAvailabilityService $leadCampaignAvailabilityService;

    /** @var LeadDeliveryService $leadDeliveryService */
    protected LeadDeliveryService $leadDeliveryService;

    /** @var QuoteCompanyRepository $quoteCompanyRepository */
    protected QuoteCompanyRepository $quoteCompanyRepository;

    /** @var ZipCodeSetRepository $zipCodeRepository */
    protected ZipCodeSetRepository $zipCodeRepository;

    /** @var LeadAggregatorsService $leadAggregatorsService */
    protected LeadAggregatorsService $leadAggregatorsService;

    /** @var BestRevenueRecordingService $bestRevenueRecordingService */
    protected BestRevenueRecordingService $bestRevenueRecordingService;

    /** @var LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository */
    protected LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository;

    /** @var ConsumerProductRepository $consumerProductRepository */
    protected ConsumerProductRepository $consumerProductRepository;

    const LEAD_UNLOCKED         = 0;
    const LEAD_LOCKED_BY_SYSTEM = -1;

    /**
     * Whether we should log the progress of this job.
     *
     * @var bool
     */
    protected bool $shouldLog;

    /**
     * @param LeadProcessingRepository $repository
     * @param Dispatcher $dispatcher
     * @param LeadProcessingQueueRepository $queueRepository
     * @param LeadCampaignAvailabilityService $leadCampaignAvailableService
     * @param LeadDeliveryService $leadDeliveryService
     * @param QuoteCompanyRepository $quoteCompanyRepository
     * @param LeadAggregatorsService $leadAggregatorsService
     * @param ZipCodeSetRepository $zipCodeRepository
     * @param BestRevenueRecordingService $bestRevenueRecordingService
     * @param LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository
     * @param ConsumerProductRepository $consumerProductRepository
     */
    public function __construct(
        LeadProcessingRepository                 $repository,
        Dispatcher                               $dispatcher,
        LeadProcessingQueueRepository            $queueRepository,
        LeadCampaignAvailabilityService          $leadCampaignAvailableService,
        LeadDeliveryService                      $leadDeliveryService,
        QuoteCompanyRepository                   $quoteCompanyRepository,
        LeadAggregatorsService                   $leadAggregatorsService,
        ZipCodeSetRepository                     $zipCodeRepository,
        BestRevenueRecordingService              $bestRevenueRecordingService,
        LeadProcessingQueueConstraintsRepository $leadProcessingQueueConstraintsRepository,
        ConsumerProductRepository                $consumerProductRepository
    )
    {
        $this->repository                               = $repository;
        $this->dispatcher                               = $dispatcher;
        $this->queueRepository                          = $queueRepository;
        $this->leadCampaignAvailabilityService          = $leadCampaignAvailableService;
        $this->leadDeliveryService                      = $leadDeliveryService;
        $this->quoteCompanyRepository                   = $quoteCompanyRepository;
        $this->leadAggregatorsService                   = $leadAggregatorsService;
        $this->zipCodeRepository                        = $zipCodeRepository;
        $this->bestRevenueRecordingService              = $bestRevenueRecordingService;
        $this->leadProcessingQueueConstraintsRepository = $leadProcessingQueueConstraintsRepository;
        $this->consumerProductRepository                = $consumerProductRepository;
        $this->shouldLog                                = true;
    }

    /**
     * Verifies that a given lead is locked by a given lead processor.
     *
     * @param LeadProcessor $processor
     * @param EloquentQuote $lead
     * @return bool
     */
    public function isLeadReservedByProcessor(EloquentQuote $lead, LeadProcessor $processor): bool
    {
        return LeadProcessingReservedLead::query()->where(LeadProcessingReservedLead::FIELD_LEAD_ID, $lead->quoteid)
            ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, $processor->id)
            ->first() !== null;
    }

    /**
     * Checks to see if lead is locked by "system"
     *
     * @deprecated usage moved to { @see \App\Models\Odin\ConsumerProduct}
     * @param EloquentQuote $lead
     * @return bool
     */
    public function isLeadReservedBySystem(EloquentQuote $lead): bool
    {
        return LeadProcessingReservedLead::query()->where(LeadProcessingReservedLead::FIELD_LEAD_ID, $lead->quoteid)
                ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, LeadProcessingReservedLead::SYSTEM_ID)
                ->first() !== null;
    }

    /**
     * Handles cancelling a lead and deletes the related missed product record.
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param ConsumerProduct|null $consumerProduct
     * @return bool
     *
     * @throws BindingResolutionException
     */
    public function cancelLead(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string        $reason,
        ?string       $comment = null,
        ?bool         $publicComment = null,
        ?ConsumerProduct $consumerProduct = null,
    ): bool
    {
        if (!$this->isLeadReservedByProcessor($lead, $processor))
            return false;

        $this->queueRepository->changeQueues(
            lead: $lead,
            processor: $processor,
            newProcessingStatus: LeadProcessingQueueRepository::STATUS_CANCELLED,
            reason: $reason,
            comment: $comment,
            publicComment: $publicComment,
            consumerProduct: $consumerProduct
        );

        return $this->releaseLead($lead, $processor);
    }

    /**
     * Handles marking an Initial lead for further review
     * When a lead is flagged as needing further review, we will check if there are any 'Verified' budgets available for the lead
     * if not, we will attempt to allocate it as 'Unverified', before moving it to 'Under Review'
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool $publicComment
     * @param ConsumerProduct|null $consumerProduct
     * @return bool
     *
     * @throws BindingResolutionException
     */
    public function markLeadAsPendingReview(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string $reason,
        ?string $comment = null,
        ?bool $publicComment = false,
        ?ConsumerProduct $consumerProduct = null,
    ): bool
    {
        if (!$this->isLeadReservedByProcessor($lead, $processor))
            return false;

        if (empty($consumerProduct)) {
            /** @var ConsumerProduct|null $consumerProduct */
            $consumerProduct = $lead->consumer->consumerProducts()->first();
        }

        // 0-contact-request leads should not go through budget check
        if (!$consumerProduct->contact_requests) {
            $reason = $this->appendZeroContactsReason($reason);
            $this->queueRepository->changeQueues(
                lead: $lead,
                processor: $processor,
                newProcessingStatus:  LeadProcessingQueueRepository::STATUS_PENDING_REVIEW,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment,
                consumerProduct: $consumerProduct,
            );

            return $this->releaseLead($lead, $processor);
        }

        /** @var FutureCampaignAvailabilityService $service */
        $service = app()->make(FutureCampaignAvailabilityService::class);

        $companiesAvailable = $service->companiesWithAvailableBudgetCount($consumerProduct);

        if ($companiesAvailable > 0) {
            // 'Verified' budget is available, move to 'Pending Review'
            $this->queueRepository->changeQueues(
                lead: $lead,
                processor: $processor,
                newProcessingStatus: LeadProcessingQueueRepository::STATUS_PENDING_REVIEW,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment,
                consumerProduct: $consumerProduct,
            );
            return $this->releaseLead($lead, $processor);
        } else{
            // No 'Verified' or 'Unverified' budget available, mark as 'Under Review'
            $this->queueRepository->changeQueues(
                lead: $lead,
                processor: $processor,
                newProcessingStatus: LeadProcessingQueueRepository::STATUS_UNDER_REVIEW,
                reason: $reason,
                comment: $comment,
                publicComment: $publicComment,
                consumerProduct: $consumerProduct,
            );
            $this->releaseLead($lead, $processor);
        }

        return true;
    }

    /**
     * @param EloquentQuote $lead
     * @param Collection $data
     * @return bool
     */
    public function updateBasicInfo(EloquentQuote $lead, Collection $data): bool
    {
        return $this->repository->updateBasicInfo($lead->{EloquentQuote::REFERENCE}, $data);
    }

    /**
     * @param $eloquentQuoteData
     * @param $eloquentAddressData
     * @return bool
     * @throws ValidationException
     */
    protected function validateBasicInfo($eloquentQuoteData, $eloquentAddressData): bool
    {
        $validator = Validator::make(
            array_merge($eloquentQuoteData, $eloquentAddressData),
            [
                self::REQUEST_FIRST_NAME             => 'string|max:255',
                self::REQUEST_LAST_NAME              => 'string|max:255',
                self::REQUEST_EMAIL                  => 'string|max:255',
                self::REQUEST_OWN_PROPERTY           => 'string|in:Yes,No',
                self::REQUEST_ELECTRIC_COST          => 'integer|max:10000',
                self::REQUEST_COMPANIES_REQUESTED    => 'integer|min:0|max:4',
                self::REQUEST_BEST_TIME_TO_CONTACT   => 'string|max:255',
                self::REQUEST_APPOINTMENTS_REQUESTED => 'string|max:255',
                self::REQUEST_PHONE                  => 'string|max:255',
            ]
        );

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        return true;
    }

    /**
     * @param Collection $eloquentQuoteData
     * @return array
     */
    protected function getMappedEloquentQuoteData(Collection $eloquentQuoteData): array
    {

        $mappedData = array();

        foreach ($eloquentQuoteData as $key => $value) {
            $mappedData = Arr::add($mappedData, self::ELOQUENT_QUOTE_DATA_MAPPING[$key], $value);
        }

        return $mappedData;
    }

    /**
     * @param Collection $eloquentAddressData
     * @return array
     */
    protected function getMappedEloquentAddressData(Collection $eloquentAddressData): array
    {
        $mappedData = array();

        foreach ($eloquentAddressData as $key => $value) {
            $mappedData = Arr::add($mappedData, self::ELOQUENT_ADDRESS_DATA_MAPPING[$key], $value);
        }

        return $mappedData;
    }

    /**
     * Handles marking a pending review lead for under review
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $publicComment
     * @param ConsumerProduct|null $consumerProduct
     * @return bool
     *
     * @throws BindingResolutionException
     */
    public function markLeadAsUnderReview(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string        $reason,
        ?string       $comment = null,
        ?bool         $publicComment = null,
        ?ConsumerProduct $consumerProduct = null,
    ): bool
    {
        if (!$this->isLeadReservedByProcessor($lead, $processor))
            return false;

        $quoteCompanyRepository = app(QuoteCompanyRepository::class);
        $alreadySoldOffHours = $quoteCompanyRepository->getOffHourQuoteCompaniesByLead($lead)->count() > 0;
        if ($alreadySoldOffHours) {
            // If the lead has already been allocated off-hours, we won't send to 'Under Review'. We will just mark it as allocated and remove from all queues.
            $this->queueRepository->changeQueues(
                lead: $lead,
                processor: LeadProcessor::systemProcessor(),
                newProcessingStatus: LeadProcessingQueueRepository::STATUS_ALLOCATED,
                reason: '',
                consumerProduct: $consumerProduct,
            );
        }else{
            $this->queueRepository->changeQueues(
                lead: $lead,
                processor: $processor,
                newProcessingStatus:  LeadProcessingQueueRepository::STATUS_UNDER_REVIEW,
                reason:  $reason,
                comment:  $comment,
                publicComment:  $publicComment,
                consumerProduct: $consumerProduct
            );
        }

        return $this->releaseLead($lead, $processor);
    }

    /**
     * Handles marking an Initial lead as Allocated
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param $reason
     * @param string|null $comment
     * @return bool
     *
     * @throws Exception
     */
    public function markLeadAsAllocated(
        EloquentQuote $lead,
        LeadProcessor $processor,
                      $reason,
        ?string       $comment = null
    ): bool
    {
        if (!$this->isLeadReservedByProcessor($lead, $processor))
            return false;

        $this->queueRepository->changeQueues($lead, $processor, LeadProcessingQueueRepository::STATUS_ALLOCATED, $reason, $comment);

        return $this->releaseLead($lead, $processor);
    }

    /**
     * Reserves a lead for a given lead processor.
     *
     * @param EloquentQuote|null $lead
     * @param LeadProcessor $processor
     * @return bool
     */
    public function reserveLead(?EloquentQuote $lead, LeadProcessor $processor): bool
    {
        if ($lead == null)
            return false;

        /** @var LeadProcessingReservedLead $reserved */
        $reserved = LeadProcessingReservedLead::query()->where(LeadProcessingReservedLead::FIELD_LEAD_ID, $lead->quoteid)->first();
        if($reserved !== null && $reserved->processor_id !== $processor->id)
            return false;

        $this->repository->reserveLead($lead->quoteid, $processor->id);
        $this->dispatcher->dispatch(new LeadReservedEvent($lead->{EloquentQuote::REFERENCE}, $processor->id));

        return true;
    }

    /**
     * Handles getting and reserving the next lead in the queue for a given processor.
     *
     * @param LeadProcessor $processor
     * @param int|null $specificLeadId
     * @param int|null $previousLeadId
     * @return ?EloquentQuote
     * @throws Exception
     */
    public function getNextLead(LeadProcessor $processor, ?int $specificLeadId = null, ?int $previousLeadId = null): ?EloquentQuote
    {
        if ($specificLeadId !== null) {
            return $this->getAndReserveByLeadId($specificLeadId, $processor);
        }

        $lead = $this->queueRepository->getNextLead($processor, $previousLeadId);

        $this->queueRepository->flipQueueState($processor->team->primaryQueue);

        return $lead;
    }

    /**
     * Handles the retrieval and reservation of a lead by a specific lead id.
     *
     * @param int $leadId
     * @param LeadProcessor $processor
     * @return EloquentQuote|null
     */
    protected function getAndReserveByLeadId(int $leadId, LeadProcessor $processor): ?EloquentQuote
    {
        /** @var EloquentQuote|null $lead */
        $lead = EloquentQuote::query()->find($leadId);

        if (($lead->lockedbyuserid !== $processor->user->legacy_user_id || !$this->isLeadReservedByProcessor($lead, $processor))
        && !in_array($lead->status, [EloquentQuote::VALUE_STATUS_ALLOCATED, EloquentQuote::VALUE_STATUS_CANCELLED])) {
            if (!$this->reserveLead($lead, $processor)) {
                return null;
            }
        }

        return $lead;
    }

    /**
     * Released a lead from a given lead processor.
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return bool
     */
    public function releaseLead(EloquentQuote $lead, LeadProcessor $processor): bool
    {
        if ($this->isLeadReservedByProcessor($lead, $processor) || $this->isLeadReservedBySystem($lead)) {
            $this->repository->releaseLead($lead->{EloquentQuote::ID}, $processor->id);
            $this->removeHeartbeat($lead, $processor);
            $this->dispatcher->dispatch(new LeadReleasedEvent($lead->{EloquentQuote::REFERENCE}, $processor->id));
            return true;
        }
        return false;
    }

    /**
     * @param EloquentQuote $lead
     * @param string $reason
     * @return bool
     */
    public function updatePendingReviewStatusReason(EloquentQuote $lead, string $reason): bool
    {
        return $this->repository->updatePendingReviewReason($lead, $reason);
    }

    /**
     * Returns the allocated duplicates.
     *
     * @param EloquentQuote $lead
     * @return Collection
     */
    protected function getAllocatedDuplicateCompanies(EloquentQuote $lead): Collection
    {
        /** @var QuoteRepository $quoteRepository */
        $quoteRepository = app()->make(QuoteRepository::class);
        $quotes          = $quoteRepository->getAllocatedRelatedQuotesLast90Days(
            $lead->quoteid,
            $lead->useremail,
            $lead->ipaddress,
            $lead->address->zipcode,
            null
        );

        return $quotes->map(function ($lead) {
            return $lead->quoteCompanies()
                ->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '<>', null)
                ->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '<>', '')
                ->delivered()
                ->get()
                ->pluck(EloquentQuoteCompany::COMPANY_ID);
        })->flatten()->unique();
    }

    /**
     * NOTE: USE CAUTION AS THIS METHOD WILL FORCE ALLOCATION OF LEAD TO SPECIFIED CAMPAIGNS
     * REGARDLESS OF REJECTION %, BUDGET, TIME, DUPLICATES etc...
     *
     * This primary intention for this is to allocate leads to specified companies outside of standard processing hours.
     *
     * @param EloquentQuote $lead
     * @param Collection $salesTypeConfigurations
     * @param string $processingMessage
     * @return void
     */
    public function forceAllocateLeadNow(EloquentQuote $lead, Collection $salesTypeConfigurations, $processingMessage = 'Forced Allocation'): void
    {
        $this->logStatus("Starting FORCED allocation for lead: {$lead->quoteid}", [
            "lead_id"      => $lead->quoteid,
            "campaign_ids" => $salesTypeConfigurations->pluck(LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID)->toArray(),
        ]);

        $results = $this->leadDeliveryService->saveAndDeliverQuoteCompany(
            $lead->uuid,
            $this->getSelectedQuoteCompanies($salesTypeConfigurations),
            null,
            false,
            $processingMessage,
            [],
            true
        );

        if ($lead->status !== EloquentQuote::VALUE_STATUS_ALLOCATED) {
            $this->repository->updateLeadStatus($lead->uuid, EloquentQuote::VALUE_STATUS_ALLOCATED);
        }

        $this->consumerProductRepository->updateConsumerProductStatusByLegacyLeadIdIfExists($lead->quoteid, ConsumerProduct::STATUS_ALLOCATED);

        $this->logStatus(
            "Successfully delivered quote companies for lead: {$lead->quoteid}",
            [
                "lead_id" => $lead->quoteid,
                "legacy_admin_response" => $results
            ]
        );

    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @return bool
     * @throws BindingResolutionException
     */
    public function allocateLead(
        LeadProcessingAllocation $leadProcessingAllocation,
        array $ignoreCompanyIds = [],
        ?int $remainingLegs = null
    ): bool
    {
        $this->logStatus("Starting allocation for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id, "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);

        $companyIds = $this->getAllocatedDuplicateCompanies($leadProcessingAllocation->lead)
                           ->values()
                           ->toArray();

        $companyIds = array_unique(array_merge($companyIds, $ignoreCompanyIds));

        $industry = $leadProcessingAllocation->consumerProduct->industryService->industry->name;
        if(!in_array($industry, [\App\Enums\Odin\Industry::SOLAR->value, \App\Enums\Odin\Industry::ROOFING->value])) {
            /** @var OdinProcessingService $service */
            $service = app()->make(OdinProcessingService::class);

            return $service->allocateLead($leadProcessingAllocation, $companyIds, [], $remainingLegs);
        }

        if(count($companyIds) > 0) {
            $this->logStatus(
                "Duplicate allocated companies found for lead: {$leadProcessingAllocation->lead_id}",
                [
                    "lead_id" => $leadProcessingAllocation->lead_id,
                    "companies" => implode(',', $companyIds),
                    "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                ]
            );
        }

        // see if lead is in service areas of any active campaigns
        if ($this->leadCampaignAvailabilityService->areCompaniesAvailable($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
            false, null, $companyIds)) {
            // see if all valid campaigns are over budget
            $companiesWithinBudget = $this->leadCampaignAvailabilityService->companiesWithAvailableBudgetCount($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE});
            if ($companiesWithinBudget === 0) {
                $this->logStatus("No companies with budget for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);

                Cache::remember("ps-lead-unsold-no-budget-{$leadProcessingAllocation->lead_id}", self::EXPIRY_1_DAY, function() use(&$leadProcessingAllocation) {
                    DispatchPubSubEvent::dispatch(
                        EventCategory::LEADS,
                        EventName::LEAD_UNSOLD_NO_BUDGET,
                        ["lead_id" => $leadProcessingAllocation->lead_id, "initiator_id" => $leadProcessingAllocation->leadProcessor?->id ?? 0]
                    );
                });

                return $this->processCompaniesNotAvailable($leadProcessingAllocation, EloquentQuote::VALUE_STATUS_UNDER_REVIEW, $ignoreCompanyIds, $remainingLegs);
            } elseif ($companiesWithinBudget < $leadProcessingAllocation->lead->numberofquotes) {
                Cache::remember("unsold-or-undersold-lead-{$leadProcessingAllocation->lead_id}", self::EXPIRY_1_DAY,
                    function () use ($leadProcessingAllocation) {
                        LeadUndersoldEvent::dispatch($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->processing_scenario);

                        DispatchPubSubEvent::dispatch(
                            EventCategory::LEADS,
                            EventName::LEAD_UNDERSOLD,
                            ["lead_id" => $leadProcessingAllocation->lead_id, "initiator_id" => $leadProcessingAllocation->leadProcessor?->id ?? 0]
                        );

                        return true;
                    });

                $this->logStatus("Companies available are less than requested for lead: {$leadProcessingAllocation->lead_id}",
                    ["lead_id" => $leadProcessingAllocation->lead_id, "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);
            }

            if ($this->processAvailableCompanies($leadProcessingAllocation, $companyIds, $remainingLegs)) {
                $this->logStatus("Successfully processed lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id, "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);
                return true;
            }
        }

        $this->logStatus("No companies available for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id, "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);

        return $this->processCompaniesNotAvailable($leadProcessingAllocation, EloquentQuote::VALUE_STATUS_NO_COMPANIES, $ignoreCompanyIds, $remainingLegs);
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param string $leadStatus
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @return bool
     * @throws Exception
     */
    protected function processCompaniesNotAvailable(
        LeadProcessingAllocation $leadProcessingAllocation,
        string                   $leadStatus,
        array                    $ignoreCompanyIds = [],
        ?int                     $remainingLegs = null
    ): bool
    {
        $this->logStatus(
            "Processing companies not available: {$leadProcessingAllocation->lead_id}",
            [
                "lead_id" => $leadProcessingAllocation->lead_id,
                "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
            ]
        );

        Cache::remember("unsold-or-undersold-lead-{$leadProcessingAllocation->lead_id}", self::EXPIRY_1_DAY, function() use ($leadProcessingAllocation) {
            LeadUndersoldEvent::dispatch($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->processing_scenario);
            return true;
        });
        $this->bestRevenueRecordingService->updateRevenueScenario($leadProcessingAllocation->lead, null);

        Cache::remember("ps-lead-unsold-no-companies-{$leadProcessingAllocation->lead_id}", self::EXPIRY_1_DAY, function() use(&$leadProcessingAllocation) {
            DispatchPubSubEvent::dispatch(
                EventCategory::LEADS,
                EventName::LEAD_UNSOLD_NO_COMPANIES,
                ["lead_id" => $leadProcessingAllocation->lead_id, "initiator_id" => $leadProcessingAllocation->leadProcessor?->id ?? 0]
            );

            return true;
        });

        return $this->processCompaniesNotAvailableSolarAndRoofing($leadProcessingAllocation, $leadStatus, $ignoreCompanyIds, $remainingLegs);
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param string $leadStatus
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @return bool
     * @throws BindingResolutionException
     * @throws Exception
     * @throws Throwable
     */
    protected function processCompaniesNotAvailableSolarAndRoofing(
        LeadProcessingAllocation $leadProcessingAllocation,
        string                   $leadStatus,
        array                    $ignoreCompanyIds = [],
        ?int                     $remainingLegs = null
    ): bool
    {
        $lead = $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD};

        $this->attemptLeadStatusUpdate($lead, $leadStatus, $leadProcessingAllocation);

        $isApptLead = $this->isAppointmentLead($leadProcessingAllocation);

        // if this was the final attempt to allocate as a 'Verified' lead, attempt to allocate as 'Unverified'
        if ($leadProcessingAllocation->final_attempt && !$isApptLead) {
            if(!self::UNVERIFIED_ALLOCATION_STATUS || !$this->attemptToSellAsOptionalSalesType($leadProcessingAllocation)){
                $this->queueRepository->changeQueues($lead, $leadProcessingAllocation->leadProcessor, LeadProcessingQueueRepository::STATUS_UNDER_REVIEW, '');
            }
            $this->releaseLead($lead, $leadProcessingAllocation->leadProcessor);
            return true;
        }

        // Re-attempt allocation every 2 hours on first day
        // Only schedule one final attempt at 2pm for the second day, before selling as 'Unverified'
        if($this->determineLeadAgeInDays($lead) < 2) {
            $reviewLead = true;

            $delay = $this->getReattemptDelay($lead);

            // If the delay is greater than 2 hours, it means that we've rescheduled the allocation attempt for the following day
            // We want this to be the final attempt to allocate as a 'Verified' lead
            $finalAttempt = $delay > self::DELAY_2_HOURS;
            if($finalAttempt) {
                if($isApptLead) {
                    $reviewLead = false;

                    LeadUndersoldEvent::dispatch($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->processing_scenario);

                    DispatchPubSubEvent::dispatch(
                        EventCategory::LEADS,
                        EventName::LEAD_UNDERSOLD,
                        ["lead_id" => $leadProcessingAllocation->lead_id, "initiator_id" => $leadProcessingAllocation->leadProcessor?->id ?? 0]
                    );

                    $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_DELIVER_AT} = LeadProcessingAllocation::BLANK_DELIVERY;
                    $leadProcessingAllocation->save();

                    $this->logStatus(
                        "Appointment lead undersold: {$leadProcessingAllocation->lead_id}",
                        [
                            "lead_id" => $leadProcessingAllocation->lead_id
                        ]
                    );
                }
                else {
                    $this->flagFinalAllocationAttempt($leadProcessingAllocation);
                }
            }

            if($reviewLead) {
                // review lead in 2 hours or tomorrow (final attempt)
                dispatch((new ReviewLeadJob($leadProcessingAllocation, $ignoreCompanyIds, $remainingLegs, $isApptLead))->delay($delay));

                $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_DELIVER_AT} = Carbon::now('UTC')->addSeconds($delay);
                $leadProcessingAllocation->save();

                $this->logStatus(
                    "Scheduled " . ($finalAttempt ? "final" : "another") . " 'Verified' lead allocation attempt: {$leadProcessingAllocation->lead_id}",
                    [
                        "lead_id" => $leadProcessingAllocation->lead_id,
                        "delay" => $delay
                    ]
                );
            }
        }
        else if($isApptLead) {
            LeadUndersoldEvent::dispatch($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->processing_scenario);

            DispatchPubSubEvent::dispatch(
                EventCategory::LEADS,
                EventName::LEAD_UNDERSOLD,
                ["lead_id" => $leadProcessingAllocation->lead_id, "initiator_id" => $leadProcessingAllocation->leadProcessor?->id ?? 0]
            );

            $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_DELIVER_AT} = LeadProcessingAllocation::BLANK_DELIVERY;
            $leadProcessingAllocation->save();

            $this->logStatus(
                "Appointment lead undersold: {$leadProcessingAllocation->lead_id}",
                [
                    "lead_id" => $leadProcessingAllocation->lead_id
                ]
            );
        }

        // unlock lead
        $this->releaseLead($lead, $leadProcessingAllocation->leadProcessor);

        return true;
    }

    /**
     * @param EloquentQuote $lead
     * @param string $leadStatus
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @return void
     * @throws Throwable
     */
    protected function attemptLeadStatusUpdate(
        EloquentQuote $lead,
        string $leadStatus,
        LeadProcessingAllocation $leadProcessingAllocation
    ): void
    {
        if($lead->{EloquentQuote::STATUS} != $leadStatus) {
            //(╯°□°)╯︵ ┻━┻
            for($i = 0; $i < 3; $i++) {
                try {
                    $this->logStatus(
                        "Attempting to update lead status to $leadStatus: {$i}",
                        [
                            "lead_id" => $lead->{EloquentQuote::ID},
                            "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                        ]
                    );

                    $this->repository->updateLeadStatus($lead->{EloquentQuote::REFERENCE}, $leadStatus);

                    $lead->refresh();

                    if($lead->{EloquentQuote::STATUS} === $leadStatus) {
                        $this->logStatus(
                            "Update to $leadStatus succeeded: {$i}",
                            [
                                "lead_id" => $lead->{EloquentQuote::ID},
                                "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                            ]
                        );

                        break;
                    }

                    $this->logStatus(
                        "Update to $leadStatus failed: {$i}",
                        [
                            "lead_id" => $lead->{EloquentQuote::ID},
                            "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                        ]
                    );
                }
                catch(Throwable $e) {
                    $this->logStatus(
                        "Update to $leadStatus error: {$i}",
                        [
                            "lead_id" => $lead->{EloquentQuote::ID},
                            "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                        ]
                    );

                    if($i >= 2) {
                        throw $e;
                    }
                }
            }
        }

        if($lead->{EloquentQuote::STATUS} != $leadStatus) {
            throw new LeadProcessingException(
                "Lead ID: {$lead->{EloquentQuote::ID}} Status Update Failed",
                "Failed setting lead to {$leadStatus}: {$lead->{EloquentQuote::ID}}",
            );
        }
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @return bool
     */
    protected function processCompaniesNotAvailableRoofing(LeadProcessingAllocation $leadProcessingAllocation): bool
    {
        if(!$this->isAppointmentLead($leadProcessingAllocation)) {
            $sellRes = $this->deliverLeadToAggregator($leadProcessingAllocation);

            if(!$sellRes) {
                $this->repository->updateLeadStatus($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE}, EloquentQuote::VALUE_STATUS_NO_COMPANIES);
            }
        }
        else {
            $this->repository->updateLeadStatus($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE}, EloquentQuote::VALUE_STATUS_NO_COMPANIES);
        }

        $this->releaseLead($leadProcessingAllocation->lead, $leadProcessingAllocation->leadProcessor);

        return true;
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @return bool
     */
    protected function processAvailableCompanies(
        LeadProcessingAllocation $leadProcessingAllocation,
        array                    $ignoreCompanyIds = [],
        ?int                     $remainingLegs = null
    ): bool
    {
        /** @var LeadCampaignSalesTypeConfiguration[]|Collection $bestSalesTypeConfigurations */
        $bestSalesTypeConfigurations = $this->leadCampaignAvailabilityService->getBestOptionSalesTypeConfigurationsExcludingUnverified(
            $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
            $ignoreCompanyIds,
            [],
            false,
            $remainingLegs,
            $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::FIELD_CONTACT_REQUESTS}
        );

        $this->bestRevenueRecordingService->updateRevenueScenario($leadProcessingAllocation->lead, $bestSalesTypeConfigurations);

        if ($bestSalesTypeConfigurations->count() === 0) {
            $this->logStatus("Best revenue scenario returned no companies for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id, "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);
            return false;
        }

        $companiesAllEligible = $this->companiesAllEligible(
            $leadProcessingAllocation,
            $bestSalesTypeConfigurations,
            $this->determineLeadAgeInDays($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}) > 0
        );

        if ($companiesAllEligible) {
            $this->deliverLeadToCampaigns($leadProcessingAllocation, $bestSalesTypeConfigurations);
        }
        else {
            $this->logStatus("Companies are currently not eligible for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id, "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}]);
            $this->processNotAllCompaniesEligible($leadProcessingAllocation, $bestSalesTypeConfigurations, $ignoreCompanyIds, $remainingLegs);
        }

        return true;
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param LeadCampaignSalesTypeConfiguration|Collection $bestSalesTypeConfigurations
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @return void
     */
    protected function processNotAllCompaniesEligible(
        LeadProcessingAllocation $leadProcessingAllocation,
                                 $bestSalesTypeConfigurations,
        array                    $ignoreCompanyIds = [],
        ?int                     $remainingLegs = null
    ): void
    {
        $lead = $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD};

        $isApptLead = $this->isAppointmentLead($leadProcessingAllocation);

        if($this->determineLeadAgeInDays($lead) < 2) {
            $delay = $this->getReattemptDelay($lead, $bestSalesTypeConfigurations);

            dispatch((new AllocateLeadJob($leadProcessingAllocation, false, $ignoreCompanyIds, $remainingLegs))->delay($delay));

            $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_DELIVER_AT} = Carbon::now('UTC')->addSeconds($delay);
            $leadProcessingAllocation->save();

            $this->logStatus(
                "Scheduled another lead allocation attempt: {$leadProcessingAllocation->lead_id}",
                [
                    "lead_id" => $leadProcessingAllocation->lead_id,
                    "delay" => $delay
                ]
            );
        }
        else if($isApptLead) {
            LeadUndersoldEvent::dispatch($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->processing_scenario);

            DispatchPubSubEvent::dispatch(
                EventCategory::LEADS,
                EventName::LEAD_UNDERSOLD,
                ["lead_id" => $leadProcessingAllocation->lead_id, "initiator_id" => $leadProcessingAllocation->leadProcessor?->id ?? 0]
            );

            $this->logStatus(
                "Appointment lead undersold: {$leadProcessingAllocation->lead_id}",
                [
                    "lead_id" => $leadProcessingAllocation->lead_id
                ]
            );

            $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_DELIVER_AT} = LeadProcessingAllocation::BLANK_DELIVERY;
            $leadProcessingAllocation->save();
        }
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @return bool
     */
    protected function deliverLeadToAggregator(LeadProcessingAllocation $leadProcessingAllocation): bool
    {
        // deliver to aggregators
        return $this->leadAggregatorsService->sellToAggregators($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->leadProcessor->user->legacy_user_id);
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param Collection $salesTypeConfigurations
     * @param bool $attemptNextBestRevenueScenarioOnError
     * @return bool
     */
    protected function deliverLeadToCampaigns(
        LeadProcessingAllocation $leadProcessingAllocation,
        Collection               $salesTypeConfigurations,
        bool                     $attemptNextBestRevenueScenarioOnError = true
    ): bool
    {
        // If lead isn't allocated at this point, set to allocated
        // This scenario occurs when lead is set to be sold via Review Lead Job
        if ($leadProcessingAllocation->lead->status !== EloquentQuote::VALUE_STATUS_ALLOCATED) {
            $this->repository->updateLeadStatus($leadProcessingAllocation->lead->uuid, EloquentQuote::VALUE_STATUS_ALLOCATED);
        }

        // get user regardless of processor deletion status
        $processingUser = $leadProcessingAllocation->leadProcessor()->withTrashed()->first()?->user;

        // do final dupe check before allocation
        $dupeCompanyIds = $this->getAllocatedDuplicateCompanies($leadProcessingAllocation->lead)
                               ->values()
                               ->toArray();

        if (count($dupeCompanyIds) > 0) {
            $this->logStatus(
                "Pre-allocation check - Duplicate allocated companies found for lead: {$leadProcessingAllocation->lead_id}",
                [
                    "lead_id"             => $leadProcessingAllocation->lead_id,
                    "companies"           => implode(',', $dupeCompanyIds),
                    "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                ]
            );

            $configIdToCompanyId = LeadCampaignSalesTypeConfiguration::query()
                ->whereIn(LeadCampaignSalesTypeConfiguration::ID, $salesTypeConfigurations->pluck(LeadCampaignSalesTypeConfiguration::ID)->toArray())
                ->with(LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN)
                ->get()
                ->pluck(LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN . '.' . LeadCampaign::COMPANY_ID, LeadCampaignSalesTypeConfiguration::ID)
                ->toArray();

            $salesTypeConfigurations = $salesTypeConfigurations->keyBy(LeadCampaignSalesTypeConfiguration::ID);
            foreach ($salesTypeConfigurations->pluck(LeadCampaignSalesTypeConfiguration::ID)->toArray() as $configId){
                if(in_array($configIdToCompanyId[$configId], $dupeCompanyIds)){
                    $salesTypeConfigurations->forget($configId);
                }
            }
            $salesTypeConfigurations = $salesTypeConfigurations->values();

            if ($salesTypeConfigurations->count() < 1) {
                $this->logStatus(
                    "No remaining companies for allocation",
                    [
                        "lead_id"             => $leadProcessingAllocation->lead_id,
                        "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                    ]
                );

                $processor = $leadProcessingAllocation->leadProcessor()->withTrashed()->first();
                return $this->releaseLead($leadProcessingAllocation->lead, $processor);

            }

        }


        // deliver to each campaign
        $results = $this->leadDeliveryService->saveAndDeliverQuoteCompany(
            $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
            $this->getSelectedQuoteCompanies($salesTypeConfigurations),
            $processingUser?->legacy_user_id, // job will go back and add admin user for logging purposes
            false,
            $leadProcessingAllocation->processing_scenario
        );

        $this->logStatus(
            "Successfully delivered quote companies for lead: {$leadProcessingAllocation->lead_id}",
            [
                "lead_id" => $leadProcessingAllocation->lead_id,
                "legacy_admin_response" => $results,
                "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
            ]
        );

        // set to delivered and add scenario
        $leadProcessingAllocation->update([
            LeadProcessingAllocation::FIELD_DELIVERED           => LeadProcessingAllocation::DELIVERED,
            LeadProcessingAllocation::FIELD_DELIVER_AT          => Carbon::now(),
            LeadProcessingAllocation::FIELD_PROCESSING_SCENARIO => $leadProcessingAllocation->processing_scenario
        ]);

        $this->consumerProductRepository->updateConsumerProductStatusByLegacyLeadIdIfExists($leadProcessingAllocation->lead_id, ConsumerProduct::STATUS_ALLOCATED);

        $this->dispatcher->dispatch(new LeadAllocatedEvent(
            $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
            $leadProcessingAllocation->lead_processor_id,
            $leadProcessingAllocation->processing_scenario
        ));

        if (count($results["errors"]) > 0) {
            $this->logStatus(
                "Errors in delivery for lead: {$leadProcessingAllocation->lead_id}",
                [
                    "lead_id" => $leadProcessingAllocation->lead_id,
                    "consumer_product_id" => $leadProcessingAllocation->{LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID}
                ]
            );
        }

        // unlock lead
        // get processor regardless of deletion status
        $processor = $leadProcessingAllocation->leadProcessor()->withTrashed()->first();
        return $this->releaseLead($leadProcessingAllocation->lead, $processor);
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param Collection $salesTypeConfigurations
     * @param bool $oldLead
     * @return bool
     */
    protected function companiesAllEligible(
        LeadProcessingAllocation $leadProcessingAllocation,
        Collection $salesTypeConfigurations,
        bool $oldLead = false
    ): bool
    {
        if (!$this->timezoneOpenForLead($leadProcessingAllocation->lead)) {
            return false;
        }

        if (($this->withinTimezoneOpeningThreshold($leadProcessingAllocation) || $oldLead) && $this->withinRecencyThreshold($salesTypeConfigurations)) {
            return false;
        }

        return true;
    }

    /**
     * @param Collection $salesTypeConfigurations
     * @return bool
     */
    public function withinRecencyThreshold(Collection $salesTypeConfigurations): bool
    {
        return $this->getArtificialDelay($salesTypeConfigurations) > 0;
    }

    /**
     * @param LeadProcessingAllocation|EloquentQuote $leadProcessingAllocation
     * @return bool
     */
    public function withinTimezoneOpeningThreshold(LeadProcessingAllocation|EloquentQuote $leadProcessingAllocation): bool
    {
        return $this->leadWithinTimezoneOpeningThreshold($leadProcessingAllocation->lead);
    }

    /**
     * @param EloquentQuote $lead
     * @return bool
     */
    public function leadWithinTimezoneOpeningThreshold(EloquentQuote $lead): bool
    {
        /** @var LeadProcessingConfiguration|null $leadConfig */
        $leadConfig       = LeadProcessingConfiguration::query()->first();
        $openingThreshold = $leadConfig ? $leadConfig->time_zone_opening_delay_in_minutes : LeadProcessingConfiguration::TIME_ZONE_OPENING_DELAY_DEFAULT;

        $openingThreshold = round(($openingThreshold / self::MINUTES_IN_HOUR), 2);

        $utcOpenHour    = $this->getUTCOpenHourForLead($lead);
        $currentUTCHour = Carbon::now('UTC')->hour;

        $hourSinceOpen = $currentUTCHour - $utcOpenHour;
        if ($hourSinceOpen >= 0 && $hourSinceOpen < $openingThreshold) {
            return true;
        }

        return false;
    }

    /**
     * @param Collection $salesTypeConfigurations
     * @return int
     */
    protected function getArtificialDelay(Collection $salesTypeConfigurations): int
    {
        /** @var LeadProcessingConfiguration|null $leadConfig */
        $leadConfig       = LeadProcessingConfiguration::query()->first();
        $recencyThreshold = $leadConfig ? $leadConfig->{LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS} : LeadProcessingConfiguration::LEAD_RECENCY_THRESHOLD_DEFAULT;

        $mostRecentTimestamp = $this->getMostRecentTimestampDelivered($salesTypeConfigurations);

        if ($mostRecentTimestamp) {
            $mostRecentTimestamp = Carbon::createFromTimestamp($mostRecentTimestamp);

            $delay = $recencyThreshold - (int) Carbon::now()->diffInSeconds($mostRecentTimestamp, true);

            if ($delay > 0) {
                return $delay;
            }
        }

        return 0;
    }

    /**
     * @param Collection $salesTypeConfigurations
     * @return int|null
     */
    protected function getMostRecentTimestampDelivered(Collection $salesTypeConfigurations)
    {
        $quoteCompanies = $salesTypeConfigurations->map(function ($item) {
            return $this->quoteCompanyRepository->getMostRecentQuoteCompanyByCompanyId(LeadCampaignSalesTypeConfiguration::find($item['id'])->{LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN}->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::COMPANY_ID});
        });

        return $quoteCompanies
            ->sortByDesc(EloquentQuoteCompany::TIMESTAMP_DELIVERED)
            ->pluck(EloquentQuoteCompany::TIMESTAMP_DELIVERED)
            ->first();
    }

    /**
     * This function mimics what is done in the handleDeliveryButton function in AvailableLeadCampaignsForSale.vue
     *
     * @param Collection $salesTypeConfigurations
     * @return array
     */
    protected function getSelectedQuoteCompanies(Collection $salesTypeConfigurations): array
    {
        $collection = $salesTypeConfigurations->map(function ($item) {
            /** @var LeadCampaignSalesTypeConfiguration $item */

            return [
                'companyid'                        => LeadCampaignSalesTypeConfiguration::find($item['id'])->{LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN}->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::COMPANY_ID},
                'lead_sales_type_configuration_id' => $item['id'],
                'chargeable'                       => 1, // we always want to charge for leads that are auto allocated to best revenue scenario
                'non_budget_premium_sale'          => $item['non_budget_premium_sale'] ?? false
            ];
        });

        return $collection->toArray();
    }

    public function heartbeat(EloquentQuote $lead, LeadProcessor $processor): bool
    {
        if (!$this->isLeadReservedByProcessor($lead, $processor))
            return false;

        if (!$this->repository->hasHeartbeat($lead, $processor))
            dispatch((new CheckHeartbeatJob($lead->quoteid, $processor->id))->delay(self::DELAY_5_MINUTES));

        $this->repository->processHeartbeat($lead, $processor);

        return true;
    }

    /**
     * Handles removed a heartbeat for a lead if it exists.
     *
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @return void
     */
    protected function removeHeartbeat(EloquentQuote $lead, LeadProcessor $processor): void
    {
        $this->repository->removeHeartbeat($lead, $processor);
    }

    /**
     * This function tells us the soonest time that a lead can be allocated based on it's timezone
     *
     * @param EloquentQuote $lead
     * @return Carbon
     */
    public function timeLeadCanBeDeliveredUTC(EloquentQuote $lead): Carbon
    {
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->getTimezoneOffsetFromZipcode(
            $lead->address->zipcode
        );

        $timezoneConfig = $this->repository->getTimezoneConfigurationByStandardUTCOffset($standardUTCOffset);

        $localOpen  = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR};
        $localClose = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR};

        $dstAdjustment = $this->getDSTAdjustment($observingDST);

        $utcOpen  = $localOpen - $standardUTCOffset - $dstAdjustment;
        $utcClose = $localClose - $standardUTCOffset - $dstAdjustment;

        return $this->getCarbonTimeFromOpenAndClose($utcOpen, $utcClose);
    }

    /**
     * @param bool $observingDST
     * @return int
     */
    public function getDSTAdjustment(bool $observingDST): int
    {
        return ($this->queueRepository->isActiveDST() && $observingDST) ? 1 : 0;
    }

    /**
     * @param EloquentQuote $lead
     * @return int
     */
    public function getUTCOpenHourForLead(EloquentQuote $lead): int
    {
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->getTimezoneOffsetFromZipcode(
            $lead->address->zipcode
        );

        $timezoneConfig = $this->repository->getTimezoneConfigurationByStandardUTCOffset($standardUTCOffset);
        $localOpen      = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR};

        $dstAdjustment = $this->getDSTAdjustment($observingDST);

        return $localOpen - $standardUTCOffset - $dstAdjustment;
    }

    /**
     * @param int $utcOpen
     * @param int $utcClose
     * @return Carbon
     */
    protected function getCarbonTimeFromOpenAndClose(int $utcOpen, int $utcClose): Carbon
    {
        $utc = Carbon::now('UTC');

        if ($utc->hour < $utcOpen) {
            $utc->hour   = $utcOpen;
            $utc->minute = 0;
            $utc->second = 0;
        } elseif ($utc->hour >= $utcClose) {
            $utc->addDay();
            $utc->hour   = $utcOpen;
            $utc->minute = 0;
            $utc->second = 0;
        }

        return $utc;
    }

    /**
     * This function tells us if the timezone is currently open for the given lead
     *
     * @param EloquentQuote $lead
     * @param Carbon|null $currentTimeOverride
     * @return bool
     */
    public function timezoneOpenForLead(EloquentQuote $lead, ?Carbon $currentTimeOverride = null): bool
    {
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->getTimezoneOffsetFromZipcode(
            $lead->address->zipcode
        );

        $timezoneConfig = $this->repository->getTimezoneConfigurationByStandardUTCOffset($standardUTCOffset);

        $localOpen  = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR};
        $localClose = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR};

        $dstAdjustment = $this->getDSTAdjustment($observingDST);

        $utcOpen = Carbon::now($standardUTCOffset)
                         ->setTime($localOpen - $dstAdjustment, 0)
                         ->setTimezone("UTC");

        $utcClose = Carbon::now($standardUTCOffset)
                         ->setTime($localClose - $dstAdjustment, 0)
                         ->setTimezone("UTC");

        $utc = $currentTimeOverride ? $currentTimeOverride->utc() : Carbon::now('UTC');

        return $utc->between($utcOpen, $utcClose);
    }

    /**
     * Determine lead age in days. The day the lead comes in is considered day zero.
     *
     * @param EloquentQuote $lead
     * @return int
     */
    public function determineLeadAgeInDays(EloquentQuote $lead): int
    {
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->getTimezoneOffsetFromZipcode(
            $lead->{EloquentQuote::RELATION_ADDRESS}->{EloquentAddress::ZIP_CODE}
        );

        $offset = $standardUTCOffset - $this->getDSTAdjustment($observingDST);

        $today = Carbon::today($offset);
        $leadTime = Carbon::createFromTimestamp(
            $lead->{EloquentQuote::TIMESTAMP_ADDED},
            CarbonTimeZone::createFromHourOffset($offset)
        )->startOfDay();

        return (int) $today->diffInDays($leadTime, true);
    }

    /**
     * @param EloquentQuote $lead
     * @param Collection|null $bestSalesTypeConfigurations
     * @return int
     */
    protected function getReattemptDelay(EloquentQuote $lead, ?Collection $bestSalesTypeConfigurations = null): int
    {
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->getTimezoneOffsetFromZipcode(
            $lead->{EloquentQuote::RELATION_ADDRESS}->{EloquentAddress::ZIP_CODE}
        );
        $dstAdjustment = $this->getDSTAdjustment($observingDST);
        $localNow = Carbon::now($standardUTCOffset + $dstAdjustment);
        $delay = $bestSalesTypeConfigurations ? $this->getArtificialDelay($bestSalesTypeConfigurations) : self::DELAY_2_HOURS;
        $delayedTime = $localNow->copy()->addSeconds($delay);
        return $this->timezoneOpenForLead($lead, $delayedTime) ? $delay : (int) $localNow->diffInSeconds($localNow->copy()->next(self::NEXT_DAY_REATTEMPT_TIME), true);
    }

    /**
     * @param string $zipcode
     * @return array
     */
    #[ArrayShape(['standard_utc_offset' => "int", 'observing_dst' => "bool"])]
    public function getTimezoneOffsetFromZipcode(string $zipcode): array
    {
        $zipCode = $this->zipCodeRepository->getZipCode($zipcode);

        if ($zipCode) {
            $standardUTCOffset = (int) $zipCode->utc;
            $observingDST = $zipCode->dst === 'Y';
        }
        else {
            $standardUTCOffset = self::FALLBACK_UTC_OFFSET;
            $observingDST = false;
        }

        return [
            'standard_utc_offset' => $standardUTCOffset,
            'observing_dst' => $observingDST
        ];
    }

    /**
     * This function will determine if a lead is Standard or Premium
     *
     * @param EloquentQuote $lead
     * @return string
     */
    public function evaluateLeadType(EloquentQuote $lead): string
    {
        if (!$lead->{EloquentQuote::SOLAR_LEAD}) {
            return LeadType::KEY_VALUE_STANDARD;
        }
        $electricUsage = $lead->{EloquentQuote::ELECTRIC_COST};
        if (
            (!empty($electricUsage) && intval($electricUsage) >= self::PREMIUM_ELECTRICAL_USAGE) ||
            !empty($lead->{EloquentQuote::BEST_TIME_TO_CALL}) ||
            !empty($lead->{EloquentQuote::BEST_TIME_TO_CALL_OTHER})
        ) {
            return LeadType::KEY_VALUE_PREMIUM;
        }
        return LeadType::KEY_VALUE_STANDARD;

    }

    /**
     * This function will determine if a lead is Standard or Premium, and update the lead accordingly
     *
     * @param EloquentQuote $lead
     * @return string
     */
    public function evaluateLeadTypeAndUpdate(EloquentQuote $lead): string
    {
        $type = $this->evaluateLeadType($lead);
        if ($type === LeadType::KEY_VALUE_PREMIUM) {
            $this->repository->updateLeadType($lead->{EloquentQuote::REFERENCE}, $type);
        }
        return $type;
    }

    /**
     * @param Collection $leads
     * @return bool
     * @throws Exception
     */
    public function bulkEvaluateLeadTypeAndUpdate(Collection $leads): bool
    {
        $leadTypes = [];

        /** @var EloquentQuote $lead */
        foreach($leads as $lead) {
            $leadTypes[$lead->{EloquentQuote::REFERENCE}] = $lead->{EloquentQuote::SOLAR_LEAD} ? LeadType::KEY_VALUE_PREMIUM : LeadType::KEY_VALUE_STANDARD;
        }

        return $this->repository->bulkUpdateLeadType($leadTypes);
    }

    /**
     * @param EloquentQuote $lead
     * @param LeadProcessor $processor
     * @param string $reason
     * @param string|null $bestTimeToContact ,
     * @param string|null $comment
     * @return bool
     *
     * @throws Exception
     */
    public function approveLead(
        EloquentQuote $lead,
        LeadProcessor $processor,
        string        $reason,
        string        $bestTimeToContact = null,
        ?string       $comment = null
    ): bool
    {
        //Do not attempt lead allocation that is canceled
        if ($lead->status === EloquentQuote::VALUE_STATUS_CANCELLED) {
            logger()->error("Lead allocation was failed as the lead status is canceled for legacy lead: {$lead->quoteid}");

            return true;
        }

        // This will have a value if QA qualifies via conversation. Is necessary to upgrade lead to Premium
        if ($bestTimeToContact) {
            $this->updateBestTimeToContact($lead, $bestTimeToContact);
        }

        if(!$this->markLeadAsAllocated($lead, $processor, $reason, $comment)) {
            return false;
        }

        /** @var int|null $consumerProductId */
        $consumerProductId = $this->repository->getConsumerProductIdForLead($lead->{EloquentQuote::ID}, $processor);

        $this->repository->reserveLeadToSystem($lead->{EloquentQuote::ID}, $consumerProductId);
        $this->dispatcher->dispatch(new LeadReservedEvent($lead->{EloquentQuote::REFERENCE}, self::LEAD_LOCKED_BY_SYSTEM));

        $deliverAt                = $this->timeLeadCanBeDeliveredUTC($lead);
        $leadProcessingAllocation = $this->queueRepository->addLeadToAllocationQueue($lead, $processor, $reason, $deliverAt, $consumerProductId);

        $this->evaluateLeadTypeAndUpdate($lead);
        $this->repository->updateLeadClassification($lead->{EloquentQuote::REFERENCE}, EloquentQuote::CLASSIFICATION_VERIFIED);

        if ($this->timezoneOpenForLead($lead)) {
            dispatch((new AllocateLeadJob($leadProcessingAllocation)));
        } else {
            $delay = (int) Carbon::now('UTC')->diffInSeconds($deliverAt, true);
            dispatch((new AllocateLeadJob($leadProcessingAllocation))->delay($delay));
        }

        return true;
    }

    /**
     * @param EloquentQuote $lead
     * @param string $bestTimeToContact
     * @return void
     */
    protected function updateBestTimeToContact(EloquentQuote $lead, string $bestTimeToContact)
    {
        $this->repository->updateBestTimeToContact($lead->{EloquentQuote::REFERENCE}, $bestTimeToContact);
    }

    /**
     * Handles returning the minimum review time.
     *
     * @return int
     */
    public function getMinimumReviewTime(): int
    {
        if ($config = $this->repository->getLeadProcessingConfiguration()) {
            return $config->{LeadProcessingConfiguration::FIELD_MINIMUM_REVIEW_TIME};
        }

        return LeadProcessingConfiguration::MINIMUM_REVIEW_TIME_DEFAULT;
    }

    /**
     * Function to evaluate if a lead is undersold (primarily due to a delivery bounce-back),
     * and attempt to allocate the remaining legs to the 'Next Best Revenue Scenario'
     *
     * @param int $leadId
     * @return bool
     * @throws Exception
     */
    public function allocateRemainingLegs(int $leadId): bool
    {
        if (!$leadProcessingAllocation = $this->repository->getLeadProcessingAllocationByLeadId($leadId))
            return false;

        $this->logStatus("Allocating remaining legs: $leadId", ["lead_id" => $leadId]);

        $underSold = true;
        $attempts  = 0;
        while ($underSold) {
            if ($attempts > self::ALLOCATION_REATTEMPT_LIMIT) {
                break;
            }
            $attempts++;

            $leadConsumerProduct = $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_CONSUMER_PRODUCT};

            // Check for companies that have received a duplicate of this lead
            $duplicateCompanyIds = $this->getAllocatedDuplicateCompanies($leadProcessingAllocation->lead)->values()->toArray();

            $failedQuoteCompanies  = $this->repository->getUndeliveredQuoteCompanies($leadId);
            $failedCompanyIds      = $failedQuoteCompanies->pluck(EloquentQuoteCompany::COMPANY_ID)->values()->toArray();
            $soldAppointmentCompanies = array_values($this->repository->getSoldAppointmentCompanies($leadId));

            $ignoreCompanyIds = array_merge($duplicateCompanyIds, $failedCompanyIds, $soldAppointmentCompanies);

            $remainingLegs = $this->isAppointmentLead($leadProcessingAllocation) ? $this->repository->getRemainingLegsForAppointmentLead($leadId) : null;

            $contactRequests = $leadConsumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS};

            if(!is_null($remainingLegs)) {
                $soldAppts = $leadConsumerProduct
                    ->{ConsumerProduct::RELATION_APPOINTMENT_PROCESSING_ALLOCATIONS}
                    ->filter(function($apa) {
                        return $apa->{AppointmentProcessingAllocation::FIELD_ALLOCATED} || $apa->{AppointmentProcessingAllocation::FIELD_CANCELLED_REJECTED};
                    })
                    ->count();

                if($remainingLegs <= 0) {
                    $this->logStatus(
                        __METHOD__.": No more legs remaining",
                        [
                            "lead_consumer_product_id" => $leadConsumerProduct->{ConsumerProduct::FIELD_ID}
                        ]
                    );

                    continue;
                }
                else if($remainingLegs > ($contactRequests - $soldAppts)) {
                    $this->logStatus(
                        __METHOD__.": Remaining legs greater than contact requests minus sold appointments",
                        [
                            "lead_consumer_product_id" => $leadConsumerProduct->{ConsumerProduct::FIELD_ID}
                        ]
                    );

                    $remainingLegs = $contactRequests - $soldAppts;
                }
            }

            $successfulCampaignIds = $this->repository->getDeliveredQuoteCompanyCampaigns($leadId)->pluck(LeadCampaign::ID)->values()->toArray();

            // get new BRS excluding failed companies, excluding dupe companies, including delivered companies
            $nextBestSalesTypeConfigurations = $this->leadCampaignAvailabilityService->getBestOptionSalesTypeConfigurationsExcludingUnverified(
                $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
                $ignoreCompanyIds,
                $successfulCampaignIds,
                false,
                $remainingLegs,
                $contactRequests
            );

            // re-attempt delivery
            $results = $this->leadDeliveryService->saveAndDeliverQuoteCompanyForBounceBack(
                $leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
                $this->getSelectedQuoteCompanies($nextBestSalesTypeConfigurations),
                $leadProcessingAllocation->leadProcessor->user->legacy_user_id,
                false,
                $leadProcessingAllocation->processing_scenario,
                $failedQuoteCompanies->pluck(EloquentQuoteCompany::ID)->values()->toArray()// prevent failed quote companies from being re-attempted
            );
            if ($nextBestSalesTypeConfigurations->count() === count($successfulCampaignIds)) {
                break;
            }

            // check for errors
            $underSold = collect($results['errors'])->count() > 0;
        }

        // delete failed quote companies
        $this->repository->deleteFailedQuoteCompanies($leadProcessingAllocation->lead->uuid);

        $allocationCount = $this->repository->getDeliveredQuoteCompanies($leadId)->count();
        // if no successful allocations, mark 'under review'
        if ( $allocationCount === 0) {
            // change allocation row to undelivered
            $leadProcessingAllocation->update([LeadProcessingAllocation::FIELD_DELIVERED => false]);
            Cache::remember("unsold-or-undersold-lead-{$leadProcessingAllocation->lead_id}", self::EXPIRY_1_DAY, function() use ($leadProcessingAllocation) {
                LeadUnsoldEvent::dispatch($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->processing_scenario);
                return true;
            });
            return false;
        }elseif ( $allocationCount < $leadProcessingAllocation->lead->numberofquotes ){
            Cache::remember("unsold-or-undersold-lead-{$leadProcessingAllocation->lead_id}", self::EXPIRY_1_DAY, function() use ($leadProcessingAllocation) {
                LeadUndersoldEvent::dispatch($leadProcessingAllocation->lead->uuid, $leadProcessingAllocation->processing_scenario);
                return true;
            });
        }
        return true;
    }

    /**
     * @param int $leadId
     * @param int|null $consumerProductId
     * @return bool
     */
    public function createInitialLead(int $leadId, int $consumerProductId = null): bool
    {
        /** @var LeadProcessingInitial $initialQueue */
        $initialQueue = LeadProcessingInitial::updateOrCreate(
            [
                LeadProcessingInitial::FIELD_LEAD_ID => $leadId
            ],
            [
                LeadProcessingInitial::FIELD_LEAD_ID             => $leadId,
                LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId
            ]
        );

        return !empty($initialQueue);
    }

    /**
     * @param int $leadId
     * @param string $reason
     * @param LeadProcessor $processor
     * @param int|null $consumerProductId
     * @return bool
     */
    public function createPendingReviewLead(
        int $leadId,
        string $reason,
        LeadProcessor $processor,
        int $consumerProductId = null,

    ): bool
    {
        LeadProcessingPendingReview::query()->updateOrCreate([
            LeadProcessingPendingReview::FIELD_LEAD_ID => $leadId,
            LeadProcessingPendingReview::FIELD_USER_ID => $processor->id,
            LeadProcessingPendingReview::FIELD_REASON => $reason
        ], [
            LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
        ]);

        return true;
    }

    /**
     * Logs a status message of the job.
     *
     * This only runs if the shouldLog value is set to true.
     * @see LeadProcessingService::$shouldLog
     *
     * @param string $message
     * @param array $payload
     * @return void
     */
    public function logStatus(string $message, array $payload = []): void
    {
        try {
            if($this->shouldLog) {
                $payload['env'] = App::environment();

                RecordMonitoringLog::dispatch($message, $payload);
            }
        } catch (Exception $e) {}
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param string $salesType
     * @param bool $bypassCompaniesAvailableCheck
     * @return bool
     * @throws Exception
     */
    public function attemptToSellAsOptionalSalesType(LeadProcessingAllocation $leadProcessingAllocation, string $salesType = EloquentQuote::CLASSIFICATION_UNVERIFIED, bool $bypassCompaniesAvailableCheck = false): bool
    {
        switch ($salesType) {
            case EloquentQuote::CLASSIFICATION_UNVERIFIED:

                // check for available unverified budgets, and mark lead as allocated
                if($bypassCompaniesAvailableCheck || $this->leadCampaignAvailabilityService->companiesWithAvailableUnverifiedBudgetCount($leadProcessingAllocation->lead->uuid) > 0){
                    if(!$this->queueRepository->changeQueues($leadProcessingAllocation->lead, $leadProcessingAllocation->leadProcessor, LeadProcessingQueueRepository::STATUS_ALLOCATED, '')) {
                        return false;
                    }
                }else{
                    return false;
                }

                $this->logStatus("Scheduling 'Unverified' allocation for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);

                $deliverAt = $this->timeLeadCanBeDeliveredUTC($leadProcessingAllocation->lead);

                if ($this->timezoneOpenForLead($leadProcessingAllocation->lead)) {
                    dispatch(new AllocateLeadJob($leadProcessingAllocation, true));
                } else {
                    $delay = (int) Carbon::now('UTC')->diffInSeconds($deliverAt, true);
                    dispatch((new AllocateLeadJob($leadProcessingAllocation, true))->delay($delay));
                }

            // todo: case EloquentQuote::CLASSIFICATION_EMAIL_ONLY
        }

        return true;
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @return bool
     * @throws Exception
     */
    public function attemptToSellAsUnverified(LeadProcessingAllocation $leadProcessingAllocation): bool
    {
        $this->logStatus("Attempting 'Unverified' allocation for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);
        $dupeCompanyIds = $this->getAllocatedDuplicateCompanies($leadProcessingAllocation->lead)
                               ->values()
                               ->toArray();

        // see if lead is in service areas of any active campaigns
        if ($this->leadCampaignAvailabilityService->areCompaniesAvailable($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE},
            false, null, $dupeCompanyIds)) {
            // see if all valid campaigns are over budget
            $companiesWithinBudget = $this->leadCampaignAvailabilityService->companiesWithAvailableUnverifiedBudgetCount($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE});
            if ($companiesWithinBudget === 0) {
                $this->logStatus("No companies with 'Unverified' budget for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);
                // No 'Unverified' budget available, mark as 'Under Review'
                $this->queueRepository->changeQueues($leadProcessingAllocation->lead, $leadProcessingAllocation->leadProcessor, LeadProcessingQueueRepository::STATUS_UNDER_REVIEW, ''); //todo: define appropriate reason
                $this->releaseLead($leadProcessingAllocation->lead, $leadProcessingAllocation->leadProcessor);
                return false;
            }

            if ($this->processUnverifiedAvailableCompanies($leadProcessingAllocation, $dupeCompanyIds)) {
                $this->logStatus("Successfully processed lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);
                return true;
            }
        }

        $this->logStatus("No 'Unverified' companies available for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);

        return false;
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param array $ignoreCompanyIds
     * @return bool
     * @throws Exception
     */
    protected function processUnverifiedAvailableCompanies(
        LeadProcessingAllocation $leadProcessingAllocation,
        array                    $ignoreCompanyIds = []
    ): bool
    {
        /** @var LeadCampaignSalesTypeConfiguration[]|Collection $bestSalesTypeConfigurations */
        $bestSalesTypeConfigurations = $this->leadCampaignAvailabilityService->getBestOptionSalesTypeConfigurationsOnlyUnverified($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::REFERENCE}, $ignoreCompanyIds);

        if ($bestSalesTypeConfigurations->count() === 0) {
            $this->logStatus("Best 'Unverified' revenue scenario returned no companies for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);
            return false;
        }

        $companiesAllEligible = $this->companiesAllEligible(
            $leadProcessingAllocation,
            $bestSalesTypeConfigurations,
            $this->determineLeadAgeInDays($leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}) > 0
        );

        if ($companiesAllEligible) {
            $this->deliverLeadToCampaigns($leadProcessingAllocation, $bestSalesTypeConfigurations, false);
        } else {
            $this->logStatus("Companies are currently not eligible for lead: {$leadProcessingAllocation->lead_id}", ["lead_id" => $leadProcessingAllocation->lead_id]);
            // todo: Determine how we want to handle for ineligible companies.
            // As of right now, we are attempting to allocate only one time before dropping the lead.
            $this->queueRepository->changeQueues($leadProcessingAllocation->lead, $leadProcessingAllocation->leadProcessor, LeadProcessingQueueRepository::STATUS_UNDER_REVIEW, ''); //todo: define appropriate reason
            $this->releaseLead($leadProcessingAllocation->lead, $leadProcessingAllocation->leadProcessor);
            return false;
        }

        return true;
    }

    /**
     * Flag the LPA as final attempt. This will prevent any future instances of 'ReviewLeadJob' from being created.
     * On conclusion of the final attempt, if unsuccessful, we will attempt alternative delivery methods (i.e. 'Unverified')
     *
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @return void
     */
    private function flagFinalAllocationAttempt(LeadProcessingAllocation $leadProcessingAllocation): void
    {
        $leadProcessingAllocation->final_attempt = true;
        $leadProcessingAllocation->save();
    }

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @return bool
     */
    public function isAppointmentLead(LeadProcessingAllocation $leadProcessingAllocation): bool
    {
        return $leadProcessingAllocation
                ->{LeadProcessingAllocation::RELATION_CONSUMER_PRODUCT}
                ->{ConsumerProduct::RELATION_APPOINTMENT_PROCESSING_ALLOCATIONS}
                ->where(AppointmentProcessingAllocation::FIELD_ALLOCATED, true)
                ->count() > 0;
    }

    /**
     * @param string|null $reason
     * @return string
     */
    private function appendZeroContactsReason(?string $reason = ''): string
    {
        return str_contains($reason, AttemptConsumerProjectAllocationJob::REASON_ZERO_REQUESTS)
            ? $reason
            : "$reason, " . AttemptConsumerProjectAllocationJob::REASON_ZERO_REQUESTS;
    }
}
