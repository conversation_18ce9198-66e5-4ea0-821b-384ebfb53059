<?php

namespace App\Services\LeadProcessing;

use App\Contracts\Services\Communication\CommunicationContract;
use App\Models\Call;
use App\Models\LeadProcessingCommunication;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\CommunicationRepository;
use App\Repositories\LeadProcessing\LeadCommunicationRepository;
use App\Transformers\LeadProcessing\CommunicationTransformer;

class LeadCommunicationService
{

    protected LeadCommunicationRepository      $leadCommunicationRepository;
    protected LeadProcessorNotificationService $notificationService;
    protected CommunicationTransformer $leadCommunicationTransformer;
    protected CommunicationContract    $communicationService;

    /**
     * @param LeadCommunicationRepository      $leadCommunicationRepository
     * @param CommunicationContract        $communicationService
     * @param LeadProcessorNotificationService $notificationService
     * @param CommunicationTransformer         $leadCommunicationTransformer
     */
    public function __construct(
        LeadCommunicationRepository      $leadCommunicationRepository,
        CommunicationContract            $communicationService,
        LeadProcessorNotificationService $notificationService,
        CommunicationTransformer         $leadCommunicationTransformer,
        protected CommunicationRepository $communicationRepository
    )
    {
        $this->leadCommunicationRepository  = $leadCommunicationRepository;
        $this->communicationService         = $communicationService;
        $this->notificationService          = $notificationService;
        $this->leadCommunicationTransformer = $leadCommunicationTransformer;
    }

    /**
     * @param int $consumerProductId
     * @param int $processingHistoryId
     * @param int $processorId
     * @return bool
     */
    public function recordContactAttempt(
        int $consumerProductId,
        int $processingHistoryId,
        int $processorId
    ): bool
    {
        return $this->leadCommunicationRepository->createActionedRecord($consumerProductId, $processingHistoryId, $processorId);
    }

    public function createOutboundSMS(
        int           $consumerProductId,
        LeadProcessor $processor,
        string        $toNumber,
        string        $body
    ): ?LeadProcessingCommunication
    {
        $processorPhone = $this->leadCommunicationRepository->getPhoneNumberForProcessor($processor);
        if (!$processorPhone) return null;

        $smsReference = $this->communicationService->sendSMS($processorPhone->phone, $toNumber, $body);
        return $this->leadCommunicationRepository->createOutboundSMS($consumerProductId, $processor, $processorPhone->id, $toNumber, $body, $smsReference);
    }

    public function createInboundSMS(
        string $toNumber,
        string $fromNumber,
        string $reference,
        string $body
    ): ?LeadProcessingCommunication
    {
        $consumerProductId = $this->leadCommunicationRepository->getConsumerProductIdByOtherNumber($fromNumber);
        $leadProcessor = $this->leadCommunicationRepository->getLeadProcessorByOtherNumber($fromNumber);

        $communication = $this->leadCommunicationRepository->updateOrCreateInboundSMS(
            $leadProcessor,
            $toNumber,
            $fromNumber,
            $reference,
            $body,
            $consumerProductId
        );

        if ($leadProcessor && $consumerProductId) {
            /** @var EloquentQuote $lead */
            $lead = EloquentQuote::query()->find($consumerProductId);

            $this->notificationService->createNotifications(
                [$leadProcessor->id],
                'New Message',
                'SMS: ' . $lead->{EloquentQuote::FIRST_NAME} . ' ' . $lead->{EloquentQuote::LAST_NAME},
                $consumerProductId
            );
        }

        if ($leadProcessor)
            $this->notificationService->broadcastEvent("new-message", ["private-notifications-{$leadProcessor->user_id}"], $this->leadCommunicationTransformer->transformLeadProcessingCommunication($communication));

        return $communication;
    }

    /**
     * Handles returning the SMS history for a given phone number.
     * @param string $phoneNumber
     * @return array
     */
    public function getSMSHistoryByPhoneNumber(string $phoneNumber): array
    {
        $texts = $this->communicationRepository->getSMSHistoryForNumber($phoneNumber);
        return $this->leadCommunicationTransformer->transformSMSHistory($texts ?? []);
    }

    /**
     * Handles getting a lead from a phone number.
     *
     * @param string $otherNumber
     * @return array|null
     */
    public function getLeadDetails(string $otherNumber): ?array
    {
        $leadId        = $this->leadCommunicationRepository->getConsumerProductIdByOtherNumber($otherNumber);
//        $communication = $this->leadCommunicationRepository->getLeadProcessingCommunicationByOtherNumber($otherNumber);

        /** @var EloquentQuote|null $lead */
        if ($leadId === null || ($lead = EloquentQuote::query()->find($leadId)) === null)
            return null;

        // ^ $communication line above is calling a non-existent method. Pretty sure this route is dead.
        return ["lead" => [], "communication" => []];
    }

    public function createOutboundCall(
        int           $consumerProductId,
        LeadProcessor $leadProcessor,
        string        $phoneNumber,
        string        $otherNumber,
        string        $reference
    ): LeadProcessingCommunication
    {
        $phone = $this->leadCommunicationRepository->getPhoneByPhoneNumber($leadProcessor, $phoneNumber);

        return $this->leadCommunicationRepository->createOutboundCall(
            $consumerProductId,
            $leadProcessor,
            $phone->id,
            $otherNumber,
            $reference
        );
    }

    /**
     * @param LeadProcessor|null $leadProcessor
     * @param string $phoneNumber
     * @param string $otherNumber
     * @param string $reference
     * @param string $callResult
     * @return LeadProcessingCommunication
     */
    public function createInboundCall(
        ?LeadProcessor $leadProcessor,
        string         $phoneNumber,
        string         $otherNumber,
        string         $reference,
        string         $callResult
    ): LeadProcessingCommunication
    {
        $consumerProductId        = $this->leadCommunicationRepository->getConsumerProductIdByOtherNumber($otherNumber);
        $leadProcessor = $leadProcessor ?? $this->leadCommunicationRepository->getLeadProcessorByOtherNumber($otherNumber);

        return $this->leadCommunicationRepository->createInboundCall(
            $leadProcessor,
            $phoneNumber,
            $otherNumber,
            $reference,
            $callResult,
            $consumerProductId
        );
    }

    /**
     * @param string $leadProcessingCommunicationId
     * @param bool $callComplete
     * @param string|null $callResult
     * @param bool $inbound
     * @return bool
     */
    public function updateCall(
        string $leadProcessingCommunicationId,
        bool   $callComplete = false,
        string $callResult = null,
        bool   $inbound = false
    ): bool
    {
        /** @var LeadProcessingCommunication $leadProcessingCommunication */
        $leadProcessingCommunication = LeadProcessingCommunication::query()->find($leadProcessingCommunicationId);

        if ($inbound)
            switch ($callResult) {
                case Call::RESULT_MISSED:
                case Call::RESULT_BUSY:
                    $lead = $leadProcessingCommunication->lead;
                    $this->notificationService->createNotifications(
                        [$leadProcessingCommunication->{LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID}],
                        'Missed Call',
                        'You\'ve missed a call from ' . $lead->{EloquentQuote::FIRST_NAME} . ' ' . $lead->{EloquentQuote::LAST_NAME} . '.',
                        $leadProcessingCommunication->{LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID}
                    );
                    break;
                case Call::RESULT_VOICEMAIL:
                    $lead = $leadProcessingCommunication->lead;
                    $this->notificationService->createNotifications(
                        [$leadProcessingCommunication->{LeadProcessingCommunication::FIELD_LEAD_PROCESSOR_ID}],
                        'Voicemail',
                        'You have a voicemail from ' . $lead->{EloquentQuote::FIRST_NAME} . ' ' . $lead->{EloquentQuote::LAST_NAME} . '.',
                        $leadProcessingCommunication->{LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID}
                    );
                    break;
            }

        return $this->leadCommunicationRepository->updateCall(
            $leadProcessingCommunication->relation_id,
            $callComplete,
            $callResult
        );
    }
}
