<?php

namespace App\Services\LeadProcessing;

use App\Models\LeadProcessorNotification;
use App\Models\Notification;
use App\Repositories\LeadProcessing\LeadProcessorNotificationRepository;
use App\Repositories\NotificationRepository;
use App\Services\Broadcasting\PusherNotificationBroadcaster;
use App\Transformers\LeadProcessing\LeadProcessorNotificationTransformer;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class LeadProcessorNotificationService
{
    const RECENT_NOTIFICATIONS_THRESHOLD_HOURS = 48;

    protected LeadProcessorNotificationRepository $notificationRepository;
    protected LeadProcessorNotificationTransformer $transformer;

    /**
     * LeadProcessorNotificationService constructor.
     *
     * @param LeadProcessorNotificationRepository  $notificationRepository
     * @param LeadProcessorNotificationTransformer $transformer
     * @param PusherNotificationBroadcaster        $broadcaster
     */
    public function __construct(LeadProcessorNotificationRepository $notificationRepository, LeadProcessorNotificationTransformer $transformer, PusherNotificationBroadcaster $broadcaster, protected NotificationRepository $userNotificationRepository)
    {
        $this->notificationRepository = $notificationRepository;
        $this->transformer = $transformer;
    }

    /**
     * Creates an individual notification for each user specified in $leadProcessorIds
     *
     * @param array $leadProcessorIds
     * @param string $subject
     * @param string $message
     * @param int|null $leadId
     * @return bool
     */
    public function createNotifications(array $leadProcessorIds, string $subject, string $message, ?int $consumerProductId): bool
    {
        $notifications = $this->notificationRepository->createNotifications($leadProcessorIds, $subject, $message, $consumerProductId);
        if($notifications->count() > 0) {
            foreach($notifications as $notification)
                $this->dispatchNotification($notification);
            return true;
        }

        return false;
    }

    /**
     * Handles broadcasting a single event.
     *
     * @param string $event
     * @param array $channels
     * @param array $data
     * @return void
     */
    public function broadcastEvent(string $event, array $channels, array $data = []): void
    {
        $this->broadcaster->broadcast($channels, $event, $data);
    }

    /**
     * Dispatches a notification
     *
     * @param LeadProcessorNotification $notification
     */
    protected function dispatchNotification(LeadProcessorNotification $notification): void
    {
        $userId = $notification->leadProcessor->user_id;
        $channels = ["private-notifications-{$userId}"];
        $status = 'new-notification';
        $payload = $this->transformer->transformLeadProcessingNotification($notification);

        $this->broadcastEvent($status, $channels, $payload);
    }

    /**
     * Get recent notifications
     *
     * @param int $leadProcessorId
     * @return LeadProcessorNotification[]|Collection
     */
    public function getRecentProcessorNotifications(int $leadProcessorId): Collection
    {
        return $this->notificationRepository->getProcessorNotifications($leadProcessorId, self::RECENT_NOTIFICATIONS_THRESHOLD_HOURS);
    }

    /**
     * Marks a notification as read.
     *
     * @param int $id
     * @param int $processorId
     * @param NotificationRepository $userNotificationRepository
     * @return bool
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     */
    public function markNotificationAsRead(int $id, ?int $processorId, string $type = "lead-processor"): bool
    {
        $notification = $type == 'user'
            ? $this->userNotificationRepository->getNotification($id)
            : $this->notificationRepository->getNotification($id);

        if(!$notification || ($type == 'lead-processor' && $notification->lead_processor_id !== $processorId) || ($type == 'user' && $notification->user_id !== Auth::id()))
            return false;

        return $type == 'user'
            ? $this->userNotificationRepository->markAsRead($id)
            : $this->notificationRepository->markAsRead($notification);
    }

    public function markAllNotificationsAsRead(?int $processorId): bool
    {
        $this->userNotificationRepository->getUnreadNotificationsForUserQuery(Auth::id())
            ->update([
                Notification::FIELD_READ => Notification::READ
            ]);
        if($processorId)
            $this->notificationRepository->getUnreadProcessorNotificationsQuery($processorId)
                ->update([
                    LeadProcessorNotification::FIELD_READ => LeadProcessorNotification::READ
                ]);

        return true;
    }

}
