<?php

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Models\Role;

class DashboardService
{
    /**
     * @return string[]
     * @throws Exception
     */
    public function getComponentsForUser(): array
    {
        /** @var Role $role */
        $role = Auth::user()?->roles?->first()?->name;

        return match ($role) {
            "admin" => ["commission", "companies", "performance", "tasks"],
            default => [],
        };
    }
}
