<?php

namespace App\Services;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Website;
use Carbon\Carbon;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Illuminate\Support\Arr;

readonly class ConsumerService
{
    const string ALGORITHM = 'HS256';
    const string EXPIRE_AT = 'expire_at';

    public function __construct(private string $jwtSigningKey) {}

    /**
     * @param Consumer $consumer
     *
     * @return string
     */
    public function getEmailVerificationLink(Consumer $consumer): string
    {
        return rtrim(config('mail.external_links.fixr'), '/') . "/verify-email?token={$this->getConsumerToken($consumer)}";
    }

    /**
     * @param Consumer $consumer
     * @param array $consumerFields
     * @param int $expiry Expiry in minutes
     *
     * @return string
     */
    public function getConsumerToken(Consumer $consumer, array $consumerFields = [Consumer::FIELD_REFERENCE], int $expiry = 1440): string
    {
        $payload = [
            ...Arr::only($consumer->toArray(), $consumerFields),
            self::EXPIRE_AT => now()->addMinutes($expiry)->timestamp
        ];

        return JWT::encode($payload, $this->jwtSigningKey, self::ALGORITHM);
    }

    /**
     * @param string $token
     *
     * @return array|null
     */
    public function validateConsumerToken(string $token): ?array
    {
        try {
            $decoded = (array) JWT::decode($token, new Key($this->jwtSigningKey, self::ALGORITHM));

            if (now()->timestamp > Carbon::createFromTimestamp($decoded[self::EXPIRE_AT])->timestamp) {
                return null; //token expired
            }

            return $decoded;
        } catch (Exception $e) {
            return null; // Token is invalid
        }
    }

    /**
     * @param Consumer $consumer
     * @param string $default
     *
     * @return string
     */
    public function getConsumerDomain(Consumer $consumer, string $default = 'fixr.com'): string
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts()->first();

        if (!$consumerProduct->consumerProductTracking || !$consumerProduct->consumerProductTracking->url_convert) {
            return $default;
        }

        $domain = $this->getDomainFromUrl($consumerProduct->consumerProductTracking->url_convert);
        $website = Website::query()->where(Website::FIELD_URL, 'LIKE', "%$domain%")->first();


        return $website ? $domain : $default;
    }

    /**
     * @param string $url
     *
     * @return string|null
     */
    public function getDomainFromUrl(string $url): ?string
    {
        preg_match('/https?:\/\/(?:www\.)?([^\/]+)/i', $url, $matches);

        if (isset($matches[1])) {
           return $matches[1];
        }

        return null;
    }
}
