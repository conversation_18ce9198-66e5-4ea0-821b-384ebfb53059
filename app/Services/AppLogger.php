<?php

namespace App\Services;

use App\Enums\AppFeature;
use App\Enums\Log\LogLevel;
use App\Models\AppLog;
use App\Models\AppLogRelation;
use Exception;
use Throwable;

class AppLogger
{
    protected array       $context   = [];
    protected array       $relations = [];
    protected ?AppFeature $feature   = null;
    protected ?string     $function  = null;

    /**
     * @param array $context
     * @param array $relations
     * @param AppFeature|null $feature
     * @param string|null $function
     * @return self
     */
    public static function make(
        array $context = [],
        array $relations = [],
        ?AppFeature $feature = null,
        ?string $function = null,
    ): self
    {
        $instance = new self();
        $instance->context = $context;
        $instance->relations = $relations;
        $instance->feature = $feature;
        $instance->function = $function;

        return $instance;
    }

    /**
     * @param AppFeature $feature
     * @return $this
     */
    public function feature(AppFeature $feature): self
    {
        $this->feature = $feature;
        return $this;
    }

    /**
     * @param array $context
     * @return $this
     */
    public function context(array $context): self
    {
        $this->context = $context;
        return $this;
    }

    /**
     * @param array $relations
     * @return $this
     */
    public function relations(array $relations): self
    {
        $this->relations = $relations;
        return $this;
    }

    /**
     * @param string $function
     * @return $this
     */
    public function function (string $function): self
    {
        $this->function = $function;
        return $this;
    }

    /**
     * @param string $message
     * @param array $context
     * @param array $relations
     * @param string|null $function
     * @return void
     */
    public function error(
        string $message,
        array $context = [],
        array $relations = [],
        ?string $function = null,
    ): void
    {
        $this->log(
            message  : $message,
            level    : LogLevel::ERROR,
            context  : $context,
            relations: $relations,
            function : $function,
        );
    }

    /**
     * @param string $message
     * @param array $context
     * @param array $relations
     * @param string|null $function
     * @return void
     */
    public function warn(
        string $message,
        array $context = [],
        array $relations = [],
        ?string $function = null,
    ): void
    {
        $this->log(
            message  : $message,
            level    : LogLevel::WARNING,
            context  : $context,
            relations: $relations,
            function : $function,
        );
    }

    /**
     * @param string $message
     * @param array $context
     * @param array $relations
     * @param string|null $function
     * @return void
     */
    public function info(
        string $message,
        array $context = [],
        array $relations = [],
        ?string $function = null,
    ): void
    {
        $this->log(
            message  : $message,
            level    : LogLevel::INFO,
            context  : $context,
            relations: $relations,
            function : $function,
        );
    }

    /**
     * @param string $message
     * @param array $context
     * @param array $relations
     * @param string|null $function
     * @return void
     */
    public function debug(
        string $message,
        array $context = [],
        array $relations = [],
        ?string $function = null,
    ): void
    {
        $this->log(
            message  : $message,
            level    : LogLevel::DEBUG,
            context  : $context,
            relations: $relations,
            function : $function,
        );
    }

    /**
     * @param Throwable $exception
     * @param string|null $message
     * @param array $context
     * @param array $relations
     * @param string|null $function
     * @param AppFeature|null $feature
     * @return void
     */
    public function exception(
        Throwable $exception,
        ?string $message = null,
        array $context = [],
        array $relations = [],
        ?string $function = null,
        ?AppFeature $feature = null,
    ): void
    {
        $resultMessage = $exception->getMessage();
        if ($message) {
            $resultMessage .= ' | ' . $message;
        }

        $this->log(
            message   : $resultMessage,
            level     : LogLevel::ERROR,
            context   : $context,
            relations : $relations,
            function  : $function,
            stackTrace: $exception->getTraceAsString(),
            feature   : $feature,
            line      : $exception->getLine(),
            file      : $exception->getFile(),
        );
    }

    /**
     * @param string $message
     * @param LogLevel $level
     * @param array $context
     * @param array $relations
     * @param string|null $function
     * @param string|null $stackTrace
     * @param AppFeature|null $feature
     * @param string|null $line
     * @param string|null $file
     * @return void
     */
    public function log(
        string $message,
        LogLevel $level,
        array $context = [],
        array $relations = [],
        ?string $function = null,
        ?string $stackTrace = null,
        ?AppFeature $feature = null,
        ?string $line = null,
        ?string $file = null,
    ): void
    {
        $this->logToDatabase(
            message   : $message,
            level     : $level,
            context   : $context,
            relations : $relations,
            feature   : $feature,
            function  : $function,
            stackTrace: $stackTrace,
            line      : $line,
            file      : $file,
        );
    }

    /**
     * @param string $message
     * @param LogLevel $level
     * @param array $context
     * @param array $relations
     * @param AppFeature|null $feature
     * @param string|null $function
     * @param string|null $stackTrace
     * @param string|null $line
     * @param string|null $file
     * @return void
     */
    protected function logToDatabase(
        string $message,
        LogLevel $level,
        array $context = [],
        array $relations = [],
        ?AppFeature $feature = null,
        ?string $function = null,
        ?string $stackTrace = null,
        ?string $line = null,
        ?string $file = null,
    ): void
    {
        try {
            $cleanContext = json_encode([
                ...$this->context,
                ...$context
            ], JSON_PARTIAL_OUTPUT_ON_ERROR);

            $log = AppLog::query()
                ->create([
                    AppLog::FIELD_MESSAGE     => $message,
                    AppLog::FIELD_FEATURE     => $feature?->value ?? $this->feature?->value,
                    AppLog::FIELD_FUNCTION    => $function ?? $this->function,
                    AppLog::FIELD_LEVEL       => $level,
                    AppLog::FIELD_STACK_TRACE => $stackTrace,
                    AppLog::FIELD_CONTEXT     => $cleanContext,
                    AppLog::FIELD_FILE        => $file,
                    AppLog::FIELD_LINE        => $line
                ]);

            $allRelations = collect([
                ...$relations,
                ...$this->relations
            ])->unique();

            $items = $allRelations->map(function ($model) use ($log) {
                return [
                    AppLogRelation::FIELD_APP_LOG_ID    => $log->id,
                    AppLogRelation::FIELD_RELATION_TYPE => $model::class,
                    AppLogRelation::FIELD_RELATION_ID   => $model->id,
                    AppLogRelation::FIELD_CREATED_AT    => now(),
                    AppLogRelation::FIELD_UPDATED_AT    => now(),
                ];
            });

            AppLogRelation::query()->insert($items->toArray());
        } catch (Exception $exception) {
            logger()->error($exception);
        }
    }
}

