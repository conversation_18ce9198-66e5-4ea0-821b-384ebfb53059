<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class ContactCacheService
{
    const string CONTACTED_BASE_CACHE_KEY = 'contacted:';

    /**
     * @param string $contactVector
     * @return bool
     */
    public function recentlyContacted(
        string $contactVector,
    ): bool
    {
        $key = self::CONTACTED_BASE_CACHE_KEY . $contactVector;

        return Cache::has($key);
    }

    /**
     * @param string $contactVector
     * @param Carbon|null $cooldown
     * @return void
     */
    public function markRecentlyContacted(
        string $contactVector,
        ?Carbon $cooldown = null,
    ): void
    {
        $cooldown = $cooldown ?? now()->addDay();
        $key = self::CONTACTED_BASE_CACHE_KEY . $contactVector;

        Cache::put($key, true, $cooldown);
    }

}
