<?php

namespace App\Services;

use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Repositories\LeasingCompanyRepository;
use App\Repositories\Legacy\QuoteRepository;
use App\Services\Legacy\LeadDeliveryService;
use Exception;

class LeasingCompanyService
{
    public function __construct(
        protected QuoteRepository $quoteRepository,
        protected LeasingCompanyRepository $leasingCompanyRepository,
        protected LeadDeliveryService $leadDeliveryService
    ) {}

    /**
     * @param string $leadReference
     *
     * @return void
     */
    public function allocateLegacyLead(string $leadReference): void
    {
        $legacyLead = $this->quoteRepository->findByReference($leadReference);

        if (!$legacyLead) {
            $this->log("Legacy lead does not exist ($leadReference)", 'error');
            return;
        }

        //lead has been sold or is undersold
        if ($legacyLead->quoteCompanies->filter(fn(EloquentQuoteCompany $quoteCompany) => $quoteCompany->delivered)->isNotEmpty()) {
            return;
        }

        $this->attemptLegacyAllocation($legacyLead);
    }

    /**
     * @param string $message
     * @param string $level
     *
     * @return void
     */
    protected function log(string $message, string $level = 'info'): void
    {
        $logMessage = "Allocation to leasing company: $message";

        switch ($level) {
            case 'error':
                logger()->error($logMessage);
                break;
            case 'info':
                logger()->info($logMessage);
                break;
            default:
                logger()->debug($logMessage);
                break;
        }
    }

    /**
     * @param EloquentQuote $legacyLead
     *
     * @return void
     */
    protected function attemptLegacyAllocation(EloquentQuote $legacyLead): void
    {
        $legacyCampaign = $this->leasingCompanyRepository->getLeasingCompanyLegacyCampaignForZipCode($legacyLead->address->zipcode);

        if (!$legacyCampaign) {
            $this->log("No campaign found for lead: $legacyLead->uuid");
            return;
        }

        try {
            $this->leadDeliveryService->saveAndDeliverQuoteCompany(
                leadReference: $legacyLead->uuid,
                selectedQuoteCompanies: [$this->transformCampaignForLegacyAllocationRequest($legacyCampaign)]
            );
        } catch (Exception $e) {
            $this->log("Failed to allocate lead: {$legacyLead->uuid}, error: {$e->getMessage()}", 'error');
        }
    }

    /**
     * @param LeadCampaign $leadCampaign
     *
     * @return array
     */
    protected function transformCampaignForLegacyAllocationRequest(LeadCampaign $leadCampaign): array
    {
        /** @var LeadCampaignSalesTypeConfiguration $saleTypeConfiguration */
        $saleTypeConfiguration = $leadCampaign->leadSalesTypeConfigurations()
            ->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, SaleTypes::mapSaleTypeToLegacyId(SaleTypes::EXCLUSIVE))
            ->firstOrFail();

        return [
            'companyid'                        => $leadCampaign->company_id,
            'lead_sales_type_configuration_id' => $saleTypeConfiguration->id,
            'chargeable'                       => 0, //todo: confirm charge/not change
            'non_budget_premium_sale'          => false,
            'price'                            => 100 //todo: confirm price if charging
        ];
    }
}
