<?php

namespace App\Services\Broadcasting;

use Illuminate\Contracts\Broadcasting\Broadcaster;
use Pusher\Pusher;

class PusherNotificationBroadcaster implements Broadcaster
{
    const REQUEST_SOCKET_ID    = 'socket_id';
    const REQUEST_CHANNEL_NAME = 'channel_name';

    /**
     * @param Pusher $pusher
     */
    public function __construct(protected Pusher $pusher)
    {
    }

    /**
     * @inheritDoc
     */
    public function broadcast(array $channels, $event, array $payload = [])
    {
        $this->pusher->trigger($channels, $event, $payload);
    }

    /**
     * Returns the pusher instance.
     *
     * @return Pusher
     */
    public function getPusherInstance(): Pusher
    {
        return $this->pusher;
    }

    /**
     * These methods are unused, and are implemented in the base pusher broadcaster.
     * This service is only used for notifications.
     *
     * @inheritDoc
     */
    public function auth($request)
    {
        return true;
    }

    /**
     * These methods are unused, and are implemented in the base pusher broadcaster.
     * This service is only used for notifications.
     *
     * @inheritDoc
     */
    public function validAuthenticationResponse($request, $result)
    {
        return true;
    }
}
