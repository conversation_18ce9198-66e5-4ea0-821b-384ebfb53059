<?php

namespace App\Services;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\RejectionPercentageCategory;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\CrmDeliveryLog;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ProductRejectionRepository;
use App\Repositories\Odin\ProductRepository;
use App\Services\PubSub\PubSubService;
use Carbon\Carbon;
use App\Enums\Odin\Product as ProductEnum;
use Illuminate\Database\Eloquent\Collection;

class ProductRejectionCalculationService
{
    const int MINIMUM_CRM_SPEND_THRESHOLD = 5000;

    // We want to cap overall rejection percentage to avoid scenarios where effective bid is impacted so much that
    // we sell less legs than we otherwise should
    private int $cappedOverallRejectionPercentage;

    /**
     * @param ProductRejectionRepository $productRejectionRepository
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ComputedRejectionStatisticRepository $computedRejectionRepository
     * @param ProductRepository $productRepository
     * @param PubSubService $pubSubService
     */
    public function __construct(
        protected ProductRejectionRepository $productRejectionRepository,
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected ComputedRejectionStatisticRepository $computedRejectionRepository,
        protected ProductRepository $productRepository,
        protected PubSubService $pubSubService
    )
    {
        $this->cappedOverallRejectionPercentage = config('sales.leads.overall_rejection_percentage_threshold');
    }

    /**
     * @param Company $company
     * @param int $productId
     * @return void
     */
    public function calculateAndStoreCompanyProductRejectionPercentages(
        Company $company,
        int     $productId
    ): void
    {
        $existingRejectionPercentage = $this->computedRejectionRepository->getRejectionStatisticsByCompanyIdAndProductId($company->{Company::FIELD_ID}, $productId);

        $rejectionStatistics = $this->computeRejectionStatistics($company, $productId);

        $rejectionStatistics = $this->computedRejectionRepository->createOrUpdateRejectionStatistic(
            $company,
            $productId,
            $rejectionStatistics
        );

        $this->handleOverallRejectionPercentageThresholdExceeded(
            $company,
            $productId,
            $existingRejectionPercentage?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE} ?? 0,
            $rejectionStatistics->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE}
        );

        $this->handleCrmRejectionPercentageThresholdExceeded(
            $company,
            $productId,
            $existingRejectionPercentage?->{ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE} ?? 0,
            $rejectionStatistics->{ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE}
        );
    }

    /**
     * @param int $legacyQuoteId
     * @return Collection
     */
    public function getCompaniesWithFailedDeliveries(int $legacyQuoteId): Collection
    {
        return Company::query()
            ->select(Company::TABLE.'.*')
            ->join(
                DatabaseHelperService::readOnlyDatabase().'.'.CrmDeliveryLog::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::COMPANY_ID,
                '=',
                Company::TABLE.'.'.Company::FIELD_LEGACY_ID
            )
            ->where(DatabaseHelperService::readOnlyDatabase().'.'.CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::QUOTE_ID, $legacyQuoteId)
            ->where(DatabaseHelperService::readOnlyDatabase().'.'.CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::DELIVERY_STATUS, CrmDeliveryLog::VALUE_DELIVERY_STATUS_FAILED)
            ->where(DatabaseHelperService::readOnlyDatabase().'.'.CrmDeliveryLog::TABLE.'.'.CrmDeliveryLog::SEND_TEST_LEAD, false)
            ->get();
    }

    /**
     * @param Company $company
     * @param int $productId
     * @param float $existingRejectionPercentage
     * @param float $newRejectionPercentage
     * @return void
     */
    public function handleOverallRejectionPercentageThresholdExceeded(Company $company, int $productId, float $existingRejectionPercentage, float $newRejectionPercentage): void
    {
        $this->handleRejectionPercentageThresholdExceeded(RejectionPercentageCategory::OVERALL->value, $company, $productId, $existingRejectionPercentage, $newRejectionPercentage);
    }

    /**
     * @param string $rejectionCategory
     * @param Company $company
     * @param int $productId
     * @param float $existingRejectionPercentage
     * @param float $newRejectionPercentage
     * @return void
     */
    private function handleRejectionPercentageThresholdExceeded(string $rejectionCategory, Company $company, int $productId, float $existingRejectionPercentage, float $newRejectionPercentage): void
    {
        $product = $this->productRepository->getProductById($productId);

        $rejectionPercentageThreshold = $this->getRejectionThreshold($rejectionCategory);

        // For task notifications the CRM rejection should be 1 less than cutoff
        if($rejectionCategory === RejectionPercentageCategory::CRM->value) {
            $rejectionPercentageThreshold -= 1;
        }

        if($existingRejectionPercentage >= $rejectionPercentageThreshold) {
            return;
        }

        if($newRejectionPercentage >= $rejectionPercentageThreshold) {
            $eventName = match ($product->{Product::FIELD_NAME}) {
                ProductEnum::APPOINTMENT->value => $rejectionCategory === RejectionPercentageCategory::CRM->value ?
                    EventName::APPOINTMENT_CRM_REJECTION_PERCENTAGE_EXCEEDED->value :
                    EventName::APPOINTMENT_REJECTION_PERCENTAGE_EXCEEDED->value,
                default => $rejectionCategory === RejectionPercentageCategory::CRM->value ?
                    EventName::CRM_REJECTION_PERCENTAGE_EXCEEDED->value :
                    EventName::REJECTION_PERCENTAGE_EXCEEDED->value,
            };

            $this->pubSubService->handle(
                EventCategory::COMPANIES->value,
                $eventName,
                [
                    "rejection_percentage" => $newRejectionPercentage,
                    "company_reference" => $company->{Company::FIELD_REFERENCE}
                ]
            );
        }
    }

    /**
     * @param Company $company
     * @param int $productId
     * @param float $existingRejectionPercentage
     * @param float $newRejectionPercentage
     * @return void
     */
    public function handleCrmRejectionPercentageThresholdExceeded(Company $company, int $productId, float $existingRejectionPercentage, float $newRejectionPercentage): void
    {
        $this->handleRejectionPercentageThresholdExceeded(RejectionPercentageCategory::CRM->value, $company, $productId, $existingRejectionPercentage, $newRejectionPercentage);
    }

    /**
     * This is the total value of chargeable and delivered product rejected over past 30 days
     * divided by the total value of all chargeable and delivered products over past 30 days.
     * We likely don't have a use case for looking at a company's overall rejection percentage but rather
     * prefer to look at a company's rejections for a single product. We may separate rejection percentage out by
     * industry / service in the future. Todo, update explanation
     *
     * @param Company $company
     * @param int $productId
     * @param array|null $industryIds
     * @param array|null $industryServiceIds
     * @return array
     */
    public function computeRejectionStatistics(
        Company $company,
        int   $productId,
        ?array   $industryIds = null,
        ?array   $industryServiceIds = null
    ): array
    {
        $leadProductId = $this->productRepository->getLeadProductId();

        $computedRejectionStatistic = $this->computedRejectionRepository->getRejectionStatisticsByCompanyIdAndProductId($company->{Company::FIELD_ID}, $productId);

        $startTimestamp    = $this->getRejectionCalculationStartTimestamp();
        $crmStartTimestamp = $this->getCrmRejectionCalculationStartTimestamp($computedRejectionStatistic);

        $spentSum = $this->productAssignmentRepository
            ->getTotalProductAssignmentsCostForCompany(
                $company,
                true,
                true,
                true,
                [$productId],
                $industryIds,
                $industryServiceIds,
                $startTimestamp
            );

        $spentSumSinceCrmReset = $spentSum;

        if($computedRejectionStatistic?->{ComputedRejectionStatistic::FIELD_CRM_RESET_TIME}) {
            $spentSumSinceCrmReset = $this->productAssignmentRepository
                ->getTotalProductAssignmentsCostForCompany(
                    $company,
                    true,
                    true,
                    true,
                    [$productId],
                    $industryIds,
                    $industryServiceIds,
                    $crmStartTimestamp
                );
        }

        $crmRejectedValue = 0;

        $crmRejectedValue += $this->productRejectionRepository->getOdinCrmRejections(
            $company->id,
            $productId,
            $crmStartTimestamp
        );

        $totalAssignedCost              = $spentSum + $crmRejectedValue;
        $totalAssignedCostSinceCrmReset = $spentSumSinceCrmReset + $crmRejectedValue;

        if(empty($totalAssignedCost)) {
            return [
                ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST        => 0,
                ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST => 0,
                ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE  => 0,
                ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST    => 0,
                ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE     => 0,
                ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE => 0,
            ];
        }

        $manualRejectedValue = $this->productRejectionRepository->getTotalProductRejectionsCostForCompany(
            $company,
            [$productId],
            $industryIds,
            $industryServiceIds,
            $startTimestamp
        );

        $manualRejectionPercentage = $manualRejectedValue > 0 ? round($manualRejectedValue / $totalAssignedCost * 100, 2) : 0;

        if($totalAssignedCostSinceCrmReset < self::MINIMUM_CRM_SPEND_THRESHOLD) {
            $crmRejectionPercentage = 0;
        } else {
            $crmRejectionPercentage = $crmRejectedValue > 0 ? round($crmRejectedValue / $totalAssignedCostSinceCrmReset * 100, 2) : 0;
        }

        $overallRejectionPercentage = $manualRejectionPercentage + $crmRejectionPercentage;

        return [
            ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST        => $totalAssignedCost,
            ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST => $manualRejectedValue,
            ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE  => $manualRejectionPercentage,
            ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST    => $crmRejectedValue,
            ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE     => $crmRejectionPercentage,
            ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE => min($overallRejectionPercentage, $this->cappedOverallRejectionPercentage),
        ];
    }

    /**
     * Check if this Company is within the rejection threshold limit for this Product
     *
     * @param Company $company
     * @param Product $product
     * @return bool
     */
    public function companyCanManuallyRejectProduct(Company $company, Product $product): bool
    {
        $rejectionThreshold = $this->getRejectionThreshold(RejectionPercentageCategory::MANUAL->value);
        $companyRejectionPercentage = $company->rejectionStatistics()
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $product->id)
            ->first()
            ?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE}
            ?? 0.0;

        return $companyRejectionPercentage < $rejectionThreshold;
    }

    /**
     * Check if rejecting this ProductAssignment will push a Company over the rejection threshold
     *
     * @param Company $company
     * @param ProductAssignment $productAssignment
     * @return bool
     */
    public function potentialRejectionExceedsThreshold(Company $company, ProductAssignment $productAssignment): bool
    {
        $product = $productAssignment->consumerProduct->serviceProduct->product;
        /** @var ComputedRejectionStatistic|null $rejectionStatistics */
        $rejectionStatistics = $company->rejectionStatistics()
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $product->id)
            ->first();

        $currentProductCost = $rejectionStatistics->assigned_product_cost ?? 0;
        $potentialRejectedCost = ($rejectionStatistics->crm_rejected_product_cost ?? 0)
            + ($rejectionStatistics->manual_rejected_product_cost ?? 0)
            + $productAssignment->cost;
        $potentialPercentage = $currentProductCost > 0
            ? ($potentialRejectedCost / $currentProductCost) * 100
            : 0;

        $threshold = $this->getRejectionThreshold(RejectionPercentageCategory::MANUAL->value);

        return $potentialPercentage >= $threshold;
    }

    /**
     * @param string $rejectionCategory
     * @return int
     */
    private function getRejectionThreshold(string $rejectionCategory): int
    {
        return match ($rejectionCategory) {
            RejectionPercentageCategory::CRM->value    => config('sales.defaults.crm_rejection_percentage_threshold'),
            RejectionPercentageCategory::MANUAL->value => config('sales.defaults.manual_rejection_percentage_threshold'),
            default                                    => config('sales.defaults.overall_rejection_percentage_threshold'),
        };
    }

    /**
     * @return float|int|string
     */
    private function getRejectionCalculationStartTimestamp(): float|int|string
    {
        return Carbon::today()->subDays(29)->timestamp;
    }

    /**
     * @param ComputedRejectionStatistic|null $computedRejectionStatistic
     * @return float|int|string
     */
    private function getCrmRejectionCalculationStartTimestamp(?ComputedRejectionStatistic $computedRejectionStatistic): float|int|string
    {
        $floorTime = $this->getRejectionCalculationStartTimestamp();
        $resetTime = $computedRejectionStatistic?->{ComputedRejectionStatistic::FIELD_CRM_RESET_TIME}?->timestamp ?? 0;

        return max($floorTime, $resetTime);
    }
}
