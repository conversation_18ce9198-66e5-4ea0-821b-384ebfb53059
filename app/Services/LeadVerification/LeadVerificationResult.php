<?php

namespace App\Services\LeadVerification;

use Illuminate\Contracts\Support\Arrayable;

class LeadVerificationResult implements Arrayable
{
    /**
     * The driver that was used to verify this lead
     *
     * @var string
     */
    public $driver;

    /**
     * The confidence of this verification result
     * Usually a result between 0-500, where 500 is the least confident
     *
     * @var int
     */
    public $confidence;

    /**
     * Whether or not the phone number provided is valid
     *
     * @var bool
     */
    public $phone_is_valid;

    /**
     * Whether or not the phone number provided matches the name provided
     * in the lead
     *
     * @var bool|null
     */
    public $phone_name_match;

    /**
     * The name of the subscriber of this phone
     *
     * @var string|null
     */
    public $phone_subscriber_name;

    /**
     * The address match result of this lead verification
     *
     * @var string|null
     */
    public $phone_address_match;

    /**
     * The line type of the phone
     *
     * @var string|null
     */
    public $phone_line_type;

    /**
     * The name of the phone carrier
     *
     * @var string|null
     */
    public $phone_carrier;

    /**
     * Whether or not this phone has been registered for business use
     *
     * @var bool|null
     */
    public $phone_is_commercial;

    /**
     * Whether or not this phone is a prepaid phone
     *
     * @var bool|null
     */
    public $phone_is_prepaid;

    /**
     * An array of warnings provided about this phone
     *
     * @var string[]|null
     */
    public $phone_warnings;

    /**
     * Whether or not the address provided is valid
     *
     * @var bool
     */
    public $address_is_valid;

    /**
     * Whether or not the address provided matches the name provided in the lead
     *
     * @var bool|null
     */
    public $address_name_match;

    /**
     * The name of the registered owner of this address
     *
     * @var string|null
     */
    public $address_resident_name;

    /**
     * The type of building that exists on this address
     *
     * @var string|null
     */
    public $address_type;

    /**
     * Whether or not this address has been registered for business use
     *
     * @var bool|null
     */
    public $address_is_commercial;

    /**
     * Whether or not the address is performing freight forwarding or reshipping services
     *
     * @var bool|null
     */
    public $address_is_forwarder;

    /**
     * An array of warnings provided for this address
     *
     * @var string[]|null
     */
    public $address_warnings;

    /**
     * Whether or not the email address provided is valid
     *
     * @var bool
     */
    public $email_is_valid;

    /**
     * Whether or not the email provided matches the name provided in the lead
     *
     * @var bool|null
     */
    public $email_name_match;

    /**
     * The name that is registered with this email
     *
     * @var string|null
     */
    public $email_registered_name;

    /**
     * The days this email has been registered for
     *
     * @var int|null
     */
    public $email_age;

    /**
     * Whether or not the email provided is auto generated or not
     *
     * @var bool|null
     */
    public $email_is_autogenerated;

    /**
     * Whether or not the email provided is a disposable email
     *
     * @var bool|null
     */
    public $email_is_disposable;

    /**
     * An array of warnings that were provided for this email
     *
     * @var string[]|null
     */
    public $email_warnings;

    /**
     * Whether or not the ip address provided is a valid ip address
     *
     * @var bool
     */
    public $ip_is_valid;

    /**
     * Whether or not the ip provided has matched the name provided in the lead
     *
     * @var bool|null
     */
    public $ip_name_match;

    /**
     * The rough location of this ip address
     *
     * @var string|null
     */
    public $ip_geolocation;

    /**
     * The estimated distance in miles from the address provided in the lead, to the location
     * of this ip address
     *
     * @var string|null
     */
    public $ip_address_distance;

    /**
     * The estimated distance in miles from the phone provided in the lead, to the location
     * of this ip address
     *
     * @var string|null
     */
    public $ip_phone_distance;

    /**
     * Whether or not the ip provided has a risk of being behind a proxy
     *
     * @var bool|null
     */
    public $ip_is_proxy;

    /**
     * An array of warnings provided for this ip address
     *
     * @var string[]|null
     */
    public $ip_warnings;

    /**
     * LeadVerificationResult constructor.
     *
     * @param array $properties
     */
    public function __construct($properties = [])
    {
        foreach ($properties as $key => $property) {
            if (property_exists($this, $key)) {
                $this->{$key} = $property;
            }
        }
    }

    /**
     * Get the instance as an array.
     *
     * @return array
     */
    public function toArray(): array
    {
        return (array)$this;
    }
}
