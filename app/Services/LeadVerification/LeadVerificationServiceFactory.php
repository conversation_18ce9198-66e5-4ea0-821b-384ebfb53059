<?php

namespace App\Services\LeadVerification;

use App\Enums\VerificationService;
use App\Services\LeadVerification\Dummy\DummyLeadVerificationService;
use App\Services\LeadVerification\IpQualityScore\IpQualityScoreService;
use App\Services\LeadVerification\WhitePages\IdentityCheckService;
use Exception;

class LeadVerificationServiceFactory
{
    /**
     * Makes the lead verification service
     *
     * @param string $service
     * @return LeadVerificationServiceInterface
     * @throws Exception
     */
    public static function makeService(string $service): LeadVerificationServiceInterface
    {
        return match ($service) {
            VerificationService::WHITEPAGES->value => app(IdentityCheckService::class),
            VerificationService::IP_QUALITY_SCORE->value => app(IpQualityScoreService::class),
            VerificationService::DUMMY->value => app(DummyLeadVerificationService::class),
            default => throw new Exception(__METHOD__.": Invalid service - $service")
        };
    }

}
