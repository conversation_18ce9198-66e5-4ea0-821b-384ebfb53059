<?php

namespace App\Services\AddressIdentification;

use App\Contracts\Services\AddressIdentificationServiceInterface;
use Exception;
use Ramsey\Uuid\Uuid;

class DummyAddressIdentificationService implements AddressIdentificationServiceInterface
{
    /**
     * @inheritDoc
     * @throws Exception
     */
    public function getIdFromAddressComponents(string $address1, string $city, string $stateAbbr, string $zipCode, ?string $address2 = null): string
    {
        return Uuid::uuid4();
    }
}
