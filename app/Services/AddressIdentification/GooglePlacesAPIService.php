<?php

namespace App\Services\AddressIdentification;

use App\Contracts\Services\AddressIdentificationServiceInterface;
use Exception;
use GuzzleHttp\Client;

class GooglePlacesAPIService implements AddressIdentificationServiceInterface
{
    private Client $client;

    public function __construct() {
        $this->client = new Client([
            'base_uri' => config('services.google.maps.places_base_uri')
        ]);
    }

    /**
     * @inheritDoc
     *
     * Returns a Google Place ID
     */
    public function getIdFromAddressComponents(string $address1, string $city, string $stateAbbr, string $zipCode, ?string $address2 = null): string
    {
        $placeId = '';

        try {
            $addressQuery = $address1.($address2 ? " $address2" : "").", $city, $stateAbbr $zipCode";
            $key = config('services.google.maps.static_maps_key');

            $res = $this->client->get("findplacefromtext/json?fields=place_id&inputtype=textquery&key={$key}&input={$addressQuery}");

            if($res->getStatusCode() === 200) {
                $contents = json_decode($res->getBody()->getContents(), true);

                if($contents['status'] === "OK"
                && !empty($contents['candidates'])) {
                    $placeId = $contents['candidates'][0]['place_id'];
                }
                else {
                    throw new Exception("{$contents["status"]}: {$contents["error_message"]}");
                }
            }
            else {
                throw new Exception("Request failed");
            }
        }
        catch(Exception $e) {
            logger()->error("Could not get place ID for address $addressQuery: ".$e->getMessage());

            throw $e;
        }

        return $placeId;
    }
}
