<?php

namespace App\Services\AddressIdentification;

use App\Contracts\Services\AddressIdentificationServiceInterface;

class AddressIdentificationServiceFactory
{
    const DRIVER_GOOGLE = 'google';

    /**
     * @return AddressIdentificationServiceInterface
     */
    public static function make(): AddressIdentificationServiceInterface
    {
        return match(config('services.address_identification.driver')) {
            self::DRIVER_GOOGLE => new GooglePlacesAPIService(),
            default => new DummyAddressIdentificationService()
        };
    }
}
