<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;

class CalendlyService
{

    /**
     * @param string $calendlyEventUrl
     * @param int $userId
     * @return array|null
     */
    public static function getEventPayload(string $calendlyEventUrl, int $userId): ?array
    {
        /** @var User $user */
        $user = User::find($userId);

        $token = $user->calendly_api_token;
        if (empty($token))
            return null;

        try {
            $req = Http::withToken($token)->get($calendlyEventUrl);
        } catch (ConnectionException $e) {
            return null;
        }

        return $req->json()['resource'];
    }
}
