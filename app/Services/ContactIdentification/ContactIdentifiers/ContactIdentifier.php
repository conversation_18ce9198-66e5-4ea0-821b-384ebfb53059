<?php

namespace App\Services\ContactIdentification\ContactIdentifiers;

use App\DTO\ContactIdentification\Contact;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\ContactIdentification\PossibleContact;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

abstract class ContactIdentifier
{
    /**
     * @param string $value
     * @param SearchableFieldType $type
     * @return Contact[]
     */
    abstract public function search(string $value, SearchableFieldType $type): array;

    /**
     * @return array
     */
    abstract public function getAvailableSearchableFieldTypes(): array;

    /**
     * @param string $value
     * @param SearchableFieldType $type
     * @return Builder
     */
    abstract public function searchQuery(string $value, SearchableFieldType $type): Builder;

    /**
     * @param string $value
     * @param Collection $collection
     * @return Contact[]
     */
    abstract public function transformToContacts(string $value, Collection $collection): array;

    /**
     * @param Collection<PossibleContact> $contacts
     * @return Collection
     */
    public function sortPossibleContacts(Collection $contacts): Collection
    {
        return $contacts;
    }
}
