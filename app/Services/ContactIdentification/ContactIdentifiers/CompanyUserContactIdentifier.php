<?php

namespace App\Services\ContactIdentification\ContactIdentifiers;

use App\DTO\ContactIdentification\Contact;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\ContactIdentification\PossibleContact;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CompanyUserContactIdentifier extends ContactIdentifier
{
    /**
     * @param string $value
     * @param SearchableFieldType $type
     * @return Contact[]
     */
    public function search(string $value, SearchableFieldType $type): array
    {
        $possibleContacts = $this->searchQuery($value, $type)->get();

        return $this->transformToContacts($value, $possibleContacts);
    }

    /**
     * @param string $value
     * @param Collection $collection
     * @return Contact[]
     */
    public function transformToContacts(string $value, Collection $collection): array
    {
        return $collection
            ->map(function (CompanyUser $companyUser) use ($value) {
                $matchedFiled = $companyUser->email === $value
                    ? CompanyUser::FIELD_EMAIL
                    : ($companyUser->cell_phone === $value
                        ? CompanyUser::FIELD_CELL_PHONE
                        : CompanyUser::FIELD_OFFICE_PHONE
                    );

                return Contact::fromArray([
                    Contact::FIELD_ID          => $companyUser->id,
                    Contact::FIELD_TYPE        => CompanyUser::class,
                    Contact::FIELD_MATCH_FIELD => $matchedFiled
                ]);
            })->all();
    }

    /**
     * @param string $value
     * @param SearchableFieldType $type
     * @return Builder
     */
    public function searchQuery(string $value, SearchableFieldType $type): Builder
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_EMAIL, $value)
            ->whereNot(CompanyUser::FIELD_COMPANY_ID, 0);
    }

    /**
     * @return array
     */
    public function getAvailableSearchableFieldTypes(): array
    {
        return [
            SearchableFieldType::EMAIL
        ];
    }

    /**
     * @param Collection<PossibleContact> $contacts
     * @return Collection
     */
    public function sortPossibleContacts(Collection $contacts): Collection
    {
        return $contacts->sortByDesc(function (PossibleContact $contact) {
            return $contact->identifiable->created_at;
        })
            ->sortByDesc(function (PossibleContact $contact) {
                return ProductAssignment::query()
                    ->where(ProductAssignment::FIELD_COMPANY_ID, $contact->identifiable->company_id)
                    ->max(ProductAssignment::FIELD_ID) ?? 0;
            })
            ->values();
    }
}
