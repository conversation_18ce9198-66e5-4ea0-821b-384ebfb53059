<?php

namespace App\Services\ContactIdentification\ContactIdentifiers;

use App\DTO\ContactIdentification\Contact;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\Odin\Consumer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ConsumerContactIdentifier extends ContactIdentifier
{
    /**
     * @param string $value
     * @param SearchableFieldType $type
     * @return Contact[]
     */
    public function search(string $value, SearchableFieldType $type): array
    {
        $possibleContacts = $this->searchQuery($value, $type)->get();

        return $this->transformToContacts($value, $possibleContacts);
    }

    /**
     * @param string $value
     * @param SearchableFieldType|null $type
     * @return Builder
     */
    public function searchQuery(string $value, ?SearchableFieldType $type): Builder
    {
        return Consumer::query()->where(Consumer::FIELD_EMAIL, $value);
    }

    /**
     * @return array
     */
    public function getAvailableSearchableFieldTypes(): array
    {
        return [
            SearchableFieldType::EMAIL
        ];
    }

    /**
     * @param string $value
     * @param Collection $collection
     * @return array|Contact[]
     */
    public function transformToContacts(string $value, Collection $collection): array
    {
        return $collection
            ->map(fn(Consumer $consumer) => Contact::fromArray([
                Contact::FIELD_ID           => $consumer->id,
                Contact::FIELD_TYPE         => Consumer::class,
                Contact::FIELD_MATCH_FIELD  => $value === $consumer->email ? Consumer::FIELD_EMAIL : Consumer::FIELD_PHONE,
            ]))
            ->all();
    }
}
