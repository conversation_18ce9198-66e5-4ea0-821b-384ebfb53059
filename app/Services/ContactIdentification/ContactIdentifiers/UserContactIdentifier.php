<?php

namespace App\Services\ContactIdentification\ContactIdentifiers;

use App\DTO\ContactIdentification\Contact;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class UserContactIdentifier extends ContactIdentifier
{
    /**
     * @param string $value
     * @param SearchableFieldType $type
     * @return Contact[]
     */
    public function search(string $value, SearchableFieldType $type): array
    {
        $possibleContacts = $this->searchQuery($value, $type)->get();

        return $this->transformToContacts($value, $possibleContacts);
    }

    /**
     * @param string $value
     * @param SearchableFieldType|null $type
     * @return Builder
     */
    public function searchQuery(string $value, ?SearchableFieldType $type): Builder
    {
        return User::query()->where(function ($query) use ($value) {
            $query->where(User::FIELD_EMAIL, $value)
                ->orWhereJsonContains(User::FIELD_EMAIL_ALIASES, $value);
        });
    }

    /**
     * @return array
     */
    public function getAvailableSearchableFieldTypes(): array
    {
        return [SearchableFieldType::EMAIL];
    }

    /**
     * @param string $value
     * @param Collection $collection
     * @return array|Contact[]
     */
    public function transformToContacts(string $value, Collection $collection): array
    {
        return $collection->map(fn(User $user) => Contact::fromArray([
            Contact::FIELD_ID          => $user->id,
            Contact::FIELD_TYPE        => User::class,
            Contact::FIELD_MATCH_FIELD => User::FIELD_EMAIL
        ]))->all();
    }
}
