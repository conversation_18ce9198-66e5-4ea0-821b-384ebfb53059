<?php

namespace App\Services\ContactIdentification;

use App\Enums\ContactIdentification\ContactType;
use App\Models\ContactIdentification\PossibleContact;
use App\Services\ContactIdentification\ContactIdentifiers\ContactIdentifier;
use Exception;
use Illuminate\Support\Collection;

class ContactSortingService
{
    public function __construct()
    {

    }

    /**
     * Sort possible contacts by priority based on type and specific business rules.
     *
     * @param Collection $allPossibleContacts
     * @return Collection
     * @throws Exception
     */
    public function sortListByPriority(Collection $allPossibleContacts): Collection
    {
        if ($allPossibleContacts->isEmpty()) {
            return $allPossibleContacts;
        }

        $customSortedContacts = $this->applyCustomSorting($allPossibleContacts);

        $priorityOrderMap = $this->buildPriorityOrderMap();

        return $customSortedContacts
            ->sortBy(fn(PossibleContact $contact) => $this->getContactTypePriority($contact, $priorityOrderMap))
            ->values();
    }

    /**
     * Apply custom sorting logic within contact types before priority sorting.
     *
     * @param Collection $contacts
     * @return Collection
     * @throws Exception
     */
    private function applyCustomSorting(Collection $contacts): Collection
    {
        $groupedByType = $contacts->groupBy(fn($contact) => $contact->identifiable::class);

        $sortedContacts = collect();

        foreach ($groupedByType as $modelClass => $models) {
            /** @var ContactIdentifier $contactIdentifier */
            $contactIdentifier = app(ContactType::fromModelClass($modelClass)->getIdentifierClass());

            $sorted = $contactIdentifier->sortPossibleContacts($models);

            $sortedContacts->push(...$sorted);
        }

        return $sortedContacts;
    }

    /**
     * Build a map of contact types to their priority order.
     *
     * @return Collection
     */
    private function buildPriorityOrderMap(): Collection
    {
        $contactIdentificationService = new ContactIdentificationService();
        $identifiers = $contactIdentificationService->getIdentifiers();

        return collect($identifiers)
            ->map(fn($enum) => $enum->getModelClass())
            ->flip();
    }

    /**
     * Get the priority value for a contact based on its type.
     *
     * @param PossibleContact $contact
     * @param Collection $priorityOrderMap
     * @return int
     */
    private function getContactTypePriority(PossibleContact $contact, Collection $priorityOrderMap): int
    {
        $contactType = $contact->identifiable::class;
        return $priorityOrderMap[$contactType] ?? 999;
    }
}
