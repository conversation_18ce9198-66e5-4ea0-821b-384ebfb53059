<?php

namespace App\Services\ContactIdentification;

use App\DTO\ContactIdentification\Contact as ContactDTO;
use App\Enums\ContactIdentification\ContactType;
use App\Enums\ContactIdentification\IdentificationStatus;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Jobs\ContactIdentification\ContactIdentificationIdentifyJob;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use App\Models\User;
use Illuminate\Contracts\Container\BindingResolutionException;

class ContactIdentificationService
{
    /**
     * @var ContactType[]
     */
    protected array $identifiers = [
        ContactType::USER,
        ContactType::COMPANY_USER,
        ContactType::CONSUMER,
    ];

    /**
     * @param string $identifierValue
     * @param SearchableFieldType $type
     * @param bool $syncronous
     * @return IdentifiedContact|null
     */
    public function createIdentifiedContactAndDispatchJob(
        string $identifierValue,
        SearchableFieldType $type,
        bool $syncronous = false
    ): ?IdentifiedContact
    {
        if (empty($identifierValue)) return null;

        /** @var ?IdentifiedContact $identifiedContact */
        $identifiedContact = IdentifiedContact::query()
            ->where(IdentifiedContact::FIELD_IDENTIFIER_VALUE, $identifierValue)
            ->where(IdentifiedContact::FIELD_IDENTIFIER_FIELD_TYPE, $type->value)
            ->latest()
            ->first();

        if ($identifiedContact) {
            if ($identifiedContact?->nominatedContact?->{PossibleContact::RELATION_IDENTIFIED_CONTACT} === User::class) {
                return $identifiedContact;
            }

            $identifiedContact->update([
                IdentifiedContact::FIELD_IDENTIFIER_VALUE       => $identifierValue,
                IdentifiedContact::FIELD_IDENTIFIER_FIELD_TYPE  => $type->value,
                IdentifiedContact::FIELD_IDENTIFICATION_STATUS  => IdentificationStatus::IDENTIFYING,
            ]);
        } else {
            $identifiedContact = IdentifiedContact::query()->create([
                IdentifiedContact::FIELD_IDENTIFIER_VALUE       => $identifierValue,
                IdentifiedContact::FIELD_IDENTIFIER_FIELD_TYPE  => $type->value,
                IdentifiedContact::FIELD_IDENTIFICATION_STATUS  => IdentificationStatus::IDENTIFYING,
            ]);
        }

        if ($syncronous) {
            ContactIdentificationIdentifyJob::dispatchSync($identifiedContact);
            $identifiedContact->refresh();
        } else {
            ContactIdentificationIdentifyJob::dispatch($identifiedContact);
        }

        return $identifiedContact;
    }

    /**
     * TODO - Save results for data not found but searched?
     * @param string $value
     * @param SearchableFieldType $type
     * @return ContactDTO[]
     * @throws BindingResolutionException
     */
    public function identify(string $value, SearchableFieldType $type): array
    {
        $contacts = [];

        foreach ($this->identifiers as $identifier) {
            $identifierService = app()->make($identifier->getIdentifierClass());

            if (!in_array($type, $identifierService->getAvailableSearchableFieldTypes())) {
                continue;
            }

            $contacts = array_merge($contacts, $identifierService->search($value, $type));
        }

        return $contacts;
    }

    /**
     * @return ContactType[]
     */
    public function getIdentifiers(): array
    {
        return $this->identifiers;
    }
}
