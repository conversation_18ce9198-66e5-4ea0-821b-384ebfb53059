<?php

namespace App\Services;

use App\Enums\SocialMediaUrl;

class YoutubeHelperService
{
    /**
     * <PERSON><PERSON> creating an actual YouTube link for the given video ID.
     *
     * @param string|null $videoId
     * @return string|null
     */
    public static function generateYoutubeLinkFromVideoId(?string $videoId): string|null
    {
        if (is_null($videoId) || !strlen(trim($videoId))) {
            return null;
        }

        $baseUrl = SocialMediaUrl::YOUTUBE_VIDEO;

        return !empty($baseUrl)
            ? $baseUrl->value . $videoId
            : null;
    }
}
