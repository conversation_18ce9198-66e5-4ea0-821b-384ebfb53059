<?php

namespace App\Notifications;

use Carbon\Carbon;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class VerifyReviewerEmail extends VerifyEmail
{
    /**
     * @param $callback
     */
    public function __construct($callback)
    {
        $this->toMailUsing($callback);
    }
    /**
     * @return void
     */
    static function registerConfig(): void
    {
        VerifyReviewerEmail::toMailUsing(function ($notifiable, $url){
            return (new MailMessage)
                //TODO: Customise the message subject/content/from address etc.
                ->subject('Fixr: Please Verify Email Address')
                ->line('Click the button below to verify your email address.')
                ->action('Verify Email Address', $url);
        });

        VerifyReviewerEmail::createUrlUsing(function ($notifiable) {
            // Construct the URL for the Fixr project's verification page
            $domainURL = config('app.fixr_domain');
            return $domainURL . '/consumer-reviews/verify-email/' . $notifiable->reference;
        });
    }
}
