<?php

namespace App\Notifications;

use App\Models\Odin\CompanyUser;
use Carbon\Carbon;
use Illuminate\Auth\Notifications\VerifyEmail;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\URL;

class VerifyCompanyUserEmail extends VerifyEmail
{
    /**
     * This Notification is to allow making changes to CompanyUser verification
     * without polluting the base VerifyEmail Notification
     */

    static function registerConfig(string $registrationDomain, CompanyUser $companyUser): void
    {
        VerifyCompanyUserEmail::toMailUsing(function ($notifiable, $url) use ($registrationDomain, $companyUser) {
            return (new MailMessage)
                ->from("no-reply@$registrationDomain")
                ->greeting("Hi {$companyUser->first_name},")
                ->salutation("Thank you for registering with $registrationDomain!")
                ->subject('Please Verify Email Address')
                ->line('Click the button below to verify your email address.')
                ->action('Verify Email Address', $url);
        });
        VerifyCompanyUserEmail::createUrlUsing(function ($notifiable) {
            return URL::temporarySignedRoute(
                'company-registration.v2.verification.verify',
                Carbon::now()->addMinutes(Config::get('auth.verification.expire', 60)),
                [
                    'id' => $notifiable->getKey(),
                    'hash' => sha1($notifiable->getEmailForVerification()),
                ]
            );
        });
    }
}
