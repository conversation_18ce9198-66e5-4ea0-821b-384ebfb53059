<?php

namespace App\Notifications\Slack;

use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Bus\Queueable;

class UserCompanyAlertsSlackNotification extends CompanyAlertsSlackNotification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        Company $company,
        string $changeTitle,
        protected User $user,
    )
    {
        parent::__construct($company, $changeTitle);
    }

    public function getSlackRoute(): ?StringableSlackRoute
    {

        if (empty($this->user->slack_username)) {
            logger()->error('user missing slack username');
            return null;
        }

        return new StringableSlackRoute(
            $this->user->slack_username,
            config('services.slack.admin_system_bot_auth_token'),
        );
    }
}
