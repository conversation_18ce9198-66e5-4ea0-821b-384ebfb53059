<?php

namespace App\Notifications\Slack;

use App\Enums\GlobalConfigurationSlackNotificationField;
use App\Enums\Odin\Region;
use App\Events\FloorPricingUpdatedEvent;
use App\Models\Legacy\Location;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Slack\BlockKit\Blocks\SectionBlock;
use Illuminate\Notifications\Slack\SlackMessage;
use Illuminate\Notifications\Slack\SlackRoute;

class FloorPricingUpdatedSlackNotification extends BaseSlackNotification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(
        protected string $scope,
    protected string $scopeTitle,
    protected string $serviceTitle,
    protected string $industryTitle,
    protected string $locationTitle,
    protected array  $priceChanges,
    )
    {}

    /**
     * @inheritDoc
     */
    public function toSlack(object $notifiable): SlackMessage
    {
        $text = $this->scope === Region::NATIONAL->value ? 'Nationally' : "in $this->locationTitle";

        return (new SlackMessage)
            ->headerBlock("Floor Pricing updated " . $text)
            ->sectionBlock(function (SectionBlock $block) {
                $block->field("*Info*")->markdown();
                $block->field(" ");
                $block->field("• *Industry:* $this->industryTitle - $this->serviceTitle")->markdown();
                $block->field(" ");
                $block->field("• *Type:* $this->scopeTitle")->markdown();
                $block->field(" ");
            })->sectionBlock(function (SectionBlock $block) {
                $text = "*Price Changes*\n";

                foreach ($this->priceChanges as $type => $change) {
                    $typeTitle = SaleType::query()
                        ->where(SaleType::FIELD_KEY, $type)
                        ->first()
                        ?->{SaleType::FIELD_NAME};

                    if (empty($typeTitle)) {
                        logger()->error("Unable to retrieve sale type: $type from database.");
                        continue;
                    }

                    $oldPrice = $change['old'];
                    $newPrice = $change['new'];

                    $text .= "• *$typeTitle:* $$oldPrice → $$newPrice\n";
                }

                $block->text($text)->markdown();
            });


    }

    /**
     * @inheritDoc
     */
    public function getSlackRoute(): ?StringableSlackRoute
    {
        $channelField = GlobalConfigurationSlackNotificationField::PRICING_CHANNEL;
        $tokenField = GlobalConfigurationSlackNotificationField::PRICING_SLACK_BOT_USER_OAUTH_TOKEN;

        try {
            [$channel, $slackToken] = $this->getSlackRouteGlobalConfigVariables($channelField, $tokenField);
        } catch (Exception $e) {
            logger()->error($e->getMessage());
            return null;
        }

        return new StringableSlackRoute(
            $channel,
            $slackToken
        );
    }

    /**
     * @param FloorPricingUpdatedEvent $event
     * @return FloorPricingUpdatedSlackNotification|null
     */
    public static function createFromEvent(FloorPricingUpdatedEvent $event): ?FloorPricingUpdatedSlackNotification
    {
        $scope            = $event->pricingScope;
        $serviceProductId = $event->serviceProductId;
        $locationId       = $event->locationId;
        $data             = $event->data;

        if (empty($data)) {
            logger()->error("Floor pricing updated event triggered yet price change data array is empty");
            return null;
        }

        $scopeTitle = Region::tryFrom($scope)?->getTitle();
        if (empty($scopeTitle)) {
            logger()->error("Unable to retrieve retrieve region scope for scope: $scope");
            return null;
        }

        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()->find($serviceProductId);
        if (empty($serviceProduct)) {
            logger()->error("Unable to retrieve service product id: $serviceProductId");
            return null;
        }

        $serviceTitle  = $serviceProduct->service?->name;
        $industryTitle = $serviceProduct->service?->industry?->name;
        if (empty($industryTitle)) {
            logger()->error("Unable to retrieve either service or industry for: $serviceProductId");
            return null;
        }

        if ($scope === Region::NATIONAL->value) {
            $locationTitle = '';
        } else {
            $targetLocation = Location::query()->find($locationId);

            if (empty($targetLocation)) {
                logger()->error("Could not find location for location id: $locationId");
                return null;
            }

            $locationTitle = $scope === Region::STATE->value
                ? $targetLocation->{Location::STATE}
                : $targetLocation->{Location::COUNTY};
        }

        return new self(
            scope: $scope,
            scopeTitle: $scopeTitle,
            serviceTitle: $serviceTitle,
            industryTitle: $industryTitle,
            locationTitle: $locationTitle,
            priceChanges: $data,
        );
    }
}
