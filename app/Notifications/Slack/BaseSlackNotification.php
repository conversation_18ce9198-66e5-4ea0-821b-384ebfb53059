<?php

namespace App\Notifications\Slack;

use App\Enums\GlobalConfigurationKey;
use App\Enums\GlobalConfigurationSlackNotificationField;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Notifications\Slack\SlackMessage;

abstract class BaseSlackNotification extends Notification implements ShouldQueue
{
    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['slack'];
    }

    /**
     * @param object $notifiable
     * @return SlackMessage
     */
    abstract public function toSlack(object $notifiable): SlackMessage;

    /**
     * Returns the slack route to send the notification.
     *
     * @return ?StringableSlackRoute
     */
    abstract public function getSlackRoute(): ?StringableSlackRoute;

    /**
     * @param GlobalConfigurationSlackNotificationField $channel
     * @param GlobalConfigurationSlackNotificationField $token
     * @return array
     * @throws Exception
     */
    public function getSlackRouteGlobalConfigVariables(
        GlobalConfigurationSlackNotificationField $channel,
        GlobalConfigurationSlackNotificationField $token
    ): array
    {
        /** @var GlobalConfigurationRepository $globalConfigurationRepository */
        $globalConfigurationRepository = app()->make(GlobalConfigurationRepository::class);

        $globalConfigPayload = $globalConfigurationRepository
                                   ->getConfigurationPayload(GlobalConfigurationKey::SLACK)
                                   ?->toArray()['data'];

        if (empty($globalConfigPayload)) {
            throw new Exception("Unable to retrieve config payload for slack notifications.");
        }

        return [$globalConfigPayload[$channel->value], $globalConfigPayload[$token->value]];
    }

}