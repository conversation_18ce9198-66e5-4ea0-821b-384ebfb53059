<?php

namespace App\Notifications\Slack;

use Illuminate\Notifications\Slack\SlackRoute;
use Stringable;

/**
 * Need to use this instead of the SlackRoute because
 * otherwise telescopes NotificationWatcher cannot convert the object to a string.
 */
class StringableSlackRoute extends SlackRoute implements Stringable
{
    public function __construct(string $channel = null, string $token = null)
    {
        parent::__construct($channel, $token);
    }

    public function __toString(): string
    {
        return $this->channel ?? '';
    }
}