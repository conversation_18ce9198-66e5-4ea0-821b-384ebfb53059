<?php

namespace App\Constants\Company\Search;

class FilterRequestParams
{
    const REQUEST_ACCOUNT_MANAGER_IDS                                  = 'account_manager_ids';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT                     = 'amount_of_leads_purchased_object';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_ACTIVE              = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.active';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_DATE_FORMAT         = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.date_format';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_DATE_TOGGLED  = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.first_date_toggled';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_FROM_DATE     = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.first_from_date';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_OPERATOR      = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.first_operator';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_TO_DATE       = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.first_to_date';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_FIRST_VALUE         = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.first_value';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_LOGICAL             = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.logical';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_DATE_TOGGLED = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.second_date_toggled';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_FROM_DATE    = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.second_from_date';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_OPERATOR     = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.second_operator';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_TO_DATE      = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.second_to_date';
    const REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT_SECOND_VALUE        = self::REQUEST_AMOUNT_OF_LEADS_PURCHASED_OBJECT.'.second_value';
    const REQUEST_APPOINTMENT_REJECTION                                = self::REQUEST_REJECTION.'.appointment';
    const REQUEST_APPOINTMENT_REJECTION_FIRST_INPUT                    = self::REQUEST_APPOINTMENT_REJECTION.'.first.input';
    const REQUEST_APPOINTMENT_REJECTION_FIRST_OPERATOR                 = self::REQUEST_APPOINTMENT_REJECTION.'.first.operator';
    const REQUEST_APPOINTMENT_REJECTION_LOGICAL                        = self::REQUEST_APPOINTMENT_REJECTION.'.logical';
    const REQUEST_APPOINTMENT_REJECTION_SECOND_INPUT                   = self::REQUEST_APPOINTMENT_REJECTION.'.second.input';
    const REQUEST_APPOINTMENT_REJECTION_SECOND_OPERATOR                = self::REQUEST_APPOINTMENT_REJECTION.'.second.operator';
    const REQUEST_CADENCE                                              = 'cadence';
    const REQUEST_CAMPAIGN                                             = 'campaign';
    const REQUEST_CAMPAIGN_BUDGET                                      = self::REQUEST_CAMPAIGN.'.budget';
    const REQUEST_CAMPAIGN_BUDGET_COST                                 = 'campaign.budget.cost';
    const REQUEST_CAMPAIGN_BUDGET_COST_INPUT                           = 'campaign.budget.cost.input';
    const REQUEST_CAMPAIGN_BUDGET_COST_OPERATOR                        = 'campaign.budget.cost.operator';
    const REQUEST_CAMPAIGN_BUDGET_NO_LIMIT                             = 'campaign.budget.no_limit';
    const REQUEST_CAMPAIGN_BUDGET_NO_LIMIT_SELECTED                    = 'campaign.budget.no_limit.selected';
    const REQUEST_CAMPAIGN_BUDGET_VOLUME                               = 'campaign.budget.volume';
    const REQUEST_CAMPAIGN_BUDGET_VOLUME_INPUT                         = 'campaign.budget.volume.input';
    const REQUEST_CAMPAIGN_BUDGET_VOLUME_OPERATOR                      = 'campaign.budget.volume.operator';
    const REQUEST_CAMPAIGN_SERVICE_AREAS                               = 'campaign.service_areas';
    const REQUEST_CAMPAIGN_SERVICE_AREAS_LOGICAL                       = 'campaign.service_areas_logical';
    const REQUEST_CAMPAIGN_STATUS_IDS                                  = self::REQUEST_CAMPAIGN.'.status_ids';
    const REQUEST_COMPANY_NAME                                         = 'company_name';
    const REQUEST_INDUSTRY_IDS                                         = 'industry_ids';
    const REQUEST_INDUSTRY_LOGICAL                                     = 'industry_logical';
    const REQUEST_LEAD_REJECTION                                       = self::REQUEST_REJECTION.'.lead';
    const REQUEST_LEAD_REJECTION_FIRST_INPUT                           = self::REQUEST_LEAD_REJECTION.'.first.input';
    const REQUEST_LEAD_REJECTION_FIRST_OPERATOR                        = self::REQUEST_LEAD_REJECTION.'.first.operator';
    const REQUEST_LEAD_REJECTION_LOGICAL                               = self::REQUEST_LEAD_REJECTION.'.logical';
    const REQUEST_LEAD_REJECTION_SECOND_INPUT                          = self::REQUEST_LEAD_REJECTION.'.second.input';
    const REQUEST_LEAD_REJECTION_SECOND_OPERATOR                       = self::REQUEST_LEAD_REJECTION.'.second.operator';
    const REQUEST_NEVER_EXCEEDS_BUDGET                                 = 'never_exceeds_budget';
    const REQUEST_OFFICE_LOCATION_IDS                                  = 'office_location_ids';
    const REQUEST_OFFICE_LOCATION_LOGICAL                              = 'office_location_logical';
    const REQUEST_ORDER_BY                                             = 'order_by';
    const REQUEST_PAGE                                                 = 'page';
    const REQUEST_PER_PAGE                                             = 'per_page_number_of_records';
    const REQUEST_REJECTION                                            = 'rejection';
    const REQUEST_SALES_STATUS_IDS                                     = 'sales_status_ids';
    const REQUEST_STATE_ABBREVIATIONS                                  = 'state_abbreviations';
    const REQUEST_STATUS_IDS                                           = 'status_ids';
    const REQUEST_SUCCESS_MANAGER_IDS                                  = 'success_manager_ids';
    const REQUEST_EMPLOYEE_COUNT                                       = 'employee_count';
    const REQUEST_EMPLOYEE_COUNT_FIRST_INPUT                           = self::REQUEST_EMPLOYEE_COUNT.'.first.input';
    const REQUEST_EMPLOYEE_COUNT_FIRST_OPERATOR                        = self::REQUEST_EMPLOYEE_COUNT.'.first.operator';
    const REQUEST_EMPLOYEE_COUNT_LOGICAL                               = self::REQUEST_EMPLOYEE_COUNT.'.logical';
    const REQUEST_EMPLOYEE_COUNT_SECOND_INPUT                          = self::REQUEST_EMPLOYEE_COUNT.'.second.input';
    const REQUEST_EMPLOYEE_COUNT_SECOND_OPERATOR                       = self::REQUEST_EMPLOYEE_COUNT.'.second.operator';
    const REQUEST_ESTIMATED_REVENUE                                    = 'estimated_revenue';
    const REQUEST_ESTIMATED_REVENUE_FIRST_INPUT                        = self::REQUEST_ESTIMATED_REVENUE.'.first.input';
    const REQUEST_ESTIMATED_REVENUE_FIRST_OPERATOR                     = self::REQUEST_ESTIMATED_REVENUE.'.first.operator';
    const REQUEST_ESTIMATED_REVENUE_LOGICAL                            = self::REQUEST_ESTIMATED_REVENUE.'.logical';
    const REQUEST_ESTIMATED_REVENUE_SECOND_INPUT                       = self::REQUEST_ESTIMATED_REVENUE.'.second.input';
    const REQUEST_ESTIMATED_REVENUE_SECOND_OPERATOR                    = self::REQUEST_ESTIMATED_REVENUE.'.second.operator';
    const REQUEST_GOOGLE_RATING                                        = 'google_rating';
    const REQUEST_GOOGLE_RATING_FIRST_INPUT                            = self::REQUEST_GOOGLE_RATING.'.first.input';
    const REQUEST_GOOGLE_RATING_FIRST_OPERATOR                         = self::REQUEST_GOOGLE_RATING.'.first.operator';
    const REQUEST_GOOGLE_RATING_LOGICAL                                = self::REQUEST_GOOGLE_RATING.'.logical';
    const REQUEST_GOOGLE_RATING_SECOND_INPUT                           = self::REQUEST_GOOGLE_RATING.'.second.input';
    const REQUEST_GOOGLE_RATING_SECOND_OPERATOR                        = self::REQUEST_GOOGLE_RATING.'.second.operator';
    const REQUEST_GOOGLE_REVIEW_COUNT                                  = 'google_review_count';
    const REQUEST_GOOGLE_REVIEW_COUNT_FIRST_INPUT                      = self::REQUEST_GOOGLE_REVIEW_COUNT.'.first.input';
    const REQUEST_GOOGLE_REVIEW_COUNT_FIRST_OPERATOR                   = self::REQUEST_GOOGLE_REVIEW_COUNT.'.first.operator';
    const REQUEST_GOOGLE_REVIEW_COUNT_LOGICAL                          = self::REQUEST_GOOGLE_REVIEW_COUNT.'.logical';
    const REQUEST_GOOGLE_REVIEW_COUNT_SECOND_INPUT                     = self::REQUEST_GOOGLE_REVIEW_COUNT.'.second.input';
    const REQUEST_GOOGLE_REVIEW_COUNT_SECOND_OPERATOR                  = self::REQUEST_GOOGLE_REVIEW_COUNT.'.second.operator';
    const REQUEST_CONSUMER_RATING                                      = 'consumer_rating';
    const REQUEST_CONSUMER_RATING_FIRST_INPUT                          = self::REQUEST_CONSUMER_RATING.'.first.input';
    const REQUEST_CONSUMER_RATING_FIRST_OPERATOR                       = self::REQUEST_CONSUMER_RATING.'.first.operator';
    const REQUEST_CONSUMER_RATING_LOGICAL                              = self::REQUEST_CONSUMER_RATING.'.logical';
    const REQUEST_CONSUMER_RATING_SECOND_INPUT                         = self::REQUEST_CONSUMER_RATING.'.second.input';
    const REQUEST_CONSUMER_RATING_SECOND_OPERATOR                      = self::REQUEST_CONSUMER_RATING.'.second.operator';
    const REQUEST_CONSUMER_REVIEW_COUNT                                = 'consumer_review_count';
    const REQUEST_CONSUMER_REVIEW_COUNT_FIRST_INPUT                    = self::REQUEST_CONSUMER_REVIEW_COUNT.'.first.input';
    const REQUEST_CONSUMER_REVIEW_COUNT_FIRST_OPERATOR                 = self::REQUEST_CONSUMER_REVIEW_COUNT.'.first.operator';
    const REQUEST_CONSUMER_REVIEW_COUNT_LOGICAL                        = self::REQUEST_CONSUMER_REVIEW_COUNT.'.logical';
    const REQUEST_CONSUMER_REVIEW_COUNT_SECOND_INPUT                   = self::REQUEST_CONSUMER_REVIEW_COUNT.'.second.input';
    const REQUEST_CONSUMER_REVIEW_COUNT_SECOND_OPERATOR                = self::REQUEST_CONSUMER_REVIEW_COUNT.'.second.operator';
}
