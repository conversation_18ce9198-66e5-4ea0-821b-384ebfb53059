<?php

namespace App\DTO\CompanyCampaignData;

use App\DTO\DTOContract;
use App\Models\CompanyCampaignData;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class CompanyCampaignLeadAggregatesDTO implements DTOContract
{
    const string ID                                   = 'id';
    const string CAMPAIGN_ID                          = 'campaignId';
    const string CREATED_AT                           = 'createdAt';
    const string UPDATED_AT                           = 'updatedAt';
    const string LEAD_LAST_SOLD_AT                    = 'leadLastSoldAt';
    const string AVERAGE_LEAD_COST_LAST_CALCULATED_AT = 'averageLeadCostLastCalculatedAt';
    const string AVERAGE_LEAD_COST_LEAD_COUNT         = 'averageLeadCostLeadCount';
    const string AVERAGE_LEAD_COST                    = 'averageLeadCost';

    /**
     * @param int|null $id
     * @param int|null $campaignId
     * @param Carbon|null $createdAt
     * @param Carbon|null $updatedAt
     * @param Carbon|null $leadLastSoldAt
     * @param Carbon|null $averageLeadCostLastCalculatedAt
     * @param int|null $averageLeadCostLeadCount
     * @param float|null $averageLeadCost
     */
    public function __construct(
        protected ?int    $id = null,
        protected ?int    $campaignId = null,
        protected ?Carbon $createdAt = null,
        protected ?Carbon $updatedAt = null,
        protected ?Carbon $leadLastSoldAt = null,
        protected ?Carbon $averageLeadCostLastCalculatedAt = null,
        protected ?int    $averageLeadCostLeadCount = null,
        protected ?float  $averageLeadCost = null,
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::ID                                   => $this->id,
            self::CAMPAIGN_ID                          => $this->campaignId,
            self::CREATED_AT                           => $this->createdAt->toIso8601String(),
            self::UPDATED_AT                           => $this->updatedAt->toIso8601String(),
            self::LEAD_LAST_SOLD_AT                    => $this->leadLastSoldAt->toIso8601String(),
            self::AVERAGE_LEAD_COST_LAST_CALCULATED_AT => $this->averageLeadCostLastCalculatedAt->toIso8601String(),
            self::AVERAGE_LEAD_COST_LEAD_COUNT         => $this->averageLeadCostLeadCount,
            self::AVERAGE_LEAD_COST                    => $this->averageLeadCost,
        ];
    }

    /**
     * @param array $array
     * @return CompanyCampaignLeadAggregatesDTO
     */
    public static function fromArray(array $array): self
    {
        return new self(
            id                             : Arr::get($array, self::ID),
            campaignId                     : Arr::get($array, self::CAMPAIGN_ID),
            createdAt                      : Arr::get($array, self::CREATED_AT),
            updatedAt                      : Arr::get($array, self::UPDATED_AT),
            leadLastSoldAt                 : Arr::get($array, self::LEAD_LAST_SOLD_AT),
            averageLeadCostLastCalculatedAt: Arr::get($array, self::AVERAGE_LEAD_COST_LAST_CALCULATED_AT),
            averageLeadCostLeadCount       : Arr::get($array, self::AVERAGE_LEAD_COST_LEAD_COUNT),
            averageLeadCost                : Arr::get($array, self::AVERAGE_LEAD_COST),
        );
    }

    /**
     * @param CompanyCampaignData $companyCampaignData
     * @return self
     */
    public static function fromModel(CompanyCampaignData $companyCampaignData): self
    {
        return new self(
            id                             : $companyCampaignData->id,
            campaignId                     : $companyCampaignData->campaign_id,
            createdAt                      : Carbon::parse($companyCampaignData->created_at),
            updatedAt                      : Carbon::parse($companyCampaignData->updated_at),
            leadLastSoldAt                 : Carbon::parse($companyCampaignData->payload[self::LEAD_LAST_SOLD_AT]),
            averageLeadCostLastCalculatedAt: Carbon::parse($companyCampaignData->payload[self::AVERAGE_LEAD_COST_LAST_CALCULATED_AT]),
            averageLeadCostLeadCount       : $companyCampaignData->payload[self::AVERAGE_LEAD_COST_LEAD_COUNT],
            averageLeadCost                : $companyCampaignData->payload[self::AVERAGE_LEAD_COST],
        );
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return int|null
     */
    public function getCampaignId(): ?int
    {
        return $this->campaignId;
    }

    /**
     * @return Carbon|null
     */
    public function getCreatedAt(): ?Carbon
    {
        return $this->createdAt;
    }

    /**
     * @return Carbon|null
     */
    public function getUpdatedAt(): ?Carbon
    {
        return $this->updatedAt;
    }

    /**
     * @return Carbon|null
     */
    public function getLeadLastSoldAt(): ?Carbon
    {
        return $this->leadLastSoldAt;
    }

    /**
     * @return Carbon|null
     */
    public function getAverageLeadCostLastCalculatedAt(): ?Carbon
    {
        return $this->averageLeadCostLastCalculatedAt;
    }

    /**
     * @return int|null
     */
    public function getAverageLeadCostLeadCount(): ?int
    {
        return $this->averageLeadCostLeadCount;
    }

    /**
     * @return float|null
     */
    public function getAverageLeadCost(): ?float
    {
        return $this->averageLeadCost;
    }
}
