<?php

namespace App\DTO\RefundRequest;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class RefundRequestPayload implements DTOContract
{
    const string FIELD_AUTHOR_ID = 'author_id';
    const string FIELD_REASON    = 'reason';

    public function __construct(
        protected ?int $authorId = null,
        protected ?string $reason = null,
        protected Collection $items = new Collection()
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_AUTHOR_ID => $this->authorId,
            self::FIELD_REASON    => $this->reason,
        ];
    }

    /**
     * @param array $array
     * @return RefundRequestPayload
     */
    public static function fromArray(array $array): self
    {
        return new self(
            authorId: Arr::get($array, self::FIELD_AUTHOR_ID),
            reason  : Arr::get($array, self::FIELD_REASON),
        );
    }
}
