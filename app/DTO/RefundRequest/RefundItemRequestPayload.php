<?php

namespace App\DTO\RefundRequest;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class RefundItemRequestPayload implements DTOContract
{
    const string FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const string FIELD_VALUE                 = 'value';

    public function __construct(
        protected ?string $productAssignmentId = null,
        protected ?string $value = null,
    )
    {

    }

    public function toArray(): array
    {
        return [
            self::FIELD_PRODUCT_ASSIGNMENT_ID => $this->productAssignmentId,
            self::FIELD_VALUE                 => $this->value,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            productAssignmentId: Arr::get($array, self::FIELD_PRODUCT_ASSIGNMENT_ID),
            value              : Arr::get($array, self::FIELD_VALUE),
        );
    }
}
