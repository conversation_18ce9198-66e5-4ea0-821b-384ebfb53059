<?php

namespace App\DTO\BillingWorkflow;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class ActivityArgument implements DTOContract
{
    const string FIELD_EVENT_CLASS_TRIGGER = 'event_class_trigger';
    const string FIELD_CONTEXT             = 'context';
    const string FIELD_ACTION_DATA         = 'action_data';

    public function __construct(
        protected string $eventClassTrigger,
        protected array $context = [],
        protected array $actionData = [],
    )
    {

    }

    /**
     * @return string
     */
    public function getEventClassTrigger(): string
    {
        return $this->eventClassTrigger;
    }

    /**
     * @param string $eventClassTrigger
     * @return void
     */
    public function setEventClassTrigger(string $eventClassTrigger): void
    {
        $this->eventClassTrigger = $eventClassTrigger;
    }

    /**
     * @return array
     */
    public function getContext(): array
    {
        return $this->context;
    }

    /**
     * @param array $context
     * @return void
     */
    public function setContext(array $context): void
    {
        $this->context = $context;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_EVENT_CLASS_TRIGGER => $this->eventClassTrigger,
            self::FIELD_CONTEXT             => $this->context,
            self::FIELD_ACTION_DATA         => $this->actionData,
        ];
    }

    /**
     * @param array $array
     * @return ActivityArgument
     */
    public static function fromArray(array $array): ActivityArgument
    {
        return new self(
            eventClassTrigger: Arr::get($array, self::FIELD_EVENT_CLASS_TRIGGER),
            context          : Arr::get($array, self::FIELD_CONTEXT),
            actionData       : Arr::get($array, self::FIELD_ACTION_DATA),
        );
    }

    /**
     * @return array
     */
    public function getActionData(): array
    {
        return $this->actionData;
    }

    /**
     * @param array|null $actionData
     * @return void
     */
    public function setActionData(?array $actionData = null): void
    {
        $this->actionData = $actionData ?? [];
    }
}
