<?php

namespace App\DTO\Notes;

use App\DTO\DTOContract;
use App\Enums\Notes\NoteRelationType;
use Illuminate\Support\Arr;

class CreateNoteParam implements DTOContract
{
    const FIELD_CONTENT       = 'content';
    const FIELD_RELATION_ID   = 'relation_id';
    const FIELD_RELATION_TYPE = 'relation_type';
    const FIELD_USER_ID       = 'user_id';
    const FIELD_PARENT_ID     = 'parent_id';

    public function __construct(
        protected ?string $content = null,
        protected ?int $relationId = null,
        protected ?NoteRelationType $relationType = null,
        protected ?int $userId = null,
        protected ?int $parentId = null,
    )
    {

    }

    /**
     * @return string|null
     */
    public function getContent(): ?string
    {
        return $this->content;
    }

    /**
     * @param string|null $content
     */
    public function setContent(?string $content): void
    {
        $this->content = $content;
    }

    /**
     * @return int|null
     */
    public function getRelationId(): ?int
    {
        return $this->relationId;
    }

    /**
     * @param int|null $relationId
     */
    public function setRelationId(?int $relationId): void
    {
        $this->relationId = $relationId;
    }

    /**
     * @return NoteRelationType|null
     */
    public function getRelationType(): ?NoteRelationType
    {
        return $this->relationType;
    }

    /**
     * @param NoteRelationType|null $relationType
     */
    public function setRelationType(?NoteRelationType $relationType): void
    {
        $this->relationType = $relationType;
    }

    /**
     * @return int|null
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * @param int|null $userId
     */
    public function setUserId(?int $userId): void
    {
        $this->userId = $userId;
    }

    /**
     * @return int|null
     */
    public function getParentId(): ?int
    {
        return $this->parentId;
    }

    /**
     * @param int|null $parentId
     */
    public function setParentId(?int $parentId): void
    {
        $this->parentId = $parentId;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_CONTENT         => $this->content,
            self::FIELD_RELATION_ID     => $this->relationId,
            self::FIELD_RELATION_TYPE   => $this->relationType->value,
            self::FIELD_USER_ID         => $this->userId,
            self::FIELD_PARENT_ID       => $this->parentId,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::FIELD_CONTENT),
            Arr::get($array, self::FIELD_RELATION_ID),
            NoteRelationType::tryFrom(Arr::get($array, self::FIELD_RELATION_TYPE)),
            Arr::get($array, self::FIELD_USER_ID),
            Arr::get($array, self::FIELD_PARENT_ID),
        );
    }
}
