<?php

namespace App\DTO\LeadRefund;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class LeadRefundItemPayload implements DTOContract
{

    const string FIELD_PRODUCT_ASSIGNMENT_ID = 'product_assignment_id';
    const string FIELD_COST                  = 'cost';
    const string FIELD_REFUND_TYPE           = 'refund_type';
    const string FIELD_REFUND_REASON         = 'refund_reason';

    /**
     * @param int $productAssignmentId
     * @param float $cost
     * @param string $refundType
     * @param string|null $refundReason
     */
    public function __construct(
        protected int $productAssignmentId,
        protected float $cost,
        protected string $refundType,
        protected ?string $refundReason = null,
    )
    {

    }

    /**
     * @return float|null
     */
    public function getCost(): ?float
    {
        return $this->cost;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_PRODUCT_ASSIGNMENT_ID => $this->productAssignmentId,
            self::FIELD_COST                  => $this->cost,
            self::FIELD_REFUND_TYPE           => $this->refundType,
            self::FIELD_REFUND_REASON         => $this->refundReason,
        ];
    }

    /**
     * @param float|null $cost
     * @return void
     */
    public function setValue(?float $cost): void
    {
        $this->cost = $cost;
    }

    /**
     * @param string|null $refundType
     * @return void
     */
    public function setType(?string $refundType): void
    {
        $this->refundType = $refundType;
    }

    /**
     * @return string|null
     */
    public function getRefundReason(): ?string
    {
        return $this->refundReason;
    }

    /**
     * @return string|null
     */
    public function getType(): ?string
    {
        return $this->refundType;
    }

    /**
     * @return int|null
     */
    public function getProductAssignmentId(): ?int
    {
        return $this->productAssignmentId;
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            productAssignmentId: Arr::get($array, self::FIELD_PRODUCT_ASSIGNMENT_ID),
            cost               : Arr::get($array, self::FIELD_COST),
            refundType         : Arr::get($array, self::FIELD_REFUND_TYPE),
            refundReason       : Arr::get($array, self::FIELD_REFUND_REASON),
        );
    }

    /**
     * @param string|null $refundReason
     * @return void
     */
    public function setRefundReason(?string $refundReason): void
    {
        $this->refundReason = $refundReason;
    }
}
