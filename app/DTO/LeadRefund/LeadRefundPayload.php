<?php

namespace App\DTO\LeadRefund;

use App\DTO\DTOContract;
use App\Models\Odin\ProductAssignment;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class LeadRefundPayload implements DTOContract
{
    const string FIELD_AUTHOR_ID = 'author_id';
    const string FIELD_ITEMS     = 'items';

    private int $companyId;

    /**
     * @param int $authorId
     * @param Collection $items
     */
    public function __construct(
        protected int $authorId,
        protected Collection $items = new Collection(),
    )
    {

    }

    /**
     * @return int
     * @throws Exception
     */
    public function getCompanyId(): int
    {
        if (isset($this->companyId)) return $this->companyId;

        $productAssignmentIds = $this->getItems()->map(fn ($item) => $item->getProductAssignmentId());

        $companyIds = ProductAssignment::query()
            ->select(ProductAssignment::FIELD_COMPANY_ID)
            ->whereIn(ProductAssignment::FIELD_ID, $productAssignmentIds)
            ->groupBy(ProductAssignment::FIELD_COMPANY_ID)
            ->get();

        if ($companyIds->count() > 1) {
            throw new Exception('Lead refund cross companies not supported');
        }

        $this->companyId = $companyIds->first()->{ProductAssignment::FIELD_COMPANY_ID};

        return $this->companyId;
    }

    /**
     * @return Collection<LeadRefundItemPayload>
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    /**
     * @return int|null
     */
    public function getAuthorId(): ?int
    {
        return $this->authorId;
    }

    /**
     * @
     */
    public function getTotal(): float
    {
        return $this->items->sum(fn(LeadRefundItemPayload $itemPayload) => $itemPayload->getCost() ?? 0);
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_AUTHOR_ID => $this->authorId,
            self::FIELD_ITEMS     => $this->items->toArray(),
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            authorId: Arr::get($array, self::FIELD_AUTHOR_ID),
            items   : collect(Arr::get($array, self::FIELD_ITEMS))
                ->map(fn(array $data) => LeadRefundItemPayload::fromArray($data)),
        );
    }

    /**
     * @param LeadRefundItemPayload $itemPayload
     * @return void
     */
    public function addItem(LeadRefundItemPayload $itemPayload): void
    {
        $this->items->push($itemPayload);
    }
}
