<?php

namespace App\DTO\LeadRefund;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class LeadRefundItemFormattedData implements DTOContract
{
    const string FIELD_COMPANY_ID          = 'company_id';
    const string FIELD_TYPE                = 'type';
    const string FIELD_INVOICE_ITEM_ID     = 'invoice_item_id';
    const string FIELD_LEAD_REFUND_ID      = 'lead_refund_id';
    const string FIELD_LEAD_REFUND_ITEM_ID = 'lead_refund_item_id';
    const string FIELD_INVOICE_ID          = 'invoice_id';
    const string FIELD_VALUE               = 'value';

    /**
     * @param int $companyId
     * @param string $type
     * @param int $invoiceItemId
     * @param int $leadRefundId
     * @param int $leadRefundItemId
     * @param int $invoiceId
     * @param float $value
     */
    public function __construct(
        protected int $companyId,
        protected string $type,
        protected int $invoiceItemId,
        protected int $leadRefundId,
        protected int $leadRefundItemId,
        protected int $invoiceId,
        protected float $value,
    )
    {

    }


    /**
     * @return float|null
     */
    public function getValue(): ?float
    {
        return $this->value;
    }

    /**
     * @return int|null
     */
    public function getInvoiceId(): ?int
    {
        return $this->invoiceId;
    }

    /**
     * @return int|null
     */
    public function getLeadRefundItemId(): ?int
    {
        return $this->leadRefundItemId;
    }

    /**
     * @return int|null
     */
    public function getLeadRefundId(): ?int
    {
        return $this->leadRefundId;
    }

    /**
     * @return int|null
     */
    public function getInvoiceItemId(): ?int
    {
        return $this->invoiceItemId;
    }

    /**
     * @return string|null
     */
    public function getType(): ?string
    {
        return $this->type;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_COMPANY_ID          => $this->companyId,
            self::FIELD_TYPE                => $this->type,
            self::FIELD_INVOICE_ITEM_ID     => $this->invoiceItemId,
            self::FIELD_LEAD_REFUND_ID      => $this->leadRefundId,
            self::FIELD_LEAD_REFUND_ITEM_ID => $this->leadRefundItemId,
            self::FIELD_INVOICE_ID          => $this->invoiceId,
            self::FIELD_VALUE               => $this->value,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new LeadRefundItemFormattedData(
            companyId       : Arr::get($array, self::FIELD_COMPANY_ID),
            type            : Arr::get($array, self::FIELD_TYPE),
            invoiceItemId   : Arr::get($array, self::FIELD_INVOICE_ITEM_ID),
            leadRefundId    : Arr::get($array, self::FIELD_LEAD_REFUND_ID),
            leadRefundItemId: Arr::get($array, self::FIELD_LEAD_REFUND_ITEM_ID),
            invoiceId       : Arr::get($array, self::FIELD_INVOICE_ID),
            value           : Arr::get($array, self::FIELD_VALUE),
        );
    }

    /**
     * @return int
     */
    public function getCompanyId(): int
    {
        return $this->companyId;
    }
}
