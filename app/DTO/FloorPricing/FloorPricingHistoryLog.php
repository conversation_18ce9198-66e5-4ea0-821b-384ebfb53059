<?php

namespace App\DTO\FloorPricing;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class FloorPricingHistoryLog implements DTOContract
{
    const string FIELD_CAUSER_ID   = 'causer_id';
    const string FIELD_CAUSER_NAME = 'causer_name';
    const string FIELD_COUNTY      = 'county';
    const string FIELD_COUNTY_KEY  = 'county_key';
    const string FIELD_DATE        = 'date';
    const string FIELD_PRICE_FROM  = 'price_from';
    const string FIELD_PRICE_TO    = 'price_to';
    const string FIELD_SALE_TYPE   = 'sale_type';
    const string FIELD_STATE       = 'state';
    const string FIELD_STATE_ABBR  = 'state_abbr';
    const string FIELD_STATE_KEY   = 'state_key';

    public function __construct(
        protected ?string $causerId = null,
        protected ?string $causerName = null,
        protected ?string $county = null,
        protected ?string $countyKey = null,
        protected ?string $date = null,
        protected ?string $priceFrom = null,
        protected ?string $priceTo = null,
        protected ?string $saleType = null,
        protected ?string $state = null,
        protected ?string $stateAbbr = null,
        protected ?string $stateKey = null,
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_CAUSER_ID   => $this->causerId,
            self::FIELD_CAUSER_NAME => $this->causerName,
            self::FIELD_COUNTY      => $this->county,
            self::FIELD_COUNTY_KEY  => $this->countyKey,
            self::FIELD_DATE        => $this->date,
            self::FIELD_PRICE_FROM  => $this->priceFrom,
            self::FIELD_PRICE_TO    => $this->priceTo,
            self::FIELD_SALE_TYPE   => $this->saleType,
            self::FIELD_STATE       => $this->state,
            self::FIELD_STATE_ABBR  => $this->stateAbbr,
            self::FIELD_STATE_KEY   => $this->stateKey,
        ];
    }

    /**
     * @return string|null
     */
    public function getCauserId(): ?string
    {
        return $this->causerId;
    }

    /**
     * @return string|null
     */
    public function getCauserName(): ?string
    {
        return $this->causerName;
    }

    /**
     * @return string|null
     */
    public function getCounty(): ?string
    {
        return $this->county;
    }

    /**
     * @return string|null
     */
    public function getCountyKey(): ?string
    {
        return $this->countyKey;
    }

    /**
     * @return string|null
     */
    public function getDate(): ?string
    {
        return $this->date;
    }

    /**
     * @return string|null
     */
    public function getPriceFrom(): ?string
    {
        return $this->priceFrom;
    }

    /**
     * @return string|null
     */
    public function getPriceTo(): ?string
    {
        return $this->priceTo;
    }

    /**
     * @return string|null
     */
    public function getSaleType(): ?string
    {
        return $this->saleType;
    }

    /**
     * @return string|null
     */
    public function getState(): ?string
    {
        return $this->state;
    }

    /**
     * @return string|null
     */
    public function getStateAbbr(): ?string
    {
        return $this->stateAbbr;
    }

    /**
     * @return string|null
     */
    public function getStateKey(): ?string
    {
        return $this->stateKey;
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            causerId  : Arr::get($array, self::FIELD_CAUSER_ID),
            causerName: Arr::get($array, self::FIELD_CAUSER_NAME),
            county    : Arr::get($array, self::FIELD_COUNTY),
            countyKey : Arr::get($array, self::FIELD_COUNTY_KEY),
            date      : Arr::get($array, self::FIELD_DATE),
            priceFrom : Arr::get($array, self::FIELD_PRICE_FROM),
            priceTo   : Arr::get($array, self::FIELD_PRICE_TO),
            saleType  : Arr::get($array, self::FIELD_SALE_TYPE),
            state     : Arr::get($array, self::FIELD_STATE),
            stateAbbr : Arr::get($array, self::FIELD_STATE_ABBR),
            stateKey  : Arr::get($array, self::FIELD_STATE_KEY),
        );
    }
}
