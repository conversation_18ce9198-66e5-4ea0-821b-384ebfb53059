<?php

namespace App\DTO\MarketingCampaign;

use App\Models\MarketingCampaign;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class InternalEmailCampaignMetrics extends CampaignMetrics
{
    const string SENT_COUNT      = 'sent_count';
    const string DELIVERED_COUNT = 'delivered_count';
    const string FAILED_COUNT    = 'failed_count';
    const string OPENED_COUNT    = 'opened_count';
    const string CLICKED_COUNT   = 'clicked_count';

    public function __construct(
        protected ?int $sentCount = null,
        protected ?int $deliveredCount = null,
        protected ?int $failedCount = null,
        protected ?int $openedCount = null,
        protected ?int $clickedCount = null,
        ?int           $revalidates = null,
        ?int           $targets = null,
        ?int           $visits = null,
        ?Carbon        $lastRevalidatedAt = null,
        ?Carbon        $lastUpdatedAt = null,
    )
    {
        parent::__construct(
            revalidates: $revalidates,
            targets: $targets,
            visits: $visits,
            lastRevalidatedAt: $lastRevalidatedAt,
            lastUpdatedAt: $lastUpdatedAt,
        );
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::SENT_COUNT      => $this->sentCount,
            self::DELIVERED_COUNT => $this->deliveredCount,
            self::FAILED_COUNT    => $this->failedCount,
            self::OPENED_COUNT    => $this->openedCount,
            self::CLICKED_COUNT   => $this->clickedCount,
            ...parent::toArray()
        ];
    }

    /**
     * @param array $array
     * @return InternalEmailCampaignMetrics
     */
    public static function fromArray(array $array): InternalEmailCampaignMetrics
    {
        return new self(
            sentCount: Arr::get($array, self::SENT_COUNT),
            deliveredCount: Arr::get($array, self::DELIVERED_COUNT),
            failedCount: Arr::get($array, self::FAILED_COUNT),
            openedCount: Arr::get($array, self::OPENED_COUNT),
            clickedCount: Arr::get($array, self::CLICKED_COUNT),
            revalidates: Arr::get($array, self::REVALIDATES),
            targets: Arr::get($array, self::TARGETS),
            visits: Arr::get($array, self::VISITS),
            lastRevalidatedAt: Arr::get($array, self::LAST_REVALIDATED_AT) ? Carbon::parse($array[self::LAST_REVALIDATED_AT]) : null,
            lastUpdatedAt: Arr::get($array, self::LAST_UPDATED_AT) ? Carbon::parse($array[self::LAST_UPDATED_AT]) : null,
        );
    }

    public static function fromMarketingCampaign(MarketingCampaign $marketingCampaign): ?InternalEmailCampaignMetrics
    {
        $metrics = $marketingCampaign->{MarketingCampaign::FIELD_METRICS};

        if (empty($metrics)) {
            return new self();
        }

        return self::fromArray($metrics);
    }

    /**
     * @return int
     */
    public function getSentCount(): int
    {
        return $this->sentCount ?? 0;
    }

    public function setSentCount(int $sentCont): void
    {
        $this->sentCount = $sentCont;
    }

    public function getDeliveredCount(): ?int
    {
        return $this->deliveredCount;
    }

    public function getFailedCount(): ?int
    {
        return $this->failedCount;
    }

    public function getOpenedCount(): ?int
    {
        return $this->openedCount;
    }

    public function getClickedCount(): ?int
    {
        return $this->clickedCount;
    }

    public function incrementSentCount(int $incremental): void
    {
        $this->setSentCount($this->getSentCount() + $incremental);
    }
}
