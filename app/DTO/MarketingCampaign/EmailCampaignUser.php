<?php

namespace App\DTO\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use Illuminate\Contracts\Support\Arrayable;

class EmailCampaignUser implements Arrayable
{
    const string FIELD_FIRST_NAME                     = 'first_name';
    const string FIELD_LAST_NAME                      = 'last_name';
    const string FIELD_EMAIL                          = 'email';
    const string FIELD_CONSUMER_REFERENCE             = 'consumer_reference';
    const string FIELD_MARKETING_CAMPAIGN_REFERENCE   = 'marketing_campaign_reference';
    const string FIELD_MARKETING_CONSUMER_STATUS      = 'marketing_consumer_status';
    const string FIELD_MARKETING_CAMPAIGN_CONSUMER_ID = 'marketing_campaign_consumer_id';

    public function __construct(
        protected string                           $firstName,
        protected string                           $lastName,
        protected string                           $email,
        protected ?string                          $consumerReference = null,
        protected ?string                          $marketingCampaignReference = null,
        protected ?MarketingCampaignConsumerStatus $status = null,
        protected ?int                             $marketingCampaignConsumerId = null,
        protected ?string                          $externalReference = null,
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_FIRST_NAME                     => $this->firstName,
            self::FIELD_LAST_NAME                      => $this->lastName,
            self::FIELD_EMAIL                          => $this->email,
            self::FIELD_CONSUMER_REFERENCE             => $this->consumerReference,
            self::FIELD_MARKETING_CAMPAIGN_REFERENCE   => $this->marketingCampaignReference,
            self::FIELD_MARKETING_CONSUMER_STATUS      => $this->status->value,
            self::FIELD_MARKETING_CAMPAIGN_CONSUMER_ID => $this->marketingCampaignConsumerId,
        ];
    }

    public function getFirstName(): string
    {
        return $this->firstName;
    }

    public function getLastName(): string
    {
        return $this->lastName;
    }

    public function getEmail(): string
    {
        return $this->email;
    }

    public function getConsumerReference(): ?string
    {
        return $this->consumerReference;
    }

    public function getMarketingCampaignReference(): ?string
    {
        return $this->marketingCampaignReference;
    }

    /**
     * @return MarketingCampaignConsumerStatus|null
     */
    public function getMarketingConsumerStatus(): ?MarketingCampaignConsumerStatus
    {
        return $this->status;
    }

    public function getMarketingCampaignConsumerId(): ?int
    {
        return $this->marketingCampaignConsumerId;
    }

    /**
     * @param MarketingCampaignConsumerStatus|null $status
     */
    public function setStatus(?MarketingCampaignConsumerStatus $status): void
    {
        $this->status = $status;
    }

    public function getExternalReference(): ?string
    {
        return $this->externalReference;
    }
}
