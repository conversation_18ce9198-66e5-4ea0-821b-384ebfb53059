<?php

namespace App\DTO\MarketingCampaign;

use App\Models\MarketingCampaign;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class EmailCampaignMetrics extends CampaignMetrics
{
    const string OPENS         = 'opens';
    const string UNIQUE_OPENS  = 'unique_opens';
    const string OPEN_RATE     = 'open_rate';
    const string CLICKS        = 'clicks';
    const string UNIQUE_CLICKS = 'unique_clicks';
    const string CLICK_RATE    = 'click_rate';

    public function __construct(
        protected ?int   $opens = null,
        protected ?int   $uniqueOpens = null,
        protected ?float $openRate = null,
        protected ?int   $clicks = null,
        protected ?int   $uniqueClicks = null,
        protected ?float $clickRate = null,
        ?int             $revalidates = null,
        ?int             $targets = null,
        ?int             $visits = null,
        ?Carbon          $lastRevalidatedAt = null,
        ?Carbon          $lastUpdatedAt = null,
    )
    {
        parent::__construct(
            revalidates      : $revalidates,
            targets          : $targets,
            visits           : $visits,
            lastRevalidatedAt: $lastRevalidatedAt,
            lastUpdatedAt    : $lastUpdatedAt,
        );
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::OPENS         => $this->opens,
            self::UNIQUE_OPENS  => $this->uniqueOpens,
            self::OPEN_RATE     => $this->openRate,
            self::CLICKS        => $this->clicks,
            self::UNIQUE_CLICKS => $this->uniqueClicks,
            self::CLICK_RATE    => $this->clickRate,
            ...parent::toArray()
        ];
    }

    /**
     * @param array $array
     * @return EmailCampaignMetrics
     */
    public static function fromArray(array $array): EmailCampaignMetrics
    {
        return new self(
            opens            : Arr::get($array, self::OPENS),
            uniqueOpens      : Arr::get($array, self::UNIQUE_OPENS),
            openRate         : Arr::get($array, self::OPEN_RATE),
            clicks           : Arr::get($array, self::CLICKS),
            uniqueClicks     : Arr::get($array, self::UNIQUE_CLICKS),
            clickRate        : Arr::get($array, self::CLICK_RATE),
            revalidates      : Arr::get($array, self::REVALIDATES),
            targets          : Arr::get($array, self::TARGETS),
            visits           : Arr::get($array, self::VISITS),
            lastRevalidatedAt: Arr::get($array, self::LAST_REVALIDATED_AT) ? Carbon::parse($array[self::LAST_REVALIDATED_AT]) : null,
            lastUpdatedAt    : Arr::get($array, self::LAST_UPDATED_AT) ? Carbon::parse($array[self::LAST_UPDATED_AT]) : null,
        );
    }

    public static function fromMarketingCampaign(MarketingCampaign $marketingCampaign): ?EmailCampaignMetrics
    {
        $metrics = $marketingCampaign->{MarketingCampaign::FIELD_METRICS};

        if (empty($metrics)) {
            return new self();
        }

        return self::fromArray($metrics);
    }

    public function getOpens(): ?int
    {
        return $this->opens;
    }

    public function setOpens(?int $opens): void
    {
        $this->opens = $opens;
    }

    public function getUniqueOpens(): ?int
    {
        return $this->uniqueOpens;
    }

    public function setUniqueOpens(?int $uniqueOpens): void
    {
        $this->uniqueOpens = $uniqueOpens;
    }

    public function getOpenRate(): ?float
    {
        return $this->openRate;
    }

    public function setOpenRate(?float $openRate): void
    {
        $this->openRate = $openRate;
    }

    public function getClicks(): ?int
    {
        return $this->clicks;
    }

    public function setClicks(?int $clicks): void
    {
        $this->clicks = $clicks;
    }

    public function getUniqueClicks(): ?int
    {
        return $this->uniqueClicks;
    }

    public function setUniqueClicks(?int $uniqueClicks): void
    {
        $this->uniqueClicks = $uniqueClicks;
    }

    public function getClickRate(): ?float
    {
        return $this->clickRate;
    }

    public function setClickRate(?float $clickRate): void
    {
        $this->clickRate = $clickRate;
    }
}
