<?php

namespace App\DTO\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use Illuminate\Contracts\Support\Arrayable;

class AddUserToEmailCampaignResponse implements Arrayable
{
    const string FIELD_EXTERNAL_REFERENCE                    = 'external_reference';
    const string FIELD_CONSUMER_REFERENCE                    = 'consumer_reference';
    const string FIELD_MARKETING_CAMPAIGN_CONSUMER_REFERENCE = 'marketing_campaign_consumer_reference';
    const string FIELD_STATUS                                = 'status';

    public function __construct(
        protected string                          $consumerReference,
        protected string                          $marketingCampaignConsumerReference,
        protected MarketingCampaignConsumerStatus $status,
        protected ?string                         $externalReference = null,
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_EXTERNAL_REFERENCE                    => $this->consumerReference,
            self::FIELD_CONSUMER_REFERENCE                    => $this->externalReference,
            self::FIELD_MARKETING_CAMPAIGN_CONSUMER_REFERENCE => $this->marketingCampaignConsumerReference,
            self::FIELD_STATUS                                => $this->status->value,
        ];
    }

    public function getExternalReference(): ?string
    {
        return $this->externalReference;
    }

    public function getConsumerReference(): string
    {
        return $this->consumerReference;
    }

    public function getMarketingCampaignConsumerReference(): string
    {
        return $this->marketingCampaignConsumerReference;
    }

    /**
     * @return MarketingCampaignConsumerStatus
     */
    public function getStatus(): MarketingCampaignConsumerStatus
    {
        return $this->status;
    }
}
