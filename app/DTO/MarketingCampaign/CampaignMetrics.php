<?php

namespace App\DTO\MarketingCampaign;

use App\DTO\DTOContract;
use App\Models\MarketingCampaign;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class CampaignMetrics implements DTOContract
{
    const string REVALIDATES         = 'revalidates';
    const string TARGETS             = 'targets';
    const string VISITS              = 'visits';
    const string LAST_REVALIDATED_AT = 'last_revalidated_at';
    const string LAST_UPDATED_AT     = 'last_updated_at';

    /**
     * @param int|null $revalidates
     * @param int|null $targets
     * @param int|null $visits
     * @param Carbon|null $lastRevalidatedAt
     * @param Carbon|null $lastUpdatedAt
     */
    public function __construct(
        protected ?int    $revalidates = null,
        protected ?int    $targets = null,
        protected ?int    $visits = null,
        protected ?Carbon $lastRevalidatedAt = null,
        protected ?Carbon $lastUpdatedAt = null,
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::REVALIDATES         => $this->revalidates,
            self::TARGETS             => $this->targets,
            self::VISITS              => $this->visits,
            self::LAST_REVALIDATED_AT => $this->lastRevalidatedAt?->toIso8601String(),
            self::LAST_UPDATED_AT     => $this->lastUpdatedAt?->toIso8601String(),
        ];
    }

    /**
     * @param array $array
     * @return CampaignMetrics
     */
    public static function fromArray(array $array): CampaignMetrics
    {
        return new self(
            revalidates      : Arr::get($array, self::REVALIDATES),
            targets          : Arr::get($array, self::TARGETS),
            visits           : Arr::get($array, self::VISITS),
            lastRevalidatedAt: Carbon::parse(Arr::get($array, self::LAST_REVALIDATED_AT)),
            lastUpdatedAt    : Carbon::parse(Arr::get($array, self::LAST_UPDATED_AT)),
        );
    }

    /**
     * @param MarketingCampaign $marketingCampaign
     * @return CampaignMetrics|null
     */
    public static function fromMarketingCampaign(MarketingCampaign $marketingCampaign): ?CampaignMetrics
    {
        $metrics = $marketingCampaign->{MarketingCampaign::FIELD_METRICS};

        if (empty($metrics)) {
            return new self();
        }

        return new self(
            revalidates      : Arr::get($metrics, self::REVALIDATES),
            targets          : Arr::get($metrics, self::TARGETS),
            visits           : Arr::get($metrics, self::VISITS),
            lastRevalidatedAt: Arr::get($metrics, self::LAST_REVALIDATED_AT) ? Carbon::parse($metrics[self::LAST_REVALIDATED_AT]) : null,
            lastUpdatedAt    : Arr::get($metrics, self::LAST_UPDATED_AT) ? Carbon::parse($metrics[self::LAST_UPDATED_AT]) : null,
        );
    }

    /**
     * @return int|null
     */
    public function getRevalidates(): ?int
    {
        return $this->revalidates;
    }

    /**
     * @param int|null $revalidates
     * @return void
     */
    public function setRevalidates(?int $revalidates): void
    {
        $this->revalidates = $revalidates;
    }

    /**
     * @return void
     */
    public function incrementRevalidates(): void
    {
        $this->revalidates = empty($this->revalidates) ? 1 : $this->revalidates++;
    }

    /**
     * @return void
     */
    public function incrementVisits(): void
    {
        $this->visits = empty($this->visits) ? 1 : $this->visits++;
    }

    /**
     * @return Carbon|null
     */
    public function getLastRevalidatedAt(): ?Carbon
    {
        return $this->lastRevalidatedAt;
    }

    /**
     * @param Carbon|null $lastRevalidatedAt
     * @return void
     */
    public function setLastRevalidatedAt(?Carbon $lastRevalidatedAt): void
    {
        $this->lastRevalidatedAt = $lastRevalidatedAt;
    }

    /**
     * @return Carbon|null
     */
    public function getLastUpdatedAt(): ?Carbon
    {
        return $this->lastUpdatedAt;
    }

    /**
     * @param Carbon|null $lastUpdatedAt
     * @return void
     */
    public function setLastUpdatedAt(?Carbon $lastUpdatedAt): void
    {
        $this->lastUpdatedAt = $lastUpdatedAt;
    }

    /**
     * @return int|null
     */
    public function getTargets(): ?int
    {
        return $this->targets;
    }

    /**
     * @param int|null $targets
     */
    public function setTargets(?int $targets): void
    {
        $this->targets = $targets;
    }

    public function incrementTargets(int $incremental): void
    {
        $this->setTargets($this->getTargets() + $incremental);
    }

    /**
     * @return int|null
     */
    public function getVisits(): ?int
    {
        return $this->visits;
    }

    /**
     * @param int|null $visits
     */
    public function setVisits(?int $visits): void
    {
        $this->visits = $visits;
    }
}
