<?php

namespace App\DTO\MarketingCampaign;

use App\DTO\SMS;
use Illuminate\Support\Arr;

class MarketingSMS extends SMS
{
    const string MARKETING_CONSUMER_ID = 'marketing_consumer_id';

    /**
     * @param string $toPhone
     * @param string $from
     * @param string $message
     * @param int $marketingConsumerId
     * @param int|null $fromPhoneId
     * @param string|null $fromType
     * @param array|null $meta
     */
    public function __construct(
        string        $toPhone,
        string        $from,
        string        $message,
        protected int $marketingConsumerId,
        ?int          $fromPhoneId = null,
        ?string       $fromType = self::FROM_TYPE_PHONE,
        ?array        $meta = []
    )
    {
        parent::__construct(
            $toPhone,
            $from,
            $message,
            $fromPhoneId,
            $fromType,
            $meta
        );
    }

    public static function fromArray(array $array): SMS
    {
        return new self(
            toPhone: Arr::get($array, self::TO_PHONE),
            from: Arr::get($array, self::FROM),
            message: Arr::get($array, self::MESSAGE),
            marketingConsumerId: Arr::get($array, self::MARKETING_CONSUMER_ID),
            fromPhoneId: Arr::get($array, self::FROM_PHONE_ID),
            fromType: Arr::get($array, self::FROM_TYPE),
            meta: Arr::get($array, self::META),
        );
    }

    public function toArray(): array
    {
        $parent = parent::toArray();

        return array_merge($parent, [
            self::MARKETING_CONSUMER_ID => $this->getMarketingConsumerId(),
        ]);
    }

    public function getMarketingConsumerId(): int
    {
        return $this->marketingConsumerId;
    }
}
