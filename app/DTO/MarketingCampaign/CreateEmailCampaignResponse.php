<?php

namespace App\DTO\MarketingCampaign;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;

class CreateEmailCampaignResponse implements Arrayable
{

    const string FIELD_EXTERNAL_REFERENCE = 'external_reference';
    const string FIELD_EXTERNAL_CODE      = 'external_code';
    const string FIELD_ADDED_USERS        = 'added_users';

    /**
     * @param string $externalReference
     * @param string $externalCode
     * @param Collection<AddUserToEmailCampaignResponse> $addedUsers
     */
    public function __construct(
        protected string $externalReference,
        protected string    $externalCode,
        protected Collection $addedUsers,
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_EXTERNAL_REFERENCE => $this->externalReference,
            self::FIELD_EXTERNAL_CODE      => $this->externalCode,
            self::FIELD_ADDED_USERS        => $this->addedUsers->toArray()
        ];
    }

    public function getExternalReference(): string
    {
        return $this->externalReference;
    }

    public function getExternalCode(): string
    {
        return $this->externalCode;
    }

    /**
     * @return Collection<AddUserToEmailCampaignResponse>
     */
    public function getAddedUsers(): Collection
    {
        return $this->addedUsers;
    }
}
