<?php

namespace App\DTO;

class SimilarWebPPCPayloadResponse implements BasePPCPayloadResponse
{
    const string FIELD_GRANULARITY      = 'granularity';
    const string FIELD_MAIN_DOMAIN_ONLY = 'main_domain_only';
    const string FIELD_FORMAT           = 'format';
    const string FIELD_START_DATE       = 'start_date';
    const string FIELD_END_DATE         = 'end_date';
    const string FIELD_COUNTRY          = 'country';
    const string FIELD_STATUS           = 'status';
    const string FIELD_LAST_UPDATED     = 'last_updated';

    protected string $granularity;
    protected bool $main_domain_only;
    protected string $format;
    protected string $start_date;
    protected string $end_date;
    protected string $country;
    protected string $status;
    protected string $last_updated;

    /**
     * @param string $granularity
     * @param bool $main_domain_only
     * @param string $format
     * @param string $start_date
     * @param string $end_date
     * @param string $country
     * @param string $status
     * @param string $last_updated
     */
    public function __construct(
        string $granularity     = '',
        bool $main_domain_only  = false,
        string $format          = '',
        string $start_date      = '',
        string $end_date        = '',
        string $country         = '',
        string $status          = '',
        string $last_updated    = '',
    )
    {
         $this->granularity         = $granularity;
         $this->main_domain_only    = $main_domain_only;
         $this->format              = $format;
         $this->start_date          = $start_date;
         $this->end_date            = $end_date;
         $this->country             = $country;
         $this->status              = $status;
         $this->last_updated        = $last_updated;

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_GRANULARITY       => $this->granularity,
            self::FIELD_MAIN_DOMAIN_ONLY  => $this->main_domain_only,
            self::FIELD_FORMAT            => $this->format,
            self::FIELD_START_DATE        => $this->start_date,
            self::FIELD_END_DATE          => $this->end_date,
            self::FIELD_COUNTRY           => $this->country,
            self::FIELD_STATUS            => $this->status,
            self::FIELD_LAST_UPDATED      => $this->last_updated
        ];
    }
}
