<?php

namespace App\DTO\EmailAddress;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class EmailValidationResponseDTO implements DTOContract
{

    const string SUCCESS                  = "success";
    const string VALID                    = "valid";
    const string TIMED_OUT                = "timed_out";
    const string DISPOSABLE               = "disposable";
    const string FIRST_NAME               = "first_name";
    const string DELIVERABILITY           = "deliverability";
    const string SMTP_SCORE               = "smtp_score";
    const string OVERALL_SCORE            = "overall_score";
    const string CATCH_ALL                = "catch_all";
    const string GENERIC                  = "generic";
    const string COMMON                   = "common";
    const string DNS_VALID                = "dns_valid";
    const string HONEYPOT                 = "honeypot";
    const string FREQUENT_COMPLAINER      = "frequent_complainer";
    const string SUSPECT                  = "suspect";
    const string RECENT_ABUSE             = "recent_abuse";
    const string FRAUD_SCORE              = "fraud_score";
    const string LEAKED                   = "leaked";
    const string SUGGESTED_DOMAIN         = "suggested_domain";
    const string DOMAIN_VELOCITY          = "domain_velocity";
    const string DOMAIN_TRUST             = "domain_trust";
    const string USER_ACTIVITY            = "user_activity";
    const string SPAM_TRAP_SCORE          = "spam_trap_score";
    const string RISKY_TLD                = "risky_tld";
    const string SPF_RECORD               = "spf_record";
    const string DMARC_RECORD             = "dmarc_record";
    const string SANITIZED_EMAIL          = "sanitized_email";
    const string MX_RECORDS               = "mx_records";
    const string REQUEST_ID               = "request_id";
    const string A_RECORDS                = "a_records";
    const string ASSOCIATED_NAMES         = "associated_names";
    const string FIRST_SEEN               = "first_seen";
    const string ASSOCIATED_PHONE_NUMBERS = "associated_phone_numbers";
    const string DOMAIN_AGE               = "domain_age";
    const string MESSAGE                  = "message";
    const string ERRORS                   = "errors";

    public function __construct(
        protected ?bool   $success = null,
        protected ?bool   $valid = null,
        protected ?bool   $timed_out = null,
        protected ?bool   $disposable = null,
        protected ?string $first_name = null,
        protected ?string $deliverability = null,
        protected ?int    $smtp_score = null,
        protected ?int    $overall_score = null,
        protected ?bool   $catch_all = null,
        protected ?bool   $generic = null,
        protected ?bool   $common = null,
        protected ?bool   $dns_valid = null,
        protected ?bool   $honeypot = null,
        protected ?bool   $frequent_complainer = null,
        protected ?bool   $suspect = null,
        protected ?bool   $recent_abuse = null,
        protected ?float  $fraud_score = null,
        protected ?bool   $leaked = null,
        protected ?string $suggested_domain = null,
        protected ?string $domain_velocity = null,
        protected ?string $domain_trust = null,
        protected ?array  $domain_age = null, //object
        protected ?string $user_activity = null,
        protected ?string $spam_trap_score = null,
        protected ?bool   $risky_tld = null,
        protected ?bool   $spf_record = null,
        protected ?bool   $dmarc_record = null,
        protected ?string $sanitized_email = null,
        protected ?array  $mx_records = null,
        protected ?string $request_id = null,
        protected ?array  $a_records = null,
        protected ?array  $associated_names = null, //object
        protected ?array  $associated_phone_numbers = null, //object
        protected ?array  $first_seen = null, //object
        protected ?string $message = null,
        protected ?array  $errors = null,
    )
    {
    }


    public function toArray(): array
    {
        return [
            self::SUCCESS                  => $this->success,
            self::VALID                    => $this->valid,
            self::TIMED_OUT                => $this->timed_out,
            self::DISPOSABLE               => $this->disposable,
            self::FIRST_NAME               => $this->first_name,
            self::DELIVERABILITY           => $this->deliverability,
            self::SMTP_SCORE               => $this->smtp_score,
            self::OVERALL_SCORE            => $this->overall_score,
            self::CATCH_ALL                => $this->catch_all,
            self::GENERIC                  => $this->generic,
            self::COMMON                   => $this->common,
            self::DNS_VALID                => $this->dns_valid,
            self::HONEYPOT                 => $this->honeypot,
            self::FREQUENT_COMPLAINER      => $this->frequent_complainer,
            self::SUSPECT                  => $this->suspect,
            self::RECENT_ABUSE             => $this->recent_abuse,
            self::FRAUD_SCORE              => $this->fraud_score,
            self::LEAKED                   => $this->leaked,
            self::SUGGESTED_DOMAIN         => $this->suggested_domain,
            self::DOMAIN_VELOCITY          => $this->domain_velocity,
            self::DOMAIN_TRUST             => $this->domain_trust,
            self::USER_ACTIVITY            => $this->user_activity,
            self::SPAM_TRAP_SCORE          => $this->spam_trap_score,
            self::RISKY_TLD                => $this->risky_tld,
            self::SPF_RECORD               => $this->spf_record,
            self::DMARC_RECORD             => $this->dmarc_record,
            self::SANITIZED_EMAIL          => $this->sanitized_email,
            self::MX_RECORDS               => $this->mx_records,
            self::REQUEST_ID               => $this->request_id,
            self::A_RECORDS                => $this->a_records,
            self::ASSOCIATED_NAMES         => $this->associated_names,
            self::ASSOCIATED_PHONE_NUMBERS => $this->associated_phone_numbers,
            self::DOMAIN_AGE               => $this->domain_age,
            self::FIRST_SEEN               => $this->first_seen,
            self::MESSAGE                  => $this->message,
            self::ERRORS                   => $this->errors,
        ];
    }

    public static function fromArray(array $array): EmailValidationResponseDTO
    {
        return new self(
            success: Arr::get($array, self::SUCCESS),
            valid: Arr::get($array, self::VALID),
            timed_out: Arr::get($array, self::TIMED_OUT),
            disposable: Arr::get($array, self::DISPOSABLE),
            first_name: Arr::get($array, self::FIRST_NAME),
            deliverability: Arr::get($array, self::DELIVERABILITY),
            smtp_score: Arr::get($array, self::SMTP_SCORE),
            overall_score: Arr::get($array, self::OVERALL_SCORE),
            catch_all: Arr::get($array, self::CATCH_ALL),
            generic: Arr::get($array, self::GENERIC),
            common: Arr::get($array, self::COMMON),
            dns_valid: Arr::get($array, self::DNS_VALID),
            honeypot: Arr::get($array, self::HONEYPOT),
            frequent_complainer: Arr::get($array, self::FREQUENT_COMPLAINER),
            suspect: Arr::get($array, self::SUSPECT),
            recent_abuse: Arr::get($array, self::RECENT_ABUSE),
            fraud_score: Arr::get($array, self::FRAUD_SCORE),
            leaked: Arr::get($array, self::LEAKED),
            suggested_domain: Arr::get($array, self::SUGGESTED_DOMAIN),
            domain_velocity: Arr::get($array, self::DOMAIN_VELOCITY),
            domain_trust: Arr::get($array, self::DOMAIN_TRUST),
            domain_age: Arr::get($array, self::DOMAIN_AGE),
            user_activity: Arr::get($array, self::USER_ACTIVITY),
            spam_trap_score: Arr::get($array, self::SPAM_TRAP_SCORE),
            risky_tld: Arr::get($array, self::RISKY_TLD),
            spf_record: Arr::get($array, self::SPF_RECORD),
            dmarc_record: Arr::get($array, self::DMARC_RECORD),
            sanitized_email: Arr::get($array, self::SANITIZED_EMAIL),
            mx_records: Arr::get($array, self::MX_RECORDS),
            request_id: Arr::get($array, self::REQUEST_ID),
            a_records: Arr::get($array, self::A_RECORDS),
            associated_names: Arr::get($array, self::ASSOCIATED_NAMES),
            associated_phone_numbers: Arr::get($array, self::ASSOCIATED_PHONE_NUMBERS),
            first_seen: Arr::get($array, self::FIRST_SEEN),
            message: Arr::get($array, self::MESSAGE),
            errors: Arr::get($array, self::ERRORS),
        );
    }

    public function getSuccess(): ?bool
    {
        return $this->success;
    }

    public function getValid(): ?bool
    {
        return $this->valid;
    }

    public function getTimedOut(): ?bool
    {
        return $this->timed_out;
    }

    public function getDisposable(): ?bool
    {
        return $this->disposable;
    }

    public function getFirstName(): ?string
    {
        return $this->first_name;
    }

    public function getDeliverability(): ?string
    {
        return $this->deliverability;
    }

    public function getSmtpScore(): ?int
    {
        return $this->smtp_score;
    }

    public function getOverallScore(): ?int
    {
        return $this->overall_score;
    }

    public function getCatchAll(): ?bool
    {
        return $this->catch_all;
    }

    public function getGeneric(): ?bool
    {
        return $this->generic;
    }

    public function getCommon(): ?bool
    {
        return $this->common;
    }

    public function getDnsValid(): ?bool
    {
        return $this->dns_valid;
    }

    public function getHoneypot(): ?bool
    {
        return $this->honeypot;
    }

    public function getFrequentComplainer(): ?bool
    {
        return $this->frequent_complainer;
    }

    public function getSuspect(): ?bool
    {
        return $this->suspect;
    }

    public function getRecentAbuse(): ?bool
    {
        return $this->recent_abuse;
    }

    public function getFraudScore(): ?float
    {
        return $this->fraud_score;
    }

    public function getLeaked(): ?bool
    {
        return $this->leaked;
    }

    public function getSuggestedDomain(): ?string
    {
        return $this->suggested_domain;
    }

    public function getDomainVelocity(): ?string
    {
        return $this->domain_velocity;
    }

    public function getDomainTrust(): ?string
    {
        return $this->domain_trust;
    }

    public function getDomainAge(): ?array
    {
        return $this->domain_age;
    }

    public function getUserActivity(): ?string
    {
        return $this->user_activity;
    }

    public function getSpamTrapScore(): ?string
    {
        return $this->spam_trap_score;
    }

    public function getRiskyTld(): ?bool
    {
        return $this->risky_tld;
    }

    public function getSpfRecord(): ?bool
    {
        return $this->spf_record;
    }

    public function getDmarcRecord(): ?bool
    {
        return $this->dmarc_record;
    }

    public function getSanitizedEmail(): ?string
    {
        return $this->sanitized_email;
    }

    public function getMxRecords(): ?array
    {
        return $this->mx_records;
    }

    public function getRequestId(): ?string
    {
        return $this->request_id;
    }

    public function getARecords(): ?array
    {
        return $this->a_records;
    }

    public function getAssociatedNames(): ?array
    {
        return $this->associated_names;
    }

    public function getAssociatedPhoneNumbers(): ?array
    {
        return $this->associated_phone_numbers;
    }

    public function getFirstSeen(): ?array
    {
        return $this->first_seen;
    }

    public function getMessage(): ?string
    {
        return $this->message;
    }

    public function getErrors(): ?array
    {
        return $this->errors;
    }
}
