<?php

namespace App\DTO;

use Illuminate\Support\Arr;

class SelectOption implements DTOContract
{
    const string FIELD_ID      = 'id';
    const string FIELD_NAME    = 'name';
    const string FIELD_PAYLOAD = 'payload';

    /**
     * @param int $id
     * @param string $name
     * @param array|null $payload
     */
    public function __construct(
        protected int $id,
        protected string $name,
        protected ?array $payload = [],
    )
    {

    }

    /**
     * @return int
     */
    public function getId(): int
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return array|null
     */
    public function getPayload(): ?array
    {
        return $this->payload;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_ID      => $this->id,
            self::FIELD_NAME    => $this->name,
            self::FIELD_PAYLOAD => $this->payload,
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            id     : Arr::get($array, self::FIELD_ID),
            name   : Arr::get($array, self::FIELD_NAME),
            payload: Arr::get($array, self::FIELD_PAYLOAD),
        );
    }
}
