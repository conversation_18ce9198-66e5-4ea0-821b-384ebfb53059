<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class GeometryDTO implements DTOContract
{
    public function __construct(
        public readonly LocationDTO $location,
        public readonly string $location_type,
        public readonly ViewportDTO $viewport,
        public readonly ?BoundsDTO $bounds = null
    )
    {
    }

    public function isExactLocation(): bool
    {
        return $this->location_type === 'ROOFTOP';
    }

    public function isApproximateLocation(): bool
    {
        return in_array($this->location_type, [
            'RANGE_INTERPOLATED',
            'GEOMETRIC_CENTER',
            'APPROXIMATE'
        ]);
    }

    public function toArray(): array
    {
        return [
            'location'      => $this->location->toArray(),
            'location_type' => $this->location_type,
            'viewport'      => $this->viewport->toArray(),
            'bounds'        => $this->bounds?->toArray(),
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            location     : LocationDTO::fromArray(Arr::get($array, 'location', [])),
            location_type: $array['location_type'],
            viewport     : ViewportDTO::fromArray(Arr::get($array, 'viewport', [])),
            bounds       : isset($array['bounds']) ? BoundsDTO::fromArray($array['bounds']) : null
        );
    }
}
