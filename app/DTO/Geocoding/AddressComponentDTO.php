<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

/**
 * Address component DTO
 */
class AddressComponentDTO implements DTOContract
{
    public function __construct(
        public readonly string $long_name,
        public readonly string $short_name,
        public readonly array $types
    )
    {
    }

    public function hasType(string $type): bool
    {
        return in_array($type, $this->types);
    }

    public function toArray(): array
    {
        return [
            'long_name'  => $this->long_name,
            'short_name' => $this->short_name,
            'types'      => $this->types,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            long_name : Arr::get($array, 'long_name'),
            short_name: Arr::get($array, 'short_name'),
            types     : Arr::get($array, 'types')
        );
    }
}
