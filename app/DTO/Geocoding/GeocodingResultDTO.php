<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class GeocodingResultDTO implements DTOContract
{
    public function __construct(
        public readonly array $address_components,
        public readonly string $formatted_address,
        public readonly GeometryDTO $geometry,
        public readonly string $place_id,
        public readonly array $types,
        public readonly ?PlusCodeDTO $plus_code = null,
        public readonly ?bool $partial_match = null
    )
    {
    }

    /**
     * @return AddressComponentDTO[]
     */
    public function getAddressComponents(): array
    {
        return array_map(
            fn(array $component) => AddressComponentDTO::fromArray($component),
            $this->address_components
        );
    }

    public function getAddressComponentByType(string $type): ?AddressComponentDTO
    {
        foreach ($this->getAddressComponents() as $component) {
            if (in_array($type, $component->types)) {
                return $component;
            }
        }
        return null;
    }

    public function getStreetNumber(): ?string
    {
        return $this->getAddressComponentByType('street_number')?->long_name;
    }

    public function getStreetName(): ?string
    {
        return $this->getAddressComponentByType('route')?->long_name;
    }

    public function getCountyLongName(): ?string
    {
        return $this->getAddressComponentByType('administrative_area_level_2')?->long_name;
    }

    public function getCountyShotName(): ?string
    {
        return $this->getAddressComponentByType('administrative_area_level_2')?->short_name;
    }

    public function getCity(): ?string
    {
        return $this->getAddressComponentByType('locality')?->long_name
            ?? $this->getAddressComponentByType('sublocality')?->long_name;
    }

    public function getState(): ?string
    {
        return $this->getAddressComponentByType('administrative_area_level_1')?->long_name;
    }

    public function getStateShort(): ?string
    {
        return $this->getAddressComponentByType('administrative_area_level_1')?->short_name;
    }

    public function getCountry(): ?string
    {
        return $this->getAddressComponentByType('country')?->long_name;
    }

    public function getCountryCode(): ?string
    {
        return $this->getAddressComponentByType('country')?->short_name;
    }

    public function getPostalCode(): ?string
    {
        return $this->getAddressComponentByType('postal_code')?->long_name;
    }

    public function getLatitude(): float
    {
        return $this->geometry->location->lat;
    }

    public function getLongitude(): float
    {
        return $this->geometry->location->lng;
    }

    public function toArray(): array
    {
        return [
            'address_components' => array_map(
                fn(array $component) => AddressComponentDTO::fromArray($component)->toArray(),
                $this->address_components
            ),
            'formatted_address'  => $this->formatted_address,
            'geometry'           => $this->geometry->toArray(),
            'place_id'           => $this->place_id,
            'types'              => $this->types,
            'plus_code'          => $this->plus_code?->toArray(),
            'partial_match'      => $this->partial_match,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            address_components: Arr::get($array, 'address_components', []),
            formatted_address : Arr::get($array, 'formatted_address'),
            geometry          : GeometryDTO::fromArray(Arr::get($array, 'geometry')),
            place_id          : Arr::get($array, 'place_id'),
            types             : Arr::get($array, 'types'),
            plus_code         : Arr::get($array, 'plus_code') ? PlusCodeDTO::fromArray(Arr::get($array, 'plus_code')) : null,
            partial_match     : Arr::get($array, 'partial_match') ?? null
        );
    }
}
