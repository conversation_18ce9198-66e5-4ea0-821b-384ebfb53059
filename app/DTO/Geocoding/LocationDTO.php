<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

/**
 * Location DTO (latitude/longitude)
 */
class LocationDTO implements DTOContract
{
    public function __construct(
        public readonly float $lat,
        public readonly float $lng
    )
    {
    }

    public function getLatitude(): float
    {
        return $this->lat;
    }

    public function getLongitude(): float
    {
        return $this->lng;
    }

    public function toArray(): array
    {
        return [
            'lat' => $this->lat,
            'lng' => $this->lng
        ];
    }

    public function toString(): string
    {
        return "{$this->lat},{$this->lng}";
    }

    public static function fromArray(array $array): self
    {
        return new self(
            lat: (float)Arr::get($array, 'lat', 0),
            lng: (float)Arr::get($array, 'lng', 0)
        );
    }
}
