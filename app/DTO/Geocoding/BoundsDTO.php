<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

/**
 * Bounds DTO
 */
class BoundsDTO implements DTOContract
{
    public function __construct(
        public readonly LocationDTO $northeast,
        public readonly LocationDTO $southwest
    )
    {
    }

    public function contains(LocationDTO $location): bool
    {
        return $location->lat <= $this->northeast->lat
            && $location->lat >= $this->southwest->lat
            && $location->lng <= $this->northeast->lng
            && $location->lng >= $this->southwest->lng;
    }

    public function toArray(): array
    {
        return [
            'northeast' => $this->northeast->toArray(),
            'southwest' => $this->southwest->toArray(),
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            northeast: LocationDTO::fromArray(Arr::get($array, 'northeast')),
            southwest: LocationDTO::fromArray(Arr::get($array, 'southwest'))
        );
    }
}
