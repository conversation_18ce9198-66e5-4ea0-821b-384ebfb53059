<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

/**
 * Plus Code DTO
 */
class PlusCodeDTO implements DTOContract
{
    public function __construct(
        public readonly string $global_code,
        public readonly ?string $compound_code = null
    )
    {
    }

    public function hasCompoundCode(): bool
    {
        return $this->compound_code !== null;
    }

    public function toArray(): array
    {
        return [
            'global_code'   => $this->global_code,
            'compound_code' => $this->compound_code,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            global_code  : Arr::get($array, 'global_code'),
            compound_code: Arr::get($array, 'compound_code'),
        );
    }
}
