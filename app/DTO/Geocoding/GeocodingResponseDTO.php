<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

/**
 * Main response DTO for Google Geocoding API
 */
class GeocodingResponseDTO implements DTOContract
{
    public function __construct(
        public readonly array $results,
        public readonly string $status,
        public readonly ?string $error_message = null,
        public readonly ?array $info_messages = null,
        public readonly ?string $next_page_token = null,
        public readonly ?array $plus_code = null
    )
    {
    }

    /**
     * @return GeocodingResultDTO[]
     */
    public function getResults(): array
    {
        return array_map(
            fn(array $result) => GeocodingResultDTO::fromArray($result),
            $this->results
        );
    }

    public function isSuccessful(): bool
    {
        return $this->status === 'OK';
    }

    public function hasResults(): bool
    {
        return !empty($this->results);
    }

    public function getFirstResult(): ?GeocodingResultDTO
    {
        $results = $this->getResults();
        return $results[0] ?? null;
    }

    public function toArray(): array
    {
        return [
            'results'         => array_map(
                fn(array $result) => GeocodingResultDTO::fromArray($result)->toArray(),
                $this->results
            ),
            'status'          => $this->status,
            'error_message'   => $this->error_message,
            'info_messages'   => $this->info_messages,
            'next_page_token' => $this->next_page_token,
            'plus_code'       => $this->plus_code,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            results        : Arr::get($array, 'results', []),
            status         : Arr::get($array, 'status'),
            error_message  : Arr::get($array, 'error_message'),
            info_messages  : Arr::get($array, 'info_messages'),
            next_page_token: Arr::get($array, 'next_page_token'),
            plus_code      : Arr::get($array, 'plus_code')
        );
    }
}

