<?php

namespace App\DTO\Geocoding;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

/**
 * Viewport DTO
 */
class ViewportDTO implements DTOContract
{
    public function __construct(
        public readonly LocationDTO $northeast,
        public readonly LocationDTO $southwest
    )
    {
    }

    public function contains(LocationDTO $location): bool
    {
        return $location->lat <= $this->northeast->lat
            && $location->lat >= $this->southwest->lat
            && $location->lng <= $this->northeast->lng
            && $location->lng >= $this->southwest->lng;
    }

    public function getCenter(): LocationDTO
    {
        return new LocationDTO(
            lat: ($this->northeast->lat + $this->southwest->lat) / 2,
            lng: ($this->northeast->lng + $this->southwest->lng) / 2
        );
    }

    public function toArray(): array
    {
        return [
            'northeast' => $this->northeast->toArray(),
            'southwest' => $this->southwest->toArray(),
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            northeast: LocationDTO::fromArray(Arr::get($array, 'northeast', [])),
            southwest: LocationDTO::fromArray(Arr::get($array, 'southwest', []))
        );
    }
}
