<?php

namespace App\DTO\EmailService;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class OutgoingEmailDTO implements DTOContract
{
    const string TO_EMAIL              = 'to_email';
    const string FROM_EMAIL            = 'from_email';
    const string FROM_USERNAME         = 'from_username';
    const string SUBJECT               = 'subject';
    const string BODY                  = 'body';
    const string SHORTCODES            = 'shortcodes';
    const string RELATION_MODEL        = 'relation_model';
    const string RELATION_ID           = 'relation_id';
    const string EMAIL_META            = 'email_meta';
    const string EMAIL_META_MCC_ID_KEY = 'marketing_campaign_consumer_id';
    const string EMAIL_TEMPLATE_ID     = 'email_template_id';

    /**
     * @param string $toEmail
     * @param string $fromEmail
     * @param string $fromUsername
     * @param string $subject
     * @param string $body
     * @param array|null $shortcodes
     * @param string|null $relationModel
     * @param int|null $relationId
     * @param array|null $emailMeta
     * @param int|null $emailTemplateId
     */
    public function __construct(
        protected string  $toEmail,
        protected string  $fromEmail,
        protected string  $fromUsername,
        protected string  $subject,
        protected string  $body,
        protected ?array  $shortcodes = [],
        protected ?string $relationModel = null,
        protected ?int    $relationId = null,
        protected ?array  $emailMeta = null,
        protected ?int    $emailTemplateId = null,
    )
    {
    }

    /**
     * @return string
     */
    public function getToEmail(): string
    {
        return $this->toEmail;
    }

    /**
     * @return string
     */
    public function getFromEmail(): string
    {
        return $this->fromEmail;
    }

    /**
     * @return string
     */
    public function getFromUsername(): string
    {
        return $this->fromUsername;
    }

    /**
     * @return string
     */
    public function getSubject(): string
    {
        return $this->subject;
    }

    /**
     * @return string
     */
    public function getBody(): string
    {
        return $this->body;
    }

    public function getShortcodes(): ?array
    {
        return $this->shortcodes;
    }

    public function getRelationModel(): ?string
    {
        return $this->relationModel;
    }

    public function setRelationModel(?string $relationModel): void
    {
        $this->relationModel = $relationModel;
    }

    public function getRelationId(): ?int
    {
        return $this->relationId;
    }

    public function setRelationId(?int $relationId): void
    {
        $this->relationId = $relationId;
    }

    public function getEmailMeta(): ?array
    {
        return $this->emailMeta;
    }

    public function getEmailTemplateId(): ?int
    {
        return $this->emailTemplateId;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::TO_EMAIL          => $this->toEmail,
            self::FROM_EMAIL        => $this->fromEmail,
            self::FROM_USERNAME     => $this->fromUsername,
            self::SUBJECT           => $this->subject,
            self::BODY              => $this->body,
            self::SHORTCODES        => $this->shortcodes,
            self::RELATION_MODEL    => $this->relationModel,
            self::RELATION_ID       => $this->relationId,
            self::EMAIL_META        => $this->emailMeta,
            self::EMAIL_TEMPLATE_ID => $this->emailTemplateId,
        ];
    }

    /**
     * @param array $array
     * @return OutgoingEmailDTO
     */
    public static function fromArray(array $array): OutgoingEmailDTO
    {
        return new self(
            toEmail: Arr::get($array, self::TO_EMAIL),
            fromEmail: Arr::get($array, self::FROM_EMAIL),
            fromUsername: Arr::get($array, self::FROM_USERNAME),
            subject: Arr::get($array, self::SUBJECT),
            body: Arr::get($array, self::BODY),
            shortcodes: Arr::get($array, self::SHORTCODES),
            relationModel: Arr::get($array, self::RELATION_MODEL),
            relationId: Arr::get($array, self::RELATION_ID),
            emailMeta: Arr::get($array, self::EMAIL_META),
            emailTemplateId: Arr::get($array, self::EMAIL_TEMPLATE_ID),
        );
    }
}
