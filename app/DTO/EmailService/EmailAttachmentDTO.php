<?php

namespace App\DTO\EmailService;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class EmailAttachmentDTO implements DTOContract
{
    const string FIELD_DATA    = 'data';
    const string FIELD_NAME    = 'name';
    const string FIELD_OPTIONS = 'options';

    public function __construct(
        protected mixed $data,
        protected string $name,
        protected array $options = [],
    )
    {
    }

    /**
     * @return mixed
     */
    public function getData(): mixed
    {
        return $this->data;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return array
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_DATA    => $this->data,
            self::FIELD_NAME    => $this->name,
            self::FIELD_OPTIONS => $this->options,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            data   : Arr::get($array, self::FIELD_DATA),
            name   : Arr::get($array, self::FIELD_NAME),
            options: Arr::get($array, self::FIELD_OPTIONS, []),
        );
    }
}
