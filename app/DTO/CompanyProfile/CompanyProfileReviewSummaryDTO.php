<?php

namespace App\DTO\CompanyProfile;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class CompanyProfileReviewSummaryDTO implements DTOContract
{
    const string FIELD_POSITIVES = 'positives';
    const string FIELD_NEGATIVES = 'negatives';
    const string FIELD_OVERVIEW  = 'overview';
    const string FIELD_SOURCE    = 'source';

    public function __construct(
        protected ?string $positives = null,
        protected ?string $negatives = null,
        protected ?string $overview = null,
        protected ?string $source = null,
    )
    {

    }

    public function toArray()
    {
        return [
            self::FIELD_POSITIVES => $this->positives,
            self::FIELD_NEGATIVES => $this->negatives,
            self::FIELD_OVERVIEW  => $this->overview,
        ];
    }

    public static function fromArray(array $array): DTOContract
    {
        return new self(
            positives: Arr::get($array, self::FIELD_POSITIVES),
            negatives: Arr::get($array, self::FIELD_NEGATIVES),
            overview : Arr::get($array, self::FIELD_OVERVIEW),
        );
    }

    /**
     * @return string|null
     */
    public function getPositives(): ?string
    {
        return $this->positives;
    }

    /**
     * @return string|null
     */
    public function getNegatives(): ?string
    {
        return $this->negatives;
    }

    /**
     * @return string|null
     */
    public function getOverview(): ?string
    {
        return $this->overview;
    }

    /**
     * @return string|null
     */
    public function getSource(): ?string
    {
        return $this->source;
    }
}
