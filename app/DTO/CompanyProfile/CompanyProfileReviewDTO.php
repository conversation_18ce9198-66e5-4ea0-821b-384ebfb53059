<?php

namespace App\DTO\CompanyProfile;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class CompanyProfileReviewDTO implements DTOContract
{
    const string TEXT               = 'text';
    const string RATING             = 'rating';
    const string DATE               = 'date';
    const string AUTHOR             = 'author';
    const string HELPFUL            = 'helpful';
    const string ADDITIONAL_RATINGS = 'additional_ratings';
    const string SOURCE             = 'source';

    public function __construct(
        protected string $text,
        protected float  $rating,
        protected Carbon $date,
        protected string $author,
        protected ?int   $helpful = null,
        protected ?array $additionalRatings = null,
        protected string $source,
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::TEXT               => $this->text,
            self::RATING             => $this->rating,
            self::DATE               => $this->date,
            self::AUTHOR             => $this->author,
            self::HELPFUL            => $this->helpful,
            self::ADDITIONAL_RATINGS => $this->additionalRatings,
            self::SOURCE             => $this->source,
        ];
    }

    public static function fromArray(array $array): DTOContract
    {
        return new self(
            text: Arr::get($array, self::TEXT),
            rating: Arr::get($array, self::RATING),
            date: Arr::get($array, self::DATE),
            author: Arr::get($array, self::AUTHOR),
            helpful: Arr::get($array, self::HELPFUL),
            additionalRatings: Arr::get($array, self::ADDITIONAL_RATINGS),
            source: Arr::get($array, self::SOURCE),
        );
    }

    public function getText(): string
    {
        return $this->text;
    }

    public function getRating(): float
    {
        return $this->rating;
    }

    public function getDate(): Carbon
    {
        return $this->date;
    }

    public function getAuthor(): string
    {
        return $this->author;
    }

    public function getHelpful(): ?int
    {
        return $this->helpful;
    }

    public function getAdditionalRatings(): ?array
    {
        return $this->additionalRatings;
    }

    public function getSource(): string
    {
        return $this->source;
    }

}
