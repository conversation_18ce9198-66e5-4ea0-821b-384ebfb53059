<?php

namespace App\DTO\CompanyProfile;

use App\DTO\DTOContract;
use App\Models\CompanyProfile\CompanyProfile;

class CompanyProfileDTO implements DTOContract
{
    const string NAME              = 'name';
    const string CATEGORIES        = 'categories';
    const string DESCRIPTION       = 'description';
    const string GENERAL_INFO      = 'general_info';
    const string HOURS             = 'hours';
    const string WEBSITES          = 'websites';
    const string PHONES            = 'phones';
    const string ADDRESSES         = 'addresses';
    const string YEARS_IN_BUSINESS = 'years_in_business';
    const string RATING            = 'rating';
    const string PAID              = 'paid';
    const string LOGO              = 'logo';
    const string EMAIL             = 'email';
    const string OTHER_LINKS       = 'other_links';
    const string SOCIAL_LINKS      = 'social_links';
    const string DISCOVERY_SCORE   = 'discovery_score';
    const string REVIEWS           = 'reviews';
    const string SLOGAN            = 'slogan';
    const string BRANDS            = 'brands';
    const string PAYMENT_METHODS   = 'payment_methods';
    const string AKA               = 'aka';
    const string CATEGORY_SERVICES = 'category_services';
    const string CLAIMED           = 'claimed';
    const string BBB_DATA          = 'bbb_data';
    const string INDUSTRY_SERVICE  = 'industry_service';
    const string DOMAINS           = 'domains';
    const string REVIEW_SUMMARY    = 'review_summary';


    /**
     * @param string $name
     * @param array $categories
     * @param string|null $description
     * @param string|null $generalInfo
     * @param array $hours
     * @param array $websites
     * @param array $phones
     * @param array<CompanyProfileAddressDTO> $addresses
     * @param int|null $yearsInBusiness
     * @param float|null $rating
     * @param bool|null $paid
     * @param string|null $logo
     * @param string|null $email
     * @param array $otherLinks
     * @param array $socialLinks
     * @param int $discoveryScore
     * @param array<CompanyProfileReviewDTO> $reviews
     * @param string|null $slogan
     * @param array $brands
     * @param array $paymentMethods
     * @param string|null $aka
     * @param array $categoryServices
     * @param bool|null $claimed
     * @param array $bbbData
     * @param string $industryService
     */
    public function __construct(
        protected string  $name,
        protected array   $categories,
        protected string  $industryService,
        protected int     $discoveryScore,
        protected array   $hours = [],
        protected array   $websites = [],
        protected array   $domains = [],
        protected array   $phones = [],
        protected array   $addresses = [],
        protected array   $otherLinks = [],
        protected array   $socialLinks = [],
        protected array   $reviews = [],
        protected array   $brands = [],
        protected array   $paymentMethods = [],
        protected array   $categoryServices = [],
        protected array   $bbbData = [],
        protected ?string $description = null,
        protected ?string $generalInfo = null,
        protected ?int $yearsInBusiness = null,
        protected ?float $rating = null,
        protected ?bool $paid = null,
        protected ?string $logo = null,
        protected ?string $email = null,
        protected ?string $slogan = null,
        protected ?string $aka = null,
        protected ?bool $claimed = null,
        protected ?CompanyProfileReviewSummaryDTO $reviewSummary = null
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::NAME              => $this->name,
            self::CATEGORIES        => $this->categories,
            self::DESCRIPTION       => $this->description,
            self::GENERAL_INFO      => $this->generalInfo,
            self::HOURS             => $this->hours,
            self::WEBSITES          => $this->websites,
            self::DOMAINS           => $this->domains,
            self::PHONES            => $this->phones,
            self::ADDRESSES         => collect($this->addresses)->toArray(),
            self::YEARS_IN_BUSINESS => $this->yearsInBusiness,
            self::RATING            => $this->rating,
            self::PAID              => $this->paid,
            self::LOGO              => $this->logo,
            self::EMAIL             => $this->email,
            self::OTHER_LINKS       => $this->otherLinks,
            self::SOCIAL_LINKS      => $this->socialLinks,
            self::DISCOVERY_SCORE   => $this->discoveryScore,
            self::REVIEWS           => collect($this->reviews)->toArray(),
            self::SLOGAN            => $this->slogan,
            self::BRANDS            => $this->brands,
            self::PAYMENT_METHODS   => $this->paymentMethods,
            self::AKA               => $this->aka,
            self::CATEGORY_SERVICES => $this->categoryServices,
            self::CLAIMED           => $this->claimed,
            self::BBB_DATA          => $this->bbbData,
            self::INDUSTRY_SERVICE  => $this->industryService,
            self::REVIEW_SUMMARY    => $this->reviewSummary->toArray()
        ];
    }

    public static function fromArray(array $array): DTOContract
    {
        // TODO: Implement fromArray() method.
    }

    public function toCompanyProfileFields(): array
    {
        $payload = [
            CompanyProfile::FIELD_NAME              => $this->getName(),
            CompanyProfile::FIELD_CATEGORIES        => $this->getCategories(),
            CompanyProfile::FIELD_DESCRIPTION       => $this->getDescription(),
            CompanyProfile::FIELD_GENERAL_INFO      => $this->getGeneralInfo(),
            CompanyProfile::FIELD_HOURS             => $this->getHours(),
            CompanyProfile::FIELD_WEBSITES          => $this->getWebsites(),
            CompanyProfile::FIELD_PHONES            => $this->getPhones(),
            CompanyProfile::FIELD_ADDRESSES         => $this->getAddresses(),
            CompanyProfile::FIELD_YEARS_IN_BUSINESS => $this->getYearsInBusiness(),
            CompanyProfile::FIELD_RATING            => $this->getRating(),
            CompanyProfile::FIELD_PAID              => $this->getPaid(),
            CompanyProfile::FIELD_LOGO              => $this->getLogo(),
            CompanyProfile::FIELD_EMAIL             => $this->getEmail(),
            CompanyProfile::FIELD_OTHER_LINKS       => $this->getOtherLinks(),
            CompanyProfile::FIELD_SOCIAL_LINKS      => $this->getSocialLinks(),
            CompanyProfile::FIELD_DISCOVERY_SCORE   => $this->getDiscoveryScore(),
            CompanyProfile::FIELD_PAYLOAD           => $this->toArray(),
            CompanyProfile::FIELD_SUMMARY_POSITIVES => $this->getReviewSummary()?->getPositives(),
            CompanyProfile::FIELD_SUMMARY_OVERVIEW  => $this->getReviewSummary()?->getOverview(),
            CompanyProfile::FIELD_SUMMARY_NEGATIVES => $this->getReviewSummary()?->getNegatives(),
            CompanyProfile::FIELD_SUMMARY_SOURCE    => $this->getReviewSummary()?->getSource(),
        ];

        //remove null and empty string values
        return array_filter($payload, function ($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * @return CompanyProfileAddressDTO|null
     */
    public function getPrimaryAddress(): ?CompanyProfileAddressDTO
    {
        return filled($this->addresses) ? $this->addresses[0] : null;
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getCategories(): array
    {
        return $this->categories;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function getGeneralInfo(): ?string
    {
        return $this->generalInfo;
    }

    public function getHours(): array
    {
        return $this->hours;
    }

    public function getWebsites(): array
    {
        return $this->websites;
    }

    public function getPhones(): array
    {
        return $this->phones;
    }

    public function getAddresses(): array
    {
        return $this->addresses;
    }

    public function getYearsInBusiness(): ?int
    {
        return $this->yearsInBusiness;
    }

    public function getRating(): ?float
    {
        return $this->rating;
    }

    public function getPaid(): ?bool
    {
        return $this->paid;
    }

    public function getLogo(): ?string
    {
        return $this->logo;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function getOtherLinks(): array
    {
        return $this->otherLinks;
    }

    public function getSocialLinks(): array
    {
        return $this->socialLinks;
    }

    public function getDiscoveryScore(): int
    {
        return $this->discoveryScore;
    }

    public function getReviews(): array
    {
        return $this->reviews;
    }

    public function getSlogan(): ?string
    {
        return $this->slogan;
    }

    public function getBrands(): array
    {
        return $this->brands;
    }

    public function getPaymentMethods(): array
    {
        return $this->paymentMethods;
    }

    public function getAka(): ?string
    {
        return $this->aka;
    }

    public function getCategoryServices(): array
    {
        return $this->categoryServices;
    }

    public function getClaimed(): ?bool
    {
        return $this->claimed;
    }

    public function getBbbData(): array
    {
        return $this->bbbData;
    }

    public function getIndustryService(): string
    {
        return $this->industryService;
    }

    public function getDomains(): array
    {
        return $this->domains;
    }

    public function getReviewSummary(): ?CompanyProfileReviewSummaryDTO
    {
        return $this->reviewSummary;
    }
}
