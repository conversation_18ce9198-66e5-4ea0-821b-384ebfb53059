<?php

namespace App\DTO\CompanyProfile;

use App\DTO\Billing\AddressData;
use Illuminate\Support\Arr;

class CompanyProfileAddressDTO extends AddressData
{
    const string COUNTY = 'county';
    const string RAW    = 'raw';

    public function __construct(
        protected string  $raw,
        ?string           $line_one = null,
        ?string           $line_two = null,
        ?string           $city = null,
        protected ?string $county = null,
        ?string           $state = null,
        ?string           $post_code = null,
        ?string           $country = null,
    )
    {
        parent::__construct($line_one, $city, $state, $post_code, $country, $line_two);
    }

    public function toArray(): array
    {
        return [
            ...parent::toArray(),
            self::COUNTY => $this->county,
            self::RAW    => $this->raw,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            line_one: Arr::get($array, self::LINE_ONE),
            line_two: Arr::get($array, self::LINE_TWO),
            city: Arr::get($array, self::CITY),
            county: Arr::get($array, self::COUNTY),
            raw: Arr::get($array, self::RAW),
            state: Arr::get($array, self::STATE),
            post_code: Arr::get($array, self::POST_CODE),
            country: Arr::get($array, self::COUNTRY),
        );
    }

    public function getCounty(): ?string
    {
        return $this->county;
    }

    public function getRaw(): ?string
    {
        return $this->raw;
    }
}
