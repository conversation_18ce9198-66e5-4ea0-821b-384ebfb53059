<?php

namespace App\DTO\ConsumerReviews;

use App\DTO\DTOContract;
use App\Enums\Odin\IndustryServiceSlug;
use App\Http\Requests\ConsumerReviews\CreateReviewRequest;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Arr;

class CreateReviewParam implements DTOContract
{
    /**
     * @param string|null $companyReference
     * @param string|null $reviewerName
     * @param string|null $reviewerContactMethod
     * @param string|null $reviewerEmail
     * @param string|null $reviewerPhone
     * @param string|null $reviewTitle
     * @param string|null $reviewComments
     * @param float|null $reviewOverallScore
     * @param string|null $origin
     * @param array|null $customData
     * @param string|null $reviewerIp
     * @param string|null $zipCode
     * @param string|null $displayLocation
     * @param int|null $companyLocationId
     * @param int|null $industryId
     * @param int|null $industryServiceId
     * @param string|null $reviewToken
     * @param int|null $legacyId
     */
    public function __construct(
        protected ?string $companyReference = null,
        protected ?string $reviewerName = null,
        protected ?string $reviewerContactMethod = null,
        protected ?string $reviewerEmail = null,
        protected ?string $reviewerPhone = null,
        protected ?string $reviewTitle = null,
        protected ?string $reviewComments = null,
        protected ?float  $reviewOverallScore = null,
        protected ?string $origin = null,
        protected ?array  $customData = null,
        protected ?string $reviewerIp = null,
        protected ?string $zipCode = null,
        protected ?string $displayLocation = null,
        protected ?int    $companyLocationId = null,
        protected ?int    $industryId = null,
        protected ?int    $industryServiceId = null,
        protected ?string $reviewToken = null,
        protected ?int    $legacyId = null,
    )
    {
    }

    /**
     * @return string|null
     */
    public function getReviewerName(): ?string
    {
        return $this->reviewerName;
    }

    /**
     * @param string|null $reviewerName
     * @return void
     */
    public function setReviewerName(?string $reviewerName): void
    {
        $this->reviewerName = $reviewerName;
    }

    /**
     * @return string|null
     */
    public function getReviewerContactMethod(): ?string
    {
        return $this->reviewerContactMethod;
    }

    /**
     * @param string|null $reviewerContactMethod
     * @return void
     */
    public function setReviewerContactMethod(?string $reviewerContactMethod): void
    {
        $this->reviewerContactMethod = $reviewerContactMethod;
    }

    /**
     * @return string|null
     */
    public function getReviewerEmail(): ?string
    {
        return $this->reviewerEmail;
    }

    /**
     * @param string|null $reviewerEmail
     * @return void
     */
    public function setReviewerEmail(?string $reviewerEmail): void
    {
        $this->reviewerEmail = $reviewerEmail;
    }

    /**
     * @return string|null
     */
    public function getReviewerPhone(): ?string
    {
        return $this->reviewerPhone;
    }

    /**
     * @param string|null $reviewerPhone
     * @return void
     */
    public function setReviewerPhone(?string $reviewerPhone): void
    {
        $this->reviewerPhone = $reviewerPhone
            ? preg_replace('/\D/', '', $reviewerPhone)
            : null;
    }

    /**
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->reviewTitle;
    }

    /**
     * @param string|null $reviewTitle
     * @return void
     */
    public function setReviewTitle(?string $reviewTitle): void
    {
        $this->reviewTitle = $reviewTitle;
    }


    /**
     * @return string|null
     */
    public function getReviewComments(): ?string
    {
        return $this->reviewComments;
    }

    /**
     * @param string|null $reviewComments
     * @return void
     */
    public function setReviewComments(?string $reviewComments): void
    {
        $this->reviewComments = $reviewComments;
    }

    /**
     * @return float|null
     */
    public function getReviewOverallScore(): ?float
    {
        return $this->reviewOverallScore;
    }

    /**
     * @param float|null $reviewOverallScore
     * @return void
     */
    public function setReviewOverallScore(?float $reviewOverallScore): void
    {
        $this->reviewOverallScore = $reviewOverallScore;
    }

    /**
     * @return string|null
     */
    public function getCompanyReference(): ?string
    {
        return $this->companyReference;
    }

    /**
     * @param string|null $companyReference
     * @return void
     */
    public function setCompanyReference(?string $companyReference): void
    {
        $this->companyReference = $companyReference;
    }

    /**
     * @return string|null
     */
    public function getOrigin(): ?string
    {
        return $this->origin;
    }

    /**
     * @param string|null $origin
     * @return void
     */
    public function setOrigin(?string $origin): void
    {
        $this->origin = $origin;
    }

    /**
     * @return string|null
     */
    public function getReviewerIp(): ?string
    {
        return $this->reviewerIp;
    }

    /**
     * @param string|null $reviewerIp
     * @return void
     */
    public function setReviewerIp(?string $reviewerIp): void
    {
        $this->reviewerIp = $reviewerIp;
    }

    /**
     * @return array
     */
    public function getCustomData(): array
    {
        return $this->customData ?? [];
    }

    /**
     * @param array|null $customData
     * @return void
     */
    public function setCustomData(?array $customData): void
    {
        $this->$customData = $customData;
    }

    /**
     * @return string|null
     */
    public function getReviewToken(): ?string
    {
        return $this->reviewToken;
    }

    /**
     * @return string|null
     */
    public function getZipCode(): ?string
    {
        return $this->zipCode;
    }

    /**
     * @param string|null $zipCode
     * @return void
     */
    public function setZipCode(?string $zipCode): void
    {
        $this->zipCode = $zipCode;
    }

    public function getDisplayLocation(): ?string
    {
        return $this->displayLocation;
    }

    public function setDisplayLocation(?string $displayLocation): void
    {
        $this->displayLocation = $displayLocation;
    }

    /**
     * @return int|null
     */
    public function getCompanyLocationId(): ?int
    {
        return $this->companyLocationId;
    }

    /**
     * @param int|null $companyLocationId
     * @return void
     */
    public function setCompanyLocationId(?int $companyLocationId): void
    {
        $this->companyLocationId = $companyLocationId;
    }

    /**
     * @return int|null
     */
    public function getIndustryId(): ?int
    {
        return $this->industryId;
    }

    /**
     * @param int|null $industryId
     * @return void
     */
    public function setIndustryId(?int $industryId): void
    {
        $this->industryId = $industryId;
    }

    /**
     * @return int|null
     */
    public function getIndustryServiceId(): ?int
    {
        return $this->industryServiceId;
    }

    /**
     * @param int|null $industryServiceId
     * @return void
     */
    public function setIndustryServiceId(?int $industryServiceId): void
    {
        $this->industryServiceId = $industryServiceId;
    }

    /**
     * @param string|null $industryServiceSlug
     * @return void
     */
    public function setIndustryAndService(?string $industryServiceSlug): void
    {
        $service = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $industryServiceSlug)
            ->first()
            ?? IndustryService::query()
                ->where(IndustryService::FIELD_SLUG, IndustryServiceSlug::SOLAR_INSTALLATION->value)
                ->first();

        $this->setIndustryServiceId($service->id);
        $this->setIndustryId($service->industry_id);
    }

    /**
     * @return int|null
     */
    public function getLegacyId(): ?int
    {
        return $this->legacyId;
    }

    /**
     * @param int|null $legacyId
     * @return void
     */
    public function setLegacyId(?int $legacyId): void
    {
        $this->legacyId = $legacyId;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            CreateReviewRequest::FIELD_COMPANY_REFERENCE       => $this->companyReference,
            CreateReviewRequest::FIELD_REVIEWER_NAME           => $this->reviewerName,
            CreateReviewRequest::FIELD_REVIEWER_CONTACT_METHOD => $this->reviewerContactMethod,
            CreateReviewRequest::FIELD_REVIEWER_EMAIL          => $this->reviewerEmail,
            CreateReviewRequest::FIELD_REVIEWER_PHONE          => $this->reviewerPhone,
            CreateReviewRequest::FIELD_REVIEW_TITLE            => $this->reviewTitle,
            CreateReviewRequest::FIELD_REVIEW_COMMENTS         => $this->reviewComments,
            CreateReviewRequest::FIELD_REVIEW_OVERALL_SCORE    => $this->reviewOverallScore,
            CreateReviewRequest::FIELD_REGISTRATION_ORIGIN     => $this->origin,
            CreateReviewRequest::FIELD_CUSTOM_REVIEW_DATA      => $this->customData,
            CreateReviewRequest::FIELD_REVIEWER_IP             => $this->reviewerIp,
            CreateReviewRequest::FIELD_ZIP_CODE                => $this->zipCode,
            CreateReviewRequest::FIELD_COMPANY_LOCATION_ID     => $this->companyLocationId,
            CreateReviewRequest::FIELD_INDUSTRY_ID             => $this->industryId,
            CreateReviewRequest::FIELD_INDUSTRY_SERVICE_ID     => $this->industryServiceId,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        $newParams = new CreateReviewParam(
            companyReference: Arr::get($array, CreateReviewRequest::FIELD_COMPANY_REFERENCE),
            reviewerName: Arr::get($array, CreateReviewRequest::FIELD_REVIEWER_NAME),
            reviewerContactMethod: Arr::get($array, CreateReviewRequest::FIELD_REVIEWER_CONTACT_METHOD),
            reviewerEmail: Arr::get($array, CreateReviewRequest::FIELD_REVIEWER_EMAIL),
            reviewerPhone: preg_replace("/\D/", "", Arr::get($array, CreateReviewRequest::FIELD_REVIEWER_PHONE, '')),
            reviewTitle: Arr::get($array, CreateReviewRequest::FIELD_REVIEW_TITLE),
            reviewComments: Arr::get($array, CreateReviewRequest::FIELD_REVIEW_COMMENTS),
            reviewOverallScore: Arr::get($array, CreateReviewRequest::FIELD_REVIEW_OVERALL_SCORE),
            origin: Arr::get($array, CreateReviewRequest::FIELD_REGISTRATION_ORIGIN),
            customData: Arr::get($array, CreateReviewRequest::FIELD_CUSTOM_REVIEW_DATA),
            reviewerIp: Arr::get($array, CreateReviewRequest::FIELD_REVIEWER_IP),
            zipCode: Arr::get($array, CreateReviewRequest::FIELD_ZIP_CODE),
            companyLocationId: Arr::get($array, CreateReviewRequest::FIELD_INDUSTRY_ID),
            industryId: Arr::get($array, CreateReviewRequest::FIELD_INDUSTRY_SERVICE_ID),
            industryServiceId: Arr::get($array, CreateReviewRequest::FIELD_CONSUMER_REVIEW_TOKEN),
        );

        $newParams->setIndustryAndService(Arr::get($array, CreateReviewRequest::FIELD_INDUSTRY_SERVICE_SLUG));

        return $newParams;
    }
}
