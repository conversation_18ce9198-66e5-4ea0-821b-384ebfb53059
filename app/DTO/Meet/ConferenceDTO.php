<?php

namespace App\DTO\Meet;

use App\DTO\DTOContract;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class ConferenceDTO implements DTOContract
{
    const string FIELD_END_TIME     = 'end_time';
    const string FIELD_EXPIRE_TIME  = 'expire_time';
    const string FIELD_NAME         = 'name';
    const string FIELD_SPACE        = 'space';
    const string FIELD_START_TIME   = 'start_time';
    const string FIELD_TRANSCRIPTS  = 'transcripts';
    const string FIELD_PARTICIPANTS = 'participants';
    const string FIELD_RECORDINGS   = 'recordings';

    public function __construct(
        protected string $name,
        protected ?string $endTime = null,
        protected ?string $expireTime = null,
        protected ?string $space = null,
        protected ?string $startTime = null,
        protected Collection $transcripts = new Collection(),
        protected Collection $participants = new Collection(),
        protected Collection $recordings = new Collection(),
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_END_TIME     => $this->endTime,
            self::FIELD_EXPIRE_TIME  => $this->expireTime,
            self::FIELD_NAME         => $this->name,
            self::FIELD_SPACE        => $this->space,
            self::FIELD_START_TIME   => $this->startTime,
            self::FIELD_TRANSCRIPTS  => $this->transcripts,
            self::FIELD_PARTICIPANTS => $this->participants,
            self::FIELD_RECORDINGS   => $this->recordings,
        ];
    }

    /**
     * @param array $array
     * @return ConferenceDTO
     */
    public static function fromArray(array $array): ConferenceDTO
    {
        return new self(
            name        : Arr::get($array, self::FIELD_NAME),
            endTime     : Arr::get($array, self::FIELD_END_TIME),
            expireTime  : Arr::get($array, self::FIELD_EXPIRE_TIME),
            space       : Arr::get($array, self::FIELD_SPACE),
            startTime   : Arr::get($array, self::FIELD_START_TIME),
            transcripts : collect(Arr::get($array, self::FIELD_TRANSCRIPTS, []))->map(fn($item) => ConferenceTranscriptDTO::fromArray($item)),
            participants: collect(Arr::get($array, self::FIELD_PARTICIPANTS, []))->map(fn($item) => ConferenceParticipantDTO::fromArray($item)),
            recordings  : collect(Arr::get($array, self::FIELD_RECORDINGS, []))->map(fn($item) => ConferenceRecordingDTO::fromArray($item)),
        );
    }

    /**
     * @return string|null
     */
    public function getEndTime(): ?string
    {
        return $this->endTime;
    }

    /**
     * @return string|null
     */
    public function getExpireTime(): ?string
    {
        return $this->expireTime;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getSpace(): ?string
    {
        return $this->space;
    }

    /**
     * @return string|null
     */
    public function getStartTime(): ?string
    {
        return $this->startTime;
    }

    /**
     * @return Collection
     */
    public function getTranscripts(): Collection
    {
        return $this->transcripts;
    }

    /**
     * @param Collection $transcripts
     * @return void
     */
    public function setTranscripts(Collection $transcripts): void
    {
        $this->transcripts = $transcripts;
    }

    /**
     * @return Collection
     */
    public function getParticipants(): Collection
    {
        return $this->participants;
    }

    /**
     * @param Collection $participants
     * @return void
     */
    public function setParticipants(Collection $participants): void
    {
        $this->participants = $participants;
    }

    /**
     * @param Collection $recordings
     * @return void
     */
    public function setRecordings(Collection $recordings): void
    {
        $this->recordings = $recordings;
    }

    /**
     * @return Collection
     */
    public function getRecordings(): Collection
    {
        return $this->recordings;
    }

    /**
     * @return int
     */
    public function getDurationInSeconds(): int
    {
        if (!$this->startTime || !$this->endTime) {
            return 0;
        }

        $start = Carbon::parse($this->startTime);
        $end = Carbon::parse($this->endTime);

        return $end->diffInSeconds($start);
    }
}
