<?php

namespace App\DTO\Meet;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class ConferenceRecordingDTO implements DTOContract
{

    const string FIELD_END_TIME                     = 'end_time';
    const string FIELD_NAME                         = 'name';
    const string FIELD_START_TIME                   = 'start_time';
    const string FIELD_STATE                        = 'state';
    const string FIELD_DRIVE_DESTINATION_FILE       = 'drive_destination_file';
    const string FIELD_DRIVE_DESTINATION_EXPORT_URI = 'drive_destination_export_uri';

    public function __construct(
        protected string $name,
        protected ?string $endTime = null,
        protected ?string $startTime = null,
        protected ?string $state = null,
        protected ?string $driveDestinationFile = null,
        protected ?string $driveDestinationExportUri = null,
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_END_TIME                     => $this->endTime,
            self::FIELD_NAME                         => $this->name,
            self::FIELD_START_TIME                   => $this->startTime,
            self::FIELD_STATE                        => $this->state,
            self::FIELD_DRIVE_DESTINATION_FILE       => $this->driveDestinationFile,
            self::FIELD_DRIVE_DESTINATION_EXPORT_URI => $this->driveDestinationExportUri,
        ];
    }

    /**
     * @param array $array
     * @return ConferenceRecordingDTO
     */
    public static function fromArray(array $array): ConferenceRecordingDTO
    {
        return new ConferenceRecordingDTO(
            name                     : Arr::get($array, self::FIELD_NAME),
            endTime                  : Arr::get($array, self::FIELD_END_TIME),
            startTime                : Arr::get($array, self::FIELD_START_TIME),
            state                    : Arr::get($array, self::FIELD_STATE),
            driveDestinationFile     : Arr::get($array, self::FIELD_DRIVE_DESTINATION_FILE),
            driveDestinationExportUri: Arr::get($array, self::FIELD_DRIVE_DESTINATION_EXPORT_URI),
        );
    }

    /**
     * @return string|null
     */
    public function getEndTime(): ?string
    {
        return $this->endTime;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getStartTime(): ?string
    {
        return $this->startTime;
    }

    /**
     * @return string|null
     */
    public function getState(): ?string
    {
        return $this->state;
    }

    /**
     * @return string|null
     */
    public function getDriveDestinationFile(): ?string
    {
        return $this->driveDestinationFile;
    }

    /**
     * @return string|null
     */
    public function getDriveDestinationExportUri(): ?string
    {
        return $this->driveDestinationExportUri;
    }
}
