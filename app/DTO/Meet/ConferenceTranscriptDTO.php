<?php

namespace App\DTO\Meet;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class ConferenceTranscriptDTO implements DTOContract
{
    const string FIELD_END_TIME                      = 'end_time';
    const string FIELD_NAME                          = 'name';
    const string FIELD_START_TIME                    = 'start_time';
    const string FIELD_STATE                         = 'state';
    const string FIELD_DOCS_DESTINATION_DOCUMENT_ID  = 'docs_destination_document_id';
    const string FIELD_DOCS_DESTINATION_DOCUMENT_URL = 'docs_destination_document_url';
    const string FIELD_ENTRIES                       = 'entries';


    public function __construct(
        protected string $name,
        protected ?string $endTime = null,
        protected ?string $startTime = null,
        protected ?string $state = null,
        protected ?string $docsDestinationDocumentId = null,
        protected ?string $docsDestinationDocumentUrl = null,
        protected ?Collection $entries = new Collection(),

    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_END_TIME                      => $this->endTime,
            self::FIELD_NAME                          => $this->name,
            self::FIELD_START_TIME                    => $this->startTime,
            self::FIELD_STATE                         => $this->state,
            self::FIELD_DOCS_DESTINATION_DOCUMENT_ID  => $this->docsDestinationDocumentId,
            self::FIELD_DOCS_DESTINATION_DOCUMENT_URL => $this->docsDestinationDocumentUrl,
            self::FIELD_ENTRIES                       => $this->entries,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            name                      : Arr::get($array, self::FIELD_NAME),
            endTime                   : Arr::get($array, self::FIELD_END_TIME),
            startTime                 : Arr::get($array, self::FIELD_START_TIME),
            state                     : Arr::get($array, self::FIELD_STATE),
            docsDestinationDocumentId : Arr::get($array, self::FIELD_DOCS_DESTINATION_DOCUMENT_ID),
            docsDestinationDocumentUrl: Arr::get($array, self::FIELD_DOCS_DESTINATION_DOCUMENT_URL),
            entries                   : collect(Arr::get($array, self::FIELD_ENTRIES, []))->map(fn($item) => ConferenceTranscriptEntryDTO::fromArray($item)),
        );
    }

    /**
     * @return string|null
     */
    public function getEndTime(): ?string
    {
        return $this->endTime;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getStartTime(): ?string
    {
        return $this->startTime;
    }

    /**
     * @return string|null
     */
    public function getState(): ?string
    {
        return $this->state;
    }

    /**
     * @return string|null
     */
    public function getDocsDestinationDocumentId(): ?string
    {
        return $this->docsDestinationDocumentId;
    }

    /**
     * @return string|null
     */
    public function getDocsDestinationDocumentUrl(): ?string
    {
        return $this->docsDestinationDocumentUrl;
    }

    /**
     * @return Collection|null
     */
    public function getEntries(): ?Collection
    {
        return $this->entries;
    }
}
