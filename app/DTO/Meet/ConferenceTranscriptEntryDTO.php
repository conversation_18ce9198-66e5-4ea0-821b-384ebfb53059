<?php

namespace App\DTO\Meet;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class ConferenceTranscriptEntryDTO implements DTOContract
{
    const string FIELD_END_TIME      = 'end_time';
    const string FIELD_LANGUAGE_CODE = 'language_code';
    const string FIELD_NAME          = 'name';
    const string FIELD_PARTICIPANT   = 'participant';
    const string FIELD_START_TIME    = 'start_time';
    const string FIELD_TEXT          = 'text';

    public function __construct(
        protected string $name,
        protected string $participant,
        protected ?string $endTime = null,
        protected ?string $languageCode = null,
        protected ?string $startTime = null,
        protected ?string $text = null,
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_END_TIME      => $this->endTime,
            self::FIELD_LANGUAGE_CODE => $this->languageCode,
            self::FIELD_NAME          => $this->name,
            self::FIELD_PARTICIPANT   => $this->participant,
            self::FIELD_START_TIME    => $this->startTime,
            self::FIELD_TEXT          => $this->text,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            name        : Arr::get($array, self::FIELD_NAME),
            participant : Arr::get($array, self::FIELD_PARTICIPANT),
            endTime     : Arr::get($array, self::FIELD_END_TIME),
            languageCode: Arr::get($array, self::FIELD_LANGUAGE_CODE),
            startTime   : Arr::get($array, self::FIELD_START_TIME),
            text        : Arr::get($array, self::FIELD_TEXT),
        );
    }

    /**
     * @return string|null
     */
    public function getEndTime(): ?string
    {
        return $this->endTime;
    }

    /**
     * @return string|null
     */
    public function getLanguageCode(): ?string
    {
        return $this->languageCode;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string
     */
    public function getParticipant(): string
    {
        return $this->participant;
    }

    /**
     * @return string|null
     */
    public function getStartTime(): ?string
    {
        return $this->startTime;
    }

    /**
     * @return string|null
     */
    public function getText(): ?string
    {
        return $this->text;
    }
}
