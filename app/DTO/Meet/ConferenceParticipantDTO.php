<?php

namespace App\DTO\Meet;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class ConferenceParticipantDTO implements DTOContract
{
    const string FIELD_EARLIEST_START_TIME = 'earliest_start_time';
    const string FIELD_LATEST_END_TIME     = 'latest_end_time';
    const string FIELD_NAME                = 'name';
    const string FIELD_USER_DISPLAY_NAME   = 'user_display_name';
    const string FIELD_SESSIONS            = 'sessions';

    public function __construct(
        protected string $name,
        protected ?string $earliestStartTime = null,
        protected ?string $latestEndTime = null,
        protected ?string $userDisplayName = null,
        protected Collection $sessions = new Collection()
    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_EARLIEST_START_TIME => $this->earliestStartTime,
            self::FIELD_LATEST_END_TIME     => $this->latestEndTime,
            self::FIELD_NAME                => $this->name,
            self::FIELD_USER_DISPLAY_NAME   => $this->userDisplayName,
            self::FIELD_SESSIONS            => $this->sessions,
        ];
    }

    /**
     * @param array $array
     * @return ConferenceParticipantDTO
     */
    public static function fromArray(array $array): ConferenceParticipantDTO
    {
        return new self(
            name             : Arr::get($array, self::FIELD_NAME),
            earliestStartTime: Arr::get($array, self::FIELD_EARLIEST_START_TIME),
            latestEndTime    : Arr::get($array, self::FIELD_LATEST_END_TIME),
            userDisplayName  : Arr::get($array, self::FIELD_USER_DISPLAY_NAME),
            sessions         : collect(Arr::get($array, self::FIELD_SESSIONS, []))->map(fn($item) => ConferenceParticipantSessionDTO::fromArray($item)),
        );
    }

    /**
     * @return string|null
     */
    public function getEarliestStartTime(): ?string
    {
        return $this->earliestStartTime;
    }

    /**
     * @return string|null
     */
    public function getLatestEndTime(): ?string
    {
        return $this->latestEndTime;
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getUserDisplayName(): ?string
    {
        return $this->userDisplayName;
    }

    /**
     * @return Collection
     */
    public function getSessions(): Collection
    {
        return $this->sessions;
    }
}
