<?php

namespace App\DTO\Meet;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class ConferenceParticipantSessionDTO implements DTOContract
{
    const string FIELD_END_TIME   = 'end_time';
    const string FIELD_NAME       = 'name';
    const string FIELD_START_TIME = 'start_time';

    public function __construct(
        protected string $name,
        protected ?string $endTime = null,
        protected ?string $startTime = null,
    )
    {

    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getEndTime(): ?string
    {
        return $this->endTime;
    }

    /**
     * @return string|null
     */
    public function getStartTime(): ?string
    {
        return $this->startTime;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_END_TIME   => $this->endTime,
            self::FIELD_NAME       => $this->name,
            self::FIELD_START_TIME => $this->startTime,
        ];
    }

    /**
     * @param array $array
     * @return ConferenceParticipantSessionDTO
     */
    public static function fromArray(array $array): ConferenceParticipantSessionDTO
    {
        return new self(
            name     : Arr::get($array, self::FIELD_NAME),
            endTime  : Arr::get($array, self::FIELD_END_TIME),
            startTime: Arr::get($array, self::FIELD_START_TIME),
        );
    }
}
