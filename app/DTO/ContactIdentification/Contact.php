<?php

namespace App\DTO\ContactIdentification;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class Contact implements DTOContract
{
    const FIELD_ID          = 'id';
    const FIELD_TYPE        = 'type';
    const FIELD_MATCH_FIELD = 'match_field';

    public function __construct(
        protected string $id,
        protected string $type,
        protected string $matchField,
    )
    {

    }

    public function toArray(): array
    {
        return [
            self::FIELD_ID          => $this->id,
            self::FIELD_TYPE        => $this->type,
            self::FIELD_MATCH_FIELD => $this->matchField,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::FIELD_ID),
            Arr::get($array, self::FIELD_TYPE),
            Arr::get($array, self::FIELD_MATCH_FIELD),
        );
    }

    /**
     * @return string
     */
    public function getId(): string
    {
        return $this->id;
    }

    /**
     * @return string
     */
    public function getType(): string
    {
        return $this->type;
    }

    /**
     * @return string
     */
    public function getMatchField(): string
    {
        return $this->matchField;
    }
}
