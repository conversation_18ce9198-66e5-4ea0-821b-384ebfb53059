<?php

namespace App\DTO\EmailTemplate;

use App\DTO\DTOContract;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Support\Arr;

class EmailTemplatePayloadDTO implements DTOContract, CastsAttributes
{
    const string FIELD_ATTACHMENTS = 'attachments';

    /**
     * @param array $attachments
     */
    public function __construct(
        protected array $attachments = [],
    )
    {

    }

    /**
     * @return array
     */
    public function getAttachments(): array
    {
        return $this->attachments;
    }

    /**
     * @return array[]
     */
    public function toArray(): array
    {
        return [
            self::FIELD_ATTACHMENTS => $this->attachments,
        ];
    }

    /**
     * @param array|null $array
     * @return DTOContract
     */
    public static function fromArray(?array $array = []): DTOContract
    {
        return new self(
            attachments: Arr::get($array, self::FIELD_ATTACHMENTS, []),
        );
    }

    /**
     * @param $model
     * @param string $key
     * @param $value
     * @param array $attributes
     * @return DTOContract|null
     */
    public function get($model, string $key, $value, array $attributes)
    {
        return self::fromArray(json_decode($value ?? '', true));
    }

    /**
     * @param $model
     * @param string $key
     * @param $value
     * @param array $attributes
     * @return string
     */
    public function set($model, string $key, $value, array $attributes): string
    {
        return json_encode($value instanceof self ? $value->toArray() : []);
    }
}
