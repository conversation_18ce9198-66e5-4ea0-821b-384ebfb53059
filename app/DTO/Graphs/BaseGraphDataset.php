<?php

namespace App\DTO\Graphs;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class BaseGraphDataset implements DTOContract
{

    const string FIELD_LABEL = 'label';
    const string FIELD_DATA  = 'data';

    /**
     * @param string|null $label
     * @param numeric[]|null $data
     */
    public function __construct(
        protected ?string $label = null,
        protected ?array  $data = [],
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::FIELD_LABEL => $this->label,
            self::FIELD_DATA  => $this->data,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::FIELD_LABEL),
            Arr::get($array, self::FIELD_DATA)
        );
    }

    public function getLabel(): ?string
    {
        return $this->label;
    }

    public function getData(): array
    {
        return $this?->data ?? [];
    }
}