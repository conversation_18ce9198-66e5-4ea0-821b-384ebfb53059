<?php

namespace App\DTO\Billing;

use App\DTO\DTOContract;
use App\Enums\Billing\InvoiceItemTypes;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class InvoicePayload implements DTOContract
{
    const string FIELD_UUID               = 'uuid';
    const string FIELD_COMPANY_ID         = 'company_id';
    const string FIELD_DUE_DATE           = 'due_date';
    const string FIELD_ISSUE_DATE         = 'issue_date';
    const string FIELD_NOTE               = 'note';
    const string FIELD_STATUS             = 'status';
    const string FIELD_ITEMS              = 'items';
    const string FIELD_TAGS               = 'tags';
    const string FIELD_BILLING_PROFILE_ID = 'billing_profile_id';

    public function __construct(
        protected ?string $uuid = null,
        protected ?string $companyId = null,
        protected string|Carbon|null $dueDate = null,
        protected string|Carbon|null $issueDate = null,
        protected ?string $note = null,
        protected ?string $status = null,
        protected ?int $billingProfileId = null,
        protected Collection $items = new Collection(),
        protected Collection $tags = new Collection(),
    )
    {

    }

    /**
     * @return string|null
     */
    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    /**
     * @return string|null
     */
    public function getCompanyId(): ?string
    {
        return $this->companyId;
    }

    /**
     * @return string|null
     */
    public function getDueDate(): ?string
    {
        return $this->dueDate;
    }

    /**
     * @return string|null
     */
    public function getIssueDate(): ?string
    {
        return $this->issueDate;
    }

    /**
     * @return string|null
     */
    public function getNote(): ?string
    {
        return $this->note;
    }

    /**
     * @return Collection
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    /**
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * @return Collection
     */
    public function getTags(): Collection
    {
        return $this->tags;
    }

    /**
     * @return int|null
     */
    public function getBillingProfileId(): ?int
    {
        return $this->billingProfileId;
    }

    /**
     * @param int $billingProfileId
     * @return InvoicePayload
     */
    public function setBillingProfileId(int $billingProfileId): self
    {
        $this->billingProfileId = $billingProfileId;
        return $this;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_UUID               => $this->uuid,
            self::FIELD_COMPANY_ID         => $this->companyId,
            self::FIELD_DUE_DATE           => $this->dueDate,
            self::FIELD_ISSUE_DATE         => $this->issueDate,
            self::FIELD_NOTE               => $this->note,
            self::FIELD_STATUS             => $this->status,
            self::FIELD_BILLING_PROFILE_ID => $this->billingProfileId,
            self::FIELD_ITEMS              => collect($this->items)->toArray(),
            self::FIELD_TAGS               => collect($this->tags)->toArray(),
        ];
    }

    /**
     * @return Collection<InvoiceItemTypes>
     */
    public function getInvoiceItemTypes(): Collection
    {
        return collect($this->items->toArray())
            ->pluck('billable_type')
            ->unique()
            ->filter()
            ->map(fn (string $type) => InvoiceItemTypes::tryFrom($type));
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            uuid            : Arr::get($array, self::FIELD_UUID),
            companyId       : Arr::get($array, self::FIELD_COMPANY_ID),
            dueDate         : Arr::get($array, self::FIELD_DUE_DATE),
            issueDate       : Arr::get($array, self::FIELD_ISSUE_DATE),
            note            : Arr::get($array, self::FIELD_NOTE),
            status          : Arr::get($array, self::FIELD_STATUS),
            billingProfileId: Arr::get($array, self::FIELD_BILLING_PROFILE_ID),
            items           : collect(Arr::get($array, self::FIELD_ITEMS, [])),
            tags            : collect(Arr::get($array, self::FIELD_TAGS, [])),
        );
    }
}
