<?php

namespace App\DTO\Billing\Refund;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class InvoiceRefundItemDTO implements DTOContract
{
    const string ID                = 'id';
    const string INVOICE_ITEM_ID   = 'invoice_item_id';
    const string INVOICE_REFUND_ID = 'invoice_refund_id';
    const string VALUE             = 'value';


    public function __construct(
        protected ?int   $id = null,
        protected ?int   $invoiceItemId = null,
        protected ?int   $invoiceRefundId = null,
        protected ?float $value = null,
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::ID                => $this->id,
            self::INVOICE_ITEM_ID   => $this->invoiceItemId,
            self::INVOICE_REFUND_ID => $this->invoiceRefundId,
            self::VALUE             => $this->value,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::ID),
            Arr::get($array, self::INVOICE_ITEM_ID),
            Arr::get($array, self::INVOICE_REFUND_ID),
            Arr::get($array, self::VALUE),
        );
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getInvoiceItemId(): ?int
    {
        return $this->invoiceItemId;
    }

    public function getInvoiceRefundId(): ?int
    {
        return $this->invoiceRefundId;
    }

    public function getValue(): ?float
    {
        return $this->value;
    }
}
