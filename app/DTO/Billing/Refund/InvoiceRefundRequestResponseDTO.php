<?php

namespace App\DTO\Billing\Refund;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class InvoiceRefundRequestResponseDTO implements DTOContract
{

    const string FIELD_EXTERNAL_REFUND_ID   = 'external_refund_id';
    const string FIELD_CHARGE_ID            = 'charge_id';
    const string FIELD_EXTERNAL_CHARGE_ID   = 'external_charge_id';
    const string FIELD_INTERNAL_REFUND_UUID = 'internal_refund_uuid';

    public function __construct(
        public ?string $externalRefundId = null,
        public ?string $chargeId = null,
        public ?string $externalChargeId = null,
        public ?string $internalRefundUuid = null,
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_EXTERNAL_REFUND_ID   => $this->externalRefundId,
            self::FIELD_CHARGE_ID            => $this->chargeId,
            self::FIELD_EXTERNAL_CHARGE_ID   => $this->externalChargeId,
            self::FIELD_INTERNAL_REFUND_UUID => $this->internalRefundUuid,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            externalRefundId  : Arr::get($array, self::FIELD_EXTERNAL_REFUND_ID),
            chargeId          : Arr::get($array, self::FIELD_CHARGE_ID),
            externalChargeId  : Arr::get($array, self::FIELD_EXTERNAL_CHARGE_ID),
            internalRefundUuid: Arr::get($array, self::FIELD_INTERNAL_REFUND_UUID),
        );
    }
}
