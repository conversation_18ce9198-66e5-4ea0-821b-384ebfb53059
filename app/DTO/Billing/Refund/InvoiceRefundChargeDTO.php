<?php

namespace App\DTO\Billing\Refund;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class InvoiceRefundChargeDTO implements DTOContract
{

    const string ID                    = 'id';
    const string UUID                  = 'uuid';
    const string AMOUNT                = 'amount';
    const string REQUEST_STATUS        = 'request_status';
    const string INVOICE_REFUND_ID     = 'invoice_refund_id';
    const string REFUNDED_PAYMENT_ID   = 'refunded_payment_id';
    const string REFUND_TRANSACTION_ID = 'refund_transaction_id';


    public function __construct(
        protected ?int    $id = null,
        protected ?string $uuid = null,
        protected ?float  $amount = null,
        protected ?string $requestStatus = null,
        protected ?int    $invoiceRefundId = null,
        protected ?int    $refundedPaymentId = null,
        protected ?int    $refundTransactionId = null,
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::ID                    => $this->id,
            self::UUID                  => $this->uuid,
            self::AMOUNT                => $this->amount,
            self::REQUEST_STATUS        => $this->requestStatus,
            self::INVOICE_REFUND_ID     => $this->invoiceRefundId,
            self::REFUNDED_PAYMENT_ID   => $this->refundedPaymentId,
            self::REFUND_TRANSACTION_ID => $this->refundTransactionId,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::ID),
            Arr::get($array, self::UUID),
            Arr::get($array, self::AMOUNT),
            Arr::get($array, self::REQUEST_STATUS),
            Arr::get($array, self::INVOICE_REFUND_ID),
            Arr::get($array, self::REFUNDED_PAYMENT_ID),
            Arr::get($array, self::REFUND_TRANSACTION_ID),
        );
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function getRequestStatus(): ?string
    {
        return $this->requestStatus;
    }

    public function getInvoiceRefundId(): ?int
    {
        return $this->invoiceRefundId;
    }

    public function getRefundedPaymentId(): ?int
    {
        return $this->refundedPaymentId;
    }

    public function getRefundTransactionId(): ?int
    {
        return $this->refundTransactionId;
    }
}
