<?php

namespace App\DTO\Billing\Refund;

use App\DTO\DTOContract;
use App\Enums\InvoiceRefundStatus;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class InvoiceRefundDTO implements DTOContract
{
    const string ID                     = 'id';
    const string UUID                   = 'uuid';
    const string INVOICE_ID             = 'invoice_id';
    const string TOTAL                  = 'total';
    const string STATUS                 = 'status';
    const string REASON                 = 'reason';
    const string INVOICE_REFUND_ITEMS   = 'invoice_refund_items';
    const string INVOICE_REFUND_CHARGES = 'invoice_refund_charges';

    /**
     * @param int|null $id
     * @param string|null $uuid
     * @param int|null $invoice_id
     * @param float|null $total
     * @param InvoiceRefundStatus|null $status
     * @param string|null $reason
     * @param Collection<InvoiceRefundItemDTO> $invoiceRefundItemsCollection
     * @param Collection<InvoiceRefundChargeDTO> $invoiceRefundChargesCollection
     */
    public function __construct(
        protected ?int                 $id = null,
        protected ?string              $uuid = null,
        protected ?int                 $invoice_id = null,
        protected ?float               $total = null,
        protected ?InvoiceRefundStatus $status = null,
        protected ?string              $reason = null,
        protected Collection           $invoiceRefundItemsCollection = new Collection(),
        protected Collection           $invoiceRefundChargesCollection = new Collection(),
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::ID                     => $this->id,
            self::UUID                   => $this->uuid,
            self::INVOICE_ID             => $this->invoice_id,
            self::TOTAL                  => $this->total,
            self::STATUS                 => $this->status->value,
            self::REASON                 => $this->reason,
            self::INVOICE_REFUND_ITEMS   => $this->invoiceRefundItemsCollection->toArray(),
            self::INVOICE_REFUND_CHARGES => $this->invoiceRefundChargesCollection->toArray(),
        ];
    }

    public static function fromArray(array $array): self
    {
        $items = collect(Arr::get($array, self::INVOICE_REFUND_ITEMS, []))->map(fn(array $data) => InvoiceRefundItemDTO::fromArray($data));

        $refundCharges = collect(Arr::get($array, self::INVOICE_REFUND_CHARGES, []))->map(fn(array $data) => InvoiceRefundChargeDTO::fromArray($data));

        $status = InvoiceRefundStatus::tryFrom(Arr::get($array, self::STATUS));

        return new self(
            Arr::get($array, self::ID),
            Arr::get($array, self::UUID),
            Arr::get($array, self::INVOICE_ID),
            Arr::get($array, self::TOTAL),
            $status,
            Arr::get($array, self::REASON),
            $items,
            $refundCharges,
        );
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    public function getInvoiceId(): ?int
    {
        return $this->invoice_id;
    }

    public function getTotal(): ?float
    {
        return $this->total;
    }

    public function getStatus(): ?InvoiceRefundStatus
    {
        return $this->status;
    }

    public function getReason(): ?string
    {
        return $this->reason;
    }


    /**
     * @return Collection<InvoiceRefundItemDTO>
     */
    public function getInvoiceRefundItemsCollection(): Collection
    {
        return $this->invoiceRefundItemsCollection;
    }

    /**
     * @return Collection<InvoiceRefundChargeDTO>
     */
    public function getInvoiceRefundChargesCollection(): Collection
    {
        return $this->invoiceRefundChargesCollection;
    }
}
