<?php

namespace App\DTO\Billing\Refund;

use App\DTO\Billing\InvoiceItemDTO;
use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class InvoiceRefundDataDTO implements DTOContract
{
    public const string ID            = 'id';
    public const string ITEMS         = 'items';
    public const string CUSTOM_AMOUNT = 'custom_amount';
    public const string REASON        = 'reason';
    public const string REFUNDED_AT   = 'refunded_at';
    public const string UUID          = 'uuid';

    /**
     * @param int|null $id
     * @param Collection<InvoiceItemDTO>|null $items
     * @param float|null $custom_amount
     * @param string|null $reason
     * @param Carbon|null $refunded_at
     * @param string|null $uuid
     */
    public function __construct(
        public ?int $id = null,
        public ?Collection $items = new Collection(),
        public ?float $custom_amount = null,
        public ?string $reason = null,
        public ?Carbon $refunded_at = null,
        public ?string $uuid = null,
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::ID            => $this->id,
            self::ITEMS         => $this->items->toArray(),
            self::CUSTOM_AMOUNT => $this->custom_amount,
            self::REASON        => $this->reason,
            self::REFUNDED_AT   => $this->refunded_at,
            self::UUID          => $this->uuid,
        ];
    }

    /**
     * @param array $array
     * @return InvoiceRefundDataDTO
     */
    public static function fromArray(array $array): self
    {
        return new self(
            id           : Arr::get($array, self::ID),
            items        : Arr::get($array, self::ITEMS),
            custom_amount: Arr::get($array, self::CUSTOM_AMOUNT),
            reason       : Arr::get($array, self::REASON),
            refunded_at  : Carbon::parse(Arr::get($array, self::REFUNDED_AT)),
            uuid         : Arr::get($array, self::UUID),
        );
    }

    /**
     * @return string|null
     */
    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return Collection<InvoiceItemDTO>
     */
    public function getItems(): Collection
    {
        return $this->items;
    }

    /**
     * @return float|null
     */
    public function getCustomAmount(): ?float
    {
        return $this->custom_amount;
    }

    /**
     * @return string|null
     */
    public function getReason(): ?string
    {
        return $this->reason;
    }

    /**
     * @return Carbon|null
     */
    public function getRefundedAt(): ?Carbon
    {
        return $this->refunded_at;
    }
}
