<?php

namespace App\DTO\Billing\PaymentMethods;

use App\DTO\Billing\AddressData;
use App\DTO\DTOContract;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class PaymentMethodDTO implements DTOContract
{
    const string ID               = 'id';
    const string TYPE             = 'type';
    const string BRAND            = 'brand';
    const string LAST4            = 'last4';
    const string EXPIRY_MONTH     = 'expiry_month';
    const string EXPIRY_YEAR      = 'expiry_year'; // 2 digits year
    const string NAME             = 'name';
    const string STATUS           = 'status';
    const string IS_DEFAULT       = 'isDefault';
    const string ADDRESS          = 'address';
    const string EXPIRY_FULL_YEAR = 'full_year'; // 4 digits year

    public function __construct(
        protected ?string      $id = null,
        protected ?string      $type = null,
        protected ?string      $brand = null,
        protected ?string      $last4 = null,
        protected ?string      $expiry_month = null,
        protected ?string      $expiry_year = null,
        protected ?string      $name = null,
        protected ?string      $status = null,
        protected ?bool        $is_default = null,
        protected ?AddressData $address = null,
        protected ?string      $full_year = null
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::ID               => $this->id,
            self::TYPE             => $this->type,
            self::BRAND            => $this->brand,
            self::LAST4            => $this->last4,
            self::EXPIRY_MONTH     => $this->expiry_month,
            self::EXPIRY_YEAR      => $this->expiry_year,
            self::NAME             => $this->name,
            self::STATUS           => $this->status,
            self::IS_DEFAULT       => $this->is_default,
            self::ADDRESS          => $this->address,
            self::EXPIRY_FULL_YEAR => $this->full_year
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::ID),
            Arr::get($array, self::TYPE),
            Arr::get($array, self::BRAND),
            Arr::get($array, self::LAST4),
            Arr::get($array, self::EXPIRY_MONTH),
            Arr::get($array, self::EXPIRY_YEAR),
            Arr::get($array, self::NAME),
            Arr::get($array, self::STATUS),
            Arr::get($array, self::IS_DEFAULT),
            Arr::get($array, self::ADDRESS),
            Arr::get($array, self::EXPIRY_FULL_YEAR),
        );
    }

    public function getExpiry(): string
    {
        return $this->expiry_month . '/' . $this->expiry_year;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getType(): ?string
    {
        return $this->type;
    }

    public function getBrand(): ?string
    {
        return $this->brand;
    }

    public function getLast4(): ?string
    {
        return $this->last4;
    }

    public function getExpiryMonth(): ?string
    {
        return $this->expiry_month;
    }

    public function getExpiryYear(): ?string
    {
        return $this->expiry_year;
    }

    /**
     * get 4 digits expiry year
     *
     * @return string|null
     */
    public function getExpiryFullYear(): ?string
    {
        return $this->full_year;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function getStatus(): ?string
    {
        return $this->status;
    }

    public function getIsDefault(): ?bool
    {
        return $this->is_default;
    }

    public function getAddress(): ?AddressData
    {
        return $this->address;
    }

    /**
     * @return bool
     */
    public function expired(): bool
    {
        if (!$this->getExpiryMonth() || !$this->getExpiryFullYear()) {
            return false;
        }

        return Carbon::create($this->getExpiryFullYear(), $this->getExpiryMonth())->endOfMonth()->isPast();
    }
}
