<?php

namespace App\DTO\Billing\Credit;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class CreditTypePayload implements DTOContract
{
    const string FIELD_NAME              = 'name';
    const string FIELD_SLUG              = 'slug';
    const string FIELD_DESCRIPTION       = 'description';
    const string FIELD_LINE_ITEM_TEXT    = 'line_item_text';
    const string FIELD_EXPIRY            = 'expiry';
    const string FIELD_EXPIRES_IN_DAYS   = 'expires_in_days';
    const string FIELD_CONSUMPTION_ORDER = 'consumption_order';
    const string FIELD_CASH              = 'cash';
    const string FIELD_ACTIVE            = 'active';
    const string FIELD_ID                = 'id';

    public function __construct(
        protected ?string $name = null,
        protected ?string $slug = null,
        protected ?string $description = null,
        protected ?string $lineItemText = null,
        protected ?bool $expiry = null,
        protected ?int $expiresInDays = null,
        protected ?int $consumptionOrder = null,
        protected ?bool $cash = null,
        protected ?bool $active = null,
        protected ?string $id = null,
    )
    {

    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @return string|null
     */
    public function getSlug(): ?string
    {
        return $this->slug;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @return string|null
     */
    public function getLineItemText(): ?string
    {
        return $this->lineItemText;
    }

    /**
     * @return bool|null
     */
    public function getExpiry(): ?bool
    {
        return $this->expiry;
    }

    /**
     * @return int|null
     */
    public function getExpiresInDays(): ?int
    {
        return $this->expiresInDays;
    }

    /**
     * @return int|null
     */
    public function getConsumptionOrder(): ?int
    {
        return $this->consumptionOrder;
    }

    /**
     * @return bool|null
     */
    public function getCash(): ?bool
    {
        return $this->cash;
    }

    /**
     * @return bool|null
     */
    public function getActive(): ?bool
    {
        return $this->active;
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * @param bool|null $expiry
     * @return void
     */
    public function setExpiry(?bool $expiry): void
    {
        $this->expiry = $expiry;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_NAME              => $this->name,
            self::FIELD_SLUG              => $this->slug,
            self::FIELD_DESCRIPTION       => $this->description,
            self::FIELD_LINE_ITEM_TEXT    => $this->lineItemText,
            self::FIELD_EXPIRY            => $this->expiry,
            self::FIELD_EXPIRES_IN_DAYS   => $this->expiresInDays,
            self::FIELD_CONSUMPTION_ORDER => $this->consumptionOrder,
            self::FIELD_CASH              => $this->cash,
            self::FIELD_ACTIVE            => $this->active,
            self::FIELD_ID                => $this->id,
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            name            : Arr::get($array, self::FIELD_NAME),
            slug            : Arr::get($array, self::FIELD_SLUG),
            description     : Arr::get($array, self::FIELD_DESCRIPTION),
            lineItemText    : Arr::get($array, self::FIELD_LINE_ITEM_TEXT),
            expiry          : Arr::get($array, self::FIELD_EXPIRY),
            expiresInDays   : Arr::get($array, self::FIELD_EXPIRES_IN_DAYS),
            consumptionOrder: Arr::get($array, self::FIELD_CONSUMPTION_ORDER),
            cash            : Arr::get($array, self::FIELD_CASH),
            active          : Arr::get($array, self::FIELD_ACTIVE),
            id              : Arr::get($array, self::FIELD_ID),
        );
    }
}
