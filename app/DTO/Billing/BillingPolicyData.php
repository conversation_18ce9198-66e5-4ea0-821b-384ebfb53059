<?php

namespace App\DTO\Billing;

use App\DTO\DTOContract;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use Illuminate\Support\Arr;

class BillingPolicyData implements DTOContract
{
    const string FIELD_ID     = 'id';
    const string FIELD_EVENT  = 'event';
    const string FIELD_ACTION = 'action';
    const string FIELD_ORDER  = 'sort_order';

    public function __construct(
        protected ?int $id = null,
        protected ?string $event = null,
        protected ?string $action = null,
        protected ?int $sortOrder = null,
    )
    {

    }

    public function getEvent(): ?BillingPolicyEventType
    {
        if (isset($this->event)) {
            return BillingPolicyEventType::tryFrom($this->event);
        }

        return null;
    }

    public function getAction(): ?BillingPolicyActionType
    {
        if (isset($this->action)) {
            return BillingPolicyActionType::tryFrom($this->action);
        }

        return null;
    }

    public function getSortOrder(): ?int
    {
        return $this->sortOrder;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_ID     => $this->id,
            self::FIELD_EVENT  => $this->event,
            self::FIELD_ACTION => $this->action,
            self::FIELD_ORDER  => $this->sortOrder,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::FIELD_ID),
            Arr::get($array, self::FIELD_EVENT),
            Arr::get($array, self::FIELD_ACTION),
            Arr::get($array, self::FIELD_ORDER),
        );
    }
}
