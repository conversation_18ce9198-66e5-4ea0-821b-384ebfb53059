<?php

namespace App\DTO\Billing;

use App\DTO\DTOContract;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use Illuminate\Support\Arr;

class InvoiceItemTaxData implements DTOContract
{

    const string INVOICE_ITEM_ID = 'invoiceItemId';
    const string AMOUNT          = 'amount';
    const string TAX             = 'tax';
    const string QUANTITY        = 'quantity';
    const string REFERENCE       = 'reference';

    public function __construct(
        protected ?int    $invoiceItemId = null,
        protected ?float  $amount = null,
        protected ?float  $tax = null,
        protected ?int    $quantity = null,
        protected ?string $reference = null,
    )
    {
    }

    public function getInvoiceItemId(): int
    {
        return $this->invoiceItemId;
    }

    public function getAmount(): ?float
    {
        return $this->amount;
    }

    public function getTax(): ?float
    {
        return $this->tax;
    }

    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function getReference(): ?string
    {
        return $this->reference;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::INVOICE_ITEM_ID => $this->invoiceItemId,
            self::AMOUNT          => $this->amount,
            self::TAX             => $this->tax,
            self::QUANTITY        => $this->quantity,
            self::REFERENCE       => $this->reference,
        ];
    }

    /**
     * Creates new InvoiceItemTaxData object from the given array
     * @param array $array
     * @return InvoiceItemTaxData
     */
    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::INVOICE_ITEM_ID),
            Arr::get($array, self::AMOUNT),
            Arr::get($array, self::TAX),
            Arr::get($array, self::QUANTITY),
            Arr::get($array, self::REFERENCE),
        );
    }
}
