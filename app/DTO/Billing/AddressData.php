<?php

namespace App\DTO\Billing;

use App\DTO\DTOContract;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use Illuminate\Support\Arr;

class AddressData implements DTOContract
{
    const string LINE_ONE  = "line_one";
    const string LINE_TWO  = 'line_two';
    const string CITY      = "city";
    const string STATE     = "state";
    const string POST_CODE = "post_code";
    const string COUNTRY   = "country";

    public function __construct(
        protected ?string $line_one = null,
        protected ?string $city = null,
        protected ?string $state = null,
        protected ?string $post_code = null,
        protected ?string $country = null,
        protected ?string $line_two = null,
    )
    {
    }

    public function toArray(): array
    {
        return [
            self::LINE_ONE  => $this->line_one,
            self::CITY      => $this->city,
            self::STATE     => $this->state,
            self::POST_CODE => $this->post_code,
            self::COUNTRY   => $this->country,
            self::LINE_TWO  => $this->line_two,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::LINE_ONE),
            Arr::get($array, self::CITY),
            Arr::get($array, self::STATE),
            Arr::get($array, self::POST_CODE),
            Arr::get($array, self::COUNTRY),
            Arr::get($array, self::LINE_TWO),
        );
    }

    public function getLineOne(): ?string
    {
        return $this->line_one;
    }

    public function getLineTwo(): ?string
    {
        return $this->line_two;
    }

    public function getCity(): ?string
    {
        return $this->city;
    }

    public function getState(): ?string
    {
        return $this->state;
    }

    public function getPostCode(): ?string
    {
        return $this->post_code;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }
}
