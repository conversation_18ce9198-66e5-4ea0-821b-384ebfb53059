<?php

namespace App\DTO\Billing\MakeChangeRequestInvoice;

use App\DTO\DTOContract;
use App\Models\Billing\CompanyPaymentMethod;
use Illuminate\Support\Arr;

class PaymentMethodAttemptDTO implements DTOContract
{
    const string FIELD_ATTEMPTS                  = 'attempts';
    const string FIELD_COMPANY_PAYMENT_METHOD_ID = 'company_payment_method_id';

    public function __construct(
        protected int $attempts = 0,
        protected ?int $companyPaymentMethodId = null,
        protected ?CompanyPaymentMethod $companyPaymentMethod = null
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_ATTEMPTS                  => $this->attempts,
            self::FIELD_COMPANY_PAYMENT_METHOD_ID => $this->companyPaymentMethodId,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new PaymentMethodAttemptDTO(
            attempts              : Arr::get($array, self::FIELD_ATTEMPTS),
            companyPaymentMethodId: Arr::get($array, self::FIELD_COMPANY_PAYMENT_METHOD_ID),
        );
    }

    /**
     * @return int
     */
    public function getAttempts(): int
    {
        return $this->attempts;
    }

    /**
     * @param int $step
     * @return void
     */
    public function decrementAttempt(int $step = 1): void
    {
        $this->attempts -= $step;
    }

    /**
     * @return int|null
     */
    public function getCompanyPaymentMethodId(): ?int
    {
        return $this->companyPaymentMethodId;
    }

    /**
     * @return CompanyPaymentMethod|null
     */
    public function getCompanyPaymentMethod(): ?CompanyPaymentMethod
    {
        if ($this->companyPaymentMethod?->id === $this->companyPaymentMethodId && !empty($this->companyPaymentMethodId)) {
            $this->companyPaymentMethod = CompanyPaymentMethod::query()->find($this->companyPaymentMethodId);
        }

        return $this->companyPaymentMethod;
    }

    /**
     * @param int|null $companyPaymentMethodId
     * @return void
     */
    public function setCompanyPaymentMethodId(?int $companyPaymentMethodId): void
    {
        $this->companyPaymentMethodId = $companyPaymentMethodId;
    }
}
