<?php

namespace App\DTO\Billing\InvoicePdf;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class TableHeader implements DTOContract
{
    const string FIELD_FIELD = 'field';
    const string FIELD_TITLE = 'title';

    /**
     * @param string|null $field
     * @param string|null $title
     */
    public function __construct(
        protected ?string $field = null,
        protected ?string $title = null,
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_FIELD => $this->field,
            self::FIELD_TITLE => $this->title,
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            field: Arr::get($array, self::FIELD_FIELD),
            title: Arr::get($array, self::FIELD_TITLE),
        );
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function getField(): ?string
    {
        return $this->field;
    }
}
