<?php

namespace App\DTO\Billing\InvoicePdf;

use App\DTO\DTOContract;
use App\Services\CurrencyService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;

class Money implements DTOContract
{
    const string FIELD_AMOUNT   = 'amount';
    const string FIELD_CURRENCY = 'currency';

    public function __construct(
        protected float $amount,
        protected string $currency = 'USD'
    )
    {
    }

    /**
     * @return string
     * @throws BindingResolutionException
     */
    public function formatToCurrency(): string
    {
        /** @var CurrencyService $currencyService */
        $currencyService = app()->make(CurrencyService::class);

        return $currencyService->formatToCurrency($this->amount / 100, $this->currency);
    }

    /**
     * @return string
     */
    public function getAmount(): string
    {
        return $this->amount;
    }

    /**
     * @return array
     * @throws BindingResolutionException
     */
    public function toArray(): array
    {
        return [
            'formatted' => $this->formatToCurrency(),
            'value'     => $this->amount,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            amount  : Arr::get($array, self::FIELD_AMOUNT),
            currency: Arr::get($array, self::FIELD_CURRENCY),
        );
    }
}
