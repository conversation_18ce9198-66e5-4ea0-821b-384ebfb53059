<?php

namespace App\DTO\Billing\InvoicePdf;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class Table implements DTOContract
{
    const string FIELD_TITLE   = 'title';
    const string FIELD_ROWS    = 'rows';
    const string FIELD_HEADERS = 'headers';
    const string FIELD_TOTALS  = 'totals';
    const string FIELD_META    = 'meta';

    public function __construct(
        protected ?string $title = null,
        protected Collection $rows = new Collection(),
        protected Collection $headers = new Collection(),
        protected TableTotals $totals = new TableTotals(),
        protected ?string $meta = null,
    )
    {

    }

    /**
     * @param string|null $title
     * @return void
     */
    public function setTitle(?string $title): void
    {
        $this->title = $title;
    }

    /**
     * @param Collection $rows
     * @return void
     */
    public function setRows(Collection $rows): void
    {
        $this->rows = $rows;
    }

    /**
     * @param Collection $headers
     * @return void
     */
    public function setHeaders(Collection $headers): void
    {
        $this->headers = $headers;
    }

    /**
     * @param TableTotals $totals
     * @return void
     */
    public function setTotals(TableTotals $totals): void
    {
        $this->totals = $totals;
    }

    /**
     * @param Collection $row
     * @return void
     */
    public function addRow(Collection $row): void
    {
        $this->rows->add($row);
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_TITLE   => $this->title,
            self::FIELD_ROWS    => collect($this->rows)->toArray(),
            self::FIELD_HEADERS => collect($this->headers)->toArray(),
            self::FIELD_TOTALS  => collect($this->totals)->toArray(),
            self::FIELD_META    => $this->meta,
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            title  : Arr::get($array, self::FIELD_TITLE),
            rows   : collect(Arr::get($array, self::FIELD_ROWS, [])),
            headers: collect(Arr::get($array, self::FIELD_HEADERS, []))->map(fn($rows) => TableHeader::fromArray($rows)),
            totals : TableTotals::fromArray(Arr::get($array, self::FIELD_TOTALS, [])),
            meta   : Arr::get($array, self::FIELD_META),
        );
    }

    /**
     * @return TableTotals|null
     */
    public function getTotals(): ?TableTotals
    {
        return $this->totals;
    }

    /**
     * @return Collection
     */
    public function getHeaders(): Collection
    {
        return $this->headers;
    }

    /**
     * @return Collection
     */
    public function getRows(): Collection
    {
        return $this->rows;
    }

    /**
     * @return string|null
     */
    public function getTitle(): ?string
    {
        return $this->title;
    }

    /**
     * @return string|null
     */
    public function getMeta(): ?string
    {
        return $this->meta;
    }
}
