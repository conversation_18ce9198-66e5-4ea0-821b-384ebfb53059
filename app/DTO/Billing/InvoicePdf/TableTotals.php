<?php

namespace App\DTO\Billing\InvoicePdf;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class TableTotals implements DTOContract
{
    const string FIELD_TOTAL         = 'total';
    const string FIELD_SUBTOTAL      = 'subtotal';
    const string FIELD_TAX           = 'tax';
    const string FIELD_CUSTOM_TOTALS = 'custom_totals';

    /**
     * @param Money|null $total
     * @param Money|null $subtotal
     * @param Money|null $tax
     * @param Collection<SummaryItem>|null $customTotals
     */
    public function __construct(
        protected ?Money      $total = null,
        protected ?Money      $subtotal = null,
        protected ?Money      $tax = null,
        protected ?Collection $customTotals = new Collection()
    )
    {

    }

    /**
     * @param Money|null $total
     * @return void
     */
    public function setTotal(?Money $total): void
    {
        $this->total = $total;
    }

    /**
     * @param Money|null $subtotal
     * @return void
     */
    public function setSubtotal(?Money $subtotal): void
    {
        $this->subtotal = $subtotal;
    }

    /**
     * @param Money|null $tax
     * @return void
     */
    public function setTax(?Money $tax): void
    {
        $this->tax = $tax;
    }


    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_TOTAL         => $this->total,
            self::FIELD_SUBTOTAL      => $this->subtotal,
            self::FIELD_TAX           => $this->tax,
            self::FIELD_CUSTOM_TOTALS => $this->customTotals->toArray(),
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            total   : Arr::get($array, self::FIELD_TOTAL),
            subtotal: Arr::get($array, self::FIELD_SUBTOTAL),
            tax     : Arr::get($array, self::FIELD_TAX)
        );
    }

    /**
     * @return Money|null
     */
    public function getTotal(): ?Money
    {
        return $this->total;
    }

    /**
     * @return Money|null
     */
    public function getSubtotal(): ?Money
    {
        return $this->subtotal;
    }

    /**
     * @return Money|null
     */
    public function getTax(): ?Money
    {
        return $this->tax;
    }

    /**
     * @return Collection<SummaryItem>|null
     */
    public function getCustomTotals(): ?Collection
    {
        return $this->customTotals;
    }
}
