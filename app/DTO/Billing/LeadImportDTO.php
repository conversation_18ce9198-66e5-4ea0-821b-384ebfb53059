<?php

namespace App\DTO\Billing;

use App\DTO\DTOContract;
use App\Enums\LeadImportStatusEnum;
use Illuminate\Support\Arr;

class LeadImportDTO implements DTOContract
{
    const string CONSUMER_PRODUCT_ID = 'consumer_product_id';
    const string STATUS              = 'status';
    const string PRICE               = 'price';

    public function __construct(
        protected int $consumerProductId,
        protected LeadImportStatusEnum $status,
        protected int $price,
    )
    {

    }

    /**
     * @return int
     */
    public function getConsumerProductId(): int
    {
        return $this->consumerProductId;
    }

    /**
     * @return LeadImportStatusEnum
     */
    public function getStatus(): LeadImportStatusEnum
    {
        return $this->status;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::STATUS              => $this->status->value,
            self::CONSUMER_PRODUCT_ID => $this->consumerProductId
        ];
    }

    /**
     * @param array $array
     * @return LeadImportDTO
     */
    public static function fromArray(array $array): LeadImportDTO
    {
        return new LeadImportDTO(
            consumerProductId: Arr::get($array, self::CONSUMER_PRODUCT_ID),
            status           : LeadImportStatusEnum::tryFrom(
                Arr::get($array, self::STATUS)
            ),
            price            : Arr::get($array, self::PRICE),
        );
    }

    /**
     * @return int
     */
    public function getPrice(): int
    {
        return $this->price;
    }
}
