<?php

namespace App\DTO\Billing;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class InvoiceItemDTO implements DTOContract
{
    const string FIELD_INVOICE_ITEM_ID = 'invoice_item_id';
    const string FIELD_INVOICE_ID      = 'invoice_id';
    const string FIELD_BILLABLE_ID     = 'billable_id';
    const string FIELD_BILLABLE_TYPE   = 'billable_type';
    const string FIELD_UNIT_PRICE      = 'unit_price';
    const string FIELD_QUANTITY        = 'quantity';
    const string FIELD_DESCRIPTION     = 'description';
    const string FIELD_ADDED_BY        = 'added_by';

    public function __construct(
        protected ?int    $invoice_item_id = null,
        protected ?int    $invoice_id = null,
        protected ?int    $billable_id = null,
        protected ?string $billable_type = null,
        protected ?float  $unit_price = null,
        protected ?int    $quantity = null,
        protected ?string $description = null,
        protected ?string $added_by = null,
    )
    {
    }

    /**
     * @return int|null
     */
    public function getInvoiceId(): ?int
    {
        return $this->invoice_id;
    }

    /**
     * @return int|null
     */
    public function getInvoiceItemId(): ?int
    {
        return $this->invoice_item_id;
    }

    /**
     * @return int|null
     */
    public function getBillableId(): ?int
    {
        return $this->billable_id;
    }

    /**
     * @return string|null
     */
    public function getBillableType(): ?string
    {
        return $this->billable_type;
    }

    /**
     * @return float|null
     */
    public function getUnitPrice(): ?float
    {
        return $this->unit_price;
    }

    /**
     * @return int|null
     */
    public function getQuantity(): ?int
    {
        return $this->quantity;
    }

    public function getItemValue(): ?float
    {
        return $this->quantity * $this->unit_price;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @return int|null
     */
    public function getAddedBy(): ?int
    {
        return $this->added_by;
    }

    /**
     * @param string|null $billable_type
     * @return void
     */
    public function setBillableType(?string $billable_type): void
    {
        $this->billable_type = $billable_type;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_INVOICE_ITEM_ID => $this->invoice_item_id,
            self::FIELD_INVOICE_ID      => $this->invoice_id,
            self::FIELD_BILLABLE_ID     => $this->billable_id,
            self::FIELD_BILLABLE_TYPE   => $this->billable_type,
            self::FIELD_UNIT_PRICE      => $this->unit_price,
            self::FIELD_QUANTITY        => $this->quantity,
            self::FIELD_DESCRIPTION     => $this->description,
            self::FIELD_ADDED_BY        => $this->added_by,
        ];
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::FIELD_INVOICE_ITEM_ID),
            Arr::get($array, self::FIELD_INVOICE_ID),
            Arr::get($array, self::FIELD_BILLABLE_ID),
            Arr::get($array, self::FIELD_BILLABLE_TYPE),
            Arr::get($array, self::FIELD_UNIT_PRICE),
            Arr::get($array, self::FIELD_QUANTITY),
            Arr::get($array, self::FIELD_DESCRIPTION),
            Arr::get($array, self::FIELD_ADDED_BY),
        );
    }
}
