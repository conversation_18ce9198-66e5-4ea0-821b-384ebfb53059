<?php

namespace App\DTO\SalesIntel;

use App\Enums\Prospects\ProspectSource;
use App\Models\Odin\IndustryService;
use Illuminate\Contracts\Support\Arrayable;

class ProspectDTO implements Arrayable
{
    public array $source_data;

    public ?string $company_id;

    public ?string $primary_name;

    public ?string $primary_domain;

    public ?string $phone_number;

    public ?string $street;

    public ?string $city;

    public ?string $state;

    public function __construct(array $data)
    {
        $this->source_data = $data;

        $data = fluent($data);

        $location = fluent(collect($data->locations)->last(default: []));
        $phoneNumber = fluent(collect($data->phone_numbers)->first(default: []));

        $this->company_id = $data->company_id;
        $this->primary_name = $data->primary_name;
        $this->primary_domain = $data->primary_domain;
        $this->phone_number = $phoneNumber->value;
        $this->street = $location->street1;
        $this->city = str($location->city)->kebab();
        $this->state = $location->state;

        $this->source_data['zip_codes'][] = str($location->postal_code)->before('-');
    }

    public function toArray()
    {
        if (blank($this->primary_name) || blank($this->primary_domain)) {
            return [];
        }

        return [
            'external_reference' => $this->company_id,
            'user_id' => 0, // Set a blank default since NULL isn't available
            'company_id' => 0, // Set a blank default since NULL isn't available
            'industry_service_ids' => $this->determineIndustryServices(),
            'source' => ProspectSource::SALESINTEL,
            'source_data' => $this->source_data,
            'company_name' => $this->primary_name,
            'company_website' => $this->primary_domain,
            'company_phone' => $this->phone_number,
            'address_street' => $this->street,
            'address_city_key' => $this->city,
            'address_state_abbr' => $this->state,
        ];
    }

    private function determineIndustryServices() {
        $industryServiceIds = collect();

        if (!empty($this->source_data['company'])) {
            $naics = collect(fluent(fluent($this->source_data)->company)->naics_codes);

            $naics->transform(fn($code) => match($code) {
                '238160' => IndustryService::whereSlug('roof-replacement')->first()?->id,
                '238110' => IndustryService::whereSlug('foundation-repair')->first()?->id,
                '238170' => IndustryService::whereSlug('siding-replacement')->first()?->id,
                '238220' => IndustryService::whereSlug('central-air-install-replace')->first()?->id,
                default => null
            });

            $industryServiceIds = $naics->filter()->values();
        }

        return $industryServiceIds;
    }
}
