<?php

namespace App\DTO\SalesIntel;

use App\Models\Odin\CompanyUser;
use Illuminate\Support\Collection;

class PersonDTO
{
    public ?string $first_name;

    public ?string $last_name;

    public ?string $job_department;

    public ?string $job_title;

    public Collection $work_emails;

    public Collection $phone_numbers;

    public function __construct(array $data)
    {
        $data = fluent($data);

        $this->first_name = $data->first_name;
        $this->last_name = $data->last_name;
        $this->job_department = $data->job_department;
        $this->job_title = $data->job_title;
        $this->work_emails = collect($data->work_emails);
        $this->phone_numbers = collect($data->phone_numbers)->map(fn ($phone) => collect($phone));
    }

    public function toArray()
    {
        return [
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->getPrimaryEmail(),
            'department' => $this->job_department,
            'title' => $this->job_title,
            'office_phone' => $this->phone_numbers->firstWhere('type', 'work_hq')?->get('value'),
            'cell_phone' => $this->phone_numbers->firstWhere('type', 'mobile')?->get('value'),
        ];
    }

    public function arrayFor(string $model)
    {
        if (
            blank($this->first_name)
            || blank($this->last_name)
            || $this->work_emails->isEmpty()
            || $this->phone_numbers->isEmpty()
        ) {
            return [];
        }

        $data = $this->toArray();

        if ($model === CompanyUser::class) {
            $data = [
                ...$data,
                'status' => 1,
                'is_contact' => 1,
                'import_source' => CompanyUser::IMPORT_SOURCE_SALES_INTEL,
            ];
        }

        return $data;
    }

    /**
     * @return string|null
     */
    public function getPrimaryEmail(): ?string
    {
        return $this->work_emails->first();
    }
}
