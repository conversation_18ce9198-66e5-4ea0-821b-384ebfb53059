<?php

namespace App\DTO\Prospects;

use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Enums\Prospects\ProspectResolution;
use App\DTO\DTOContract;
use Illuminate\Support\Arr;

/**
 * @property ?string $external_reference
 * @property ProspectStatus $status
 * @property ProspectResolution|null $resolution
 * @property array $industry_service_ids
 * @property ProspectSource $source
 * @property array $source_data
 * @property string $company_name
 * @property string $company_description
 * @property string|null $company_website
 * @property string|null $address_street
 * @property string|null $address_city_key
 * @property string|null $address_state_abbr
 * @property string|null $decision_maker_first_name
 * @property string|null $decision_maker_last_name
 * @property string|null $decision_maker_email
 * @property string|null $notes
 */
class NewBuyerProspectDTO implements DTOContract
{
    public function __construct(
        public ?string $external_reference,
        public ProspectStatus $status,
        public ?ProspectResolution $resolution,
        public array $industry_service_ids,
        public ProspectSource $source,
        public array $source_data,
        public string $company_name,
        public ?string $company_description,
        public ?string $company_website,
        public ?string $address_street,
        public ?string $address_city_key,
        public ?string $address_state_abbr,
        public ?string $decision_maker_first_name,
        public ?string $decision_maker_last_name,
        public ?string $decision_maker_email,
        public ?string $notes
    ) {}

    public function toArray(): array
    {
        return [
            'external_reference' => $this->external_reference,
            'status' => $this->status?->value,
            'resolution' => $this->resolution?->value,
            'industry_service_ids' => $this->industry_service_ids,
            'source' => $this->source?->value,
            'source_data' => $this->source_data,
            'company_name' => $this->company_name,
            'company_description' => $this->company_description,
            'company_website' => $this->company_website,
            'address_street' => $this->address_street,
            'address_city' => $this->address_city_key,
            'address_state_abbr' => $this->address_state_abbr,
            'decision_maker_first_name' => $this->decision_maker_first_name,
            'decision_maker_last_name' => $this->decision_maker_last_name,
            'decision_maker_email' => $this->decision_maker_email,
            'notes' => $this->notes
        ];
    }

    public static function fromArray(array $array): DTOContract
    {
        return new self(
            Arr::get($array, 'external_reference'),
            Arr::get($array, 'status'),
            Arr::get($array, 'resolution'),
            Arr::get($array, 'industry_service_ids'),
            Arr::get($array, 'source'),
            Arr::get($array, 'source_data'),
            Arr::get($array, 'company_name'),
            Arr::get($array, 'company_description'),
            Arr::get($array, 'company_website'),
            Arr::get($array, 'address_street'),
            Arr::get($array, 'address_city'),
            Arr::get($array, 'address_state_abbr'),
            Arr::get($array, 'decision_maker_first_name'),
            Arr::get($array, 'decision_maker_last_name'),
            Arr::get($array, 'decision_maker_email'),
            Arr::get($array, 'notes')
        );
    }
}
