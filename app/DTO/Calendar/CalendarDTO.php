<?php

namespace App\DTO\Calendar;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class CalendarDTO implements DTOContract
{
    const string FIELD_ID               = 'id';
    const string FIELD_SUMMARY          = 'summary';
    const string FIELD_DESCRIPTION      = 'description';
    const string FIELD_TIMEZONE         = 'timeZone';

    public function __construct(
        protected ?string $id = null,
        protected ?string $summary = null,
        protected ?string $description = null,
        protected ?string $timeZone = null,
    )
    {
    }

    /**
     * @return string|null
     */
    public function getTimeZone(): ?string
    {
        return $this->timeZone;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @return string|null
     */
    public function getSummary(): ?string
    {
        return $this->summary;
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_ID               => $this->id,
            self::FIELD_SUMMARY          => $this->summary,
            self::FIELD_DESCRIPTION      => $this->description,
            self::FIELD_TIMEZONE         => $this->timeZone,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            Arr::get($array, self::FIELD_ID),
            Arr::get($array, self::FIELD_SUMMARY),
            Arr::get($array, self::FIELD_DESCRIPTION),
            Arr::get($array, self::FIELD_TIMEZONE),
        );
    }
}
