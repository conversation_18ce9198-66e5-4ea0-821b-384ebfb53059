<?php

namespace App\DTO\Calendar;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CalendarEventDTO implements DTOContract
{
    const string FIELD_ID              = 'id';
    const string FIELD_SUMMARY         = 'summary';
    const string FIELD_DESCRIPTION     = 'description';
    const string FIELD_LOCATION        = 'location';
    const string FIELD_START           = 'start';
    const string FIELD_END             = 'end';
    const string FIELD_STATUS          = 'status';
    const string FIELD_ATTENDEES       = 'attendees';
    const string FIELD_CREATED_AT      = 'created_at';
    const string FIELD_MEETING_URL     = 'meeting_url';
    const string FIELD_RECURRENCE_RULE = 'recurrence_rule';
    const string FIELD_RECURRENCE_DATA = 'recurrence_data';

    public function __construct(
        protected ?string $id = null,
        protected ?string $summary = null,
        protected ?string $description = null,
        protected ?string $location = null,
        protected ?string $start = null,
        protected ?string $end = null,
        protected ?string $status = null,
        protected ?string $timezone = null,
        protected Collection $attendees = new Collection(),
        protected ?string $createdAt = null,
        protected ?string $meetingUrl = null,
        protected ?string $recurrenceRule = null,
        protected ?RecurrenceRuleDTO $recurrenceData = null,

    )
    {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_ID              => $this->id,
            self::FIELD_SUMMARY         => $this->summary,
            self::FIELD_DESCRIPTION     => $this->description,
            self::FIELD_LOCATION        => $this->location,
            self::FIELD_START           => $this->start,
            self::FIELD_END             => $this->end,
            self::FIELD_STATUS          => $this->status,
            self::FIELD_ATTENDEES       => $this->attendees,
            self::FIELD_CREATED_AT      => $this->createdAt,
            self::FIELD_MEETING_URL     => $this->meetingUrl,
            self::FIELD_RECURRENCE_RULE => $this->recurrenceRule,
            self::FIELD_RECURRENCE_DATA => $this->recurrenceData,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            id            : Arr::get($array, self::FIELD_ID),
            summary       : Arr::get($array, self::FIELD_SUMMARY),
            description   : Arr::get($array, self::FIELD_DESCRIPTION),
            location      : Arr::get($array, self::FIELD_LOCATION),
            start         : Arr::get($array, self::FIELD_START),
            end           : Arr::get($array, self::FIELD_END),
            status        : Arr::get($array, self::FIELD_STATUS),
            attendees     : collect(Arr::get($array, self::FIELD_ATTENDEES, []))->map(fn(array $item) => CalendarEventAttendeeDTO::fromArray($item)),
            createdAt     : Arr::get($array, self::FIELD_CREATED_AT),
            meetingUrl    : Arr::get($array, self::FIELD_MEETING_URL),
            recurrenceRule: Arr::get($array, self::FIELD_RECURRENCE_RULE),
            recurrenceData: Arr::get($array, self::FIELD_RECURRENCE_DATA),
        );
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->createdAt;
    }

    /**
     * @return string|null
     */
    public function getStart(): ?string
    {
        return $this->start;
    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getSummary(): ?string
    {
        return $this->summary;
    }

    /**
     * @return string|null
     */
    public function getDescription(): ?string
    {
        return $this->description;
    }

    /**
     * @return string|null
     */
    public function getLocation(): ?string
    {
        return $this->location;
    }

    /**
     * @return string|null
     */
    public function getEnd(): ?string
    {
        return $this->end;
    }

    /**
     * @return string|null
     */
    public function getStatus(): ?string
    {
        return $this->status;
    }

    /**
     * @return Collection<CalendarEventAttendeeDTO>
     */
    public function getAttendees(): Collection
    {
        return $this->attendees;
    }

    /**
     * @return string|null
     */
    public function getMeetingUrl(): ?string
    {
        return $this->meetingUrl;
    }

    /**
     * @return string|null
     */
    public function getRecurrenceRule(): ?string
    {
        return $this->recurrenceRule;
    }

    /**
     * @return ?RecurrenceRuleDTO
     */
    public function getRecurrenceData(): ?RecurrenceRuleDTO
    {
        return $this->recurrenceData;
    }

    /**
     * @return string|null
     */
    public function getTimezone(): ?string
    {
        return $this->timezone;
    }
}
