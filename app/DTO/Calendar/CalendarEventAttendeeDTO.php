<?php

namespace App\DTO\Calendar;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class CalendarEvent<PERSON>ttendeeDTO implements DTOContract
{
    const string FIELD_COMMENT         = 'comment';
    const string FIELD_DISPLAY_NAME    = 'display_name';
    const string FIELD_EMAIL           = 'email';
    const string FIELD_RESPONSE_STATUS = 'response_status';
    const string FIELD_IS_ORGANIZER    = 'is_organizer';

    public function __construct(
        protected ?string $comment = null,
        protected ?string $displayName = null,
        protected ?string $email = null,
        protected ?string $responseStatus = null,
        protected ?bool $isOrganizer = null,
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_COMMENT         => $this->comment,
            self::FIELD_DISPLAY_NAME    => $this->displayName,
            self::FIELD_EMAIL           => $this->email,
            self::FIELD_RESPONSE_STATUS => $this->responseStatus,
            self::FIELD_IS_ORGANIZER    => $this->isOrganizer,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            comment       : Arr::get($array, self::FIELD_COMMENT),
            displayName   : Arr::get($array, self::FIELD_DISPLAY_NAME),
            email         : Arr::get($array, self::FIELD_EMAIL),
            responseStatus: Arr::get($array, self::FIELD_RESPONSE_STATUS),
            isOrganizer   : Arr::get($array, self::FIELD_IS_ORGANIZER),
        );
    }

    /**
     * @return string|null
     */
    public function getComment(): ?string
    {
        return $this->comment;
    }

    /**
     * @return string|null
     */
    public function getDisplayName(): ?string
    {
        return $this->displayName;
    }

    /**
     * @return string|null
     */
    public function getEmail(): ?string
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getResponseStatus(): ?string
    {
        return $this->responseStatus;
    }

    /**
     * @return bool
     */
    public function getIsOrganizer(): bool
    {
        return $this->isOrganizer ?? false;
    }
}
