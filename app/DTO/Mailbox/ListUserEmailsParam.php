<?php

namespace App\DTO\Mailbox;


use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class ListUserEmailsParam implements DTOContract
{
    const FIELD_TAB         = 'tab';
    const FIELD_TEXT_QUERY  = 'text_query';
    const FIELD_UUID        = 'uuid';
    const FIELD_COMPANY_ID  = 'company_id';

    public function __construct(
        protected ?string $tab = null,
        protected ?string $textQuery = null,
        protected ?string $uuid = null,
        protected ?int $companyId = null,
    )
    {

    }

    public function toArray(): array
    {
        return [
            self::FIELD_TAB         => $this->tab,
            self::FIELD_TEXT_QUERY  => $this->textQuery,
            self::FIELD_UUID        => $this->uuid,
            self::FIELD_COMPANY_ID  => $this->companyId,
        ];
    }

    public static function fromArray(array $array): DTOContract
    {
        return new ListUserEmailsParam(
            Arr::get($array, self::FIELD_TAB),
            Arr::get($array, self::FIELD_TEXT_QUERY),
            Arr::get($array, self::FIELD_UUID),
            Arr::get($array, self::FIELD_COMPANY_ID),
        );
    }

    /**
     * @return string|null
     */
    public function getTab(): ?string
    {
        return $this->tab;
    }

    /**
     * @param string|null $tab
     */
    public function setTab(?string $tab): void
    {
        $this->tab = $tab;
    }

    /**
     * @return string|null
     */
    public function getTextQuery(): ?string
    {
        return $this->textQuery;
    }

    /**
     * @param string|null $textQuery
     */
    public function setTextQuery(?string $textQuery): void
    {
        $this->textQuery = $textQuery;
    }

    /**
     * @return string|null
     */
    public function getUuid(): ?string
    {
        return $this->uuid;
    }

    /**
     * @param string|null $uuid
     */
    public function setUuid(?string $uuid): void
    {
        $this->uuid = $uuid;
    }

    /**
     * @return int|null
     */
    public function getCompanyId(): ?int
    {
        return $this->companyId;
    }

    /**
     * @param int|null $companyId
     */
    public function setCompanyId(?int $companyId): void
    {
        $this->companyId = $companyId;
    }
}
