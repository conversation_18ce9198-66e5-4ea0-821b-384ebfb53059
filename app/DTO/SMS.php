<?php

namespace App\DTO;

use Illuminate\Support\Arr;

class SMS implements DTOContract
{
    const string TO_PHONE          = 'to_phone';
    const string FROM              = 'from';
    const string MESSAGE           = 'message';
    const string FROM_PHONE_ID     = 'from_phone_id';
    const string FROM_TYPE         = 'from_type';
    const string FROM_TYPE_PHONE   = 'phone';
    const string FROM_TYPE_SERVICE = 'service';
    const string META              = 'meta';

    public function __construct(
        protected string  $toPhone,
        protected string  $from,
        protected string  $message,
        protected ?int    $fromPhoneId = null,
        protected ?string $fromType = self::FROM_TYPE_PHONE,
        protected ?array  $meta = [],
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::TO_PHONE      => $this->getToPhone(),
            self::FROM          => $this->getFrom(),
            self::MESSAGE       => $this->getMessage(),
            self::FROM_PHONE_ID => $this->getFromPhoneId(),
            self::FROM_TYPE     => $this->getFromType(),
            self::META          => $this->getMeta(),
        ];
    }

    public static function fromArray(array $array): SMS
    {
        return new self(
            toPhone: Arr::get($array, self::TO_PHONE),
            from: Arr::get($array, self::FROM),
            message: Arr::get($array, self::MESSAGE),
            fromPhoneId: Arr::get($array, self::FROM_PHONE_ID),
            fromType: Arr::get($array, self::FROM_TYPE),
            meta: Arr::get($array, self::META),
        );
    }

    public function getToPhone(): string
    {
        return $this->toPhone;
    }

    public function getFrom(): string
    {
        return $this->from;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getFromPhoneId(): ?int
    {
        return $this->fromPhoneId;
    }

    public function getFromType(): string
    {
        return $this->fromType;
    }

    public function getMeta(): array
    {
        return $this->meta;
    }
}
