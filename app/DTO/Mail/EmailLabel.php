<?php

namespace App\DTO\Mail;


use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use JetBrains\PhpStorm\ArrayShape;

class EmailLabel implements DTOContract
{
    const FIELD_ID      = 'id';
    const FIELD_NAME    = 'name';

    public function __construct(
        protected ?string $id = null,
        protected ?string $name = null,
    )
    {

    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * @param string|null $id
     */
    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getName(): ?string
    {
        return $this->name;
    }

    /**
     * @param string|null $name
     */
    public function setName(?string $name): void
    {
        $this->name = $name;
    }

    /**
     * @return array
     */
    #[ArrayShape([self::FIELD_ID => "null|string", self::FIELD_NAME => "null|string"])]
    public function toArray(): array
    {
        return [
            self::FIELD_ID      => $this->id,
            self::FIELD_NAME    => $this->name,
        ];
    }

    /**
     * @param array $array
     * @return EmailLabel
     */
    public static function fromArray(array $array): EmailLabel
    {
        return new EmailLabel(
            Arr::get($array, self::FIELD_ID),
            Arr::get($array, self::FIELD_NAME)
        );
    }
}
