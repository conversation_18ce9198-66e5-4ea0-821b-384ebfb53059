<?php

namespace App\DTO\Mail;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;
use JetBrains\PhpStorm\ArrayShape;

class EmailFlags implements DTOContract
{
    const FIELD_IS_INBOX     = 'is_inbox';
    const FIELD_IS_STARRED   = 'is_starred';
    const FIELD_IS_IMPORTANT = 'is_important';
    const FIELD_IS_SENT      = 'is_sent';
    const FIELD_IS_DRAFT     = 'is_draft';
    const FIELD_IS_SPAM      = 'is_spam';
    const FIELD_IS_TRASH     = 'is_trash';
    const FIELD_IS_ARCHIVED  = 'is_archived';
    const FIELD_IS_READ      = 'is_read';

    public function __construct(
        protected ?bool $isInbox        = false,
        protected ?bool $isStarred      = false,
        protected ?bool $isImportant    = false,
        protected ?bool $isSent         = false,
        protected ?bool $isDraft        = false,
        protected ?bool $isSpam         = false,
        protected ?bool $isTrash        = false,
        protected ?bool $isArchived     = false,
        protected ?bool $isRead         = false,
    )
    {

    }

    /**
     * @return bool|null
     */
    public function getIsInbox(): ?bool
    {
        return $this->isInbox;
    }

    /**
     * @param bool|null $isInbox
     */
    public function setIsInbox(?bool $isInbox): void
    {
        $this->isInbox = $isInbox;
    }

    /**
     * @return bool|null
     */
    public function getIsStarred(): ?bool
    {
        return $this->isStarred;
    }

    /**
     * @param bool|null $isStarred
     */
    public function setIsStarred(?bool $isStarred): void
    {
        $this->isStarred = $isStarred;
    }

    /**
     * @return bool|null
     */
    public function getIsImportant(): ?bool
    {
        return $this->isImportant;
    }

    /**
     * @param bool|null $isImportant
     */
    public function setIsImportant(?bool $isImportant): void
    {
        $this->isImportant = $isImportant;
    }

    /**
     * @return bool|null
     */
    public function getIsSent(): ?bool
    {
        return $this->isSent;
    }

    /**
     * @param bool|null $isSent
     */
    public function setIsSent(?bool $isSent): void
    {
        $this->isSent = $isSent;
    }

    /**
     * @return bool|null
     */
    public function getIsDraft(): ?bool
    {
        return $this->isDraft;
    }

    /**
     * @param bool|null $isDraft
     */
    public function setIsDraft(?bool $isDraft): void
    {
        $this->isDraft = $isDraft;
    }

    /**
     * @return bool|null
     */
    public function getIsSpam(): ?bool
    {
        return $this->isSpam;
    }

    /**
     * @param bool|null $isSpam
     */
    public function setIsSpam(?bool $isSpam): void
    {
        $this->isSpam = $isSpam;
    }

    /**
     * @return bool|null
     */
    public function getIsTrash(): ?bool
    {
        return $this->isTrash;
    }

    /**
     * @param bool|null $isTrash
     */
    public function setIsTrash(?bool $isTrash): void
    {
        $this->isTrash = $isTrash;
    }

    /**
     * @return bool|null
     */
    public function getIsArchived(): ?bool
    {
        return $this->isArchived;
    }

    /**
     * @param bool|null $isArchived
     */
    public function setIsArchived(?bool $isArchived): void
    {
        $this->isArchived = $isArchived;
    }


    /**
     * @return bool|null
     */
    public function getIsRead(): ?bool
    {
        return $this->isRead;
    }

    /**
     * @param bool|null $isRead
     */
    public function setIsRead(?bool $isRead): void
    {
        $this->isRead = $isRead;
    }

    /**
     * @return array
     */
    #[ArrayShape([
        self::FIELD_IS_INBOX        => "bool|null",
        self::FIELD_IS_STARRED      => "bool|null",
        self::FIELD_IS_IMPORTANT    => "bool|null",
        self::FIELD_IS_SENT         => "bool|null",
        self::FIELD_IS_DRAFT        => "bool|null",
        self::FIELD_IS_SPAM         => "bool|null",
        self::FIELD_IS_TRASH        => "bool|null",
        self::FIELD_IS_ARCHIVED     => "bool|null",
        self::FIELD_IS_READ         => "bool|null",
    ])]
    public function toArray(): array
    {
        return [
            self::FIELD_IS_INBOX     => $this->isInbox,
            self::FIELD_IS_STARRED   => $this->isStarred,
            self::FIELD_IS_IMPORTANT => $this->isImportant,
            self::FIELD_IS_SENT      => $this->isSent,
            self::FIELD_IS_DRAFT     => $this->isDraft,
            self::FIELD_IS_SPAM      => $this->isSpam,
            self::FIELD_IS_TRASH     => $this->isTrash,
            self::FIELD_IS_ARCHIVED  => $this->isArchived,
            self::FIELD_IS_READ      => $this->isRead,
        ];
    }

    /**
     * @param array $array
     * @return EmailFlags
     */
    public static function fromArray(array $array): EmailFlags
    {
        return new EmailFlags(
            Arr::get($array, self::FIELD_IS_INBOX),
            Arr::get($array, self::FIELD_IS_STARRED),
            Arr::get($array, self::FIELD_IS_IMPORTANT),
            Arr::get($array, self::FIELD_IS_SENT),
            Arr::get($array, self::FIELD_IS_DRAFT),
            Arr::get($array, self::FIELD_IS_SPAM),
            Arr::get($array, self::FIELD_IS_TRASH),
            Arr::get($array, self::FIELD_IS_ARCHIVED),
            Arr::get($array, self::FIELD_IS_READ),
        );
    }

}
