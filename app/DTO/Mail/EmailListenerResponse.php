<?php

namespace App\DTO\Mail;

use App\DTO\DTOContract;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class EmailListenerResponse implements DTOContract
{
    const FIELD_EXPIRES_AT = 'expires_at';


    public function __construct(
        protected ?Carbon $expiresAt = null,
    )
    {

    }

    /**
     * @return Carbon|null
     */
    public function getExpiresAt(): ?Carbon
    {
        return $this->expiresAt;
    }

    /**
     * @param Carbon|null $expiresAt
     */
    public function setExpiresAt(?Carbon $expiresAt): void
    {
        $this->expiresAt = $expiresAt;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_EXPIRES_AT => $this->expiresAt
        ];
    }

    public static function fromArray(array $array): EmailListenerResponse
    {
        return new EmailListenerResponse(
            Arr::get($array, self::FIELD_EXPIRES_AT),
        );
    }
}
