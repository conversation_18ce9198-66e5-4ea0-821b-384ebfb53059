<?php

namespace App\DTO\Mail;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class Email implements DTOContract
{
    const FIELD_ID          = 'id';
    const FIELD_SNIPPET     = 'snippet';
    const FIELD_THREAD_ID   = 'thread_id';
    const FIELD_HISTORY_ID  = 'history_id';
    const FIELD_META        = 'meta';
    const FIELD_ATTACHMENTS = 'attachments';
    const FIELD_LABEL_IDS   = 'label_ids';
    const FIELD_CONTENT     = 'content';
    const FIELD_FLAGS       = 'flags';

    public function __construct(
        protected ?string       $id = null,
        protected ?string       $snippet = null,
        protected ?string       $threadId = null,
        protected ?string       $historyId = null,
        protected ?EmailMeta    $meta = new EmailMeta(),
        protected ?array        $attachments = null,
        protected ?array        $labelIds = null,
        protected ?string       $content = null,
        protected ?EmailFlags   $flags = new EmailFlags(),
    )
    {

    }

    /**
     * @return string|null
     */
    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * @param string|null $id
     */
    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getSnippet(): ?string
    {
        return $this->snippet;
    }

    /**
     * @param string|null $snippet
     */
    public function setSnippet(?string $snippet): void
    {
        $this->snippet = $snippet;
    }

    /**
     * @return string|null
     */
    public function getThreadId(): ?string
    {
        return $this->threadId;
    }

    /**
     * @param string|null $threadId
     */
    public function setThreadId(?string $threadId): void
    {
        $this->threadId = $threadId;
    }

    /**
     * @return string|null
     */
    public function getHistoryId(): ?string
    {
        return $this->historyId;
    }

    /**
     * @param string|null $historyId
     */
    public function setHistoryId(?string $historyId): void
    {
        $this->historyId = $historyId;
    }

    /**
     * @return EmailMeta|null
     */
    public function getMeta(): ?EmailMeta
    {
        return $this->meta;
    }

    /**
     * @param EmailMeta|null $meta
     */
    public function setMeta(?EmailMeta $meta): void
    {
        $this->meta = $meta;
    }

    /**
     * @return array|null
     */
    public function getAttachments(): ?array
    {
        return $this->attachments;
    }

    /**
     * @param array|null $attachments
     */
    public function setAttachments(?array $attachments): void
    {
        $this->attachments = $attachments;
    }

    /**
     * @return array|null
     */
    public function getLabelIds(): ?array
    {
        return $this->labelIds;
    }

    /**
     * @param array|null $labelIds
     */
    public function setLabelIds(?array $labelIds): void
    {
        $this->labelIds = $labelIds;
    }

    /**
     * @return string|null
     */
    public function getContent(): ?string
    {
        return $this->content;
    }

    /**
     * @param string|null $content
     */
    public function setContent(?string $content): void
    {
        $this->content = $content;
    }


    /**
     * @return EmailFlags|null
     */
    public function getFlags(): ?EmailFlags
    {
        return $this->flags;
    }

    /**
     * @param EmailFlags|null $flags
     */
    public function setFlags(?EmailFlags $flags): void
    {
        $this->flags = $flags;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_ID              => $this->id,
            self::FIELD_SNIPPET         => $this->snippet,
            self::FIELD_THREAD_ID       => $this->threadId,
            self::FIELD_HISTORY_ID      => $this->historyId,
            self::FIELD_META            => $this->meta->toArray(),
            self::FIELD_ATTACHMENTS     => collect($this->attachments)->toArray(),
            self::FIELD_LABEL_IDS       => $this->labelIds,
            self::FIELD_CONTENT         => $this->content,
            self::FIELD_FLAGS           => $this->flags->toArray(),
        ];
    }

    public static function fromArray(array $array): Email
    {
        $attachments = collect(Arr::get($array, self::FIELD_ATTACHMENTS))
            ->map(fn(array $data) => new EmailPart(...$data))->all();

        return new Email(
            id         : Arr::get($array, self::FIELD_ID),
            snippet    : Arr::get($array, self::FIELD_SNIPPET),
            threadId   : Arr::get($array, self::FIELD_THREAD_ID),
            historyId  : Arr::get($array, self::FIELD_HISTORY_ID),
            meta       : EmailMeta::fromArray(Arr::get($array, self::FIELD_META)),
            attachments: $attachments,
            labelIds   : Arr::get($array, self::FIELD_LABEL_IDS),
            content    : Arr::get($array, self::FIELD_CONTENT),
            flags      : EmailFlags::fromArray(Arr::get($array, self::FIELD_FLAGS)),
        );
    }

}
