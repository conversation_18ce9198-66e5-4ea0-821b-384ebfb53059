<?php

namespace App\DTO\Mail;


use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class EmailPart implements DTOContract
{
    const FIELD_ID              = 'id';
    const FIELD_MIMETYPE        = 'mimeType';
    const FIELD_CONTENT         = 'content';
    const FIELD_FILENAME        = 'fileName';
    const FIELD_ATTACHMENT_ID   = 'attachmentId';
    const FIELD_SIZE            = 'size';
    const FIELD_CONTENT_ID      = 'contentId';
    const FIELD_URL             = 'url';


    public function __construct(
        protected ?string $id = null,
        protected ?string $mimeType = null,
        protected ?string $content = null,
        protected ?string $fileName = null,
        protected ?string $attachmentId = null,
        protected ?int    $size = null,
        protected ?string $contentId = null,
        protected ?string $url = null,
    )
    {

    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function setId(?string $id): void
    {
        $this->id = $id;
    }

    public function getAttachmentId(): ?string
    {
        return $this->attachmentId;
    }

    public function setAttachmentId(?string $attachmentId): void
    {
        $this->attachmentId = $attachmentId;
    }

    public function getFileName(): ?string
    {
        return $this->fileName;
    }

    public function setFileName(?string $fileName): void
    {
        $this->fileName = $fileName;
    }

    public function getMimeType(): ?string
    {
        return $this->mimeType;
    }

    public function setMimeType(?string $mimeType): void
    {
        $this->mimeType = $mimeType;
    }

    public function getContent(): ?string
    {
        return $this->content;
    }

    public function setContent(?string $content): void
    {
        $this->content = $content;
    }

    public function getSize(): ?int
    {
        return $this->size;
    }

    public function setSize(?int $size): void
    {
        $this->size = $size;
    }

    /**
     * @return string|null
     */
    public function getContentId(): ?string
    {
        return $this->contentId;
    }

    /**
     * @param string|null $contentId
     */
    public function setContentId(?string $contentId): void
    {
        $this->contentId = $contentId;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_ID              => $this->id,
            self::FIELD_MIMETYPE        => $this->mimeType,
            self::FIELD_CONTENT         => $this->content,
            self::FIELD_FILENAME        => $this->fileName,
            self::FIELD_ATTACHMENT_ID   => $this->attachmentId,
            self::FIELD_SIZE            => $this->size,
            self::FIELD_CONTENT_ID      => $this->contentId,
            self::FIELD_URL             => $this->url,
        ];
    }

    public static function fromArray(array $array): EmailPart
    {
        return new EmailPart(
            Arr::get($array, self::FIELD_ID),
            Arr::get($array, self::FIELD_MIMETYPE),
            Arr::get($array, self::FIELD_CONTENT),
            Arr::get($array, self::FIELD_FILENAME),
            Arr::get($array, self::FIELD_ATTACHMENT_ID),
            Arr::get($array, self::FIELD_SIZE),
            Arr::get($array, self::FIELD_CONTENT_ID),
            Arr::get($array, self::FIELD_URL),
        );
    }

    /**
     * @return string|null
     */
    public function getUrl(): ?string
    {
        return $this->url;
    }

    /**
     * @param string|null $url
     */
    public function setUrl(?string $url): void
    {
        $this->url = $url;
    }
}
