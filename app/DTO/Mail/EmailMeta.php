<?php

namespace App\DTO\Mail;

use App\DTO\DTOContract;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class EmailMeta implements DTOContract
{
    const string FIELD_FROM       = 'from';
    const string FIELD_DATE       = 'date';
    const string FIELD_TO         = 'to';
    const string FIELD_SUBJECT    = 'subject';
    const string FIELD_BCC        = 'bcc';
    const string FIELD_CC         = 'cc';
    const string FIELD_MESSAGE_ID = 'message_id';
    const string FIELD_REFERENCES = 'references';


    public function __construct(
        protected ?string $from = null,
        protected ?Carbon $date = null,
        protected ?array $to = [],
        protected ?string $subject = null,
        protected ?array $bcc = [],
        protected ?array $cc = [],
        protected ?string $messageId = null,
        protected ?string $references = null
    )
    {

    }

    public function toArray(): array
    {
        return [
            self::FIELD_FROM       => $this->from,
            self::FIELD_DATE       => $this->date,
            self::FIELD_TO         => $this->to,
            self::FIELD_SUBJECT    => $this->subject,
            self::FIELD_BCC        => $this->bcc,
            self::FIELD_CC         => $this->cc,
            self::FIELD_MESSAGE_ID => $this->messageId,
            self::FIELD_REFERENCES => $this->references,
        ];
    }

    public static function fromArray(array $array): EmailMeta
    {
        return new EmailMeta(
            Arr::get($array, self::FIELD_FROM),
            Arr::get($array, self::FIELD_DATE),
            Arr::get($array, self::FIELD_TO),
            Arr::get($array, self::FIELD_SUBJECT),
            Arr::get($array, self::FIELD_BCC),
            Arr::get($array, self::FIELD_CC),
            Arr::get($array, self::FIELD_MESSAGE_ID),
            Arr::get($array, self::FIELD_REFERENCES),
        );
    }

    public function getFrom(): ?string
    {
        return $this->from;
    }

    public function setFrom(string $from): void
    {
        $this->from = $this->removeNameFromEmail($from);
    }

    public function getDate(): ?string
    {
        return $this->date;
    }

    public function getUtcDate(): ?string
    {
        return Carbon::parse($this->date)->utc();
    }

    public function setDate(string|Carbon $date): void
    {
        if (is_string($date)) {
            // Remove the timezone information
            $date = preg_replace('/\s+\(.+\)$/', '', $date);

            $date = Carbon::parse($date);
        }

        $this->date = $date;
    }

    public function getTo(): ?array
    {
        return $this->to;
    }

    public function setTo(string|array $to): void
    {
        if (is_string($to))
            $to = $this->extractEmailsFromString($to);

        $this->to = $to;
    }

    public function getSubject(): ?string
    {
        return $this->subject;
    }

    public function setSubject(string $subject): void
    {
        $this->subject = $subject;
    }

    public function getBcc(): ?array
    {
        return $this->bcc;
    }

    public function setBcc(string|array $bcc): void
    {
        if (is_string($bcc))
            $bcc = $this->extractEmailsFromString($bcc);

        $this->bcc = $bcc;
    }

    public function getCc(): ?array
    {
        return $this->cc;
    }

    public function setCc(string|array $cc): void
    {
        if (is_string($cc))
            $cc = $this->extractEmailsFromString($cc);

        $this->cc = $cc;
    }

    private function removeNameFromEmail(string $email): string
    {
        $pattern = '/<[^\s<>]+@[^\s<>]+>/';

        if (preg_match($pattern, $email, $matches))
            return trim($matches[0], '<>');

        return trim($email);
    }

    private function extractEmailsFromString(string $str): array
    {
        return array_map(function ($email) {
            return $this->removeNameFromEmail($email);
        }, explode(',', $str));
    }

    /**
     * @return string|null
     */
    public function getMessageId(): ?string
    {
        return $this->messageId;
    }

    /**
     * @param string|null $messageId
     */
    public function setMessageId(?string $messageId): void
    {
        $this->messageId = $messageId;
    }

    /**
     * @return string|null
     */
    public function getReferences(): ?string
    {
        return $this->references;
    }

    /**
     * @param string|null $references
     */
    public function setReferences(?string $references): void
    {
        $this->references = $references;
    }
}
