<?php

namespace App\DTO\Mail;


use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class AccessTokenResponse implements DTOContract
{
    const string FIELD_ACCESS_TOKEN  = 'access_token';
    const string FIELD_REFRESH_TOKEN = 'refresh_token';

    public function __construct(
        protected ?string $accessToken = null,
        protected ?string $refreshToken = null,
    )
    {

    }

    /**
     * @return string|null
     */
    public function getAccessToken(): ?string
    {
        return $this->accessToken;
    }

    /**
     * @param string|null $accessToken
     */
    public function setAccessToken(?string $accessToken): void
    {
        $this->accessToken = $accessToken;
    }

    /**
     * @return string|null
     */
    public function getRefreshToken(): ?string
    {
        return $this->refreshToken;
    }

    /**
     * @param string|null $refreshToken
     */
    public function setRefreshToken(?string $refreshToken): void
    {
        $this->refreshToken = $refreshToken;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_ACCESS_TOKEN  => $this->accessToken,
            self::FIELD_REFRESH_TOKEN => $this->refreshToken,
        ];
    }

    public static function fromArray(array $array): DTOContract
    {
        return new AccessTokenResponse(
            Arr::get($array, self::FIELD_ACCESS_TOKEN),
            Arr::get($array, self::FIELD_REFRESH_TOKEN)
        );
    }
}
