<?php

namespace App\DTO\Mail;

use App\DTO\DTOContract;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class ListEmailQueryDTO implements DTOContract
{
    const string FIELD_RAW_QUERY   = 'raw_query';
    const string FIELD_AFTER_DATE  = 'after_date';
    const string FIELD_BEFORE_DATE = 'before_date';

    public function __construct(
        public ?string $rawQuery = null,
        public Carbon|string|null $afterDate = null,
        public Carbon|string|null $beforeDate = null
    )
    {
        if (filled($afterDate)) {
            $this->afterDate = Carbon::parse($afterDate);
        }

        if (filled($beforeDate)) {
            $this->beforeDate = Carbon::parse($beforeDate);
        }
    }

    /**
     * Generate the Gmail query string.
     */
    public function toQueryString(): string
    {
        $queryParts = collect();

        if (filled($this->rawQuery)) {
            $queryParts->push($this->rawQuery);
        }
        if (filled($this->afterDate)) {
            $queryParts->push("after:{$this->afterDate->unix()}");
        }
        if (filled($this->beforeDate)) {
            $queryParts->push("before:{$this->beforeDate->unix()}");
        }

        if ($queryParts->isEmpty()) {
            return '';
        }

        $str = $queryParts->implode(' AND ');

        return "($str)";
    }

    /**
     * @return string|null
     */
    public function getRawQuery(): ?string
    {
        return $this->rawQuery;
    }

    /**
     * @return Carbon|string|null
     */
    public function getAfterDate(): Carbon|string|null
    {
        return $this->afterDate;
    }

    /**
     * @return Carbon|string|null
     */
    public function getBeforeDate(): Carbon|string|null
    {
        return $this->beforeDate;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_RAW_QUERY   => $this->rawQuery,
            self::FIELD_AFTER_DATE  => $this->afterDate,
            self::FIELD_BEFORE_DATE => $this->beforeDate,
        ];
    }

    /**
     * @param array $array
     * @return ListEmailQueryDTO
     */
    public static function fromArray(array $array): ListEmailQueryDTO
    {
        return new self(
            rawQuery  : Arr::get($array, self::FIELD_RAW_QUERY),
            afterDate : Arr::get($array, self::FIELD_AFTER_DATE),
            beforeDate: Arr::get($array, self::FIELD_BEFORE_DATE),
        );
    }
}
