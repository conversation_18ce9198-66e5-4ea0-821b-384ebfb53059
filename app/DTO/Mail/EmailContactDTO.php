<?php

namespace App\DTO\Mail;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class EmailContactDTO implements DTOContract
{
    const string FIELD_EMAIL = 'email';

    public function __construct(
        protected string $email,
    )
    {

    }

    /**
     * @return string
     */
    public function __toString(): string
    {
        return json_encode($this->toArray());
    }

    /**
     * @return string
     */
    public function getEmail(): string
    {
        return $this->email;
    }

    /**
     * @return string[]
     */
    public function toArray(): array
    {
        return [
            self::FIELD_EMAIL => $this->email,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            email: Arr::get($array, self::FIELD_EMAIL),
        );
    }
}
