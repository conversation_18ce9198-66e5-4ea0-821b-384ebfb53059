<?php

namespace App\DTO\Affiliate;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class PayoutStrategyValueBoundaries implements DTOContract
{
    const string MAX = 'max';
    const string MIN = 'min';
    public function __construct(
        protected int $max,
        protected int $min,
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::MAX => $this->max,
            self::MIN => $this->min,
        ];
    }

    public static function fromArray(array $array): PayoutStrategyValueBoundaries
    {
        return new self(
            max: Arr::get($array, self::MAX),
            min: Arr::get($array, self::MIN),
        );
    }

    /**
     * @return int
     */
    public function getMax(): int
    {
        return $this->max;
    }
    public function getMin(): int
    {
        return $this->min;
    }
}
