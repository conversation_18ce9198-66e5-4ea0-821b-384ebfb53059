<?php

namespace App\ConsumerProcessing\Events;

use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\DataModels\Campaigns\ConsumerProject;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AssignmentDeliveryFailureEvent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(
        public ConsumerProject $consumerProject,
        public PostAllocationData $postAllocationData,
    ) {}
}
