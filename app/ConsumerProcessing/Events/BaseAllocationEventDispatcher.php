<?php

namespace App\ConsumerProcessing\Events;

use App\Enums\EventCategory;
use App\Enums\EventName;
use Illuminate\Contracts\Queue\ShouldQueue;

abstract class BaseAllocationEventDispatcher
{
    /**
     * @param EventCategory $category
     * @param EventName $name
     * @param array $payload
     * @return void
     */
    public abstract function dispatchEvent(EventCategory $category, EventName $name, array $payload): void;

    /**
     * @param EventCategory $category
     * @param EventName $name
     * @param array $payload
     * @param string $eventKey
     * @param int|null $uniqueForSeconds
     * @return void
     */
    public abstract function dispatchUniqueEvent(EventCategory $category, EventName $name, array $payload, string $eventKey, ?int $uniqueForSeconds = null): void;
}
