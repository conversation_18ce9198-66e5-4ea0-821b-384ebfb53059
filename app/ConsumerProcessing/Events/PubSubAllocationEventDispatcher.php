<?php

namespace App\ConsumerProcessing\Events;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use Illuminate\Support\Facades\Cache;

class PubSubAllocationEventDispatcher extends BaseAllocationEventDispatcher
{
    /**
     * @inheritDoc
     */
    public function dispatchEvent(EventCategory $category, EventName $name, array $payload): void
    {
        dispatch($this->buildPayload($category, $name, $payload));
    }

    /**
     * @inheritDoc
     */
    public function dispatchUniqueEvent(EventCategory $category, EventName $name, array $payload, string $eventKey, ?int $uniqueForSeconds = null): void
    {
        $event = $this->buildPayload($category, $name, $payload);
        if ($uniqueForSeconds > 0) {
            Cache::remember($eventKey, $uniqueForSeconds, function () use ($event) {
                dispatch($event);
            });
        }
        else {
            Cache::rememberForever($eventKey, function () use ($event) {
                dispatch($event);
            });
        }

    }

    /**
     * @param EventCategory $category
     * @param EventName $name
     * @param array $payload
     * @return DispatchPubSubEvent
     */
    private function buildPayload(EventCategory $category, EventName $name, array $payload): DispatchPubSubEvent
    {
        return new DispatchPubSubEvent($category, $name, $payload);
    }
}
