<?php

namespace App\ConsumerProcessing\Repositories;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\DataModels\Campaigns\ConsumerProject;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\Product;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ProductRepository;
use App\Services\DatabaseHelperService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

class AvailableCompanyCampaignRepository
{
    public function __construct(
        protected CampaignMediator $mediator,
        protected ProductRepository $productRepository,
        protected LocationRepository $locationRepository
    ) {}

    /**
     * Returns the available campaigns for a given project.
     *
     * @param ConsumerProject $project
     * @return Collection
     * @throws BindingResolutionException
     */
    public function getAvailableCampaignsForProject(ConsumerProject $project): Collection
    {
        $excludeCompanies = $this->getCompaniesToExclude($project);
        $primaryService = $this->getServiceIDOfProject($project);
        $secondaryServices = $project->secondaryIndustryServices();

        $campaignsQuery = CompanyCampaign::query()
            ->with([CompanyCampaign::RELATION_CAMPAIGN_PROPERTY_TYPES])
            ->when($secondaryServices, fn(Builder $query) => $query->whereIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID, [...$secondaryServices, $primaryService]))
            ->when(!$secondaryServices, fn(Builder $query) => $query->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID, $primaryService))
            ->where(CompanyCampaign::TABLE . '.' .CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            ->whereHas(
                CompanyCampaign::RELATION_COMPANY,
                fn(Builder $query) => $query->where(Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
            )
            ->when(!empty($excludeCompanies), fn(Builder $query) => $query->whereNotIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID, $excludeCompanies));

        if ($project->getUnderReview()) {
            $campaignsQuery
                ->select([
                    CompanyCampaign::TABLE . '.*'
                ])
                ->distinct()
                ->leftJoin(
                    CompanyConfiguration::TABLE,
                    CompanyConfiguration::TABLE . '.' . CompanyConfiguration::FIELD_COMPANY_ID,
                    CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID,
                )
                ->where(CompanyConfiguration::TABLE . '.' . CompanyConfiguration::FIELD_ACCEPT_UNDER_REVIEW_LEADS, true);
        }

        $campaigns = $campaignsQuery->get();

        $campaigns = $this->mediator->preFilter($campaigns, $project);

        return $this->mediator->filter($campaigns, $project);
    }

    /**
     * @param IndustryService $service
     * @param string $zipCode
     * @param Product $product
     * @param int[] $excludedCompanies
     *
     * @return Collection<CompanyCampaign>
     */
    public function getAvailableCampaignsForServiceProductAndZipCode(
        IndustryService $service,
        string $zipCode,
        Product $product = Product::LEAD,
        array $excludedCompanies = []
    ): Collection
    {
        $zipCodeLocation = $this->locationRepository->getZipCode($zipCode);
        $countyLocation = $this->locationRepository->getCountyFromZipcode($zipCode);
        $product = $this->productRepository->getProductByName($product->value);

        if (!$zipCodeLocation) {
            return collect();
        }

        return CompanyCampaign::query()
            ->select(CompanyCampaign::TABLE . '.*')
            ->join(
                Company::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID
            )
            ->join(
                CompanyCampaignLocationModule::TABLE,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID,
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID
            )
            ->join(
                CompanyCampaignLocationModuleLocation::TABLE,
                CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID,
                CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_ID
            )
            ->join(Location::TABLE,
                Location::TABLE .'.'. Location::ID, '=',
                CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->join(Location::TABLE .' as county_locations', fn(JoinClause $join) =>
                $join->on('county_locations.' . Location::COUNTY_KEY, '=', Location::TABLE .'.'. Location::COUNTY_KEY)
                    ->on('county_locations.' . Location::STATE_KEY, '=', Location::TABLE .'.'. Location::STATE_KEY)
                ->where('county_locations.' . Location::TYPE, Location::TYPE_COUNTY)
            )->leftJoin(CompanyConfiguration::TABLE,
                CompanyConfiguration::TABLE .'.'. CompanyConfiguration::FIELD_COMPANY_ID, '=',
                Company::TABLE .'.'. Company::FIELD_ID)
            ->when(!empty($excludedCompanies), fn(Builder $query) => $query->whereNotIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID, $excludedCompanies))
            ->whereNull(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_DELETED_AT)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID, $service->id)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_PRODUCT_ID, $product->id)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_STATUS, CampaignStatus::ACTIVE)
            ->where(Company::TABLE . '.' . Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
            ->where(fn(Builder $query) => $this->filterCampaignsByZipCodeOrCounty($query, $zipCodeLocation->id, $countyLocation->id))
            ->get()
            ->unique(CompanyCampaign::FIELD_ID);
    }

    /**
     * @param Builder $query
     * @param int $zipCodeLocationId
     * @param int $countyLocationId
     * @return Builder
     */
    protected function filterCampaignsByZipCodeOrCounty(Builder &$query, int $zipCodeLocationId, int $countyLocationId): Builder
    {
        return $query->where(fn(Builder $query) =>
            $query->orWhere(fn(Builder $query) =>
                $query->where(fn(Builder $query) =>
                    $query->orWhere(CompanyConfiguration::TABLE .'.'. CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING, true)
                        ->orWhere(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ZIP_CODE_TARGETED, true)
        )->where(CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID, $zipCodeLocationId)
            )->orWhere(fn(Builder $query) =>
                $query->where(CompanyConfiguration::TABLE .'.'. CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING, false)
            ->where(CompanyCampaign::FIELD_ZIP_CODE_TARGETED, false)
            ->where('county_locations.' . Location::ID, $countyLocationId)
        ));
    }

    /**
     * Returns the service id of a given project.
     *
     * @TODO: this can be done better.
     *
     * @param ConsumerProject $project
     * @return int
     */
    protected function getServiceIDOfProject(ConsumerProject $project): int
    {
        // TODO: Should the project not include the service? Feels hacky to get it from the ConsumerProduct when there's
        //       a chance it couldn't be a lead??
        return $project->leadConsumerProduct()->serviceProduct->industry_service_id;
    }

    /**
     * @param ConsumerProject $consumerProject
     *
     * @return array
     */
    protected function getCompaniesToExclude(ConsumerProject $consumerProject): array
    {
        $companiesAlreadyAssigned = $consumerProject->consumer
            ->productAssignments()
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->get()
            ->pluck(ProductAssignment::FIELD_COMPANY_ID);

        return array_unique([
            ...$companiesAlreadyAssigned->toArray(),
            ...$consumerProject->getExcludedCompanies() ?? [],
            ...$this->getDuplicateCompanies($consumerProject)
        ]);
    }

    /**
     * @param ConsumerProject $consumerProject
     *
     * @return array
     */
    protected function getDuplicateCompanies(ConsumerProject $consumerProject): array
    {
        /** @var ConsumerProductRepository $repository */
        $repository = app(ConsumerProductRepository::class);

        $timeframeInDays = 90;

        return $repository->getDuplicateConsumerProductQueryWithJoin($consumerProject->leadConsumerProduct(), $timeframeInDays)
            ->join(
                ProductAssignment::TABLE,
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID
            )
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::CREATED_AT, '>=', now()->subDays($timeframeInDays))
            ->select(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID)
            ->get()
            ->pluck(ProductAssignment::FIELD_COMPANY_ID)
            ->toArray();
    }
}
