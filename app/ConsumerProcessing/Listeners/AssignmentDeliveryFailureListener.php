<?php

namespace App\ConsumerProcessing\Listeners;

use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\ConsumerProcessing\Events\AssignmentDeliveryFailureEvent;
use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\CompanyCampaign\CRMDeliveryFailureEvent;
use App\Jobs\DispatchPubSubEvent;
use App\Models\LeadProcessingAllocation;
use App\Models\Odin\Company;
use App\Services\ConsumerProductLifecycleTrackingService;
use Illuminate\Contracts\Events\Dispatcher;

class AssignmentDeliveryFailureListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(
        protected Dispatcher $dispatcher,
    ) {}

    /**
     * Handle the event.
     *
     * @param AssignmentDeliveryFailureEvent $event
     * @return void
     */
    public function handle(AssignmentDeliveryFailureEvent $event):  void
    {
        $this->firePubSubEvents($event);

        $project = $event->consumerProject ?? null;
        $data = $event->postAllocationData ?? null;

        $potentialCrmRejections = $data->getPotentialCRMRejections();

        if ($potentialCrmRejections) {
            /** @var LeadProcessingAllocation $allocation */
            $processorId = LeadProcessingAllocation::query()
                ->where(LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $project->leadConsumerProduct()->id)
                ->first()
                ?->lead_processor_id;
            $hadDeliveries = $data->companiesDeliveredTo() > 0;

            event(new CRMDeliveryFailureEvent(
                $project->leadConsumerProduct()->id,
                $processorId,
                $potentialCrmRejections,
                $hadDeliveries
            ));
        }

        // If every Company had at least one successful delivery, and all legs were sold, don't re-queue job?
        if ($project->max_contact_requests > $data->companiesDeliveredTo()){
            ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($project->leadConsumerProduct());
            AttemptConsumerProjectAllocationJob::dispatch($event->consumerProject);
        }

    }

    /**
     * @param AssignmentDeliveryFailureEvent $event
     *
     * @return void
     */
    protected function firePubSubEvents(AssignmentDeliveryFailureEvent $event): void
    {
        $event->postAllocationData->getFailedAssignments()->each(fn (array $assignmentStatus) => DispatchPubSubEvent::dispatch(EventCategory::LEADS, EventName::DELIVERY_FAILURE, [
            'company_reference' => Company::query()->find($assignmentStatus[PostAllocationData::COMPANY_ID])?->{Company::FIELD_REFERENCE},
            'lead_id' => $event->consumerProject->leadConsumerProduct()->id,
            'future_campaign' => true,
            'campaign_id' => $assignmentStatus[PostAllocationData::CAMPAIGN_ID]
        ]));
    }
}
