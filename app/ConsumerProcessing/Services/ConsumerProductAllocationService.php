<?php

namespace App\ConsumerProcessing\Services;

use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\QualityTier as QualityTierModel;
use App\Models\SaleType;
use App\Repositories\Campaigns\Modules\Budget\BudgetRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\QualityTierRepository;
use App\Repositories\Odin\SaleTypeRepository;
use App\Services\Campaigns\ProductBiddingService;
use App\Services\Odin\ConsumerProductService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class ConsumerProductAllocationService
{
    public function __construct(
        protected CampaignMediator $campaignMediator,
        protected CompanyCampaignRepository $campaignRepository,
        protected BudgetRepository $budgetRepository,
        protected ProductBiddingService $biddingService,
        protected SaleTypeRepository $saleTypeRepository,
        protected QualityTierRepository $qualityTierRepository,
        protected ConsumerProjectProcessingService $consumerProjectProcessingService,
        protected LocationRepository $locationRepository,
        protected ConsumerProductService $consumerProductService
    ) {}

    /**
     * @param ConsumerProduct $consumerProduct
     * @param array $campaignIds
     * @param SaleTypes $saleType
     * @param QualityTier $qualityTier
     *
     * @return bool
     * @throws BindingResolutionException
     */
    public function allocateConsumerProduct(ConsumerProduct $consumerProduct, array $campaignIds, SaleTypes $saleType, QualityTier $qualityTier): bool
    {
        $allocationData = $this->campaignMediator->allocate(
            $this->prepareAssignments(
                campaigns: $this->campaignRepository->findMany(
                    companyCampaignIds: $campaignIds,
                    relations: [CompanyCampaign::RELATION_CAMPAIGN_PROPERTY_TYPES],
                ),
                consumerProduct: $consumerProduct,
                saleType: $this->saleTypeRepository->getSalesTypeByName($saleType->value),
                qualityTier: $this->qualityTierRepository->findByName($qualityTier->value)
            )
        );

        $this->campaignMediator->postAllocation(
            project: $this->consumerProjectProcessingService->prepareConsumerProject(
                $consumerProduct->consumer,
                $consumerProduct->address
            ),
            productAssignments: $allocationData->getProductAssignments(),
            handleFailure: false
        );

        $this->consumerProductService->updateConsumerProductStatus($consumerProduct->id);

        return true;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param int[] $campaignIds
     *
     * @return bool
     */
    public function isDuplicateAllocations(ConsumerProduct $consumerProduct, array $campaignIds): bool
    {
        $companies = $this->campaignRepository->findMany($campaignIds)->pluck(CompanyCampaign::FIELD_COMPANY_ID)->toArray();

        return $consumerProduct->productAssignment()
            ->whereIn(ProductAssignment::FIELD_COMPANY_ID, $companies)
            ->exists();
    }

    /**
     * @param Collection<CompanyCampaign> $campaigns
     * @param ConsumerProduct $consumerProduct
     * @param SaleType $saleType
     * @param QualityTierModel $qualityTier
     *
     * @return Collection<ProposedProductAssignment>
     */
    protected function prepareAssignments(Collection $campaigns, ConsumerProduct $consumerProduct, SaleType $saleType, QualityTierModel $qualityTier): Collection
    {
        $zipCode = $this->locationRepository->getZipCode($consumerProduct->address->zip_code);
        $county = $this->locationRepository->getCounty($zipCode->state_key, $zipCode->county_key);
        $state = $this->locationRepository->getState($zipCode->state_key);

        $residentialPropertyTypeId = PropertyType::RESIDENTIAL->model()->id;

        return $campaigns->map(fn(CompanyCampaign $campaign) => new ProposedProductAssignment(
            companyId: $campaign->company_id,
            campaignId: $campaign->id,
            budgetId: $this->budgetRepository->getVerifiedBudgetForCampaignOrFail($campaign)->id,
            price: $this->biddingService->getProductBid(
                companyCampaign: $campaign,
                countyLocationId: $county->id,
                stateLocationId: $state->id,
                propertyTypeId: $campaign->campaignPropertyTypes->first()?->id ?? $residentialPropertyTypeId,
                qualityTierId: $qualityTier->id,
                salesTypeId: $saleType->id
            ),
            productId: $campaign->product_id,
            salesTypeId: $saleType->id,
            consumerProductId: $consumerProduct->id,
            qualityTierId: $qualityTier->id
        ));
    }
}
