<?php

namespace App\ConsumerProcessing\Services;

use App\ConsumerProcessing\DataModel\ConsumerProjectAllocationResult;
use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\ConsumerProcessing\Repositories\AvailableCompanyCampaignRepository;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\LeadProcessingConfiguration;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;

class ConsumerProjectProcessingService
{
    public function __construct(
        protected AvailableCompanyCampaignRepository $campaignRepository,
        protected AllocationSchedulingService        $schedulingService,
        protected TimeZoneHelperService              $timeZoneHelperService,
    ) {}

    /**
     * Handles allocating a consumer.
     *
     * @param Consumer $consumer
     * @param Address $address
     * @param int[]|null $excludedCompanies
     *
     * @return ConsumerProjectAllocationResult
     * @throws Exception
     */
    public function allocate(Consumer $consumer, Address $address, ?array $excludedCompanies = null): ConsumerProjectAllocationResult
    {
        /**
         * Will remove this docblock after work has been completed - however, roughly representing what was on the whiteboard
         *
         * Alloc Lead
         *  |
         * \/
         * Qualify Consumer Product (lead/appt/etc)
         *  |
         * \/
         * Fetch matching campaigns -> Consumer Product -> Avail Camp Service
         *  |
         * \/
         * Run Pre L/C -> camps -> run Pre L/C method
         *  |
         * \/
         * Filter Camps -> ConsProd/Camps -> Run Filter L/C method
         *  |
         * \/
         * Calculate BRS
         *  |
         * \/
         * Allocate -> camps/consprod (changed w/ proposed assignments) -> run allocate L/C methods
         *  |
         * \/
         * Post Alloc -> camps/consprod -> run post-alloc L/C methods
         */
        $project = $this->prepareConsumerProject(
            consumer: $consumer,
            address: $address,
            lastQualifiedAt: Carbon::now(),
            skipTimezoneDelay: $this->ignoreTimezoneDelay(),
            excludedCompanies: $excludedCompanies,
            isDirectLead: false,
        );

        if ($project->leadProductIsInvoiced()) {
            logger()->warning("An invoiced product entered allocation: id - " . $project->leadConsumerProduct()?->id);

            return new ConsumerProjectAllocationResult(false);
        }

        $delay = $this->schedulingService->delayToTimeZoneOpenForConsumerProject($project);
        ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($project->leadConsumerProduct(), $delay);
        AttemptConsumerProjectAllocationJob::dispatch($project)->delay($delay);

        return new ConsumerProjectAllocationResult(true); // todo: not really any useful data to return, {queued: true}?
    }

    /**
     * Handles creating a consumer project from the given consumer.
     *
     * @param Consumer $consumer
     * @param Address $address
     * @param Carbon|null $lastQualifiedAt
     * @param bool $skipTimezoneDelay
     * @param bool $unverifiedOnly
     * @param int[]|null $excludedCompanies
     * @param bool $isDirectLead
     * @return ConsumerProject
     */
    public function prepareConsumerProject(
        Consumer $consumer,
        Address $address,
        ?Carbon $lastQualifiedAt = null,
        bool $skipTimezoneDelay = true,
        bool $unverifiedOnly = false,
        ?array $excludedCompanies = null,
        bool $isDirectLead = false,
    ): ConsumerProject
    {
        return new ConsumerProject(
            consumer: $consumer,
            address: $address,
            last_qualified_at: $lastQualifiedAt,
            skipTimezoneDelay: $skipTimezoneDelay,
            unverifiedOnly: $unverifiedOnly,
            excludedCompanies: $excludedCompanies,
            isDirectLead: $isDirectLead,
        );
    }

    /**
     * Returns the available campaigns for this project.
     *
     * @param ConsumerProject $project
     * @return Collection
     */
    public function getAvailableCampaigns(ConsumerProject $project): Collection
    {
        return $this->campaignRepository->getAvailableCampaignsForProject($project);
    }

    /**
     * @param ConsumerProject $project
     * @return Collection
     */
    public function getPotentialProductTypes(ConsumerProject $project): Collection
    {
        $products = collect($project->potentialProducts())
            ->map(fn($types, $slug) => Product::query()
                ->where(Product::FIELD_NAME, ProductEnum::from($slug)->value)
                ->first()
            )->filter();

        // Order the products. Make sure Direct Leads run first then Appointment then Lead if we have more than 1 product
        if ($products->count() > 1) {
            $products = $products->sortBy(function(Product $product) {
                return match ($product->name) {
                    ProductEnum::DIRECT_LEADS->value => -2,
                    ProductEnum::APPOINTMENT->value => -1,
                    default => 1
                };
            });
        }

        return $products;
    }

    /**
     * @param ConsumerProject $project
     * @param Collection<int, ProposedProductAssignment> $newAssignments
     * @return bool
     */
    public function companiesEligibleToReceiveAssignments(ConsumerProject $project, Collection $newAssignments): bool
    {
        if (
            !$project->skipTimezoneDelay &&
            $this->consumerProjectCreatedBeforeTimezoneOpen($project) &&
            $this->consumerProjectWithinTimezoneOpeningThreshold($project) &&
            $this->withinRecencyThreshold($newAssignments)
        ){
            return false;
        }
        return true;
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $updatedAssignments
     * @return void
     */
    public function updateExistingProductAssignments(Collection $updatedAssignments): void
    {
        foreach ($updatedAssignments as $assignment){
            $assignment->existingProductAssignment()->update([
                ProductAssignment::FIELD_SALE_TYPE_ID => $assignment->salesTypeId,
                ProductAssignment::FIELD_COST => $assignment->price,
            ]);
        }
    }

    /**
     * @param ConsumerProject $project
     * @return bool
     * @throws Exception
     */
    private function consumerProjectWithinTimezoneOpeningThreshold(ConsumerProject $project): bool
    {
        return $this->leadWithinTimezoneOpeningThreshold($project->leadConsumerProduct());
    }

    /**
     * @param Collection $newAssignments
     * @return bool
     */
    private function withinRecencyThreshold(Collection $newAssignments): bool
    {
        return $this->getArtificialDelay($newAssignments) > 0;
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $assignments
     * @return int
     */
    protected function getArtificialDelay(Collection $assignments): int
    {
        /** @var LeadProcessingConfiguration|null $leadConfig */
        $leadConfig       = LeadProcessingConfiguration::query()->first();
        $recencyThreshold = $leadConfig ? $leadConfig->{LeadProcessingConfiguration::FIELD_LEAD_RECENCY_THRESHOLD_IN_SECONDS} : LeadProcessingConfiguration::LEAD_RECENCY_THRESHOLD_DEFAULT;

        $companies = collect();
        /** @var ProposedProductAssignment $assignment */
        foreach ($assignments as $assignment){
            $companies->push($assignment->company());
        }

        $mostRecentDeliveryTime = $this->getMostRecentTimeDelivered($companies);

        if ($mostRecentDeliveryTime) {
            $delay = $recencyThreshold - (int) Carbon::now()->diffInSeconds($mostRecentDeliveryTime, true);

            if ($delay > 0) {
                return $delay;
            }
        }

        return 0;
    }

    /**
     * @param Collection<int, Company> $companies
     * @return Carbon|null
     */
    private function getMostRecentTimeDelivered(Collection $companies): ?Carbon
    {
        /** @var ProductAssignmentRepository $productAssignmentRepository */
        $productAssignmentRepository = app(ProductAssignmentRepository::class);
        $productAssignments = $productAssignmentRepository->getProductAssignmentsByCompanyIds(
            $companies->pluck(Company::FIELD_ID)->toArray(),
            2
        );
        return $productAssignments->max(ProductAssignment::FIELD_DELIVERED_AT);
    }

    /**
     * @return bool
     */
    private function ignoreTimezoneDelay(): bool
    {
        if (app()->isProduction()) return false;

        return config('sales.ignore_timezone_delay');
    }

    private function consumerProjectCreatedBeforeTimezoneOpen(ConsumerProject $project): bool
    {
        $timeZoneOpenHourUTC = $this->timeZoneOpenHourUTC($project);
        $timezoneOpenDuration = $timeZoneOpenHourUTC->diffInMinutes(Carbon::now('UTC'), false);
        return $project->ageInMinutes() > $timezoneOpenDuration;
    }

    /**
     * @param ConsumerProject $project
     * @return Carbon
     */
    private function timeZoneOpenHourUTC(ConsumerProject $project): Carbon
    {
        $timeZoneOpenHourUTC = Carbon::now('UTC');
        $timeZoneOpenHourUTC->hour   = $this->timeZoneHelperService->getUTCOpenHourForProduct($project->leadConsumerProduct());
        $timeZoneOpenHourUTC->minute = 0;
        $timeZoneOpenHourUTC->second = 0;
        return $timeZoneOpenHourUTC;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return bool
     * @throws Exception
     */
    public function leadWithinTimezoneOpeningThreshold(ConsumerProduct $consumerProduct): bool
    {
        $leadConfig       = LeadProcessingConfiguration::query()->first();
        $openingThreshold = $leadConfig ? $leadConfig->time_zone_opening_delay_in_minutes : LeadProcessingConfiguration::TIME_ZONE_OPENING_DELAY_DEFAULT;

        $openingThreshold = round(($openingThreshold / 60), 2);

        $utcOpenHour    = $this->timeZoneHelperService->getUTCOpenHourForProduct($consumerProduct);
        $currentUTCHour = Carbon::now('UTC')->hour;

        $hourSinceOpen = $currentUTCHour - $utcOpenHour;

        return $hourSinceOpen >= 0 && $hourSinceOpen < $openingThreshold;
    }
}
