<?php

namespace App\ConsumerProcessing\Services;

use App\DataModels\Campaigns\ConsumerProject;
use App\Models\LeadProcessingTimeZoneConfiguration;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Carbon\Carbon;
use Exception;

class AllocationSchedulingService
{
    const string NEXT_DAY_REATTEMPT_TIME     = '09:10'; // 9:10 AM
    const string NEXT_DAY_FINAL_ATTEMPT_TIME = '10:00'; // 10:00 AM

    public function __construct(
        protected ProductProcessingRepository $processingRepository,
        protected TimeZoneHelperService       $timeZoneHelperService,
    ) {}

    /**
     * @param ConsumerProject $project
     * @return int
     * @throws Exception
     */
    public function delayToTimeZoneOpenForConsumerProject(ConsumerProject $project): int
    {
        if ($project->skipTimezoneDelay) {
            return 0;
        }

        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->timeZoneHelperService->getTimezoneOffsetDataFromZipcode(
            $project->zipCode()
        );

        $timezoneConfig = $this->processingRepository->getTimezoneConfigurationByStandardUTCOffset($standardUTCOffset);

        $dstAdjustment = $this->timeZoneHelperService->getDSTAdjustment($observingDST);
        $utcOffset     = $standardUTCOffset + $dstAdjustment;

        $localOpenHour    = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR};
        $localCloseHour   = $timezoneConfig->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR};
        $localCurrentHour = Carbon::now($utcOffset)->hour;

        logger()->info('DST Adjustment: ', [$dstAdjustment]);
        logger()->info('Local Open Hour: ', [$localOpenHour]);
        logger()->info('Local Close Hour: ', [$localCloseHour]);
        logger()->info('Local Current Hour: ', [$localCurrentHour]);

        // TZ is currently open, no delay
        if ($localCurrentHour >= $localOpenHour && $localCurrentHour < $localCloseHour)
            return 0;

        return (int) Carbon::now($utcOffset)
            ->diffInSeconds(Carbon::now($utcOffset)->next($this->intTo24HourFormat($localOpenHour)), true);
    }

    /**
     * @param string $zipCode
     * @param bool $finalAttempt
     * @return float|int
     * @throws Exception
     */
    public function getNextDayDelay(string $zipCode, bool $finalAttempt = false): float|int
    {
        $reattemptTime = $finalAttempt ? self::NEXT_DAY_FINAL_ATTEMPT_TIME : self::NEXT_DAY_REATTEMPT_TIME;
        ['standard_utc_offset' => $standardUTCOffset, 'observing_dst' => $observingDST] = $this->timeZoneHelperService->getTimezoneOffsetDataFromZipcode(
            $zipCode
        );
        $dstAdjustment = $this->timeZoneHelperService->getDSTAdjustment($observingDST);
        $localNow = Carbon::now($standardUTCOffset + $dstAdjustment);

        if ($localNow->copy()->next($reattemptTime)->isToday()) {
            return $localNow->diffInSeconds($localNow->copy()->addDay()->startOfDay()->next($reattemptTime));
        }

        return $localNow->diffInSeconds($localNow->copy()->next($reattemptTime));
    }

    /**
     * @param int $hour
     * @return string
     */
    private function intTo24HourFormat(int $hour): string
    {
        return $hour < 10 ? ('0' . $hour . ':00') : ($hour . ':00');
    }

}
