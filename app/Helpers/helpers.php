<?php

use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

/**
 * This function attempts to sanitize the domain. Example: Take in https://www.solarreviews.com/blog?example=test and return solarreviews.com
 *
 * @param string $website
 * @return string|null
 */
function getWebsiteDomain(string $website): string|null
{
    // Remove whitespace and make lower case
    $website = strtolower(trim($website));

    try{
        // Remove http / https if exists
        $website = parse_url($website)['host'];
    } catch (Exception) {
    }

    $website = rtrim(preg_replace('#^(?:.+?\\.)+(.+?\\.(?:co\\.uk|com|net|gov|solar|energy|mil|edu|biz|th|cn|de|ca|au|us|info|site|me|io|org|))#', '$1', $website),"/");

    if($website === "http:" || $website === "https:") {
        return null;
    }

    return $website;
}


/**
 * @param string|null $encodedString
 * @return string
 */
function safeBase64Decode(?string $encodedString = null): string
{
    if (empty($encodedString)) return '';

    return base64_decode(str_replace('-', '+', str_replace('_', '/', $encodedString)));
}

/**
 * @param string|null $string
 * @return string
 */
function safeBase64Encode(?string $string = null): string
{
    if (empty($string)) return '';

    return str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($string));
}

/**
 * @param ?string $email
 * @return string
 */
function obfuscateEmail(?string $email): string
{
    return preg_replace_callback('/^(\w)(.*)(@)(\w)(.*)(\.\w+)$/', function ($matches) {
        $local = $matches[1] . str_repeat('*', strlen($matches[2]));
        $domain = $matches[4] . str_repeat('*', strlen($matches[5]));
        return $local . $matches[3] . $domain . $matches[6];
    }, $email ?? '');
}

/**
 * @param ?string $phone
 * @return string
 */
function obfuscatePhone(?string $phone): string
{
    return preg_replace('/\d/', '*', $phone ?? '');
}

/**
 * Turns a phone string into a string to be used with sql pattern matching
 * ex. +1 (123)456-7890 => %123%456%7890%
 *     ************     => %123%456%7890%
 *     (123)456-7890    => %123%456%7890%
 *     ******.456.7890  => %123%456%7890%
 *     **************   => %123%456%7890%
 *
 * @param string $phone
 * @return string
 */
function normalizePhoneNumberSql(string $phone): string
{
    $withCountryCodePattern    = '/^.*(\d{1,3}).*(\d{3}).*(\d{3}).*(\d{4}).*$/';
    $withoutCountryCodePattern = '/^.*(\d{3}).*(\d{3}).*(\d{4}).*$/';
    if (preg_match($withCountryCodePattern, $phone, $matches)) {
        return '%' . $matches[2] . '%' . $matches[3] . '%' . $matches[4] . '%';
    } else if (preg_match($withoutCountryCodePattern, $phone, $matches)) {
        return '%' . $matches[1] . '%' . $matches[2] . '%' . $matches[3] . '%';
    } else {
        return '%' . $phone . '%';
    }
}


/**
 * Get value by property in snake, camel or normal case
 * @param string $property
 * @param array $context
 * @param mixed|null $default
 * @return mixed
 */
function getValueCaseInsensitive(
    string $property,
    array $context,
    mixed $default = null
): mixed
{
    if (($value = Arr::get($context, $property)) !== null) {
        return [$value, $property];
    }

    $normalizedProperty = str_replace('-', '_', $property); // Replace hyphens with underscores for snake_case compatibility

    $formats = [
        'snake_case' => Str::snake($normalizedProperty),
        'kebab-case' => Str::kebab($property),
        'camelCase'  => Str::camel($property),
    ];

    foreach ($formats as $format => $formattedKey) {
        if (($value = Arr::get($context, $formattedKey)) !== null) {
            return [$value, $formattedKey];
        }
    }

    return [$default, null];
}


/**
 * @param string $type
 * @param string $prefix
 * @return array
 */
function getAllAssetContents(string $type, string $prefix): array
{
    $path = public_path('build/assets');
    $files = File::files($path);
    $contents = [];

    foreach ($files as $file) {
        $filename = $file->getFilename();

        if (str_starts_with($filename, $prefix . '-') && $file->getExtension() === $type) {
            $contents[] = File::get($file->getPathname());
        }
    }

    return $contents;
}

/**
 * @param Carbon|string $startTime
 * @param Carbon|string $endTime
 * @return array
 */
function calculateDiffInMinutesAndSeconds(
    Carbon|string $startTime,
    Carbon|string $endTime,
): array
{
    $date1 = Carbon::parse($startTime);
    $date2 = Carbon::parse($endTime);

    $diffInMinutes = $date2->diffInMinutes($date1);
    $diffInSeconds = $date2->diffInSeconds($date1) % 60;

    return [
        'minutes' => $diffInMinutes,
        'seconds' => $diffInSeconds,
    ];
}
