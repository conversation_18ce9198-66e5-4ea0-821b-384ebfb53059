<?php

namespace App\Helpers;

use Illuminate\Support\Carbon;

class CarbonHelper extends Carbon
{
    const string TIMEZONE_MOUNTAIN    = 'MST';
    const string TIMEZONE_DENVER      = 'America/Denver';
    const string FORMAT_DATE          = 'M d, Y';
    const string FORMAT_BASE          = 'M d, Y h:i A';
    const string FORMAT_BASE_TIMEZONE = 'M d, Y h:i A (T)';
    const string FORMAT_DATETIME_WITH_TIMEZONE = 'M d Y, h:i:sa (T)';

    /**
     * Parse with timezone. If not provided we will use denver
     * @param string $datetime
     * @param string $defaultTimezone
     * @return CarbonHelper
     */
    public static function parseWithTimezone(string $datetime, string $defaultTimezone = self::TIMEZONE_DENVER): CarbonHelper
    {
        return self::parse($datetime)->tz($defaultTimezone);
    }

    /**
     * @return CarbonHelper
     */
    public function endOfDayUTC(): CarbonHelper
    {
        return $this->endOfDay()->utc();
    }

    /**
     * @return CarbonHelper
     */
    public function startOfDayUTC(): CarbonHelper
    {
        return $this->startOfDay()->utc();
    }

    /**
     * @param string|null $format
     * @param string|null $timezone
     * @return string
     */
    public function toFormat(?string $format = self::FORMAT_BASE, ?string $timezone = null): string
    {
        if ($timezone) {
            $this->timezone($timezone);
        }

        return $this->format($format);
    }

    /**
     * @param string $dateTimeString
     * @param string $timezone
     * @return Carbon
     */
    public static function toTimezone(string $dateTimeString, string $timezone = self::TIMEZONE_MOUNTAIN): Carbon
    {
        $dateOnly = self::parse($dateTimeString)->format('Y-m-d');

        return self::createFromFormat('Y-m-d', $dateOnly, $timezone);
    }
}
