<?php
namespace  App\Helpers;

class Sanitizer
{
    /**
     * @param $input
     * @return mixed|string
     */
    public static function sanitizeString($input): mixed
    {
        if (!is_string($input)) {
            return $input;
        }
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }

    /**
     * @param $input
     * @return mixed
     */
    public static function sanitizeEmail($input): mixed
    {
        if (!is_string($input)) {
            return $input;
        }
        return filter_var($input, FILTER_SANITIZE_EMAIL);
    }

    /**
     * @param $input
     * @return mixed
     */
    public static function sanitizeNumber($input): mixed
    {
        if (!is_numeric($input)) {
            return $input;
        }
        return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT);
    }
}
