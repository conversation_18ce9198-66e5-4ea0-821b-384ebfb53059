<?php

namespace App\Utilities\AffiliatesPortal;

use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response as GuzzleResponse;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Throwable;

class ApiClient
{
    private PendingRequest $client;

    public function __construct()
    {
        $this->client = Http::withToken(config('services.affiliates_portal_api.token'))
            ->baseUrl(config('services.affiliates_portal_api.url'))
            ->throw(function(Response $res, Throwable $t) {
                logger()->error("Affiliates Portal Error");
                logger()->error([
                    "status" => $res->getStatusCode(),
                    "uri" => (string) $res->effectiveUri(),
                    "body" => $res->body()
                ]);
            })
            ->accept("application/json");

        if(config('app.debug')) {
            $this->client
                ->withRequestMiddleware(function (Request $request) {
                    logger()->debug([
                        "uri" => (string) $request->getUri(),
                        "method" => $request->getMethod(),
                        "headers" => $request->getHeaders(),
                        "body" => $request->getBody()->getContents()
                    ]);

                    return $request;
                })
                ->withResponseMiddleware(function (GuzzleResponse $response) {
                    logger()->debug([
                        "status" => $response->getStatusCode(),
                        "body" => (string) $response->getBody()
                    ]);

                    return $response;
                });
        }
    }

    /**
     * @param string $affiliateUuid
     * @return array
     * @throws ConnectionException
     */
    public function getUsers(string $affiliateUuid): array
    {
        return $this->client->get('users', ['affiliate_id' => $affiliateUuid])->json('data');
    }

    /**
     * @param array $data
     * @return int
     * @throws ConnectionException
     */
    public function createUser(array $data): int
    {
        return $this->client->post('users', $data)->json('id');
    }

    /**
     * @param int $id
     * @param array $data
     * @return void
     * @throws ConnectionException
     */
    public function updateUser(int $id, array $data): void
    {
        $this->client->patch("users/{$id}", $data);
    }

    /**
     * @param int $id
     * @return void
     * @throws ConnectionException
     */
    public function deleteUser(int $id): void
    {
        $this->client->delete("users/{$id}");
    }
}
