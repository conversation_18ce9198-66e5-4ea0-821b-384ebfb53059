<?php

namespace App\Abstracts\ProductPricing;

use App\DataModels\Odin\Prices\BestRevenueScenarioDataModel;
use App\DataModels\Odin\Prices\BRSCampaignPrices;
use App\DataModels\Odin\Prices\PotentialBRSCampaigns;
use App\DataModels\Odin\Prices\FilteredBRSPrices;
use App\DataModels\Odin\Prices\SaleTypeLimits;
use App\Enums\Odin\Product as ProductEnum;
use App\Jobs\RecordMonitoringLog;
use App\Jobs\WriteBestRevenueScenarioLogs;
use App\Models\BestRevenueScenarioLog;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductCampaign;
use App\Services\Odin\Appointments\AppointmentService;
use Exception;
use Illuminate\Support\Collection;
use Throwable;

abstract class BestRevenueScenarioServiceAbstract
{
    /**
     * @param int $consumerProductId
     * @param array|null $excludedCompanyIds
     * @param int|null $remainingLegs
     * @param int|null $overrideSaleTypeId
     * @return BestRevenueScenarioDataModel|null
     * @throws Throwable
     */
    final public function getBestRevenueScenario(int $consumerProductId, ?array $excludedCompanyIds = [], ?int $remainingLegs = null, ?int $overrideSaleTypeId = null): ?BestRevenueScenarioDataModel
    {
        try {
            $runId = floor(microtime(true));

            $step = 1;

            $potentialCampaigns = $this->getPotentialCampaigns($consumerProductId, $excludedCompanyIds, $overrideSaleTypeId);

            if(!$potentialCampaigns || $potentialCampaigns->isEmpty()) {
                $this->logProcess(
                    $runId,
                    $consumerProductId,
                    BestRevenueScenarioLog::MESSAGE_NO_POTENTIAL_CAMPAIGNS_FOUND,
                    $step++,
                    collect([0 => collect([0])])
                );

                return null;
            }
            else {
                $this->logProcess(
                    $runId,
                    $consumerProductId,
                    BestRevenueScenarioLog::MESSAGE_POTENTIAL_CAMPAIGNS_FOUND,
                    $step++,
                    $potentialCampaigns->getCampaignIds()
                );
            }

            if($potentialCampaigns->product === ProductEnum::APPOINTMENT) {
                $unfilteredPotentialCampaigns = $potentialCampaigns->copy();

                $potentialCampaigns = $this->filterOutUnavailableCampaigns($consumerProductId, $potentialCampaigns);

                $removedCampaigns = $this->getRemovedCampaigns($unfilteredPotentialCampaigns->getCampaignIds(), $potentialCampaigns->getCampaignIds());

                $this->logProcess(
                    $runId,
                    $consumerProductId,
                    BestRevenueScenarioLog::MESSAGE_REMOVED_BY_SCHEDULING_STEP,
                    $step++,
                    $removedCampaigns
                );

                if($potentialCampaigns->isEmpty()) {
                    return null;
                }
            }

            $campaignPrices = $this->populatePrices($potentialCampaigns);

            $removedCampaigns = $campaignPrices ? $this->getRemovedCampaigns($potentialCampaigns->getCampaignIds(), $campaignPrices->getCampaignIds()) : $potentialCampaigns->getCampaignIds();

            $this->logProcess(
                $runId,
                $consumerProductId,
                BestRevenueScenarioLog::MESSAGE_REMOVED_BY_PRICING_STEP,
                $step++,
                $removedCampaigns
            );

            if(!$campaignPrices || $campaignPrices->isEmpty()) {
                return null;
            }

            $filteredCampaignPrices = $this->filterOverBudgetCampaigns($campaignPrices, $consumerProductId);

            $removedCampaigns = $filteredCampaignPrices ? $this->getRemovedCampaigns($campaignPrices->getCampaignIds(), $filteredCampaignPrices->getCampaignIds()) : $campaignPrices->getCampaignIds();

            $this->logProcess(
                $runId,
                $consumerProductId,
                BestRevenueScenarioLog::MESSAGE_REMOVED_BY_OVER_BUDGET_FILTER_STEP,
                $step++,
                $removedCampaigns
            );

            if(!$filteredCampaignPrices || $filteredCampaignPrices->isEmpty()) {
                return null;
            }

            $bestRevenueScenario = $this->determineMostProfitableSaleType($runId, $consumerProductId, $filteredCampaignPrices, $remainingLegs);

            $this->logProcess(
                $runId,
                $consumerProductId,
                BestRevenueScenarioLog::MESSAGE_REMOVED_BY_BRS_SORTING_STEP,
                $step++,
                $this->getRemovedCampaigns($filteredCampaignPrices->getCampaignIds(), $bestRevenueScenario->getCampaignIds())
            );

            $this->logProcess(
                $runId,
                $consumerProductId,
                BestRevenueScenarioLog::MESSAGE_BEST_REVENUE_SCENARIO_RESULT,
                $step++,
                $bestRevenueScenario->getCampaignIds()
            );

            return $bestRevenueScenario;
        }
        catch(Throwable $e) {
            RecordMonitoringLog::dispatch(
                __METHOD__.': '.substr($e->getMessage(), 0, 255),
                [
                    "consumer_product_id" => $consumerProductId,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param int $consumerProductId
     * @param array|null $excludedCompanyIds
     * @param int|null $overrideSaleTypeId
     * @return PotentialBRSCampaigns|null
     */
    abstract public function getPotentialCampaigns(int $consumerProductId, ?array $excludedCompanyIds = [], ?int $overrideSaleTypeId = null): ?PotentialBRSCampaigns;

    /**
     * @param PotentialBRSCampaigns $potentialBRSCampaigns
     * @return BRSCampaignPrices|null
     */
    abstract public function populatePrices(PotentialBRSCampaigns $potentialBRSCampaigns): ?BRSCampaignPrices;

    /**
     * @param BRSCampaignPrices $BRSCampaignPrices
     * @param int $consumerProductId
     * @return FilteredBRSPrices|null
     */
    abstract public function filterOverBudgetCampaigns(BRSCampaignPrices $BRSCampaignPrices, int $consumerProductId): ?FilteredBRSPrices;

    /**
     * @param ConsumerProduct $product
     * @return SaleTypeLimits
     */
    abstract public function getSaleTypeLimits(ConsumerProduct $product): SaleTypeLimits;

    /**
     * @param Company $company
     * @param ConsumerProduct $consumerProduct
     * @param ProductCampaign|null $productCampaign
     * @param array|null $excludedCompanyIds
     * @return string
     */
    abstract public function investigateAllocationFailure(Company $company, ConsumerProduct $consumerProduct, ?ProductCampaign $productCampaign = null, ?array $excludedCompanyIds = []): string;

    /**
     * @param int $runId
     * @param int $consumerProductId
     * @param FilteredBRSPrices $filteredBRSPrices
     * @param int|null $remainingLegs
     * @return BestRevenueScenarioDataModel
     * @throws Exception
     */
    private function determineMostProfitableSaleType(int $runId, int $consumerProductId, FilteredBRSPrices $filteredBRSPrices, ?int $remainingLegs = null): BestRevenueScenarioDataModel
    {
        /** @var ConsumerProduct $product */
        $product = ConsumerProduct::query()->findOrFail($consumerProductId);
        $leadSalesTypesLimits = $this->getSaleTypeLimits($product)->toArray();

        $brs = new BestRevenueScenarioDataModel($consumerProductId, $filteredBRSPrices->qualityTier, $runId);
        $removedCampaignMessages = collect();

        if($remainingLegs > 0) {
            $saleTypeId = array_search($product->{ConsumerProduct::FIELD_CONTACT_REQUESTS}, $leadSalesTypesLimits);

            $campaignPrices = $filteredBRSPrices->getSaleTypeCampaigns($saleTypeId);

            $sortedCampaignPrices = $this->sortCampaignPrices($campaignPrices);

            $saleTypeCampaignPrices = $this->getHighestSaleTypeCampaignPrices($remainingLegs, $sortedCampaignPrices);

            $brs->setScenario(
                $saleTypeId,
                round($saleTypeCampaignPrices->sum(FilteredBRSPrices::PRICE), 2),
                $saleTypeCampaignPrices
            );

            $removedCampaignMessages =
                $this->determineCampaignsNotChosenReasons(
                    $saleTypeId,
                    $sortedCampaignPrices,
                    $saleTypeCampaignPrices,
                    $removedCampaignMessages
                );
        }
        else {
            foreach($filteredBRSPrices as $saleTypeId => $campaignPrices) {
                if(!in_array($saleTypeId, array_keys($leadSalesTypesLimits))) {
                    continue;
                }

                $sortedCampaignPrices = $this->sortCampaignPrices($campaignPrices);

                $saleTypeCampaignPrices = $this->getHighestSaleTypeCampaignPrices($leadSalesTypesLimits[$saleTypeId], $sortedCampaignPrices);

                $totalPrice = round($saleTypeCampaignPrices->sum(FilteredBRSPrices::PRICE), 2);

                if($totalPrice > $brs->{BestRevenueScenarioDataModel::TOTAL_PRICE}) {
                    $brs->setScenario(
                        $saleTypeId,
                        $totalPrice,
                        $saleTypeCampaignPrices
                    );
                }

                $removedCampaignMessages =
                    $this->determineCampaignsNotChosenReasons(
                        $saleTypeId,
                        $sortedCampaignPrices,
                        $saleTypeCampaignPrices,
                        $removedCampaignMessages
                    );
            }
        }

        $this->bulkInsertBestRevenueScenarioLog($runId, $consumerProductId, 4, $removedCampaignMessages);

        $brs->unrectifyPrices();

        return $brs;
    }

    /**
     * @param Collection $campaignPrices
     * @return Collection
     */
    private function sortCampaignPrices(Collection &$campaignPrices): Collection
    {
        /**
         * Prefer higher price, lower budget usage, lower rejection percentage - in that priority
         */
        return $campaignPrices->shuffle()->sort(function($a, $b) {
            if($a[FilteredBRSPrices::PRICE] < $b[FilteredBRSPrices::PRICE]) {
                return 1;
            }
            else if($a[FilteredBRSPrices::PRICE] === $b[FilteredBRSPrices::PRICE]
                && $a[FilteredBRSPrices::BUDGET_USAGE] > $b[FilteredBRSPrices::BUDGET_USAGE]) {
                return 1;
            }
            else if($a[FilteredBRSPrices::PRICE] === $b[FilteredBRSPrices::PRICE]
                && round($a[FilteredBRSPrices::BUDGET_USAGE], 2) === round($b[FilteredBRSPrices::BUDGET_USAGE], 2)
                && $a[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID] > $b[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID]) {
                return 1;
            }

            return -1;
        })->keyBy(FilteredBRSPrices::CAMPAIGN_ID);
    }

    private function getHighestSaleTypeCampaignPrices(int $saleTypeLimit, Collection $sortedCampaignPrices): Collection
    {
        $usedCompanyIds = [];
        $saleTypeCampaignPrices = collect();
        foreach($sortedCampaignPrices as $campaignId => $cp) {
            if(!in_array($cp[FilteredBRSPrices::COMPANY_ID], $usedCompanyIds, true)) {
                $saleTypeCampaignPrices->put($campaignId, $cp);

                if($saleTypeCampaignPrices->count() >= $saleTypeLimit) {
                    break;
                }

                $usedCompanyIds[] = $cp[FilteredBRSPrices::COMPANY_ID];
            }
        }

        return $saleTypeCampaignPrices;
    }

    /**
     * @param int $runId
     * @param int $consumerProductId
     * @param int $brsMessageId
     * @param int $step
     * @param Collection $campaigns
     * @return bool
     * @throws Exception
     */
    private function logProcess(
        int $runId,
        int $consumerProductId,
        int $brsMessageId,
        int $step,
        Collection $campaigns
    ): bool
    {
        if(!in_array($brsMessageId, array_keys(BestRevenueScenarioLog::MESSAGES) , true)) {
            throw new Exception("Invalid BRS log message ID");
        }

        $campaignMessages = collect();
        foreach($campaigns as $saleTypeId => $campaignIds) {
            $campaignMessages->put($saleTypeId, collect(array_fill_keys($campaignIds->toArray(), $brsMessageId)));
        }

        $this->bulkInsertBestRevenueScenarioLog($runId, $consumerProductId, $step, $campaignMessages);

        return true;
    }

    /**
     * @param int $saleTypeId
     * @param Collection $sortedCampaignPrices
     * @param Collection $saleTypeCampaignPrices
     * @param Collection $removedCampaignMessages
     * @return Collection
     */
    private function determineCampaignsNotChosenReasons(int $saleTypeId, Collection $sortedCampaignPrices, Collection $saleTypeCampaignPrices, Collection $removedCampaignMessages): Collection
    {
        $removedOptions = $sortedCampaignPrices->diffKeys($saleTypeCampaignPrices);
        $lastChosenCampaignPrice = $saleTypeCampaignPrices->last();
        $removedCampaignMessages->put($saleTypeId, collect());
        foreach($removedOptions as $campaignId => $cp) {
            $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_RANDOM;

            if ($lastChosenCampaignPrice[FilteredBRSPrices::PRICE] > $cp[FilteredBRSPrices::PRICE]) {
                $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_PRICE;
            }

            if ($lastChosenCampaignPrice[FilteredBRSPrices::BUDGET_USAGE] < $cp[FilteredBRSPrices::BUDGET_USAGE]) {
                if ($messageId === BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_RANDOM) {
                    $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_BUDGET_USAGE;
                }
                else if ($messageId === BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_PRICE) {
                    $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_P_BU;
                }
            }

            if ($lastChosenCampaignPrice[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID] < $cp[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID]) {
                if ($messageId === BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_RANDOM) {
                    $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_REJECTION_PERCENTAGE;
                }
                else if ($messageId === BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_PRICE) {
                    $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_P_RP;
                }
                else if ($messageId === BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_BUDGET_USAGE) {
                    $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_BU_RP;
                }
                else if ($messageId === BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_P_BU) {
                    $messageId = BestRevenueScenarioLog::MESSAGE_NOT_CHOSEN_P_BU_RP;
                }
            }

            $removedCampaignMessages->get($saleTypeId)->put($campaignId, $messageId);
        }

        return $removedCampaignMessages;
    }

    /**
     * @param int $runId
     * @param int $consumerProductId
     * @param int $step
     * @param Collection $campaignMessages
     * @return bool
     */
    private function bulkInsertBestRevenueScenarioLog(int $runId, int $consumerProductId, int $step, Collection $campaignMessages): bool
    {
        try {
            WriteBestRevenueScenarioLogs::dispatch($runId, $consumerProductId, $step, $campaignMessages);

            return true;
        }
        catch(Throwable $e) {
            logger()->error($e);
        }

        return false;
    }

    /**
     * @param Collection $previousCampaignIds
     * @param Collection $currentCampaignIds
     * @return Collection
     */
    private function getRemovedCampaigns(Collection $previousCampaignIds, Collection $currentCampaignIds): Collection
    {
        $removedCampaigns = collect();

        foreach($previousCampaignIds as $saleTypeId => $campaignIds) {
            $removed = $campaignIds->diff($currentCampaignIds->get($saleTypeId, collect()));

            if($removed->isNotEmpty()) {
                $removedCampaigns->put($saleTypeId, $removed);
            }
        }

        return $removedCampaigns;
    }

    /**
     * @param int $consumerProductId
     * @param PotentialBRSCampaigns $potentialBRSCampaigns
     * @return PotentialBRSCampaigns
     */
    private function filterOutUnavailableCampaigns(int $consumerProductId, PotentialBRSCampaigns $potentialBRSCampaigns): PotentialBRSCampaigns
    {
        $companiesUsingCalendars = CompanyConfiguration::query()
            ->whereIn(CompanyConfiguration::FIELD_COMPANY_ID, $potentialBRSCampaigns->getCompanyIds())
            ->where(CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE, true)
            ->where(CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR, true)
            ->pluck(CompanyConfiguration::FIELD_COMPANY_ID)
            ->toArray();

        $companyCampaignIds = $potentialBRSCampaigns->getCampaignIdsForCompanies($companiesUsingCalendars);

        $availabilities = app(AppointmentService::class)->getProductCampaignsAvailability(
            $consumerProductId,
            $companyCampaignIds->collapse()->filter()->unique()->toArray()
        );

        $potentialBRSCampaigns->removeCampaigns(collect(array_keys(array_filter($availabilities, fn($scheduleId) => empty($scheduleId)))));

        foreach($companyCampaignIds as $saleTypeId => $campaignIds) {
            foreach($campaignIds as $campaignId) {
                $potentialBRSCampaigns->setCampaignSchedule(
                    $campaignId,
                    $saleTypeId,
                    $availabilities[$campaignId]
                );
            }
        }

        return $potentialBRSCampaigns;
    }
}
