<?php

namespace App\Abstracts\Workflows;

use App\Contracts\Workflows\IsLogicNode;
use App\Models\WorkflowAction;

abstract class LogicNode extends Action implements IsLogicNode
{
    /**
     * Handles returning the next action if this logic node passes.
     *
     * @return WorkflowAction|null
     */
    public function getNextAction(): ?WorkflowAction
    {
        if ($this->compare())
            /** @var WorkflowAction */
            return $this->getAction()->children()->first();

        return parent::getNextAction();
    }
}
