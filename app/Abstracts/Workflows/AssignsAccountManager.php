<?php

namespace App\Abstracts\Workflows;

use App\Contracts\Workflows\InteractsWithRoundRobin;
use App\Enums\RoundRobinType;

abstract class AssignsAccountManager extends Action implements InteractsWithRoundRobin
{
    /**
     * @inheritDoc
     */
    public function burnsTurn(): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function roundRobin(): RoundRobinType
    {
        return RoundRobinType::ACCOUNT_MANAGER;
    }
}
