<?php

namespace App\Abstracts\Workflows;

use App\Contracts\Workflows\ConditionalShortcodeContract;
use App\Enums\EventName;
use App\Workflows\Shortcodes\AccountManagerEmailFooterShortcode;
use App\Workflows\Shortcodes\AccountManagerNameShortcode;
use App\Workflows\Shortcodes\AccountManagerPhoneShortcode;
use App\Workflows\Shortcodes\AccountManagerTypeShortcode;
use App\Workflows\Shortcodes\AppointmentCountShortcode;
use App\Workflows\Shortcodes\AppointmentRejectionPercentageShortcode;
use App\Workflows\Shortcodes\AppointmentsRequestedShortcode;
use App\Workflows\Shortcodes\AppointmentsShortcode;
use App\Workflows\Shortcodes\AppointmentTypeShortcode;
use App\Workflows\Shortcodes\BestTimeToCallShortcode;
use App\Workflows\Shortcodes\BusinessDevelopmentManagerEmailShortcode;
use App\Workflows\Shortcodes\CampaignBudgetShortcode;
use App\Workflows\Shortcodes\CampaignIdShortcode;
use App\Workflows\Shortcodes\CampaignNameShortcode;
use App\Workflows\Shortcodes\CampaignReactivationDateShortcode;
use App\Workflows\Shortcodes\CampaignStatusReasonShortcode;
use App\Workflows\Shortcodes\CampaignStatusTypeShortcode;
use App\Workflows\Shortcodes\ChargebackAmountShortcode;
use App\Workflows\Shortcodes\ChargebackReasonShortcode;
use App\Workflows\Shortcodes\CompaniesOverBudgetShortcode;
use App\Workflows\Shortcodes\CompanyAdminProfileUrlShortcode;
use App\Workflows\Shortcodes\CompanyContactEmailForPromotionsShortcode;
use App\Workflows\Shortcodes\CompanyContactEmailShortcode;
use App\Workflows\Shortcodes\CompanyContactNameForPromotionsShortcode;
use App\Workflows\Shortcodes\CompanyContactNameShortcode;
use App\Workflows\Shortcodes\CompanyIdShortcode;
use App\Workflows\Shortcodes\CompanyNameShortcode;
use App\Workflows\Shortcodes\CompanyStatusShortcode;
use App\Workflows\Shortcodes\CompanyTypeShortcode;
use App\Workflows\Shortcodes\ConsumerEmailVerificationLinkShortcode;
use App\Workflows\Shortcodes\ConsumerFirstNameShortcode;
use App\Workflows\Shortcodes\ConsumerLastNameShortcode;
use App\Workflows\Shortcodes\ConsumerTopCompanyShortcode;
use App\Workflows\Shortcodes\CurrentTimeShortcode;
use App\Workflows\Shortcodes\EventNewValueShortcode;
use App\Workflows\Shortcodes\EventOldValueShortcode;
use App\Workflows\Shortcodes\EventDateShortcode;
use App\Workflows\Shortcodes\EventValueNameShortcode;
use App\Workflows\Shortcodes\InitiatorShortcode;
use App\Workflows\Shortcodes\InvoiceBillingContactNameShortcode;
use App\Workflows\Shortcodes\InvoiceIdShortcode;
use App\Workflows\Shortcodes\InvoiceIssueDateShortcode;
use App\Workflows\Shortcodes\InvoiceTotal;
use App\Workflows\Shortcodes\InvoiceTotalCreditsApplied;
use App\Workflows\Shortcodes\InvoiceTotalIssued;
use App\Workflows\Shortcodes\InvoiceTotalPaid;
use App\Workflows\Shortcodes\LastResultShortcode;
use App\Workflows\Shortcodes\LeadCountyShortcode;
use App\Workflows\Shortcodes\LeadPhoneNumberShortcode;
use App\Workflows\Shortcodes\LeadsReceivedShortcode;
use App\Workflows\Shortcodes\LeadStateShortcode;
use App\Workflows\Shortcodes\MissedCallFromPhoneShortcode;
use App\Workflows\Shortcodes\MissedCallToPhoneShortcode;
use App\Workflows\Shortcodes\NumberOfHunterTasksInLeadCountyToday;
use App\Workflows\Shortcodes\OnboardingManagerEmailShortcode;
use App\Workflows\Shortcodes\PaymentFailedCountShortcode;
use App\Workflows\Shortcodes\LeadRejectionPercentageShortcode;
use App\Workflows\Shortcodes\RecipientFirstNameShortcode;
use App\Workflows\Shortcodes\RecipientNameShortcode;
use App\Workflows\Shortcodes\RelationshipManagerNameShortcode;
use App\Workflows\Shortcodes\RelationshipManagerPhoneShortcode;
use App\Workflows\Shortcodes\ReviewIdShortcode;
use App\Workflows\Shortcodes\ReviewRatingShortcode;
use App\Workflows\Shortcodes\SalesBaitReceivedCountShortcode;
use App\Workflows\Shortcodes\SMSBodyShortcode;
use App\Workflows\Shortcodes\SMSForShortcode;
use App\Workflows\Shortcodes\SMSFromShortcode;
use App\Workflows\Shortcodes\TaskAssignedUserShortcode;
use App\Workflows\Shortcodes\TaskIdShortcode;
use App\Workflows\Shortcodes\TaskNameShortcode;
use App\Workflows\Shortcodes\TaskReassignedFromUserShortcode;
use App\Workflows\Shortcodes\UnsubscribeShortcode;
use App\Workflows\Shortcodes\UserActioningShortcode;
use App\Workflows\WorkflowPayload;
use App\Workflows\Shortcodes\LeadSoldToCountShortcode;
use App\Workflows\Shortcodes\LeadRoofSizeShortcode;
use App\Workflows\Shortcodes\LeadElectricBillShortcode;
use App\Workflows\Shortcodes\LeadAppointmentTimeShortcode;
use App\Workflows\Shortcodes\LeadIndustryShortcode;
use App\Workflows\Shortcodes\LeadIdShortcode;
use App\Workflows\Shortcodes\AccountManagerEmailShortcode;

abstract class WorkflowShortcodeServiceAbstract
{
    const array WORKFLOW_SHORTCODES = [
        AccountManagerNameShortcode::class,
        AccountManagerTypeShortcode::class,
        AccountManagerPhoneShortcode::class,
        AccountManagerEmailFooterShortcode::class,
        RelationshipManagerNameShortcode::class,
        RelationshipManagerPhoneShortcode::class,
        AppointmentsRequestedShortcode::class,
        CampaignBudgetShortcode::class,
        CampaignNameShortcode::class,
        CampaignIdShortcode::class,
        CompanyNameShortcode::class,
        CompanyIdShortcode::class,
        CompanyTypeShortcode::class,
        CompanyStatusShortcode::class,
        CompanyContactNameShortcode::class,
        CompanyContactNameForPromotionsShortcode::class,
        CompanyContactEmailShortcode::class,
        CompanyContactEmailForPromotionsShortcode::class,
        RecipientNameShortcode::class,
        RecipientFirstNameShortcode::class,
        CurrentTimeShortcode::class,
        InitiatorShortcode::class,
        LastResultShortcode::class,
        LeadsReceivedShortcode::class,
        PaymentFailedCountShortcode::class,
        LeadRejectionPercentageShortcode::class,
        AppointmentRejectionPercentageShortcode::class,
        SalesBaitReceivedCountShortcode::class,
        TaskAssignedUserShortcode::class,
        TaskReassignedFromUserShortcode::class,
        TaskIdShortcode::class,
        TaskNameShortcode::class,
        UserActioningShortcode::class,
        EventNewValueShortcode::class,
        EventOldValueShortcode::class,
        EventDateShortcode::class,
        LeadSoldToCountShortcode::class,
        LeadRoofSizeShortcode::class,
        LeadElectricBillShortcode::class,
        LeadAppointmentTimeShortcode::class,
        LeadIndustryShortcode::class,
        LeadIdShortcode::class,
        LeadPhoneNumberShortcode::class,
        LeadStateShortcode::class,
        CampaignStatusTypeShortcode::class,
        CampaignReactivationDateShortcode::class,
        CampaignStatusReasonShortcode::class,
        SMSForShortcode::class,
        SMSBodyShortcode::class,
        SMSFromShortcode::class,
        BestTimeToCallShortcode::class,
        CompaniesOverBudgetShortcode::class,
        MissedCallFromPhoneShortcode::class,
        MissedCallToPhoneShortcode::class,
        NumberOfHunterTasksInLeadCountyToday::class,
        AppointmentTypeShortcode::class,
        AppointmentCountShortcode::class,
        AppointmentsShortcode::class,
        UnsubscribeShortcode::class,
        EventValueNameShortcode::class,
        ChargebackAmountShortcode::class,
        ChargebackReasonShortcode::class,
        InvoiceIdShortcode::class,
        ReviewIdShortcode::class,
        ReviewRatingShortcode::class,
        ConsumerEmailVerificationLinkShortcode::class,

        InvoiceTotalIssued::class,
        InvoiceTotalPaid::class,
        InvoiceIdShortcode::class,
        InvoiceIssueDateShortcode::class,
        InvoiceBillingContactNameShortcode::class,
        InvoiceTotalCreditsApplied::class,
        InvoiceTotal::class,

        ConsumerFirstNameShortcode::class,
        ConsumerLastNameShortcode::class,
        LeadCountyShortcode::class,
        ConsumerTopCompanyShortcode::class,

        AccountManagerEmailShortcode::class,
        BusinessDevelopmentManagerEmailShortcode::class,
        OnboardingManagerEmailShortcode::class,
        CompanyAdminProfileUrlShortcode::class,
    ];

    /**
     * @param string $message
     * @param WorkflowPayload|null $payload
     * @return string
     */
    public abstract function handle(string $message, ?WorkflowPayload $payload = null): string;

    /**
     * Returns the available shortcodes.
     * If an EventName is provided, any conditional shortcodes that fail their predicate function will be filtered out
     * @param ?EventName $eventFilter
     * @return array
     */
    public function getShortcodes(?EventName $eventFilter = null): array
    {
        return collect(self::WORKFLOW_SHORTCODES)->map(function(string $shortcode) use ($eventFilter) {
            /** @var WorkflowPipelineShortcode $class */
            $class = app()->make($shortcode);

            if ($class->isDeprecated())
                return null;

            if (
                $eventFilter
                && in_array(ConditionalShortcodeContract::class, class_implements($class))
                && !$class->conditionalEventValidation($eventFilter)
            ) {
                return null;
            }
            return ["label" => $class->getLabel(), "value" => $class->getKey()];
        })->filter()->values()->toArray();
    }
}
