<?php

namespace App\Abstracts\Workflows;

use App\Contracts\Workflows\ActionContract;
use App\Contracts\Workflows\ActionVisitor;
use App\Enums\EventCategory;
use App\Factories\ValueTypeFactory;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\WorkflowAction;
use App\Repositories\TargetRepository;
use App\Services\ValueTypeProcessingService;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Collection;

abstract class Action implements ActionContract
{
    /**
     * @param WorkflowAction $workflowAction
     * @param WorkflowPayload $payload
     */
    public function __construct(
        protected WorkflowAction $workflowAction,
        protected WorkflowPayload $payload,
        protected ?int $runningWorkflowId = null
    ) {}

    /**
     * @return int|null
     */
    public function getRunningWorkflowId(): ?int
    {
        return $this->runningWorkflowId;
    }

    /**
     * Handles returning the payload for this action.
     *
     * @return WorkflowPayload
     */
    public function getPayload(): WorkflowPayload
    {
        return $this->payload;
    }

    /**
     * Handles returning the database record of this action.
     *
     * @return WorkflowAction
     */
    public function getAction(): WorkflowAction
    {
        return $this->workflowAction;
    }

    /**
     * Handles returning the next action.
     *
     * @return WorkflowAction|null
     */
    public function getNextAction(): ?WorkflowAction
    {
        /** @var WorkflowAction|null $action */
        $action = $this->getAction()->siblings()->first();

        return $action;
    }

    /**
     * Handles returning the database record of this action's children.
     *
     * @return Collection<WorkflowAction>|null
     */
    public function getChildActions(): ?Collection
    {
        return $this->getAction()->children;
    }

    /**
     * Accepts the visitor and calls the respective method.
     *
     * @param ActionVisitor $visitor
     * @return mixed
     */
    public function accept(ActionVisitor $visitor): mixed
    {
        return $visitor->visit($this);
    }

    /**
     * Returns target id from the event.
     *
     * @return int|string
     */
    protected function getTargetIdFromEvent(): int|string
    {
        return match (EventCategory::from($this->getPayload()->event->eventCategory)) {
            EventCategory::COMPANIES, EventCategory::LEADS, EventCategory::BILLING => $this->getPayload()->event->get('company_reference', 0),
            EventCategory::CAMPAIGNS => $this->getTargetIdFromCampaign(),
            default                  => 0
        };
    }

    /**
     * @return string
     */
    protected function getTargetIdFromCampaign(): string
    {
        $query = LeadCampaign::query();

        if ($this->getPayload()->event->get('campaign_reference'))
            $query->where(LeadCampaign::UUID, $this->getPayload()->event->get('campaign_reference'));
        else
            $query->where(LeadCampaign::ID, $this->getPayload()->event->get('campaign_id'));

        return $query->first()?->company?->{EloquentCompany::REFERENCE} ?? '';
    }

    /**
     * Returns the target repository for use in children.
     *
     * @return TargetRepository
     */
    protected function getTargetRepository(): TargetRepository
    {
        return app()->make(TargetRepository::class);
    }

    /**
     * Processes a value.
     *
     * @param array $payload
     * @return mixed
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    protected function processValue(array $payload): mixed
    {
        /** @var ValueTypeProcessingService $service */
        $service = app()->make(ValueTypeProcessingService::class);
        $value = $service->process(ValueTypeFactory::fromPayload($payload), $this->getPayload());

        return is_numeric($value) ? floatval($value) : $value;
    }
}
