<?php

namespace App\Abstracts\Workflows;

use App\Abstracts\SimpleShortcode;
use App\Models\Odin\Company;
use App\Models\RunningWorkflow;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Str;

abstract class WorkflowPipelineShortcode extends SimpleShortcode
{
    protected bool $isDeprecated = false;

    /**
     * Returns the value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected abstract function getValue(WorkflowPayload $payload): string;

    /**
     * @inheritDoc
     */
    public function handle(mixed $data, \Closure $next)
    {
        $data["message"] = Str::contains($data["message"], "{{$this->getKey()}}")
            ? $this->replace($data["message"], $this->getValue($data["payload"]))
            : $data["message"];

        $next($data);
    }

    /**
     * Disables sending the shortcode to the frontend
     * Will still work for backend replacements
     *
     * @return bool
     */
    public final function isDeprecated(): bool
    {
        return $this->isDeprecated;
    }

    /**
     * @param WorkflowPayload $payload
     *
     * @return Company|null
     */
    protected function getCompany(WorkflowPayload $payload): ?Company
    {
        if ($payload->event->get('company_reference')) {
            return Company::query()->where(Company::FIELD_REFERENCE, $payload->event->get('company_reference'))->first();
        }

        if ($payload->event->get('company_id')) {
            return Company::query()->where(Company::FIELD_ID, $payload->event->get('company_id'))->first();
        }

        return null;
    }
}
