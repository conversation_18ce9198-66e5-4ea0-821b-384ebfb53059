<?php

namespace App\Abstracts;

use Illuminate\Database\Eloquent\Factories\Factory;
use OverflowException;

abstract class ResetUniqueFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        $this->faker->seed(mt_rand());

        try {
            return $this->getData();
        }
        catch(OverflowException $e) {
            return $this->getData(true);
        }
    }

    /**
     * @param bool $reset
     * @return array
     */
    abstract public function getData(bool $reset = false): array;
}
