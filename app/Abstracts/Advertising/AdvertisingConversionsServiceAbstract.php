<?php

namespace App\Abstracts\Advertising;

use App\DataModels\Advertising\ConversionRevenueInfo;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\AdvertisingAccount;
use App\Models\AdvertisingAccountWebsite;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Services\Advertising\AdvertisingLoggingService;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;

abstract class AdvertisingConversionsServiceAbstract
{
    const LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID = ConversionRevenueInfo::CONSUMER_PRODUCT_TRACKING_ID;
    const LEAD_REVENUE_INFO_TRACK_TYPE = ConversionRevenueInfo::TRACK_TYPE;
    const LEAD_REVENUE_INFO_TRACK_CODE = ConversionRevenueInfo::TRACK_CODE;
    const LEAD_REVENUE_INFO_TRACK_PAYLOAD = ConversionRevenueInfo::TRACK_PAYLOAD;
    const LEAD_REVENUE_INFO_TRACK_CREATION_TIMESTAMP = ConversionRevenueInfo::TRACK_CREATION_TIMESTAMP;
    const LEAD_REVENUE_INFO_REVENUE = ConversionRevenueInfo::REVENUE;
    const LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP = ConversionRevenueInfo::DELIVERED_TIMESTAMP;
    const LEAD_REVENUE_INFO_REJECTED_TIMESTAMP = ConversionRevenueInfo::REJECTED_TIMESTAMP;
    const LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP = ConversionRevenueInfo::ORIGINAL_UPLOAD_TIMESTAMP;
    const LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS = ConversionRevenueInfo::PRODUCT_ASSIGNMENT_IDS;
    const LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS = ConversionRevenueInfo::PRODUCT_REJECTION_IDS;
    const LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE = ConversionRevenueInfo::CONVERSION_UPLOADED_BEFORE;
    const LEAD_REVENUE_INFO_CONVERSION_URL = ConversionRevenueInfo::CONVERSION_URL;

    const SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS = 'successful_product_assignment_ids';
    const SUCCESSFUL_PRODUCT_REJECTION_IDS = 'successful_product_rejection_ids';
    const SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS = 'successful_consumer_product_tracking_ids';

    /**
     * @param AdvertisingAccount $advertisingAccount
     * @param AdvertisingPlatform $platform
     * @return ConversionRevenueInfo
     */
    public function getLeadsWithTrackingIdAndEstimatedRevenue(AdvertisingAccount $advertisingAccount, AdvertisingPlatform $platform): ConversionRevenueInfo
    {
        $accountId = $advertisingAccount->{AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID};

        $estimatedRevenueCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ESTIMATED_REVENUE;
        $consumerProductTrackingIdCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID;
        $trackTypeCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_TYPE;
        $trackCodeCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE;
        $trackPayloadCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::PAYLOAD;
        $trackCreatedAtCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::CREATED_AT;
        $trackUrlConvertCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::URL_CONVERT;
        $consumerProductTrackingConversionUploadedCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::CONVERSION_UPLOADED;

        $origins = AdvertisingAccountWebsite::query()
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM, $platform->value)
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->pluck(AdvertisingAccountWebsite::FIELD_WEBSITE_ID)
            ->toArray();

        $startTime = Carbon::now('UTC')->subHours(72 + $advertisingAccount->{AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS})->toDateTimeString();

        $conversionRevenueInfo = new ConversionRevenueInfo();

        ConsumerProductTracking::query()
            ->selectRaw(implode(',', [
                "$consumerProductTrackingIdCol AS ".self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID,
                "$trackTypeCol AS ".self::LEAD_REVENUE_INFO_TRACK_TYPE,
                "$trackCodeCol AS ".self::LEAD_REVENUE_INFO_TRACK_CODE,
                "$trackPayloadCol AS ".self::LEAD_REVENUE_INFO_TRACK_PAYLOAD,
                "MIN(UNIX_TIMESTAMP($trackCreatedAtCol)) AS ".self::LEAD_REVENUE_INFO_TRACK_CREATION_TIMESTAMP,
                "MAX(UNIX_TIMESTAMP($trackCreatedAtCol)) AS ".self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP,
                "MAX(UNIX_TIMESTAMP($trackCreatedAtCol)) AS ".self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP,
                "MIN(UNIX_TIMESTAMP($trackCreatedAtCol)) AS ".self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP,
                "MAX($estimatedRevenueCol) AS ".self::LEAD_REVENUE_INFO_REVENUE,
                "'' AS ".self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS,
                "'' AS ".self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS,
                "MAX($consumerProductTrackingConversionUploadedCol) AS ".self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE,
                "$trackUrlConvertCol AS ".self::LEAD_REVENUE_INFO_CONVERSION_URL
            ]))
            ->join(ConsumerProduct::TABLE, function($join) {
                $join->on(
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID,
                    '=',
                    ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID
                );
            })
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->where(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::CONVERSION_UPLOADED, false)
            ->whereIn(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_TYPE, $this->getTrackTypes())
            ->whereNotNull(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE)
            ->whereNotNull($trackCreatedAtCol)
            ->whereNull(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CLONED_FROM_ID)
            ->where(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE, '!=', '')
            ->whereIn(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::WEBSITE_ID, $origins)
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CREATED_AT, '>=', $startTime)
            ->groupBy(self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID)
            ->having(self::LEAD_REVENUE_INFO_REVENUE, '>', 0)
            ->get()
            ->each(function($r) use (&$conversionRevenueInfo) {
                $conversionRevenueInfo->set(
                    $r->{self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID},
                    $r->{self::LEAD_REVENUE_INFO_TRACK_TYPE},
                    $r->{self::LEAD_REVENUE_INFO_TRACK_CODE},
                    json_decode($r->{self::LEAD_REVENUE_INFO_TRACK_PAYLOAD}, true) ?? [],
                    $r->{self::LEAD_REVENUE_INFO_TRACK_CREATION_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_REVENUE},
                    $r->{self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP},
                    array_filter(explode(',', $r->{self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS})),
                    array_filter(explode(',', $r->{self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS})),
                    $r->{self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE},
                    $r->{self::LEAD_REVENUE_INFO_CONVERSION_URL}
                );
            });

        return $conversionRevenueInfo;
    }

    /**
     * @param AdvertisingAccount $advertisingAccount
     * @param AdvertisingPlatform $platform
     * @return ConversionRevenueInfo
     */
    public function getSoldLeadsWithTrackingIdAndRevenue(AdvertisingAccount $advertisingAccount, AdvertisingPlatform $platform): ConversionRevenueInfo
    {
        $accountId = $advertisingAccount->{AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID};

        $costCol = ProductAssignment::FIELD_COST;
        $consumerProductTrackingIdCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID;
        $trackTypeCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_TYPE;
        $trackCodeCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE;
        $trackPayloadCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::PAYLOAD;
        $trackCreatedAtCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::CREATED_AT;
        $deliveryTimestampCol = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED_AT;
        $rejectedTimestampCol = ProductRejection::TABLE.'.'.ProductRejection::CREATED_AT;
        $productRejectionsIdCol = ProductRejection::TABLE.'.'.ProductRejection::FIELD_ID;
        $productAssignmentIdsCol = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID;
        $productAssignmentConversionUploadedCol = ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONVERSION_UPLOADED;
        $trackUrlConvertCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::URL_CONVERT;
        $consumerProductTrackingConversionUploadedCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::CONVERSION_UPLOADED;

        $origins = AdvertisingAccountWebsite::query()
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM, $platform->value)
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->pluck(AdvertisingAccountWebsite::FIELD_WEBSITE_ID)
            ->toArray();

        $recentlyModifiedTrackingIds = $this->getRecentlyModifiedTrackingIds(
            Carbon::now('UTC')->subHours(72 + $advertisingAccount->{AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS}),
            $deliveryTimestampCol,
            $rejectedTimestampCol,
            $origins
        );

        $conversionRevenueInfo = new ConversionRevenueInfo();

        ConsumerProductTracking::query()
            ->selectRaw(implode(',', [
                "$consumerProductTrackingIdCol AS ".self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID,
                "$trackTypeCol AS ".self::LEAD_REVENUE_INFO_TRACK_TYPE,
                "$trackCodeCol AS ".self::LEAD_REVENUE_INFO_TRACK_CODE,
                "$trackPayloadCol AS ".self::LEAD_REVENUE_INFO_TRACK_PAYLOAD,
                "MIN(UNIX_TIMESTAMP($trackCreatedAtCol)) AS ".self::LEAD_REVENUE_INFO_TRACK_CREATION_TIMESTAMP,
                "MAX(UNIX_TIMESTAMP($deliveryTimestampCol)) AS ".self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP,
                "COALESCE(MAX(UNIX_TIMESTAMP($rejectedTimestampCol)), 0) AS ".self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP,
                "MIN(UNIX_TIMESTAMP($deliveryTimestampCol)) AS ".self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP,
                "SUM(IF($productRejectionsIdCol IS NULL, $costCol, 0)) AS ".self::LEAD_REVENUE_INFO_REVENUE,
                "GROUP_CONCAT(DISTINCT $productAssignmentIdsCol SEPARATOR ',') AS ".self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS,
                "GROUP_CONCAT(DISTINCT $productRejectionsIdCol SEPARATOR ',') AS ".self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS,
                "MAX($productAssignmentConversionUploadedCol OR $consumerProductTrackingConversionUploadedCol) AS ".self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE,
                "$trackUrlConvertCol AS ".self::LEAD_REVENUE_INFO_CONVERSION_URL
            ]))
            ->join(ConsumerProduct::TABLE, function($join) {
                $join->on(
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID,
                    '=',
                    ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID
                );
            })
            ->join(ProductAssignment::TABLE, function($join) {
                $join->on(
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                    '=',
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID
                )
                ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
                ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true);
            })
            ->leftJoin(ProductRejection::TABLE, function($join) {
                $join
                    ->on(
                        ProductRejection::TABLE.'.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                        '=',
                        ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID
                    )
                    ->whereNull(ProductRejection::TABLE.'.'.ProductRejection::FIELD_DELETED_AT);
            })
            ->whereIn(  ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID, $recentlyModifiedTrackingIds)
            ->whereNotNull($trackCreatedAtCol)
            ->whereNull(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CLONED_FROM_ID)
            ->groupBy(self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID)
            ->having(self::LEAD_REVENUE_INFO_REVENUE, '>', 0)
            ->get()
            ->each(function($r) use (&$conversionRevenueInfo) {
                $conversionRevenueInfo->set(
                    $r->{self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID},
                    $r->{self::LEAD_REVENUE_INFO_TRACK_TYPE},
                    $r->{self::LEAD_REVENUE_INFO_TRACK_CODE},
                    json_decode($r->{self::LEAD_REVENUE_INFO_TRACK_PAYLOAD}, true) ?? [],
                    $r->{self::LEAD_REVENUE_INFO_TRACK_CREATION_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_REVENUE},
                    $r->{self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP},
                    array_filter(explode(',', $r->{self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS})),
                    array_filter(explode(',', $r->{self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS})),
                    $r->{self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE},
                    $r->{self::LEAD_REVENUE_INFO_CONVERSION_URL}
                );
            });

        return $conversionRevenueInfo;
    }

    /**
     * @param Carbon $startTime
     * @param string $deliveryTimestampCol
     * @param string $rejectedTimestampCol
     * @param array $origins
     * @return Collection
     */
    private function getRecentlyModifiedTrackingIds(
        Carbon $startTime,
        string $deliveryTimestampCol,
        string $rejectedTimestampCol,
        array $origins
    ): Collection
    {
        return ProductAssignment::query()
            ->join(ConsumerProduct::TABLE, function($join) {
                $join->on(
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                    '=',
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID
                );
            })
            ->join(ConsumerProductTracking::TABLE, function($join) {
                $join->on(
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID,
                    '=',
                    ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID
                );
            })
            ->leftJoin(ProductRejection::TABLE, function($join) {
                $join->on(
                    ProductRejection::TABLE.'.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                    '=',
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID
                );
            })
            ->where(function($where) use ($deliveryTimestampCol, $rejectedTimestampCol, $startTime) {
                $where
                    ->where($deliveryTimestampCol, '>=', $startTime)
                    ->orWhere($rejectedTimestampCol, '>=', $startTime);
            })
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONVERSION_UPLOADED, false)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true)
            ->whereIn(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_TYPE, $this->getTrackTypes())
            ->whereNotNull(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE)
            ->where(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE, '!=', '')
            ->whereIn(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::WEBSITE_ID, $origins)
            ->select([
                ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID
            ])
            ->distinct()
            ->get()
            ->pluck(ConsumerProductTracking::ID)
            ->unique();
    }

    /**
     * @param AdvertisingAccount $advertisingAccount
     * @param AdvertisingPlatform $platform
     * @return ConversionRevenueInfo
     */
    public function getUnsoldGoodToSellLeadsWithTrackingId(AdvertisingAccount $advertisingAccount, AdvertisingPlatform $platform): ConversionRevenueInfo
    {
        $consumerProductTrackingIdCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID;
        $trackTypeCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_TYPE;
        $trackCodeCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE;
        $trackPayloadCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::PAYLOAD;
        $trackCreatedAtCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::CREATED_AT;
        $trackUrlConvertCol = ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::URL_CONVERT;
        $consumerProductCreatedAtCol = ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CREATED_AT;

        $origins = AdvertisingAccountWebsite::query()
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM, $platform->value)
            ->where(AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID, $advertisingAccount->{AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID})
            ->pluck(AdvertisingAccountWebsite::FIELD_WEBSITE_ID)
            ->toArray();

        $startTime = Carbon::now('UTC')->subHours(72 + $advertisingAccount->{AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS})->toDateTimeString();

        $conversionRevenueInfo = new ConversionRevenueInfo();

        ConsumerProduct::query()
            ->join(ConsumerProductTracking::TABLE, function($join) {
                $join->on(
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID,
                    '=',
                    ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID
                );
            })
            ->leftJoin(ProductAssignment::TABLE, function($join) {
                $join->on(
                    ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                    '=',
                    ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID
                );
            })
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_GOOD_TO_SELL, true)
            ->where(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::CONVERSION_UPLOADED, false)
            ->whereIn(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_TYPE, $this->getTrackTypes())
            ->whereNotNull(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE)
            ->whereNotNull($trackCreatedAtCol)
            ->whereNull(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CLONED_FROM_ID)
            ->where(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::AD_TRACK_CODE, '!=', '')
            ->whereIn(ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::WEBSITE_ID, $origins)
            ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CREATED_AT, '>=', $startTime)
            ->whereNull(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->selectRaw(implode(',', [
                "$consumerProductTrackingIdCol AS ".self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID,
                "$trackTypeCol AS ".self::LEAD_REVENUE_INFO_TRACK_TYPE,
                "$trackCodeCol AS ".self::LEAD_REVENUE_INFO_TRACK_CODE,
                "$trackPayloadCol AS ".self::LEAD_REVENUE_INFO_TRACK_PAYLOAD,
                "MIN(UNIX_TIMESTAMP($trackCreatedAtCol)) AS ".self::LEAD_REVENUE_INFO_TRACK_CREATION_TIMESTAMP,
                "MAX(UNIX_TIMESTAMP({$consumerProductCreatedAtCol})) AS ".self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP,
                "0 AS ".self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP,
                "0 AS ".self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP,
                "0 AS ".self::LEAD_REVENUE_INFO_REVENUE,
                "'' AS ".self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS,
                "'' AS ".self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS,
                "0 AS ".self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE,
                "$trackUrlConvertCol AS ".self::LEAD_REVENUE_INFO_CONVERSION_URL
            ]))
            ->groupBy(self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID)
            ->distinct()
            ->get()
            ->each(function($r) use (&$conversionRevenueInfo) {
                $conversionRevenueInfo->set(
                    $r->{self::LEAD_REVENUE_INFO_CONSUMER_PRODUCT_TRACKING_ID},
                    $r->{self::LEAD_REVENUE_INFO_TRACK_TYPE},
                    $r->{self::LEAD_REVENUE_INFO_TRACK_CODE},
                    json_decode($r->{self::LEAD_REVENUE_INFO_TRACK_PAYLOAD}, true) ?? [],
                    $r->{self::LEAD_REVENUE_INFO_TRACK_CREATION_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_REVENUE},
                    $r->{self::LEAD_REVENUE_INFO_DELIVERED_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_REJECTED_TIMESTAMP},
                    $r->{self::LEAD_REVENUE_INFO_ORIGINAL_UPLOAD_TIMESTAMP},
                    array_filter(explode(',', $r->{self::LEAD_REVENUE_INFO_PRODUCT_ASSIGNMENT_IDS})),
                    array_filter(explode(',', $r->{self::LEAD_REVENUE_INFO_PRODUCT_REJECTION_IDS})),
                    $r->{self::LEAD_REVENUE_INFO_CONVERSION_UPLOADED_BEFORE},
                    $r->{self::LEAD_REVENUE_INFO_CONVERSION_URL}
                );
            });

        return $conversionRevenueInfo;
    }

    /**
     * @param array $productAssignmentIds
     * @param array $productRejectionIds
     * @param array $consumerProductTrackingIds
     * @return bool
     */
    public function markConversionsUploaded(array $productAssignmentIds, array $productRejectionIds, array $consumerProductTrackingIds): bool
    {
        if(!empty($productAssignmentIds)) {
            ProductAssignment::query()
                ->whereIn(ProductAssignment::FIELD_ID, $productAssignmentIds)
                ->update([
                    ProductAssignment::FIELD_CONVERSION_UPLOADED => true
                ]);

            $trackingIds = ProductAssignment::query()
                ->whereIntegerInRaw(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID, $productAssignmentIds)
                ->join(ConsumerProduct::TABLE, function($join) {
                    $join->on(
                        ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
                        '=',
                        ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID
                    );
                })
                ->join(ConsumerProductTracking::TABLE, function($join) {
                    $join->on(
                        ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID,
                        '=',
                        ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID
                    );
                })
                ->select([
                    ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID
                ])
                ->distinct()
                ->pluck(ConsumerProductTracking::ID)
                ->toArray();

            $consumerProductTrackingIds = array_unique(array_merge($consumerProductTrackingIds, $trackingIds));
        }

        if(!empty($productRejectionIds)) {
            ProductRejection::query()
                ->whereIn(ProductRejection::FIELD_ID, $productRejectionIds)
                ->update([
                    ProductRejection::FIELD_CONVERSION_UPLOADED => true
                ]);

            $trackingIds = ProductRejection::query()
                ->whereIntegerInRaw(ProductRejection::TABLE.'.'.ProductRejection::FIELD_ID, $productRejectionIds)
                ->join(ProductAssignment::TABLE, function($join) {
                    $join->on(
                        ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID,
                        '=',
                        ProductRejection::TABLE.'.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID
                    );
                })
                ->join(ConsumerProduct::TABLE, function($join) {
                    $join->on(
                        ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID,
                        '=',
                        ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID
                    );
                })
                ->join(ConsumerProductTracking::TABLE, function($join) {
                    $join->on(
                        ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID,
                        '=',
                        ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID
                    );
                })
                ->select([
                    ConsumerProductTracking::TABLE.'.'.ConsumerProductTracking::ID
                ])
                ->distinct()
                ->pluck(ConsumerProductTracking::ID)
                ->toArray();

            $consumerProductTrackingIds = array_unique(array_merge($consumerProductTrackingIds, $trackingIds));
        }

        if(!empty($consumerProductTrackingIds)) {
            ConsumerProductTracking::query()
                ->whereIn(ConsumerProductTracking::ID, $consumerProductTrackingIds)
                ->update([
                    ConsumerProductTracking::CONVERSION_UPLOADED => true
                ]);
        }

        return true;
    }

    /**
     * @param AdvertisingAccount $advertisingAccount
     * @param AdvertisingPlatform $platform
     * @return bool
     * @throws Exception
     */
    public function checkIfTimeToUploadConversions(AdvertisingAccount $advertisingAccount, AdvertisingPlatform $platform): bool
    {
        $nextRunTime = (int) $advertisingAccount->{AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_LAST_RUN_TIMESTAMP} + ($advertisingAccount->{AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS} * Carbon::MINUTES_PER_HOUR * Carbon::SECONDS_PER_MINUTE);

        if($nextRunTime > time()) {
            $displayName = AdvertisingPlatform::displayName($platform->value);

            AdvertisingLoggingService::writeLog(
                "Offline conversion upload - Not time yet to upload {$displayName} account {$advertisingAccount->{AdvertisingAccount::FIELD_ID}} conversions",
                [
                    'platform' => $platform->value,
                    'account_id' => $advertisingAccount->{AdvertisingAccount::FIELD_ID}
                ]
            );

            return false;
        }

        return true;
    }

    /**
     * @param array $accountIds
     * @return bool
     */
    public function updateUploadConversionsLastRunTimestamp(array $accountIds): bool
    {
        AdvertisingAccount::query()
            ->whereIn(AdvertisingAccount::FIELD_ID, $accountIds)
            ->update([
                AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_LAST_RUN_TIMESTAMP => time()
            ]);

        return true;
    }

    /**
     * @param string $accountId
     * @param ConversionRevenueInfo $conversionRevenueInfo
     * @param AdvertiserEnum $advertiser
     * @return array
     */
    abstract public function uploadConversions(string $accountId, ConversionRevenueInfo $conversionRevenueInfo, AdvertiserEnum $advertiser): array;

    /**
     * @return array
     */
    abstract public function getTrackTypes(): array;
}
