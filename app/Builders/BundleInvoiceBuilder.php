<?php

namespace App\Builders;

use App\Enums\BundleInvoiceStatus;
use App\Http\Requests\SearchBundleInvoicesRequest;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class BundleInvoiceBuilder
 * Used to simplify building a query for the BundleInvoices model
 *
 * @package App\Builders
 */
class BundleInvoiceBuilder
{

    /**
     * @param string|null $bundleName
     * @param float|null $costFrom
     * @param float|null $costTo
     * @param float|null $creditFrom
     * @param float|null $creditTo
     * @param mixed|null $status
     * @param array|null $issuedAt
     * @param mixed|null $companyName
     * @param string|null $sortCol
     * @param string|null $sortDir
     */
    public function __construct(
        protected ?string $bundleName = null,
        protected ?float $costFrom = null,
        protected ?float $costTo = null,
        protected ?float $creditFrom = null,
        protected ?float $creditTo = null,
        protected mixed $status = null,
        protected ?array $issuedAt = null,
        protected mixed $companyName = null,
        protected ?string $sortCol = null,
        protected ?string $sortDir = null,
        protected ?bool $autoApprovedOnly = null,
    ){
    }

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param mixed $bundleName
     * @return $this
     */
    public function forBundleName(string $bundleName = null): self
    {
        $this->bundleName = $bundleName;

        return $this;
    }

    /**
     * @param mixed $costFrom
     * @return $this
     */
    public function forCostFrom(float $costFrom = null): self
    {
        $this->costFrom = $costFrom;

        return $this;
    }

    /**
     * @param mixed $costTo
     * @return $this
     */
    public function forCostTo(float $costTo = null): self
    {
        $this->costTo = $costTo;

        return $this;
    }

    /**
     * @param mixed $creditFrom
     * @return $this
     */
    public function forCreditFrom(float $creditFrom = null): self
    {
        $this->creditFrom = $creditFrom;

        return $this;
    }

    /**
     * @param mixed $creditTo
     * @return $this
     */
    public function forCreditTo(float $creditTo = null): self
    {
        $this->creditTo = $creditTo;

        return $this;
    }

    /**
     * @param mixed $status
     * @return $this
     */
    public function forStatus(mixed $status = null): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * Expects a date range array of two date values, 0 index being date_from and 1 index being date_to.
     *
     * @param array|null $issuedAt
     * @return $this
     */
    public function forIssuedAt(array $issuedAt = null):self
    {
        $this->issuedAt = $issuedAt;

        return $this;
    }

    /**
     * @param mixed $companyName
     * @return $this
     */
    public function forCompanyName(mixed $companyName = null):self
    {
        $this->companyName = $companyName;

        return $this;
    }

    /**
     * @param bool|null $autoApprovedOnly
     * @return $this
     */
    public function forAutoApprovedOnly(bool $autoApprovedOnly = null):self
    {
        $this->autoApprovedOnly = $autoApprovedOnly;

        return $this;
    }

    /**
     * @param mixed $sortColumn
     * @return $this
     */
    public function sortColumn(string $sortColumn): self
    {
        $this->sortCol = $sortColumn;

        return $this;
    }

    /**
     * @param mixed $sortDirection
     * @return $this
     */
    public function sortDirection(string $sortDirection): self
    {
        $this->sortDir = $sortDirection;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query with all join, where, select, and orderby statements attached to retrieve BundleInvoices.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = BundleInvoice::query();
        $selectCols = [BundleInvoice::TABLE.'.*'];
        $query = $this->createJoinsForQuery($query);

        if($this->bundleName !== null) {
            $selectCols[] = Bundle::TABLE.'.'.Bundle::FIELD_NAME;
            $query->where(Bundle::TABLE.'.'.Bundle::FIELD_NAME, 'LIKE', '%'.$this->bundleName.'%');
        }
        if($this->costFrom !== null && $this->costFrom > 0){
            $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_COST, '>=', $this->costFrom);
        }
        if($this->costTo !== null && $this->costTo > 0) {
            $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_COST, '<=', $this->costTo);
        }
        if($this->creditFrom !== null && $this->creditFrom > 0) {
            $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_CREDIT, '>=', $this->creditFrom);
        }
        if($this->creditTo !== null && $this->creditTo > 0) {
            $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_CREDIT, '<=', $this->creditTo);
        }
        if($this->status !== null) {
            // Handle the case where the status int value has already been passed
            // If the status string is passed, get the int value before adding the 'where' clause
            if (gettype($this->status) === 'integer' || gettype($this->status) === 'double') {
                $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_STATUS, '=', $this->status);
            } else {
                $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_STATUS, '=', BundleInvoiceStatus::fromDisplayString($this->status));
            }
        }
        if($this->issuedAt !== null && is_array($this->issuedAt)) {
            $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_ISSUED_AT, '>=', $this->issuedAt[0]);
            $query->where(BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_ISSUED_AT, '<=', $this->issuedAt[1]);
        }
        if($this->companyName !== null && $this->companyName !== '') {
            $selectCols[] = Company::TABLE.'.'.Company::FIELD_NAME;
            $query->where(Company::TABLE . '.' . Company::FIELD_ID, '=', $this->companyName)
                ->orWhere(Company::TABLE . '.' . Company::FIELD_NAME, 'LIKE', '%' . $this->companyName . '%');
        }

        if ($this->autoApprovedOnly === true) {
            $query->whereHas(BundleInvoice::RELATION_BUNDLE, function (Builder $query) {
                $query->where(Bundle::FIELD_IS_AUTO_APPROVED, $this->autoApprovedOnly);
            });
        }

        if($this->sortCol && $this->sortDir !== null) {
            switch ($this->sortCol) {
                case SearchBundleInvoicesRequest::REQUEST_COMPANY_NAME:
                    $selectCols[] = Company::TABLE.'.'.Company::FIELD_NAME;
                    $query->orderBy(Company::TABLE.'.'.Company::FIELD_NAME, $this->sortDir);
                    break;
                case SearchBundleInvoicesRequest::REQUEST_BUNDLE_NAME:
                    $selectCols[] = Bundle::TABLE.'.'.Bundle::FIELD_NAME;
                    $query->orderBy(Bundle::TABLE.'.'.Bundle::FIELD_NAME, $this->sortDir);
                    break;
                default:
                    $query = $query->orderBy($this->sortCol, $this->sortDir);
            }
        }

        $query = $query->select($selectCols);

        return $query;
    }

    /**
     * Join the 'Bundle' and 'Companies' tables if the respective filters and sort are set.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function createJoinsForQuery(Builder $query): Builder
    {
        if($this->bundleName !== null || $this->sortCol === SearchBundleInvoicesRequest::REQUEST_BUNDLE_NAME) {
            $query->join(Bundle::TABLE, Bundle::TABLE . '.' . Bundle::FIELD_ID, '=', BundleInvoice::TABLE . '.' . BundleInvoice::FIELD_BUNDLE_ID);
        }

        if (($this->companyName !== null && $this->companyName !== '') || $this->sortCol === SearchBundleInvoicesRequest::REQUEST_COMPANY_NAME) {
            $query->join(Company::TABLE, Company::TABLE.'.'.Company::FIELD_ID, '=', BundleInvoice::TABLE.'.'.BundleInvoice::FIELD_COMPANY_ID);
        }

        return $query;
    }


}
