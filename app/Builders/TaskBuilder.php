<?php

namespace App\Builders;

use App\Enums\SupportedTimezones;
use App\Models\CompletedWorkflow;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\Company;
use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Models\TaskNote;
use App\Models\User;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class TaskBuilder
{
    public function __construct(
        protected ?string $query = null,
        protected bool $searchNames = true,
        protected bool $searchCompanies = false,
        protected ?string $sortByColumn = null,
        protected string $sortDirection = 'asc',
        protected ?int $forId = null,
        protected ?int $priority = null,
        protected ?Carbon $date = null,
        protected ?string $dateOperator = '=',
        protected bool $completed = false,
        protected ?int $companyId = null,
        protected ?int $categoryId = null,
        protected ?SupportedTimezones $timezone = null,
        protected bool $includeMuted = false,
    ) { }

    /**
     * Creates a new task builder.
     *
     * @return TaskBuilder
     */
    public static function query(): TaskBuilder
    {
        return new self();
    }

    /**
     * Whether to search task names in the query builder.
     *
     * @param bool $search
     * @return $this
     */
    public function allowNames(bool $search = true): self
    {
        $this->searchNames = $search;

        return $this;
    }

    /**
     * Whether to search tasks by company names/ids in the query builder.
     *
     * @param bool $search
     * @return $this
     */
    public function allowCompanies(bool $search = true): self
    {
        $this->searchCompanies = $search;

        return $this;
    }

    public function allowCompleted(bool $allow = true): self
    {
        $this->completed = $allow;

        return $this;
    }

    /**
     * Handles the sort order of the resulting query from the query builder.
     *
     * @param string|null $column
     * @param string $sortDirection
     * @return $this
     */
    public function sortBy(?string $column = null, string $sortDirection = 'asc'): self
    {
        $this->sortByColumn = $column;
        $this->sortDirection = $sortDirection;

        return $this;
    }

    /**
     * Sets the query for searching by.
     *
     * @param string|null $query
     * @return $this
     */
    public function setQuery(?string $query = null): self
    {
        $this->query = $query;

        return $this;
    }

    /**
     * Allows querying by the assigned user id.
     *
     * @param int|null $forId
     * @return $this
     */
    public function for(?int $forId = null): self
    {
        $this->forId = $forId;

        return $this;
    }

    /**
     * Allows querying by the company id
     *
     * @param int|null $companyId
     * @return $this
     */
    public function setCompanyId(?int $companyId = null): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * Allows querying by the category id
     *
     * @param int|null $categoryId
     * @return $this
     */
    public function setCategoryId(?int $categoryId = null): self
    {
        $this->categoryId = $categoryId;

        return $this;
    }

    /**
     * Filters by the priority for a task.
     *
     * @param int|null $priority
     * @return $this
     */
    public function setPriority(?int $priority = null): self
    {
        $this->priority = $priority;

        return $this;
    }

    public function setTimezone(?SupportedTimezones $timezone = null): self
    {
        $this->timezone = $timezone;

        return $this;
    }

    /**
     * Sets the date to filter tasks by.
     *
     * @param Carbon|null $date
     * @param string $operator
     * @return $this
     */
    public function setDate(?Carbon $date = null, string $operator = "="): self
    {
        $this->date = $date;
        $this->dateOperator = $operator;

        return $this;
    }

    /**
     * @param bool $includeMuted
     * @return $this
     */
    public function setIncludeMuted(bool $includeMuted = true): self
    {
        $this->includeMuted = $includeMuted;
        return $this;
    }

    /**
     * Runs the query builder, and returns a list of tasks.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()
                    ->get();
    }

    /**
     * Return the count of the query
     *
     * @return Int
     */
    public function count(): Int
    {
        return $this->count();
    }

    /**
     * Returns the query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = Task::query()
            ->selectRaw(Task::TABLE.'.*')
            ->with([
                Task::RELATION_ASSIGNED_USER => function ($with) {
                    $with->select([
                        User::FIELD_ID,
                        User::FIELD_NAME,
                    ]);
                },
                Task::RELATION_TASK_CATEGORY,
                Task::RELATION_COMPANY  => function ($with) {
                    $with->select([
                        Company::FIELD_ID,
                        Company::FIELD_NAME,
                    ]);
                },
                Task::RELATION_RUNNING_WORKFLOW.'.'.RunningWorkflow::RELATION_LEGACY_LEAD => function ($with) {
                    $with->select([
                        EloquentQuote::QUOTE_ID,
                        EloquentQuote::ADDRESS_ID
                    ]);
                },
                Task::RELATION_COMPLETED_WORKFLOW.'.'.CompletedWorkflow::RELATION_LEGACY_LEAD => function ($with) {
                    $with->select([
                        EloquentQuote::QUOTE_ID,
                        EloquentQuote::ADDRESS_ID
                    ]);
                },
                Task::RELATION_RUNNING_WORKFLOW.'.'.RunningWorkflow::RELATION_LEGACY_LEAD.'.'.EloquentQuote::RELATION_ADDRESS,
                Task::RELATION_COMPLETED_WORKFLOW.'.'.CompletedWorkflow::RELATION_LEGACY_LEAD.'.'.EloquentQuote::RELATION_ADDRESS,
                Task::RELATION_RUNNING_WORKFLOW.'.'.RunningWorkflow::RELATION_COMPANY => function ($with) {
                    $with->select([
                        Company::FIELD_ID,
                        Company::FIELD_NAME,
                    ]);
                },
            ]);

        if($this->searchCompanies === true && (strlen(trim($this->query ?? "")) > 0 || $this->companyId !== null))
            $query = $this->joinPayloadAndCompany($query);

        if(($this->searchNames === true || $this->searchCompanies === true) && $this->query !== null && strlen(trim($this->query ?? "")) > 0) {
            $query = $query->where(function(Builder $query) {
                if($this->searchCompanies === true) {
                    $query = $query->where(function(Builder $query) {
                        return $query->where(Company::TABLE.'.'.Company::FIELD_ID, $this->query)
                            ->orWhere(Company::TABLE.'.'.Company::FIELD_NAME, 'LIKE', "%{$this->query}%")
                            ->orWhere('PC.'.Company::FIELD_ID, $this->query)
                            ->orWhere('PC.'.Company::FIELD_NAME, 'LIKE', "%{$this->query}%");
                    });
                }

                if($this->searchNames === true)
                    $query = $query->orWhere(Task::FIELD_SUBJECT, 'LIKE', "%{$this->query}%");

                return $query;
            });
        }

        if($this->companyId !== null) {
            $query = $query->where(function(Builder $query) {
                return $query->where(Company::TABLE.'.'.Company::FIELD_ID, $this->companyId)
                    ->orWhere('PC.'.Company::FIELD_ID, $this->companyId);
            });
        }

        if($this->forId !== null)
            $query = $query->where(Task::FIELD_ASSIGNED_USER_ID, $this->forId);

        if($this->priority !== null)
            $query = $query->where(Task::FIELD_PRIORITY, $this->priority);

        if ($this->date !== null)
            $query = $query->whereDate(Task::FIELD_AVAILABLE_AT, $this->dateOperator, $this->date);

        if($this->timezone !== null)
            $query = $query->where(Task::FIELD_TIMEZONE, $this->timezone);

        $query = $query->where(Task::FIELD_COMPLETED, $this->completed ? Task::TASK_COMPLETED : Task::TASK_NOT_COMPLETED);

        if($this->sortByColumn !== null)
            $query = $query->orderBy($this->sortByColumn, $this->sortDirection);

        if ($this->categoryId !== null) $query->where(Task::FIELD_TASK_CATEGORY_ID, $this->categoryId);

        if (!$this->includeMuted) {
            $query->where(Task::FIELD_MUTED, false);
        } else {
            $query->where(Task::FIELD_MUTED, true);
        }

        return $query;
    }

    /**
     * Encapsulation of the query additions for getting payloads, and companies joined to the table.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function joinPayloadAndCompany(Builder $query): Builder
    {
        return $query->leftJoin(RunningWorkflow::TABLE, function(JoinClause $join) {
            return $join->on(
                RunningWorkflow::TABLE.'.'.RunningWorkflow::FIELD_ID,
                '=',
                Task::TABLE.'.'.Task::FIELD_RUNNING_WORKFLOW_ID
            );
        })->leftJoin(Company::TABLE, function(JoinClause $join) {
            return $join->on(
                Company::TABLE.'.'.Company::FIELD_ID,
                '=',
                RunningWorkflow::TABLE.'.'.RunningWorkflow::VIRTUAL_FIELD_EVENT_COMPANY_ID
            );
        })->leftJoin(Company::TABLE .' as PC', function(JoinClause $join) {
            return $join->on(
                'PC.'.Company::FIELD_ID,
                '=',
                Task::TABLE.'.'.Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID
            );
        });
    }

    /**
     * Returns a paginated list of this builder.
     *
     * @param int $items
     * @param array $columns
     * @param string $pageName
     * @param int|null $page
     * @return LengthAwarePaginator
     */
    public function paginate(int $items, array $columns = ['*'], string $pageName = 'page', ?int $page = null): LengthAwarePaginator
    {
        return $this->getQuery()
                    ->paginate($items, $columns, $pageName, $page);
    }
}
