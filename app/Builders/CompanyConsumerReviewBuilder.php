<?php

namespace App\Builders;

use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use Illuminate\Database\Eloquent\Builder;

class CompanyConsumerReviewBuilder
{
    /**
     * @param int|null $companyId
     * @param int|null $status
     * @param int|null $score
     * @param int|null $date
     * @param string|null $text
     * @param int|null $notId
     * @param string|null $ip
     * @param string|null $sortBy
     */
    public function __construct(
        protected ?int $companyId   = null,
        protected ?int $status      = null,
        protected ?int $score       = null,
        protected ?int $date        = null,
        protected ?string $text     = null,
        protected ?int $notId       = null,
        protected ?string $ip       = null,
        protected ?string $sortBy   = null,
        protected ?int $verified    = null,
    ){}

    /**
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function forCompanyId(?int $id = null): self
    {
        $this->companyId = $id;

        return $this;
    }

    /**
     * @param int|null $status
     * @return $this
     */
    public function forStatus(?int $status = null): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @param bool|null $verifiedStatus
     * @return $this
     */
    public function forVerifiedStatus(?bool $verifiedStatus = null): self
    {
        $this->verified = $verifiedStatus;

        return $this;
    }


    /**
     * @param int|null $score
     * @return $this
     */
    public function forScore(?int $score = null): self
    {
        $this->score = $score;

        return $this;
    }

    /**
     * @param int|null $date
     * @return $this
     */
    public function forDate(?int $date = null): self
    {
        $this->date = $date;

        return $this;
    }

    /**
     * @param string|null $text
     * @return $this
     */
    public function forText(?string $text = null): self
    {
        $this->text = $text;

        return $this;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function forNotId(int $id = null): self
    {
        $this->notId = $id;

        return $this;
    }

    /**
     * @param string|null $ip
     * @return $this
     */
    public function forIp(?string $ip = null): self
    {
        $this->ip = $ip;

        return $this;
    }

    /**
     * @param string|null $sortBy
     * @return $this
     */
    public function forSortBy(?string $sortBy = null): self
    {
        $this->sortBy = $sortBy;
        return $this;
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = Review::query();

        $query = $query->with([
            Review::RELATION_REVIEWER,
            Review::RELATION_REVIEW_DATA,
        ]);

        if ($this->companyId !== null) {
            $query = $query->where(Review::FIELD_COMPANY_ID, $this->companyId);
        }

        if ($this->status !== null) {
            $query->where(Review::FIELD_STATUS, $this->status);
        }

        if ($this->score !== null) {
            if ($this->score === 0) {
                $query->whereHas(Review::RELATION_REVIEW_DATA, function ($query) {
                    $query->where(ReviewData::FIELD_OVERALL_SCORE, '=', $this->score);
                });
            } else {
                $query->whereHas(Review::RELATION_REVIEW_DATA, function ($query) {
                    $query->where(ReviewData::FIELD_OVERALL_SCORE, '>=', $this->score);
                    $query->where(ReviewData::FIELD_OVERALL_SCORE, '<', ceil($this->score) + 1);
                });
            }
        }

        if ($this->text !== null) {
            $query->where(function ($query) {
                $query->whereHas(Review::RELATION_REVIEW_DATA, function ($query) {
                    $query->where(ReviewData::FIELD_COMMENTS, 'like', '%' . $this->text . '%');
                })->orWhereHas(Review::RELATION_REVIEW_DATA, function ($query) {
                    $query->where(ReviewData::FIELD_TITLE, 'like', '%' . $this->text . '%');
                });
            });
        }

        if ($this->verified !== null) {
            $query->where(Review::FIELD_IS_VERIFIED, $this->verified);
        }

        if ($this->sortBy === 'ASC') {
            $query->orderBy(Review::FIELD_CREATED_AT, 'ASC');
        } else {
            $query->orderBy(Review::FIELD_CREATED_AT, 'DESC');
        }

        if ($this->notId !== null) {
            $query->whereNot(Review::FIELD_ID, $this->notId);
        }

        if ($this->ip !== null) {
            $query->whereHas(Review::RELATION_REVIEW_DATA, function ($query) {
                $query->whereJsonContains(ReviewData::FIELD_DATA, [ReviewData::DATA_KEY_USER_IP => $this->ip]);
            });
        }

        return $query;
    }
}
