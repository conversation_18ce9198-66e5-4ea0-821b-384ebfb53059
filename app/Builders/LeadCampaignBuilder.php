<?php


namespace App\Builders;

use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLeadCategory;
use App\Models\Legacy\LeadCategory;
use App\Repositories\ComputedRejectionStatisticRepository;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class LeadCampaignBuilder
{

    protected array  $allowedCompanyStatuses           = [];
    protected bool   $onlyPurchasingCompanies          = false;
    protected bool   $onlySolarCompanies               = false;
    protected bool   $onlyCampaignsSetToReactivateSoon = false;
    protected bool   $onlyNoLimitBudgetCampaigns       = false;
    protected bool   $excludeLowBidCampaigns           = false;
    protected array  $allowedCampaignStatuses          = [];
    protected array  $relations                        = [LeadCampaign::RELATION_COMPANY];
    protected ?float $rejectionThreshold               = null;
    protected array  $mustIncludeCategories            = [];

    public static function query(): LeadCampaignBuilder
    {
        return new self();
    }

    /**
     * @return LeadCampaignBuilder
     */
    public function onlyActiveCompanies(): LeadCampaignBuilder
    {
        $this->allowedCompanyStatuses = [EloquentCompany::STATUS_ACTIVE];
        return $this;
    }

    /**
     * @return LeadCampaignBuilder
     */
    public function onlySolarCompanies(): LeadCampaignBuilder
    {
        $this->onlySolarCompanies = true;
        return $this;
    }

    /**
     * Exclude companies where the legacy column 'buyingsolarestimateleads' is set to false
     * @return LeadCampaignBuilder
     */
    public function onlyPurchasingCompanies(): LeadCampaignBuilder
    {
        $this->onlyPurchasingCompanies = true;
        return $this;
    }

    public function onlyCampaignsSetToReactivateSoon(): LeadCampaignBuilder
    {
        $this->onlyCampaignsSetToReactivateSoon = true;
        return $this;
    }

    public function onlyNoLimitBudgetCampaigns(): LeadCampaignBuilder
    {
        $this->onlyNoLimitBudgetCampaigns = true;
        return $this;
    }

    /**
     * @return LeadCampaignBuilder
     */
    public function onlyActiveCampaigns(): LeadCampaignBuilder
    {
        $this->allowedCampaignStatuses = [LeadCampaign::STATUS_ACTIVE];
        return $this;
    }

    /**
     * @param float $rejectionThreshold
     * @return LeadCampaignBuilder
     */
    public function onlyCompaniesWithRejectionBelowThreshold(float $rejectionThreshold): LeadCampaignBuilder
    {
        $this->rejectionThreshold = $rejectionThreshold;
        return $this;
    }

    public function with(array $with): LeadCampaignBuilder
    {
        $this->relations = $with;

        return $this;
    }

    /**
     * Todo: Right now this function is only being used for Leads. Rejection function should be updated if this function
     * is used for multiple products
     *
     * @return Collection
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function get(): Collection
    {
        $query = LeadCampaign::query();

        $query->whereHas(LeadCampaign::RELATION_COMPANY, function($query) {
            if( count($this->allowedCompanyStatuses) > 0 ){
                $query->whereIn(EloquentCompany::STATUS, $this->allowedCompanyStatuses);
            }

            if($this->onlyPurchasingCompanies){
                $query->where(EloquentCompany::BUYING_SOLAR_ESTIMATE_LEADS, 1);
            }

            if($this->onlySolarCompanies) {
                $query->where(EloquentCompany::TYPE, EloquentCompany::TYPE_INSTALLER);
            }
        });

        if( count($this->allowedCampaignStatuses) > 0 ){
            $query->whereIn(LeadCampaign::STATUS, $this->allowedCampaignStatuses);
        }

        if( count($this->mustIncludeCategories) > 0 ){
            $query->whereHas(LeadCampaign::RELATION_LEAD_CAMPAIGN_LEAD_CATEGORIES, function ($query){
                $query->whereIn(LeadCampaignLeadCategory::LEAD_CATEGORY_ID, $this->mustIncludeCategories);
            });
        }

        if($this->onlyCampaignsSetToReactivateSoon) {
            $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE)
                ->whereNotNull(LeadCampaign::REACTIVATE_DATE)
                ->where(LeadCampaign::REACTIVATE_DATE, '<=', Carbon::now()->addDays(2)); // We want any campaigns that
        }

        if($this->onlyNoLimitBudgetCampaigns) {
            $query->where(LeadCampaign::MAX_DAILY_SPEND, 0)
                ->whereNull(LeadCampaign::MAX_DAILY_LEAD);
        }

        if($this->excludeLowBidCampaigns) {
            $query->whereDoesntHave(LeadCampaign::RELATION_LOW_BIDS);
        }

        $leadCampaigns = $query->with($this->relations)->get();

        if($this->rejectionThreshold){
            /** @var ComputedRejectionStatisticRepository $rejectionRepository */
            $rejectionRepository = app()->make(ComputedRejectionStatisticRepository::class);
            $rejectionThreshold = $this->rejectionThreshold;
            $ids = $leadCampaigns->pluck(LeadCampaign::COMPANY_ID)->unique();

            // Todo: Make product specific
            $rejections = $rejectionRepository->getLeadRejectionPercentageForCompaniesByLegacyIds($ids->toArray());

            $leadCampaigns = $leadCampaigns->filter(function($leadCampaign) use ($rejectionThreshold, $rejections, $rejectionRepository){

                $rejectionPercent = $rejectionRepository->getComputedRejectionStatisticsByLegacyIdFromRejectionsCollection(
                    $rejections,
                    $leadCampaign->{LeadCampaign::COMPANY_ID}
                );

                return ($rejectionPercent?->{ComputedRejectionStatistic::ACTIVE_REJECTION_FIELD_IMPACTING_ELIGIBILITY} ?? 0) < $rejectionThreshold;
            });
        }

        return $leadCampaigns;
    }

    /**
     * @return LeadCampaignBuilder
     */
    public function onlyResidentialCampaigns(): LeadCampaignBuilder
    {
        $this->mustIncludeCategories = [LeadCategory::RESIDENTIAL];
        return $this;
    }

    public function excludeLowBidCampaigns(): LeadCampaignBuilder
    {
        $this->excludeLowBidCampaigns = true;

        return $this;
    }
}
