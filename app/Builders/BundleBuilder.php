<?php

namespace App\Builders;

use App\Http\Requests\SearchBundleInvoicesRequest;
use App\Http\Requests\SearchBundlesRequest;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class BundleBuilder
 * Used to simplify building a query for the Bundle model
 *
 * @package App\Builders
 */
class BundleBuilder
{

    /**
     * @param string|null $bundleName
     * @param float|null $costFrom
     * @param float|null $costTo
     * @param float|null $creditFrom
     * @param float|null $creditTo
     * @param bool|null $active
     * @param int|null $industryId
     * @param string|null $sortCol
     * @param string|null $sortDir
     */
    public function __construct(
        protected ?string $bundleName = null,
        protected ?float $costFrom = null,
        protected ?float $costTo = null,
        protected ?float $creditFrom = null,
        protected ?float $creditTo = null,
        protected ?bool $active = null,
        protected ?int $industryId = null,
        protected ?string $sortCol = null,
        protected ?string $sortDir = null,
        protected ?bool $autoApprovedOnly = null,
    ){
    }

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param mixed $bundleName
     * @return $this
     */
    public function forBundleName(?string $bundleName = null): self
    {
        $this->bundleName = $bundleName;

        return $this;
    }

    /**
     * @param mixed $costFrom
     * @return $this
     */
    public function forCostFrom(float $costFrom = null): self
    {
        $this->costFrom = $costFrom;

        return $this;
    }

    /**
     * @param float|null $costTo
     * @return $this
     */
    public function forCostTo(float $costTo = null): self
    {
        $this->costTo = $costTo;

        return $this;
    }

    /**
     * @param float|null $creditFrom
     * @return $this
     */
    public function forCreditFrom(float $creditFrom = null): self
    {
        $this->creditFrom = $creditFrom;

        return $this;
    }

    /**
     * @param float|null $creditTo
     * @return $this
     */
    public function forCreditTo(float $creditTo = null): self
    {
        $this->creditTo = $creditTo;

        return $this;
    }

    /**
     * @param bool|null $active
     * @return $this
     */
    public function forActive(?bool $active = null): self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @param int|null $industryId
     * @return $this
     */
    public function forIndustry(?int $industryId = null): self
    {
        $this->industryId = $industryId;

        return $this;
    }

    public function forAutoApprovedOnly(?bool $autoApprovedOnly = null): self
    {
        $this->autoApprovedOnly = $autoApprovedOnly;

        return $this;
    }


    /**
     * @param mixed $sortColumn
     * @return $this
     */
    public function sortColumn(?string $sortColumn = Bundle::FIELD_NAME): self
    {
        $this->sortCol = $sortColumn;

        return $this;
    }

    /**
     * @param mixed $sortDirection
     * @return $this
     */
    public function sortDirection(?string $sortDirection = 'asc'): self
    {
        $this->sortDir = $sortDirection;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = Bundle::query();
        $selectCols = [Bundle::TABLE.'.*'];
        $query = $this->createJoinsForQuery($query);

        if($this->bundleName !== null && strlen(trim($this->bundleName)) > 0)
        {
            $query->where(Bundle::FIELD_NAME, 'LIKE', "%$this->bundleName%");
        }
        if($this->costFrom !== null && $this->costFrom > 0){
            $query->where(Bundle::FIELD_COST, '>=', $this->costFrom);
        }
        if($this->costTo !== null && $this->costTo > 0) {
            $query->where(Bundle::FIELD_COST, '<=', $this->costTo);
        }
        if($this->creditFrom !== null && $this->creditFrom > 0) {
            $query->where(Bundle::FIELD_CREDIT, '>=', $this->creditFrom);
        }
        if($this->creditTo !== null && $this->creditTo > 0) {
            $query->where(Bundle::FIELD_CREDIT, '<=', $this->creditTo);
        }
        if($this->active !== null && $this->active === true) {
            $query->whereNotNull(Bundle::FIELD_ACTIVATED_AT);
        }
        if($this->active !== null && $this->active === false) {
            $query->whereNull(Bundle::FIELD_ACTIVATED_AT);
        }
        if($this->industryId !== null && $this->industryId !== '') {
            $selectCols[] = Industry::TABLE.'.'.Industry::FIELD_NAME;
            $query->where(Industry::TABLE . '.' . Industry::FIELD_ID, '=', $this->industryId);
        }
        if ($this->autoApprovedOnly === true) {
            $query->where(Bundle::FIELD_IS_AUTO_APPROVED, $this->autoApprovedOnly);
        }
        if($this->sortCol && $this->sortDir !== null) {
            if ($this->sortCol === SearchBundlesRequest::REQUEST_INDUSTRY_ID) {
                $selectCols[] = Industry::TABLE.'.'.Industry::FIELD_NAME;
                $query->orderBy(Industry::TABLE.'.'.Industry::FIELD_NAME, $this->sortDir);
            } else {
                $query = $query->orderBy($this->sortCol, $this->sortDir);
            }
        }

        $query = $query->select($selectCols);

        return $query;
    }

    /**
     * Join the 'Industries' table if the respective filters and/or sort are set.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function createJoinsForQuery(Builder $query): Builder
    {
        if($this->industryId !== null || $this->sortCol === SearchBundlesRequest::REQUEST_INDUSTRY_ID) {
            $query->join(Industry::TABLE, Industry::TABLE . '.' . Industry::FIELD_ID, '=', Bundle::TABLE . '.' . Bundle::FIELD_INDUSTRY_ID);
        }

        return $query;
    }



}
