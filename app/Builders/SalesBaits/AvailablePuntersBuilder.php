<?php

namespace App\Builders\SalesBaits;

use App\DataModels\SalesBait\SalesBaitPunter;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\SalesBaitRegisteredInterest;
use App\Repositories\CompanyContactRepository;
use App\Repositories\Legacy\LegacyUserRepository;
use App\Services\PubSub\ContactSubscriptionService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class AvailablePuntersBuilder
{

    protected CompanyContactRepository $contactRepository;
    protected LegacyUserRepository $userRepository;
    protected ContactSubscriptionService $subscriptionService;

    /**
     * @param LeadCampaign|null $campaign
     * @param bool $includeUsers
     * @param bool $hasPhone
     * @param bool $hasEmail
     * @param EloquentCompany|null $company
     * @throws BindingResolutionException
     */
    public function __construct(
        protected ?LeadCampaign $campaign = null,
        protected bool $includeUsers = true,
        protected bool $hasPhone = false,
        protected bool $hasEmail = false,
        protected ?EloquentCompany $company = null
    ){
        $this->contactRepository = app()->make(CompanyContactRepository::class);
        $this->userRepository = app()->make(LegacyUserRepository::class);
        $this->subscriptionService = app()->make(ContactSubscriptionService::class);
    }

    /**
     * @param LeadCampaign|null $campaign
     * @return AvailablePuntersBuilder
     * @throws BindingResolutionException
     */
    public static function query(?LeadCampaign $campaign = null): AvailablePuntersBuilder
    {
        return new self($campaign);
    }

    /**
     * @return $this
     */
    public function includeUsersIfNoContactsAvailable(): AvailablePuntersBuilder
    {
        $this->includeUsers = true;
        return $this;
    }

    /**
     * @return $this
     */
    public function hasPhone(): AvailablePuntersBuilder
    {
        $this->hasPhone = true;
        return $this;
    }

    /**
     * @return $this
     */
    public function hasEmail(): AvailablePuntersBuilder
    {
        $this->hasEmail = true;
        return $this;
    }

    /**
     * Sets the company that this builder operates on.
     *
     * @param EloquentCompany $company
     * @return $this
     */
    public function forCompany(EloquentCompany $company): AvailablePuntersBuilder
    {
        $this->company = $company;

        return $this;
    }

    protected function getByCampaign(): Collection
    {
        $contacts = $this->contactRepository->getActiveContactsByCampaignId($this->campaign->id, $this->hasPhone, $this->hasEmail);
        $contactPunters = $this->convertContactsToPuntersBasedOnSubscription($contacts);

        $userPunters = collect();
        if( $contactPunters->isEmpty() && $this->includeUsers ){
            $users = $this->userRepository->getActiveUsersByCompanyId($this->campaign->company_id, $this->hasPhone, $this->hasEmail);
            $userPunters = $this->convertUsersToPuntersBasedOnSubscription($users);
        }

        return $contactPunters->merge($userPunters);
    }

    protected function getByCompany(): Collection
    {
        $contacts = $this->contactRepository->getActiveContactsForCompany($this->company->companyid, $this->hasPhone, $this->hasEmail);
        $contactPunters = $this->convertContactsToPuntersBasedOnSubscription($contacts);

        $userPunters = collect();
        if( $contactPunters->isEmpty() && $this->includeUsers ){
            $users = $this->userRepository->getActiveUsersByCompanyId($this->company->companyid, $this->hasPhone, $this->hasEmail);
            $userPunters = $this->convertUsersToPuntersBasedOnSubscription($users);
        }

        return $contactPunters->merge($userPunters);
    }

    /**
     * @return Collection<int, SalesBaitPunter>
     */
    public function get(): Collection
    {
        return $this->campaign ? $this->getByCampaign() : $this->getByCompany();
    }

    /**
     * @param Collection $contacts
     * @return Collection<int, SalesBaitPunter>
     */
    private function convertContactsToPuntersBasedOnSubscription(Collection $contacts): Collection
    {
        $contactSubscriptionStatuses = $this->subscriptionService->getStatusesForContacts($contacts);
        $punters = collect();
        foreach ($contacts as $contact){
            if(!array_key_exists($contact->contactid, $contactSubscriptionStatuses->toArray()) || $contactSubscriptionStatuses[$contact->contactid] == null)
                continue;

            $status = $contactSubscriptionStatuses[$contact->contactid];
            $phone = ($this->hasPhone && $status->smsSubscribed) ? $contact->phone : null;
            $email = ($this->hasEmail && $status->emailSubscribed) ? $contact->email : null;
            if($phone || $email){
                $punters->push(new SalesBaitPunter(
                    $contact->contactid,
                    $contact->firstname,
                    $email,
                    $phone,
                    SalesBaitRegisteredInterest::TYPE_CONTACT
                ));
            }
        }
        return $punters;
    }

    /**
     * @param Collection $users
     * @return Collection<int, SalesBaitPunter>
     */
    private function convertUsersToPuntersBasedOnSubscription(Collection $users): Collection
    {
        $userSubscriptionStatuses = $this->subscriptionService->getStatusesForUsers($users);
        $punters = collect();
        foreach ($users as $user){
            if(!array_key_exists($user->userid, $userSubscriptionStatuses->toArray()) || $userSubscriptionStatuses[$user->userid] == null)
                continue;

            $status = $userSubscriptionStatuses[$user->userid];
            $phone = ($this->hasPhone && $status->smsSubscribed) ? $user->phone : null;
            $email = ($this->hasEmail && $status->emailSubscribed) ? $user->email : null;
            if($phone || $email){
                $punters->push(new SalesBaitPunter(
                    $user->userid,
                    $user->firstname,
                    $email,
                    $phone,
                    SalesBaitRegisteredInterest::TYPE_USER
                ));
            }
        }
        return $punters;
    }

}
