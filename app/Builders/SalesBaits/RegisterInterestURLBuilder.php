<?php

namespace App\Builders\SalesBaits;

use App\Models\Legacy\EloquentQuote;
use App\Models\SalesBaitRegisteredInterest;
use Illuminate\Support\Facades\Hash;
use JetBrains\PhpStorm\ArrayShape;

/**
 * Register Interest URL Builder
 *
 * Handles building a unique URL for tracking sales bait interests.
 */
class RegisterInterestURLBuilder
{
    const SR_URL                    = "https://solarreviews.com/";
    const SR_DEV_URL                = "https://devwww.solarreviews.com/";
    const RC_URL                    = "https://roofingcalculator.com/";
    const RC_DEV_URL                = "https://devwww.roofing-calculator.com/";
    const DEFAULT_ENDPOINT          = 'register-purchase-interest';
    const TYPE_USER                 = 'user';
    const TYPE_CONTACT              = 'contact';
    const DEFAULT_REGISTRATION_TYPE = "companymarketing";

    const TYPE_MAPPINGS = [
        SalesBaitRegisteredInterest::TYPE_USER    => self::TYPE_USER,
        SalesBaitRegisteredInterest::TYPE_CONTACT => self::TYPE_CONTACT
    ];

    public function __construct(
        public string $baseUrl = self::SR_URL,
        public string $endPoint = self::DEFAULT_ENDPOINT,
        public string $userType = self::TYPE_USER,
        public ?int   $relationId = null,
        public ?int   $salesBaitId = null,
        public string $industry = EloquentQuote::LEAD_INDUSTRY_SOLAR,
        public string $registrationType = self::DEFAULT_REGISTRATION_TYPE,
    ) {}

    /**
     * Creates a new builder
     *
     * @return RegisterInterestURLBuilder
     */
    public static function new(): RegisterInterestURLBuilder
    {
        return new self();
    }

    /**
     * Handles setting the base URL.
     *
     * @param string $baseUrl
     * @return RegisterInterestURLBuilder
     */
    public function setBaseUrl(string $baseUrl): RegisterInterestURLBuilder
    {
        $this->baseUrl = $baseUrl;

        return $this;
    }

    /**
     * Handles setting the end point.
     *
     * @param string $endPoint
     * @return RegisterInterestURLBuilder
     */
    public function setEndPoint(string $endPoint): RegisterInterestURLBuilder
    {
        $this->endPoint = $endPoint;

        return $this;
    }

    /**
     * Defines that this url is for a user.
     *
     * @return $this
     */
    public function forUser(): RegisterInterestURLBuilder
    {
        $this->userType = self::TYPE_USER;

        return $this;
    }

    /**
     * Defines that this url is for a contact.
     *
     * @return $this
     */
    public function forContact(): RegisterInterestURLBuilder
    {
        $this->userType = self::TYPE_CONTACT;

        return $this;
    }

    /**
     * Handles setting the type of relation (user/contact/etc)
     *
     * @param string $type
     * @return RegisterInterestURLBuilder
     */
    public function setType(string $type): RegisterInterestURLBuilder
    {
        $this->userType = $type;

        return $this;
    }

    /**
     * Handles setting the relation id (user/contact/etc)
     *
     * @param int|null $relationId
     * @return RegisterInterestURLBuilder
     */
    public function setRelationId(?int $relationId): RegisterInterestURLBuilder
    {
        $this->relationId = $relationId;

        return $this;
    }

    /**
     * Handles setting the sales bait id
     *
     * @param int|null $salesBaitId
     * @return RegisterInterestURLBuilder
     */
    public function setSalesBaitId(?int $salesBaitId): RegisterInterestURLBuilder
    {
        $this->salesBaitId = $salesBaitId;

        return $this;
    }

    /**
     * Handles setting the industry.
     *
     * @param string $industry
     * @return RegisterInterestURLBuilder
     */
    public function setIndustry(string $industry): RegisterInterestURLBuilder
    {
        $this->industry = $industry;

        return $this;
    }

    /**
     * Handles setting the registration type.
     *
     * @param string $registrationType
     * @return RegisterInterestURLBuilder
     */
    public function setRegistrationType(string $registrationType): RegisterInterestURLBuilder
    {
        $this->registrationType = $registrationType;
        return $this;
    }

    /**
     * Handles building the URL for registering interest.
     *
     * @return string
     */
    public function build(): string
    {
        $url    = rtrim($this->baseUrl, "/") . "/" . ltrim($this->endPoint, "/");
        $params = $this->getParams();
        $params["token"] = $this->getToken();

        return $url . '?' . http_build_query($params);
    }

    /**
     * Returns the params for the url.
     *
     * @return array
     */
    #[ArrayShape(["registration-type" => "string", "user-type" => "string", "id" => "int|null", "tracking-id" => "int|null", "industry" => "string"])]
    protected function getParams(): array
    {
        return [
            "registration-type" => $this->registrationType,
            "user-type"         => $this->userType,
            "id"                => $this->relationId,
            "tracking-id"       => $this->salesBaitId,
            "industry"          => $this->industry
        ];
    }

    /**
     * Returns a unique token for the given parameters.
     *
     * @return string
     */
    protected function getToken(): string
    {
        $params = $this->getParams();

        return Hash::make(collect($params)->values()->join(""));
    }
}
