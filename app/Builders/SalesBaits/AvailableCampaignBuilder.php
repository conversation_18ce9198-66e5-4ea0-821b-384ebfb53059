<?php

namespace App\Builders\SalesBaits;

use App\Builders\SalesBaits\Concerns\SalesBaitBuilderGenericQueries;
use App\DataModels\SalesBait\AvailableSalesBaitCompany;
use App\Models\ContactSubscription;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentUser;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRestrictedCompany;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class AvailableCampaignBuilder
{
    use SalesBaitBuilderGenericQueries;

    const DEFAULT_SALES_BAIT_STATUSES        = [EloquentCompany::STATUS_INACTIVE, EloquentCompany::STATUS_PENDING, EloquentCompany::STATUS_PRESALES, EloquentCompany::STATUS_REGISTERING];
    const DEFAULT_MAX_CAMPAIGN_BAITS_PER_DAY = 2; // todo: not yet implemented

    /**
     * Constructor for the available companies' builder.
     *
     * @param EloquentQuote|null $lead
     * @param array $statuses
     * @param bool $includePausedActiveBuyers
     */
    public function __construct(
        protected ?EloquentQuote $lead = null,
        protected array          $statuses = self::DEFAULT_SALES_BAIT_STATUSES,
        protected bool           $includePausedActiveBuyers = true,
        protected bool           $excludeRestricted = true,
    ) {}

    /**
     * Initializes a new query for fetching available companies.
     *
     * @param EloquentQuote|null $lead
     * @param array $statuses
     * @param bool $includePausedActiveBuyers
     * @return AvailableCampaignBuilder
     */
    public static function query(
        ?EloquentQuote $lead = null,
        array          $statuses = self::DEFAULT_SALES_BAIT_STATUSES,
        bool           $includePausedActiveBuyers = true
    ): AvailableCampaignBuilder
    {
        return new AvailableCampaignBuilder($lead, $statuses, $includePausedActiveBuyers);
    }

    /**
     * Sets the lead for this query.
     *
     * @param EloquentQuote|null $lead
     * @return AvailableCampaignBuilder
     */
    public function setLead(?EloquentQuote $lead): AvailableCampaignBuilder
    {
        $this->lead = $lead;

        return $this;
    }

    /**
     * Sets the statuses of companies we'd like to sale bait too.
     *
     * @param array $statuses
     * @return AvailableCampaignBuilder
     */
    public function setStatuses(array $statuses): AvailableCampaignBuilder
    {
        $this->statuses = $statuses;

        return $this;
    }

    /**
     * Whether we want to include active buyers with paused campaigns in this query.
     *
     * @param bool $includePausedActiveBuyers
     * @return AvailableCampaignBuilder
     */
    public function includePausedActiveBuyers(bool $includePausedActiveBuyers = true): AvailableCampaignBuilder
    {
        $this->includePausedActiveBuyers = $includePausedActiveBuyers;

        return $this;
    }

    /**
     * Whether restricted companies should be included in this builder.
     *
     * @param bool $exclude
     * @return $this
     */
    public function excludeRestrictedCompanies(bool $exclude = true): AvailableCampaignBuilder
    {
        $this->excludeRestricted = $exclude;

        return $this;
    }

    /**
     * Handles running the logic for fetching available companies.
     *
     * @return Collection<int, AvailableSalesBaitCompany>
     */
    public function get(): Collection
    {
        if ($this->lead == null)
            throw new \RuntimeException("SalesBait Campaign Builder Expected A Lead. See AvailableCampaignBuilder::setLead");

        $companyQuery = EloquentCompany::query()->select(DB::raw(EloquentCompany::TABLE . '.*'));
        $companyQuery = $this->applySharedCompanyMappings($companyQuery);

        // Add location joins
        $companyQuery->join(LeadCampaign::TABLE, LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID, '=', EloquentCompany::TABLE . '.' . EloquentCompany::ID)
                     ->join(LeadCampaignLocation::TABLE, LeadCampaign::TABLE . '.' . LeadCampaign::ID, '=', LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID)
                     ->join(Location::TABLE, Location::TABLE . '.' . Location::ID, '=', LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LOCATION_ID)
                     ->where(Location::ZIP_CODE, $this->lead->address->zipcode)
                     ->groupBy(EloquentCompany::COMPANY_ID);

        $companies = $companyQuery->get();
        $campaigns = $this->getCampaigns($companies);

        return $campaigns
            ->groupBy(LeadCampaign::COMPANY_ID)
            ->map(fn($campaigns, $companyId) => new AvailableSalesBaitCompany($companies->where(EloquentCompany::COMPANY_ID, $companyId)->first(), $campaigns));
    }

    /**
     * Handles returning the campaigns for a company.
     *
     * @param Collection $companies
     * @return Collection
     */
    protected function getCampaigns(Collection $companies): Collection
    {
        return LeadCampaign::query()->select(DB::raw(LeadCampaign::TABLE . '.*'))
                           ->withCount([LeadCampaign::RELATION_SALES_BAIT_LEADS => fn($query) => $query->where('created_at', '>=', \Carbon\Carbon::now()->startOfDay())])
                           ->whereIn(LeadCampaign::COMPANY_ID, $companies->pluck(EloquentCompany::COMPANY_ID)->toArray())
                           ->join(LeadCampaignLocation::TABLE, LeadCampaign::TABLE . '.' . LeadCampaign::ID, '=', LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID)
                           ->join(Location::TABLE, Location::TABLE . '.' . Location::ID, '=', LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LOCATION_ID)
                           ->where(Location::ZIP_CODE, $this->lead->address->zipcode)
                           ->having(SalesBaitLead::TABLE . '_count', '<', 2)
                           ->groupBy(LeadCampaign::TABLE . '.' . LeadCampaign::ID)->get();
    }
}
