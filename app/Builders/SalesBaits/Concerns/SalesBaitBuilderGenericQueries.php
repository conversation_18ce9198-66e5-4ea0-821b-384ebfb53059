<?php

namespace App\Builders\SalesBaits\Concerns;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\SalesBaitRestrictedCompany;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;

trait SalesBaitBuilderGenericQueries
{
    protected function applySharedCompanyMappings(Builder $query): Builder
    {
        $types        = [];

        if ($this->lead->solar_lead)
            $types[] = EloquentCompany::TYPE_INSTALLER;
        if ($this->lead->roofing_lead)
            $types[] = EloquentCompany::TYPE_ROOFER;

        $query->whereIn(EloquentCompany::TABLE . '.' . EloquentCompany::TYPE, $types);

        if($this->excludeRestricted) {
            $query->leftJoin(
                DatabaseHelperService::database().'.'.SalesBaitRestrictedCompany::TABLE,
                SalesBaitRestrictedCompany::TABLE.'.'.SalesBaitRestrictedCompany::FIELD_COMPANY_ID,
                '=',
                EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID
            )->where(SalesBaitRestrictedCompany::TABLE.'.'.SalesBaitRestrictedCompany::FIELD_COMPANY_ID, null);
        }

        if (count($this->statuses) > 0) {
            $query = $this->addStatusMappingToQuery($query);
        }

        return $query;
    }

    /**
     * Handles adding the status mapping to the query.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function addStatusMappingToQuery(Builder $query): Builder
    {
        return $query->where(function (Builder $query) {
            $query->whereIn(EloquentCompany::TABLE . '.' . EloquentCompany::STATUS, $this->statuses);

            if ($this->includePausedActiveBuyers) {
                $query->orWhere(function ($query) {
                    $query->where(EloquentCompany::TABLE . '.' . EloquentCompany::STATUS, EloquentCompany::STATUS_ACTIVE)
                        ->whereDoesntHave(EloquentCompany::RELATION_CAMPAIGNS, function ($query) {
                            $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)->orWhere(function ($query) {
                                $query->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE)
                                    ->where(LeadCampaign::REACTIVATE_DATE, '<>', null);
                            });
                        });
                });
            }
        });
    }
}
