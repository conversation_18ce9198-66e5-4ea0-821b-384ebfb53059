<?php

namespace App\Builders;

use App\DataModels\CompanyRevenueDataModel;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyRevenueBuilder
{
    const GROUP_DAILY = "daily";
    const GROUP_MONTHLY = "monthly";
    const GROUP_YEARLY = "yearly";

    const PERIOD_ALL_TIME = "all-time";

    public function __construct(
        protected ?int $companyId = null,
        protected string $groupBy = self::GROUP_MONTHLY,
        protected string $period = self::PERIOD_ALL_TIME,
        protected int $duration = 1,
    ) {}

    /**
     * Returns a new instance of this builder.
     *
     * @return CompanyRevenueBuilder
     */
    public static function query(): CompanyRevenueBuilder
    {
        return new CompanyRevenueBuilder();
    }

    /**
     * Sets the company this builder should operate on.
     *
     * @param int $companyId
     * @return $this
     */
    public function forCompany(int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * Sets the grouping this builder should group by
     *
     * @param string $groupBy
     * @return $this
     */
    public function groupBy(string $groupBy = self::GROUP_MONTHLY): self
    {
        $this->groupBy = $groupBy;

        return $this;
    }

    /**
     * Sets the period type this builder should operate on.
     *
     * @param string $period
     * @return $this
     */
    public function setPeriod(string $period = self::PERIOD_ALL_TIME): self
    {
        $this->period = $period;

        return $this;
    }

    /**
     * Sets the duration this builder should operate on.
     *
     * @param int $duration
     * @return $this
     */
    public function setPeriodDuration(int $duration = 1): self
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * Runs the builder.
     *
     * @return Collection<CompanyRevenueDataModel>
     */
    public function get(): Collection
    {
        $query = EloquentQuoteCompany::query()
            ->where(EloquentQuoteCompany::COMPANY_ID, $this->companyId)
            ->where(EloquentQuoteCompany::CHARGEABLE, EloquentQuoteCompany::IS_CHARGEABLE)
            ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::IS_DELIVERED)
            ->whereNotNull(EloquentQuoteCompany::TIMESTAMP_DELIVERED)
            ->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '<>', 0)
            ->whereHas(EloquentQuoteCompany::RELATION_QUOTE, function(Builder $query) {
               return $query->whereIn(EloquentQuote::STATUS, [EloquentQuote::VALUE_STATUS_INITIAL, EloquentQuote::VALUE_STATUS_ALLOCATED]);
            })
            ->orderBy(EloquentQuoteCompany::TIMESTAMP_DELIVERED, 'asc');

        return $this->applyGroupingAndSelects($this->applyDuration($query))
            ->get()
            ->map(fn($item) => new CompanyRevenueDataModel(
                $this->companyId,
                $item->period,
                $item->revenue
            ));
    }

    /**
     * Applies the grouping to the query.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function applyGroupingAndSelects(Builder $query): Builder
    {
        $query = match ($this->groupBy) {
            self::GROUP_DAILY => $query->select([DB::raw("sum(cost) as revenue"), DB::raw('DATE_FORMAT(FROM_UNIXTIME(timestampdelivered), "%Y-%m-%d") as period')]),
            self::GROUP_MONTHLY => $query->select([DB::raw("sum(cost) as revenue"), DB::raw('DATE_FORMAT(FROM_UNIXTIME(timestampdelivered), "%M, %Y") as period')]),
            self::GROUP_YEARLY => $query->select([DB::raw("sum(cost) as revenue"), DB::raw('DATE_FORMAT(FROM_UNIXTIME(timestampdelivered), "%Y") as period')]),
            default => $query
        };

        return $query->groupBy("period");
    }

    /**
     * Applies the duration to the query.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function applyDuration(Builder $query): Builder
    {
        return match ($this->period) {
            self::GROUP_DAILY => $query->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', Carbon::now()->subDays($this->duration)->timestamp),
            self::GROUP_MONTHLY => $query->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', Carbon::now()->subMonths($this->duration)->timestamp),
            self::GROUP_YEARLY => $query->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', Carbon::now()->subYears($this->duration)->timestamp),
            default => $query
        };
    }
}
