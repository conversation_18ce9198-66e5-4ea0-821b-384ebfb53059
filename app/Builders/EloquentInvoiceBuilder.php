<?php

namespace App\Builders;

use App\Models\Legacy\EloquentInvoice;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class EloquentInvoiceBuilder
{
    public function __construct(
        protected ?int $legacyCompanyId = null,
        protected ?string $status = null,
        protected string|int|null $fromDate = null,
        protected string|int|null $toDate = null,
        protected ?int $invoiceId = null,
    ) {}

    /**
     * Creates an instance of industry builder.
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function forLegacyCompanyId(?int $id = null): self
    {
        $this->legacyCompanyId = $id;

        return $this;
    }


    /**
     * Allows querying for Consumer Product Statuses
     * @param string|null $status
     * @return $this
     */
    public function forStatus(?string $status = null): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @param int|string|null $fromDate
     * @return $this
     */
    public function forFromDate(int|string|null $fromDate = null): self
    {
        $this->fromDate = $fromDate;

        return $this;
    }

    /**
     * @param int|string|null $toDate
     * @return $this
     */
    public function forToDate(int|string|null $toDate = null): self
    {
        $this->toDate = $toDate;

        return $this;
    }

    /**
     * @param int|null $invoiceId
     * @return $this
     */
    public function forInvoiceNumber(?int $invoiceId = null): self
    {
        $this->invoiceId = $invoiceId;
        return $this;
    }

    /**
     * Returns a paginated instance of the query.
     *
     * @param int|null $perPage
     * @param array $columns
     * @param string $pageName
     * @param int|null $page
     * @return LengthAwarePaginator
     */
    public function paginate(?int $perPage = null, array $columns = ['*'], string $pageName = 'page', ?int $page = null): LengthAwarePaginator
    {
        return $this->getQuery()->paginate($perPage, $columns, $pageName, $page);
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = EloquentInvoice::query()->select(
            EloquentInvoice::TABLE.'.'.EloquentInvoice::ID,
            EloquentInvoice::TABLE.'.'.EloquentInvoice::COMPANY_ID,
            EloquentInvoice::TABLE.'.'.EloquentInvoice::TIMESTAMP_ADDED,
            EloquentInvoice::TABLE.'.'.EloquentInvoice::TIMESTAMP_PAYMENT_DUE,
            EloquentInvoice::TABLE.'.'.EloquentInvoice::STATUS,
        );

        if ($this->invoiceId) {
            $query = $query->where(EloquentInvoice::ID, $this->invoiceId);
            if ($query->count() === 1) return $query;
        }

        if ($this->legacyCompanyId !== null) {
            $query = $query->where(EloquentInvoice::COMPANY_ID, $this->legacyCompanyId);
        }
        if ($this->status !== null) {
            $query = $query->where(EloquentInvoice::STATUS, $this->status);
        }
        if ($this->fromDate !== null) {
            $query = $query->where(EloquentInvoice::TIMESTAMP_ADDED, '>', $this->fromDate);
        }
        if ($this->toDate !== null) {
            $query = $query->where(EloquentInvoice::TIMESTAMP_ADDED, '<', $this->toDate);
        }

        $query->orderBy(EloquentInvoice::TIMESTAMP_ADDED, 'DESC');

        return $query;

    }


}
