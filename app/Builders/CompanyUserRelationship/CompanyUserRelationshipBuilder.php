<?php

namespace App\Builders\CompanyUserRelationship;

use App\Models\CompanyUserRelationship;
use App\Models\Role;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;

class CompanyUserRelationshipBuilder
{
    /**
     * @param bool|null $trashed
     * @param string|null $name
     * @param int|null $companyId
     * @param array|null $roles
     * @param bool|null $active
     */
    public function __construct(
        protected ?bool $trashed = false,
        protected ?string $name = null,
        protected ?int $companyId = null,
        protected ?array $roles = null,
        protected ?bool $active = null,
    )
    {
    }

    /**
     * @param string|null $name
     * @return $this
     */
    public function forName(?string $name = null): CompanyUserRelationshipBuilder
    {
        $this->name = $name;
        return $this;
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): CompanyUserRelationshipBuilder
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     * @param array|null $roles
     * @return $this
     */
    public function forRoles(?array $roles = null): CompanyUserRelationshipBuilder
    {
        $this->roles = $roles;
        return $this;
    }

    /**
     * @return $this
     */
    public function withTrashed(): CompanyUserRelationshipBuilder
    {
        $this->trashed = true;
        return $this;
    }

    /**
     * @param bool|null $active
     * @return $this
     */
    public function forActive(?bool $active = null): CompanyUserRelationshipBuilder
    {
        $this->active = $active;
        return $this;
    }

    /**
     * @return Builder
     */
    public function query(): Builder
    {
        $query = CompanyUserRelationship::withTrashed($this->trashed);

        $query->with([CompanyUserRelationship::RELATION_USER, CompanyUserRelationship::RELATION_ROLE]);

        $query
            ->when($this->companyId, function ($query) {
                return $query->where(CompanyUserRelationship::FIELD_COMPANY_ID, $this->companyId);
            })
            ->when($this->name, function ($query) {
                return $query->whereHas(CompanyUserRelationship::RELATION_USER, function ($query) {
                    return $query->where(User::FIELD_NAME, 'like', '%' . $this->name . '%');
                });
            })
            ->when(filled($this->roles), function ($query) {
                return $query->whereHas(CompanyUserRelationship::RELATION_ROLE, function ($query) {
                    return $query->whereIn(Role::FIELD_NAME, $this->roles);
                });
            })
            ->when(!is_null($this->active),  function ($query) {
                return $this->active
                    ? $query->whereNull(CompanyUserRelationship::FIELD_DELETED_AT)
                    : $query->whereNotNull(CompanyUserRelationship::FIELD_DELETED_AT);
            });

        return $query;
    }
}
