<?php

namespace App\Builders\CompanyUser;

use App\Builders\BaseBuilder;
use App\Models\Odin\CompanyUser;
use Illuminate\Database\Eloquent\Builder;

class CompanyUserBuilder extends BaseBuilder
{
    protected ?int $companyId;
    public function forCompany(?int $companyId): CompanyUserBuilder
    {
        $this->companyId = $companyId;

        return $this;
    }

    public function getQuery(): Builder
    {
        $query = CompanyUser::query();

        $query->when($this->companyId, fn (Builder $query) => $query->where(CompanyUser::FIELD_COMPANY_ID, $this->companyId));

        return $query;
    }

    public static function query(): CompanyUserBuilder
    {
        return new CompanyUserBuilder();
    }
}
