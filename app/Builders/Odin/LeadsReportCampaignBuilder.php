<?php

namespace App\Builders\Odin;

use App\Contracts\Builders\Odin\LeadsReport\LeadsReportBuilderContract;
use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\LeadsReport\LeadsReportQueryEnum;
use App\Enums\SupportedTimezones;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\CompanyUserRelationship;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\User;
use App\Services\Filterables\LeadsReport\LeadsReportCampaignStatusFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportCompanyUserRelationFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportIndustryFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportLocationFilterable;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LeadsReportCampaignBuilder implements LeadsReportBuilderContract
{
    /**
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param LeadsReportGroupEnum|null $group
     * @param array|null $columns
     * @param Collection|null $limitedLocations
     * @param array|null $industries
     * @param array|null $states
     * @param array|null $counties
     * @param array|null $campaignStatuses
     * @param int|null $companyId
     * @param array|null $campaigns
     * @param array|null $companyUserRelations
     * @param string $db
     * @param string $dbRead
     */
    public function __construct(
        protected ?Carbon $startDate = null,
        protected ?Carbon $endDate = null,
        protected ?LeadsReportGroupEnum $group = null,
        protected ?array $columns = null,
        protected ?Collection $limitedLocations = null,
        protected ?array $industries = null,
        protected ?array $states = null,
        protected ?array $counties = null,
        protected ?array $campaignStatuses = null,
        protected ?int $companyId = null,
        protected ?array $campaigns = null,
        protected ?array $companyUserRelations = null,
        protected string $db = '',
        protected string $dbRead = '',
    ){}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param Carbon|null $startDate
     * @return $this
     */
    public function forStartDate(?Carbon $startDate = null): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    /**
     * @param Carbon|null $endDate
     * @return $this
     */
    public function forEndDate(?Carbon $endDate = null): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    /**
     * @param LeadsReportGroupEnum|null $group
     * @return $this
     */
    public function forGroup(?LeadsReportGroupEnum $group = null): self
    {
        $this->group = $group;

        return $this;
    }

    /**
     * @param array|null $columns
     * @return $this
     */
    public function forColumns(?array $columns = null): self
    {
        $this->columns = $columns;

        return $this;
    }

    /**
     * @param Collection|null $limitedLocations
     * @return $this
     */
    public function forLimitedLocations(?Collection $limitedLocations = null): self
    {
        $this->limitedLocations = $limitedLocations;

        return $this;
    }

    /**
     * @param mixed|null $filters
     * @return $this
     */
    public function withFilters(mixed $filters = null): self
    {
        $this->states = $filters?->{LeadsReportLocationFilterable::ID}?->{LeadsReportLocationFilterable::ID} ?? null;
        $this->counties = $filters?->{LeadsReportLocationFilterable::ID}?->{LeadsReportLocationFilterable::CHILD_FILTERABLE_COUNTY_ID} ?? null;
        $this->industries = $filters?->{LeadsReportIndustryFilterable::ID} ?? null;
        $this->campaignStatuses = $filters?->{LeadsReportCampaignStatusFilterable::ID} ?? null;
        $this->companyUserRelations = $filters?->{LeadsReportCompanyUserRelationFilterable::ID} ?? null;

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return self
     */
    public function forCompany(?int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     * @param array|null $campaigns
     * @return self
     */
    public function forCampaigns(?array $campaigns): self
    {
        $this->campaigns = $campaigns;
        return $this;
    }

    /**
     * @param string $db
     * @return $this
     */
    public function fromDatabase(string $db = ''): self
    {
        $this->db = $db;

        return $this;
    }

    /**
     * @param string $dbRead
     * @return $this
     */
    public function fromReadonlyDatabase(string $dbRead = ''): self
    {
        $this->dbRead = $dbRead;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function addZipCodeTotals(Builder $query): Builder
    {
        $totalZipCountQuery =  DB::table(Location::TABLE)
            ->select(Location::STATE, Location::COUNTY, DB::raw('COUNT('.Location::ZIP_CODE.') as total_zip_count'))
            ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
            ->groupBy(Location::STATE, Location::COUNTY);
        return $query->leftJoin(DB::raw("({$totalZipCountQuery->toSql()}) as tz"), function ($join) {
            $join->on('tz.'.Location::STATE, '=', 'l.'.Location::STATE)
                ->on('tz.'.Location::COUNTY, '=', 'l.'.Location::COUNTY);
        })
            ->mergeBindings($totalZipCountQuery);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function locationLimit(Builder $query): Builder
    {
        return $query->where(function($query) {
            foreach ($this->limitedLocations as $location) {
                $query->orWhere(function($query) use ($location) {
                    $query->where('l.'.Location::STATE, $location->{Location::STATE})
                        ->where('l.'.Location::COUNTY, $location->{Location::COUNTY});
                });
            }
        });
    }

    /**
     * @return array
     * @throws Exception
     */
    protected function getSelectQueries(): array
    {
        $dateString = 'CONVERT_TZ(cp.'.ConsumerProduct::CREATED_AT.', @@session.time_zone, \''.SupportedTimezones::MOUNTAIN->value.'\')';
        $selectQueries = [DB::raw(LeadsReportGroupEnum::getSelectQuery($this->group, $dateString))];
        $calculatedColumns = LeadsReportColumnEnum::getAllCalculatedColumns();

        foreach ($this->columns as $column) {
            /** @var LeadsReportColumnEnum $column */
            if (!in_array($column, $calculatedColumns) && in_array(LeadsReportQueryEnum::CAMPAIGN, LeadsReportColumnEnum::getQueryType($column))) {
                $selectQueries[] = DB::raw($column->getSelectQuery());
            }
        }
        return $selectQueries;
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = DB::table($this->db.'.'.CompanyCampaign::TABLE.' AS cc')
            ->leftJoin($this->db.'.'.CompanyCampaignLocationModule::TABLE.' AS cclm', 'cclm.'.CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID, '=', 'cc.'.CompanyCampaign::FIELD_ID)
            ->leftJoin($this->db.'.'.CompanyCampaignLocationModuleLocation::TABLE.' AS cclml', 'cclml.'.CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID, '=', 'cclm.'.CompanyCampaignLocationModule::FIELD_ID)
            ->join($this->db.'.'.Company::TABLE.' AS co', 'co.'.Company::FIELD_ID, '=', 'cc.'.CompanyCampaign::FIELD_COMPANY_ID)
            ->leftJoin(Location::TABLE.' AS l', 'l.'.Location::ID, '=', 'cclml.'.CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)
            ->leftJoin($this->db.'.'.BudgetContainer::TABLE.' AS bc', 'bc.'.BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', 'cc.'.CompanyCampaign::FIELD_ID)
            ->leftJoin($this->db.'.'.Budget::TABLE.' AS b', 'b.'.Budget::FIELD_BUDGET_CONTAINER_ID, '=', 'bc.'.BudgetContainer::FIELD_ID)
            ->join($this->db.'.'.IndustryService::TABLE.' AS is2', 'is2.'.IndustryService::FIELD_ID, '=', 'cc.'.CompanyCampaign::FIELD_SERVICE_ID)
            ->join($this->db.'.'.Industry::TABLE.' AS i', 'i.'.Industry::FIELD_ID, '=', 'is2.'.IndustryService::FIELD_INDUSTRY_ID)
            ->select($this->getSelectQueries())
            ->groupBy(LeadsReportColumnEnum::GROUP_KEY);

        if (in_array(LeadsReportColumnEnum::CAMPAIGN_ZIP_CODE_COVERAGE, $this->columns)) {
            $query = $this->addZipCodeTotals($query);
        }

        if ($this->group === LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP ||
            $this->companyUserRelations) {
            $query
                ->join(CompanyUserRelationship::TABLE.' as cur', 'cur.'.CompanyUserRelationship::FIELD_COMPANY_ID, '=', 'co.'.Company::FIELD_ID)
                ->join(User::TABLE.' as u', 'u.'.User::FIELD_ID, '=', 'cur.'.CompanyUserRelationship::FIELD_USER_ID)
                ->whereNull('cur.'.CompanyUserRelationship::FIELD_DELETED_AT);
        }

        if ($this->limitedLocations)
            $query = $this->locationLimit($query);
        if ($this->industries)
            $query->whereIn('i.'.Industry::FIELD_ID, $this->industries);
        if ($this->states)
            $query->whereIn('l.'.Location::STATE, $this->states);
        if ($this->counties)
            $query->whereIn('l.'.Location::STATE, $this->states)
                ->whereIn('l.'.Location::COUNTY, $this->counties);
        if ($this->companyId)
            $query->where('co.'.Company::FIELD_ID, $this->companyId);
        if ($this->campaigns)
            $query->whereIn('cc.'.CompanyCampaign::FIELD_ID, $this->campaigns);
        if ($this->campaignStatuses)
            $query->whereIn('cc.'.CompanyCampaign::FIELD_STATUS, $this->campaignStatuses);

        return $query;
    }
}
