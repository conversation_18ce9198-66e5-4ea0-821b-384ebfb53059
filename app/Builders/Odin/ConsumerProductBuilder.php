<?php

namespace App\Builders\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ConsumerProductBuilder
{
    public function __construct(
        protected ?int      $industryId = null,
        protected ?int      $industryServiceId = null,
        protected ?int      $status = null,
        protected ?bool     $assignmentCount = false,
        protected ?string   $state = null,
        protected ?string   $county = null,
        protected ?array    $zipCodes = null,
        protected ?Carbon   $fromDate = null,
        protected ?Carbon   $toDate = null,
        protected ?bool     $address = false,
    ){}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function forIndustryId(?int $id = null): self
    {
        $this->industryId = $id;
        return $this;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function forIndustryServiceId(?int $id = null): self
    {
        $this->industryServiceId = $id;
        return $this;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function forStatus(?int $id = null): self
    {
        $this->status = $id;
        return $this;
    }

    /**
     * @return $this
     */
    public function withAssignmentCount(): self
    {
        $this->assignmentCount = true;
        return $this;
    }

    /**
     * @param string|null $stateKey
     * @return $this
     */
    public function forState(?string $stateKey = null): self
    {
        $this->state = $stateKey;
        return $this;
    }

    /**
     * @param string|null $countyKey
     * @return $this
     */
    public function forCounty(?string $countyKey = null): self
    {
        $this->county = $countyKey;
        return $this;
    }

    /**
     * @param array|null $zipCodes
     * @return $this
     */
    public function forZipCodes(?array $zipCodes = null): self
    {
        $this->zipCodes = $zipCodes;
        return $this;
    }

    /**
     * @param Carbon|null $fromDate
     * @return $this
     */
    public function forFromDate(?Carbon $fromDate = null): self
    {
        $this->fromDate = $fromDate;
        return $this;
    }

    /**
     * @param Carbon|null $toDate
     * @return $this
     */
    public function forToDate(?Carbon $toDate = null): self
    {
        $this->toDate = $toDate;
        return $this;
    }

    /**
     * @return $this
     */
    public function withAddress(): self
    {
        $this->address = true;
        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = ConsumerProduct::query()
            ->join(Consumer::TABLE, Consumer::TABLE .'.'. Consumer::FIELD_ID, '=', ConsumerProduct::FIELD_CONSUMER_ID)
            ->select(ConsumerProduct::TABLE . '.*', Consumer::TABLE . '.legacy_id');

        if ($this->address || $this->state || $this->county || $this->zipCodes) {
            $query->with(ConsumerProduct::RELATION_ADDRESS);
        }

        if ($this->assignmentCount) {
            $query->withCount(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT);
        }

        if ($this->status) {
            $query->where(ConsumerProduct::FIELD_STATUS, $this->status);
        }

        if ($this->state) {
            $query->whereHas(ConsumerProduct::RELATION_ADDRESS, fn(Builder $query) =>
                $query->where(Address::TABLE .'.'. Address::FIELD_STATE, $this->state)
            );
        }

        if ($this->county) {
            $query->whereHas(ConsumerProduct::RELATION_ADDRESS, fn(Builder $query) =>
                $query->where(Address::TABLE .'.'. Address::FIELD_COUNTRY, $this->county)
            );
        }

        if ($this->zipCodes) {
            $query->whereHas(ConsumerProduct::RELATION_ADDRESS, fn(Builder $query) =>
                $query->whereIn(Address::TABLE .'.'. Address::FIELD_ZIP_CODE, $this->zipCodes)
            );
        }

        if ($this->fromDate) {
            $query->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CREATED_AT, '>', $this->fromDate->format("Y-m-d H:i:s"));
        }

        if ($this->toDate) {
            $query->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CREATED_AT, '<', $this->toDate->format("Y-m-d H:i:s"));
        }

        return $query;

    }
}
