<?php

namespace App\Builders\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyService;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class IndustryBuilder
{
    /**
     * @param int|null $forIndustryId
     * @param array $forIndustryIds
     * @param int|null $forServiceId
     * @param bool $industryData
     * @param bool $servicesData
     * @param bool $servicedCompaniesData
     * @param bool $appendServicesData
     * @param bool $appendCompaniesData
     * @param bool $appendCompanyServicesData
     * @param string|null $sortByColumn
     * @param string $sortDirection
     */
    public function __construct(
        protected ?int $forIndustryId               = null,
        protected array $forIndustryIds             = [],
        protected ?int $forServiceId                = null,
        protected bool $industryData                = false,
        protected bool $servicesData                = false,
        protected bool $servicedCompaniesData       = false,
        protected bool $appendServicesData          = false,
        protected bool $appendCompaniesData         = false,
        protected bool $appendCompanyServicesData   = false,
        protected bool $appendIndustryConfiguration = false,
        protected ?string $sortByColumn             = null,
        protected string $sortDirection             = 'desc'
    ){}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * Facilitates whether services should be added into the resulting query generated by the query builder.
     *
     * @param bool $append
     * @return $this
     */
    public function appendServicesData(bool $append = true): self
    {
        $this->appendServicesData = $append;

        return $this;
    }

    /**
     * Facilitates whether companies should be added into the resulting query generated by the query builder.
     *
     * @param bool $append
     * @return $this
     */
    public function appendCompaniesData(bool $append = true): self
    {
        $this->appendCompaniesData = $append;

        return $this;
    }

    /**
     * Facilitates whether company services should be added into the resulting query generated by the query builder.
     *
     * @param bool $append
     * @return $this
     */
    public function appendCompanyServicesData(bool $append = true): self
    {
        $this->appendCompanyServicesData = $append;

        return $this;
    }

    /**
     * @param bool $append
     * @return $this
     */
    public function appendIndustryConfiguration(bool $append = true): self
    {
        $this->appendIndustryConfiguration = $append;

        return $this;
    }

    /**
     * Accommodates the sorting order of the resulting query generated by the query builder.
     *
     * @param $sortByColumn
     * @param $sortDirection
     * @return $this
     */
    public function sortBy($sortByColumn, $sortDirection): self
    {
        $this->sortByColumn  = $sortByColumn;
        $this->sortDirection = $sortDirection;

        return $this;
    }

    /**
     * Allows querying against a specific industry.
     *
     * @param int|null $id
     * @return $this
     */
    public function forIndustryId(?int $id = null): self
    {
        $this->forIndustryId = $id;

        return $this;
    }

    /**
     * Allows querying against a list of industries.
     *
     * @param array $ids
     * @return $this
     */
    public function forIndustryIds(array $ids = []): self
    {
        $this->forIndustryIds = $ids;

        return $this;
    }

    /**
     * Allows querying against a specific service.
     *
     * @param int|null $id
     * @return $this
     */
    public function forServiceId(?int $id = null): self
    {
        $this->forServiceId = $id;

        return $this;
    }

    /**
     * Allows querying to fetch industry data.
     *
     * @param bool $getData
     * @return $this
     */
    public function getIndustryData(bool $getData = true): self
    {
        $this->industryData = $getData;

        return $this;
    }

    /**
     * Allows querying to fetch services data.
     *
     * @param bool $getData
     * @return $this
     */
    public function getServicesData(bool $getData = true): self
    {
        $this->servicesData = $getData;

        return $this;
    }

    /**
     * Allows querying to fetch serviced companies data.
     *
     * @param bool $getData
     * @return $this
     */
    public function getServicedCompaniesData(bool $getData = true): self
    {
        $this->servicedCompaniesData = $getData;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        if($this->industryData === true) {

            $query = Industry::query();

            if(!empty($this->forIndustryIds))
                $query->whereIn(Industry::FIELD_ID, $this->forIndustryIds);
            elseif(!empty($this->forIndustryId))
                $query->where(Industry::FIELD_ID, $this->forIndustryId);

            if($this->appendServicesData === true) {
                $query->with([Industry::RELATION_SERVICES => function ($query) {
                    if($this->appendCompanyServicesData === true)
                        $query->with([IndustryService::RELATION_COMPANY_SERVICES]);
                }]);
            }

            if($this->appendCompaniesData === true) {
                //TODO: Implement 'companies' relation here
            }

            if ($this->appendIndustryConfiguration) {
                $query->with(Industry::RELATION_INDUSTRY_CONFIGURATION);
            }

            return $query;
        }

        if($this->servicesData === true && !empty($this->forIndustryId)) {
            $query = IndustryService::query()->where(IndustryService::FIELD_INDUSTRY_ID, $this->forIndustryId);
            if($this->appendCompanyServicesData === true)
                $query->with([IndustryService::RELATION_COMPANY_SERVICES]);

            return $query;
        }

        if($this->servicedCompaniesData === true && !empty($this->forServiceId)) {
            /** @var CompanyService $companies */
            $companies = CompanyService::query()
                ->where(CompanyService::FIELD_INDUSTRY_SERVICE_ID, $this->forServiceId)
                ->pluck(CompanyService::FIELD_COMPANY_ID);

            return Company::query()->whereIn(Company::FIELD_ID, $companies);
        }
    }
}
