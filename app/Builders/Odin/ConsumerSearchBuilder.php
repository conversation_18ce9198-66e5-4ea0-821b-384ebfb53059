<?php

namespace App\Builders\Odin;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\ConsumerProcessingActivity;
use App\Models\Legacy\Location;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class ConsumerSearchBuilder
{
    const string SOLD_TO_COMPANY         = 'sold_to';
    const string NOT_SOLD_TO_COMPANY     = 'not_sold_to';
    const string ALL_COMPANY             = 'all_company';
    const string ALIAS_LAST_CONTACTED_AT = 'last_contact_at';

    public function __construct(
        protected ?string $searchText = null,
        protected ?int    $companyId = null,
        protected ?array  $campaignIds = null,
        protected ?int    $searchId = null,
        protected ?bool   $excludeCloned = null,
        protected ?int    $excludeConsumerProductStatus = null,
        protected ?int    $excludeMarketingCampaignId = null,
        protected ?string $soldToCompany = 'all',
    )
    {
    }

    public static function query(): self
    {
        return new self();
    }

    public function searchText(?string $search): self
    {
        $this->searchText = $search;
        return $this;
    }

    public function companyId(?int $companyId = null): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    public function searchId(?int $searchId = null): self
    {
        $this->searchId = $searchId;
        return $this;
    }

    public function campaignIds(?array $campaignIds = null): self
    {
        $this->campaignIds = $campaignIds;
        return $this;
    }

    public function soldToCompany(?string $soldToCompany = null): self
    {
        $this->soldToCompany = $soldToCompany;
        return $this;
    }

    public function excludeMarketingCampaign(?int $marketingCampaignId = null): self
    {
        $this->excludeMarketingCampaignId = $marketingCampaignId;
        return $this;
    }

    public function excludeCloned(?bool $excludeCloned = null): self
    {
        $this->excludeCloned = $excludeCloned;
        return $this;
    }

    public function excludeConsumerProductStatus(?int $consumerProductStatus = null): self
    {
        $this->excludeConsumerProductStatus = $consumerProductStatus;
        return $this;
    }

    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    //TODO: comments and pre-raised leads
    //TODO: move joins / relations to ConsumerFilterableService?
    //TODO: optimize
    public function getBaseQuery(): Builder
    {
        $query = Consumer::query()
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID, '=', Consumer::TABLE . '.' . Consumer::FIELD_ID)
            ->leftJoin(ProductAssignment::TABLE, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID)
            ->join(Address::TABLE, Address::TABLE . '.' . Address::FIELD_ID, '=', ConsumerProduct::FIELD_ADDRESS_ID)
            ->leftJoin(ConsumerProductTracking::TABLE, ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::ID, '=', ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
            ->leftJoin(Website::TABLE, Website::TABLE . '.' . Website::FIELD_ID, '=', ConsumerProductTracking::TABLE . '.' . ConsumerProductTracking::WEBSITE_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID, '=', ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(Product::TABLE, Product::TABLE . '.' . Product::FIELD_ID, '=', ServiceProduct::FIELD_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE . '.' . IndustryService::FIELD_ID, '=', ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_INDUSTRY_SERVICE_ID);

        if (!empty($this->searchId)) {
            $query->where(function ($query) {
                $query->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, '=', $this->searchId)
                    ->orWhere(Consumer::TABLE . '.' . Consumer::FIELD_LEGACY_ID, '=', $this->searchId)
                    ->orWhere(Consumer::TABLE . '.' . Consumer::FIELD_ID, '=', $this->searchId);
            });
        }

        if ($this->searchText) {
            $query->where(
                fn(Builder $query) => $query->where(DB::raw("CONCAT(addresses.address_1, ' ', addresses.address_2, ' ', addresses.city, ' ', addresses.state, ' ', addresses.zip_code)"), 'LIKE', "%{$this->searchText}%")
                    ->orWhere(DB::raw("CONCAT(consumers.first_name, ' ', consumers.last_name)"), 'LIKE', "%{$this->searchText}%")
                    ->orWhere(Consumer::TABLE . '.' . Consumer::FIELD_PHONE, 'LIKE', "%$this->searchText%")
                    ->orWhere(Consumer::TABLE . '.' . Consumer::FIELD_EMAIL, 'LIKE', "%$this->searchText%")
            );
        }

        if ($this->excludeCloned) {
            $query->whereNull(Consumer::TABLE . '.' . Consumer::FIELD_CLONED_FROM_ID);
        }

        if ($this->excludeMarketingCampaignId) {
            $query->whereDoesntHave(Consumer::RELATION_MARKETING_CONSUMER, function (Builder $builder) {
                $builder->where(MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID, $this->excludeMarketingCampaignId);
            });
        }

        if ($this->excludeConsumerProductStatus) {
            $query->whereDoesntHave(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $builder) {
                $builder->where(ConsumerProduct::FIELD_STATUS, '=', $this->excludeConsumerProductStatus);
            });
        }

        if (filled($this->companyId)) {
            if (!empty($this->campaignIds)) {
                $locationBaseQuery = CompanyCampaignLocationModule::query()
                    ->whereIntegerInRaw(
                        CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID,
                        $this->campaignIds
                    );
            } else {
                $locationBaseQuery = CompanyCampaign::query()
                    ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID, $this->companyId)
                    ->join(CompanyCampaignLocationModule::TABLE,
                        CompanyCampaignLocationModule::TABLE . '.' . BaseCompanyCampaignModule::FIELD_CAMPAIGN_ID,
                        '=',
                        CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                    );
            }

            $companyLocations = $locationBaseQuery
                ->join(CompanyCampaignLocationModuleLocation::TABLE,
                    CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID,
                    '=',
                    CompanyCampaignLocationModule::TABLE . '.' . CompanyCampaignLocationModule::FIELD_ID
                )
                ->distinct()
                ->pluck(
                    CompanyCampaignLocationModuleLocation::TABLE . '.' . CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID,
                );

            $query->join(
                Location::TABLE,
                Address::TABLE . '.' . Address::FIELD_ZIP_CODE,
                '=',
                Location::TABLE . '.' . Location::ZIP_CODE
            )->whereIntegerInRaw(
                Location::TABLE . '.' . Location::ID,
                $companyLocations
            );

            if ($this->soldToCompany === self::SOLD_TO_COMPANY) {
                $query->whereHas(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $builder) {
                    $builder->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function (Builder $builder) {
                        $builder->where(ProductAssignment::FIELD_COMPANY_ID, $this->companyId)
                            ->where(ProductAssignment::FIELD_DELIVERED, true)
                            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
                            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS);
                    });
                });
            } else if ($this->soldToCompany === self::NOT_SOLD_TO_COMPANY) {
                $query->whereDoesntHave(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $builder) {
                    $builder->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function (Builder $builder) {
                        $builder->where(ProductAssignment::FIELD_COMPANY_ID, $this->companyId)
                            ->where(ProductAssignment::FIELD_DELIVERED, true)
                            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
                            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
                            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS);
                    });
                });
            }

        }

        return $query;
    }

    public function getQuery(): Builder
    {
        return $this->getBaseQuery()
            ->leftJoin(ConsumerProcessingActivity::TABLE, fn(JoinClause $join) => $join->on(ConsumerProcessingActivity::TABLE . '.' . ConsumerProcessingActivity::FIELD_CONSUMER_ID, '=', Consumer::TABLE . '.' . Consumer::FIELD_ID)
                ->where(ConsumerProcessingActivity::TABLE . '.' . ConsumerProcessingActivity::FIELD_ACTIVITY_TYPE, '=', ConsumerProcessingActivityType::CALL)
            )->with([
                Consumer::RELATION_CONSUMER_PRODUCT,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_ADDRESS,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING . '.' . ConsumerProductTracking::RELATION_WEBSITE,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_SERVICE_PRODUCT,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_INDUSTRY_SERVICE,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_INDUSTRY_SERVICE . '.' . IndustryService::RELATION_INDUSTRY,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_SERVICE_PRODUCT . '.' . ServiceProduct::RELATION_PRODUCT,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT,
                Consumer::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT . '.' . ProductAssignment::RELATION_COMPANY,
            ])
            ->groupBy(Consumer::TABLE . '.' . Consumer::FIELD_ID)
            ->select(DB::raw('consumers.*, MAX(' . ConsumerProcessingActivity::TABLE . '.' . ConsumerProcessingActivity::CREATED_AT . ') as ' . self::ALIAS_LAST_CONTACTED_AT));
    }

    public function getAggregatesQuery(): Builder
    {
        $standardTier = QualityTier::query()->where(QualityTier::FIELD_NAME, QualityTierEnum::STANDARD)->first()->{QualityTier::FIELD_ID};
        $premiumTier = QualityTier::query()->where(QualityTier::FIELD_NAME, QualityTierEnum::PREMIUM)->first()->{QualityTier::FIELD_ID};

        if (! (is_int($standardTier) && is_int($premiumTier))) {
            throw new \Exception('Standard tier and premium tier ids must be integers.');
        }

        $soldProductAssignmentString = 'product_assignments.chargeable = true AND product_assignments.delivered = true AND product_rejections.id IS NULL AND product_cancellations.id IS NULL';

        return $this->getBaseQuery()
            ->leftJoin(ProductRejection::TABLE, fn(JoinClause $join) => $join->on(
                ProductRejection::TABLE . '.' . ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID,
                '=',
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
            )->whereNull(ProductRejection::TABLE . '.' . ProductRejection::FIELD_DELETED_AT))
            ->leftJoin(ProductCancellation::TABLE, fn(JoinClause $join) => $join->on(
                ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID,
                '=',
                ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID
            )->whereNull(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_DELETED_AT))
            ->select(
                DB::raw("COUNT(DISTINCT CASE WHEN $soldProductAssignmentString THEN product_assignments.id END) / COUNT(DISTINCT CASE WHEN $soldProductAssignmentString THEN consumer_products.id END) as average_installers_lead_sold"),
                DB::raw("COUNT(DISTINCT CASE WHEN product_assignments.quality_tier_id = $premiumTier THEN product_assignments.id END) as premium_lead_count"),
                DB::raw("COUNT(DISTINCT CASE WHEN product_assignments.quality_tier_id = $standardTier OR product_assignments.quality_tier_id IS NULL THEN product_assignments.id END) as standard_lead_count"),
            );
    }

    public function getMarketingFieldsQuery(): Builder
    {
        return $this->getBaseQuery()
            ->groupBy(Consumer::TABLE . '.' . Consumer::FIELD_ID)
            ->select(
                Consumer::TABLE . '.' . Consumer::FIELD_ID,
                Consumer::TABLE . '.' . Consumer::FIELD_REFERENCE,
                Consumer::TABLE . '.' . Consumer::FIELD_FIRST_NAME,
                Consumer::TABLE . '.' . Consumer::FIELD_LAST_NAME,
                Consumer::TABLE . '.' . Consumer::FIELD_EMAIL,
                'mcc1' . '.' . MarketingCampaignConsumer::FIELD_STATUS,
                'mcc1' . '.' . MarketingCampaignConsumer::FIELD_ID . ' as mcc_id',
                'mcc1' . '.' . MarketingCampaignConsumer::FIELD_CREATED_AT . ' as mcc_created_at',
                'mcc1' . '.' . MarketingCampaignConsumer::FIELD_EXTERNAL_REFERENCE . ' as mcc_external_reference'
            )
            ->leftJoin(MarketingCampaignConsumer::TABLE . ' as mcc1',
                'mcc1' . '.' . MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE,
                '=', Consumer::TABLE . '.' . Consumer::FIELD_REFERENCE
            )
            ->leftJoin(MarketingCampaignConsumer::TABLE . ' as mcc2', function (JoinClause $clause) {
                $clause->on('mcc2' . '.' . MarketingCampaignConsumer::FIELD_CONSUMER_REFERENCE, '=', Consumer::TABLE . '.' . Consumer::FIELD_REFERENCE)->on('mcc1' . '.' . MarketingCampaignConsumer::FIELD_ID, '<', 'mcc2' . '.' . MarketingCampaignConsumer::FIELD_ID);
            })->whereNull('mcc2.id');
    }

    /**
     * @return Builder
     */
    public function getDripCampaignEstimate(): Builder
    {
        return $this->getBaseQuery()
            ->select(
                DB::raw('DATE(' . ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CREATED_AT . ') as date'),
                DB::raw('COUNT(DISTINCT ' . Consumer::TABLE . '.' . Consumer::FIELD_ID . ') as count'),
            )->groupBy('date');
    }
}
