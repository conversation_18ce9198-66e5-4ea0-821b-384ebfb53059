<?php

namespace App\Builders\Odin;

use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class FloorPricingBuilder
{

    public function __construct(
        protected ?int $productId = null,
        protected ?int $industryServiceId = null,
        protected ?int $serviceProductId = null,
        protected ?int $qualityTierId = null,
        protected ?int $propertyTypeId = null,
        protected ?int $stateLocationId = null,
    ) {}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * Handles applying product type filtering
     *
     * @param int|null $productTypeId
     * @return $this
     */
    public function setProductId(?int $productTypeId = null): self
    {
        $this->productId = $productTypeId;
        return $this;
    }

    /**
     * <PERSON>les applying product type filtering
     *
     * @param int|null $industryServiceId
     * @return $this
     */
    public function setIndustryServiceId(?int $industryServiceId = null): self
    {
        $this->industryServiceId = $industryServiceId;
        return $this;
    }

    /**
     * @param int|null $serviceProductId
     * @return $this
     */
    public function setServiceProductId(?int $serviceProductId = null): self
    {
        $this->serviceProductId = $serviceProductId;

        return $this;
    }

    /**
     * Handles applying quality tier type filtering
     *
     * @param int|null $qualityTierId
     * @return $this
     */
    public function setQualityTierId(?int $qualityTierId = null): self
    {
        $this->qualityTierId = $qualityTierId;
        return $this;
    }

    /**
     * Handles applying property type filtering
     *
     * @param int|null $propertyTypeId
     * @return $this
     */
    public function setPropertyTypeId(?int $propertyTypeId = null): self
    {
        $this->propertyTypeId = $propertyTypeId;

        return $this;
    }

    /**
     * Handles applying quality tier filtering
     *
     * @param int|null $stateId
     * @return $this
     */
    public function setStateLocationId(?int $stateId = null): self
    {
        $this->stateLocationId = $stateId;

        return $this;
    }

    /**
     * Runs the query builder, and returns a list of tasks.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()
            ->get();
    }

    /**
     * Returns the query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        if ($this->stateLocationId) {
            $query = $this->getQueryForCounties();
        } else {
            $query = ProductStateFloorPrice::query();

            if ($this->industryServiceId && $this->productId && !$this->serviceProductId)
                $this->setServiceProductId($this->getServiceProductId());

            if ($this->serviceProductId) {
                $query->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $this->serviceProductId);
            }

            if ($this->qualityTierId)
                $query->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $this->qualityTierId);

            if($this->propertyTypeId)
                $query->where(ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $this->propertyTypeId);
        }

        return $query;
    }

    /**
     * @return Builder
     */
    public function getQueryForCounties(): Builder
    {
        $query = ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID, $this->stateLocationId);

        if ($this->industryServiceId && $this->productId && !$this->serviceProductId)
            $this->setServiceProductId($this->getServiceProductId());

        if ($this->serviceProductId)
            $query->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $this->serviceProductId);

        if ($this->qualityTierId)
            $query->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $this->qualityTierId);

        if($this->propertyTypeId)
            $query->where(ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, $this->propertyTypeId);

        return $query;
    }

    /**
     * @return int
     */
    private function getServiceProductId(): int
    {
        return ServiceProduct::query()
            ->where([
                ServiceProduct::FIELD_PRODUCT_ID          => $this->productId,
                ServiceProduct::FIELD_INDUSTRY_SERVICE_ID => $this->industryServiceId,
            ])->firstOrFail()
            ->id;
    }
}
