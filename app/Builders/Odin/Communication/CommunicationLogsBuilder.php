<?php

namespace App\Builders\Odin\Communication;

use App\Models\Call;
use App\Models\Phone;
use App\Models\Text;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class CommunicationLogsBuilder
{
    const COMMUNICATION_TYPE_COLUMN = 'communication_type';
    const COMMUNICATION_QUERY_ALIAS = 'inner';

    public function __construct(
        private ?array $userPhonesIds = null,
        protected ?string $userPhone = null,
        protected ?string $externalPhone = null,
        protected ?string $communicationDescription = null,
        protected ?array $communicationTypes = null,
        protected ?array $dateTime = null
    )
    {
    }
    /**
     * Creates an instance of builder.
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param int[] $userPhonesIds
     * @return $this
     */
    public function setUserPhonesIds(array $userPhonesIds): self
    {
        $this->userPhonesIds = $userPhonesIds;
        return $this;
    }

    /**
     * @param string $userPhone
     * @return $this
     */
    public function setUserPhone(string $userPhone): self
    {
        $this->userPhone = $userPhone;
        return $this;
    }

    /**
     * @param string $externalPhone
     * @return $this
     */
    public function setExternalPhone(string $externalPhone): self
    {
        $this->externalPhone = $externalPhone;
        return $this;
    }

    /**
     * @param string $communicationDescription
     * @return $this
     */
    public function setCommunicationDescription(string $communicationDescription): self
    {
        $this->communicationDescription = $communicationDescription;
        return $this;
    }

    /**
     * @param string[] $communicationTypes
     * @return $this
     */
    public function setCommunicationTypes(array $communicationTypes): self
    {
        $this->communicationTypes = $communicationTypes;
        return $this;
    }

    /**
     * @param string $dateFrom
     * @return $this
     */
    public function setDateFrom(string $dateFrom): self
    {
        $this->dateTime['from'] = $dateFrom;

        return $this;
    }

    /**
     * @param string $dateTo
     * @return $this
     */
    public function setDateTo(string $dateTo): self
    {
        $this->dateTime['to'] = $dateTo;

        return $this;
    }

    /**
     * Returns a paginated instance of the query.
     * @param int $page
     * @param int $perPage
     * @param array $options
     * @return LengthAwarePaginator
     * @throws Exception
     */
    public function paginate(int $page = 1, int $perPage = 100, array $options = []): LengthAwarePaginator
    {
        $items = $this->getCallsAndTextCollection();
        $page = $page ?: (Paginator::resolveCurrentPage() ?: 1);

        return new LengthAwarePaginator($items->forPage($page, $perPage)->values(), $items->count(), $perPage, $page, $options);

    }

    private function getTextsGroupedAsChat(): Builder
    {
        return Text::query()->selectRaw("MAX(" . Text::FIELD_ID . ")")
            ->groupBy([Text::FIELD_PHONE_ID, Text::FIELD_OTHER_NUMBER])
            ->whereIn(Text::FIELD_PHONE_ID, $this->userPhonesIds);
    }


    private function applyFilters($query, $classTable)
    {
        if ($this->userPhone)
            $query->where(Phone::TABLE . '.' . Phone::FIELD_PHONE, 'LIKE', '%' . $this->userPhone . '%');

        if ($this->externalPhone)
            $query->where($classTable .'.'. Call::FIELD_OTHER_NUMBER, 'LIKE', '%' . $this->externalPhone . '%');

        if ($this->communicationDescription)
            $query->where($classTable .'.'. Call::FIELD_DIRECTION, $this->communicationDescription);

        if ($this->dateTime) {
            if (isset($this->dateTime['from'])) {
                $query->where($classTable .  '.created_at', '>=', $this->dateTime['from']);
            }

            if (isset($this->dateTime['to'])) {
                $query->where($classTable .  '.created_at', '<=', $this->dateTime['to']);
            }
        }

        return $query;
    }

    /**
     * Returns a query for this instance.
     * @throws Exception
     */
    public function getCallsAndTextCollection()
    {
        if (count($this->userPhonesIds) === 0) {
            throw new Exception('User must have at least one phone number');
        }

        if(is_null($this->communicationTypes)) {
            $this->setCommunicationTypes(['call', 'text']);
        }
        $calls = collect();
        $texts = collect();

        if(in_array('call', $this->communicationTypes)) {
            $callsQuery = Call::query()
                ->addSelect([
                    Call::TABLE.'.'.Call::FIELD_ID,
                    Call::TABLE.'.'.Call::FIELD_PHONE_ID,
                    Call::TABLE.'.'.Call::FIELD_DIRECTION,
                    Call::TABLE.'.'.'created_at',
                    Call::TABLE.'.'.Call::FIELD_OTHER_NUMBER,
                    Call::TABLE.'.'.Call::FIELD_RELATION_ID,
                    Call::TABLE.'.'.Call::FIELD_RELATION_TYPE,
                    Call::TABLE.'.'.Call::FIELD_CALL_START,
                    Call::TABLE.'.'.Call::FIELD_CALL_END,
                    Call::TABLE.'.'.Call::FIELD_RESULT,
                    Phone::TABLE.'.'.Phone::FIELD_PHONE
                ])
                ->whereIn(Phone::TABLE.'.'.Phone::FIELD_ID, $this->userPhonesIds)
                ->join(Phone::TABLE, Phone::TABLE.'.'.Phone::FIELD_ID, '=', Call::TABLE.'.'.Call::FIELD_PHONE_ID);

            $calls = $this->applyFilters($callsQuery, Call::TABLE)->get();
        }
        if(in_array('text', $this->communicationTypes)) {
            $textsQuery = Text::query()
                ->addSelect([
                    Text::TABLE.'.'.Text::FIELD_ID,
                    Text::TABLE.'.'.Text::FIELD_PHONE_ID,
                    Text::TABLE.'.'.Text::FIELD_DIRECTION,
                    Text::TABLE.'.'.'created_at',
                    Text::TABLE.'.'.Text::FIELD_OTHER_NUMBER,
                    Text::TABLE.'.'.Text::FIELD_RELATION_ID,
                    Text::TABLE.'.'.Text::FIELD_RELATION_TYPE,
                    Phone::TABLE.'.'.Phone::FIELD_PHONE
                ])
                ->join(Phone::TABLE, Phone::TABLE.'.'.Phone::FIELD_ID, '=', Text::TABLE.'.'.Text::FIELD_PHONE_ID)
                ->whereIn(Text::TABLE.'.'.Text::FIELD_ID, $this->getTextsGroupedAsChat());

            $texts = $this->applyFilters($textsQuery, Text::TABLE)->get();
        }

        return collect([$calls, $texts])->flatten(1)->sortByDesc('created_at');
    }

}
