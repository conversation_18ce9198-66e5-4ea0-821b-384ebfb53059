<?php

namespace App\Builders\Odin;

use App\Contracts\Builders\Odin\LeadsReport\LeadsReportBuilderContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\LeadsReport\LeadsReportQueryEnum;
use App\Enums\SupportedTimezones;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\CompanyUserRelationship;
use App\Models\DailyAdCost;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\Location;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Models\PingPostAffiliates\PingPostAffiliateLead;
use App\Models\PingPostPublishers\PingPostPublisherLead;
use App\Models\User;
use App\Services\Filterables\LeadsReport\LeadsReportAdvertiserFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportCampaignStatusFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportCompanyUserRelationFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportIndustryFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportLeadTypeFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportLocationFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportOriginFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportPlatformFilterable;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LeadsReportLegsBuilder implements LeadsReportBuilderContract
{
    /**
     * @param Carbon|null $startDate
     * @param Carbon|null $endDate
     * @param LeadsReportGroupEnum|null $group
     * @param array|null $columns
     * @param Collection|null $limitedLocations
     * @param array|null $industries
     * @param array|null $states
     * @param array|null $counties
     * @param array|null $campaignStatuses
     * @param array|null $origins
     * @param array|null $advertisers
     * @param array|null $platforms
     * @param int|null $companyId
     * @param array|null $campaigns
     * @param array|null $leadTypes
     * @param array|null $companyUserRelations
     * @param string $db
     * @param string $dbRead
     */
    public function __construct(
        protected ?Carbon $startDate = null,
        protected ?Carbon $endDate = null,
        protected ?LeadsReportGroupEnum $group = null,
        protected ?array $columns = null,
        protected ?Collection $limitedLocations = null,
        protected ?array $industries = null,
        protected ?array $states = null,
        protected ?array $counties = null,
        protected ?array $campaignStatuses = null,
        protected ?array $origins = null,
        protected ?array $advertisers = null,
        protected ?array $platforms = null,
        protected ?int $companyId = null,
        protected ?array $campaigns = null,
        protected ?array $leadTypes = null,
        protected ?array $companyUserRelations = null,
        protected string $db = '',
        protected string $dbRead = '',
    ){}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param Carbon|null $startDate
     * @return $this
     */
    public function forStartDate(?Carbon $startDate = null): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    /**
     * @param Carbon|null $endDate
     * @return $this
     */
    public function forEndDate(?Carbon $endDate = null): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    /**
     * @param LeadsReportGroupEnum|null $group
     * @return $this
     */
    public function forGroup(?LeadsReportGroupEnum $group = null): self
    {
        $this->group = $group;

        return $this;
    }

    /**
     * @param array|null $columns
     * @return $this
     */
    public function forColumns(?array $columns = null): self
    {
        $this->columns = $columns;

        return $this;
    }

    /**
     * @param Collection|null $limitedLocations
     * @return $this
     */
    public function forLimitedLocations(?Collection $limitedLocations = null): self
    {
        $this->limitedLocations = $limitedLocations;

        return $this;
    }

    /**
     * @param mixed|null $filters
     * @return $this
     */
    public function withFilters(mixed $filters = null): self
    {
        $this->states = $filters?->{LeadsReportLocationFilterable::ID}?->{LeadsReportLocationFilterable::ID} ?? null;
        $this->counties = $filters?->{LeadsReportLocationFilterable::ID}?->{LeadsReportLocationFilterable::CHILD_FILTERABLE_COUNTY_ID} ?? null;
        $this->industries = $filters?->{LeadsReportIndustryFilterable::ID} ?? null;
        $this->campaignStatuses = $filters?->{LeadsReportCampaignStatusFilterable::ID} ?? null;
        $this->origins = $filters?->{LeadsReportOriginFilterable::ID} ?? null;
        $this->advertisers = $filters?->{LeadsReportAdvertiserFilterable::ID} ?? null;
        $this->platforms = $filters?->{LeadsReportPlatformFilterable::ID} ?? null;
        $this->leadTypes = $filters?->{LeadsReportLeadTypeFilterable::ID} ?? null;
        $this->companyUserRelations = $filters?->{LeadsReportCompanyUserRelationFilterable::ID} ?? null;

        return $this;
    }

    /**
     * @param int $industryId
     * @return $this
     */
    public function forIndustryId(int $industryId):self
    {
        $this->industries = [$industryId];
        return $this;
    }

    /**
     * @param int|null $companyId
     * @return self
     */
    public function forCompany(?int $companyId): self
    {
        $this->companyId = $companyId;
        return $this;
    }

    /**
     * @param array|null $campaigns
     * @return self
     */
    public function forCampaigns(?array $campaigns): self
    {
        $this->campaigns = $campaigns;
        return $this;
    }

    /**
     * @param string $db
     * @return $this
     */
    public function fromDatabase(string $db = ''): self
    {
        $this->db = $db;

        return $this;
    }

    /**
     * @param string $dbRead
     * @return $this
     */
    public function fromReadonlyDatabase(string $dbRead = ''): self
    {
        $this->dbRead = $dbRead;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function platformLimit(Builder $query): Builder
    {
        return $query->where(function($query) {
            if (in_array(AdvertisingPlatform::GOOGLE->value, $this->platforms))
                $query->orWhereIn('cpar.' . ConsumerProductAffiliateRecord::FIELD_TRACK_NAME, array_map(function($item) {
                    return str_replace("'", "", $item);
                }, LeadsReportColumnEnum::GOOGLE_TRACK_NAMES));
            if (in_array(AdvertisingPlatform::META->value, $this->platforms))
                $query->orWhereIn('cpar.' . ConsumerProductAffiliateRecord::FIELD_TRACK_NAME, array_map(function($item) {
                    return str_replace("'", "", $item);
                }, LeadsReportColumnEnum::META_TRACK_NAMES));
            if (in_array(AdvertisingPlatform::MICROSOFT->value, $this->platforms))
                $query->orWhereIn('cpar.' . ConsumerProductAffiliateRecord::FIELD_TRACK_NAME, array_map(function($item) {
                    return str_replace("'", "", $item);
                }, LeadsReportColumnEnum::MICROSOFT_TRACK_NAMES));
        });
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function leadTypesFilter(Builder $query): Builder
    {
        if (in_array(LeadsReportLeadTypeFilterable::SECONDARY_SERVICE, $this->leadTypes))
            $query = $query->where('cp.'.ConsumerProduct::FIELD_IS_SECONDARY_SERVICE, true);

        if (in_array(LeadsReportLeadTypeFilterable::AFFILIATE, $this->leadTypes))
            $query->join(PingPostAffiliateLead::TABLE.' as ppal', 'ppal.'.PingPostAffiliateLead::FIELD_CONSUMER_PRODUCT_ID, '=', 'cp.'.ConsumerProduct::FIELD_ID);

        if (in_array(LeadsReportLeadTypeFilterable::EMAIL_MARKETING, $this->leadTypes))
            $query->join(MarketingCampaignConsumer::TABLE.' as mcc', 'mcc.'.MarketingCampaignConsumer::FIELD_CLONED_CONSUMER_PRODUCT_ID, '=', 'cp.'.ConsumerProduct::FIELD_ID);

        if (in_array(LeadsReportLeadTypeFilterable::PING_POST, $this->leadTypes))
            $query->whereNotNull('ppl.'.PingPostPublisherLead::FIELD_ID);

        return $query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function locationLimit(Builder $query): Builder
    {
        return $query->where(function($query) {
            foreach ($this->limitedLocations as $location) {
                $query->orWhere(function($query) use ($location) {
                    $query->where('l.'.Location::STATE, $location->{Location::STATE})
                        ->where('l.'.Location::COUNTY, $location->{Location::COUNTY});
                });
            }
        });
    }

    /**
     * @return array
     * @throws Exception
     */
    protected function getSelectQueries(): array
    {
        $dateString = 'CONVERT_TZ(cp.'.ConsumerProduct::CREATED_AT.', @@session.time_zone, \''.SupportedTimezones::MOUNTAIN->value.'\')';
        $selectQueries = [DB::raw(LeadsReportGroupEnum::getSelectQuery($this->group, $dateString))];
        $calculatedColumns = LeadsReportColumnEnum::getAllCalculatedColumns();

        foreach ($this->columns as $column) {
            /** @var LeadsReportColumnEnum $column */
            if (!in_array($column, $calculatedColumns) && in_array(LeadsReportQueryEnum::LEAD, LeadsReportColumnEnum::getQueryType($column))) {
                $selectQueries[] = DB::raw($column->getSelectQuery());
            }
        }
        return $selectQueries;
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = DB::table($this->db.'.'.Consumer::TABLE.' AS c')
            ->leftJoin($this->db.'.'.ConsumerProduct::TABLE.' AS cp', 'cp.'.ConsumerProduct::FIELD_CONSUMER_ID, '=', 'c.'.Consumer::FIELD_ID)
            ->leftJoin($this->db.'.'.ProductAssignment::TABLE.' AS pa', 'pa.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', 'cp.'.ConsumerProduct::FIELD_ID)
            ->leftJoin($this->db.'.'.ProductRejection::TABLE.' AS pr', 'pr.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, '=', 'pa.'.ProductAssignment::FIELD_ID)
            ->leftJoin($this->db.'.'.ProductCancellation::TABLE.' AS pc', 'pc.'.ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID, '=', 'pa.'.ProductAssignment::FIELD_ID)
            ->leftJoin($this->db.'.'.ConsumerProductAffiliateRecord::TABLE.' AS cpar', 'cpar.'.ConsumerProductAffiliateRecord::FIELD_ID, '=', 'cp.'.ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID)
            ->leftJoin($this->db.'.'.Affiliate::TABLE.' AS aff', 'cpar.'.ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID, '=', 'aff.'.Affiliate::FIELD_ID)
            ->leftJoin($this->db.'.'.Company::TABLE.' as co', 'co.'.Company::FIELD_ID, '=', 'pa.'.ProductAssignment::FIELD_COMPANY_ID)
            ->join($this->db.'.'.ServiceProduct::TABLE.' AS sp', 'sp.'.ServiceProduct::FIELD_ID, '=', 'cp.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join($this->db.'.'.IndustryService::TABLE.' AS is2', 'is2.'.IndustryService::FIELD_ID, '=', 'sp.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join($this->db.'.'.Industry::TABLE.' AS i', 'i.'.Industry::FIELD_ID, '=', 'is2.'.IndustryService::FIELD_INDUSTRY_ID)
            ->select($this->getSelectQueries())
            ->groupBy(LeadsReportColumnEnum::GROUP_KEY)
            ->where('cp.'.ConsumerProduct::CREATED_AT, '>=', $this->startDate->format('Y-m-d H:i:s'))
            ->where('cp.'.ConsumerProduct::CREATED_AT, '<', $this->endDate->format('Y-m-d H:i:s'))
            ->whereNotIn('c.' . Consumer::FIELD_CLASSIFICATION, [Consumer::CLASSIFICATION_EMAIL_ONLY, Consumer::CLASSIFICATION_VERIFIED_EMAIL_ONLY])
            ->orderBy('cp.'.ConsumerProduct::CREATED_AT);


        if ($this->group === LeadsReportGroupEnum::STATE ||
            $this->group === LeadsReportGroupEnum::COUNTY ||
            $this->group === LeadsReportGroupEnum::COMPANY_COUNTY ||
            $this->group === LeadsReportGroupEnum::ZIP_CODE ||
            $this->group === LeadsReportGroupEnum::CAMPAIGN ||
            $this->group === LeadsReportGroupEnum::CAMPAIGN_COUNTY ||
            $this->group === LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE ||
            in_array(LeadsReportColumnEnum::STATE, $this->columns) ||
            in_array(LeadsReportColumnEnum::COUNTY, $this->columns) ||
            in_array(LeadsReportColumnEnum::CAMPAIGN_ZIP_CODES, $this->columns) ||
            in_array(LeadsReportColumnEnum::SOLD_STATE_COUNT, $this->columns) ||
            in_array(LeadsReportColumnEnum::SOLD_COUNTY_COUNT, $this->columns) ||
            in_array(LeadsReportColumnEnum::SOLD_ZIP_COUNT, $this->columns) ||
            in_array(LeadsReportColumnEnum::GENERATED_STATE_COUNT, $this->columns) ||
            in_array(LeadsReportColumnEnum::GENERATED_COUNTY_COUNT, $this->columns) ||
            in_array(LeadsReportColumnEnum::GENERATED_ZIP_COUNT, $this->columns) ||
            $this->states ||
            $this->counties ||
            $this->limitedLocations) {
            $query->join($this->db.'.'.Address::TABLE.' as a', 'a.'.Address::FIELD_ID, '=', 'cp.'.ConsumerProduct::FIELD_ADDRESS_ID)
                ->leftJoin(Location::TABLE.' as l', function(JoinClause $join) {
                    // Left join dramatically speeds query up. There is only one locations record per non-null zip code so left join is fine
                    $join->on('l.'.Location::ZIP_CODE, '=', 'a.'.Address::FIELD_ZIP_CODE)
                        ->whereNotNull('l.'.Location::ZIP_CODE);
                });
        }

        if (in_array(LeadsReportColumnEnum::COMPANY_ALL_TIME_REVENUE, $this->columns)) {
            $companyRevenueSubQuery = EloquentInvoice::query()
                ->join($this->dbRead.'.'.EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, '=', EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID)
                ->select([
                    DB::raw('SUM((' . EloquentInvoiceItem::TABLE.'.'.EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::TABLE.'.'.EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TABLE.'.'.EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS company_all_time_revenue'),
                    EloquentInvoice::TABLE.'.'.EloquentInvoice::COMPANY_ID,
                ])
                ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID)
                ->groupBy(EloquentInvoice::TABLE.'.'.EloquentInvoice::COMPANY_ID);

            $query = $query->leftJoinSub($companyRevenueSubQuery, LeadsReportColumnEnum::COMPANY_REV_TABLE, function ($join) {
                $join->on(LeadsReportColumnEnum::COMPANY_REV_TABLE.'.'.EloquentInvoice::COMPANY_ID, '=', LeadsReportColumnEnum::COMPANIES_TABLE.'.'.Company::FIELD_LEGACY_ID);
            });
        }

        if (in_array(LeadsReportColumnEnum::TIME_FRAME_REVENUE, $this->columns)) {
            $timeFrameRevenueSubQuery = ProductAssignment::query()
                ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID, '=', ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
                ->leftJoin(ProductRejection::TABLE, ProductRejection::TABLE.'.'.ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID, '=', ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID)
                ->leftJoin(ProductCancellation::TABLE, ProductCancellation::TABLE.'.'.ProductCancellation::FIELD_PRODUCT_ASSIGNMENT_ID, '=', ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID)
                ->select([
                    DB::raw('SUM('.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COST.') as time_frame_revenue'),
                ])
                ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::CREATED_AT, '>=', $this->startDate->format('Y-m-d H:i:s'))
                ->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::CREATED_AT, '<', $this->endDate->format('Y-m-d H:i:s'))
                ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
                ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
                ->whereNull(ProductRejection::TABLE . '.' . ProductRejection::FIELD_ID)
                ->whereNull(ProductCancellation::TABLE . '.' . ProductCancellation::FIELD_ID);

            $query = $query->crossJoinSub($timeFrameRevenueSubQuery, LeadsReportColumnEnum::TIME_FRAME_REV_TABLE);
        }

        if (in_array(LeadsReportColumnEnum::TIME_FRAME_AD_COST, $this->columns)) {
            $timeFrameCostSubQuery = DailyAdCost::query()
                ->join(Location::TABLE, Location::TABLE.'.'.Location::ID, '=', DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_LOCATION_ID)
                ->select([
                    DB::raw('SUM('.DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_COST.') as time_frame_ad_cost'),
                ])
                ->where(DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_DATE, '>=', $this->startDate->format('Y-m-d'))
                ->where(DailyAdCost::TABLE.'.'.DailyAdCost::FIELD_DATE, '<', $this->endDate->format('Y-m-d'))
                ->where(Location::TABLE.'.'.Location::TYPE, '=', Location::TYPE_COUNTY);

            $query = $query->crossJoinSub($timeFrameCostSubQuery, LeadsReportColumnEnum::TIME_FRAME_COST_TABLE);
        }

        if ($this->group === LeadsReportGroupEnum::CAMPAIGN ||
            $this->group === LeadsReportGroupEnum::CAMPAIGN_COUNTY ||
            $this->group === LeadsReportGroupEnum::CAMPAIGN_ZIP_CODE ||
            $this->campaigns ||
            $this->campaignStatuses ||
            in_array(LeadsReportColumnEnum::CAMPAIGN, $this->columns)
        ) {
            $query->leftJoin($this->db . '.' . Budget::TABLE . ' AS b', 'b.' . Budget::FIELD_ID, '=', 'pa.' . ProductAssignment::FIELD_BUDGET_ID)
                ->leftJoin($this->db . '.' . BudgetContainer::TABLE . ' AS bc', 'bc.' . BudgetContainer::FIELD_ID, '=', 'b.' . Budget::FIELD_BUDGET_CONTAINER_ID)
                ->leftJoin($this->db . '.' . CompanyCampaign::TABLE . ' AS cc', 'cc.' . CompanyCampaign::FIELD_ID, '=', 'bc.' . BudgetContainer::FIELD_CAMPAIGN_ID);
        }

        if ($this->group === LeadsReportGroupEnum::AFFILIATE_CAMPAIGN) {
            $query->leftJoin($this->db . '.' . Campaign::TABLE . ' as afc', 'afc.'.Campaign::FIELD_ID, '=', 'cpar.'.ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID);
        }

        if ($this->origins || $this->group === LeadsReportGroupEnum::ORIGIN) {
            $query->join($this->db.'.'.Website::TABLE.' as w', 'w.'.Website::FIELD_ID, '=', 'c.'.Consumer::FIELD_WEBSITE_ID);
        }

        if ($this->group === LeadsReportGroupEnum::COMPANY || $this->group === LeadsReportGroupEnum::COMPANY_COUNTY)
            $query->whereNotNull('co.'.Company::FIELD_ID);

        if ($this->group === LeadsReportGroupEnum::COMPANY_USER_RELATIONSHIP ||
            $this->companyUserRelations) {
            // This join will duplicate consumer product entries, only to be used when grouping by company user relationship or filtering by it
            $query
                ->join(CompanyUserRelationship::TABLE.' as cur', 'cur.'.CompanyUserRelationship::FIELD_COMPANY_ID, '=', 'co.'.Company::FIELD_ID)
                ->join(User::TABLE.' as u', 'u.'.User::FIELD_ID, '=', 'cur.'.CompanyUserRelationship::FIELD_USER_ID)
                ->whereNull('cur.'.CompanyUserRelationship::FIELD_DELETED_AT);
        }

        if (in_array(LeadsReportColumnEnum::PING_POST_LEADS, $this->columns) ||
            in_array(LeadsReportColumnEnum::PING_POST_REVENUE, $this->columns) ||
            ($this->leadTypes && in_array(LeadsReportLeadTypeFilterable::PING_POST, $this->leadTypes))
        ) {
            $query->leftJoin(PingPostPublisherLead::TABLE.' as ppl', 'ppl.'.PingPostPublisherLead::FIELD_CONSUMER_PRODUCT_ID, '=', 'cp.'.ConsumerProduct::FIELD_ID);
        }

        if ($this->limitedLocations)
            $query = $this->locationLimit($query);
        if ($this->companyUserRelations)
            $query->whereIn('cur.'.CompanyUserRelationship::FIELD_USER_ID, $this->companyUserRelations);
        if ($this->industries)
            $query->whereIn('i.'.Industry::FIELD_ID, $this->industries);
        if ($this->states)
            $query->whereIn('l.'.Location::STATE, $this->states);
        if ($this->counties)
            $query->whereIn('l.'.Location::STATE, $this->states)
                ->whereIn('l.'.Location::COUNTY, $this->counties);
        if ($this->companyId)
            $query->where('co.'.Company::FIELD_ID, $this->companyId);
        if ($this->campaigns)
            $query->whereIn('cc.'.CompanyCampaign::FIELD_ID, $this->campaigns);
        if ($this->campaignStatuses)
            $query->whereIn('cc.'.CompanyCampaign::FIELD_STATUS, $this->campaignStatuses);
        if ($this->origins)
            $query->whereIn('w.'.Website::FIELD_ID, $this->origins);
        if ($this->advertisers)
            $query->whereIn(
                'cpar.'.ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID,
                array_map(function ($advertiser) {return Advertiser::fromKey($advertiser)->getAffiliateCode();}, $this->advertisers)
            );
        if ($this->platforms)
            $query = $this->platformLimit($query);
        if ($this->leadTypes)
            $query = $this->leadTypesFilter($query);

        return $query;
    }
}
