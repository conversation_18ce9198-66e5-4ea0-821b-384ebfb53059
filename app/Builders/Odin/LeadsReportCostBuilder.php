<?php

namespace App\Builders\Odin;

use App\Contracts\Builders\Odin\LeadsReport\LeadsReportBuilderContract;
use App\Enums\Advertising\Advertiser;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\LeadsReport\LeadsReportColumnEnum;
use App\Enums\LeadsReport\LeadsReportGroupEnum;
use App\Enums\LeadsReport\LeadsReportQueryEnum;
use App\Models\DailyAdCost;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Services\Filterables\LeadsReport\LeadsReportAdvertiserFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportIndustryFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportLocationFilterable;
use App\Services\Filterables\LeadsReport\LeadsReportPlatformFilterable;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class LeadsReportCostBuilder implements LeadsReportBuilderContract
{
    /**
     * @param  Carbon|null  $startDate
     * @param  Carbon|null  $endDate
     * @param  LeadsReportGroupEnum|null  $group
     * @param  array|null  $columns
     * @param  Collection|null  $limitedLocations
     * @param  array|null  $industries
     * @param  array|null  $states
     * @param  array|null  $counties
     * @param  array|null  $advertisers
     * @param  array|null  $platforms
     * @param  string  $db
     * @param  string  $dbRead
     */
    public function __construct(
        protected ?Carbon $startDate = null,
        protected ?Carbon $endDate = null,
        protected ?LeadsReportGroupEnum $group = null,
        protected ?array $columns = null,
        protected ?Collection $limitedLocations = null,
        protected ?array $industries = null,
        protected ?array $states = null,
        protected ?array $counties = null,
        protected ?array $advertisers = null,
        protected ?array $platforms = null,
        protected string $db = '',
        protected string $dbRead = '',
    ){}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param Carbon|null $startDate
     * @return $this
     */
    public function forStartDate(?Carbon $startDate = null): self
    {
        $this->startDate = $startDate;

        return $this;
    }

    /**
     * @param Carbon|null $endDate
     * @return $this
     */
    public function forEndDate(?Carbon $endDate = null): self
    {
        $this->endDate = $endDate;

        return $this;
    }

    /**
     * @param LeadsReportGroupEnum|null $group
     * @return $this
     */
    public function forGroup(?LeadsReportGroupEnum $group = null): self
    {
        $this->group = $group;

        return $this;
    }

    /**
     * @param array|null $columns
     * @return $this
     */
    public function forColumns(?array $columns = null): self
    {
        $this->columns = $columns;

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return self
     */
    public function forCompany(?int $companyId): self
    {
        // Company has no effect on cost builder
        return $this;
    }

    /**
     * @param array|null $campaigns
     * @return self
     */
    public function forCampaigns(?array $campaigns): self
    {
        // Campaigns have no effect on cost builder
        return $this;
    }

    /**
     * @param Collection|null $limitedLocations
     * @return $this
     */
    public function forLimitedLocations(?Collection $limitedLocations = null): self
    {
        $this->limitedLocations = $limitedLocations;

        return $this;
    }

    /**
     * @param mixed|null $filters
     * @return $this
     */
    public function withFilters(mixed $filters = null): self
    {
        $this->states = $filters?->{LeadsReportLocationFilterable::ID}?->{LeadsReportLocationFilterable::ID} ?? null;
        $this->counties = $filters?->{LeadsReportLocationFilterable::ID}?->{LeadsReportLocationFilterable::CHILD_FILTERABLE_COUNTY_ID} ?? null;
        $this->industries = $filters?->{LeadsReportIndustryFilterable::ID} ?? null;
        $this->advertisers = $filters?->{LeadsReportAdvertiserFilterable::ID} ?? null;
        $this->platforms = $filters?->{LeadsReportPlatformFilterable::ID} ?? null;

        return $this;
    }

    /**
     * @param int $industryId
     * @return $this
     */
    public function forIndustryId(int $industryId):self
    {
        $this->industries = [$industryId];
        return $this;
    }

    /**
     * @param string $db
     * @return $this
     */
    public function fromDatabase(string $db = ''): self
    {
        $this->db = $db;

        return $this;
    }

    /**
     * @param string $dbRead
     * @return $this
     */
    public function fromReadonlyDatabase(string $dbRead = ''): self
    {
        $this->dbRead = $dbRead;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function locationLimit(Builder $query): Builder
    {
        return $query->where(function($query) {
            foreach ($this->limitedLocations as $location) {
                $query->orWhere(function($query) use ($location) {
                    $query->where('l.'.Location::STATE, $location->{Location::STATE})
                        ->where('l.'.Location::COUNTY, $location->{Location::COUNTY});
                });
            }
        });
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function platformLimit(Builder $query): Builder
    {
        return $query->where(function($query) {
            if (in_array(AdvertisingPlatform::GOOGLE->value, $this->platforms))
                $query->orWhere('dac.' . DailyAdCost::FIELD_PLATFORM, AdvertisingPlatform::getInteger(AdvertisingPlatform::GOOGLE->value));
            if (in_array(AdvertisingPlatform::META->value, $this->platforms))
                $query->orWhere('dac.' . DailyAdCost::FIELD_PLATFORM, AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value));
            if (in_array(AdvertisingPlatform::MICROSOFT->value, $this->platforms))
                $query->orWhere('dac.' . DailyAdCost::FIELD_PLATFORM, AdvertisingPlatform::getInteger(AdvertisingPlatform::MICROSOFT->value));
        });
    }

    /**
     * @return string
     * @throws Exception
     */
    protected function getGroupSelectQuery(): string
    {
        // Override for platform
        if ($this->group === LeadsReportGroupEnum::PLATFORM)
            return
                'CASE WHEN dac.'.DailyAdCost::FIELD_PLATFORM.' = '.AdvertisingPlatform::getInteger(AdvertisingPlatform::GOOGLE->value).' THEN \'Google\' '.
                'WHEN dac.'.DailyAdCost::FIELD_PLATFORM.' = '.AdvertisingPlatform::getInteger(AdvertisingPlatform::META->value).' THEN \'Meta\' '.
                'WHEN dac.'.DailyAdCost::FIELD_PLATFORM.' = '.AdvertisingPlatform::getInteger(AdvertisingPlatform::MICROSOFT->value).' THEN \'Microsoft\' '.
                'END as '.LeadsReportColumnEnum::GROUP_KEY;

        return LeadsReportGroupEnum::getSelectQuery($this->group, 'dac.'.DailyAdCost::FIELD_DATE);
    }

    /**
     * @return array
     * @throws Exception
     */
    protected function getSelectQueries(): array
    {
        $selectQueries = [DB::raw($this->getGroupSelectQuery())];
        $calculatedColumns = LeadsReportColumnEnum::getAllCalculatedColumns();

        foreach ($this->columns as $column) {
            /** @var LeadsReportColumnEnum $column */
            if (!in_array($column, $calculatedColumns) && in_array(LeadsReportQueryEnum::COST, LeadsReportColumnEnum::getQueryType($column))) {
                $selectQueries[] = DB::raw($column->getSelectQuery());
            }
        }
        return $selectQueries;
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = DB::table($this->db.'.'.DailyAdCost::TABLE.' AS dac')
            ->join(Location::TABLE.' as l', 'l.'.Location::ID, '=', 'dac.'.DailyAdCost::FIELD_LOCATION_ID)
            ->select($this->getSelectQueries())
            ->groupBy(LeadsReportColumnEnum::GROUP_KEY)
            ->where('dac.'.DailyAdCost::FIELD_DATE, '>=', $this->startDate->format('Y-m-d'))
            ->where('dac.'.DailyAdCost::FIELD_DATE, '<', $this->endDate->format('Y-m-d'))
            ->where('l.'.Location::TYPE, '=', Location::TYPE_COUNTY)
            ->orderBy('dac.'.DailyAdCost::FIELD_DATE);

        if ($this->industries || LeadsReportGroupEnum::INDUSTRY) {
            $query->join($this->db.'.'.Industry::TABLE.' AS i', 'i.'.Industry::FIELD_ID, '=', 'dac.'.DailyAdCost::FIELD_INDUSTRY_ID);
        }

        if ($this->industries)
            $query->whereIn('i.'.Industry::FIELD_ID, $this->industries);
        if ($this->states)
            $query->whereIn('l.'.Location::STATE, $this->states);
        if ($this->counties)
            $query->whereIn('l.'.Location::STATE, $this->states)
                ->whereIn('l.'.Location::COUNTY, $this->counties);

        if ($this->limitedLocations)
            $query = $this->locationLimit($query);

        if ($this->advertisers)
            $query->whereIn('dac.'.DailyAdCost::FIELD_ADVERTISER, array_map(function ($advertiser) {return Advertiser::fromKey($advertiser)->value;}, $this->advertisers));

        if ($this->platforms)
            $query = $this->platformLimit($query);

        return $query;
    }
}
