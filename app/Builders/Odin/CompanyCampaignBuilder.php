<?php

namespace App\Builders\Odin;

use App\Enums\Campaigns\CampaignStatus as CampaignStatusEnum;
use App\Enums\Campaigns\CampaignType;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\IndustryServiceSlug as ServiceEnum;
use App\Enums\Odin\PropertyType;
use App\Models\Odin\PropertyType as PropertyTypeModel;
use App\Models\CompanyCampaignPropertyType;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class CompanyCampaignBuilder
{
    const DEFAULT_PAGINATION_LIMIT = 10;

    protected bool $companyJoined = false;

    /**
     * @param ?int $companyId
     * @param ?CampaignStatusEnum[] $statuses
     * @param ?IndustryEnum[] $industries
     * @param ?ServiceEnum[] $services
     * @param ?ProductEnum[] $products
     * @param array|null $companyStatuses
     * @param array|null $propertyTypes
     * @param array|null $eagerLoadRelations
     * @param array|null $companyConsolidatedStatuses
     * @param bool|null $excludeLowBidCampaigns
     * @param bool|null $excludeCustomBudgets
     * @param array|null $companyAdminStatues
     * @param array|null $companySystemStatuses
     * @param bool|null $excludeCampaignsWithAdAutomationDisabled
     */
    public function __construct(
        protected ?int $companyId = null,
        protected ?array $statuses = null,
        protected ?array $industries = null,
        protected ?array $services = null,
        protected ?array $products = null,
        protected ?array $companyStatuses = null,
        protected ?array $propertyTypes = null,
        protected ?array $eagerLoadRelations = null,
        protected ?array $companyConsolidatedStatuses = null,
        protected ?bool $excludeLowBidCampaigns = null,
        protected ?bool $excludeCustomBudgets = false,
        protected ?array $companyAdminStatues = null,
        protected ?array $companySystemStatuses = null,
        protected ?bool $excludeCampaignsWithAdAutomationDisabled = null,
    ) {}

    public static function query(): self
    {
        return new self();
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * @param ?CampaignStatusEnum[] $statuses
     * @return $this
     */
    public function forStatuses(?array $statuses): self
    {
        $this->statuses = $statuses;

        return $this;
    }

    /**
     * @param ?IndustryEnum[] $industries
     * @return $this
     */
    public function forIndustries(?array $industries): self
    {
        $this->industries = $industries;

        return $this;
    }

    /**
     * @param ?ServiceEnum[] $services
     * @return $this
     */
    public function forServices(?array $services): self
    {
        $this->services = $services;

        return $this;
    }

    /**
     * @param ?ProductEnum[] $products
     * @return $this
     */
    public function forProducts(?array $products): self
    {
        $this->products = $products;

        return $this;
    }

    /**
     * @param array $companyStatues
     *
     * @return $this
     * @deprecated
     */
    public function forCompanyStatues(array $companyStatues): static
    {
        $this->companyStatuses = $companyStatues;

        return $this;
    }

    /**
     * @param CompanyConsolidatedStatus[] $statuses
     *
     * @return $this
     */
    public function forCompanyConsolidatedStatuses(array $statuses): static
    {
        $this->companyConsolidatedStatuses = $statuses;

        return $this;
    }

    /**
     * @param PropertyType[] $propertyTypes
     *
     * @return $this
     */
    public function forPropertyTypes(array $propertyTypes): static
    {
        $this->propertyTypes = $propertyTypes;

        return $this;
    }

    /**
     * @param string[] $relations
     *
     * @return $this
     */
    public function eagerLoadRelations(array $relations): static
    {
        $this->eagerLoadRelations = $relations;

        return $this;
    }

    /**
     * @param bool|null $excludeLowBids
     * @return $this
     */
    public function excludeLowBids(?bool $excludeLowBids = true): self
    {
        $this->excludeLowBidCampaigns = $excludeLowBids;

        return $this;
    }

    /**
     * Remove campaign types which don't have a standard verified budget
     *
     * @param bool|null $excludeCustomBudgets
     * @return $this
     */
    public function excludeCustomBudgets(?bool $excludeCustomBudgets = true): self
    {
        $this->excludeCustomBudgets = $excludeCustomBudgets;

        return $this;
    }

    /**
     * @param array $adminStatues
     *
     * @return $this
     */
    public function forCompanyAdminStatues(array $adminStatues): static
    {
        $this->companyAdminStatues = $adminStatues;

        return $this;
    }

    /**
     * @param array $systemStatuses
     *
     * @return $this
     */
    public function forCompanySystemStatues(array $systemStatuses): static
    {
        $this->companySystemStatuses = $systemStatuses;

        return $this;
    }

    /**
     * @param bool $value
     *
     * @return $this
     */
    public function setExclusionOfAdAutomationDisabledCampaigns(bool $value): static
    {
        $this->excludeCampaignsWithAdAutomationDisabled = $value;
        return $this;
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = CompanyCampaign::query()
            ->select(CompanyCampaign::TABLE . '.*' );

        if ($this->companyId) {
            $query->where(CompanyCampaign::FIELD_COMPANY_ID, $this->companyId);
        }

        if ($this->statuses) {
            $query->whereIn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_STATUS, $this->statuses);
        }

        if ($this->excludeCampaignsWithAdAutomationDisabled) {
            $query->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_EXCLUDED_FROM_AD_AUTOMATION, false);
        }

        if ($this->industries) {
            $industryIds = Industry::query()
                ->whereIn(Industry::FIELD_SLUG, array_map(fn(IndustryEnum $enum) => $enum->getSlug(), $this->industries))
                ->pluck(Industry::FIELD_ID)
                ->toArray();
            $serviceIds = IndustryService::query()
                ->whereIn(IndustryService::FIELD_INDUSTRY_ID, $industryIds)
                ->pluck(IndustryService::FIELD_ID)
                ->toArray();

            $query->whereIn(CompanyCampaign::FIELD_SERVICE_ID, $serviceIds);
        }

        if ($this->services) {
            $serviceIds = IndustryService::query()
                ->whereIn(IndustryService::FIELD_SLUG, array_map(fn(ServiceEnum $enum) => $enum->getSlug(), $this->services))
                ->pluck(IndustryService::FIELD_ID)
                ->toArray();

            $query->whereIn(CompanyCampaign::FIELD_SERVICE_ID, $serviceIds);
        }

        if ($this->products) {
            $productIds = Product::query()
                ->whereIn(Product::FIELD_NAME, array_map(fn(ProductEnum $enum) => $enum->value, $this->products))
                ->pluck(Product::FIELD_ID)
                ->toArray();

            $query->whereIn(CompanyCampaign::FIELD_PRODUCT_ID, $productIds);
        }

        if ($this->companyStatuses !== null) {
            $this->filterCompanyStatuses($query);
        }

        if ($this->companyConsolidatedStatuses) {
            $this->filterCompanyConsolidatedStatuses($query);
        }

        if ($this->eagerLoadRelations !== null) {
            $query->with($this->eagerLoadRelations);
        }

        if ($this->propertyTypes !== null) {
            $this->filterPropertyTypes($query);
        }

        if ($this->excludeLowBidCampaigns) {
            $query->where(CompanyCampaign::FIELD_HAS_LOW_BIDS, false);
        }

        if ($this->excludeCustomBudgets) {
            $query->whereNotIn(CompanyCampaign::FIELD_TYPE, [
                CampaignType::UNVERIFIED_ONLY_LEAD_CAMPAIGN,
                CampaignType::EXCLUSIVE_ONLY_LEAD_CAMPAIGN,
            ]);
        }

        if ($this->companyAdminStatues) {
            $this->filterCompanyAdminStatuses($query);
        }

        if ($this->companySystemStatuses) {
            $this->filterCompanySystemStatuses($query);
        }

        return $query;
    }

    /**
     * @param int|null $page
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function getPaginatedQuery(?int $page = null, ?int $perPage = null): LengthAwarePaginator
    {
        $page = $page ?? 1;
        $perPage = $perPage ?? self::DEFAULT_PAGINATION_LIMIT;

        return $this->getQuery()
            ->paginate($perPage, ['*'], 'page', $page);
    }

    /**
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterCompanyAdminStatuses(Builder $query): void
    {
        $this->joinCompany($query);
        $query->whereIn(Company::TABLE  . '.' . Company::FIELD_ADMIN_STATUS, $this->companyAdminStatues);
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterCompanySystemStatuses(Builder $query): void
    {
        $this->joinCompany($query);
        $query->whereIn(Company::TABLE  . '.' . Company::FIELD_SYSTEM_STATUS, $this->companySystemStatuses);
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function joinCompany(Builder $query): void
    {
        if ($this->companyJoined) {
            return;
        }

        $query->join(
            Company::TABLE,
            Company::TABLE . '.' . Company::FIELD_ID,
            '=',
            CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID
        );

        $this->companyJoined = true;
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterCompanyStatuses(Builder $query): void
    {
        $this->joinCompany($query);
        $query->whereIn(Company::TABLE . '.' .  Company::FIELD_STATUS, $this->companyStatuses);
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterCompanyConsolidatedStatuses(Builder $query): void
    {
        $this->joinCompany($query);
        $query->whereIn(Company::TABLE . '.' . Company::FIELD_CONSOLIDATED_STATUS, $this->companyConsolidatedStatuses);
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterPropertyTypes(Builder $query): void
    {
        $query->join(
            CompanyCampaignPropertyType::TABLE,
            CompanyCampaignPropertyType::TABLE . '.' . CompanyCampaignPropertyType::FIELD_COMPANY_CAMPAIGN_ID,
            CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID
        )->join(
            PropertyTypeModel::TABLE,
            PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_ID,
            CompanyCampaignPropertyType::TABLE . '.' . CompanyCampaignPropertyType::FIELD_PROPERTY_TYPE_ID
        )->whereIn(PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_NAME, $this->propertyTypes);
    }
}
