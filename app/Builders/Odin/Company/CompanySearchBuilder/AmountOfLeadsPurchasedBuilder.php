<?php

namespace App\Builders\Odin\Company\CompanySearchBuilder;

use App\Contracts\Builders\Odin\Company\CompanySearchBuilder\AmountOfLeadsPurchasedBuilderContract;
use App\Enums\Logical;
use App\Enums\Operator;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Expression;
use Illuminate\Database\Query\JoinClause;

class AmountOfLeadsPurchasedBuilder implements AmountOfLeadsPurchasedBuilderContract
{
    const COLUMN_LEAD_COST_ONE = 'lead_cost_one';
    const COLUMN_LEAD_COST_TWO = 'lead_cost_two';

    protected ?int $firstFromDateTimestamp  = null;
    protected ?int $firstToDateTimestamp    = null;
    protected ?int $secondFromDateTimestamp = null;
    protected ?int $secondToDateTimestamp   = null;
    protected bool $hasFirstDateRange       = false;
    protected bool $hasSecondDateRange      = false;

    /**
     * @throws Exception
     */
    public function __construct(
        protected Builder $query,
        protected bool $active,
        protected int $firstValue,
        protected Operator $firstOperator,
        protected ?int $secondValue = null,
        protected ?Operator $secondOperator = null,
        protected ?bool $firstDateToggled = false,
        protected ?bool $secondDateToggled = false,
        protected ?Logical $logical = null,
        protected ?Carbon $firstFromDate = null,
        protected ?Carbon $firstToDate = null,
        protected ?Carbon $secondFromDate = null,
        protected ?Carbon $secondToDate = null,
    ) {
        $this->cancelIfInactive();

        $this->hasFirstDateRange  = $this->firstDateToggled && $this->firstFromDate && $this->firstToDate;
        $this->hasSecondDateRange = $this->secondDateToggled && $this->secondFromDate && $this->secondToDate;

        $this->firstFromDateTimestamp  = $this->getDateTimestamp($this->firstFromDate);
        $this->firstToDateTimestamp    = $this->getDateTimestamp($this->firstToDate);
        $this->secondFromDateTimestamp = $this->getDateTimestamp($this->secondFromDate);
        $this->secondToDateTimestamp   = $this->getDateTimestamp($this->secondToDate);

        $this->query->groupBy(Company::TABLE.'.id');

        $this->getJoin();
    }

    /**
     * If the filter is not active, throw an exception.
     *
     * @throws Exception
     */
    private function cancelIfInactive(): void
    {
        if (!$this->active) {
            throw new Exception('This builder is not active');
        }
    }

    /**
     * @param  Carbon|null  $date
     *
     * @return int|null
     */
    private function getDateTimestamp(?Carbon $date): ?int
    {
        return $date?->timestamp;
    }

    /**
     * Get the join between the companies table and the legacy leads table.
     *
     * @return void
     */
    private function getJoin(): void
    {
        $this->query->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.' as '.EloquentQuoteCompany::TABLE,
            function (JoinClause $join) {
                $join->on(Company::TABLE.".".Company::FIELD_LEGACY_ID, '=',
                    EloquentQuoteCompany::TABLE.".".EloquentQuoteCompany::COMPANY_ID);

                $days = $days ?? config('models.EloquentQuoteCompany.range_by_delivered_timestamp_in_days');
                $join->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_DELIVERED,
                    '>=', now()->subDays($days)->timestamp);

                $join->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, '=',
                    1);

                $join->where(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, '=', 1);

                if ($this->hasFirstDateRange) {
                    $join->whereBetween(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY,
                        [$this->firstFromDateTimestamp, $this->firstToDateTimestamp]);
                }

                if ($this->logical instanceof Logical && $this->hasSecondDateRange) {
                    $join->orWhereBetween(EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY,
                        [$this->secondFromDateTimestamp, $this->secondToDateTimestamp]);
                }
            });
    }

    /**
     * Get the lead cost columns depending on how many date ranges are set.
     *
     * @return $this
     */
    public function getLeadCostColumns(): self
    {
        $legacyLeadTableName = EloquentQuoteCompany::TABLE;

        $timestampOfInitialDeliveryFieldName = EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY;

        $leadCostOneColumn = self::COLUMN_LEAD_COST_ONE;
        $leadCostTwoColumn = self::COLUMN_LEAD_COST_TWO;

        $selectOneWithDateRange = "SUM(CASE WHEN `$legacyLeadTableName`.`$timestampOfInitialDeliveryFieldName` BETWEEN ? AND ? THEN `$legacyLeadTableName`.`cost` ELSE 0 END) AS `$leadCostOneColumn`";

        $selectTwoWithDateRange = "SUM(CASE WHEN `$legacyLeadTableName`.`$timestampOfInitialDeliveryFieldName` BETWEEN ? AND ? THEN `$legacyLeadTableName`.`cost` ELSE 0 END) AS `$leadCostTwoColumn`";

        $selectOneWithoutDateRange = "SUM(`$legacyLeadTableName`.`cost`) AS `$leadCostOneColumn`";

        if ($this->hasFirstDateRange && $this->hasSecondDateRange) {
            $this->query->selectRaw("$selectOneWithDateRange, $selectTwoWithDateRange", [
                $this->firstFromDateTimestamp,
                $this->firstToDateTimestamp,
                $this->secondFromDateTimestamp,
                $this->secondToDateTimestamp,
            ]);
        } elseif ($this->hasFirstDateRange) {
            $this->query->selectRaw("$selectOneWithDateRange", [
                $this->firstFromDateTimestamp,
                $this->firstToDateTimestamp,
            ]);
        } elseif ($this->hasSecondDateRange) {
            $this->query->selectRaw("$selectOneWithDateRange", [
                $this->secondFromDateTimestamp,
                $this->secondToDateTimestamp,
            ]);
        } else {
            $this->query->selectRaw("$selectOneWithoutDateRange");
        }

        return $this;
    }

    /**
     * Filter by the lead cost columns if they exist.
     *
     * @return $this
     * @see getLeadCostColumns
     */
    public function filterByLeadCosts(): self
    {
        $columnsList = $this->query->getQuery()->columns;

        $expression = null;

        foreach ($columnsList as $column) {
            if ($column instanceof Expression) {
                $expression = $column;
            }
        }

        if (!$expression instanceof Expression) {
            return $this;
        }

        $grammar = $this->query->getQuery()->getGrammar();

        $string = $expression->getValue($grammar);

        $hasLeadCostOneColumn = str_contains($string, self::COLUMN_LEAD_COST_ONE);
        $hasLeadCostTwoColumn = str_contains($string, self::COLUMN_LEAD_COST_TWO);

        $leadCostOneColumn = self::COLUMN_LEAD_COST_ONE;
        $leadCostTwoColumn = self::COLUMN_LEAD_COST_TWO;

        if (!$hasLeadCostOneColumn) {
            return $this;
        }

        if (!$hasLeadCostTwoColumn) {
            $leadCostTwoColumn = $leadCostOneColumn;
        }

        try {
            $firstOperatorSql = Operator::sqlOperator($this->firstOperator);
        } catch (Exception) {
            return $this;
        }

        $this->query->having($leadCostOneColumn, $firstOperatorSql, $this->firstValue);

        if ($this->logical instanceof Logical) {
            try {
                $secondOperatorSql = Operator::sqlOperator($this->secondOperator);
            } catch (Exception) {
                return $this;
            }

            if ($this->logical === Logical::OR) {
                $this->query->orHaving($leadCostTwoColumn, $secondOperatorSql, $this->secondValue);
            } else {
                $this->query->having($leadCostTwoColumn, $secondOperatorSql, $this->secondValue);
            }
        }

        return $this;
    }
}
