<?php

namespace App\Builders\Odin\Company;

use App\Builders\Odin\Company\CompanySearchBuilder\AmountOfLeadsPurchasedBuilder;
use App\Constants\Company\Search\AmountOfLeadsPurchasedArray;
use App\Contracts\Builders\Odin\Company\CompanySearchBuilderContract;
use App\Enums\Cadence;
use App\Enums\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\Logical;
use App\Enums\Odin\StateAbbreviation;
use App\Enums\Operator;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\CompanyMetric;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Repositories\Odin\ProductRepository;
use App\Rules\SortQueryParam;
use App\Services\DatabaseHelperService;
use Closure;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\Rules\Exists;
use Illuminate\Validation\ValidationException;

class CompanySearchBuilder implements CompanySearchBuilderContract
{
    protected bool $joinedLegacyCompanies = false;

    private const SORTABLE_APPOINTMENT_REJECTION_PERCENTAGE = 'appointment_rejection_percentage';
    private const SORTABLE_LEAD_REJECTION_PERCENTAGE        = 'lead_rejection_percentage';
    private const SORTABLE_COMPANY_NAME                     = 'company_name';
    private const SORTABLE_LEADS_PURCHASED_LAST_30_DAYS     = 'total_cost_spent';
    private const SORTABLE_SALES_STATUS                     = 'sales_status';
    private const SORTABLE_STATUS                           = 'consolidated_status';
    private Builder $query;

    const PAYLOAD_KEY_GOOGLE_REVIEW_COUNT   = 'google_review_count';
    const PAYLOAD_KEY_GOOGLE_RATING         = 'google_rating';
    const PAYLOAD_KEY_ESTIMATED_REVENUE     = 'revenue_in_thousands';
    const PAYLOAD_KEY_EMPLOYEE_COUNT        = 'employee_count';

    const PAYLOAD_KEYS = [
        self::PAYLOAD_KEY_GOOGLE_REVIEW_COUNT,
        self::PAYLOAD_KEY_GOOGLE_RATING,
        self::PAYLOAD_KEY_ESTIMATED_REVENUE,
        self::PAYLOAD_KEY_EMPLOYEE_COUNT
    ];

    /**
     * @param  ProductRepository|null  $productRepository
     * @param  string|null  $searchText
     * @param  array  $orderBy
     * @param  array  $statusIds
     * @param  array  $salesStatusIds
     * @param  array  $stateAbbreviations
     * @param  array  $accountManagerIds
     * @param  array  $industryIds
     * @param  Logical  $industryLogical
     * @param  array  $officeLocationIds
     * @param  Logical  $officeLocationLogical
     * @param  string|null  $companyName
     * @param  string|null  $companyContact
     * @param  array  $successManagerIds
     * @param  array  $amountOfLeadsPurchasedObject
     * @param  bool|null  $neverExceedsBudget
     * @param  Cadence|null  $cadence
     * @param  array  $campaignStatusIds
     * @param  bool|null  $campaignBudgetNoLimitSelected
     * @param  int|null  $campaignBudgetVolumeInput
     * @param  Operator|null  $campaignBudgetVolumeOperator
     * @param  int|null  $campaignBudgetCostInput
     * @param  Operator|null  $campaignBudgetCostOperator
     * @param  array  $campaignServiceAreas
     * @param  Logical  $campaignServiceAreasLogical
     * @param  int|null  $leadRejectionFirstInput
     * @param  int|null  $leadRejectionSecondInput
     * @param  Operator|null  $leadRejectionFirstOperator
     * @param  Operator|null  $leadRejectionSecondOperator
     * @param  Logical|null  $leadRejectionLogical
     * @param  int|null  $appointmentRejectionFirstInput
     * @param  int|null  $appointmentRejectionSecondInput
     * @param  Operator|null  $appointmentRejectionFirstOperator
     * @param  Operator|null  $appointmentRejectionSecondOperator
     * @param  Logical|null  $appointmentRejectionLogical
     * @param  int|null  $employeeCountFirstInput
     * @param  Operator|null  $employeeCountFirstOperator
     * @param  Logical|null  $employeeCountLogical
     * @param  int|null  $employeeCountSecondInput
     * @param  Operator|null  $employeeCountSecondOperator
     * @param  int|null  $estimatedRevenueFirstInput
     * @param  Operator|null  $estimatedRevenueFirstOperator
     * @param  Logical|null  $estimatedRevenueLogical
     * @param  int|null  $estimatedRevenueSecondInput
     * @param  Operator|null  $estimatedRevenueSecondOperator
     * @param  int|null  $consumerRatingFirstInput
     * @param  Operator|null  $consumerRatingFirstOperator
     * @param  Logical|null  $consumerRatingLogical
     * @param  int|null  $consumerRatingSecondInput
     * @param  Operator|null  $consumerRatingSecondOperator
     * @param  int|null  $consumerReviewCountFirstInput
     * @param  Operator|null  $consumerReviewCountFirstOperator
     * @param  Logical|null  $consumerReviewCountLogical
     * @param  int|null  $consumerReviewCountSecondInput
     * @param  Operator|null  $consumerReviewCountSecondOperator
     * @param  int|null  $googleRatingFirstInput
     * @param  Operator|null  $googleRatingFirstOperator
     * @param  Logical|null  $googleRatingLogical
     * @param  int|null  $googleRatingSecondInput
     * @param  Operator|null  $googleRatingSecondOperator
     * @param  int|null  $googleReviewCountFirstInput
     * @param  Operator|null  $googleReviewCountFirstOperator
     * @param  Logical|null  $googleReviewCountLogical
     * @param  int|null  $googleReviewCountSecondInput
     * @param  Operator|null  $googleReviewCountSecondOperator
     */
    public function __construct(
        protected ?ProductRepository $productRepository = null,
        protected ?string $searchText = null,
        protected array $orderBy = [],
        protected array $statusIds = [],
        protected array $salesStatusIds = [],
        protected array $stateAbbreviations = [],
        protected array $accountManagerIds = [],
        protected array $industryIds = [],
        protected Logical $industryLogical = Logical::OR,
        protected array $officeLocationIds = [],
        protected Logical $officeLocationLogical = Logical::OR,
        protected ?string $companyName = null,
        protected ?string $companyContact = null,
        protected array $successManagerIds = [],
        protected array $amountOfLeadsPurchasedObject = [
            AmountOfLeadsPurchasedArray::FIRST_DATE_TOGGLED => false,
            AmountOfLeadsPurchasedArray::SECOND_DATE_TOGGLED => false,
            AmountOfLeadsPurchasedArray::FIRST_FROM_DATE => null,
            AmountOfLeadsPurchasedArray::FIRST_TO_DATE => null,
            AmountOfLeadsPurchasedArray::SECOND_FROM_DATE => null,
            AmountOfLeadsPurchasedArray::SECOND_TO_DATE => null,
            AmountOfLeadsPurchasedArray::ACTIVE => false,
            AmountOfLeadsPurchasedArray::FIRST_VALUE => 0,
            AmountOfLeadsPurchasedArray::SECOND_VALUE => null,
            AmountOfLeadsPurchasedArray::FIRST_OPERATOR => Operator::GREATER_THAN,
            AmountOfLeadsPurchasedArray::SECOND_OPERATOR => null,
            AmountOfLeadsPurchasedArray::LOGICAL => null,
        ],
        protected ?bool $neverExceedsBudget = null,
        protected ?Cadence $cadence = null,
        protected array $campaignStatusIds = [],
        protected ?bool $campaignBudgetNoLimitSelected = null,
        protected ?int $campaignBudgetVolumeInput = null,
        protected ?Operator $campaignBudgetVolumeOperator = null,
        protected ?int $campaignBudgetCostInput = null,
        protected ?Operator $campaignBudgetCostOperator = null,
        protected array $campaignServiceAreas = [],
        protected Logical $campaignServiceAreasLogical = Logical::OR,
        protected ?int $leadRejectionFirstInput = null,
        protected ?int $leadRejectionSecondInput = null,
        protected ?Operator $leadRejectionFirstOperator = null,
        protected ?Operator $leadRejectionSecondOperator = null,
        protected ?Logical $leadRejectionLogical = null,
        protected ?int $appointmentRejectionFirstInput = null,
        protected ?int $appointmentRejectionSecondInput = null,
        protected ?Operator $appointmentRejectionFirstOperator = null,
        protected ?Operator $appointmentRejectionSecondOperator = null,
        protected ?Logical $appointmentRejectionLogical = null,
        protected ?int $employeeCountFirstInput = null,
        protected ?Operator $employeeCountFirstOperator = null,
        protected ?Logical $employeeCountLogical = null,
        protected ?int $employeeCountSecondInput = null,
        protected ?Operator $employeeCountSecondOperator = null,
        protected ?int $estimatedRevenueFirstInput = null,
        protected ?Operator $estimatedRevenueFirstOperator = null,
        protected ?Logical $estimatedRevenueLogical = null,
        protected ?int $estimatedRevenueSecondInput = null,
        protected ?Operator $estimatedRevenueSecondOperator = null,
        protected ?int $consumerRatingFirstInput = null,
        protected ?Operator $consumerRatingFirstOperator = null,
        protected ?Logical $consumerRatingLogical = null,
        protected ?int $consumerRatingSecondInput = null,
        protected ?Operator $consumerRatingSecondOperator = null,
        protected ?int $consumerReviewCountFirstInput = null,
        protected ?Operator $consumerReviewCountFirstOperator = null,
        protected ?Logical $consumerReviewCountLogical = null,
        protected ?int $consumerReviewCountSecondInput = null,
        protected ?Operator $consumerReviewCountSecondOperator = null,
        protected ?int $googleRatingFirstInput = null,
        protected ?Operator $googleRatingFirstOperator = null,
        protected ?Logical $googleRatingLogical = null,
        protected ?int $googleRatingSecondInput = null,
        protected ?Operator $googleRatingSecondOperator = null,
        protected ?int $googleReviewCountFirstInput = null,
        protected ?Operator $googleReviewCountFirstOperator = null,
        protected ?Logical $googleReviewCountLogical = null,
        protected ?int $googleReviewCountSecondInput = null,
        protected ?Operator $googleReviewCountSecondOperator = null,
    ) {
        $this->query = Company::query()
            ->leftJoin(CompanyData::TABLE, CompanyData::TABLE .'.'. CompanyData::FIELD_COMPANY_ID,
                '=', Company::TABLE .'.'. Company::FIELD_ID)
            ->leftJoin(CompanyMetric::TABLE, CompanyMetric::TABLE .'.'. CompanyMetric::FIELD_COMPANY_ID,
                '=', Company::TABLE .'.'. Company::FIELD_ID);

        $this->query->from(DatabaseHelperService::database().'.'.Company::TABLE.' as '.Company::TABLE);

        $subQuery = InvoiceSnapshot::mostRecentByInvoice()
            ->select(
                InvoiceSnapshot::TABLE .'.'. InvoiceSnapshot::FIELD_COMPANY_ID,
                DB::raw('SUM('.InvoiceSnapshot::TABLE .'.'. InvoiceSnapshot::FIELD_TOTAL_PAID.') AS lifetime_revenue'),
            )->groupBy(InvoiceSnapshot::TABLE .'.'. InvoiceSnapshot::FIELD_COMPANY_ID);

        $this->query->distinct(Company::TABLE.'.'.Company::FIELD_ID)
            ->select(
                Company::TABLE.'.'.Company::FIELD_ID,
                Company::TABLE.'.'.Company::FIELD_NAME,
                Company::TABLE.'.'.Company::FIELD_ENTITY_NAME,
                Company::TABLE.'.'.Company::FIELD_ADMIN_STATUS,
                Company::TABLE.'.'.Company::FIELD_SYSTEM_STATUS,
                Company::TABLE.'.'.Company::FIELD_CAMPAIGN_STATUS,
                Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS,
                Company::TABLE.'.'.Company::FIELD_SALES_STATUS,
                Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                Company::TABLE .'.'. Company::FIELD_LEGACY_LIFETIME_REVENUE_CENT,
                DB::raw('COALESCE(invoice_snapshot.lifetime_revenue, 0) + '. Company::TABLE .'.'. Company::FIELD_LEGACY_LIFETIME_REVENUE_CENT . ' as lifetime_revenue'),
                CompanyData::TABLE .'.'. CompanyData::FIELD_PAYLOAD,
            )
            ->leftJoinSub($subQuery, 'invoice_snapshot',
                'invoice_snapshot.company_id',
                '=',
                Company::TABLE.'.'.Company::FIELD_ID
            );
    }

    public static function query(): self
    {
        return new self();
    }

    public function getQuery(): Builder
    {
        $this->filterByCompanyName();

        $this->filterByCompanyContact();

        return $this->query;
    }

    public function searchText(?string $search): self
    {
        $this->companyName = $search;

        return $this;
    }

    public function searchContact(?string $search): self
    {
        $this->companyContact = $search;

        return $this;
    }

    /**
     * @param  array  $statusIds
     *
     * @return $this
     * @throws ValidationException
     */
    public function setStatusIds(array $statusIds): self
    {
        Validator::make($statusIds, [
            '*' => [new Enum(CompanyConsolidatedStatus::class)],
        ])->validate();

        $this->statusIds = $statusIds;

        return $this;
    }

    /**
     * @param  array  $salesStatusIds
     *
     * @return $this
     * @throws ValidationException
     */
    public function setSalesStatusIds(array $salesStatusIds): self
    {
        Validator::make($salesStatusIds, [
            '*' => [new Enum(CompanySalesStatus::class)],
        ])->validate();

        $this->salesStatusIds = $salesStatusIds;

        return $this;
    }

    /**
     * @param  array  $stateAbbreviations
     *
     * @return $this
     * @throws ValidationException
     */
    public function setStateAbbreviations(array $stateAbbreviations): self
    {
        Validator::make($stateAbbreviations, [
            '*' => [new Enum(StateAbbreviation::class)],
        ])->validate();

        $this->stateAbbreviations = $stateAbbreviations;

        return $this;
    }

    /**
     * @param  array  $accountManagerIds
     *
     * @return $this
     * @throws ValidationException
     */
    public function setAccountsManagerIds(array $accountManagerIds): self
    {
        Validator::make($accountManagerIds, [
            '*' => [new Exists(AccountManager::class, AccountManager::FIELD_ID)],
        ])->validate();

        $this->accountManagerIds = $accountManagerIds;

        return $this;
    }

    /**
     * @param  array  $industryIds
     *
     * @return $this
     * @throws ValidationException
     */
    public function setIndustryIds(array $industryIds): self
    {
        Validator::make($industryIds, [
            '*' => [new Exists(Industry::class, Industry::FIELD_ID)],
        ])->validate();

        $this->industryIds = $industryIds;

        return $this;
    }

    /**
     * @param  Logical  $industryLogical
     *
     * @return $this
     */
    public function setIndustryLogical(Logical $industryLogical): self
    {
        $this->industryLogical = $industryLogical;

        return $this;
    }

    /**
     * @param  array  $officeLocationIds
     *
     * @return $this
     * @throws ValidationException
     */
    public function setOfficeLocationIds(array $officeLocationIds): self
    {
        Validator::make($officeLocationIds, [
            '*' => [new Enum(StateAbbreviation::class)],
        ])->validate();

        $this->officeLocationIds = $officeLocationIds;

        return $this;
    }

    /**
     * @param  Logical  $officeLocationLogical
     *
     * @return $this
     */
    public function setOfficeLocationLogical(Logical $officeLocationLogical): self
    {
        $this->officeLocationLogical = $officeLocationLogical;

        return $this;
    }

    /**
     * @param  string|null  $companyName
     *
     * @return $this
     */
    public function setCompanyName(?string $companyName): self
    {
        $this->companyName = $companyName;

        return $this;
    }

    /**
     * @param  array  $successManagerIds
     *
     * @return $this
     * @throws ValidationException
     */
    public function setSuccessManagerIds(array $successManagerIds): self
    {
        Validator::make($successManagerIds, [
            '*' => [new Exists(SuccessManager::class, SuccessManager::FIELD_ID)],
        ])->validate();

        $this->successManagerIds = $successManagerIds;

        return $this;
    }

    /*
     * @param array|null $amountOfLeadsPurchasedObject
     * @return $this
     * @throws ValidationException
     */
    public function setAmountOfLeadsPurchasedObject(?array $amountOfLeadsPurchasedObject): self
    {
        if (gettype($amountOfLeadsPurchasedObject) == 'array' && !empty($amountOfLeadsPurchasedObject) && data_get($amountOfLeadsPurchasedObject,
                'active')) {
            Validator::make($amountOfLeadsPurchasedObject, [
                AmountOfLeadsPurchasedArray::FIRST_DATE_TOGGLED => [
                    'nullable', 'boolean',
                ],
                AmountOfLeadsPurchasedArray::SECOND_DATE_TOGGLED => [
                    'nullable', 'boolean',
                ],
                AmountOfLeadsPurchasedArray::FIRST_FROM_DATE => ['nullable', 'date'],
                AmountOfLeadsPurchasedArray::FIRST_TO_DATE => ['nullable', 'date'],
                AmountOfLeadsPurchasedArray::SECOND_FROM_DATE => ['nullable', 'date'],
                AmountOfLeadsPurchasedArray::SECOND_TO_DATE => ['nullable', 'date'],
                AmountOfLeadsPurchasedArray::ACTIVE => ['required', 'boolean'],
                AmountOfLeadsPurchasedArray::FIRST_VALUE => ['required', 'numeric'],
                AmountOfLeadsPurchasedArray::SECOND_VALUE => ['nullable', 'numeric'],
                AmountOfLeadsPurchasedArray::FIRST_OPERATOR => [new Enum(Operator::class)],
                AmountOfLeadsPurchasedArray::SECOND_OPERATOR => [
                    'nullable', new Enum(Operator::class),
                ],
                AmountOfLeadsPurchasedArray::LOGICAL => [
                    'nullable', new Enum(Logical::class),
                ],
            ])->validate();

            $firstOperator = AmountOfLeadsPurchasedArray::FIRST_OPERATOR;

            $amountOfLeadsPurchasedObject[$firstOperator] =
                !$amountOfLeadsPurchasedObject[$firstOperator] instanceof Operator
                    ? Operator::tryFrom($amountOfLeadsPurchasedObject[$firstOperator])
                    : $amountOfLeadsPurchasedObject[$firstOperator];

            $secondOperator = AmountOfLeadsPurchasedArray::SECOND_OPERATOR;

            $amountOfLeadsPurchasedObject[$secondOperator] =
                !$amountOfLeadsPurchasedObject[$secondOperator] instanceof Operator
                    ? Operator::tryFrom($amountOfLeadsPurchasedObject[$secondOperator])
                    : $amountOfLeadsPurchasedObject[$secondOperator];

            $logical = AmountOfLeadsPurchasedArray::LOGICAL;

            $amountOfLeadsPurchasedObject[$logical] =
                !$amountOfLeadsPurchasedObject[$logical] instanceof Logical
                    ? Logical::tryFrom($amountOfLeadsPurchasedObject[$logical])
                    : $amountOfLeadsPurchasedObject[$logical];

            $this->amountOfLeadsPurchasedObject = $amountOfLeadsPurchasedObject;
        }

        return $this;
    }

    /**
     * @return $this
     */
    public function appendUsers(): self
    {
        $this->query->with(Company::RELATION_USERS);

        return $this;
    }

    /**
     * @param  array  $relations
     *
     * @return $this
     */
    public function with(array $relations): self
    {
        $this->query->with($relations);

        return $this;
    }

    /**
     * @param  array|null  $orderBy
     *
     * @return $this
     * @throws ValidationException
     */
    public function setOrderBy(?array $orderBy): self
    {
        if ($orderBy) {
            Validator::make($orderBy, [
                '.*' => ['string', 'sometimes', new SortQueryParam],
            ])->validate();

            $this->orderBy = $orderBy;
        }

        return $this;
    }

    /**
     * @param  bool|null  $neverExceedsBudget
     *
     * @return $this
     */
    public function setNeverExceedsBudget(?bool $neverExceedsBudget): self
    {
        $this->neverExceedsBudget = $neverExceedsBudget;

        return $this;
    }

    /**
     * @param  \App\Enums\Cadence|null  $cadence
     *
     * @return $this
     */
    public function setCadence(?Cadence $cadence): self
    {
        $this->cadence = $cadence;

        return $this;
    }

    /**
     * @param  array  $serviceAreas
     * @param  Logical  $logical
     *
     * @return $this
     */
    public function setCampaignServiceAreas(array $serviceAreas, Logical $logical): self
    {
        $this->campaignServiceAreas        = $serviceAreas;
        $this->campaignServiceAreasLogical = $logical;

        return $this;
    }

    /**
     * @throws ValidationException
     */
    public function setCampaignStatusIds(array $campaignStatusIds): self
    {
        Validator::make($campaignStatusIds, [
            '*' => [new Enum(CampaignStatus::class)],
        ])->validate();

        $this->campaignStatusIds = $campaignStatusIds;

        return $this;
    }

    /**
     * @param  bool  $noLimitSelected
     * @param  int|null  $volumeInput
     * @param  Operator|null  $volumeOperator
     * @param  int|null  $costInput
     * @param  Operator|null  $costOperator
     *
     * @return $this
     */
    public function setCampaignBudget(
        ?bool $noLimitSelected,
        ?int $volumeInput,
        ?Operator $volumeOperator,
        ?int $costInput,
        ?Operator $costOperator,
    ): CompanySearchBuilder {
        $this->campaignBudgetNoLimitSelected = $noLimitSelected;
        $this->campaignBudgetVolumeInput     = $volumeInput;
        $this->campaignBudgetVolumeOperator  = $volumeOperator;
        $this->campaignBudgetCostInput       = $costInput;
        $this->campaignBudgetCostOperator    = $costOperator;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  int|null  $secondInput
     * @param  Operator|null  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  Logical|null  $logical
     * @return $this
     */
    public function setLeadRejection(
        ?int $firstInput,
        ?int $secondInput,
        ?Operator $firstOperator,
        ?Operator $secondOperator,
        ?Logical $logical
    ): self {
        $this->leadRejectionFirstInput             = $firstInput;
        $this->leadRejectionSecondInput            = $secondInput;
        $this->leadRejectionFirstOperator = $firstOperator;
        $this->leadRejectionSecondOperator = $secondOperator;
        $this->leadRejectionLogical = $logical;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  int|null  $secondInput
     * @param  Operator|null  $firstOperator
     * @param  Operator|null  $secondOperator
     * @param  Logical|null  $logical
     * @return self
     */
    public function setAppointmentRejection(
        ?int $firstInput,
        ?int $secondInput,
        ?Operator $firstOperator,
        ?Operator $secondOperator,
        ?Logical $logical
    ): self {
        $this->appointmentRejectionFirstInput             = $firstInput;
        $this->appointmentRejectionSecondInput            = $secondInput;
        $this->appointmentRejectionFirstOperator = $firstOperator;
        $this->appointmentRejectionSecondOperator = $secondOperator;
        $this->appointmentRejectionLogical = $logical;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setEmployeeCount(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self {
        $this->employeeCountFirstInput = $firstInput;
        $this->employeeCountFirstOperator = $firstOperator;
        $this->employeeCountLogical = $logical;
        $this->employeeCountSecondInput = $secondInput;
        $this->employeeCountSecondOperator = $secondOperator;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setEstimatedRevenue(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self {
        $this->estimatedRevenueFirstInput = $firstInput;
        $this->estimatedRevenueFirstOperator = $firstOperator;
        $this->estimatedRevenueLogical = $logical;
        $this->estimatedRevenueSecondInput = $secondInput;
        $this->estimatedRevenueSecondOperator = $secondOperator;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setGoogleRating(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self {
        $this->googleRatingFirstInput = $firstInput;
        $this->googleRatingFirstOperator = $firstOperator;
        $this->googleRatingLogical = $logical;
        $this->googleRatingSecondInput = $secondInput;
        $this->googleRatingSecondOperator = $secondOperator;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setGoogleReview(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self {
        $this->googleReviewCountFirstInput = $firstInput;
        $this->googleReviewCountFirstOperator = $firstOperator;
        $this->googleReviewCountLogical = $logical;
        $this->googleReviewCountSecondInput = $secondInput;
        $this->googleReviewCountSecondOperator = $secondOperator;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setConsumerRating(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self {
        $this->consumerRatingFirstInput = $firstInput;
        $this->consumerRatingFirstOperator = $firstOperator;
        $this->consumerRatingLogical = $logical;
        $this->consumerRatingSecondInput = $secondInput;
        $this->consumerRatingSecondOperator = $secondOperator;

        return $this;
    }

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return self
     */
    public function setConsumerReviewCount(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): self {
        $this->consumerReviewCountFirstInput = $firstInput;
        $this->consumerReviewCountFirstOperator = $firstOperator;
        $this->consumerReviewCountLogical = $logical;
        $this->consumerReviewCountSecondInput = $secondInput;
        $this->consumerReviewCountSecondOperator = $secondOperator;

        return $this;
    }

    /**
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->query->get();
    }

    /**
     * @param  Closure|int|null  $perPage
     * @param  array|string  $columns
     * @param  string  $pageName
     * @param  int|null  $page
     *
     * @return LengthAwarePaginator
     */
    public function paginate(
        Closure|int|null $perPage = null,
        array|string $columns = ['*'],
        string $pageName = 'page',
        int|null $page = null,
    ): LengthAwarePaginator {
        return $this->query->paginate(
            perPage: $perPage,
            columns: $columns,
            pageName: $pageName,
            page: $page,
        );
    }

    /**
     * Plucks a value from the query.
     *
     * @param $column
     * @param $key
     * @return \Illuminate\Support\Collection
     */
    public function pluck($column, $key = null): \Illuminate\Support\Collection
    {
        return $this->query->pluck($column, $key);
    }

    /**
     * @return self
     * @throws Exception
     */
    public function filter(): self
    {
        $this->filterByCompanyName();

        $this->filterByStatusIds();

        $this->filterBySalesStatusIds();

        $this->filterByAccountManagerIds();

        $this->filterBySuccessManagerIds();

        $this->filterByIndustries();

        $this->filterByOfficeLocation();

        $this->filterByLeadsPurchased();

        $this->filterByNeverExceedsBudget();

        $this->filterByConsumerReviews();

        if ($this->cadenceIsEnabled()) {
            if ($this->cadence === Cadence::NOT_ASSIGNED) {
                $this->query->whereDoesntHave(Company::RELATION_CADENCE_ROUTINES);
            } else {
                $this->query->whereHas(Company::RELATION_CADENCE_ROUTINES, function (Builder $query) {

                    if ($this->cadence === Cadence::ONGOING) {
                        $query->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_PENDING);
                    }

                    if ($this->cadence === Cadence::COMPLETED) {
                        $query->where(CompanyCadenceRoutine::FIELD_STATUS, CompanyCadenceRoutine::STATUS_CONCLUDED);
                    }
                });
            }
        }

        $this->filterByCampaignStatusOrServiceArea();

        $this->filterByCampaignBudget();

        $leadRejectionIsSet = $this->isLeadRejectionSet();
        $appointmentRejectionIsSet = $this->isAppointmentRejectionSet();

        if ($leadRejectionIsSet) {
            $leadProductId = $this->productRepository->getLeadProductId();

            $this->filterByProductRejectionPercentage(
                $leadProductId,
                $this->leadRejectionFirstInput,
                $this->leadRejectionFirstOperator,
                $this->leadRejectionLogical,
                $this->leadRejectionSecondInput,
                $this->leadRejectionSecondOperator
            );
        }

        if ($appointmentRejectionIsSet) {
            $appointmentProductId = $this->productRepository->getAppointmentProductId();

            $this->filterByProductRejectionPercentage(
                $appointmentProductId,
                $this->appointmentRejectionFirstInput,
                $this->appointmentRejectionFirstOperator,
                $this->appointmentRejectionLogical,
                $this->appointmentRejectionSecondInput,
                $this->appointmentRejectionSecondOperator
            );
        }

        $this->handleCompanyDataPayloadFilters();
        $this->orderCompanies();

        return $this;
    }

    /**
     * @return void
     */
    private function handleCompanyDataPayloadFilters(): void
    {
        foreach(self::PAYLOAD_KEYS as $payloadKey) {
            $isFilterEnabled = false;

            switch($payloadKey) {
                case self::PAYLOAD_KEY_EMPLOYEE_COUNT:
                    $isFilterEnabled = $this->employeeCountIsEnabled();
                    $firstInput      = $this->employeeCountFirstInput;
                    $firstOperator   = $this->employeeCountFirstOperator;
                    $logical         = $this->employeeCountLogical;
                    $secondInput     = $this->employeeCountSecondInput;
                    $secondOperator  = $this->employeeCountSecondOperator;
                    break;
                case self::PAYLOAD_KEY_ESTIMATED_REVENUE:
                    $isFilterEnabled = $this->estimatedRevenueIsEnabled();
                    $firstInput      = $this->estimatedRevenueFirstInput;
                    $firstOperator   = $this->estimatedRevenueFirstOperator;
                    $logical         = $this->estimatedRevenueLogical;
                    $secondInput     = $this->estimatedRevenueSecondInput;
                    $secondOperator  = $this->estimatedRevenueSecondOperator;
                    break;
                case self::PAYLOAD_KEY_GOOGLE_RATING:
                    $isFilterEnabled = $this->googleRatingIsEnabled();
                    $firstInput      = $this->googleRatingFirstInput;
                    $firstOperator   = $this->googleRatingFirstOperator;
                    $logical         = $this->googleRatingLogical;
                    $secondInput     = $this->googleRatingSecondInput;
                    $secondOperator  = $this->googleRatingSecondOperator;
                    break;
                case self::PAYLOAD_KEY_GOOGLE_REVIEW_COUNT:
                    $isFilterEnabled = $this->googleReviewCountIsEnabled();
                    $firstInput      = $this->googleReviewCountFirstInput;
                    $firstOperator   = $this->googleReviewCountFirstOperator;
                    $logical         = $this->googleReviewCountLogical;
                    $secondInput     = $this->googleReviewCountSecondInput;
                    $secondOperator  = $this->googleReviewCountSecondOperator;
                    break;
                default:
                    break;
            }

            if($isFilterEnabled) {
                $this->handleCompanyDataPayloadFilter($firstInput, $firstOperator, $logical, $secondInput, $secondOperator, $payloadKey);
            }
        }
    }

    /**
     * @param int $firstInput
     * @param Operator $firstOperator
     * @param Logical|null $logical
     * @param int|null $secondInput
     * @param Operator|null $secondOperator
     * @param string $payloadKey
     * @return void
     */
    private function handleCompanyDataPayloadFilter(
        int       $firstInput,
        Operator  $firstOperator,
        ?Logical   $logical,
        ?int      $secondInput,
        ?Operator $secondOperator,
        string    $payloadKey
    ): void
    {
        $this->query->whereHas(Company::RELATION_DATA, function ($has) use (
            $firstInput,
            $firstOperator,
            $logical,
            $secondInput,
            $secondOperator,
            $payloadKey
        ) {
            $sqlOperator = Operator::sqlOperator($firstOperator);

            $has->whereNotNull(CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->$payloadKey")
                ->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->$payloadKey",
                    $sqlOperator,
                    $firstInput
                );

            if ($logical instanceof Logical && $secondOperator instanceof Operator && gettype($secondInput) == 'integer') {
                $sqlOperator = Operator::sqlOperator($secondOperator);

                $has->where(
                    CompanyData::TABLE.'.'.CompanyData::FIELD_PAYLOAD."->$payloadKey",
                    $sqlOperator,
                    $secondInput
                );
            }
        });
    }

    /**
     * @return void
     */
    private function filterByCompanyName(): void
    {
        if ($this->isCompanyNameIsSet()) {
            /**
             * Strip special characters from the company.name column for string comparison
             */
            $stripedCompanyNameColumn = "REGEXP_REPLACE(REGEXP_REPLACE(". Company::TABLE . "." . Company::FIELD_NAME . ", '[^ A-Za-z0-9_-]', ''), '[-_ ]+', ' ')";
            $this->query->whereRaw($stripedCompanyNameColumn . ' like ' . "'%$this->companyName%'" );

            if (is_numeric($this->companyName)) {
                $this->companyName = intval($this->companyName);

                $this->query->orWhere(Company::TABLE.'.'.Company::FIELD_ID, $this->companyName);

                $this->query->orWhereExists(function (\Illuminate\Database\Query\Builder $query) {
                    $query->from(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, 'lc');

                    $query->whereColumn('lc.'.EloquentCompany::COMPANY_ID, Company::TABLE.'.'.Company::FIELD_LEGACY_ID);

                    $query->where('lc.'.EloquentCompany::COMPANY_ID, $this->companyName);
                });
            }
        }
    }

    /**
     * @return void
     */
    private function filterByCompanyContact(): void
    {
        if ($this->isCompanyContactIsSet()) {
            $this->query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                $query->whereHas(Company::RELATION_USERS, function (Builder $query) {
                    $query->where(CompanyUser::FIELD_IS_CONTACT, true);

                    $query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                        $hasNoLetters = preg_match('/[a-zA-Z]/', $this->companyContact) === 0;

                        $phoneNumberOnlyDigits = preg_replace('/\D+/', '', $this->companyContact);

                        $isProbablyEmail = filter_var($this->companyContact, FILTER_VALIDATE_EMAIL);

                        if (!!$phoneNumberOnlyDigits && $hasNoLetters && !$isProbablyEmail) {
                            $query->orWhere(CompanyUser::FIELD_FORMATTED_CELL_PHONE, 'like',
                                "%$phoneNumberOnlyDigits%");

                            $query->orWhere(CompanyUser::FIELD_FORMATTED_OFFICE_PHONE, 'like',
                                "%$phoneNumberOnlyDigits%");
                        }

                        $query->orWhere(CompanyUser::FIELD_FIRST_NAME, 'like', "%$this->companyContact%");

                        $query->orWhere(CompanyUser::FIELD_LAST_NAME, 'like', "%$this->companyContact%");

                        $query->orWhereRaw("CONCAT_WS(' ', ".CompanyUser::FIELD_FIRST_NAME.", ".CompanyUser::FIELD_LAST_NAME.") LIKE '%$this->companyContact%'");

                        $query->orWhere(CompanyUser::FIELD_EMAIL, 'like', "%$this->companyContact%");
                    });
                });

                $query->orWhere(Company::FIELD_WEBSITE, 'like', "%$this->companyContact%");
            });
        }
    }

    /**
     * @return bool
     */
    private function isCompanyNameIsSet(): bool
    {
        return !is_null($this->companyName) && strlen($this->companyName) > 0;
    }

    /**
     * @return bool
     */
    private function isCompanyContactIsSet(): bool
    {
        return !is_null($this->companyContact) && strlen($this->companyContact) > 0;
    }

    /**
     * @return void
     */
    private function filterByStatusIds(): void
    {
        if ($this->areStatusIdsSet()) {
            $this->query->whereIn(Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS, $this->statusIds);
        }
    }

    /**
     * @return bool
     */
    private function areStatusIdsSet(): bool
    {
        return !empty($this->statusIds);
    }

    /**
     * @return void
     */
    private function filterBySalesStatusIds(): void
    {
        if ($this->areSalesStatusIdsSet()) {
            $this->query->whereIn(Company::TABLE.'.'.Company::FIELD_SALES_STATUS, $this->salesStatusIds);
        }
    }

    /**
     * @return bool
     */
    private function areSalesStatusIdsSet(): bool
    {
        return !empty($this->salesStatusIds);
    }

    /**
     * @return void
     */
    private function filterByAccountManagerIds(): void
    {
        if ($this->areAccountManagerIdsSet()) {
            $this->query->whereHas(Company::RELATION_ACCOUNT_MANAGER_CLIENTS,
                function (Builder $query) {
                    $query->whereIn(AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID, $this->accountManagerIds);

                    $query->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE);

                });
        }
    }

    /**
     * @return bool
     */
    private function areAccountManagerIdsSet(): bool
    {
        return !empty($this->accountManagerIds);
    }

    /**
     * @return void
     */
    private function filterBySuccessManagerIds(): void
    {
        if ($this->areSuccessManagerIdsSet()) {
            $this->query->whereHas(Company::RELATION_SUCCESS_MANAGER_CLIENTS,
                function (Builder $query) {
                    $query->whereIn(SuccessManagerClient::FIELD_SUCCESS_MANAGER_ID, $this->successManagerIds);

                    $query->where(SuccessManagerClient::FIELD_STATUS, SuccessManagerClient::STATUS_ACTIVE);

                });
        }
    }

    /**
     * @return bool
     */
    private function areSuccessManagerIdsSet(): bool
    {
        return !empty($this->successManagerIds);
    }

    /**
     * @return void
     */
    private function filterByIndustries(): void
    {
        if ($this->areIndustriesSet()) {
            if (Logical::OR === $this->industryLogical) {
                $this->query->whereHas(Company::RELATION_INDUSTRIES, function (Builder $query) {
                    $query->whereIn(CompanyIndustry::FIELD_INDUSTRY_ID, $this->industryIds);
                });
            } else {
                $this->query->whereHas(Company::RELATION_INDUSTRIES, function (Builder $query) {
                    $query->whereIn(CompanyIndustry::FIELD_INDUSTRY_ID, $this->industryIds);
                }, '=', count($this->industryIds));
            }
        }
    }

    /**
     * @return bool
     */
    private function areIndustriesSet(): bool
    {
        return !empty($this->industryIds) && $this->industryLogical instanceof Logical;
    }

    /**
     * @return void
     */
    private function filterByOfficeLocation(): void
    {
        if ($this->areOfficeLocationIdsSet() && $this->isOfficeLocationLogicalSet()) {
            if (Logical::OR === $this->officeLocationLogical) {
                $this->query->whereHas(Company::RELATION_LOCATIONS, function (Builder $query) {
                    $query->whereHas(CompanyLocation::RELATION_ADDRESS, function (Builder $query) {
                        $query->whereIn(Address::FIELD_STATE, $this->officeLocationIds);
                    });
                });
            } else {
                $this->query->whereHas(Company::RELATION_LOCATIONS, function (Builder $query) {
                    $query->whereHas(CompanyLocation::RELATION_ADDRESS, function (Builder $query) {
                        $query->whereIn(Address::FIELD_STATE, $this->officeLocationIds);
                    });
                }, '=', count($this->officeLocationIds));
            }
        }
    }

    /**
     * @return bool
     */
    private function areOfficeLocationIdsSet(): bool
    {
        return !empty($this->officeLocationIds);
    }

    /**
     * @return bool
     */
    private function isOfficeLocationLogicalSet(): bool
    {
        return !is_null($this->officeLocationLogical);
    }

    /**
     * @return void
     */
    private function filterByLeadsPurchased(): void
    {
        if ($this->isLeadsPurchasedSet()) {
            try {
                /**
                 * @var AmountOfLeadsPurchasedBuilder $amountOfLeadsPurchasedBuilder
                 */
                $amountOfLeadsPurchasedBuilder = app(AmountOfLeadsPurchasedBuilder::class, [
                    'query' => $this->query,
                    ...$this->amountOfLeadsPurchasedObject,
                ]);

                $amountOfLeadsPurchasedBuilder
                    ->getLeadCostColumns()
                    ->filterByLeadCosts();
            } catch (Exception) {
            }
        }
    }

    /**
     * @return bool
     */
    private function isLeadsPurchasedSet(): bool
    {
        $isActive           = data_get($this->amountOfLeadsPurchasedObject, AmountOfLeadsPurchasedArray::ACTIVE);
        $firstValueIsSet    = gettype(data_get($this->amountOfLeadsPurchasedObject,
                AmountOfLeadsPurchasedArray::FIRST_VALUE)) == 'integer';
        $firstOperatorIsSet = data_get($this->amountOfLeadsPurchasedObject,
                AmountOfLeadsPurchasedArray::FIRST_OPERATOR) instanceof Operator;

        return $isActive && $firstValueIsSet && $firstOperatorIsSet;
    }

    /**
     * @return void
     */
    private function filterByNeverExceedsBudget(): void
    {
        if ($this->isNeverExceedsBudgetSet()) {
            $this->query->whereHas(Company::RELATION_LEGACY_COMPANY, function (Builder $query) {
                $query->from(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE,
                    EloquentCompany::TABLE);

                $query->where(EloquentCompany::TABLE.'.'.EloquentCompany::NEVER_EXCEED_BUDGET,
                    $this->neverExceedsBudget);
            });
        }
    }

    /**
     * @return void
     * @throws Exception
     */
    private function filterByConsumerReviews(): void
    {
        $this->joinLegacyCompaniesOnCompanies();

        if($this->consumerRatingIsEnabled() || $this->consumerReviewCountIsEnabled()) {
            $this->query->join(
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::COMPANY_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
            );
        }

        if($this->consumerRatingIsEnabled()) {
            $this->query->where(
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::BAYESIAN_ALL_TIME,
                Operator::sqlOperator($this->consumerRatingFirstOperator),
                $this->consumerRatingFirstInput
            );
        }

        if($this->consumerReviewCountIsEnabled()) {
            $this->query->where(
                DatabaseHelperService::readOnlyDatabase().'.'.CompanyRanking::TABLE.'.'.CompanyRanking::REVIEW_COUNT,
                Operator::sqlOperator($this->consumerReviewCountFirstOperator),
                $this->consumerReviewCountFirstInput
            );
        }
    }

    /**
     * @return bool
     */
    private function isNeverExceedsBudgetSet(): bool
    {
        return !is_null($this->neverExceedsBudget);
    }

    /**
     * @return bool
     */
    private function cadenceIsEnabled(): bool
    {
        return !is_null($this->cadence);
    }

    /**
     * @return void
     */
    private function filterByCampaignStatusOrServiceArea(): void
    {
        $campaignStatusIdsAreSet         = $this->areCampaignStatusIdsSet();
        $campaignServiceAreasAreSet      = $this->areCampaignServiceAreasSet();
        $campaignServiceAreaLogicalIsSet = $this->isCampaignServiceAreaLogicalSet();

        if ($campaignStatusIdsAreSet || ($campaignServiceAreasAreSet && $campaignServiceAreaLogicalIsSet)) {

            $this->query->whereExists(function (\Illuminate\Database\Query\Builder $query) use (
                $campaignStatusIdsAreSet,
                $campaignServiceAreasAreSet,
                $campaignServiceAreaLogicalIsSet,
            ) {
                $this->connectCompanyToLeadCampaign($query);

                $this->filterByCampaignStatusIds($query);

                if (($campaignServiceAreasAreSet && $campaignServiceAreaLogicalIsSet)) {
                    $this->filterByCampaignServiceAreas($query);
                }
            });
        }
    }

    /**
     * @return bool
     */
    private function areCampaignStatusIdsSet(): bool
    {
        return !empty($this->campaignStatusIds);
    }

    /**
     * @return bool
     */
    private function areCampaignServiceAreasSet(): bool
    {
        return !empty($this->campaignServiceAreas);
    }

    /**
     * @return bool
     */
    private function isCampaignServiceAreaLogicalSet(): bool
    {
        return !is_null($this->campaignServiceAreasLogical);
    }

    /**
     * @param  \Illuminate\Database\Query\Builder  $query
     *
     * @return void
     */
    private function connectCompanyToLeadCampaign(\Illuminate\Database\Query\Builder $query): void
    {
        $query->fromRaw(
            '`'.DatabaseHelperService::readOnlyDatabase()."`.`".LeadCampaign::TABLE.'`',
        );

        $query->whereColumn(
            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID,
            Company::TABLE.'.'.Company::FIELD_LEGACY_ID);

        $query->whereNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.".".LeadCampaign::DELETED_AT);
    }

    /**
     * @param  \Illuminate\Database\Query\Builder  $query
     *
     * @return void
     */
    private function filterByCampaignStatusIds(
        \Illuminate\Database\Query\Builder $query,
    ): void {
        if ($this->areCampaignStatusIdsSet()) {
            $campaignStatusColumn = DatabaseHelperService::readOnlyDatabase().".".LeadCampaign::TABLE.".".LeadCampaign::STATUS;

            $campaignReactivateDateColumn = DatabaseHelperService::readOnlyDatabase().".".LeadCampaign::TABLE.".".LeadCampaign::REACTIVATE_DATE;

            $query->where(function ($query) use ($campaignStatusColumn, $campaignReactivateDateColumn) {
                if (in_array(CampaignStatus::ACTIVE->value, $this->campaignStatusIds)) {
                    $query->orWhere($campaignStatusColumn, '=',
                        LeadCampaign::STATUS_ACTIVE);
                }

                if (in_array(CampaignStatus::PAUSED->value, $this->campaignStatusIds)) {
                    $query
                        ->orWhere(function ($query) use ($campaignStatusColumn, $campaignReactivateDateColumn) {
                            $query
                                ->where($campaignStatusColumn,
                                    LeadCampaign::STATUS_INACTIVE)
                                ->whereNotNull($campaignReactivateDateColumn);
                        });
                }

                if (in_array(CampaignStatus::OFF->value, $this->campaignStatusIds)) {
                    $query
                        ->orWhere(function ($query) use ($campaignStatusColumn, $campaignReactivateDateColumn) {
                            $query->where($campaignStatusColumn,
                                LeadCampaign::STATUS_INACTIVE)
                                ->whereNull($campaignReactivateDateColumn);
                        });
                }
            });
        }
    }

    /**
     * @param  \Illuminate\Database\Query\Builder  $query
     *
     * @return void
     */
    private function filterByCampaignServiceAreas(\Illuminate\Database\Query\Builder $query): void
    {
        if (!$this->areCampaignServiceAreasSet() || !$this->isCampaignServiceAreaLogicalSet()) {
            return;
        }

        $query->fromRaw(
            '`'.DatabaseHelperService::readOnlyDatabase()."`.`".LeadCampaign::TABLE."`,".
            '`'.DatabaseHelperService::readOnlyDatabase().'`.`'.LeadCampaignLocation::TABLE."`,".
            '`'.Location::TABLE.'`',
        );

        $query->whereColumn(
            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLocation::TABLE.'.'.LeadCampaignLocation::LEAD_CAMPAIGN_ID,
            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID,
        );

        $query->whereColumn(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLocation::TABLE.'.'.LeadCampaignLocation::LOCATION_ID,
            Location::TABLE.'.'.Location::ID);

        $query->whereIn(Location::TABLE.'.'.Location::STATE_ABBREVIATION,
            $this->campaignServiceAreas);

        if ($this->campaignServiceAreasLogical === Logical::AND) {
            $aggregation = 'COUNT(DISTINCT(`'.Location::TABLE.'`.`'.Location::STATE_ABBREVIATION.'`)) as `count`';

            $query->select(Company::TABLE.'.'.Company::FIELD_ID);

            $query->selectRaw($aggregation);

            $query->groupBy(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID);

            $query->havingRaw('`count` = ?',
                [count($this->campaignServiceAreas)]);
        }
    }

    /**
     * @return void
     */
    private function filterByCampaignBudget(): void
    {
        $noLimitSet = $this->isCampaignBudgetNoLimitSet();
        $volumeSet  = $this->isCampaignBudgetVolumeSet();
        $costSet    = $this->isCampaignBudgetCostSet();

        if ($noLimitSet || $volumeSet || $costSet) {
            $this->query->where(function (Builder|\Illuminate\Database\Query\Builder $query) use (
                $noLimitSet,
                $volumeSet,
                $costSet,
            ) {
                if ($noLimitSet) {
                    $this->filterByCampaignBudgetNoLimit($query);
                }

                if ($volumeSet) {
                    $this->filterByCampaignBudgetVolume($query);
                }

                if ($costSet) {
                    $this->filterByCampaignBudgetCost($query);
                }
            });
        }
    }

    /**
     * @return bool
     */
    private function isCampaignBudgetNoLimitSet(): bool
    {
        return !is_null($this->campaignBudgetNoLimitSelected);
    }

    /**
     * @return bool
     */
    private function isCampaignBudgetVolumeSet(): bool
    {
        return !is_null($this->campaignBudgetVolumeInput)
            && !is_null($this->campaignBudgetVolumeOperator);
    }

    /**
     * @return bool
     */
    private function isCampaignBudgetCostSet(): bool
    {
        return !is_null($this->campaignBudgetCostInput)
            && !is_null($this->campaignBudgetCostOperator);
    }

    /**
     * @param  Builder|\Illuminate\Database\Query\Builder  $query
     *
     * @return void
     */
    private function filterByCampaignBudgetNoLimit(
        Builder|\Illuminate\Database\Query\Builder $query,
    ): void {
        $query->orWhere(function ($query) {
            if ($this->campaignBudgetNoLimitSelected) {
                $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS, function (Builder $query) {
                    $query->whereHas(ProductCampaign::RELATION_BUDGETS, function (Builder $query) {
                        $query->where(ProductCampaignBudget::FIELD_VALUE_TYPE,
                            ProductCampaignBudget::VALUE_TYPE_NO_LIMIT);
                    });
                });
            }

            $query->orWhereExists(function (\Illuminate\Database\Query\Builder $query) {
                $this->connectCompanyToLeadCampaign($query);

                $query->whereNotNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID);

                $query->where(function (
                    Builder|\Illuminate\Database\Query\Builder $query,
                ) {
                    $query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                        $query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                            $query->whereNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD)
                                ->orWhere(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD,
                                    '=',
                                    0);
                        });

                        $query->where(function (Builder|\Illuminate\Database\Query\Builder $query) {
                            $query->whereNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND)->orWhere(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND,
                                '=', 0);
                        });
                    });
                });
            });
        });
    }

    /**
     * @param  Builder|\Illuminate\Database\Query\Builder  $query
     *
     * @return void
     */
    private function filterByCampaignBudgetVolume(
        Builder|\Illuminate\Database\Query\Builder $query,
    ): void {
        $query->orWhere(function ($query) {
            $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS,
                function (Builder $query) {
                    $query->whereHas(ProductCampaign::RELATION_BUDGETS,
                        function (Builder $query) {
                            $query->where(function (Builder $query) {
                                try {
                                    $sqlOperator = Operator::sqlOperator($this->campaignBudgetVolumeOperator);
                                } catch (Exception $e) {
                                    throw new Exception('Invalid volume operator'.$e->getMessage());
                                }

                                $query->orWhere(ProductCampaignBudget::FIELD_VALUE_TYPE,
                                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS)->where(ProductCampaignBudget::FIELD_VALUE,
                                    $sqlOperator, $this->campaignBudgetVolumeInput);
                            });
                        });
                });

            $query->orWhereExists(function (\Illuminate\Database\Query\Builder $query) {
                $this->connectCompanyToLeadCampaign($query);

                $query->whereNotNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID);

                $query->where(function (
                    Builder|\Illuminate\Database\Query\Builder $query,
                ) {
                    try {
                        $sqlOperator = Operator::sqlOperator($this->campaignBudgetVolumeOperator);
                    } catch (Exception $e) {
                        throw new Exception('Invalid volume operator'.$e->getMessage());
                    }

                    $query->orWhere(function (
                        Builder|\Illuminate\Database\Query\Builder $query,
                    ) use (
                        $sqlOperator,
                    ) {
                        $query->where(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD,
                            $sqlOperator,
                            $this->campaignBudgetVolumeInput);
                    });
                });
            });
        });
    }

    /**
     * @param  Builder|\Illuminate\Database\Query\Builder  $query
     *
     * @return void
     */
    private function filterByCampaignBudgetCost(
        Builder|\Illuminate\Database\Query\Builder $query,
    ): void {
        $query->orWhere(function ($query) {
            $query->whereHas(Company::RELATION_PRODUCT_CAMPAIGNS,
                function (Builder $query) {
                    $query->whereHas(ProductCampaign::RELATION_BUDGETS,
                        function (Builder $query) {
                            $query->where(function (Builder $query) {
                                try {
                                    $sqlOperator = Operator::sqlOperator($this->campaignBudgetCostOperator);
                                } catch (Exception $e) {
                                    throw new Exception('Invalid volume operator'.$e->getMessage());
                                }

                                $query->orWhere(ProductCampaignBudget::FIELD_VALUE_TYPE,
                                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND)->where(ProductCampaignBudget::FIELD_VALUE,
                                    $sqlOperator, $this->campaignBudgetCostInput);
                            });
                        });
                });

            $query->orWhereExists(function (\Illuminate\Database\Query\Builder $query) {
                $this->connectCompanyToLeadCampaign($query);

                $query->whereNotNull(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID);

                $query->where(function (
                    Builder|\Illuminate\Database\Query\Builder $query,
                ) {
                    try {
                        $sqlOperator = Operator::sqlOperator($this->campaignBudgetCostOperator);
                    } catch (Exception $e) {
                        throw new Exception('Invalid volume operator'.$e->getMessage());
                    }

                    $query->orWhere(function (
                        Builder|\Illuminate\Database\Query\Builder $query,
                    ) use (
                        $sqlOperator,
                    ) {
                        $query->where(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND,
                            $sqlOperator,
                            $this->campaignBudgetCostInput);
                    });
                });
            });
        });
    }

    /**
     * @return bool
     */
    private function isLeadRejectionSet(): bool
    {
        return $this->getOperatorInputLogicalPairIsSet(
            $this->leadRejectionFirstInput,
            $this->leadRejectionFirstOperator,
            $this->leadRejectionLogical,
            $this->leadRejectionSecondInput,
            $this->leadRejectionSecondOperator,
        );
    }

    /**
     * @param  int|null  $firstInput
     * @param  Operator|null  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return bool
     */
    private function getOperatorInputLogicalPairIsSet(
        ?int $firstInput,
        ?Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator,
    ): bool {
        $firstInputSet = !is_null($firstInput);
        $firstOperatorSet = !is_null($firstOperator);

        $logicalIsSet = !is_null($logical);

        $result = true;

        if ($logicalIsSet) {
            $secondInputSet = !is_null($secondInput);
            $secondOperatorSet = !is_null($secondOperator);

            $result &= $secondInputSet && $secondOperatorSet;
        }

        return $firstInputSet && $firstOperatorSet && $result;
    }

    /**
     * @return bool
     */
    private function isAppointmentRejectionSet(): bool
    {
        return $this->getOperatorInputLogicalPairIsSet(
            $this->appointmentRejectionFirstInput,
            $this->appointmentRejectionFirstOperator,
            $this->appointmentRejectionLogical,
            $this->appointmentRejectionSecondInput,
            $this->appointmentRejectionSecondOperator,
        );
    }

    /**
     * @param  int  $productId
     * @param  int  $firstInput
     * @param  Operator  $firstOperator
     * @param  Logical|null  $logical
     * @param  int|null  $secondInput
     * @param  Operator|null  $secondOperator
     * @return void
     */
    private function filterByProductRejectionPercentage(
        int $productId,
        int $firstInput,
        Operator $firstOperator,
        ?Logical $logical,
        ?int $secondInput,
        ?Operator $secondOperator
    ): void {
        $this->query->whereExists(function (\Illuminate\Database\Query\Builder $query) use (
            $productId,
            $firstInput,
            $firstOperator,
            $logical,
            $secondInput,
            $secondOperator
        ) {
            $query->from(ComputedRejectionStatistic::TABLE);

            $query->whereColumn(Company::TABLE.'.'.Company::FIELD_ID,
                ComputedRejectionStatistic::TABLE.'.'.ComputedRejectionStatistic::FIELD_COMPANY_ID);

            $query->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, '=',
                $productId);

            $query->groupBy(Company::TABLE.'.'.Company::FIELD_ID);

            $query->select(DB::raw('avg(`'.ComputedRejectionStatistic::TABLE.'`.`'.ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE.'`) as avg_rejection_percentage'));

            $filter = function (\Illuminate\Database\Query\Builder $query) use (
                $firstInput,
                $firstOperator,
                $logical,
                $secondInput,
                $secondOperator
            ) {
                $firstOperatorSql = Operator::sqlOperator($firstOperator);

                $query->having('avg_rejection_percentage', $firstOperatorSql,
                    $firstInput);

                if ($logical instanceof Logical && $secondOperator instanceof Operator && gettype($secondInput) == 'integer') {

                    $secondOperatorSql = Operator::sqlOperator($secondOperator);

                    if ($logical === Logical::AND) {
                        $query->having('avg_rejection_percentage', $secondOperatorSql,
                            $secondInput);
                    } else {
                        $query->orHaving('avg_rejection_percentage', $secondOperatorSql,
                            $secondInput);
                    }
                }
            };

            $filter($query);
        });
    }

    private function employeeCountIsEnabled(): bool
    {
        return !is_null($this->employeeCountFirstInput) && !is_null($this->employeeCountFirstOperator);
    }

    private function estimatedRevenueIsEnabled(): bool
    {
        return !is_null($this->estimatedRevenueFirstInput) && !is_null($this->estimatedRevenueFirstOperator);
    }

    private function googleRatingIsEnabled(): bool
    {
        return !is_null($this->googleRatingFirstInput) && !is_null($this->googleRatingFirstOperator);
    }

    private function googleReviewCountIsEnabled(): bool
    {
        return !is_null($this->googleReviewCountFirstInput) && !is_null($this->googleReviewCountFirstOperator);
    }

    private function consumerRatingIsEnabled(): bool
    {
        return !is_null($this->consumerRatingFirstInput) && !is_null($this->consumerRatingFirstOperator);
    }

    private function consumerReviewCountIsEnabled(): bool
    {
        return !is_null($this->consumerReviewCountFirstInput) && !is_null($this->consumerReviewCountFirstOperator);
    }

    /**
     * @return void
     */
    private function orderCompanies(): void
    {
        foreach ($this->orderBy as $item) {
            $item = explode(':', $item);

            if ($item[0] == self::SORTABLE_STATUS) {
                $this->query->orderBy($item[0], $item[1]);
            } elseif ($item[0] == self::SORTABLE_SALES_STATUS) {
                $this->query->orderBy($item[0], $item[1]);
            } elseif ($item[0] == self::SORTABLE_COMPANY_NAME) {
                $this->query->orderBy(Company::TABLE.'.'.Company::FIELD_NAME, $item[1]);
            } elseif ($item[0] == self::SORTABLE_LEAD_REJECTION_PERCENTAGE) {
                $this->query->orderBy($item[0], $item[1]);
            } elseif ($item[0] == self::SORTABLE_APPOINTMENT_REJECTION_PERCENTAGE) {
                $this->query->orderBy($item[0], $item[1]);
            } elseif ($item[0] == self::SORTABLE_LEADS_PURCHASED_LAST_30_DAYS) {
                $this->joinEloquentQuoteCompany();

                $this->query->where(function (Builder $query) use ($item) {
                    $query->selectRaw(implode(',', [
                        Company::TABLE.'.*',
                        "SUM(`lqc`.`cost`) as `total_cost_spent_number`",
                    ]));

                    $query->orderBy('total_cost_spent_number', $item[1]);
                });
            }
        }
    }

    /**
     * @return void
     */
    private function joinEloquentQuoteCompany(): void
    {
        $this->query
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE." as lqc",
                function ($join) {
                    /**
                     * @var JoinClause $join
                     */
                    $join
                        ->on(Company::FIELD_LEGACY_ID, '=', "lqc.".EloquentQuoteCompany::COMPANY_ID);

                    $join->where('lqc.'.EloquentQuoteCompany::CHARGEABLE, 1);
                    $join->where('lqc.'.EloquentQuoteCompany::CHARGEABLE, 1);
                    $thirtyDaysAgo = Carbon::now()->subDays(30)->getTimestamp();
                    $join->where('lqc.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=',
                        $thirtyDaysAgo);
                });
    }

    /**
     * @return void
     */
    private function joinLegacyCompaniesOnCompanies(): void
    {
        if(!$this->joinedLegacyCompanies) {
            $this->query->join(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
                '=',
                Company::TABLE.'.'.Company::FIELD_LEGACY_ID
            );

            $this->joinedLegacyCompanies = true;
        }
    }
}
