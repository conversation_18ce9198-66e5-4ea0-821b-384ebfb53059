<?php

namespace App\Builders\Odin;

use App\Http\Controllers\API\CompanySearchController;
use App\Models\AccountManagerClient;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyService;
use App\Models\Odin\IndustryService;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use App\Services\DatabaseHelperService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CompanyBuilder
{

    protected LegacyCompanyRepository $legacyCompanyRepository;

    /**
     * @param mixed|null $campaignStatus
     * @param mixed|null $companyName
     * @param String|null $entityName
     * @param String|null $state
     * @param array|null $adminStatus
     * @param Int|null $accountManager
     * @param String|null $paymentMethod
     * @param mixed|null $superPremiumOptIn
     * @param Int|null $status
     * @param array|null $serviceType
     * @param String|null $industryId
     * @param String|null $type
     * @param mixed|null $sortColumn
     * @param mixed $sortDirection
     * @throws BindingResolutionException
     */
    public function __construct(
        protected mixed $campaignStatus = null,
        protected mixed $companyName = null,
        protected String|null $entityName = null,
        protected String|null $state = null,
        protected Array|null $adminStatus = null,
        protected Int|null $accountManager = null,
        protected String|null $paymentMethod = null,
        protected mixed $superPremiumOptIn = null,
        protected Int|null $status = null,
        protected Array|null $serviceType = null,
        protected String|null $industryId = null,
        protected String|null $type = null,
        protected mixed $sortColumn = null,
        protected mixed $sortDirection = 'asc',
        protected String|null $website = null,
    ){
        $this->legacyCompanyRepository = app()->make(LegacyCompanyRepository::class);
    }

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    public function forCampaignStatus(mixed $campaignStatus): self
    {
        $this->campaignStatus = $campaignStatus;

        return $this;
    }

    /**
     * Allows searching via company Name OR company ID - both legacy_ID and Odin_ID
     *
     * @param mixed $companyName
     * @return $this
     */
    public function forCompanyName(mixed $companyName): self
    {
        $this->companyName = $companyName;

        return $this;
    }

    /**
     * @param String|null $entityName
     * @return $this
     */
    public function forEntityName(?String $entityName = null): self
    {
        $this->entityName = $entityName;

        return $this;
    }

    public function orForWebsite(?String $website = null): self
    {
        $this->website = $website;

        return $this;
    }

    /**
     * @param String|null $state
     * @return $this
     */
    public function forState(?String $state): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @param array|null $adminStatus
     * @return $this
     */
    public function forAdminStatus(?Array $adminStatus): self
    {
        $this->adminStatus = $adminStatus;

        return $this;
    }

    /**
     * @param Int|null $accountManager
     * @return $this
     */
    public function forAccountManager(?Int $accountManager): self
    {
        $this->accountManager = $accountManager;

        return $this;
    }

    /**
     * @param String|null $paymentMethod
     * @return $this
     */
    public function forPaymentMethod(?String $paymentMethod): self
    {
        $this->paymentMethod = $paymentMethod;

        return $this;
    }

    /**
     * @param mixed $superPremiumOptIn
     * @return $this
     */
    public function forSuperPremiumOptIn(mixed $superPremiumOptIn): self
    {
        $this->superPremiumOptIn = $superPremiumOptIn;

        return $this;
    }

    /**
     * @param Int|null $status
     * @return $this
     */
    public function forStatus(?Int $status): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @param array|null $serviceType
     * @return $this
     */
    public function forServiceType(?Array $serviceType): self
    {
        $this->serviceType = $serviceType;

        return $this;
    }

    /**
     * @param String|null $industryId
     * @return $this
     */
    public function forIndustryId(?String $industryId): self
    {
        $this->industryId = $industryId;

        return $this;
    }

    /**
     * @param String|null $type
     * @return $this
     */
    public function forType(?String $type): self
    {
        $this->type = $type;

        return $this;
    }

    /**
     * @param mixed $sortColumn
     * @return $this
     */
    public function sortColumn(mixed $sortColumn): self
    {
        $this->sortColumn = $sortColumn;

        return $this;
    }

    public function sortDirection(mixed $sortDirection): self
    {
        $this->sortDirection = $sortDirection;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = Company::query();

        $query = $query->select(Company::TABLE.'.*');

        if($this->superPremiumOptIn !== null || $this->campaignStatus !== null || $this->superPremiumOptIn !== null)
        {
            $query = $this->queryLegacyModels($query);
        }

        if($this->companyName !== null) {
            $companyName = $this->companyName;
            //Allow search for companyName, Odin ID
            $query = $query->where(function ($query) use ($companyName) {
                $query->where(Company::TABLE.'.'.Company::FIELD_ID, intval($companyName))
                    ->orWhere(Company::TABLE.'.'.Company::FIELD_NAME, 'LIKE', '%'.$companyName.'%');
            });
        }

        if($this->entityName !== null && strlen(trim($this->entityName)) > 0) {
            $query = $query->where(function (Builder $query) {
                $query
                    ->where(Company::TABLE.'.'.Company::FIELD_ID, intval($this->entityName))
                    ->orWhere(Company::TABLE.'.'.Company::FIELD_ENTITY_NAME, 'LIKE', "%{$this->entityName}%");
            });
        }

        if($this->website !== null) {
            $domain = ltrim($this->website,"https://");
            $domain = ltrim($domain,"www.");
            $query = $query->orWhere(Company::FIELD_WEBSITE, 'LIKE', "%$domain%");
        }

        if($this->state !== null){
            $query = $query->join(CompanyLocation::TABLE, CompanyLocation::TABLE.'.'.CompanyLocation::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID)
                ->join(Address::TABLE, Address::TABLE.'.'.Address::FIELD_ID, '=', CompanyLocation::TABLE.'.'.CompanyLocation::FIELD_ADDRESS_ID)
                ->where(Address::TABLE.'.'.Address::FIELD_STATE, $this->state);
        }
        if($this->adminStatus !== null) {
            $query = $query->whereIn(Company::TABLE.'.'.Company::FIELD_ADMIN_STATUS, $this->adminStatus);
        }
        if($this->status !== null) {
            $query = $query->where(Company::TABLE.'.'.Company::FIELD_STATUS,$this->status);
        }
        if($this->serviceType !== null) {
            $query = $query->join(CompanyService::TABLE, CompanyService::TABLE.'.'.CompanyService::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID)
                ->join(IndustryService::TABLE, IndustryService::TABLE.'.'.IndustryService::FIELD_ID, '=', CompanyService::TABLE.'.'.CompanyService::FIELD_INDUSTRY_SERVICE_ID)
                ->whereIn(IndustryService::TABLE.'.'.IndustryService::FIELD_SLUG, $this->serviceType);
        }
        if($this->industryId !== null) {
            $query = $query->join(CompanyIndustry::TABLE, CompanyIndustry::TABLE.'.'.CompanyIndustry::FIELD_COMPANY_ID, '=', Company::TABLE.'.'.Company::FIELD_ID)
                ->where(CompanyIndustry::TABLE.'.'.CompanyIndustry::FIELD_INDUSTRY_ID, $this->industryId);
        }
        if($this->accountManager !== null && $this->accountManager > 0)
        {
            $query = $query->join(AccountManagerClient::TABLE, AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_COMPANY_REFERENCE, '=', Company::TABLE.'.'.Company::FIELD_REFERENCE);
            if($this->accountManager === -1) $query = $query->whereNull(AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID);
            else $query = $query
                ->where(AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_ACCOUNT_MANAGER_ID, $this->accountManager)
                ->where(AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE);
        }

        if($this->sortColumn && $this->sortDirection !== null) {
            $query = $query->orderBy($this->sortColumn, $this->sortDirection);
        }

        Return $query;
    }


    /**
     * @param $query
     * @return Builder
     */
    public function queryLegacyModels($query): Builder
    {
        $legacyCompanyTable = DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE;
        $legacyLeadCampaignTable = DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE;

        $searchQuery = $query->join($legacyCompanyTable, $legacyCompanyTable.'.'.EloquentCompany::ID, '=', Company::TABLE.'.'.Company::FIELD_LEGACY_ID);

        if($this->paymentMethod !== null) {
            $searchQuery = $searchQuery->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::PAYMENT_SOURCE, $this->paymentMethod);
        }

        if($this->superPremiumOptIn !== null)
        {
            $superPremiumOptIn = $this->superPremiumOptIn;
            $searchQuery = $searchQuery->whereExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable, $superPremiumOptIn) {
                $query->select('*')
                    ->from($legacyLeadCampaignTable)
                    ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                    ->where($legacyLeadCampaignTable.'.'.LeadCampaign::ALLOW_NON_BUDGET_PREMIUM_LEADS, $superPremiumOptIn);
            });
        }

        if($this->campaignStatus !== null)
        {
            $overBudgetSubTable = 'over_budget_sub_table';

            switch ($this->campaignStatus) {
                case CompanySearchController::CAMPAIGN_STATUS_ACTIVE:
                    $searchQuery = $searchQuery->whereExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable) {
                        $query->select('*')
                            ->from($legacyLeadCampaignTable)
                            ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                            ->where($legacyLeadCampaignTable.'.'.LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE);
                    })
                        ->whereNotExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable) {
                            $query->select('*')
                                ->from($legacyLeadCampaignTable)
                                ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                                ->where($legacyLeadCampaignTable.'.'.LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE);
                        });
                    break;
                case CompanySearchController::CAMPAIGN_STATUS_ONE_PAUSED:
                    $searchQuery = $searchQuery->whereExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable) {
                        $query->select('*')
                            ->from($legacyLeadCampaignTable)
                            ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                            ->where($legacyLeadCampaignTable.'.'.LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE);
                    });
                    break;
                case CompanySearchController::CAMPAIGN_STATUS_PAUSED:
                    $searchQuery = $searchQuery->whereExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable) {
                        $query->select('*')
                            ->from($legacyLeadCampaignTable)
                            ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                            ->where($legacyLeadCampaignTable.'.'.LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE);
                    })
                        ->whereNotExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable) {
                            $query->select('*')
                                ->from($legacyLeadCampaignTable)
                                ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                                ->where($legacyLeadCampaignTable.'.'.LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE);
                        });
                    break;
                case CompanySearchController::CAMPAIGN_STATUS_ONE_OVER_BUDGET:
                    $searchQuery = $searchQuery->whereExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable, $overBudgetSubTable) {
                        $query->select('*')
                            ->from($legacyLeadCampaignTable)
                            ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                            ->joinSub($this->getOverBudgetSubQuery(), $overBudgetSubTable, function ($query) use ($overBudgetSubTable, $legacyLeadCampaignTable) {
                                $query->on($overBudgetSubTable.'.'.LeadCampaign::ID, '=', $legacyLeadCampaignTable.'.'.LeadCampaign::ID);
                            })
                            ->where($legacyLeadCampaignTable.'.'.LeadCampaign::STATUS, true)
                            ->whereNull($legacyLeadCampaignTable.'.'.LeadCampaign::DELETED_AT)
                            ->where("{$overBudgetSubTable}.over_budget", '>', 0);
                    });
                    break;
                case CompanySearchController::CAMPAIGN_STATUS_OVER_BUDGET:
                    $leadCampaignIdField = $legacyLeadCampaignTable.'.'.LeadCampaign::ID;

                    $searchQuery = $searchQuery->whereExists(function ($query) use ($legacyLeadCampaignTable, $legacyCompanyTable, $overBudgetSubTable, $leadCampaignIdField) {
                        $query->select('*')
                            ->from($legacyLeadCampaignTable)
                            ->whereColumn($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID, $legacyCompanyTable.'.'.EloquentCompany::ID)
                            ->joinSub($this->getOverBudgetSubQuery(), $overBudgetSubTable, function ($query) use ($overBudgetSubTable, $legacyLeadCampaignTable) {
                                $query->on($overBudgetSubTable.'.'.LeadCampaign::ID, '=', $legacyLeadCampaignTable.'.'.LeadCampaign::ID);
                            })
                            ->where($legacyLeadCampaignTable.'.'.LeadCampaign::STATUS, true)
                            ->whereNull($legacyLeadCampaignTable.'.'.LeadCampaign::DELETED_AT)
                            ->groupBy($legacyLeadCampaignTable.'.'.LeadCampaign::COMPANY_ID)
                            ->havingRaw(implode(' ', [
                                "COUNT(DISTINCT $leadCampaignIdField)",
                                '=',
                                "SUM(IF({$overBudgetSubTable}.over_budget > 0, 1, 0))",
                            ]));
                    });
                    break;
            }
        }

        return $searchQuery;
    }

    /**
     * @return \Illuminate\Database\Query\Builder
     */
    public function getOverBudgetSubQuery(): \Illuminate\Database\Query\Builder
    {
        $lastModifiedLeadLimitField = DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::LAST_MODIFIED_LEAD_LIMIT;
        $maxDailySpendField = DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_SPEND;
        $maxDailyLeadField = DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAX_DAILY_LEAD;
        $maxBudgetUsageField = DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::MAXIMUM_BUDGET_USAGE;
        $leadCampaignTable = DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE;

        return DB::table($leadCampaignTable)
            ->select([
                $leadCampaignTable.'.'.LeadCampaign::ID,
                $leadCampaignTable.'.'.LeadCampaign::COMPANY_ID,
                $leadCampaignTable.'.'.LeadCampaign::STATUS,
                DB::raw(
                    implode(
                        ' ',
                        [
                            "IF($maxDailySpendField > 0, SUM(cost), COUNT(cost))", // Budget spent
                            "/ (FLOOR((UNIX_TIMESTAMP() - IF($lastModifiedLeadLimitField > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP($lastModifiedLeadLimitField), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))) / 86400) + 1)", // Budget timeframe
                            '* 100',
                            "/ IF($maxDailySpendField > 0, $maxDailySpendField, $maxDailyLeadField)", // Daily budget
                            "> $maxBudgetUsageField", // Check if budget usage is over max budget usage
                            'AS `over_budget`',
                        ]
                    )
                )
            ])
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignSalesTypeConfiguration::TABLE, function ($join) use ($leadCampaignTable) {
                $join->on(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, '=', $leadCampaignTable.'.'.LeadCampaign::ID);
            })
            ->leftJoin(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE, function ($join) use ($lastModifiedLeadLimitField) {
                $join->on(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::ID, '=', DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID)
                    ->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGEABLE, true)
                    ->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::DELIVERED, true)
                    ->where(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::INCLUDE_IN_BUDGET, true)
                    ->whereRaw(DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY." >= IF($lastModifiedLeadLimitField > DATE_SUB(NOW(), INTERVAL 1 MONTH), UNIX_TIMESTAMP($lastModifiedLeadLimitField), UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 MONTH)))");
            })
            ->groupBy($leadCampaignTable.'.'.LeadCampaign::ID);
    }

}
