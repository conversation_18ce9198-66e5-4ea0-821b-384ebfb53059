<?php

namespace App\Builders\Odin;

use App\Models\Odin\Industry;
use App\Models\Legacy\Location;
use App\Models\MissedProducts\MissedProduct;
use App\Models\Odin\Address;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class MissedProductBuilder
{
    const int DEFAULT_DAYS_TO_QUERY = 7;

    const string ALIAS_INDUSTRY_SLUG         = 'industry_slug';
    const string ALIAS_INDUSTRY_NAME         = 'industry_name';
    const string ALIAS_INDUSTRY_SERVICE_SLUG = 'industry_service_slug';
    const string ALIAS_INDUSTRY_SERVICE_NAME = 'industry_service_name';

    private Builder $query;

    public function __construct(
        protected ?int $forCompanyId = null,
        protected ?array $forIndustryIds = null,
        protected ?array $forProductIds = null,
        protected ?array $forIndustryServiceIds = null,
        protected ?array $forCountyLocationIds = null,
        protected ?array $forCityKeys = null,
        protected ?array $forZipCodeLocationIds = null,
        protected ?Carbon $forFromDate = null,
        protected ?Carbon $forToDate = null,
        protected ?array $excludeDateRange = null,
        protected ?array $addSelectColumns = null,
    )
    {
        $this->query = MissedProduct::query();
    }

    /**
     * @return self
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param int $companyId
     * @return self
     */
    public function forCompany(int $companyId): self
    {
        $this->forCompanyId = $companyId;

        return $this;
    }

    /**
     * @param int|int[] $industryIds
     * @return $this
     */
    public function forIndustries(int|array $industryIds): self
    {
        $this->forIndustryIds = $this->asArray($industryIds);

        return $this;
    }

    /**
     * @param int|int[] $productIds
     * @return $this
     */
    public function forProducts(int|array $productIds): self
    {
        $this->forProductIds = $this->asArray($productIds);

        return $this;
    }

    /**
     * @param int|int[] $serviceIds
     * @return $this
     */
    public function forIndustryServices(int|array $serviceIds): self
    {
        $this->forIndustryServiceIds = $this->asArray($serviceIds);

        return $this;
    }

    /**
     * @param int|int[] $locationIds
     * @return $this
     */
    public function forCountyLocations(int|array $locationIds): self
    {
        $this->forCountyLocationIds = $this->asArray($locationIds);

        return $this;
    }

    /**
     * @param string|string[] $cityKeys
     * @return $this
     */
    public function forCityKeys(string|array $cityKeys): self
    {
        $this->forCityKeys = $this->asArray($cityKeys);

        return $this;
    }

    /**
     * @param int|int[] $zipCodeLocationIds
     * @return $this
     */
    public function forZipCodeLocations(int|array $zipCodeLocationIds): self
    {
        $this->forZipCodeLocationIds = $this->asArray($zipCodeLocationIds);

        return $this;
    }

    /**
     * @param Carbon $date
     * @return $this
     */
    public function fromDate(Carbon $date): self
    {
        $this->forFromDate = $date;

        return $this;
    }

    /**
     * @param Carbon $date
     * @return $this
     */
    public function toDate(Carbon $date): self
    {
        $this->forToDate = $date;

        return $this;
    }

    /**
     * @param array|null $excludeDateRange
     * @return $this
     */
    public function excludeDateRange(?array $excludeDateRange): self
    {
        $this->excludeDateRange = $excludeDateRange;

        return $this;
    }

    /**
     * @param string|string[] $columns
     * @return $this
     */
    public function addSelectColumns(string|array $columns): self
    {
        $this->addSelectColumns = $this->asArray($columns);

        return $this;
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $this->addSelects();
        $this->addJoins();
        $this->addConstraints();

        return $this->query;
    }

    /**
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()
            ->get();
    }

    /**
     * @param int|null $page
     * @param int|null $perPage
     * @return LengthAwarePaginator
     */
    public function paginate(?int $page = 1, ?int $perPage = 25): LengthAwarePaginator
    {
        return $this->getQuery()
            ->paginate(
                perPage: $perPage,
                page: $page,
            );
    }

    /**
     * @return void
     */
    protected function addConstraints(): void
    {
        if ($this->forIndustryIds)
            $this->query->whereIn(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $this->forIndustryIds);
        if ($this->forIndustryServiceIds)
            $this->query->whereIn(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $this->forIndustryServiceIds);
        if ($this->forProductIds)
            $this->query->whereIn(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, $this->forProductIds);
        if ($this->forCityKeys !== null)
            $this->query->whereIn(Location::TABLE .'.'. Location::CITY_KEY, $this->forCityKeys);
        if ($this->forCountyLocationIds !== null)
            $this->query->whereIn(Address::TABLE . '.' . Address::FIELD_COUNTY_LOCATION_ID, $this->forCountyLocationIds);
        if ($this->forZipCodeLocationIds !== null)
            $this->query->whereIn(Address::TABLE .'.'. Address::FIELD_ZIP_CODE_LOCATION_ID, $this->forZipCodeLocationIds);
        if ($this->forCompanyId)
            $this->query->whereNull(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID);
        $this->handleDateConstraints();
    }

    /**
     * @return void
     */
    protected function addJoins(): void
    {
        $this->query->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, '=', MissedProduct::TABLE . '.' . MissedProduct::FIELD_CONSUMER_PRODUCT_ID)
            ->join(Address::TABLE, Address::TABLE . '.' . Address::FIELD_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ADDRESS_ID)
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE . '.' . ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
            ->join(Industry::TABLE, Industry::TABLE .'.'. Industry::FIELD_ID, '=', IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID);

        if ($this->forCompanyId)
            $this->query->leftJoin(ProductAssignment::TABLE, fn(JoinClause $join) =>
                $join->on(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID)
                    ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COMPANY_ID, '=', $this->forCompanyId)
            );

        if ($this->forCityKeys)
            $this->query->join(Location::TABLE, Location::TABLE . Location::ID, '=', Address::TABLE .'.'. Address::FIELD_ZIP_CODE_LOCATION_ID);
    }

    /**
     * @return void
     */
    protected function addSelects(): void
    {
        $defaultSelect = [
            ConsumerProduct::TABLE . '.*',
            MissedProduct::TABLE . '.*',
            ...$this->getSelectStrings(Address::TABLE, [Address::FIELD_ZIP_CODE_LOCATION_ID, Address::FIELD_COUNTY_LOCATION_ID]),
            ...$this->getSelectStrings(ServiceProduct::TABLE, [ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, ServiceProduct::FIELD_PRODUCT_ID]),
            ...$this->getSelectStrings(IndustryService::TABLE, [IndustryService::FIELD_INDUSTRY_ID, self::ALIAS_INDUSTRY_SERVICE_NAME => IndustryService::FIELD_NAME, self::ALIAS_INDUSTRY_SERVICE_SLUG => IndustryService::FIELD_SLUG]),
            ...$this->getSelectStrings(Industry::TABLE, [self::ALIAS_INDUSTRY_NAME => Industry::FIELD_NAME, self::ALIAS_INDUSTRY_SLUG => Industry::FIELD_SLUG]),
        ];

        $this->query->select([
            ...$defaultSelect,
            ...($this->addSelectColumns ?? []),
        ]);
    }

    /**
     * @return void
     */
    protected function handleDateConstraints(): void
    {
        $fromDate = $this->forFromDate ?? $this->getDefaultFromDate();
        $toDate = $this->forToDate ?? now();

        if ($this->excludeDateRange !== null) {
            //This is primarily for handling chunks of time when the company was in a non-purchasing status
            $excludeFrom = $this->excludeDateRange[0] <= $this->forFromDate
                ? $fromDate
                : $this->excludeDateRange;
            $excludeTo = $this->excludeDateRange[1] && $this->excludeDateRange[1] < $toDate
                ? $this->excludeDateRange[1]
                : $toDate;

            if ($excludeFrom === $fromDate) {
                $this->query->whereBetween(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, [$excludeTo, $toDate]);
            }
            else {
                $this->query->where(fn(Builder $query) =>
                    $query->whereBetween(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, [$fromDate, $excludeFrom])
                        ->orWhereBetween(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, [$excludeTo, $toDate])
                );
            }
        }
        else {
            $this->query->whereBetween(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, [$fromDate, $toDate]);
        }
    }

    /**
     * @return Carbon
     */
    protected function getDefaultFromDate(): Carbon
    {
        return now()->subDays(self::DEFAULT_DAYS_TO_QUERY);
    }

    /**
     * @param string $table
     * @param string[] $columns
     * @return string[]
     */
    protected function getSelectStrings(string $table, array $columns): array
    {
        $selectColumns = [];
        foreach($columns as $alias => $column) {
            if (gettype($alias) === 'string')
                $selectColumns[] = "$table.$column as $alias";
            else
                $selectColumns[] = "$table.$column";
        }

        return $selectColumns;
    }

    /**
     * @param int|string|array $value
     * @return int[]|string[]
     */
    protected function asArray(int|string|array $value): array
    {
        return (is_array($value))
            ? $value
            : [$value];
    }
}