<?php

namespace App\Builders\Odin;

use App\Models\CompanyContract;
use App\Models\Contract;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class CompanyContractBuilder
{
    /**
     * @param int|null $companyId
     * @param string|null $contractName
     * @param string|null $contractType
     */
    public function __construct(
        protected ?int $companyId = null,
        protected ?string $contractName = null,
        protected ?string $contractType = null,
    ){}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     *
     * @param int|null $id
     * @return $this
     */
    public function forCompanyId(?int $id = null): self
    {
        $this->companyId = $id;

        return $this;
    }

    /**
     *
     * @param string|null $contractName
     * @return $this
     */
    public function forContractName(?string $contractName = null): self
    {
        $this->contractName = $contractName;

        return $this;
    }

    /**
     *
     * @param string|null $contractType
     * @return $this
     */
    public function forContractType(?string $contractType = null): self
    {
        $this->contractType = $contractType;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = CompanyContract::query();

        //eager load relationships
        $query = $query->with([
            CompanyContract::RELATION_COMPANY_USER,
            CompanyContract::RELATION_CONTRACT,
            CompanyContract::RELATION_CONTRACT.'.'.Contract::RELATION_CONTRACT_KEY,
            CompanyContract::RELATION_CONTRACT.'.'.Contract::RELATION_WEBSITE
        ]);

        if($this->companyId !== null) {
            $query = $query->where(CompanyContract::TABLE.'.'.CompanyContract::FIELD_COMPANY_ID, $this->companyId);
        }

        if($this->contractName !== null) {
            $query->whereHas('contractModel.contractKey', function ($query) {
                $query->where('name', 'like', '%' . $this->contractName . '%');
            });
        }

        if ($this->contractType !== null) {
            $query->where(CompanyContract::FIELD_CONTRACT_TYPE, 'like', '%'.$this->contractType.'%');
        }

        $query->orderBy(CompanyContract::FIELD_CREATED_AT, 'DESC');

        return $query;
    }

}
