<?php

namespace App\Builders\Odin;

use App\DataModels\CompanyRevenueDataModel;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CompanyRevenueBuilder
{
    const GROUP_DAILY   = "daily";
    const GROUP_MONTHLY = "monthly";
    const GROUP_YEARLY  = "yearly";

    const PERIOD_ALL_TIME = "all-time";

    public function __construct(
        protected ?int $companyId = null,
        protected string $groupBy = self::GROUP_MONTHLY,
        protected string $period = self::PERIOD_ALL_TIME,
        protected int $duration = 1,
    ) {}

    /**
     * Returns a new instance of this builder.
     *
     * @return CompanyRevenueBuilder
     */
    public static function query(): CompanyRevenueBuilder
    {
        return new CompanyRevenueBuilder();
    }

    /**
     * Sets the company this builder should operate on.
     *
     * @param int $companyId
     * @return $this
     */
    public function forCompany(int $companyId): self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * Sets the grouping this builder should group by
     *
     * @param string $groupBy
     * @return $this
     */
    public function groupBy(string $groupBy = self::GROUP_MONTHLY): self
    {
        $this->groupBy = $groupBy;

        return $this;
    }

    /**
     * Sets the period type this builder should operate on.
     *
     * @param string $period
     * @return $this
     */
    public function setPeriod(string $period = self::PERIOD_ALL_TIME): self
    {
        $this->period = $period;

        return $this;
    }

    /**
     * Sets the duration this builder should operate on.
     *
     * @param int $duration
     * @return $this
     */
    public function setPeriodDuration(int $duration = 1): self
    {
        $this->duration = $duration;

        return $this;
    }

    /**
     * Runs the builder.
     *
     * @return Collection<CompanyRevenueDataModel>
     */
    public function get(): Collection
    {
        $query = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $this->companyId)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->whereNotNull(ProductAssignment::FIELD_DELIVERED_AT)
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '<>', 0)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->orderBy(ProductAssignment::FIELD_DELIVERED_AT, 'asc');

        return $this->applyGroupingAndSelects($this->applyDuration($query))
            ->get()
            ->map(fn($item) => new CompanyRevenueDataModel(
                $this->companyId,
                $item->period,
                $item->revenue
            ));
    }

    /**
     * Applies the grouping to the query.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function applyGroupingAndSelects(Builder $query): Builder
    {
        $query = match ($this->groupBy) {
            self::GROUP_DAILY => $query->select([DB::raw("sum(cost) as revenue"), DB::raw('DATE_FORMAT(delivered_at, "%Y-%m-%d") as period')]),
            self::GROUP_MONTHLY => $query->select([DB::raw("sum(cost) as revenue"), DB::raw('DATE_FORMAT(delivered_at, "%M, %Y") as period')]),
            self::GROUP_YEARLY => $query->select([DB::raw("sum(cost) as revenue"), DB::raw('DATE_FORMAT(delivered_at, "%Y") as period')]),
            default => $query
        };

        return $query->groupBy("period");
    }

    /**
     * Applies the duration to the query.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function applyDuration(Builder $query): Builder
    {
        return match ($this->period) {
            self::GROUP_DAILY => $query->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', Carbon::now()->startOfDay()->subDays($this->duration)),
            self::GROUP_MONTHLY => $query->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', Carbon::now()->startOfMonth()->subMonths($this->duration)),
            self::GROUP_YEARLY => $query->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', Carbon::now()->startOfYear()->subYears($this->duration)),
            default => $query
        };
    }
}
