<?php

namespace App\Builders\Odin;

use App\Models\Call;
use App\Models\Email;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Text;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CompanyCommunicationsBuilder
{
    protected ?Company $company = null;

    protected ?User $user = null;

    protected string $column;

    protected string $direction;

    /**
     * Creates an instance of builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self;
    }

    /**
     * @return $this
     */
    public function forCompany(Company $company): self
    {
        $this->company = $company;

        return $this;
    }

    /**
     * @return $this
     */
    public function forUser(User $user): self
    {
        $this->user = $user;

        return $this;
    }

    /**
     * @return $this
     */
    protected function column(mixed $column): self
    {
        $this->column = $column;

        return $this;
    }

    public function ascending(): self
    {
        $this->direction = 'ASC';

        return $this;
    }

    public function orderByContactType(): self
    {
        return $this->column('contact_type');
    }

    public function orderByDestinationModel(): self
    {
        return $this->column('contact_dest_model');
    }

    /**
     * Returns a final collection by running the query builder.
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a single record
     */
    public function first()
    {
        return $this->getQuery()->first();
    }

    /**
     * Returns a query for this instance.
     */
    public function getQuery(): Builder
    {
        return $this->callsByCompanyLocation()
            ->union($this->callsByCompanyUser())
            ->union($this->callsByCompany())
            ->union($this->calls())
            ->union($this->texts())
            ->union($this->emails())
            ->orderBy(
                $this->column ?? 'contact_date',
                $this->direction ?? 'DESC'
            );
    }

    // --- Queries ---

    protected function callsByCompanyLocation(): Builder
    {
        $query = CompanyLocation::query();

        return $this->joinCallsByRelation($query, 'company_locations.id', 'company_locations')
            ->joinPhones($query)
            ->joinUserPhones($query)
            ->joinUsers($query)
            ->joinCompanies($query, 'company_locations.company_id')
            ->select($query, Call::class, CompanyLocation::class)
            ->filter($query);
    }

    protected function callsByCompanyUser(): Builder
    {
        $query = CompanyUser::query();

        return $this->joinCallsByRelation($query, 'company_users.id', 'company_user')
            ->joinPhones($query)
            ->joinUserPhones($query)
            ->joinUsers($query)
            ->joinCompanies($query)
            ->select($query, Call::class, CompanyUser::class)
            ->filter($query);
    }

    protected function callsByCompany(): Builder
    {
        $query = Company::query();

        return $this->joinCallsByRelation($query, 'companies.id', 'company')
            ->joinPhones($query)
            ->joinUserPhones($query)
            ->joinUsers($query)
            ->select($query, Call::class, Company::class)
            ->filter($query);
    }

    protected function calls(): Builder
    {
        $query = CompanyUser::query();

        return $this->joinCallsByPhone($query, 'company_users.formatted_cell_phone')
            ->joinPhones($query)
            ->joinUserPhones($query)
            ->joinUsers($query)
            ->joinCompanies($query)
            ->select($query, Call::class, CompanyUser::class)
            ->filter($query);
    }

    protected function texts(): Builder
    {
        $query = Text::query();

        return $this->joinCompanyUsers($query, 'cell_phone', 'texts.other_number')
            ->joinPhones($query, 'texts.phone_id')
            ->joinUserPhones($query, 'texts')
            ->joinUsers($query)
            ->joinCompanies($query)
            ->select($query, Text::class, CompanyUser::class)
            ->filter($query);
    }

    protected function emails(): Builder
    {
        $query = Email::query();

        return $this->joinCompanyUsers($query, 'id', 'emails.to_company_user_id')
            ->joinUsers($query, 'emails.from_user_id')
            ->joinCompanies($query)
            ->select($query, Email::class, CompanyUser::class)
            ->filter($query);
    }

    // --- Joins ---

    protected function joinCallsByRelation(Builder $query, string $relationId, string $relationType)
    {
        $query->join('calls', fn ($join) => $join->on('calls.relation_id', $relationId)->where('calls.relation_type', $relationType));

        return $this;
    }

    protected function joinCallsByPhone(Builder $query, string $on)
    {
        $query->join(
            'calls',
            fn ($join) => $join->on('calls.formatted_other_number', $on)
                ->whereNotIn('calls.relation_type', ['company_user', 'company_location', 'company'])
        );

        return $this;
    }

    protected function joinPhones(Builder $query, string $on = 'calls.phone_id')
    {
        $query->join('phones', 'phones.id', $on);

        return $this;
    }

    protected function joinUserPhones(Builder $query, string $table = 'calls')
    {
        $query->join(
            'user_phones',
            fn ($join) => $join->on('user_phones.phone_id', 'phones.id')
                ->where(fn ($query) => $query->whereColumn("$table.created_at", '>=', 'user_phones.created_at')->whereNull('user_phones.deleted_at'))
                ->orWhere(fn ($query) => $query->whereBetweenColumns("$table.created_at", ['user_phones.created_at', 'user_phones.deleted_at']))
        );

        return $this;
    }

    protected function joinCompanyUsers(Builder $query, string $identifier, string $related)
    {
        $query->join('company_users', "company_users.$identifier", $related);

        return $this;
    }

    protected function joinUsers(Builder $query, string $on = 'user_phones.user_id')
    {
        $query->join('users', 'users.id', $on);

        return $this;
    }

    protected function joinCompanies(Builder $query, string $on = 'company_users.company_id')
    {
        $query->join('companies', 'companies.id', $on);

        return $this;
    }

    // --- Helpers ---

    protected function select(Builder $query, string $method, string $model)
    {
        $query->selectRaw(
            $this->baseSelects()
                ->merge($this->methodSelects($method))
                ->merge($this->modelSelects($model))
                ->map(fn ($column, $name) => "$column as $name")
                ->join(', ')
        );

        return $this;
    }

    protected function baseSelects()
    {
        return collect([
            'company_id' => 'companies.id',
            'company_name' => 'companies.name',
            'user_id' => 'users.id',
            'user_name' => 'users.name',
        ]);
    }

    protected function methodSelects(string $class)
    {
        $table = with(new $class)->getTable();

        $selects = collect([
            'contact_date' => "$table.created_at",
            'contact_model' => $this->sanitizeClassName($class),
            'contact_model_id' => "$table.id",
        ]);

        return $selects->merge(match ($class) {
            Call::class => [
                'contact_type' => "CONCAT(calls.relation_type, '_call')",
                'contact_dest' => 'calls.other_number',
                'contact_contents' => 'calls.external_reference',
            ],
            Text::class => [
                'contact_type' => '"text"',
                'contact_dest' => 'texts.other_number',
                'contact_contents' => 'texts.message_body',
            ],
            Email::class => [
                'contact_type' => '"email"',
                'contact_dest' => 'emails.to_address',
                'contact_contents' => 'emails.body',
            ]
        });
    }

    protected function modelSelects(string $class)
    {
        return collect(match ($class) {
            CompanyLocation::class => [
                'contact_name' => 'company_locations.name',
                'contact_dest_model' => $this->sanitizeClassName($class),
                'contact_dest_model_id' => 'company_locations.id',
            ],

            CompanyUser::class => [
                'contact_name' => 'CONCAT(company_users.first_name, " ", company_users.last_name)',
                'contact_dest_model' => $this->sanitizeClassName($class),
                'contact_dest_model_id' => 'company_users.id',
            ],

            Company::class => [
                'contact_name' => 'companies.name',
                'contact_dest_model' => $this->sanitizeClassName($class),
                'contact_dest_model_id' => 'companies.id',
            ]
        });
    }

    protected function filter(Builder $query): Builder
    {
        return $query
            ->when($this->company, fn ($query) => $query->where('companies.id', $this->company->id))
            ->when($this->user, fn ($query) => $query->where('users.id', $this->user->id));
    }

    protected function sanitizeClassName(string $class)
    {
        return str($class)->replace('\\', '\\\\')->wrap('\'');
    }
}
