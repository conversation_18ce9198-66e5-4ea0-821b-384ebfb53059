<?php

namespace App\Builders;

use App\Http\Requests\OpportunityNotifications\OpportunityNotificationRequest;
use App\Models\MissedProducts\OpportunityNotification;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class OpportunityNotificationBuilder
 * Used to simplify building a query for the OpportunityNotification model
 *
 * @package App\Builders
 */
class OpportunityNotificationBuilder
{
    const COLUMN_ID = OpportunityNotification::FIELD_ID;
    const ASC_DIRECTION = 'asc';
    const DESC_DIRECTION = 'desc';

    /**
     * @param string|null $recipients
     * @param string|null $content
     * @param string|null $deliveryMethod
     * @param array|null $sentAt
     * @param mixed|null $companyName
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param Carbon|null $sinceDate
     * @param int|null $configId
     */
    public function __construct(
        protected ?string $recipients = null,
        protected ?string $content = null,
        protected ?string $deliveryMethod = null,
        protected ?array $sentAt = null,
        protected mixed $companyName = null,
        protected ?string $sortCol = null,
        protected ?string $sortDir = null,
        protected ?Carbon $sinceDate = null,
        protected ?int $configId = null,
    ){
    }

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param string|null $recipients
     * @return $this
     */
    public function forRecipients(?string $recipients = null): self
    {
        $this->recipients = $recipients;

        return $this;
    }

    /**
     * @param string|null $content
     * @return $this
     */
    public function forContent(?string $content = null): self
    {
        $this->content = $content;

        return $this;
    }

    /**
     * @param string|null $deliveryMethod
     * @return $this
     */
    public function forDeliveryMethod(?string $deliveryMethod = null): self
    {
        $this->deliveryMethod = $deliveryMethod;

        return $this;
    }

    /**
     * Expects a date range array of two date values, 0 index being date_from and 1 index being date_to.
     *
     * @param array|null $sentAt
     * @return $this
     */
    public function forSentAt(array $sentAt = null):self
    {
        $this->sentAt = $sentAt;

        return $this;
    }

    /**
     * @param mixed $companyName
     * @return $this
     */
    public function forCompanyName(mixed $companyName = null):self
    {
        $this->companyName = $companyName;

        return $this;
    }

    /**
     * @param int|null $configId
     * @return $this
     */
    public function forConfigId(int $configId = null):self
    {
        $this->configId = $configId;

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(int $companyId = null):self
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * @param mixed $sortColumn
     * @return $this
     */
    public function sortColumn(string $sortColumn): self
    {
        $this->sortCol = $sortColumn;

        return $this;
    }

    /**
     * @param mixed $sortDirection
     * @return $this
     */
    public function sortDirection(string $sortDirection): self
    {
        $this->sortDir = $sortDirection;

        return $this;
    }

    /**
     * @param Carbon $date
     * @return $this
     */
    public function since(Carbon $date):self
    {
        $this->sinceDate = $date;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = OpportunityNotification::query();
        $selectCols = [OpportunityNotification::TABLE.'.*'];
        $query = $this->createJoinsForQuery($query);

        if($this->recipients !== null && strlen(trim($this->recipients)) > 0)
        {
            $query->where(OpportunityNotification::FIELD_RECIPIENTS, 'LIKE', "%$this->recipients%");
        }
        if($this->content !== null && strlen(trim($this->content)) > 0)
        {
            $query->where(OpportunityNotification::FIELD_CONTENT, 'LIKE', "%$this->content%");
        }
        if($this->deliveryMethod !== null && strlen(trim($this->deliveryMethod)) > 0)
        {
            $query->where(OpportunityNotification::FIELD_DELIVERY_METHOD, '=', $this->deliveryMethod);
        }
        if($this->sentAt !== null && is_array($this->sentAt)) {
            $from = Carbon::parse($this->sentAt[0])
                ->hour(0)
                ->minute(0)
                ->seconds(0);

            $to = Carbon::parse($this->sentAt[1])
                ->hour(23)
                ->minute(59)
                ->seconds(59);

            $query->where(OpportunityNotification::TABLE.'.'.OpportunityNotification::FIELD_SENT_AT, '>=', $from);
            $query->where(OpportunityNotification::TABLE.'.'.OpportunityNotification::FIELD_SENT_AT, '<=', $to);
        }
        if (!empty($this->sinceDate)) {
            $query->where(OpportunityNotification::TABLE.'.'.OpportunityNotification::FIELD_SENT_AT, '>', $this->sinceDate);
        }
        if (!empty($this->configId)) {
            $query->where(OpportunityNotification::TABLE.'.'.OpportunityNotification::FIELD_CONFIG_ID, $this->configId);
        }
        if (!empty($this->companyId)) {
            $query->where(OpportunityNotification::TABLE.'.'.OpportunityNotification::FIELD_COMPANY_ID, $this->companyId);
        }
        if($this->companyName !== null && $this->companyName !== '') {
            $selectCols[] = Company::TABLE.'.'.Company::FIELD_NAME;
            $query->where(Company::TABLE . '.' . Company::FIELD_ID, '=', $this->companyName)
                ->orWhere(Company::TABLE . '.' . Company::FIELD_NAME, 'LIKE', '%' . $this->companyName . '%');
        }
        if($this->sortCol && $this->sortDir !== null) {
            switch ($this->sortCol) {
                case OpportunityNotificationRequest::REQUEST_COMPANY_NAME:
                    $selectCols[] = Company::TABLE.'.'.Company::FIELD_NAME;
                    $query->orderBy(Company::TABLE.'.'.Company::FIELD_NAME, $this->sortDir);
                    break;
                default:
                    $query = $query->orderBy($this->sortCol, $this->sortDir);
            }
        }

        $query = $query->select($selectCols);

        return $query;
    }

    /**
     * Join the 'Companies' and 'ConsumerProducts' tables if the respective filters and/or sort are set.
     *
     * @param Builder $query
     * @return Builder
     */
    protected function createJoinsForQuery(Builder $query): Builder
    {
        if (($this->companyName !== null && $this->companyName !== '') || $this->sortCol === OpportunityNotificationRequest::REQUEST_COMPANY_NAME) {
            $query->join(Company::TABLE, Company::TABLE.'.'.Company::FIELD_ID, '=', OpportunityNotification::TABLE.'.'.OpportunityNotification::FIELD_COMPANY_ID);
        }

        return $query;
    }



}
