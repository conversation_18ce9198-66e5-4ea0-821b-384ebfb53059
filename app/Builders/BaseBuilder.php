<?php

namespace App\Builders;

use App\Contracts\Builders\BuilderContract;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

abstract class BaseBuilder implements BuilderContract
{
    /**
     * Runs the query builder, and returns a list of tasks.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }


    /**
     * Runs the query builder, and return the count of the query
     *
     * @return int
     */
    public function count(): int
    {
        return $this->getQuery()->count();
    }

    /**
     * Returns a paginated list of this builder.
     *
     * @param int $page
     * @param int $perPage
     * @return LengthAwarePaginator
     */
    public function paginate(int $page, int $perPage): LengthAwarePaginator
    {
        return $this->getQuery()->paginate($perPage, ['*'], 'page', $page);
    }
}
