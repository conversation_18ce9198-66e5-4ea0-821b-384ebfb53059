<?php

namespace App\Builders\Affiliates;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Enums\Affiliate\ReportGrouping;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Payout;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class AffiliateReportBuilder
{
    const string ID_COL             = 'id';
    const string NAME_COL           = 'name';
    const string REVENUE_COL        = 'revenue';
    const string PAYOUT_COL         = 'payout';
    const string LEAD_COUNT_COL     = 'lead_count';
    const string LEAD_GTS_COUNT_COL = 'lead_gts_count';
    const string LEG_SOLD_COUNT_COL = 'leg_count';

    protected array $sortColumnsMap = [
        self::ID_COL => self::ID_COL,
        self::NAME_COL => self::NAME_COL,
        self::REVENUE_COL => self::REVENUE_COL,
        self::PAYOUT_COL => self::PAYOUT_COL,
        self::LEAD_COUNT_COL => self::LEAD_COUNT_COL,
        self::LEAD_GTS_COUNT_COL => self::LEAD_GTS_COUNT_COL,
        self::LEG_SOLD_COUNT_COL => self::LEG_SOLD_COUNT_COL,
    ];

    public function __construct(
        protected ?Carbon $fromDate = null,
        protected ?Carbon $toDate = null,
        protected ?array $affiliateIds = null,
        protected ?string $name = null,
        protected ?array $industries = null,
        protected array $sortBy = [],
        protected ReportGrouping $grouping = ReportGrouping::AFFILIATE,
    )
    {}

    public static function query(): self
    {
        return new self();
    }

    public function fromDate(?Carbon $date): self
    {
        $this->fromDate = $date;

        return $this;
    }

    public function toDate(?Carbon $date): self
    {
        $this->toDate = $date;

        return $this;
    }

    public function affiliateIds(?array $affiliateIds): self
    {
        $this->affiliateIds = $affiliateIds;

        return $this;
    }

    public function name(?string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function industries(?array $industryIds): self
    {
        $this->industries = $industryIds;

        return $this;
    }

    public function sorting(array $sortBy = []): self
    {
        $this->sortBy = $sortBy;

        return $this;
    }

    public function grouping(ReportGrouping $grouping): self
    {
        $this->grouping = $grouping;

        return $this;
    }

    /**
     * @param Builder|JoinClause $query
     * @return AffiliateReportBuilder
     */
    public function filterDateRange(Builder|JoinClause $query): self
    {
        if (filled($this->fromDate) && filled($this->toDate)) {
            $query->whereBetween(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CREATED_AT, [
                $this->fromDate->clone()->startOfDay()->utc(),
                $this->toDate->clone()->endOfDay()->utc(),
            ]);
        }

        return $this;
    }

    /**
     * @throws Exception
     */
    public function filterName(Builder $query): self
    {
        if (filled($this->name)) {
            $query->where($this->getGroupName(), 'like',"%$this->name%");
        }

        return $this;
    }

    public function filterAffiliateIds(Builder $query): self
    {
        if(!empty($this->affiliateIds)) {
            $affiliateIdColumn = match ($this->grouping) {
                ReportGrouping::AFFILIATE => Affiliate::TABLE .'.'.Affiliate::FIELD_ID,
                ReportGrouping::CAMPAIGN => Campaign::TABLE .'.'.Campaign::FIELD_AFFILIATE_ID,
            };

            $query->whereIntegerInRaw($affiliateIdColumn, $this->affiliateIds);
        }

        return $this;
    }

    public function filterIndustries(Builder $query): self
    {
        if(!empty($this->industries)) {
            $query->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'.ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
                ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
                ->whereIntegerInRaw(IndustryService::TABLE .'.'. IndustryService::FIELD_INDUSTRY_ID, $this->industries);
        }

        return $this;
    }

    /**
     * @param Builder $query
     * @return $this
     */
    public function sortBy(Builder $query): self
    {
        $fieldDirections = collect($this->sortBy)
            ->map(fn($item) => explode(':', $item));

        foreach ($fieldDirections as [$field, $direction]) {
            $colField = Arr::get($this->sortColumnsMap, $field);

            if (filled($colField)) {
                $query->orderBy($colField, $direction);
            }
        }

        return $this;
    }

    /**
     * @throws Exception
     */
    public function getQuery(): Builder
    {

        // get revenue and legs sold per consumer product id
        $productAssignmentSubquery = ProductAssignment::query()
            ->select([
                ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,
                DB::raw('SUM('. ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COST .') as ' . self::REVENUE_COL),
                DB::raw('COALESCE(COUNT(DISTINCT '. ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID .'), 0) as ' . self::LEG_SOLD_COUNT_COL),
            ])
            ->join(ConsumerProduct::TABLE, function(JoinClause $join) {
                $join->on(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID,'=', ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID);
                $this->filterDateRange($join);
            })
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CHARGEABLE, '=', true)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED, '=', true)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->groupBy(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID);

        $payoutType = PayoutStrategy::TABLE .'.'. PayoutStrategy::FIELD_TYPE;
        $payoutValue = PayoutStrategy::TABLE .'.'. PayoutStrategy::FIELD_VALUE;
        // join revenue and legs sold with consumer product data and group by affiliate or campaign id
        $payoutSubquery = Payout::query()
            ->select([
                DB::raw("SUM(CASE"
                    ." when $payoutType = '" . PayoutStrategyTypeEnum::REVENUE_PERCENTAGE->value . "' then sub.".self::REVENUE_COL . " * $payoutValue"
                    ." when $payoutType = '" . PayoutStrategyTypeEnum::RAW_LEAD_FLAT_VALUE->value . "' then $payoutValue"
                    ." when $payoutType = '" . PayoutStrategyTypeEnum::SOLD_LEAD_FLAT_VALUE->value . "' and sub.consumer_product_id is not null then sub.".self::REVENUE_COL . " * $payoutValue"
                    ." end) as " . self::PAYOUT_COL),
                DB::raw("COUNT(DISTINCT ".ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID. ") as ". self::LEAD_COUNT_COL),
                DB::raw("COUNT(DISTINCT IF(" . ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_GOOD_TO_SELL . ", " .ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID. ", NULL)) as ". self::LEAD_GTS_COUNT_COL),
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID,
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID,
                DB::raw('SUM(sub.'.self::REVENUE_COL.') as '.self::REVENUE_COL),
                DB::raw('SUM(sub.'.self::LEG_SOLD_COUNT_COL.') as '.self::LEG_SOLD_COUNT_COL),
                $this->getGroupColumn() . ' as group_id',
            ])
            ->join(
                ConsumerProduct::TABLE,
                function (JoinClause $join) {
                    $join->on(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID,
                        '=',
                        Payout::TABLE .'.'. Payout::FIELD_CONSUMER_PRODUCT_ID);
                    $this->filterDateRange($join);
                }
            )->join(
                ConsumerProductAffiliateRecord::TABLE,
                ConsumerProductAffiliateRecord::TABLE .'.'. ConsumerProductAffiliateRecord::FIELD_ID,
                '=',
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID,
            )->join(PayoutStrategy::TABLE,
                PayoutStrategy::TABLE .'.'. PayoutStrategy::FIELD_ID,
                '=',
                Payout::TABLE .'.'. Payout::FIELD_PAYOUT_STRATEGY_ID
            )
            ->leftJoinSub(
                $productAssignmentSubquery,
                'sub',
                'sub.consumer_product_id',
                '=',
                ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID
            )->groupBy($this->getGroupColumn());


        $query = $this->getBaseQuery();

        $leftJoinCol = match ($this->grouping) {
            ReportGrouping::AFFILIATE => Affiliate::TABLE .'.'.Affiliate::FIELD_ID,
            ReportGrouping::CAMPAIGN => Campaign::TABLE .'.'.Campaign::FIELD_ID,
        };

        $this->filterIndustries($payoutSubquery);

        $query->addSelect([
            DB::raw("COALESCE(sub.".self::PAYOUT_COL.", 0) as ".self::PAYOUT_COL),
            DB::raw('COALESCE(sub.'.self::REVENUE_COL.', 0) as '.self::REVENUE_COL),
            DB::raw("COALESCE(sub.".self::LEAD_COUNT_COL.", 0) as ".self::LEAD_COUNT_COL),
            DB::raw("COALESCE(sub.".self::LEAD_GTS_COUNT_COL.", 0) as ".self::LEAD_GTS_COUNT_COL),
            DB::raw('COALESCE(sub.'.self::LEG_SOLD_COUNT_COL.', 0) as '.self::LEG_SOLD_COUNT_COL),
        ])->leftJoinSub($payoutSubquery, 'sub', 'sub.group_id', '=', $leftJoinCol);

        $this->filterAffiliateIds($query);
        $this->filterName($query);

        $this->sortBy($query);

        return $query;
    }

    /**
     * @return string
     * @throws Exception
     */
    private function getGroupColumn(): string
    {
        return match ($this->grouping) {
            ReportGrouping::AFFILIATE => ConsumerProductAffiliateRecord::TABLE .'.'. ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID,
            ReportGrouping::CAMPAIGN => ConsumerProductAffiliateRecord::TABLE .'.'. ConsumerProductAffiliateRecord::FIELD_CAMPAIGN_ID,
            default => throw new Exception('Unknown grouping')
        };
    }

    /**
     * @return string
     * @throws Exception
     */
    private function getGroupName(): string
    {
        return match ($this->grouping) {
            ReportGrouping::AFFILIATE => Affiliate::TABLE . '.' . Affiliate::FIELD_NAME,
            ReportGrouping::CAMPAIGN => Campaign::TABLE . '.' . Campaign::FIELD_NAME,
            default => throw new Exception('Unknown grouping')
        };
    }

    private function getBaseQuery(): Builder
    {
        return match ($this->grouping) {
            ReportGrouping::AFFILIATE => Affiliate::query()->select([
                Affiliate::TABLE .'.'.Affiliate::FIELD_ID . ' as ' . self::ID_COL,
                Affiliate::TABLE .'.'.Affiliate::FIELD_UUID,
                Affiliate::TABLE .'.'.Affiliate::FIELD_NAME. ' as ' . self::NAME_COL,
            ]),
            ReportGrouping::CAMPAIGN => Campaign::query()->select([
                Campaign::TABLE .'.'. Campaign::FIELD_ID. ' as ' . self::ID_COL,
                Campaign::TABLE .'.'. Campaign::FIELD_NAME. ' as ' . self::NAME_COL,
            ]),
        };
    }
}
