<?php

namespace App\Builders;

use App\Models\Legacy\EloquentUser;
use Carbon\Carbon;
use Firebase\JWT\JWT;

class DashboardShadowBuilder
{
    const PAYLOAD_USER_ID = 'uid';
    const PAYLOAD_USER_NAME = 'uname';
    const PAYLOAD_USER_EMAIL = 'uemail';
    const PAYLOAD_USER_COMPANY_ID = 'ucid';
    const PAYLOAD_USER_COMPANY_NAME = 'ucname';

    const PAYLOAD_ADMIN_ID = 'auid';
    const PAYLOAD_ADMIN_USER_NAME = 'auname';
    const PAYLOAD_ADMIN_USER_EMAIL = 'auemail';

    const PAYLOAD_ISSUED_AT = 'iat';
    const PAYLOAD_EXPIRATION_TIME = 'exp';
    const PAYLOAD_DATA = 'data';  // Option data

    public function __construct(
        public int $issuedAt,
        protected ?int $userId = null,
        protected ?int $shadowingUserId = null,
        protected ?int $expiration = null,
        protected ?string $signingKey = null,
        protected string $algorithm = 'HS256'
    ) {}

    /**
     * Creates a new builder instance.
     *
     * @return self
     */
    public static function query(): self
    {
        return new DashboardShadowBuilder(Carbon::now()->timestamp);
    }

    /**
     * Sets the id of the user shadowing.
     *
     * @param int|null $userId
     * @return $this
     */
    public function forUser(?int $userId = null): self
    {
        $this->userId = $userId;

        return $this;
    }

    /**
     * Sets the id of the user shadowing.
     *
     * @param int|null $userId
     * @return $this
     */
    public function setShadower(?int $userId = null): self
    {
        $this->shadowingUserId = $userId;

        return $this;
    }

    /**
     * Sets the expiration (in seconds) that this token will expire after.
     *
     * @param int|null $expiration
     * @return $this
     */
    public function setExpiration(?int $expiration = null): self
    {
        $this->expiration = $expiration;

        return $this;
    }

    /**
     * Sets the signing key for this builder.
     *
     * @param string|null $key
     * @return $this
     */
    public function setSigningKey(?string $key = null): self
    {
        $this->signingKey = $key;

        return $this;
    }

    public function setHashingAlgorithm(string $algorithm = 'HS256'): self
    {
        $this->algorithm = $algorithm;

        return $this;
    }

    /**
     * Returns the payload for the JWT.
     *
     * @TODO: Upgrade this when we get rid of legacy user models.
     *
     * @return array
     */
    protected function getPayload(): array
    {
        $payload = [
            self::PAYLOAD_USER_ID => $this->userId,
            self::PAYLOAD_ISSUED_AT => $this->issuedAt,
            self::PAYLOAD_EXPIRATION_TIME => Carbon::createFromTimestamp($this->issuedAt)->addSeconds($this->expiration)->timestamp,
            self::PAYLOAD_DATA => [],
        ];

        /** @var EloquentUser|null $user */
        $user = EloquentUser::query()->find($this->userId);

        if($user) {
            $payload[self::PAYLOAD_USER_NAME] = $user->firstname . ' ' . $user->lastname;
            $payload[self::PAYLOAD_USER_EMAIL] = $user->email;
            $payload[self::PAYLOAD_USER_COMPANY_ID] = $user->companyid;

            if($user->company) {
                $payload[self::PAYLOAD_USER_COMPANY_NAME] = $user->company->companyname;
            }
        }

        if($this->shadowingUserId) {
            /** @var EloquentUser|null $adminUser */
            $adminUser = EloquentUser::query()->findOrFail($this->shadowingUserId);

            if($adminUser) {
                $payload[self::PAYLOAD_ADMIN_ID] = $this->shadowingUserId;
                $payload[self::PAYLOAD_ADMIN_USER_NAME] = $adminUser->firstname . ' ' . $adminUser->lastname;
                $payload[self::PAYLOAD_ADMIN_USER_EMAIL] = $adminUser->email;
            }
        }

        return $payload;
    }

    /**
     * Runs the builder, and returns the shadow token.
     *
     * @return string|null
     */
    public function getToken(): ?string
    {
        return JWT::encode($this->getPayload(), $this->signingKey, $this->algorithm);
    }
}
