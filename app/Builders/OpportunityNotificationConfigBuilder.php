<?php

namespace App\Builders;

use App\Models\MissedProducts\OpportunityNotificationConfig;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class OpportunityNotificationConfigBuilder
 * Used to simplify building a query for the OpportunityNotificationConfig model
 *
 * @package App\Builders
 */
class OpportunityNotificationConfigBuilder
{

    /**
     * @param string|null $name
     * @param string|null $maxFrequency
     * @param string|null $sendTime
     * @param int|null $leadThreshold
     * @param bool|null $active
     * @param string|null $sortCol
     * @param string|null $sortDir
     * @param array|null $countingRelations
     */
    public function __construct(
        protected ?string $name = null,
        protected ?string $maxFrequency = null,
        protected ?string $sendTime = null,
        protected ?int    $leadThreshold = null,
        protected ?bool   $active = null,
        protected ?string $sortCol = null,
        protected ?string $sortDir = null,
        protected ?array  $countingRelations = null,
    ){
    }

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @param string|null $name
     * @return $this
     */
    public function forName(?string $name = null): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * @param int|null $frequency
     * @return $this
     */
    public function forMaxFrequency(?int $frequency = null): self
    {
        $this->maxFrequency = $frequency;

        return $this;
    }

    /**
     * @param string|null $sendTime
     * @return $this
     */
    public function forSendTime(?string $sendTime = null): self
    {
        $this->sendTime = $sendTime;

        return $this;
    }

    /**
     * @param int|null $leadThreshold
     * @return $this
     */
    public function forLeadThreshold(?int $leadThreshold = null):self
    {
        $this->leadThreshold = $leadThreshold;

        return $this;
    }

    /**
     * @param mixed $active
     * @return $this
     */
    public function forActive(mixed $active = null):self
    {
        $this->active = $active;

        return $this;
    }

    /**
     * @param mixed $sortColumn
     * @return $this
     */
    public function sortColumn(?string $sortColumn = null): self
    {
        $this->sortCol = $sortColumn;

        return $this;
    }

    /**
     * @param mixed $sortDirection
     * @return $this
     */
    public function sortDirection(?string $sortDirection = null): self
    {
        $this->sortDir = $sortDirection;

        return $this;
    }


    /**
     * Add withCount to the query
     * @param array|null $countingRelations
     * @return $this
     */
    public function withCountingRelations(?array $countingRelations = null): static
    {
        $this->countingRelations = $countingRelations;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = OpportunityNotificationConfig::query();

        if($this->name !== null && strlen(trim($this->name)) > 0)
        {
            $query->where(OpportunityNotificationConfig::FIELD_NAME, 'LIKE', "%$this->name%");
        }
        if($this->maxFrequency)
        {
            $query->where(OpportunityNotificationConfig::FIELD_MAXIMUM_SEND_FREQUENCY, '=', $this->maxFrequency);
        }
        if($this->sendTime !== null && strlen(trim($this->sendTime)) > 0)
        {
            $query->where(OpportunityNotificationConfig::FIELD_SEND_TIME, '=', $this->sendTime);
        }
        if($this->leadThreshold !== null && $this->leadThreshold >= 0)
        {
            $query->where(OpportunityNotificationConfig::FIELD_LEAD_THRESHOLD, '=', $this->leadThreshold);
        }
        if($this->active != null) {
             $query->where(OpportunityNotificationConfig::FIELD_ACTIVE, $this->active);
        }
        if($this->sortCol && $this->sortDir !== null) {
            $query = $query->orderBy($this->sortCol, $this->sortDir);
        }

        if (!empty($this->countingRelations)) {
            foreach ($this->countingRelations as $relation) {
                $query->withCount($relation);
            }
        }


        return $query;
    }
}
