<?php

namespace App\Builders\Appointments;

use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\ProductAssignmentStatus;
use App\Enums\Odin\QualityTier;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;

class AppointmentSearchBuilder
{
    public function __construct(
        protected ?int $companyId = null,
        protected ?int $appointmentId = null,
        protected ?int $campaignId = null,
        protected ?string $status = null,
        protected ?int $invoiceId = null,
        protected ?string $contactSearch = null,
        protected ?string $addressSearch = null,
        protected ?string $stateAbbr = null,
        protected ?string $city = null,
        protected ?string $zipCode = null,
        protected ?int $startTimestamp = null,
        protected ?int $endTimestamp = null,
        protected ?QualityTier $appointmentCategory = null,
        protected ?bool $delivered = null
    ) {}

    /**
     * @return static
     */
    public static function newQuery(): static
    {
        return new static();
    }

    /**
     * @param int $companyId
     *
     * @return $this
     */
    public function forCompany(int $companyId): static
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * @param int $appointmentId
     *
     * @return $this
     */
    public function forAppointment(int $appointmentId): static
    {
        $this->appointmentId = $appointmentId;

        return $this;
    }

    /**
     * @param int $campaignId
     *
     * @return $this
     */
    public function forCampaign(int $campaignId): static
    {
        $this->campaignId = $campaignId;

        return $this;
    }

    /**
     * @param string $status
     *
     * @return $this
     */
    public function forStatus(string $status): static
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @param int $invoiceId
     *
     * @return $this
     */
    public function forInvoice(int $invoiceId): static
    {
        $this->invoiceId = $invoiceId;

        return $this;
    }

    /**
     * @param string $contact
     *
     * @return $this
     */
    public function forContact(string $contact): static
    {
        $this->contactSearch = $contact;

        return $this;
    }

    /**
     * @param string $address
     *
     * @return $this
     */
    public function forAddress(string $address): static
    {
        $this->addressSearch = $address;

        return $this;
    }

    /**
     * @param string $statAbbr
     *
     * @return $this
     */
    public function forStateAbbr(string $statAbbr): static
    {
        $this->stateAbbr = $statAbbr;

        return $this;
    }

    /**
     * @param string $city
     *
     * @return $this
     */
    public function forCity(string $city): static
    {
        $this->city = $city;

        return $this;
    }

    /**
     * @param string $zipCode
     *
     * @return $this
     */
    public function forZipCode(string $zipCode): static
    {
        $this->zipCode = $zipCode;

        return $this;
    }

    /**
     * @param int $timestamp
     *
     * @return $this
     */
    public function forStartTimestamp(int $timestamp): static
    {
        $this->startTimestamp = $timestamp;

        return $this;
    }

    /**
     * @param int $timestamp
     *
     * @return $this
     */
    public function forEndTimestamp(int $timestamp): static
    {
        $this->endTimestamp = $timestamp;

        return $this;
    }

    /**
     * @param QualityTier $category
     *
     * @return $this
     */
    public function forAppointmentCategory(QualityTier $category): static
    {
        $this->appointmentCategory = $category;

        return $this;
    }

    /**
     * @param bool $status
     *
     * @return $this
     */
    public function forDeliveryStatus(bool $status): static
    {
        $this->delivered = $status;

        return $this;
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = ProductAssignment::query()
            ->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_SERVICE_PRODUCT . '.' . ServiceProduct::RELATION_PRODUCT, function ($has) {
                return $has->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value);
            });

        if ($this->companyId !== null)
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $this->companyId);
        if ($this->delivered !== null)
            $query->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, $this->delivered);
        if ($this->appointmentId !== null)
            $query->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID, $this->appointmentId);
        if ($this->campaignId !== null)
            $query->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CAMPAIGN_ID, $this->campaignId);
        if ($this->status !== null)
            $this->filterStatus($query);
        if ($this->invoiceId !== null)
            $this->filterInvoice($query);
        if ($this->contactSearch !== null)
            $this->filterContact($query);
        if ($this->addressSearch !== null || $this->stateAbbr !== null || $this->city !== null || $this->zipCode !== null)
            $this->filterLocation($query);
        if ($this->startTimestamp !== null)
            $query->whereRaw(sprintf(
                "UNIX_TIMESTAMP(%s) >= %s",
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED_AT,
                $this->startTimestamp
            ));
        if ($this->endTimestamp !== null)
            $query->whereRaw(sprintf(
                "UNIX_TIMESTAMP(%s) <= %s",
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED_AT,
                $this->endTimestamp
            ));
        if ($this->appointmentCategory !== null)
            $query->whereHas(ProductAssignment::RELATION_APPOINTMENT, function($has) {
                $has->where(ProductAppointment::APPOINTMENT_TYPE, $this->appointmentCategory);
            });

        return $query;
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterLocation(Builder $query): void
    {
        $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_ADDRESS, function ($has) {
            if ($this->addressSearch !== null) {
                $has->where(Address::FIELD_ADDRESS_1, 'LIKE', "%{$this->addressSearch}%")
                    ->orWhere(Address::FIELD_ADDRESS_2, 'LIKE', "%{$this->addressSearch}%");
            }

            if ($this->stateAbbr !== null) {
                $has->where(Address::FIELD_STATE, $this->stateAbbr);

                if ($this->city !== null) {
                    $has->where(Address::FIELD_CITY, preg_replace('/[^\w]/', ' ', $this->city));
                }
            }

            if ($this->zipCode !== null) {
                $has->where(Address::FIELD_ZIP_CODE, $this->zipCode);
            }
        });
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterContact(Builder $query): void
    {
        $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER, function($has) {
            $has->where(Consumer::FIELD_FIRST_NAME, 'LIKE', "%{$this->contactSearch}%")
                ->orWhere(Consumer::FIELD_LAST_NAME, 'LIKE', "%{$this->contactSearch}%")
                ->orWhere(Consumer::FIELD_EMAIL, 'LIKE', "%{$this->contactSearch}%");
        });
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterInvoice(Builder $query): void
    {
        $query->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE, function ($join) {
            $join->on(
                DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::ID,
                '=',
                DatabaseHelperService::database() . '.' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_LEGACY_ID
            );
        })
            ->join(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE, function ($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase() . '.' . EloquentQuoteCompany::TABLE . '.' . EloquentQuoteCompany::INVOICE_ITEM_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ITEM_ID
                    )
                    ->where(EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, $this->invoiceId);
            });
    }

    /**
     * @param Builder $query
     *
     * @return void
     */
    protected function filterStatus(Builder $query): void
    {
        switch ($this->status) {
            case ProductAssignmentStatus::SOLD->value:
                $query
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true)
                    ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
                    ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS);
                break;
            case ProductAssignmentStatus::REJECTED->value:
                $query->has(ProductAssignment::RELATION_PRODUCT_REJECTIONS);
                break;
            case ProductAssignmentStatus::CANCELLED->value:
                $query
                    ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, false)
                    ->has(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS);
                break;
            default:
                logger()->error("Invalid status: {$this->status}");
        }
    }
}
