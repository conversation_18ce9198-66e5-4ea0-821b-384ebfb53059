<?php

namespace App\Builders;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Legacy\EloquentUtility;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLeadCategory;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadCampaignUtility;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductCampaign;
use App\Services\DatabaseHelperService;
use App\Services\Odin\Appointments\AppointmentService;
use App\Traits\LegacyBrsPotentialLeadCampaigns;
use App\Traits\LegacyBrsPotentialProductCampaigns;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class LegacyBrsPotentialCampaignsBuilder
{
    use LegacyBrsPotentialProductCampaigns, LegacyBrsPotentialLeadCampaigns;

    const SALE_TYPE_ID_COL = 'sale_type_id';
    const CAMPAIGN_ID_COL = 'campaign_id';
    const COMPANY_ID_COL = 'company_id';

    private Builder $query;

    private ProductEnum $product;

    private int $consumerProductId;

    /**
     * @param ProductEnum $product
     * @param int $consumerProductId
     * @throws Exception
     */
    public function __construct(ProductEnum $product, int $consumerProductId) {
        $this->product = $product;
        $this->consumerProductId = $consumerProductId;

        if($product === ProductEnum::LEAD) {
            $this->query = LeadCampaign::query();
        }
        else if($product === ProductEnum::APPOINTMENT) {
            $this->query = ProductCampaign::query();
        }
        else {
            throw new Exception("Invalid product");
        }
    }

    /**
     * @param ProductEnum $product
     * @param int $consumerProductId
     * @return static
     * @throws Exception
     */
    public static function query(ProductEnum $product, int $consumerProductId): self
    {
        return new self($product, $consumerProductId);
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        return clone $this->query;
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function setQuery(Builder $query): Builder
    {
        $this->query = $query;

        return $this->query;
    }

    /**
     * @param float $rejectionPercentageThreshold
     * @param array $excludedLegacyCompanyIds
     * @param array $companyTypes
     * @return $this
     */
    public function attachLeadConstraints(
        float $rejectionPercentageThreshold,
        array $excludedLegacyCompanyIds,
        array $companyTypes
    ): self
    {
        $this->query = $this->addLeadConstraints(
            $this->query,
            $rejectionPercentageThreshold,
            $excludedLegacyCompanyIds,
            $companyTypes
        );

        return $this;
    }

    /**
     * @param string $industry
     * @param array $excludedCompanyIds
     * @return $this
     */
    public function attachAppointmentConstraints(
        string $industry,
        array $excludedCompanyIds
    ): self
    {
        $this->query = $this->addAppointmentConstraints(
            $this->query,
            $industry,
            $excludedCompanyIds
        );

        return $this;
    }

    /**
     * @param array $saleTypeIds
     * @param int $leadCategoryId
     * @param string $zipcode
     * @param string $industry
     * @param string $stateAbbr
     * @param int $utilityId
     * @param string|null $utilityName
     * @return $this
     */
    public function attachSharedConstraints(
        array $saleTypeIds,
        int $leadCategoryId,
        string $zipcode,
        string $industry,
        string $stateAbbr,
        int $utilityId,
        ?string $utilityName
    ): self
    {
        $this
            ->joinLeadCampaignLocation()
            ->joinLocation()
            ->joinLeadCampaignSalesTypeConfiguration()
            ->joinLeadCampaignLeadCategory()
            ->whereLeadCampaignIsNotManagedByAdmin2()
            ->whereLeadCampaignSalesTypeConfigurationIn($saleTypeIds)
            ->whereLeadCategoryIs($leadCategoryId)
            ->whereZipCodeIs($zipcode);

        if(
            config('sales.are_utility_filters_active') &&
            $industry === IndustryEnum::SOLAR->value &&
            trim(strtolower($utilityName)) !== 'other'
        ) {
            $this->whereUtilityIs($stateAbbr, $utilityId);
        }

        return $this;
    }

    /**
     * @return $this
     */
    public function joinLeadCampaignLocation(): self
    {
        $this->query->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLocation::TABLE, function($join) {
            $join
                ->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLocation::TABLE.'.'.LeadCampaignLocation::LEAD_CAMPAIGN_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
                );
        });

        return $this;
    }

    /**
     * @return $this
     */
    public function joinLocation()
    {
        $this->query->join(Location::TABLE, function($join) {
            $join
                ->on(
                    Location::TABLE.'.'.Location::ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLocation::TABLE.'.'.LeadCampaignLocation::LOCATION_ID
                );
        });

        return $this;
    }

    /**
     * @return $this
     */
    public function joinLeadCampaignSalesTypeConfiguration()
    {
        $this->query->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignSalesTypeConfiguration::TABLE, function($join) {
            $join
                ->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
                );
        });

        return $this;
    }

    /**
     * @return $this
     */
    public function joinLeadCampaignLeadCategory()
    {
        $this->query->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLeadCategory::TABLE, function($join) {
            $join
                ->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignLeadCategory::TABLE.'.'.LeadCampaignLeadCategory::LEAD_CAMPAIGN_ID,
                    '=',
                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
                );
        });

        return $this;
    }

    /**
     * @return $this
     */
    public function whereLeadCampaignIsNotManagedByAdmin2(): self
    {
        $this->query->where(LeadCampaign::IS_MANAGED_BY_A2, false);

        return $this;
    }

    /**
     * @param array $saleTypeIds
     * @return $this
     */
    public function whereLeadCampaignSalesTypeConfigurationIn(array $saleTypeIds): self
    {
        $this->query
            ->where(LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::STATUS, true)
            ->whereIn(LeadCampaignSalesTypeConfiguration::TABLE.'.'.LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, $saleTypeIds);

        return $this;
    }

    /**
     * @param int $leadCategoryId
     * @return $this
     */
    public function whereLeadCategoryIs(int $leadCategoryId): self
    {
        $this->query->where(LeadCampaignLeadCategory::TABLE.'.'.LeadCampaignLeadCategory::LEAD_CATEGORY_ID, $leadCategoryId);

        return $this;
    }

    /**
     * @param string $zipcode
     * @return $this
     */
    public function whereZipCodeIs(string $zipcode): self
    {
        $this->query
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_ZIP_CODE)
            ->where(Location::TABLE.'.'.Location::ZIP_CODE, $zipcode);

        return $this;
    }

    /**
     * @param string $stateAbbr
     * @param int $legacyUtilityId
     * @return $this
     */
    public function whereUtilityIs(string $stateAbbr, int $legacyUtilityId): self
    {
        $this->query
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignUtility::TABLE, function($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignUtility::TABLE.'.'.LeadCampaignUtility::LEAD_CAMPAIGN_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
                    );
            })
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentUtility::TABLE, function($join) {
                $join
                    ->on(
                        DatabaseHelperService::readOnlyDatabase().'.'.EloquentUtility::TABLE.'.'.EloquentUtility::ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaignUtility::TABLE.'.'.LeadCampaignUtility::UTILITY_ID
                    );
            })
            ->where(EloquentUtility::TABLE.'.'.EloquentUtility::FIELD_STATE, $stateAbbr)
            ->where(EloquentUtility::TABLE.'.'.EloquentUtility::ID, $legacyUtilityId);

        return $this;
    }

    /**
     * @return Collection
     */
    public function get(): Collection
    {
        $this->query
            ->distinct()
            ->groupBy([
                self::CAMPAIGN_ID_COL,
                self::SALE_TYPE_ID_COL
            ]);

        if(config('sales.appointments.log_brs_queries')) {
            $rawQuery = base64_encode(gzcompress(Str::replaceArray('?', $this->query->getBindings(), $this->query->toSql()), 9));

            $campaigns = $this->query->get();

            AppointmentService::writeAppointmentLog(
                "Legacy BRS query: {$this->consumerProductId}",
                [
                    "consumer_product_id" => $this->consumerProductId,
                    "query" => $rawQuery,
                    "results" => $campaigns->groupBy([self::SALE_TYPE_ID_COL, self::CAMPAIGN_ID_COL])->toArray()
                ]
            );

            return $campaigns;
        }

        return $this->query->get();
    }
}
