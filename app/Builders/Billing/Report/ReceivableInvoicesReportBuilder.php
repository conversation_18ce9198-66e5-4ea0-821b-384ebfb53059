<?php

namespace App\Builders\Billing\Report;

use App\Enums\Billing\InvoiceStates;
use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Billing\InvoiceTransaction;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ReceivableInvoicesReportBuilder extends BaseInvoiceReportBuilder
{
    public array $sortColumnsMap = [
        'company_id'        => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID,
        'invoice_id'        => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID,
        'created_at'        => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE,
        'issued_at'         => Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT,
        'due_at'            => Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT,
        'total_receivable'  => 'total_receivable',
    ];


    /**
     * @param Builder $query
     */
    private function __construct(Builder $query)
    {
        parent::__construct($query);
    }

    /**
     * @param string|null $date
     * @return self
     * @throws Exception
     */
    public static function query(
        string $date = null,
    ): ReceivableInvoicesReportBuilder
    {
        if (empty($date)) {
            throw new Exception('Date is required');
        }

        $query = InvoiceSnapshot::mostRecentByInvoice([
            'date_to'             => $date,
            'exclude_credit_paid' => true,
        ])
            ->hasRelevantOutstandingAmount()
            ->select([
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_ID . ' as ' . InvoiceSnapshot::FIELD_ID,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_UUID . ' as ' . InvoiceSnapshot::FIELD_UUID,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID . ' as ' . InvoiceSnapshot::FIELD_INVOICE_ID,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID . ' as ' . InvoiceSnapshot::FIELD_COMPANY_ID,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_ACCOUNT_MANAGER_ID . ' as ' . InvoiceSnapshot::FIELD_ACCOUNT_MANAGER_ID,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_SUCCESS_MANAGER_ID . ' as ' . InvoiceSnapshot::FIELD_SUCCESS_MANAGER_ID,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_BUSINESS_DEVELOPMENT_MANAGER_ID . ' as ' . InvoiceSnapshot::FIELD_BUSINESS_DEVELOPMENT_MANAGER_ID,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_STATUS . ' as ' . InvoiceSnapshot::FIELD_STATUS,
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE . ' as ' . InvoiceSnapshot::FIELD_DATE,
                Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ' as ' . Invoice::FIELD_ISSUE_AT,
                Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT . ' as ' . Invoice::FIELD_DUE_AT,
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_VALUE . ') as ' . InvoiceSnapshot::FIELD_TOTAL_VALUE),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ') as ' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_REFUNDED . ') as ' . InvoiceSnapshot::FIELD_TOTAL_REFUNDED),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_PAID . ') as ' . InvoiceSnapshot::FIELD_TOTAL_PAID),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS . ') as ' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_RECOVERED . ') as ' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_RECOVERED),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_LOST . ') as ' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_LOST),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED . ') as ' . InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK . ') as ' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_WON . ') as ' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_WON),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_LOST . ') as ' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_LOST),
                DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_WRITTEN_OFF . ') as ' . InvoiceSnapshot::FIELD_TOTAL_WRITTEN_OFF),
                DB::raw('COUNT(DISTINCT ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID . ') as invoices_count'),
                DB::raw('COUNT(DISTINCT ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID . ') as companies_count'),
                DB::raw('SUM(
                    CASE
                        WHEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ' > 0
                        THEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . '
                        WHEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK . ' > 0
                        THEN ' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK . '
                        ELSE 0
                    END
                ) as total_receivable'),

            ])
            ->join(Invoice::TABLE, Invoice::TABLE . '.' . Invoice::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID);

        $builder = new self($query);

        $builder->joinCompanyUserRelationships();

        return $builder;
    }

    /**
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return $this
     */
    public function forDateRange(?string $dateFrom = null, ?string $dateTo = null): ReceivableInvoicesReportBuilder
    {
        if (filled($dateFrom)) {
            $this->query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE, '>=', CarbonHelper::parseWithTimezone($dateFrom)->startOfDayUTC());
        }

        if (filled($dateTo)) {
            $this->query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_DATE, '<=', CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC());
        }

        return $this;
    }

    /**
     * @param string|null $invoiceStatus
     * @return ReceivableInvoicesReportBuilder
     */
    public function forInvoiceStatus(?string $invoiceStatus = null): BaseInvoiceReportBuilder
    {
        if (isset($invoiceStatus)) {
            $this->query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_STATUS, $invoiceStatus);
        }

        return $this;
    }

    /**
     * @return $this
     */
    public function joinCompany(): self
    {
        $this->safeJoin('company', fn() => $this->query->leftJoin(
            DatabaseHelperService::database() . '.' . Company::TABLE,
            Company::TABLE . '.' . Company::FIELD_ID,
            InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID,
        ));

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): ReceivableInvoicesReportBuilder
    {
        $this->joinCompany();

        if (filled($companyId)) {
            $this->query->where(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID, $companyId);
        }

        return $this;

    }

    /**
     * @param array|null $scenarios
     * @return $this
     */
    public function forScenarios(?array $scenarios = null): ReceivableInvoicesReportBuilder
    {
        if (filled($scenarios)) {
            $this->query->whereIn(InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_SCENARIO, $scenarios);
        }

        return $this;
    }


    /**
     * @param array|null $types
     * @return $this
     */
    public function forTypes(?array $types = null): ReceivableInvoicesReportBuilder
    {
        if (filled($types)) {
            $this->query->whereIn(InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_TYPE, $types);
        }

        return $this;
    }
}
