<?php

namespace App\Builders\Billing\Report;

use App\Builders\Billing\BillingBuilder;
use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use \Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Facades\DB;

class OverdueInvoicesReportBuilder extends BillingBuilder
{
    /**
     * @var array|string[]
     */
    protected array $sortColumnsMap = [
        'days_to_pay'                      => 'days_to_pay',
        'company_id'                       => Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID,
        'invoice_id'                       => Invoice::TABLE . '.' . Invoice::FIELD_ID,
        'issue_date'                       => Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT,
        'due_date'                         => Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT,
        'outstanding_total'                => 'total_outstanding',
        'pay_date'                         => 'pay_date',
        'payment_method'                   => 'payment_method',
        'items_total'                      => 'total_value',
        'billing_profile_due_in_days'      => 'billing_profile_due_in_days',
        'issued_count'                     => 'issued_count',
        'unpaid_count'                  => 'unpaid_count',
        'due_count'                        => 'due_count',
        'total_due'                        => 'total_due',
        'avg_days_to_pay'                  => 'avg_days_to_pay',
        'invoice_status'                   => 'invoice_status',
        'company_name'                     => 'company_name',
    ];


    /**
     * @param Builder|QueryBuilder $builder
     */
    public function __construct(protected Builder|QueryBuilder $builder)
    {
        parent::__construct($builder);
    }


    /**
     * @param array $filters
     * @return $this
     */
    public function applyFilters(array $filters): self
    {
        return $this;
    }

    /**
     * @param string|null $date
     * @param string|null $reference
     * @return BillingBuilder
     */
    public static function query(string $date = null, string $reference = null): BillingBuilder
    {
        $issuedQuery = InvoiceSnapshot::mostRecentByInvoice([
            'date_to'   => $date,
            'in_status' => [InvoiceStates::ISSUED]
        ]);

        $paidQuery = InvoiceSnapshot::mostRecentByInvoice([
            'date_to'   => $date,
            'in_status' => [InvoiceStates::PAID]
        ]);

        $date = Carbon::parse($date)->utc()->endOfDay()->format('Y-m-d H:i:s');

        $base = Invoice::query()
            ->select([
                Invoice::TABLE . '.' . Invoice::FIELD_ID . ' as invoice_id',
                Invoice::TABLE . '.' . Invoice::FIELD_STATUS . ' as invoice_status',
                Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ' as issue_at',
                Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT . ' as due_at',
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_PAYMENT_METHOD . ' as payment_method',
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID . ' as billing_profile_id',
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_DUE_IN_DAYS . ' as billing_profile_due_in_days',
                'paid_snapshot.date as pay_date',
                DB::raw('COUNT(DISTINCT invoices.id) as issued_count'),
                DB::raw('COUNT(DISTINCT IF(invoices.due_at >= invoices.issue_at and paid_snapshot.date is null, invoices.id, NULL)) as due_count'),
                DB::raw('SUM(IF(invoices.due_at >= invoices.issue_at, latest_snapshot.total_outstanding, 0)) as total_due'),
                DB::raw('SUM(issued_snapshot.total_value) as total_value'),
                DB::raw('SUM(latest_snapshot.total_outstanding) as total_outstanding'),
                DB::raw('COUNT(DISTINCT IF(paid_snapshot.date is null, invoices.id, NULL)) as unpaid_count'),
                DB::raw("
                  SUM(DATEDIFF(
                    CONVERT_TZ(" . Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT . ", '+00:00', 'America/Denver'),
                    CONVERT_TZ(COALESCE(paid_snapshot.date, '$date'), '+00:00', 'America/Denver')
                  )) as days_to_pay
                "),
                DB::raw("
                    AVG(
                      IF(paid_snapshot.date IS NULL, 0,
                        DATEDIFF(
                          CONVERT_TZ(" . Invoice::TABLE . '.' . Invoice::FIELD_DUE_AT . ", '+00:00', 'America/Denver'),
                          CONVERT_TZ(paid_snapshot.date, '+00:00', 'America/Denver')
                        )
                      )
                    ) as avg_days_to_pay
                ")
            ])
            ->where(Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT, '<=', $date)
            ->join(BillingProfile::TABLE, BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID, Invoice::TABLE . '.' . Invoice::FIELD_BILLING_PROFILE_ID)
            ->leftJoinSub($paidQuery, 'paid_snapshot', 'paid_snapshot.invoice_id', Invoice::TABLE . '.' . Invoice::FIELD_ID)
            ->leftJoinSub($issuedQuery, 'issued_snapshot', 'issued_snapshot.invoice_id', Invoice::TABLE . '.' . Invoice::FIELD_ID)
            ->leftJoinSub(InvoiceSnapshot::mostRecentByInvoice(), 'latest_snapshot', 'latest_snapshot.invoice_id', Invoice::TABLE . '.' . Invoice::FIELD_ID)
            ->whereNotIn(Invoice::TABLE . '.' . Invoice::FIELD_STATUS, [
                InvoiceStates::DELETED,
                InvoiceStates::WRITTEN_OFF,
                InvoiceStates::VOIDED,
            ]);

        if ($reference === 'company') {
            $base->groupBy(Company::TABLE . '.' . Company::FIELD_ID);
        } else {
            $base->groupBy(Invoice::TABLE . '.' . Invoice::FIELD_ID);
        }


        return new self($base);
    }

    /**
     * @param string|null $paymentStatus
     * @return $this
     */
    public function forPaymentStatus(?string $paymentStatus = null): static
    {
        if (empty($paymentStatus) || $paymentStatus === 'all') {
            return $this;
        }

        $this->query->whereNull(
            columns: 'paid_snapshot.id',
            not    : $paymentStatus === 'paid'
        );

        return $this;
    }

    /**
     * @param int|null $invoiceId
     * @return $this
     */
    public function forInvoiceId(?int $invoiceId = null): static
    {
        if (filled($invoiceId)) {
            $this->query->where(Invoice::TABLE . '.' . Invoice::FIELD_ID, $invoiceId);
        }

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): self
    {
        if (filled($companyId)) {
            $this->query->where(Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID, $companyId);
        }
        return $this;
    }


    /**
     * @param string|null $paymentMethod
     * @return $this
     */
    public function forPaymentMethod(?string $paymentMethod = null): self
    {
        if (filled($paymentMethod)) {
            $this->query->where(BillingProfile::TABLE . '.' . BillingProfile::FIELD_PAYMENT_METHOD, $paymentMethod);
        }
        return $this;
    }
}
