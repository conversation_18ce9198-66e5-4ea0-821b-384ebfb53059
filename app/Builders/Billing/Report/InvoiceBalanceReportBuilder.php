<?php

namespace App\Builders\Billing\Report;

use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use \Illuminate\Database\Query\Builder as QueryBuilder;

class InvoiceBalanceReportBuilder extends BaseInvoiceReportBuilder
{
    protected array $sortColumnsMap = [
        'invoice_id'                  => InvoiceSnapshot::FIELD_INVOICE_ID,
        'company_id'                  => InvoiceSnapshot::FIELD_COMPANY_ID,
        'total_credits_applied'       => InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED,
        'total_value'                 => InvoiceSnapshot::FIELD_TOTAL_VALUE,
        'total_outstanding'           => InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING,
        'total_refunded'              => InvoiceSnapshot::FIELD_TOTAL_REFUNDED,
        'total_paid'                  => InvoiceSnapshot::FIELD_TOTAL_PAID,
        'total_collections'           => InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS,
        'total_collections_recovered' => InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_RECOVERED,
        'total_collections_lost'      => InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_LOST,
        'total_credit_applied'        => InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED,
    ];

    /**
     * @param Builder|QueryBuilder $query
     */
    public function __construct(protected Builder|QueryBuilder $query)
    {
        parent::__construct($query);
    }

    /**
     * @return self
     */
    public static function query(): InvoiceBalanceReportBuilder
    {
        $query = InvoiceSnapshot::mostRecentByInvoice()
            ->select([
                    InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID . ' as ' . InvoiceSnapshot::FIELD_INVOICE_ID,
                    InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID . ' as ' . InvoiceSnapshot::FIELD_COMPANY_ID,
                    Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_VALUE . ') as ' . InvoiceSnapshot::FIELD_TOTAL_VALUE),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ') as ' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_REFUNDED . ') as ' . InvoiceSnapshot::FIELD_TOTAL_REFUNDED),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_PAID . ') as ' . InvoiceSnapshot::FIELD_TOTAL_PAID),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS . ') as ' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_RECOVERED . ') as ' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_RECOVERED),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_LOST . ') as ' . InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_LOST),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED . ') as ' . InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED),
                ]
            )
            ->join(Invoice::TABLE, Invoice::TABLE . '.' . Invoice::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID)
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID)
            ->whereDoesntHave(InvoiceSnapshot::RELATION_INVOICE, function (Builder $query) {
                $query->whereIn(Invoice::FIELD_STATUS, [
                    InvoiceStates::DRAFT->value,
                    InvoiceStates::VOIDED->value,
                    InvoiceStates::DELETED->value,
                    InvoiceStates::WRITTEN_OFF->value
                ]);
            });

        return new self($query);
    }

    /**
     * @param array $sortBy
     * @return Builder|QueryBuilder
     */
    public function getQuery(array $sortBy = []): Builder|QueryBuilder
    {
        $instance = new InvoiceBalanceReportBuilder(
            DB::query()->fromSub($this->query, 'sub')
        );

        return $instance->sortBy($sortBy ?? [])->query;
    }
}
