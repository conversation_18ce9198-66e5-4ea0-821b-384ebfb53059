<?php

namespace App\Builders\Billing\Report;

use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class AgedInvoicesBuilder extends BaseInvoiceReportBuilder
{
    protected array $sortColumnsMap = [
        'company_id'   => Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID,
        'invoice_id'   => Invoice::TABLE . '.' . Invoice::FIELD_ID,
        '0_15'         => '0_15',
        '16_30'        => '16_30',
        '31_60'        => '31_60',
        '61_90'        => '61_90',
        '90_plus'      => '90_plus',
        'total_issued' => 'total_issued',
    ];

    /**
     * @param Builder $query
     */
    private function __construct(Builder $query)
    {
        parent::__construct($query);
    }

    /**
     * @param null $dateFrom
     * @param null $dateTo
     * @return self
     */
    public static function query($dateFrom = null, $dateTo = null): AgedInvoicesBuilder
    {
        $query = InvoiceSnapshot::mostRecentByInvoice([
            'date_from'           => $dateFrom,
            'date_to'             => $dateTo,
            'exclude_credit_paid' => true,
        ])
            ->hasRelevantOutstandingAmount()
            ->select([
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 0 AND 15 THEN invoice_snapshots.total_outstanding ELSE null END) AS `0_15`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 16 AND 30 THEN invoice_snapshots.total_outstanding ELSE null END) AS `16_30`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 31 AND 60 THEN invoice_snapshots.total_outstanding ELSE null END) AS `31_60`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') BETWEEN 61 AND 90 THEN invoice_snapshots.total_outstanding ELSE null END) AS `61_90`',),
                    DB::raw('SUM(CASE WHEN DATEDIFF(NOW(), ' . Invoice::TABLE . '.' . Invoice::FIELD_ISSUE_AT . ') > 90 THEN invoice_snapshots.total_outstanding ELSE null END) AS `90_plus`'),
                    DB::raw('SUM(' . InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ') as total_issued'),
                    InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID . ' as invoice_id',
                    Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                    Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                ]
            )
            ->join(Invoice::TABLE, Invoice::TABLE . '.' . Invoice::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID);

        $instance = new self($query);

        $instance->joinCompany();

        return $instance;
    }
}
