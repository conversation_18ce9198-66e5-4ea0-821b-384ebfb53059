<?php

namespace App\Builders\Billing\Report;

use App\Builders\Billing\BillingBuilder;
use App\Helpers\CarbonHelper;
use App\Models\Billing\Credit;
use App\Models\Billing\CreditBillingProfile;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use \Illuminate\Database\Query\Builder as QueryBuilder;

abstract class CreditMovementBaseBuilder extends BillingBuilder
{
    /**
     * @param Builder|QueryBuilder $builder
     */
    public function __construct(protected Builder|QueryBuilder $builder)
    {
        parent::__construct($this->builder);
    }

    /**
     * @return string
     */
    abstract protected function getCompanyIdColumn(): string;

    /**
     * @return string
     */
    abstract protected function getInvoiceIdColumn(): string;

    /**
     * @return string
     */
    abstract protected function getValueColumn(): string;

    /**
     * @return string
     */
    abstract protected function getCreditTypeColumn(): string;

    /**
     * @return string
     */
    abstract protected function getActionTypeColumn(): string;

    /**
     * @return string
     */
    abstract protected function getCreditExpiresAtColumn(): string;

    /**
     * @return void
     */
    public function addJoins(): void
    {

    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): self
    {
        if (filled($companyId)) {
            $this->builder->where(
                $this->getCompanyIdColumn(),
                $companyId
            );
        }

        return $this;
    }

    /**
     * @return string
     */
    abstract protected function getDateColumn(): string;

    /**
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return CreditMovementBaseBuilder
     */
    function forDateRange(?string $dateFrom = null, ?string $dateTo = null): self
    {
        if (filled($dateFrom)) {
            $this->builder->where($this->getDateFromColumn(), '>=', CarbonHelper::parseWithTimezone($dateFrom)->startOfDayUTC());
        }

        if (filled($dateTo)) {
            $this->builder->where($this->getDateColumn(), '<=', CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC());
        }

        return $this;
    }

    /**
     * @return string
     */
    abstract public function getCreditTypeSlugColumn(): string;

    /**
     * @param string|null $creditType
     * @return void
     */
    public function forCreditType(?string $creditType = null): void
    {
        if (filled($creditType)) {
            $this->builder->where($this->getCreditTypeSlugColumn(), $creditType);
        }
    }

    /**
     * @param string|null $type
     * @return void
     */
    public function forActionType(?string $type = null): void
    {
        if (filled($type)) {
            $this->builder->having('action_type', $type);
        }
    }

    /**
     * @param int|null $invoiceId
     * @return void
     */
    public function forInvoiceId(?int $invoiceId = null): void
    {
        if (filled($invoiceId)) {
            $this->builder->having('invoice_id', $invoiceId);
        }
    }

    /**
     * @return $this
     */
    public function joinCompany(): self
    {
        $this->safeJoin('company', fn() => $this->query->leftJoin(
            DatabaseHelperService::database() . '.' . Company::TABLE,
            Company::TABLE . '.' . Company::FIELD_ID,
            $this->getCompanyIdColumn(),
        ));

        return $this;
    }

    /**
     * @return Builder|QueryBuilder
     */
    public function getQuery(): Builder|QueryBuilder
    {
        $this->select([
            DB::raw($this->getCreditTypeColumn() . ' AS credit_type'),
            DB::raw($this->getDateColumn() . ' AS created_at'),
            DB::raw($this->getCompanyIdColumn() . ' AS company_id'),
            DB::raw($this->getInvoiceIdColumn() . ' AS invoice_id'),
            DB::raw($this->getValueColumn() . ' AS value'),
            DB::raw("'" . $this->getActionTypeColumn() . "'" . ' AS action_type'),
            DB::raw($this->getCreditExpiresAtColumn() . ' AS credit_expires_at'),
            DB::raw('GROUP_CONCAT(' . CreditBillingProfile::TABLE . '.' . CreditBillingProfile::FIELD_BILLING_PROFILE_ID . ') AS billing_profile_ids'),
        ]);

        $this->addJoins();

        $this->builder->leftJoin(
            CreditBillingProfile::TABLE,
            CreditBillingProfile::TABLE . '.' . CreditBillingProfile::FIELD_CREDIT_ID,
            Credit::TABLE . '.' . Credit::FIELD_ID
        );

        $this->builder->groupBy($this->getColumnToGroup());

        return $this->builder;
    }

    protected abstract function getColumnToGroup();

    /**
     * @param array $filters
     * @return CreditMovementBaseBuilder
     */
    public function applyFilters(array $filters = []): self
    {
        [
            'dates'          => $dates,
            'company_id'     => $companyId,
            'credit_type'    => $creditType,
            'invoice_id'     => $invoiceId,
            'action_type'    => $actionType,
            'filter_by_role' => $filterByRole,
        ] = $filters;

        $this->addJoins();

        $this->forDateRange(...$dates);
        $this->forCompanyId($companyId);
        $this->forActionType($actionType);
        $this->forCreditType($creditType);
        $this->forInvoiceId($invoiceId);
        $this->filterByRole($filterByRole);

        return $this;
    }
}
