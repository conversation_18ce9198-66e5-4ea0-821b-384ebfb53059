<?php

namespace App\Builders\Billing\Report;

use App\Builders\Billing\BillingBuilder;
use App\Models\Billing\Credit;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Builder;
use \Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Support\Arr;

class CreditOutstandingReport extends BillingBuilder
{
    /**
     * @var array|string[]
     */
    protected array $sortColumnsMap = [
        'company_id'      => Credit::FIELD_COMPANY_ID,
        'remaining_value' => Credit::FIELD_REMAINING_VALUE,
        'applied_at'      => Credit::FIELD_CREATED_AT,
        'expires_at'      => Credit::FIELD_EXPIRES_AT,
    ];

    /**
     * @param Builder|QueryBuilder $builder
     */
    public function __construct(protected Builder|QueryBuilder $builder)
    {
        parent::__construct($builder);
    }


    /**
     * @param string|null $creditType
     * @return $this
     */
    public function forCreditType(?string $creditType = null): self
    {
        if (filled($creditType)) {
            $this->builder->where(Credit::FIELD_CREDIT_TYPE, $creditType);
        }

        return $this;
    }

    /**
     * @param array $filters
     * @return $this
     */
    public function applyFilters(array $filters): self
    {
        $this->safeJoin('company', function () {
            $this->builder->join(
                Company::TABLE,
                Company::TABLE . '.' . Company::FIELD_ID,
                '=',
                Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID
            )->addSelect(Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name');
        });

        $this->forCompanyId(Arr::get($filters, 'company_id'));
        $this->forCreditType(Arr::get($filters, 'credit_type'));
        $this->filterByRole(Arr::get($filters, 'filter_by_role'));

        return $this;
    }

    /**
     * @return BillingBuilder
     */
    public static function query(): BillingBuilder
    {
        $base = Credit::query()
            ->select([
                Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID,
                Credit::TABLE . '.' . Credit::FIELD_ID,
                Credit::TABLE . '.' . Credit::FIELD_CREATED_AT,
                Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT,
                Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE,
                Credit::TABLE . '.' . Credit::FIELD_REMAINING_VALUE
            ])
            ->with(Credit::RELATION_BILLING_PROFILES)
            ->groupBy(Credit::TABLE . '.' . Credit::FIELD_ID)
            ->where(Credit::TABLE . '.' . Credit::FIELD_REMAINING_VALUE, '>', 0);

        return new self($base);
    }
}
