<?php

namespace App\Builders\Billing\Report;

use App\Builders\Billing\BillingBuilder;
use App\Enums\Billing\Reports\CreditMovementType;
use App\Models\Billing\Credit;
use Illuminate\Database\Eloquent\Builder;
use \Illuminate\Database\Query\Builder as QueryBuilder;

class CompanyCreditBuilder extends CreditMovementBaseBuilder
{
    protected array $sortColumnsMap = [
        'remaining_value' => Credit::FIELD_REMAINING_VALUE
    ];

    public function __construct(protected Builder|QueryBuilder $builder)
    {
        parent::__construct($builder);
    }

    /**
     * @return BillingBuilder
     */
    public static function query(): BillingBuilder
    {
        return new self(Credit::query());
    }

    /**
     * @return string
     */
    protected function getDateFromColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_CREATED_AT;
    }

    /**
     * @return string
     */
    public function getCreditTypeSlugColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE;
    }

    /**
     * @return string
     */
    protected function getCompanyIdColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID;
    }

    /**
     * @return string
     */
    protected function getInvoiceIdColumn(): string
    {
        return 'null';
    }

    /**
     * @return string
     */
    protected function getValueColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_INITIAL_VALUE;
    }

    /**
     * @return string
     */
    protected function getCreditTypeColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE;
    }

    /**
     * @return string
     */
    protected function getActionTypeColumn(): string
    {
        return CreditMovementType::ADDED_TO_COMPANY->value;
    }

    /**
     * @return string
     */
    protected function getDateColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_CREATED_AT;
    }

    /**
     * @return string
     */
    protected function getCreditExpiresAtColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT;
    }

    /**
     * @return string
     */
    protected function getColumnToGroup(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_ID;
    }
}
