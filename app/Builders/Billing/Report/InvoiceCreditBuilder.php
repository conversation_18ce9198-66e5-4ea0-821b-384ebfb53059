<?php

namespace App\Builders\Billing\Report;

use App\Builders\Billing\BillingBuilder;
use App\Enums\Billing\Reports\CreditMovementType;
use App\Models\Billing\Credit;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceCredit;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;

class InvoiceCreditBuilder extends CreditMovementBaseBuilder
{
    public function __construct(protected Builder|QueryBuilder $builder)
    {
        parent::__construct($builder);
    }

    /**
     * @return void
     */
    public function addJoins(): void
    {
        $this->safeJoin(Credit::TABLE, fn() => $this->builder->join(Credit::TABLE, InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_CREDIT_ID, '=', Credit::TABLE . '.' . Credit::FIELD_ID));
        $this->safeJoin(Invoice::TABLE, fn() => $this->builder->join(Invoice::TABLE, InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_INVOICE_ID, '=', Invoice::TABLE . '.' . Invoice::FIELD_ID));
    }

    /**
     * @return string
     */
    protected function getDateFromColumn(): string
    {
        return InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_CREATED_AT;
    }

    /**
     * @return string
     */
    public function getCreditTypeSlugColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE;
    }

    /**
     * @return string
     */
    protected function getCompanyIdColumn(): string
    {
        return Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID;
    }

    /**
     * @return string
     */
    protected function getInvoiceIdColumn(): string
    {
        return InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_INVOICE_ID;
    }

    /**
     * @return string
     */
    protected function getValueColumn(): string
    {
        return InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_AMOUNT_APPLIED;
    }

    /**
     * @return string
     */
    protected function getCreditTypeColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE;
    }

    /**
     * @return string
     */
    protected function getActionTypeColumn(): string
    {
        return CreditMovementType::APPLIED_TO_INVOICE->value;
    }

    /**
     * @return string
     */
    protected function getCreditExpiresAtColumn(): string
    {
        return Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT;
    }

    /**
     * @return string
     */
    protected function getDateColumn(): string
    {
        return InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_CREATED_AT;
    }

    public static function query(): BillingBuilder
    {
        return new self(InvoiceCredit::query());
    }

    /**
     * @return string
     */
    protected function getColumnToGroup(): string
    {
        return InvoiceCredit::TABLE . '.' . InvoiceCredit::FIELD_ID;
    }
}
