<?php

namespace App\Builders\Billing\Report;

use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Billing\InvoiceTransaction;
use App\Models\Odin\Company;
use Illuminate\Support\Facades\DB;

class RevenueReportBuilder extends BaseInvoiceReportBuilder
{
    const string QUERY_ID = 'query';

    public array $sortColumnsMap = [
        'company_id' => self::QUERY_ID . '.' . Invoice::FIELD_COMPANY_ID,
        'invoice_id' => self::QUERY_ID . '.' . 'invoice_id',
        'created_at' => self::QUERY_ID . '.' . InvoiceTransaction::FIELD_DATE,
        'date'       => self::QUERY_ID . '.' . InvoiceTransaction::FIELD_DATE,
        'type'       => self::QUERY_ID . '.' . InvoiceTransaction::FIELD_TYPE,
        'amount'     => self::QUERY_ID . '.' . InvoiceTransaction::FIELD_AMOUNT,
        'status'     => self::QUERY_ID . '.' . 'invoice_status',
    ];

    /**
     * @return self
     */
    public static function query(): RevenueReportBuilder
    {
        $firstQuery = DB::table(InvoiceTransaction::TABLE)
            ->select([
                Invoice::TABLE . '.' . Invoice::FIELD_ID . ' as invoice_id',
                InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_DATE . ' as date',
                InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_AMOUNT . ' as amount',
                Invoice::TABLE . '.' . Invoice::FIELD_STATUS . ' as invoice_status',
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_SCOPE . ' as scope',
                InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_TYPE . ' as type',
                InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_SCENARIO . ' as scenario',
            ])
            ->join(Invoice::TABLE, Invoice::TABLE . '.' . Invoice::FIELD_UUID, InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_INVOICE_UUID)
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, Invoice::TABLE . '.' . Invoice::FIELD_COMPANY_ID)
            ->whereNull(InvoiceTransaction::TABLE . '.' . InvoiceTransaction::FIELD_DELETED_AT);

        $secondQuery = DB::table(InvoiceSnapshot::TABLE)
            ->select([
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID . ' as invoice_id',
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_CREATED_AT . ' as date',
                InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING . ' as amount',
                Invoice::TABLE . '.' . Invoice::FIELD_STATUS . ' as invoice_status',
                Company::TABLE . '.' . Company::FIELD_ID . ' as company_id',
                Company::TABLE . '.' . Company::FIELD_NAME . ' as company_name',
                DB::raw('null as scope'),
                Invoice::TABLE . '.' . Invoice::FIELD_STATUS . ' as type',
                DB::raw('null as scenario'),
            ])
            ->join(Invoice::TABLE, Invoice::TABLE . '.' . Invoice::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID)
            ->join(Company::TABLE, Company::TABLE . '.' . Company::FIELD_ID, InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID)
            ->whereRaw(InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_STATUS . ' IN ("deleted", "voided")');

        $query = $firstQuery->union($secondQuery);

        return new self(DB::table(DB::raw("({$query->toSql()}) as " . self::QUERY_ID))->mergeBindings($query));
    }

    /**
     * @param string|null $dateFrom
     * @param string|null $dateTo
     * @return $this
     */
    public function forDateRange(?string $dateFrom = null, ?string $dateTo = null): RevenueReportBuilder
    {
        $this->query->whereBetween(
            self::QUERY_ID . '.' . InvoiceTransaction::FIELD_DATE, [
            CarbonHelper::parseWithTimezone($dateFrom)->startOfDayUTC(),
            CarbonHelper::parseWithTimezone($dateTo)->endOfDayUTC(),
        ]);

        return $this;
    }

    /**
     * @param int|null $companyId
     * @return $this
     */
    public function forCompanyId(?int $companyId = null): RevenueReportBuilder
    {
        if (filled($companyId)) {
            $this->query->where(self::QUERY_ID . '.' . Invoice::FIELD_COMPANY_ID, $companyId);
        }

        return $this;

    }

    /**
     * @param array|null $scenarios
     * @return $this
     */
    public function forScenarios(?array $scenarios = null): RevenueReportBuilder
    {
        if (filled($scenarios)) {
            $this->query->whereIn(self::QUERY_ID . '.' . InvoiceTransaction::FIELD_SCENARIO, $scenarios);
        }

        return $this;
    }


    /**
     * @param array|null $types
     * @return $this
     */
    public function forTypes(?array $types = null): RevenueReportBuilder
    {
        if (filled($types)) {
            $this->query->whereIn(self::QUERY_ID . '.' . InvoiceTransaction::FIELD_TYPE, $types);
        }

        return $this;
    }
}
