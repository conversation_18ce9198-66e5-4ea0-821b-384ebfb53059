<?php

namespace App\Builders\Pricing;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\ServiceProduct;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class CustomFloorPricingBuilder extends BasePricingBuilder
{
    const string ALIAS_CUSTOM_STATE_FLOOR_PRICE  = 'custom_state_floor_price';
    const string ALIAS_CUSTOM_COUNTY_FLOOR_PRICE = 'custom_county_floor_price';

    protected int $companyCampaignId;
    protected bool $customFloorPricesOnly = false;

    /**
     * @return self
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * This also sets the module_id and service_product_id, as they may be used in joins by the base builder
     *
     * @param int $companyCampaignId
     * @return $this
     */
    public function forCompanyCampaign(int $companyCampaignId): self
    {
        $this->companyCampaignId = $companyCampaignId;

        /** @var CompanyCampaign $campaign */
        $campaign = CompanyCampaign::query()
            ->findOrFail($companyCampaignId);
        $this->serviceProductId = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $campaign->service_id)
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $campaign->product_id)
            ->first()
            ->id;
        $this->biddingModuleId = $campaign->bidPriceModule->id;
        $this->campaignType = $campaign->type;

        return $this;
    }

    /**
     * Only return rows with custom pricing
     * @param bool|null $customPricesOnly
     * @return self
     */
    public function customPricesOnly(?bool $customPricesOnly = true): self
    {
        $this->customFloorPricesOnly = $customPricesOnly;

        return $this;
    }

    /**
     * @inheritDoc
     */
    protected function additionalQueryStatements(Builder &$query): void
    {
        if ($this->companyCampaignId) {
            $this->joinCustomCampaignStateFloors($query);
            if ($this->withCounties || $this->countyLocationIds)
                $this->joinCustomCampaignCountyFloors($query);
        }

        if ($this->aliasPriceColumns)
            $this->addCustomCampaignAliases($query);
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinCustomCampaignStateFloors(Builder &$query): void
    {
        if ($this->customFloorPricesOnly)
            $query->join(CustomCampaignStateFloorPrice::TABLE, $this->stateFloorJoinClause(...));
        else
            $query->leftJoin(CustomCampaignStateFloorPrice::TABLE, $this->stateFloorJoinClause(...));
    }

    /**
     * @param JoinClause $join
     * @return JoinClause
     */
    private function stateFloorJoinClause(JoinClause $join): JoinClause
    {
        return $join->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID)
            ->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_QUALITY_TIER_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID)
            ->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID)
            ->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID)
            ->when($this->companyCampaignId, fn(JoinClause $join) =>
                $join->where(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $this->companyCampaignId)
            );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinCustomCampaignCountyFloors(Builder &$query): void
    {
        if ($this->customFloorPricesOnly)
            $query->join(CustomCampaignCountyFloorPrice::TABLE, $this->countyFloorJoinClause(...));
        else
            $query->leftJoin(CustomCampaignCountyFloorPrice::TABLE, $this->countyFloorJoinClause(...));
    }

    /**
     * @param JoinClause $join
     * @return JoinClause
     */
    private function countyFloorJoinClause(JoinClause $join): JoinClause
    {
        return $join->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_STATE_LOCATION_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID)
            ->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_QUALITY_TIER_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID)
            ->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID)
            ->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID)
            ->when($this->companyCampaignId, fn(JoinClause $join) =>
                $join->where(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $this->companyCampaignId)
            )->when($this->countyLocationIds, fn(JoinClause $query) =>
                $query->whereIn(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $this->countyLocationIds)
            );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function addCustomCampaignAliases(Builder &$query): void
    {
        $selects = [CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE . ' as ' . self::ALIAS_CUSTOM_STATE_FLOOR_PRICE];
        if ($this->withCounties || $this->countyLocationIds)
            $selects[] = CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE . ' as ' . self::ALIAS_CUSTOM_COUNTY_FLOOR_PRICE;

        $query->addSelect($selects);
    }
}
