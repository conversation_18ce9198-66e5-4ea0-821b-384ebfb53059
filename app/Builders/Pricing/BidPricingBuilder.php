<?php

namespace App\Builders\Pricing;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

/**
 * This builder is focused on bids in specific campaigns, rather than general pricing
 */
class BidPricingBuilder
{
    public function __construct(
        protected CompanyCampaign $companyCampaign,
        protected ?bool           $stateBids = true,
        protected ?bool           $countyBids = null,
        protected ?bool           $joinStatePrices = null,
        protected ?int            $withFloorPrices = null,
        protected ?bool           $aliasPriceColumns = null,
        protected ?int            $stateLocationId = null,
    ) {}

    /**
     * @param CompanyCampaign $companyCampaign
     * @return self
     */
    public static function query(CompanyCampaign $companyCampaign): self
    {
        return new self($companyCampaign);
    }

    /**
     * @param bool|null $forStateBids
     * @return $this
     */
    public function forStateBids(?bool $forStateBids = true): self
    {
        $this->stateBids = $forStateBids;

        return $this;
    }

    /**
     * @param bool|null $forCountyBids
     * @return $this
     */
    public function forCountyBids(?bool $forCountyBids = true): self
    {
        $this->countyBids = $forCountyBids;
        $this->stateBids = false;

        return $this;
    }

    /**
     * @param bool|null $joinBids
     * @return $this
     */
    public function withStatePrices(?bool $joinBids = true): self
    {
        $this->joinStatePrices = $joinBids;

        return $this;
    }

    /**
     * @param bool|null $withFloorPrices
     * @return $this
     */
    public function withFloorPrices(?bool $withFloorPrices = true): self
    {
        $this->withFloorPrices = $withFloorPrices;

        return $this;
    }

    /**
     * @param int|null $stateLocationId
     * @return $this
     */
    public function forStateLocation(?int $stateLocationId = null): self
    {
        $this->stateLocationId = $stateLocationId;

        return $this;
    }

    /**
     * @return $this
     */
    public function aliasPriceColumns(): self
    {
        $this->aliasPriceColumns = true;

        return $this;
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = $this->countyBids
            ? $this->companyCampaign->bidPriceModule->countyBids()->getQuery()
            : $this->companyCampaign->bidPriceModule->stateBids()->getQuery();

        if ($this->countyBids && $this->joinStatePrices)
            $this->joinStateBidPrices($query);

        if ($this->withFloorPrices)
            $this->joinFloorPrices($query);

        if ($this->stateLocationId)
            $this->constrainByStateLocation($query);

        if ($this->aliasPriceColumns)
            $this->selectAliasPriceColumns($query);

        return $query;
    }

    /**
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()
            ->get();
    }

    /**
     * @param $query
     * @return void
     */
    protected function constrainByStateLocation(&$query): void
    {
        $targetTable = $this->countyBids
            ? ProductCountyBidPrice::TABLE
            : ProductStateBidPrice::TABLE;

        $query->where($targetTable .'.'. ProductStateBidPrice::FIELD_STATE_LOCATION_ID, $this->stateLocationId);
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function selectAliasPriceColumns(Builder $query): void
    {
        $selects = [];
        if ($this->stateBids || $this->joinStatePrices) $selects[] = ProductStateBidPrice::TABLE . '.' . ProductStateBidPrice::FIELD_PRICE . ' as ' . BasePricingBuilder::ALIAS_STATE_BID_PRICE;
        if ($this->countyBids) $selects[] = ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_PRICE . ' as ' . BasePricingBuilder::ALIAS_COUNTY_BID_PRICE;
        if ($this->withFloorPrices) {
            if ($this->stateBids || $this->joinStatePrices) $selects[] = ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PRICE . ' as ' . BasePricingBuilder::ALIAS_STATE_FLOOR_PRICE;
            if ($this->countyBids) $selects[] = ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_PRICE . ' as ' . BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE;
            if ($this->companyCampaign->uses_custom_floor_prices) {
                if ($this->stateBids || $this->joinStatePrices) $selects[] = CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE .' as '. CustomFloorPricingBuilder::ALIAS_CUSTOM_STATE_FLOOR_PRICE;
                if ($this->countyBids) $selects[] = CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE .' as '. CustomFloorPricingBuilder::ALIAS_CUSTOM_COUNTY_FLOOR_PRICE;
            }
        }

        $query->addSelect($selects);
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinStateBidPrices(Builder &$query): void
    {
        $query->leftJoin(ProductStateBidPrice::TABLE, fn(JoinClause $join) =>
            $join->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_STATE_LOCATION_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_STATE_LOCATION_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_MODULE_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_MODULE_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_QUALITY_TIER_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_QUALITY_TIER_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_SALE_TYPE_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_SALE_TYPE_ID)
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinFloorPrices(Builder &$query): void
    {
        $customFloorPrices = $this->companyCampaign->uses_custom_floor_prices;

        if ($this->countyBids) {
            $this->joinStandardCountyFloorPrices($query);
            if ($customFloorPrices)
                $this->joinCustomCountyFloorPrices($query);
            if ($this->joinStatePrices) {
                $this->joinStandardStateFloorPrices($query);
                if ($customFloorPrices)
                    $this->joinCustomStateFloorPrices($query);
            }
        }
        else {
            $this->joinStandardStateFloorPrices($query);
            if ($customFloorPrices)
                $this->joinCustomStateFloorPrices($query);
        }
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinStandardStateFloorPrices(Builder &$query): void
    {
        $toTable = $this->countyBids
            ? ProductCountyBidPrice::TABLE
            : ProductStateBidPrice::TABLE;
        $query->leftJoin(ProductStateFloorPrice::TABLE, fn(JoinClause $join) =>
            $join->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, '=', $toTable .'.'. ProductCountyBidPrice::FIELD_STATE_LOCATION_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, '=', $toTable .'.'. ProductCountyBidPrice::FIELD_QUALITY_TIER_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', $toTable .'.'. ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID, '=', $toTable .'.'. ProductCountyBidPrice::FIELD_SALE_TYPE_ID)
                ->on(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, '=', $toTable .'.'. ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID)
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinStandardCountyFloorPrices(Builder &$query): void
    {
        $query->leftJoin(ProductCountyFloorPrice::TABLE, fn(JoinClause $join) =>
            $join->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_QUALITY_TIER_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_SALE_TYPE_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID)
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinCustomStateFloorPrices(Builder &$query): void
    {
        $toTable = $this->countyBids
            ? ProductCountyBidPrice::TABLE
            : ProductStateBidPrice::TABLE;

        $query->leftJoin(CustomCampaignStateFloorPrice::TABLE, fn(JoinClause $join) =>
            $join->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_STATE_LOCATION_ID, '=', $toTable .'.'. ProductStateBidPrice::FIELD_STATE_LOCATION_ID)
                ->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_QUALITY_TIER_ID, '=', $toTable .'.'. ProductStateBidPrice::FIELD_QUALITY_TIER_ID)
                ->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', $toTable .'.'. ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID)
                ->on(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_SALE_TYPE_ID, '=', $toTable .'.'. ProductStateBidPrice::FIELD_SALE_TYPE_ID)
                ->where(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $this->companyCampaign->id)
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinCustomCountyFloorPrices(Builder &$query): void
    {
        $query->leftJoin(CustomCampaignCountyFloorPrice::TABLE, fn(JoinClause $join) =>
        $join->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID)
            ->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_QUALITY_TIER_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_QUALITY_TIER_ID)
            ->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID)
            ->on(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_SALE_TYPE_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_SALE_TYPE_ID)
            ->where(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_COMPANY_CAMPAIGN_ID, $this->companyCampaign->id)
        );
    }
}
