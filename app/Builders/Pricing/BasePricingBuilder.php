<?php

namespace App\Builders\Pricing;

use App\Enums\Campaigns\CampaignType;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes as SaleTypesEnum;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;

/**
 * Base class for pricing queries. With the updated CompanyCampaign pricing logic, ProductStateFloorPrice
 *  is the only price which *must* exist for a given product/sale/quality/property scope, so it forms
 *  the base of the query.
 *
 * Joins are kept in separate methods, so extended classes can override them if special
 *  join constraints are required
 *
 * Note that the composite index on the floor prices tables starts with service_product_id
 * Queries that use joins but don't supply a service product id may be slow as the index will not be used
 */
class BasePricingBuilder
{
    const string ALIAS_STATE_FLOOR_PRICE  = 'state_floor_price';
    const string ALIAS_COUNTY_FLOOR_PRICE = 'county_floor_price';
    const string ALIAS_STATE_BID_PRICE    = 'state_bid_price';
    const string ALIAS_COUNTY_BID_PRICE   = 'county_bid_price';

    public function __construct(
        protected ?int   $serviceProductId = null,
        protected ?int   $biddingModuleId = null,
        protected ?array $stateLocationIds = null,
        protected ?array $countyLocationIds = null,
        protected ?array $propertyTypeIds = null,
        protected ?array $saleTypeIds = null,
        protected ?array $qualityTierIds = null,
        protected ?CampaignType $campaignType = null,
        protected ?bool  $autoScoping = true,
        protected ?bool  $withCounties = false,
        protected ?bool  $innerCounties = false,
        protected ?bool  $withBids = false,
        protected ?bool  $aliasPriceColumns = false,
        protected ?array $selectColumns = ['*'],
    ) {}

    /**
     * @return $this
     */
    public function withBids(): self
    {
        $this->withBids = true;

        return $this;
    }

    /**
     * @return $this
     */
    public function withCounties(?bool $innerJoin = false): self
    {
        $this->withCounties = true;
        $this->innerCounties = $innerJoin;

        return $this;
    }

    public function select(string|array $columns = ['*']): self
    {
        if (gettype($columns) === 'array')
            $this->selectColumns = $columns;
        else if (gettype($columns) === 'string')
            $this->selectColumns = [$columns];

        return $this;
    }

    /**
     * @param int $stateLocationId
     * @return $this
     */
    public function forState(int $stateLocationId): self
    {
        $this->stateLocationIds = [$stateLocationId];

        return $this;
    }

    /**
     * @param int[] $stateLocationIds
     * @return $this
     */
    public function forStates(array $stateLocationIds): self
    {
        $this->stateLocationIds = $stateLocationIds;

        return $this;
    }

    /**
     * Note that county constraint will only function if ->withCounties() is used
     * @param int $countyLocationId
     * @return $this
     */
    public function forCounty(int $countyLocationId): self
    {
        $this->countyLocationIds = [$countyLocationId];

        return $this;
    }

    /**
     * @param int[] $countyLocationIds
     * @return $this
     */
    public function forCounties(array $countyLocationIds): self
    {
        $this->countyLocationIds = $countyLocationIds;

        return $this;
    }

    /**
     * @param int $serviceProductId
     * @return $this
     */
    public function forServiceProduct(int $serviceProductId): self
    {
        $this->serviceProductId = $serviceProductId;

        return $this;
    }

    /**
     * @param int $saleTypeId
     * @return $this
     */
    public function forSaleType(int $saleTypeId): self
    {
        $this->saleTypeIds = [$saleTypeId];

        return $this;
    }

    /**
     * @param int[] $saleTypeIds
     * @return $this
     */
    public function forSaleTypes(array $saleTypeIds): self
    {
        $this->saleTypeIds = $saleTypeIds;

        return $this;
    }

    /**
     * @param int $qualityTierId
     * @return self
     */
    public function forQualityTier(int $qualityTierId): self
    {
        $this->qualityTierIds = [$qualityTierId];

        return $this;
    }

    /**
     * @param int[] $qualityTierIds
     * @return $this
     */
    public function forQualityTiers(array $qualityTierIds): self
    {
        $this->qualityTierIds = $qualityTierIds;

        return $this;
    }

    /**
     * @param int $propertyTypeId
     * @return $this
     */
    public function forPropertyType(int $propertyTypeId): self
    {
        $this->propertyTypeIds = [$propertyTypeId];

        return $this;
    }

    /**
     * @param array $propertyTypeIds
     * @return $this
     */
    public function forPropertyTypes(array $propertyTypeIds): self
    {
        $this->propertyTypeIds = $propertyTypeIds;

        return $this;
    }

    /**
     * @param CampaignType|null $campaignType
     * @return $this
     */
    public function forCampaignType(?CampaignType $campaignType): self
    {
        $this->campaignType = $campaignType;

        return $this;
    }

    /**
     * Disable automatic scoping of QualityTier and SalesType by service product
     * Note that auto-scoping will be overridden by supplied quality_tier or sales_type ids
     * Auto-scoping also requires a serviceProductId
     *
     * @return $this
     */
    public function disableAutoScoping(): self
    {
        $this->autoScoping = false;

        return $this;
    }

    public function aliasPriceColumns(): self
    {
        $this->aliasPriceColumns = true;

        return $this;
    }

    /**
     * @param int|null $biddingModuleId
     * @return $this
     */
    public function forBiddingModule(?int $biddingModuleId = null): self
    {
        $this->biddingModuleId = $biddingModuleId;

        return $this;
    }

    /**
     * @return self
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = ProductStateFloorPrice::query()
            ->select($this->selectColumns);

        if ($this->withCounties)
            $this->joinCountyFloors($query);

        if ($this->withBids)
            $this->joinStateBids($query);

        if ($this->withCounties && $this->withBids)
            $this->joinCountyBids($query);

        if ($this->aliasPriceColumns)
            $this->selectAliasedPriceColumns($query);

        if ($this->autoScoping && $this->serviceProductId)
            $this->autoConstrainSaleTypeAndQualityTier();

        if ($this->stateLocationIds)
            $query->whereIn(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $this->stateLocationIds);

        if ($this->countyLocationIds && $this->withCounties) {
            $query->where(fn(Builder $query) =>
                $query->whereIn(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $this->countyLocationIds)
                    ->orWhereNull(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID)
            );
        }

        if ($this->serviceProductId)
            $query->where(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $this->serviceProductId);

        if ($this->propertyTypeIds)
            $query->whereIn(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $this->propertyTypeIds);

        if ($this->qualityTierIds)
            $query->whereIn(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $this->qualityTierIds);

        if ($this->saleTypeIds)
            $query->whereIn(ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $this->saleTypeIds);

        $this->additionalQueryStatements($query);

        return $query;
    }

    /**
     * This is for or extending the query in child classes without needing to rewrite the whole
     *  of getQuery() if overriding isn't needed
     *
     * @param Builder $query
     */
    protected function additionalQueryStatements(Builder &$query): void
    {
    }

    /**
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()
            ->get();
    }

    /**
     * Constrain the query by SaleType and QualityTier as dictated by respective enums
     *
     * @return void
     */
    public function autoConstrainSaleTypeAndQualityTier(): void
    {
        if (!$this->saleTypeIds) {
            $this->saleTypeIds = $this->campaignType
                ? SaleTypesEnum::byCompanyCampaignType($this->campaignType, SaleTypesEnum::RETURN_TYPE_ID)
                : SaleTypesEnum::byServiceProductId($this->serviceProductId, SaleTypesEnum::RETURN_TYPE_ID);
        }
        if (!$this->qualityTierIds) {
            $this->qualityTierIds = QualityTierEnum::byServiceProductId($this->serviceProductId, QualityTierEnum::RETURN_TYPE_ID);
        }
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinCountyFloors(Builder &$query): void
    {
        $query->leftJoin(ProductCountyFloorPrice::TABLE, fn(JoinClause $join) =>
            $join->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID)
                ->on(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID)
                ->when($this->countyLocationIds, fn(JoinClause $query) =>
                    $query->whereIn(ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $this->countyLocationIds)
            ));
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinStateBids(Builder &$query): void
    {
        $query->leftJoin(ProductStateBidPrice::TABLE, fn(JoinClause $join) =>
            $join->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_STATE_LOCATION_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_STATE_LOCATION_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_QUALITY_TIER_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_QUALITY_TIER_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_SALE_TYPE_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SALE_TYPE_ID)
                ->on(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID, '=', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID)
                ->where(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_MODULE_ID, $this->biddingModuleId ?? -1)
        );
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function joinCountyBids(Builder &$query): void
    {
        $joinClause = fn(JoinClause $join) => $join->on(ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_STATE_LOCATION_ID, '=', ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_STATE_LOCATION_ID)
            ->on(ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_QUALITY_TIER_ID, '=', ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_QUALITY_TIER_ID)
            ->on(ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID, '=', ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID)
            ->on(ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_SALE_TYPE_ID, '=', ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SALE_TYPE_ID)
            ->on(ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID, '=', ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID)
            ->when($this->countyLocationIds, fn(JoinClause $query) =>
                $query->whereIn(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, $this->countyLocationIds)
            )->where(ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_MODULE_ID, $this->biddingModuleId ?? -1);

        if ($this->innerCounties)
            $query->join(ProductCountyBidPrice::TABLE, $joinClause);
        else
            $query->leftJoin(ProductCountyBidPrice::TABLE, $joinClause);
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function selectAliasedPriceColumns(Builder &$query): void
    {
        $selects = [ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PRICE . ' as ' . self::ALIAS_STATE_FLOOR_PRICE];
        if ($this->withCounties) $selects[] = ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_PRICE . ' as ' . self::ALIAS_COUNTY_FLOOR_PRICE;
        if ($this->withBids) $selects[] = ProductStateBidPrice::TABLE . '.' . ProductStateBidPrice::FIELD_PRICE . ' as ' . self::ALIAS_STATE_BID_PRICE;
        if ($this->withCounties && $this->withBids) $selects[] = ProductCountyBidPrice::TABLE . '.' . ProductCountyBidPrice::FIELD_PRICE . ' as ' . self::ALIAS_COUNTY_BID_PRICE;

        $query->addSelect($selects);
    }
}
