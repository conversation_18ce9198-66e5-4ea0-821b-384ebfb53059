<?php

namespace App\Transformers\Advertising;

use App\Http\Controllers\API\Advertising\AdvertisingAPIController;
use App\Models\AdvertisingCampaign;
use Exception;
use Illuminate\Support\Collection;

class AdvertisingCampaignsTransformer
{
    /**
     * Handles transforming a collection of advertising campaigns to their client side representation.
     *
     * @param Collection $campaigns
     * @return array
     */
    public function transformCampaigns(Collection $campaigns): array
    {
        return $campaigns->map(function ($campaign) {
            return $this->transformCampaign($campaign);
        })->values()->toArray();
    }

    /**
     * Handles transforming a single advertising campaign to its client side representation.
     *
     * @param $campaign
     * @return array
     * @throws Exception
     */
    public function transformCampaign($campaign): array
    {
        $secondsInUnit = match($campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT]) {
            AdvertisingCampaign::RUN_INTERVAL_WEEKS => 604800,
            AdvertisingCampaign::RUN_INTERVAL_HOURS => 3600,
            AdvertisingCampaign::RUN_INTERVAL_MINUTES => 60,
            default => throw new Exception("Invalid run interval unit: ".$campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT])
        };

        return [
            AdvertisingCampaign::FIELD_PLATFORM => $campaign[AdvertisingCampaign::FIELD_PLATFORM],
            AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID => (string) $campaign[AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID],
            AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID => (string) $campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID],
            'name' => $campaign['name'],
            'status' => $campaign['status'],
            'campaign_budget_amount' => $campaign['campaign_budget_amount'] ?? "Unknown",
            AdvertisingAPIController::REQUEST_LOCATIONS => $campaign['locations'],
            AdvertisingAPIController::REQUEST_AUTOMATED => $campaign['automated'],
            AdvertisingAPIController::REQUEST_RUN_INTERVAL => (int) $campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS] / $secondsInUnit,
            AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT => $campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT],
            AdvertisingAPIController::REQUEST_AUTOMATION_PARAMETERS => $campaign['automation_parameters']
        ];
    }
}
