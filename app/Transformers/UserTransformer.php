<?php

namespace App\Transformers;

use App\Models\User;
use Illuminate\Support\Collection;

class UserTransformer
{
    /**
     * @param User $user
     *
     * @return array
     */
    public function transform(User $user): array
    {
        return [
            'id' => $user->id,
            'name' => $user->name,
            'email' => $user->email
        ];
    }

    /**
     * @param Collection $users
     * @return array
     */
    public function transformUsers(Collection $users): array
    {
        if(empty($users)) return [];

        return $users->map(function($user) {
            return $this->transform($user);
        })->values()->toArray();
    }
}
