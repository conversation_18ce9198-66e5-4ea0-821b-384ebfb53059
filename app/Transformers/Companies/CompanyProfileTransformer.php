<?php

namespace App\Transformers\Companies;

use App\Models\Legacy\EloquentArbSubscription;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyAddress;
use App\Models\Legacy\EloquentCompanyCRMAction;
use App\Models\Legacy\EloquentCompanyOption;
use App\Models\Legacy\EloquentCompanyPaymentProfile;
use App\Models\Legacy\EloquentConfiguration;
use App\Models\Legacy\EloquentHistoryLog;
use App\Models\Legacy\EloquentOption;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\DashboardShadowRepository;
use App\Repositories\Legacy\CompanyCRMRepository;
use App\Repositories\Legacy\ConfigurationRepository;
use App\Repositories\Legacy\ReviewRepository;
use App\Repositories\RevenueRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class CompanyProfileTransformer
{
    public function __construct(
        protected ConfigurationRepository $configurationRepository,
        protected RevenueRepository $revenueRepository,
        protected ReviewRepository $reviewRepository,
        protected CompanyCRMRepository $CRMRepository,
        protected ComputedRejectionStatisticRepository $computedRejectionStatisticRepository,
        protected DashboardShadowRepository $shadowRepository
    ) {}

    /**
     * @param EloquentCompany $company
     * @return array
     */
    public function transformCompanyProfileOverview(EloquentCompany $company): array
    {
        return [
            'id' => $company->companyid,
            'name' => $company->companyname,
            'website' => $company->website,
            'phone' => $company->defaultAddress?->address?->phone,
            'image' => $company->logoUrl(),
            'type' => $company->type,
            'active' => $company->status === EloquentCompany::STATUS_ACTIVE,
            'status' => $company->status,
            'ranking' => 'Excellent', //todo
            'prescreened' => $company->isPrescreeed(),
            'lead_rejection_percentage' => number_format($this->computedRejectionStatisticRepository->getCompanyLeadRejectionPercentage($company->miCompany), 1) . '%',
            'appointment_rejection_percentage' => '0%', // todo: Remove from frontend
            'mainOfficeLocation' => $company->defaultAddress?->address?->getFullAddress() ?? $company->addresses()->first()?->address?->getFullAddress() ?? 'No Default Address',
            'employees' => $company->employee_count,
            'revenue' => $company->revenue_in_thousands,
            'shadow_token' => $this->shadowRepository->getShadowToken($company),
            'shadow_url' => $this->shadowRepository->getShadowUrl($company),
            'account_manager' => [
                "id" => $company->miCompany?->accountManager?->id,
                "name" => $company->miCompany?->accountManager?->name ?? 'None Assigned',
            ],
            'success_manager' => [
                "id"   => $company->successManagerClient()?->success_manager_id,
                "name" => $company->successManagerClient()?->successManager?->user?->name ?? "None Assigned"
            ]
          ];
    }

    /**
     * @param EloquentCompany $company
     *
     * @return array
     */
    public function transformCompanyProfile(EloquentCompany $company): array
    {
        return [
            'id' => $company->companyid,
            'status' => $company->status,
            'tradingName' => $company->companyname,
            'entityName' => $company->companylegalentityname,
            'type' => $company->type,
            'dateRegistered'=> $company->timestampadded,
            'buyingLeads' => $this->transformLeadBuyingStatus($company->buyingsolarestimateleads),
            'familyBusiness' => $company->familybusiness ? 'Yes' : 'No',
            'yearStartedSolar' => $company->yearstartedsolar,
            'addresses' => $this->transformCompanyAddress($company),
            'phoneRequiredForReview' => $company->is_phone_required_for_review ? 'Yes' : 'No',
            'autoBilling' => $company->autobilling ? 'Yes' : 'No',
            'licenses' => $company->companyLicenses,
            'profilePageLink' => $company->companyUrl(),
            'website' => $company->website,
            'websiteLinkActive' => $this->configurationRepository->getConfigurationValueByName(EloquentConfiguration::NAME_WEBSITE_ACTIVE, $company->companyid, EloquentConfiguration::REL_TYPE_COMPANY_ID)?->value ? 'Yes' : 'No',
            'phoneActive' => $this->configurationRepository->getConfigurationValueByName(EloquentConfiguration::NAME_PHONE_NUMBER_ACTIVE, $company->companyid, EloquentConfiguration::REL_TYPE_COMPANY_ID)?->value ? 'Yes' : 'No',
            'srPhoneActive' => $this->configurationRepository->getConfigurationValueByName(EloquentConfiguration::NAME_SR_PHONE_NUMBER_ACTIVE, $company->companyid, EloquentConfiguration::REL_TYPE_COMPANY_ID)?->value ? 'Yes' : 'No',
            'lastChanged' => $company->timestampstatusupdated,
            'lastQuote' => $this->revenueRepository->getLastLead($company)?->timestampdelivered,
            'lastReview' => $this->reviewRepository->getLastReview($company->companyid)?->timestampadded,
            'lastRevised' =>  Arr::get($this->CRMRepository->getCompaniesLastUpdated([$company->companyid]), "{$company->companyid}." . EloquentCompanyCRMAction::TIMESTAMP_ADDED),
            'lastLogin' => $this->getLastLogin($company),
            'logoFileSquare' => $company->linktologo,
            'logoFileRectangle' => $company->rectanglelinktologo,
            'mediaCount' => $company->companyMedia()->count(),
            'attachmentCount' => $company->attachments()->count(),
            'disallowRanking' => $company->disallow_ranking ? 'Yes' : 'No',
            'enableMultiCampaigns' => $company->enable_multi_campaign ? 'Yes' : 'No',
            'onlyAcceptJornaya' => $company->enable_lead_compliance_jornaya ? 'Yes' : 'No',
            'enableWatchdog' => $company->enable_watchdog_compliance_links ? 'Yes' : 'No',
            'contractApproved' => $this->configurationRepository->getConfigurationValueByName(EloquentConfiguration::NAME_CONTRACT_APPROVED, $company->companyid, EloquentConfiguration::REL_TYPE_COMPANY_ID)?->timestampupdated,
            'socialMediaLinks' => $company->socialMediaLink,
            'accreditations' => $this->getCompanyOptions($company),
            'pricingPlan' => $company->pricingPlan,
            'paymentSource' => $company->payment_source,
            'dateOfNextCharge' => $company->timestampnextinvoice,
            'neverExceedBudget' => $company->never_exceed_budget ? 'Yes' : 'No',
            'allowLeadSalesWithoutCreditCard' => $company->allow_lead_sales_without_cc ? 'Yes' : 'No',
            'payingBy' => $this->transformPaymentMethod($company),
            'activePaymentGateway' => $this->transformPaymentGateway($company)
        ];
    }

    /**
     * @param int $status
     *
     * @return string|null
     */
    public function transformLeadBuyingStatus(int $status): ?string
    {
        return match ($status) {
            EloquentCompany::BUYING_LEADS_STATUS_ACTIVE => 'Active',
            EloquentCompany::BUYING_LEADS_STATUS_INACTIVE => 'Inactive',
            EloquentCompany::BUYING_LEADS_STATUS_PAUSED => 'Paused',
            EloquentCompany::BUYING_LEADS_STATUS_ADMIN_LOCKED => 'Admin Locked',
            default => null,
        };
    }

    /**
     * @param EloquentCompany $company
     *
     * @return int|null
     */
    public function getLastLogin(EloquentCompany $company): ?int
    {
        return $company->historyLog()
            ->where(EloquentHistoryLog::ACTIVITY, EloquentHistoryLog::ACTIVITY_LOGIN)
            ->orderBy(EloquentHistoryLog::TIMESTAMP_ADDED, 'DESC')
            ->first()?->timestampadded;
    }

    /**
     * @param EloquentCompany $company
     *
     * @return Collection<EloquentOption>
     */
    public function getCompanyOptions(EloquentCompany $company): Collection
    {
        return EloquentOption::query()
            ->selectRaw(EloquentOption::TABLE . '.*')
            ->join(EloquentCompanyOption::TABLE, EloquentCompanyOption::TABLE . '.' . EloquentCompanyOption::OPTION_ID,'=' ,EloquentOption::TABLE . '.' . EloquentOption::OPTION_ID)
            ->where(EloquentCompanyOption::TABLE . '.' . EloquentCompanyOption::COMPANY_ID, $company->companyid)
            ->where(EloquentOption::TABLE . '.' . EloquentOption::TYPE, EloquentOption::TYPE_INSTALLER)
            ->get();
    }

    /**
     * @param EloquentCompany $company
     *
     * @return array
     */
    public function transformCompanyAddress(EloquentCompany $company): array
    {
        return $company->companyAddresses->map(fn(EloquentCompanyAddress $companyAddress) => [
            'name' => $companyAddress->name,
            'fullAddress' => $companyAddress->address?->getFullAddress(),
            'address' => $companyAddress->address
        ])->toArray();
    }

    /**
     * @param EloquentCompany $company
     *
     * @return string
     */
    public function transformPaymentMethod(EloquentCompany $company): string
    {
        if (!$company->arbsubscriptionid || !$company->autobilling) return 'Invoice';

        /** @var EloquentArbSubscription|null $arbSubscription */
        $arbSubscription = EloquentArbSubscription::query()->where(EloquentArbSubscription::ARB_SUBSCRIPTION_ID, $company->arbsubscriptionid);

        if ($arbSubscription && $arbSubscription->active) return 'Subscription';
        if ($arbSubscription && !$arbSubscription->active) return 'INACTIVE Subscription';

        return 'Not set';
    }

    /**
     * @param EloquentCompany $company
     *
     * @return string
     */
    public function transformPaymentGateway(EloquentCompany $company): string
    {
        /** @var EloquentCompanyPaymentProfile|null $paymentProfile */
        $paymentProfile = EloquentCompanyPaymentProfile::query()->where(EloquentCompanyPaymentProfile::COMPANY_ID, $company->companyid)->first();

        if (!$paymentProfile) return 'Not set';

        return match ($paymentProfile->provider_code) {
            EloquentCompanyPaymentProfile::SHORT_CODE_STRIPE => EloquentCompanyPaymentProfile::DISPLAY_NAME_STRIPE,
            EloquentCompanyPaymentProfile::SHORT_CODE_AUTHORIZE_NET => EloquentCompanyPaymentProfile::DISPLAY_NAME_AUTHORIZE_NET,
            default => 'Unknown'
        };
    }
}
