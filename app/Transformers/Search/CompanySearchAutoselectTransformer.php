<?php

namespace App\Transformers\Search;

use App\DataModels\Search\SearchResultDataModel;
use Illuminate\Support\Collection;

class CompanySearchAutoselectTransformer
{
    /**
     * @param SearchResultDataModel $companySearchResult
     * @return array
     */
    public function transformCompanies(SearchResultDataModel $companySearchResult): array
    {
        return [
            'id' => $companySearchResult->id,
            'name' => $companySearchResult->displayName,
        ];
    }

    /**
     * @param Collection $searchResults
     * @return array
     */
    public function transform(Collection $searchResults): array
    {
        $companies = $searchResults->pluck('items')->first();
        return $companies->map(fn($company) => $this->transformCompanies($company))->toArray();
    }
}
