<?php

namespace App\Transformers\EmailTemplates;

use App\Enums\EmailTemplateType;
use App\Models\EmailTemplate;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Support\Collection;

class TemplatesListTransformer
{
    /**
     * Handles transforming email templates into their respective client side representations.
     *
     * @param Collection $templates
     * @return array
     */
    public function transformTemplates(Collection $templates): array
    {
        return $templates->map(function ($template) {
            return $this->transformTemplate($template);
        })->toArray();
    }

    /**
     * Handles transforming an email template into its respective client side representation
     *
     * @param EmailTemplate $template
     * @return array
     */
    public function transformTemplate(EmailTemplate $template): array
    {
        $updateTime = $template->{EmailTemplate::FIELD_UPDATED_AT} ?? $template->{EmailTemplate::FIELD_CREATED_AT};

        $owner = $template->owner()->withTrashed()->first();

        return [
            'id'                 => $template->{EmailTemplate::FIELD_ID},
            'name'               => $template->{EmailTemplate::FIELD_NAME},
            'subject'            => $template->{EmailTemplate::FIELD_SUBJECT},
            'owner_name'         => $owner->name . " (". $owner->email . ")",
            'industry'           => $template->{EmailTemplate::RELATION_INDUSTRY}?->{Industry::FIELD_NAME} ?? "Global",
            'last_updated'       => $updateTime->format('Y-m-d H:i:s') . " " . strtoupper($updateTime->timezone->getAbbreviatedName()),
            'default_lead_email' => $template->default_lead_delivery_template,
            'type_name'          => EmailTemplateType::label($template->{EmailTemplate::FIELD_TYPE}),
            'active'             => $template->{EmailTemplate::FIELD_ACTIVE},
        ];
    }
}
