<?php

namespace App\Transformers\EmailTemplates;

use App\Models\EmailTemplateBackground;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Support\Collection;

class TemplateBackgroundsListTransformer
{
    /**
     * Handles transforming email templates into their respective client side representations.
     *
     * @param Collection $templates
     * @return array
     */
    public function transformTemplates(Collection $templates): array
    {
        return $templates->map(function ($template) {
            return $this->transformTemplate($template);
        })->toArray();
    }

    /**
     * Handles transforming an email template into its respective client side representation
     *
     * @param EmailTemplateBackground $template
     * @return array
     */
    public function transformTemplate(EmailTemplateBackground $template): array
    {
        $updateTime = $template->{EmailTemplateBackground::FIELD_UPDATED_AT} ?? $template->{EmailTemplateBackground::FIELD_CREATED_AT};

        return [
            'id'           => $template->{EmailTemplateBackground::FIELD_ID},
            'name'         => $template->{EmailTemplateBackground::FIELD_NAME},
            'owner_name'   => $template->{EmailTemplateBackground::RELATION_OWNER}->{User::FIELD_NAME}." (".$template->{EmailTemplateBackground::RELATION_OWNER}->{User::FIELD_EMAIL}.")",
            'industry'     => $template->{EmailTemplateBackground::RELATION_INDUSTRY}?->{Industry::FIELD_NAME} ?? "Global",
            'last_updated' => $updateTime->format('Y-m-d H:i:s')." ".strtoupper($updateTime->timezone->getAbbreviatedName())
        ];
    }
}
