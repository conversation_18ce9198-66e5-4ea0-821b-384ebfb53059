<?php

namespace App\Transformers\EmailTemplates;

use App\Enums\EmailTemplateType;
use App\Models\EmailTemplate;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Auth;
use App\Services\EmailTemplates\EmailTemplateImageService;

class TemplateTransformer
{
    /**
     * @param EmailTemplateImageService $emailTemplateImageService
     */
    public function __construct(private EmailTemplateImageService $emailTemplateImageService) {}

    /**
     * Handles transforming an email template into its respective client side representation
     *
     * @param EmailTemplate $template
     * @return array
     * @throws Exception
     */
    public function transformTemplate(EmailTemplate $template): array
    {
        $updateTime = $template->{EmailTemplate::FIELD_UPDATED_AT} ?? $template->{EmailTemplate::FIELD_CREATED_AT};

        $shortcodes = $this->emailTemplateImageService->extractShortcodes($template->{EmailTemplate::FIELD_CONTENT});

        $imageNames = [];
        foreach($shortcodes as $shortcode) {
            $imageNames[EmailTemplateImageService::UPLOAD_BASE_PATH."/{$template->{EmailTemplate::FIELD_ID}}/$shortcode"] = $shortcode;
        }

        $objects = $this->emailTemplateImageService->downloadImageDataUrlsFromCloudStorage($template->{EmailTemplate::FIELD_ID}, array_keys($imageNames));

        $contentImages = [];
        foreach($objects as $path => $imageDataUrl) {
            $contentImages[$imageNames[$path]] = $imageDataUrl;
        }

        return [
            'id' => $template->{EmailTemplate::FIELD_ID},
            'name' => $template->{EmailTemplate::FIELD_NAME},
            'subject' => $template->{EmailTemplate::FIELD_SUBJECT},
            'personal' => $template->{EmailTemplate::FIELD_PERSONAL},
            'type' => $template->{EmailTemplate::FIELD_TYPE},
            'content' => $template->{EmailTemplate::FIELD_CONTENT},
            'content_images' => $contentImages,
            'industry_id' => $template->{EmailTemplate::FIELD_INDUSTRY_ID},
            'background_id' => $template->{EmailTemplate::FIELD_BACKGROUND_ID},
            'is_owner' => Auth::id() === $template->{EmailTemplate::FIELD_OWNER_USER_ID},
            'owner_name' => $template->{EmailTemplate::RELATION_OWNER}->{User::FIELD_NAME}." (".$template->{EmailTemplate::RELATION_OWNER}->{User::FIELD_EMAIL}.")",
            'last_updated' => $updateTime->format('Y-m-d H:i:s')." ".strtoupper($updateTime->timezone->getAbbreviatedName()),
            'engine' => $template->{EmailTemplate::FIELD_ENGINE},
            'active' => $template->{EmailTemplate::FIELD_ACTIVE},
            'payload' => $template->payload->toArray(),
        ];
    }
}
