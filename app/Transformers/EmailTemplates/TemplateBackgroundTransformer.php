<?php

namespace App\Transformers\EmailTemplates;

use App\Models\EmailTemplateBackground;
use App\Models\User;
use App\Services\EmailTemplates\EmailTemplateImageService;
use Exception;
use Illuminate\Support\Facades\Auth;

class TemplateBackgroundTransformer
{
    /**
     * @param EmailTemplateImageService $emailTemplateImageService
     */
    public function __construct(private EmailTemplateImageService $emailTemplateImageService) {}

    /**
     * Handles transforming an email template background into its respective client side representation
     *
     * @param EmailTemplateBackground $template
     * @return array
     * @throws Exception
     */
    public function transformTemplate(EmailTemplateBackground $template): array
    {
        $updateTime = $template->{EmailTemplateBackground::FIELD_UPDATED_AT} ?? $template->{EmailTemplateBackground::FIELD_CREATED_AT};

        $headerShortcodes = $this->emailTemplateImageService->extractShortcodes($template->{EmailTemplateBackground::FIELD_HEADER});
        $footerShortcodes = $this->emailTemplateImageService->extractShortcodes($template->{EmailTemplateBackground::FIELD_FOOTER});

        $headerNames = [];
        foreach($headerShortcodes as $headerShortcode) {
            $headerNames[EmailTemplateImageService::UPLOAD_BASE_PATH."/{$template->{EmailTemplateBackground::FIELD_ID}}/$headerShortcode"] = $headerShortcode;
        }

        $footerNames = [];
        foreach($footerShortcodes as $footerShortcode) {
            $footerNames[EmailTemplateImageService::UPLOAD_BASE_PATH."/{$template->{EmailTemplateBackground::FIELD_ID}}/$footerShortcode"] = $footerShortcode;
        }

        $headerObjects = $this->emailTemplateImageService->downloadImageDataUrlsFromCloudStorage($template->{EmailTemplateBackground::FIELD_ID}, array_keys($headerNames));
        $footerObjects = $this->emailTemplateImageService->downloadImageDataUrlsFromCloudStorage($template->{EmailTemplateBackground::FIELD_ID}, array_keys($footerNames));

        $headerImages = [];
        foreach($headerObjects as $headerPath => $headerImageDataUrl) {
            $headerImages[$headerNames[$headerPath]] = $headerImageDataUrl;
        }

        $footerImages = [];
        foreach($footerObjects as $footerPath => $footerImageDataUrl) {
            $footerImages[$footerNames[$footerPath]] = $footerImageDataUrl;
        }

        return [
            'id' => $template->{EmailTemplateBackground::FIELD_ID},
            'name' => $template->{EmailTemplateBackground::FIELD_NAME},
            'personal' => $template->{EmailTemplateBackground::FIELD_PERSONAL},
            'header' => $template->{EmailTemplateBackground::FIELD_HEADER},
            'footer' => $template->{EmailTemplateBackground::FIELD_FOOTER},
            'header_images' => $headerImages,
            'footer_images' => $footerImages,
            'industry_id' => $template->{EmailTemplateBackground::FIELD_INDUSTRY_ID},
            'is_owner' => Auth::id() === $template->{EmailTemplateBackground::FIELD_OWNER_USER_ID},
            'owner_name' => $template->{EmailTemplateBackground::RELATION_OWNER}->{User::FIELD_NAME}." (".$template->{EmailTemplateBackground::RELATION_OWNER}->{User::FIELD_EMAIL}.")",
            'last_updated' => $updateTime->format('Y-m-d H:i:s')." ".strtoupper($updateTime->timezone->getAbbreviatedName())
        ];
    }
}
