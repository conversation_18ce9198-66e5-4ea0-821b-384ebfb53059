<?php

namespace App\Transformers\API;

use App\Models\Odin\Consumer;

class ConsumerAPITransformer
{
    const FIRST_NAME   = 'firstName';
    const LAST_NAME    = 'lastName';
    const EMAIL        = 'email';
    const PHONE_NUMBER = 'phoneNumber';

    /**
     * @param Consumer $consumer
     * @return array
     */
    public function transformConsumer(Consumer $consumer): array
    {
        return [
            self::FIRST_NAME   => $consumer->first_name,
            self::LAST_NAME    => $consumer->last_name,
            self::EMAIL        => $consumer->email,
            self::PHONE_NUMBER => $consumer->phone
        ];
    }
}
