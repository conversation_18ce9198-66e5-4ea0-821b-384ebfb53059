<?php

namespace App\Transformers\API;

use App\Enums\Odin\BudgetCategory;
use App\Http\Controllers\API\ClientDashboard\CampaignController;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use Illuminate\Support\Collection;

class ProductCampaignAPITransformer
{
    const BUDGETS = 'budgets';

    const DESCRIPTION_MAPPING = [
        'In-Home' => 'Face to face meetings with customer',
        'Online'  => 'Zoom / Video call with customer'
    ];

    /**
     * @param Collection $campaigns
     * @return array
     */
    public function transformProductCampaigns(Collection $campaigns): array
    {
        $transformed = collect();
        foreach ($campaigns as $campaign) {
            $transformed->push($this->transformProductCampaign($campaign));
        }
        return $transformed->toArray();
    }

    /**
     * @param ProductCampaign $campaign
     * @return array
     */
    public function transformProductCampaign(ProductCampaign $campaign): array
    {
        $transformedBudgets = collect();
        foreach ($campaign->{ProductCampaign::RELATION_BUDGETS} as $budget) {
            if($budget->{ProductCampaignBudget::FIELD_CATEGORY} === BudgetCategory::VERIFIED) {
                $transformedBudgets->push([
                    CampaignController::BUDGETS_ID => $budget->{ProductCampaignBudget::FIELD_ID},
                    CampaignController::BUDGETS_TIER => $budget->{ProductCampaignBudget::FIELD_QUALITY_TIER},
                    CampaignController::BUDGETS_CATEGORY => $budget->{ProductCampaignBudget::FIELD_CATEGORY},
                    CampaignController::BUDGETS_STATUS => $budget->{ProductCampaignBudget::FIELD_STATUS},
                    CampaignController::BUDGETS_DESCRIPTION => self::DESCRIPTION_MAPPING[$budget->{ProductCampaignBudget::FIELD_QUALITY_TIER}->value],
                    CampaignController::BUDGETS_BUDGET_TYPE => $budget->{ProductCampaignBudget::FIELD_VALUE_TYPE},
                    CampaignController::BUDGETS_BUDGET_VALUE => $budget->{ProductCampaignBudget::FIELD_VALUE}
                ]);
            }
        }

        return [
            ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $campaign->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID},
            ProductCampaign::FIELD_ID => $campaign->{ProductCampaign::FIELD_ID},
            ProductCampaign::FIELD_NAME => $campaign->{ProductCampaign::FIELD_NAME},
            ProductCampaign::FIELD_STATUS => $campaign->{ProductCampaign::FIELD_STATUS},
            ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP => $campaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP},
            CampaignController::CAMPAIGN_PRODUCT => $campaign->{ProductCampaign::RELATION_PRODUCT}->{Product::FIELD_NAME},
            self::BUDGETS => $transformedBudgets->toArray()
        ];
    }

}
