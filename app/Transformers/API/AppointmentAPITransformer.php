<?php

namespace App\Transformers\API;

use App\Contracts\ZipcodeRepositoryInterface;
use App\Enums\Odin\Product;
use App\Enums\Odin\ProductAssignmentStatus;
use App\Enums\Odin\SolarConfigurableFields;
use App\Enums\Odin\TcpaService;
use App\Enums\RejectionReasons;
use App\Enums\Timezone;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTcpaRecord;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCancellation;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Services\Odin\Appointments\AppointmentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Collection;

class AppointmentAPITransformer
{
    const ID                   = 'id';
    const APPOINTMENT_CATEGORY = 'appointmentCategory';
    const CAMPAIGN             = 'campaign';
    const DATE_SENT            = 'dateSent';
    const SOURCE               = 'source';
    const CONTACT              = 'contact';
    const ADDRESS              = 'address';
    const COST                 = 'cost';
    const STATUS               = 'status';
    const CAN_REJECT           = 'canReject';
    const REJECTION_EXPIRY     = 'rejectionExpiry';
    const ELECTRIC_SPEND       = 'electricSpend';
    const APPOINTMENT_TIME     = 'appointmentTime';
    const SERVICE              = 'service';
    const REJECTION_REASON     = 'rejectionReason';
    const REJECTION_NOTES      = 'rejectionNotes';
    const REJECTION_TIME       = 'rejectionTime';
    const CANCELLED_TIME       = 'cancelledTime';
    const CANCELLED_REASON     = 'cancelledReason';
    const UTILITY              = 'utility';
    const LEAD_ID              = 'leadId';
    const WATCHDOG_ID          = 'watchdogId';

    /**
     * @param ConsumerAPITransformer $consumerTransformer
     * @param ZipCodeSetRepository $zipCodeSetRepository
     */
    public function __construct(
        private readonly ConsumerAPITransformer $consumerTransformer,
        private readonly ZipCodeSetRepository $zipCodeSetRepository
    ) {}

    /**
     * @param Collection $appointments
     * @return array
     * @throws Exception
     */
    public function transformAppointments(Collection $appointments): array
    {
        $appointments->loadMissing([
            ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER,
            ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_ADDRESS,
            ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_APPOINTMENT,
            ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA,
            ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_SERVICE,
            ProductAssignment::RELATION_PRODUCT_REJECTIONS,
            ProductAssignment::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_CONSUMER_PRODUCT_TCPA_RECORD => function($with) {
                $with->where(ConsumerProductTcpaRecord::FIELD_TCPA_SERVICE_TYPE, TcpaService::WATCHDOG->value);
            },
            ProductAssignment::RELATION_APPOINTMENT.'.'.ProductAppointment::RELATION_LEAD_PRODUCT_ASSIGNMENTS
        ]);

        $transformed = collect();

        /** @var ProductAssignment $appointment */
        foreach ($appointments as $appointment){
            $transformed->push($this->transformAppointment($appointment));
        }
        return $transformed->toArray();
    }

    /**
     * @param ProductAssignment $appointment
     * @return array
     * @throws Exception
     */
    private function transformAppointment(ProductAssignment $appointment): array
    {
        if(!empty($appointment->{ProductAssignment::FIELD_CHARGEABLE})
        && !empty($appointment->{ProductAssignment::FIELD_DELIVERED})
        && $appointment->{ProductAssignment::RELATION_PRODUCT_REJECTIONS}->isEmpty()
        && $appointment->{ProductAssignment::RELATION_PRODUCT_CANCELLATIONS}->isEmpty()) {
            $status = ProductAssignmentStatus::SOLD->value;
        }
        else if($appointment->{ProductAssignment::RELATION_PRODUCT_REJECTIONS}->isNotEmpty()) {
            $status = ProductAssignmentStatus::REJECTED->value;
        }
        else if($appointment->{ProductAssignment::RELATION_PRODUCT_CANCELLATIONS}->isNotEmpty()
            || empty($appointment->{ProductAssignment::FIELD_CHARGEABLE})) {
            $status = ProductAssignmentStatus::CANCELLED->value;
        }
        else {
            throw new Exception("Invalid appointment state: {$appointment->{ProductAssignment::FIELD_ID}}");
        }

        $consumerProduct = $appointment->{ProductAssignment::RELATION_CONSUMER_PRODUCT};

        $appointmentRejection = $appointment->{ProductAssignment::RELATION_PRODUCT_REJECTIONS}->first();

        $rejectionReason = $appointmentRejection ? RejectionReasons::getSolarReasons(Product::APPOINTMENT)[RejectionReasons::from(explode('|', $appointmentRejection->{ProductRejection::FIELD_REASON})[0])->value] : '';
        $rejectionNotes = $appointmentRejection ? explode('|', $appointmentRejection->{ProductRejection::FIELD_REASON})[1] : '';

        $cancelledTime = $appointment
            ->{ProductAssignment::RELATION_PRODUCT_CANCELLATIONS}
            ?->first()
            ?->{ProductCancellation::FIELD_CREATED_AT}
            ?->format('m/d/Y, g:i A') ?? '';

        $cancelledReason = $appointment->{ProductAssignment::RELATION_PRODUCT_CANCELLATIONS}?->first()?->{ProductCancellation::FIELD_REASON}?->value;

        $utcOffsetAccountingForDST = $this->zipCodeSetRepository->getUtc($consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->{Address::FIELD_ZIP_CODE});

        $timezoneName = Timezone::displayAbbreviations(Timezone::isDST())[$utcOffsetAccountingForDST];

        $appointmentTime = Carbon::createFromFormat('Y-m-d H:i:s', $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT}, $utcOffsetAccountingForDST)->format('m/d/Y, g:i A')." {$timezoneName}";

        $consumerProductDataPayload = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->{ConsumerProductData::FIELD_PAYLOAD};

        $dateSent = $appointment->{ProductAssignment::FIELD_DELIVERED_AT}->setTimezone($utcOffsetAccountingForDST)->format('m/d/Y, h:i A')." {$timezoneName}";

        return [
            self::ID => $appointment->{ProductAssignment::FIELD_ID},
            self::CAMPAIGN => $appointment->{ProductAssignment::RELATION_CAMPAIGN}()->withTrashed()->first()?->{ProductCampaign::FIELD_NAME},
            self::APPOINTMENT_CATEGORY => $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT_TYPE},
            self::DATE_SENT => $dateSent,
            self::SOURCE => $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_TRACKING}?->{ConsumerProductTracking::RELATION_WEBSITE}?->{Website::FIELD_URL},
            self::CONTACT => $this->consumerTransformer->transformConsumer($consumerProduct->{ConsumerProduct::RELATION_CONSUMER}),
            self::ADDRESS => $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}->getFullAddress(),
            self::COST => $appointment->{ProductAssignment::FIELD_COST},
            self::STATUS => $status,
            self::CAN_REJECT => $appointment->{AppointmentService::ALIAS_WITHIN_REJECTION_WINDOW} && $status === ProductAssignmentStatus::SOLD->value,
            self::REJECTION_EXPIRY => $appointment->{ProductAssignment::FIELD_REJECTION_EXPIRY}->timestamp,
            self::ELECTRIC_SPEND => $consumerProductDataPayload[SolarConfigurableFields::ELECTRIC_COST->value] ?? 0,
            self::APPOINTMENT_TIME => $appointmentTime,
            self::SERVICE => $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::FIELD_NAME},
            self::REJECTION_REASON => $rejectionReason,
            self::REJECTION_NOTES => $rejectionNotes,
            self::REJECTION_TIME => $appointmentRejection?->{ProductRejection::CREATED_AT}->format('m/d/Y, g:i A'),
            self::CANCELLED_TIME => $cancelledTime,
            self::CANCELLED_REASON => $cancelledReason,
            self::UTILITY => $consumerProductDataPayload[SolarConfigurableFields::UTILITY_NAME->value] ?? '',
            self::LEAD_ID => $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_LEGACY_ID},
            self::WATCHDOG_ID => $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_TCPA_RECORD}?->{ConsumerProductTcpaRecord::FIELD_TCPA_ID} ?? ''
        ];
    }
}
