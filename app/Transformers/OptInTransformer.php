<?php

namespace App\Transformers;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Services\CompanyOptInNameService;
use Illuminate\Support\Collection;

class OptInTransformer
{

    /**
     * @param Collection $campaigns
     * @param Collection|null $companies
     * @return array
     */
    public static function transform(Collection $campaigns, ?Collection $companies): array
    {

        $optIns = [];
        /** @var CompanyCampaign $campaign */
        foreach ($campaigns as $campaign) {
            $optInName = CompanyOptInNameService::getOptInNameByCampaign($campaign);
            $optIns[]  = [
                'reference'          => $campaign->company->reference,
                'campaign_reference' => $campaign->reference,
                'name'               => $optInName?->name ?? $campaign->company->name,
                'opt_in_name_id'     => $optInName?->id,
                'bayesian_all_time'  => $campaign->company->companyRanking?->bayesian_all_time ?? 0,
                'review_count'       => $campaign->company->companyRanking?->review_count ?? 0,
                'phone'              => null, // todo
            ];
        }

        if ($companies) {
            /** @var Company $company */
            foreach ($companies as $company) {
                $optInName = CompanyOptInNameService::getOptInNameByCompany($company);
                $optIns[]  = [
                    'reference'          => $company->reference,
                    'campaign_reference' => null,
                    'name'               => $optInName?->name ?? $company->name,
                    'opt_in_name_id'     => $optInName?->id,
                    'bayesian_all_time'  => $company->companyRanking?->bayesian_all_time ?? 0,
                    'review_count'       => $company->companyRanking?->review_count ?? 0,
                    'phone'              => null, // todo
                ];
            }
        }

        return $optIns;
    }
}
