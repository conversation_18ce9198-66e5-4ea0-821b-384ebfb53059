<?php

namespace App\Transformers\ProductAssignment;

use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;

class ProductAssignmentTransformer
{

    /**
     * @param Collection<int, ProductAssignment> $productAssignments
     * @return Collection<int, ProposedProductAssignment>
     */
    public function transformAssignmentsToProposedAssignments(Collection $productAssignments): Collection
    {
        // $productAssignments->load(ProductAssignment::RELATION_PRODUCT);
        $proposedAssignments = collect();
        foreach ($productAssignments as $assignment) {
            $proposedAssignments->push($this->transformAssignmentToProposedAssignment($assignment));
        }
        return $proposedAssignments;
    }

    /**
     * @param ProductAssignment $assignment
     * @return ProposedProductAssignment
     */
    private function transformAssignmentToProposedAssignment(ProductAssignment $assignment): ProposedProductAssignment
    {
        return new ProposedProductAssignment(
            companyId: $assignment->company_id,
            campaignId: $assignment->campaign_id,
            budgetId: $assignment->budget_id ?? 0, // todo budget_id not yet added to ProductAssignment
            price: $assignment->cost,
            productId: $assignment->consumerProduct->serviceProduct->product_id,
            salesTypeId: $assignment->sale_type_id,
            consumerProductId: $assignment->consumer_product_id,
            isAsserted: true,
            isExistingAssignment: true,
            qualityTierId: $assignment->quality_tier_id
        );
    }
}
