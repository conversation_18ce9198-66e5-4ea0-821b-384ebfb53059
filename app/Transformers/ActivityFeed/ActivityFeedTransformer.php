<?php

namespace App\Transformers\ActivityFeed;

use App\Models\ActivityFeed;
use App\Models\Call;
use App\Models\Text;
use App\Models\Legacy\EloquentEmail;
use App\Models\User;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;

class ActivityFeedTransformer
{
    public function transformActivityFeed(Collection $activityFeed): array
    {
        return $activityFeed->each(function($activityItem) {
            return $this->transformActivityItem($activityItem);
        })->toArray();
    }

    #[ArrayShape(['subject' => "string", 'body' => "string", 'user_id' => "int", 'user_name' => "string", 'company_id' => "int", 'created_at' => "string", 'updated_at' => "string"])]
    public function transformActivityItem(ActivityFeed $activity): array
    {
        $activityItem = $activity->item;
        $user = User::query()->find($activityItem->user_id)->get();
        return [
            'subject' => $activityItem->name,
            'body'  => $activityItem->body,
            'user_id' => $activityItem->user_id,
            'user_name' => $user->first_name,
            'company_id' => $activityItem->company_id,
            'created_at' => $activityItem->created_at,
            'updated_at' => $activityItem->updated_at,
        ];
    }
}
