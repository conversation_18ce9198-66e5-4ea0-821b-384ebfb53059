<?php

namespace App\Transformers;

use App\Models\TaskNote;
use App\Models\User;
use Illuminate\Support\Collection;

class TaskNotesTransformer
{
    /**
     * Handles transforming a collection of lead campaigns to their client side representation.
     *
     * @param Collection|TaskNote $taskNotes
     * @return array
     */
    public function transformCampaigns(Collection $taskNotes): array
    {
        return $taskNotes->map(function (TaskNote $taskNote) {
            return $this->transformTaskNote($taskNote);
        })->values()->toArray();
    }

    /**
     * Handles transforming a single lead campaign to its client side representation.
     *
     * @param TaskNote $taskNote
     * @return array
     */
    public function transformTaskNote(TaskNote $taskNote): array
    {
        return [
            "user_name" => $taskNote->user()->first()?->{User::FIELD_NAME},
            "date"      => $taskNote->created_at->toISOString(),
            "note"      => $taskNote->{TaskNote::FIELD_NOTE}
        ];
    }
}
