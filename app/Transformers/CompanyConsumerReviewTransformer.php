<?php
namespace App\Transformers;

use App\Enums\CompanyConsumerReviewStatus;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewReply;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Support\Collection;

class CompanyConsumerReviewTransformer
{
    /**
     * @param Collection $reviews
     * @return Collection
     */
    public function transformReviews(Collection $reviews): Collection
    {
        return $reviews->map(function(Review $review) {
            return $this->transform($review);
        });
    }

    /**
     * @param Collection $reviews
     * @return Collection
     */
    public function transformReviewsForDashboard(Collection $reviews): Collection
    {
        return $reviews->map(function(Review $review) {
            return $this->transformForDashboard($review);
        });
    }

    /**
     * @param Review $review
     * @return array
     */
    public function transform(Review $review): array
    {
        return [
            'id'                    => $review->{Review::FIELD_ID},
            'reviewer_name'         => htmlspecialchars_decode($review->{Review::RELATION_REVIEWER}->name),
            'title'                 => htmlspecialchars_decode($review->{Review::RELATION_REVIEW_DATA}->title),
            'comment'               => htmlspecialchars_decode($review->{Review::RELATION_REVIEW_DATA}->comments),
            'score'                 => $review->{Review::RELATION_REVIEW_DATA}->overall_score,
            'status'                => $review->{Review::FIELD_STATUS},
            'display_status'        => CompanyConsumerReviewStatus::label($review->{Review::FIELD_STATUS}),
            'created'               => $review->{Review::FIELD_CREATED_AT},
            'review_replies'        => $this->transformReviewReplies($review->{Review::RELATION_REVIEW_REPLIES}),
            'review_data'           => $review->{Review::RELATION_REVIEW_DATA}->data,
            'review_is_verified'    => $review->{Review::FIELD_IS_VERIFIED},
            'email'                 => $review->{Review::RELATION_REVIEWER}->email,
            'phone'                 => $review->{Review::RELATION_REVIEWER}->phone,
            'website'               => $review->website,
        ];
    }

    /**
     * @param Review $review
     * @return array
     */
    public function transformForDashboard(Review $review): array
    {
        return [
            'id'                    => $review->{Review::FIELD_ID},
            'reviewer_name'         => htmlspecialchars_decode($review->{Review::RELATION_REVIEWER}->name),
            'title'                 => htmlspecialchars_decode($review->{Review::RELATION_REVIEW_DATA}->title),
            'comment'               => htmlspecialchars_decode($review->{Review::RELATION_REVIEW_DATA}->comments),
            'score'                 => $review->{Review::RELATION_REVIEW_DATA}->overall_score,
            'status'                => $review->{Review::FIELD_STATUS},
            'display_status'        => CompanyConsumerReviewStatus::label($review->{Review::FIELD_STATUS}),
            'created'               => $review->{Review::FIELD_CREATED_AT},
            'review_replies'        => $this->transformReviewReplies($review->{Review::RELATION_REVIEW_REPLIES}),
            'review_data'           => $review->{Review::RELATION_REVIEW_DATA}->data,
            'review_is_verified'    => $review->{Review::FIELD_IS_VERIFIED},
        ];
    }

    /**
     * @param Collection $replies
     * @return Collection
     */
    public function transformReviewReplies(Collection $replies): Collection
    {
        return $replies->map(function(ReviewReply $reply) {
            return $this->transformReviewReply($reply);
        });
    }

    /**
     * @param ReviewReply $reviewReply
     * @return array
     */
    public function transformReviewReply(ReviewReply $reviewReply): array
    {
        return [
            'id'        => $reviewReply->{ReviewReply::FIELD_ID},
            'review_id' => $reviewReply->{ReviewReply::FIELD_REVIEW_ID},
            'name'      => $this->getReviewReplyName($reviewReply),
            'comments'  => $reviewReply->{ReviewReply::FIELD_COMMENTS},
        ];
    }

    public function getReviewReplyName(ReviewReply $reviewReply): string
    {
        if ($reviewReply->{ReviewReply::FIELD_COMPANY_USER_ID}) {
            $companyUser = CompanyUser::findOrFail($reviewReply->{ReviewReply::FIELD_COMPANY_USER_ID});
            return $companyUser->{CompanyUser::FIELD_FIRST_NAME} . " " . $companyUser->{CompanyUser::FIELD_LAST_NAME};
        } else if ($reviewReply->{ReviewReply::FIELD_ADMIN_USER_ID}) {
            $user = User::findOrFail($reviewReply->{ReviewReply::FIELD_ADMIN_USER_ID});
            return $user->{User::FIELD_NAME};
        } else {
            return "";
        }
    }
}
