<?php

namespace App\Transformers;

use App\Models\Notification;

class NotificationTransformer
{
    const TIMESTAMP = 'timestamp';

    /**
     * @param Notification $notification
     * @return array
     */
    public function transformNotification(Notification $notification): array
    {
        return [
            Notification::FIELD_ID      => $notification->{Notification::FIELD_ID},
            Notification::FIELD_USER_ID => $notification->{Notification::FIELD_USER_ID},
            Notification::FIELD_FROM_ID => $notification->{Notification::FIELD_FROM_ID},
            Notification::FIELD_SUBJECT => $notification->{Notification::FIELD_SUBJECT},
            'message'                   => $notification->{Notification::FIELD_BODY},
            Notification::FIELD_TYPE    => $notification->{Notification::FIELD_TYPE},
            Notification::FIELD_READ    => $notification->{Notification::FIELD_READ},
            Notification::FIELD_PAYLOAD => $notification->{Notification::FIELD_PAYLOAD},
            Notification::FIELD_LINK    => $notification->{Notification::FIELD_LINK},
            Notification::FIELD_LINK_TYPE => $notification->{Notification::FIELD_LINK_TYPE},
            self::TIMESTAMP             => $notification->{Notification::CREATED_AT}->timestamp,
        ];
    }
}
