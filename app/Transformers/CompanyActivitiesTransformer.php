<?php

namespace App\Transformers;

use App\Models\ActivityFeed;

class CompanyActivitiesTransformer
{
    /**
     * <PERSON>les transforming a single lead campaign to its client side representation.
     *
//     * @param ActivityFeed $activityFeed
     * @return array
     */
    public function transformActivityFeed(): array
    {
        return [
            'category' => 'Email',
            'itemData' => [
                'fromaddress' => 'text from',
                'toaddress'=> 'test to',
                'content'=> 'wow a big bit of content',
                'subject' => 'test subject',
                'timestampadded' => 1670821239,
            ]
        ];
    }
}
