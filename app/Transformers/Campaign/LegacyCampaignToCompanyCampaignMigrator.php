<?php

namespace App\Transformers\Campaign;

use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Enums\Campaigns\CampaignExternalRelationType;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\CompanyCampaignPropertyType;
use App\Models\Legacy\Crm;
use App\Models\Legacy\CrmIntegration;
use App\Models\Legacy\EloquentLeadCampaignPauseReasons;
use App\Models\Legacy\LeadCampaignCrmIntegration;
use App\Models\Legacy\LeadCampaignLeadCategory;
use App\Models\Legacy\LeadDeliveryMethod;
use App\Models\Odin\CompanyUser;
use App\Enums\Campaigns\CampaignType;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Campaigns\Modules\Budget\ContainerType;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\Product;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Legacy\LeadCampaignDeliveryMethod;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadPrice;
use App\Models\Legacy\LeadSalesType;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry as IndustryModel;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ServiceProduct;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ProductRepository;
use App\Repositories\Odin\PropertyTypeRepository;
use App\Repositories\Odin\QualityTierRepository;
use App\Repositories\Odin\ServiceProductRepository;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use App\Models\Legacy\LeadCampaign;
use App\Models\Campaigns\CompanyCampaign;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use Throwable;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Models\SaleType as SaleTypeModel;

class LegacyCampaignToCompanyCampaignMigrator
{
    const BUDGET_LAST_MODIFIED_AT = 'budget_last_modified_at';
    const BUDGET_TYPE = 'budget_type';
    const BUDGET_VALUE = 'budget_value';
    const BUDGET_STATUS = 'budget_status';

    const SERVICE_PRODUCT_ID = 'service_product_id';
    const COMPANY_CAMPAIGN_UUID = 'company_campaign_uuid';
    const COMPANY_CAMPAIGN_ID = 'company_campaign_id';
    const LEAD_CAMPAIGN_ID = 'lead_campaign_id';
    const PRODUCT_ID = 'product_id';

    const string CRM_MIGRATION_TABLE       = 'crm_template_migrations';
    const string CRM_MIGRATION_COMPANY_ID  = 'company_id';
    const string CRM_MIGRATION_LEGACY_ID   = 'legacy_crm_integration_id';
    const string CRM_MIGRATION_TEMPLATE_ID = 'company_crm_template_id';

    private Carbon $now;
    private int $leadProductId = 0;
    private int $appointmentProductId = 0;
    private int $solarInstallationServiceId = 0;
    private int $roofReplacementServiceId = 0;
    private array $saleTypes = [];
    private array $propertyTypes = [];
    private array $qualityTiers = [];
    private array $stateLocations = [];
    private array $companyIdMap = [];
    private array $lowBidCampaignIds = [];

    private array $companyCampaignUuidLeadCampaignIdsMap = [];
    private array $companyCampaignInfoLeadCampaignIdsMap = [];
    private array $companyCampaignIds = [];
    private array $crmTemplateIdMap = [];

    private int $migrated = 0;
    private int $campaignsPerMinute = 0;
    private int $elapsed = 0;
    private ?IndustryEnum $industry = null;

    private bool $migrateCrmIntegrationsAsCompanyTemplates = true;

    public function __construct(
        private readonly ProductRepository $productRepository,
        private readonly ServiceProductRepository $serviceProductRepository,
        private readonly QualityTierRepository $qualityTierRepository,
        private readonly PropertyTypeRepository $propertyTypeRepository,
        private readonly LocationRepository $locationRepository,
        private readonly LegacyCRMIntegrationTransformer $integrationTransformer,
    )
    {
        $this->now                          = Carbon::now('UTC');
        $this->leadProductId                = $this->productRepository->getLeadProductId();
        $this->appointmentProductId         = $this->productRepository->getAppointmentProductId();
        $this->solarInstallationServiceId   = $this->serviceProductRepository->getDefaultServiceProductByIndustry(IndustryEnum::SOLAR->value)->{ServiceProduct::FIELD_INDUSTRY_SERVICE_ID};
        $this->roofReplacementServiceId     = $this->serviceProductRepository->getDefaultServiceProductByIndustry(IndustryEnum::ROOFING->value)->{ServiceProduct::FIELD_INDUSTRY_SERVICE_ID};
        $this->propertyTypes                = $this->propertyTypeRepository->all();
        $this->qualityTiers                 = $this->qualityTierRepository->all();
        $this->stateLocations               = $this->locationRepository->getStates()->pluck(Location::ID, Location::STATE_ABBREVIATION)->toArray();
        $this->lowBidCampaignIds            = $this->getLowBidCampaignIds();

        //TODO use repository once SaleTypeRepository::all() is fixed
        $this->saleTypes                    = SaleTypeModel::query()->pluck(SaleTypeModel::FIELD_ID, SaleTypeModel::FIELD_KEY)->toArray();

        $this->companyIdMap = Company::query()
            ->select([Company::FIELD_ID, Company::FIELD_LEGACY_ID])
            ->get()
            ->mapWithKeys(fn($company) => [$company->legacy_id => $company->id])
            ->toArray();
        $this->companyIdMap[0] = 0;
    }

    /**
     * @param IndustryEnum $industry
     * @param int $chunkSize
     * @return boolean
     * @throws Throwable
     */
    public function transformIndustryLeadCampaigns(
        IndustryEnum $industry,
        int $chunkSize = 250
    ): bool
    {
        $this->industry = $industry;

        try {
            $relations = [
                LeadCampaign::RELATION_COMPANY,
                LeadCampaign::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_BUDGETS,
                LeadCampaign::RELATION_LEAD_SALES_TYPE_CONFIGURATIONS,
                LeadCampaign::RELATION_LEAD_PRICES => function($has) {
                    $has->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_BID);
                },
                LeadCampaign::RELATION_LEAD_CAMPAIGN_LOCATIONS_LOCATIONS,
                LeadCampaign::RELATION_LEAD_CAMPAIGN_PAUSE_REASONS,
                LeadCampaign::RELATION_LEAD_CAMPAIGN_CRM_INTEGRATION.'.'.LeadCampaignCrmIntegration::RELATION_CRM_INTEGRATION.'.'.CrmIntegration::RELATION_CRM,
                LeadCampaign::RELATION_LEAD_CAMPAIGN_DELIVERY_METHODS.'.'.LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD.'.'.LeadDeliveryMethod::RELATION_COMPANY_CONTACT,
                LeadCampaign::RELATION_LEAD_CAMPAIGN_LEAD_CATEGORIES
            ];

            $this->displayProgress(0);

            $campaignQuery = LeadCampaign::query()
                ->join(DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE, function($join) {
                    $join->on(
                        DatabaseHelperService::readOnlyDatabase().'.'.EloquentCompany::TABLE.'.'.EloquentCompany::COMPANY_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::COMPANY_ID
                    );
                })
                ->leftJoin(DatabaseHelperService::database().'.'.CompanyCampaignRelation::TABLE, function($join) {
                    $join
                        ->on(
                            DatabaseHelperService::database().'.'.CompanyCampaignRelation::TABLE.'.'.CompanyCampaignRelation::FIELD_RELATION_ID,
                            '=',
                            DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
                        )
                        ->where(CompanyCampaignRelation::FIELD_RELATION_TYPE, CampaignExternalRelationType::LEGACY_LEAD_CAMPAIGN->value);
                })
                ->when(
                    $industry->isManagedByAdmin2(),
                    function($query) use ($industry) {
                        $query
                            ->join(DatabaseHelperService::database().'.'.ProductCampaign::TABLE, function($join) {
                                $join->on(
                                    DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
                                    '=',
                                    DatabaseHelperService::readOnlyDatabase().'.'.LeadCampaign::TABLE.'.'.LeadCampaign::ID
                                );
                            })
                            ->join(DatabaseHelperService::database().'.'.IndustryService::TABLE, function($join) {
                                $join->on(
                                    DatabaseHelperService::database().'.'.IndustryService::TABLE.'.'.IndustryService::FIELD_ID,
                                    '=',
                                    DatabaseHelperService::database().'.'.ProductCampaign::TABLE.'.'.ProductCampaign::FIELD_INDUSTRY_SERVICE_ID
                                );
                            })
                            ->join(DatabaseHelperService::database().'.'.IndustryModel::TABLE, function($join) {
                                $join->on(
                                    DatabaseHelperService::database().'.'.IndustryModel::TABLE.'.'.IndustryModel::FIELD_ID,
                                    '=',
                                    DatabaseHelperService::database().'.'.IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID
                                );
                            })
                            ->where(IndustryModel::TABLE.'.'.IndustryModel::FIELD_NAME, $industry->value)
                            ->where(EloquentCompany::TABLE.'.'.EloquentCompany::TYPE, EloquentCompany::TYPE_MULTI);
                    },
                    function($query) use ($industry) {
                        $query->where(EloquentCompany::TABLE.'.'.EloquentCompany::TYPE, $industry === IndustryEnum::ROOFING ? EloquentCompany::TYPE_ROOFER : EloquentCompany::TYPE_AGGREGATOR);
                    }
                )
                ->whereNull(CompanyCampaignRelation::TABLE.'.'.CompanyCampaignRelation::FIELD_ID)
                ->orderBy(LeadCampaign::TABLE.'.'.LeadCampaign::ID)
                ->select(LeadCampaign::TABLE.'.'.LeadCampaign::ID)
                ->distinct()
                ->limit($chunkSize);

            while(!empty($leadCampaignIds = $campaignQuery->pluck(LeadCampaign::ID)->toArray())) {
                $startTime = microtime(true);

                $leadCampaigns = LeadCampaign::query()
                    ->whereIn(LeadCampaign::ID, $leadCampaignIds)
                    ->orderBy(LeadCampaign::ID)
                    ->with($relations)
                    ->get();

                DB::transaction(function() use ($leadCampaigns) {
                    $this->transformChunk($leadCampaigns);
                });

                $this->migrated += $leadCampaigns->count();

                $currentChunkElapsed = ceil(microtime(true) - $startTime);

                $this->elapsed += $currentChunkElapsed;

                $this->campaignsPerMinute = (int) floor($this->migrated / $this->elapsed * 60);

                $this->displayProgress(0);
            }

            return true;
        }
        catch(Throwable $e) {
            logger()->error($e);

            throw $e;
        }
    }

    /**
     * @param int $step
     * @param int $ticks Needs to be a multiple of the number of migrations steps e.g. if there's 7 migration steps it can be 42, 49, or 70, etc
     * @return void
     */
    private function displayProgress(int $step, int $ticks = 7): void
    {
        $stepSymbol = '';
        $blankSymbol = '';
        $stepSize = (int) floor($ticks / 7);
        for($j = 0; $j <= $stepSize; $j++) {
            $stepSymbol .= '=';
            $blankSymbol .= ' ';
        }

        $progress = '';
        for($i = 0; $i < 7; $i++) {
            if($i < $step) {
                $progress .= $stepSymbol;
            }
            else if($i === $step) {
                $progress .= substr_replace($stepSymbol, '>', -1, 1);
            }
            else {
                $progress .= $blankSymbol;
            }
        }

        $elapsedSecs = $this->elapsed % 60;
        $elapsedMins = (int) floor($this->elapsed / 60);

        echo "[{$progress}] {$this->migrated} legacy campaigns migrated. {$this->campaignsPerMinute} campaigns/min. {$elapsedMins} mins {$elapsedSecs} secs elapsed.\r";
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     * @throws Exception
     */
    private function transformChunk(Collection $leadCampaigns): bool
    {
        if ($this->migrateCrmIntegrationsAsCompanyTemplates) {
            $companyIds = $leadCampaigns
                ->pluck(LeadCampaign::COMPANY_ID)
                ->unique()
                ->toArray();

            $this->transformCompanyCrmTemplates($companyIds);
        }

        $leadCampaigns = $leadCampaigns->keyBy(LeadCampaign::ID);

        $this->transformCampaigns($leadCampaigns);

        $this->displayProgress(1);

        $this->transformCampaignReactivations($leadCampaigns);

        $this->displayProgress(2);

        $this->transformLeadCampaignCategories($leadCampaigns);

        $this->displayProgress(3);

        $this->transformLocations($leadCampaigns);

        $this->displayProgress(4);

        $this->transformBudgets($leadCampaigns);

        $this->displayProgress(5);

        $this->transformBids($leadCampaigns);

        $this->displayProgress(6);

        $this->transformDeliveryMethods($leadCampaigns);

        $this->displayProgress(7);

        return true;
    }

    /**
     * @param array $data
     * @param array $transformedData
     * @return array
     */
    private function mergeData(array $data, array &$transformedData): array
    {
        foreach($data as $table => $rows) {
            if(empty($transformedData[$table])) {
                $transformedData[$table] = [];
            }

            $transformedData[$table] = array_merge($transformedData[$table], $rows);
        }

        return $transformedData;
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     * @throws Exception
     */
    private function transformCampaigns(Collection &$leadCampaigns): bool
    {
        $transformedCampaigns = [
            CompanyCampaign::TABLE => []
        ];

        $this->companyCampaignUuidLeadCampaignIdsMap = [];
        $this->companyCampaignInfoLeadCampaignIdsMap = [];

        foreach($leadCampaigns as $leadCampaign) {
            $this->companyCampaignUuidLeadCampaignIdsMap[$leadCampaign->{LeadCampaign::ID}] = [];

            if($leadCampaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::TYPE} !== EloquentCompany::TYPE_MULTI) {
                $tlc = $this->transformLeadCampaign($leadCampaign);
                $this->mergeData($tlc, $transformedCampaigns);

                if ($leadCampaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN}?->{ProductCampaign::FIELD_PRODUCT_ID} === $this->appointmentProductId) {
                    $tpc = $this->transformProductCampaign($leadCampaign);
                    $this->mergeData($tpc, $transformedCampaigns);
                }

            }
            else {
                $tpc = $this->transformProductCampaign($leadCampaign);

                $this->mergeData($tpc, $transformedCampaigns);
            }
        }

        $transformedCampaigns[CompanyCampaign::TABLE] = array_filter($transformedCampaigns[CompanyCampaign::TABLE], fn($tc) => !empty($tc));
        $this->companyCampaignUuidLeadCampaignIdsMap = array_filter($this->companyCampaignUuidLeadCampaignIdsMap, fn($map) => !empty($map));

        $this->saveTransformedCampaignData($transformedCampaigns);

        $this->updateCompanyCampaignToLeadCampaignMap();

        $leadCampaignIds = array_keys($this->companyCampaignInfoLeadCampaignIdsMap);

        $leadCampaigns = $leadCampaigns->filter(fn($val, $id) => in_array($id, $leadCampaignIds, true));

        return true;
    }

    /**
     * @param LeadCampaign $leadCampaign
     * @return array[]
     * @throws Exception
     */
    private function transformLeadCampaign(LeadCampaign $leadCampaign): array
    {
        $companyCampaignRows = [];

        $serviceId = $leadCampaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::TYPE} === EloquentCompany::TYPE_ROOFER
            ? $this->roofReplacementServiceId
            : $this->solarInstallationServiceId;

        $uuid = Uuid::uuid4()->toString();

        $companyCampaignRows[] = [
            CompanyCampaign::FIELD_COMPANY_ID               => $this->companyIdMap[$leadCampaign->{LeadCampaign::COMPANY_ID}] ?? 0,
            CompanyCampaign::FIELD_PRODUCT_ID               => $this->leadProductId,
            CompanyCampaign::FIELD_SERVICE_ID               => $serviceId,
            CompanyCampaign::FIELD_TYPE                     => $leadCampaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::TYPE} === EloquentCompany::TYPE_AGGREGATOR
                ? CampaignType::SOLAR_LEAD_CAMPAIGN->value
                : CampaignType::LEAD_CAMPAIGN->value,
            CompanyCampaign::FIELD_STATUS                   => $this->getCampaignStatus($leadCampaign, false),
            CompanyCampaign::FIELD_NAME                     => $leadCampaign->{LeadCampaign::NAME},
            CompanyCampaign::FIELD_REFERENCE                => $uuid,
            CompanyCampaign::FIELD_MAXIMUM_BUDGET_USAGE     => $leadCampaign->{LeadCampaign::MAXIMUM_BUDGET_USAGE},
            CompanyCampaign::FIELD_ZIP_CODE_TARGETED        => $leadCampaign->{LeadCampaign::ZIP_CODE_TARGETED} ?? false,
            CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES => in_array($leadCampaign->id, $this->lowBidCampaignIds),
            CompanyCampaign::CREATED_AT                     => $leadCampaign->{LeadCampaign::CREATED_AT},
            CompanyCampaign::UPDATED_AT                     => $leadCampaign->{LeadCampaign::UPDATED_AT}
        ];

        $this->companyCampaignUuidLeadCampaignIdsMap[$leadCampaign->{LeadCampaign::ID}][] = $uuid;

        return [
            CompanyCampaign::TABLE => $companyCampaignRows
        ];
    }

    /**
     * @param LeadCampaign $leadCampaign
     * @return array[]
     * @throws Exception
     */
    private function transformProductCampaign(LeadCampaign $leadCampaign): array
    {
        $companyCampaignRows = [];

        if($leadCampaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN}) {
            $productCampaign = $leadCampaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN};

            $uuid = Uuid::uuid4()->toString();

            $companyType = $leadCampaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::TYPE};

            if($companyType === EloquentCompany::TYPE_MULTI) {
                $campaignType = $productCampaign->{ProductCampaign::FIELD_PRODUCT_ID} === $this->appointmentProductId ? CampaignType::APPOINTMENT_CAMPAIGN : CampaignType::LEAD_CAMPAIGN;
                $campaignName = $leadCampaign->name;
                $serviceId = $productCampaign->{ProductCampaign::FIELD_INDUSTRY_SERVICE_ID};
            }
            else if($companyType === EloquentCompany::TYPE_INSTALLER || $companyType === EloquentCompany::TYPE_AGGREGATOR) {
                $campaignType = CampaignType::SOLAR_APPOINTMENT_CAMPAIGN;
                $serviceId = $this->solarInstallationServiceId;
            }
            else if($companyType === EloquentCompany::TYPE_ROOFER) {
                $campaignType = CampaignType::APPOINTMENT_CAMPAIGN;
                $serviceId = $this->roofReplacementServiceId;
            }
            else {
                throw new Exception("Invalid company type $companyType");
            }

            $companyCampaignRows[] = [
                CompanyCampaign::FIELD_COMPANY_ID               => $productCampaign->{ProductCampaign::FIELD_COMPANY_ID},
                CompanyCampaign::FIELD_PRODUCT_ID               => $productCampaign->{ProductCampaign::FIELD_PRODUCT_ID},
                CompanyCampaign::FIELD_SERVICE_ID               => $serviceId,
                CompanyCampaign::FIELD_TYPE                     => $campaignType->value,
                CompanyCampaign::FIELD_STATUS                   => $this->getCampaignStatus($leadCampaign, true),
                CompanyCampaign::FIELD_NAME                     => $productCampaign->name,
                CompanyCampaign::FIELD_REFERENCE                => $uuid,
                CompanyCampaign::FIELD_MAXIMUM_BUDGET_USAGE     => $leadCampaign->{LeadCampaign::MAXIMUM_BUDGET_USAGE},
                CompanyCampaign::CREATED_AT                     => $productCampaign->{ProductCampaign::CREATED_AT},
                CompanyCampaign::UPDATED_AT                     => $productCampaign->{ProductCampaign::UPDATED_AT},
                CompanyCampaign::FIELD_ZIP_CODE_TARGETED        => $leadCampaign->zip_code_targeted ?? false,
                CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES => in_array($leadCampaign->id, $this->lowBidCampaignIds),
            ];

            $this->companyCampaignUuidLeadCampaignIdsMap[$leadCampaign->{LeadCampaign::ID}][] = $uuid;
        }

        return [
            CompanyCampaign::TABLE => $companyCampaignRows
        ];
    }

    /**
     * @param LeadCampaign $leadCampaign
     * @return CampaignStatus
     */
    private function getCampaignStatus(LeadCampaign $leadCampaign, bool $isAppointment = false): CampaignStatus
    {
        $productCampaign = $leadCampaign->productCampaign;

        $status = $isAppointment
            ? $productCampaign->status
            : $leadCampaign->status;

        $temporaryPause = $isAppointment
            ? $productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP} > $this->now->timestamp
            : $leadCampaign->{LeadCampaign::REACTIVATE_DATE} > $this->now;

        if ($status) {
            return CampaignStatus::ACTIVE;
        } else {
            if ($temporaryPause) {
                return CampaignStatus::PAUSED_TEMPORARILY;
            } else {
                return CampaignStatus::PAUSED_PERMANENTLY;
            }
        }
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     * @throws Exception
     */
    private function transformCampaignReactivations(Collection $leadCampaigns): bool
    {
        $campaignReactivationRows = [
            CampaignReactivation::TABLE => []
        ];

        foreach($leadCampaigns as $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaign->{LeadCampaign::ID}] as $companyCampaignInfo) {
                if($leadCampaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::TYPE} === EloquentCompany::TYPE_MULTI
                && ($productCampaign = $leadCampaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN})
                && empty($leadCampaign->{LeadCampaign::STATUS})) {
                    $reactivateAt = $productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP} > $this->now->timestamp
                        ? Carbon::createFromTimestamp($productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP})->startOfDay()
                        : null;
                    $latest = $leadCampaign->{LeadCampaign::RELATION_LEAD_CAMPAIGN_PAUSE_REASONS}()->latest()->first();

                    $campaignReactivationRows[CampaignReactivation::TABLE][] = [
                        CampaignReactivation::FIELD_CAMPAIGN_ID   => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID],
                        CampaignReactivation::FIELD_REACTIVATE_AT => $reactivateAt,
                        CampaignReactivation::FIELD_REASON        => $latest?->{EloquentLeadCampaignPauseReasons::PAUSE_REASON} ?? '',
                        CampaignReactivation::CREATED_AT          => $latest?->created_at ?? null,
                    ];
                }
                else {
                    $productId = $companyCampaignInfo[self::PRODUCT_ID];

                    if ($productId === $this->leadProductId
                    && empty($leadCampaign->{LeadCampaign::STATUS})) {
                        $reactivateAt = $leadCampaign->{LeadCampaign::REACTIVATE_DATE} > $this->now
                            ? (new Carbon($leadCampaign->{LeadCampaign::REACTIVATE_DATE}))->startOfDay()
                            : null;

                        $latest = $leadCampaign->{LeadCampaign::RELATION_LEAD_CAMPAIGN_PAUSE_REASONS}()->latest()->first();

                        $campaignReactivationRows[CampaignReactivation::TABLE][] = [
                            CampaignReactivation::FIELD_CAMPAIGN_ID   => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID],
                            CampaignReactivation::FIELD_REACTIVATE_AT => $reactivateAt,
                            CampaignReactivation::FIELD_REASON        => $latest?->{EloquentLeadCampaignPauseReasons::PAUSE_REASON} ?? '',
                            CampaignReactivation::CREATED_AT          => $latest?->created_at ?? null,
                        ];
                    }
                    else if ($productId === $this->appointmentProductId
                        && ($productCampaign = $leadCampaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN})
                        && empty($productCampaign->{ProductCampaign::FIELD_STATUS})
                        && $productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP} > $this->now->timestamp) {

                        $campaignReactivationRows[CampaignReactivation::TABLE][] = [
                            CampaignReactivation::FIELD_CAMPAIGN_ID   => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID],
                            CampaignReactivation::FIELD_REACTIVATE_AT => Carbon::createFromTimestamp($productCampaign->{ProductCampaign::FIELD_REACTIVATE_AT_TIMESTAMP}, 'UTC')->format(Carbon::DEFAULT_TO_STRING_FORMAT),
                            CampaignReactivation::FIELD_REASON        => '',
                            CampaignReactivation::CREATED_AT          => $latest?->created_at ?? null,
                        ];
                    }
                }
            }
        }

        $this->saveTransformedCampaignData($campaignReactivationRows);

        return true;
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     * @throws Exception
     */
    private function transformLeadCampaignCategories(Collection $leadCampaigns): bool
    {
        $propertyTypeRows = [
            CompanyCampaignPropertyType::TABLE => []
        ];

        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {
                foreach($leadCampaign->{LeadCampaign::RELATION_LEAD_CAMPAIGN_LEAD_CATEGORIES} as $leadCampaignLeadCategory) {
                    $propertyTypeRows[CompanyCampaignPropertyType::TABLE][] = [
                        CompanyCampaignPropertyType::FIELD_COMPANY_CAMPAIGN_ID => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID],
                        CompanyCampaignPropertyType::FIELD_PROPERTY_TYPE_ID => $this->propertyTypes[PropertyTypeEnum::fromLegacyLeadCategory($leadCampaignLeadCategory->{LeadCampaignLeadCategory::LEAD_CATEGORY_ID})->value]
                    ];
                }
            }
        }

        $this->saveTransformedCampaignData($propertyTypeRows);

        return true;
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     */
    private function transformLocations(Collection $leadCampaigns): bool
    {
        $locationModuleRows = [
            CompanyCampaignLocationModule::TABLE => []
        ];

        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {
                $locationModuleRows[CompanyCampaignLocationModule::TABLE][] = [
                    CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID]
                ];
            }
        }

        $this->saveTransformedCampaignData($locationModuleRows);

        $locationModuleIds = CompanyCampaignLocationModule::query()
            ->whereIn(CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID, $this->companyCampaignIds)
            ->pluck(CompanyCampaignLocationModule::FIELD_ID, CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID)
            ->toArray();

        $locationModuleLocationRows = [
            CompanyCampaignLocationModuleLocation::TABLE => []
        ];

        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            $locationZipCodeIds = $leadCampaign
                ->{LeadCampaign::RELATION_LEAD_CAMPAIGN_LOCATIONS_LOCATIONS}
                ->where(Location::TYPE, Location::TYPE_ZIP_CODE)
                ->pluck(Location::ZIP_CODE, Location::ID)
                ->toArray();

            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {

                $locationModuleId = $locationModuleIds[$companyCampaignInfo[self::COMPANY_CAMPAIGN_ID]];

                foreach ($locationZipCodeIds as $locationId => $zipCode) {
                    $locationModuleLocationRows[CompanyCampaignLocationModuleLocation::TABLE][] = [
                        CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID => $locationModuleId,
                        CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID => $locationId,
                        CompanyCampaignLocationModuleLocation::FIELD_ZIP_CODE => $zipCode
                    ];
                }
            }
        }

        $this->saveTransformedCampaignData($locationModuleLocationRows);

        return true;
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     * @throws Exception
     */
    private function transformBudgets(Collection $leadCampaigns): bool
    {
        $budgetContainerRows = [
            BudgetContainer::TABLE => []
        ];

        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {
                $isAppt = $companyCampaignInfo[self::PRODUCT_ID] === $this->appointmentProductId;

                $budgetContainerRows[BudgetContainer::TABLE][] = [
                    BudgetContainer::FIELD_CAMPAIGN_ID  => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID],
                    BudgetContainer::FIELD_TYPE         => ($isAppt ? ContainerType::APPOINTMENTS : ContainerType::LEADS)->value
                ];
            }
        }

        $this->saveTransformedCampaignData($budgetContainerRows);

        $budgetContainerIds = BudgetContainer::query()
            ->whereIn(BudgetContainer::FIELD_CAMPAIGN_ID, $this->companyCampaignIds)
            ->pluck(BudgetContainer::FIELD_ID, BudgetContainer::FIELD_CAMPAIGN_ID)
            ->toArray();

        $budgetRows = [
            Budget::TABLE => []
        ];

        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {
                $product = $companyCampaignInfo[self::PRODUCT_ID] === $this->appointmentProductId
                    ? Product::APPOINTMENT
                    : Product::LEAD;

                $budgetProductConfigs = BudgetProductConfigurationEnum::getProductConfigWithCategory($product);

                $budgetContainerId = $budgetContainerIds[$companyCampaignInfo[self::COMPANY_CAMPAIGN_ID]];

                $campaign = $product === Product::APPOINTMENT ? $leadCampaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN} : $leadCampaign;

                $referenceInfo = $this->getBudgetReferenceInfo($campaign);

                foreach($budgetProductConfigs as $budgetProductConfig => $budgetCategory) {
                    $bpce = BudgetProductConfigurationEnum::from($budgetProductConfig);

                    $budgetRows[Budget::TABLE][] = [
                        Budget::FIELD_BUDGET_CONTAINER_ID               => $budgetContainerId,
                        Budget::FIELD_DISPLAY_NAME                      => $bpce->getDisplayName(),
                        Budget::FIELD_KEY                               => $bpce->getKey(),
                        Budget::FIELD_STATUS                            => $referenceInfo[self::BUDGET_STATUS][$budgetCategory->value],
                        Budget::FIELD_TYPE                              => $referenceInfo[self::BUDGET_TYPE][$budgetCategory->value],
                        Budget::FIELD_VALUE                             => $referenceInfo[self::BUDGET_VALUE][$budgetCategory->value],
                        Budget::FIELD_LAST_MODIFIED_AT                  => $referenceInfo[self::BUDGET_LAST_MODIFIED_AT][$budgetCategory->value],
                        Budget::FIELD_LAST_MODIFIED_BY_COMPANY_USER_ID  => 0, //no known way to get this right now
                        Budget::FIELD_PRODUCT_CONFIGURATION             => $budgetProductConfig
                    ];
                }
            }
        }

        $this->saveTransformedCampaignData($budgetRows);

        return true;
    }

    /**
     * @param LeadCampaign|ProductCampaign $campaign
     * @return array
     */
    private function getBudgetReferenceInfo(LeadCampaign|ProductCampaign $campaign): array
    {
        if($campaign instanceof LeadCampaign) {
            $unverifiedSaleTypeConfig = $campaign->{LeadCampaign::RELATION_LEAD_SALES_TYPE_CONFIGURATIONS}()->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, 5)->first();
            $emailOnlySaleTypeConfig = $campaign->{LeadCampaign::RELATION_LEAD_SALES_TYPE_CONFIGURATIONS}()->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, 6)->first();

            return [
                self::BUDGET_LAST_MODIFIED_AT => [
                    BudgetCategory::VERIFIED->value     => $campaign->{LeadCampaign::LAST_MODIFIED_LEAD_LIMIT},
                    BudgetCategory::UNVERIFIED->value   => $unverifiedSaleTypeConfig?->{LeadCampaignSalesTypeConfiguration::LAST_MODIFIED_LEAD_LIMIT} ?? null,
                    BudgetCategory::EMAIL_ONLY->value   => $emailOnlySaleTypeConfig?->{LeadCampaignSalesTypeConfiguration::LAST_MODIFIED_LEAD_LIMIT} ?? null
                ],
                self::BUDGET_TYPE => [
                    BudgetCategory::VERIFIED->value => match($campaign->getDisplayBudgetUnit()) {
                        LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED     => BudgetType::NO_LIMIT,
                        LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD          => BudgetType::TYPE_DAILY_UNITS,
                        LeadCampaign::DISPLAY_BUDGET_UNIT_CURRENCY      => BudgetType::TYPE_DAILY_SPEND
                    },
                    BudgetCategory::UNVERIFIED->value => match($unverifiedSaleTypeConfig?->getDisplayBudgetUnit() ?? LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED) {
                        LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED     => BudgetType::NO_LIMIT,
                        LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD          => BudgetType::TYPE_DAILY_UNITS,
                        LeadCampaign::DISPLAY_BUDGET_UNIT_CURRENCY      => BudgetType::TYPE_DAILY_SPEND
                    },
                    BudgetCategory::EMAIL_ONLY->value => match($emailOnlySaleTypeConfig?->getDisplayBudgetUnit() ?? LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED) {
                        LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED     => BudgetType::NO_LIMIT,
                        LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD          => BudgetType::TYPE_DAILY_UNITS,
                        LeadCampaign::DISPLAY_BUDGET_UNIT_CURRENCY      => BudgetType::TYPE_DAILY_SPEND
                    },
                ],
                self::BUDGET_VALUE => [
                    BudgetCategory::VERIFIED->value     => $campaign->getBudget(),
                    BudgetCategory::UNVERIFIED->value   => $unverifiedSaleTypeConfig?->getBudget() ?? 0,
                    BudgetCategory::EMAIL_ONLY->value   => $emailOnlySaleTypeConfig?->getBudget() ?? 0
                ],
                self::BUDGET_STATUS => [
                    BudgetCategory::VERIFIED->value     => true,
                    BudgetCategory::UNVERIFIED->value   => $unverifiedSaleTypeConfig?->{LeadCampaignSalesTypeConfiguration::STATUS} ?? 0,
                    BudgetCategory::EMAIL_ONLY->value   => $emailOnlySaleTypeConfig?->{LeadCampaignSalesTypeConfiguration::STATUS} ?? 0
                ]
            ];
        }
        else {
            $verifiedBudget = $campaign->{ProductCampaign::RELATION_BUDGETS}()->where(ProductCampaignBudget::FIELD_CATEGORY, BudgetCategory::VERIFIED->value)->first();
            $unverifiedBudget = $campaign->{ProductCampaign::RELATION_BUDGETS}()->where(ProductCampaignBudget::FIELD_CATEGORY, BudgetCategory::UNVERIFIED->value)->first();
            $emailOnlyBudget = $campaign->{ProductCampaign::RELATION_BUDGETS}()->where(ProductCampaignBudget::FIELD_CATEGORY, BudgetCategory::EMAIL_ONLY->value)->first();

            return [
                self::BUDGET_LAST_MODIFIED_AT => [
                    BudgetCategory::VERIFIED->value     => $verifiedBudget?->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP} ?? $this->now,
                    BudgetCategory::UNVERIFIED->value   => $unverifiedBudget?->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP} ?? $this->now,
                    BudgetCategory::EMAIL_ONLY->value   => $emailOnlyBudget?->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP} ?? $this->now
                ],
                self::BUDGET_TYPE => [
                    BudgetCategory::VERIFIED->value => match($verifiedBudget?->{ProductCampaignBudget::FIELD_VALUE_TYPE} ?? ProductCampaignBudget::VALUE_TYPE_NO_LIMIT) {
                        ProductCampaignBudget::VALUE_TYPE_NO_LIMIT => BudgetType::NO_LIMIT,
                        ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND => BudgetType::TYPE_DAILY_SPEND,
                        ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS => BudgetType::TYPE_DAILY_UNITS
                    },
                    BudgetCategory::UNVERIFIED->value => match($unverifiedBudget?->{ProductCampaignBudget::FIELD_VALUE_TYPE} ?? ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND) {
                        ProductCampaignBudget::VALUE_TYPE_NO_LIMIT => BudgetType::NO_LIMIT,
                        ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND => BudgetType::TYPE_DAILY_SPEND,
                        ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS => BudgetType::TYPE_DAILY_UNITS
                    },
                    BudgetCategory::EMAIL_ONLY->value => match($emailOnlyBudget?->{ProductCampaignBudget::FIELD_VALUE_TYPE} ?? ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND) {
                        ProductCampaignBudget::VALUE_TYPE_NO_LIMIT => BudgetType::NO_LIMIT,
                        ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND => BudgetType::TYPE_DAILY_SPEND,
                        ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS => BudgetType::TYPE_DAILY_UNITS
                    },
                ],
                self::BUDGET_VALUE => [
                    BudgetCategory::VERIFIED->value     => $verifiedBudget?->{ProductCampaignBudget::FIELD_VALUE} ?? 0,
                    BudgetCategory::UNVERIFIED->value   => $unverifiedBudget?->{ProductCampaignBudget::FIELD_VALUE} ?? 0,
                    BudgetCategory::EMAIL_ONLY->value   => $emailOnlyBudget?->{ProductCampaignBudget::FIELD_VALUE} ?? 0
                ],
                self::BUDGET_STATUS => [
                    BudgetCategory::VERIFIED->value     => true,
                    BudgetCategory::UNVERIFIED->value   => $unverifiedBudget?->{ProductCampaignBudget::FIELD_STATUS} ?? 0,
                    BudgetCategory::EMAIL_ONLY->value   => $emailOnlyBudget?->{ProductCampaignBudget::FIELD_STATUS} ?? 0
                ],
            ];
        }
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     * @throws Exception
     */
    private function transformBids(Collection $leadCampaigns): bool
    {
        $bidPriceModuleRows = [
            CompanyCampaignBidPriceModule::TABLE  => [],
        ];
        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {
                $bidPriceModuleRows[CompanyCampaignBidPriceModule::TABLE][] = [
                    CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID]
                ];
            }
        }

        $this->saveTransformedCampaignData($bidPriceModuleRows);

        $bidPriceModuleIds = CompanyCampaignBidPriceModule::query()
            ->whereIn(CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID, $this->companyCampaignIds)
            ->pluck(CompanyCampaignBidPriceModule::FIELD_ID, CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID)
            ->toArray();

        $productBidPriceRows = [
            ProductCountyBidPrice::TABLE => [],
            ProductStateBidPrice::TABLE  => []
        ];
        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {

                $bidPriceModuleId = $bidPriceModuleIds[$companyCampaignInfo[self::COMPANY_CAMPAIGN_ID]];

                foreach($leadCampaign->{LeadCampaign::RELATION_LEAD_PRICES} as $leadPrice) {
                    if ($leadPrice->{LeadPrice::RELATION_LOCATION}->{Location::TYPE} === Location::TYPE_STATE) {
                        $productBidPriceRows[ProductStateBidPrice::TABLE][] = [
                            ProductStateBidPrice::FIELD_STATE_LOCATION_ID   => $this->stateLocations[$leadPrice->{LeadPrice::RELATION_LOCATION}->{Location::STATE_ABBREVIATION}],
                            ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID],
                            ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID  => $companyCampaignInfo[self::SERVICE_PRODUCT_ID],
                            ProductStateBidPrice::FIELD_SALE_TYPE_ID        => $this->saleTypes[strtolower($leadPrice->{LeadPrice::RELATION_LEAD_SALES_TYPE}->{LeadSalesType::KEY_VALUE})],
                            ProductStateBidPrice::FIELD_QUALITY_TIER_ID     => $this->qualityTiers[QualityTierEnum::fromLegacyLeadType($leadPrice->{LeadPrice::LEAD_TYPE_ID})->value],
                            ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID    => $this->propertyTypes[PropertyTypeEnum::fromLegacyLeadCategory($leadPrice->{LeadPrice::LEAD_CATEGORY_ID})->value],
                            ProductStateBidPrice::FIELD_PRICE               => $leadPrice->{LeadPrice::PRICE},
                            ProductStateBidPrice::FIELD_MODULE_ID           => $bidPriceModuleId
                        ];
                    } else {
                        $productBidPriceRows[ProductCountyBidPrice::TABLE][] = [
                            ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID  => $leadPrice->{LeadPrice::LOCATION_ID},
                            ProductCountyBidPrice::FIELD_STATE_LOCATION_ID   => $this->stateLocations[$leadPrice->{LeadPrice::RELATION_LOCATION}->{Location::STATE_ABBREVIATION}],
                            ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID],
                            ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID  => $companyCampaignInfo[self::SERVICE_PRODUCT_ID],
                            ProductCountyBidPrice::FIELD_SALE_TYPE_ID        => $this->saleTypes[strtolower($leadPrice->{LeadPrice::RELATION_LEAD_SALES_TYPE}->{LeadSalesType::KEY_VALUE})],
                            ProductCountyBidPrice::FIELD_QUALITY_TIER_ID     => $this->qualityTiers[QualityTierEnum::fromLegacyLeadType($leadPrice->{LeadPrice::LEAD_TYPE_ID})->value],
                            ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID    => $this->propertyTypes[PropertyTypeEnum::fromLegacyLeadCategory($leadPrice->{LeadPrice::LEAD_CATEGORY_ID})->value],
                            ProductCountyBidPrice::FIELD_PRICE               => $leadPrice->{LeadPrice::PRICE},
                            ProductCountyBidPrice::FIELD_MODULE_ID           => $bidPriceModuleId
                        ];
                    }
                }
            }
        }

        $this->saveTransformedCampaignData($productBidPriceRows);

        if (!in_array($this->industry, [IndustryEnum::SOLAR, IndustryEnum::ROOFING]))
            $this->updateProductCampaignBids($bidPriceModuleIds);

        return true;
    }

    /**
     * @param array $bidPriceModuleIds
     *
     * @return void
     */
    protected function updateProductCampaignBids(array $bidPriceModuleIds): void
    {
        ProductCampaign::query()->whereIn(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, array_keys($this->companyCampaignInfoLeadCampaignIdsMap))
            ->get()
            ->each(function (ProductCampaign $productCampaign) use ($bidPriceModuleIds) {
                $bidPriceModuleId = $bidPriceModuleIds[$this->companyCampaignInfoLeadCampaignIdsMap[$productCampaign->parent_legacy_lead_campaign_id][0][self::COMPANY_CAMPAIGN_ID]];

                ProductStateBidPrice::query()->where(ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaign->id)
                    ->update([ProductStateBidPrice::FIELD_MODULE_ID => $bidPriceModuleId]);
                ProductCountyBidPrice::query()->where(ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaign->id)
                    ->update([ProductCountyBidPrice::FIELD_MODULE_ID => $bidPriceModuleId]);
            });
    }

    /**
     * @param Collection $leadCampaigns
     * @return bool
     * @throws Exception
     */
    private function transformDeliveryMethods(Collection $leadCampaigns): bool
    {
        $companyCampaignDeliveryModuleRows = [
            CompanyCampaignDeliveryModule::TABLE => []
        ];
        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {
                $companyCampaignDeliveryModuleRows[CompanyCampaignDeliveryModule::TABLE][] = [
                    CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID => $companyCampaignInfo[self::COMPANY_CAMPAIGN_ID]
                ];
            }
        }

        $this->saveTransformedCampaignData($companyCampaignDeliveryModuleRows);

        $deliveryModuleIds = CompanyCampaignDeliveryModule::query()
            ->whereIn(CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID, $this->companyCampaignIds)
            ->pluck(CompanyCampaignDeliveryModule::FIELD_ID, CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID)
            ->toArray();

        $companyCampaignDeliveryModuleContactRows = [
            CompanyCampaignDeliveryModuleContact::TABLE => []
        ];
        $companyCampaignDeliveryModuleCRMRows = [
            CompanyCampaignDeliveryModuleCRM::TABLE => []
        ];
        foreach($leadCampaigns as $leadCampaignId => $leadCampaign) {
            foreach($this->companyCampaignInfoLeadCampaignIdsMap[$leadCampaignId] as $companyCampaignInfo) {
                $deliveryModuleId = $deliveryModuleIds[$companyCampaignInfo[self::COMPANY_CAMPAIGN_ID]];

                $product = $companyCampaignInfo[self::PRODUCT_ID] === $this->appointmentProductId
                    ? Product::APPOINTMENT
                    : Product::LEAD;

                foreach($leadCampaign->{LeadCampaign::RELATION_LEAD_CAMPAIGN_DELIVERY_METHODS} as $leadCampaignDeliveryMethod) {
                    if($product === Product::LEAD) {
                        $emailType = LeadCampaignDeliveryMethod::TYPE_VALUE_EMAIL;
                        $smsType = LeadCampaignDeliveryMethod::TYPE_VALUE_SMS;
                    }
                    else if($product === Product::APPOINTMENT) {
                        $emailType = LeadCampaignDeliveryMethod::TYPE_VALUE_APPT_EMAIL;
                        $smsType = LeadCampaignDeliveryMethod::TYPE_VALUE_APPT_SMS;
                    }
                    else {
                        throw new Exception("Invalid product");
                    }

                    $leadDeliveryMethod = $leadCampaignDeliveryMethod->{LeadCampaignDeliveryMethod::RELATION_LEAD_DELIVERY_METHOD};
                    $activeDeliveryTypes = $leadCampaignDeliveryMethod->getTypeArray();

                    $companyCampaignDeliveryModuleContactRows[CompanyCampaignDeliveryModuleContact::TABLE][] = [
                        CompanyCampaignDeliveryModuleContact::FIELD_MODULE_ID       => $deliveryModuleId,
                        CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID      => $leadDeliveryMethod->{LeadDeliveryMethod::RELATION_COMPANY_CONTACT}?->{CompanyUser::FIELD_ID} ?? 0, //TODO optimize
                        CompanyCampaignDeliveryModuleContact::FIELD_ACTIVE          => true,
                        CompanyCampaignDeliveryModuleContact::FIELD_EMAIL_ACTIVE    => in_array($emailType, $activeDeliveryTypes, true),
                        CompanyCampaignDeliveryModuleContact::FIELD_SMS_ACTIVE      => in_array($smsType, $activeDeliveryTypes, true)
                    ];
                }

                foreach($leadCampaign->{LeadCampaign::RELATION_LEAD_CAMPAIGN_CRM_INTEGRATION} as $leadCampaignCrmIntegration) {
                    // Integrations have been migrated as CompanyCRMTemplates
                    if ($this->migrateCrmIntegrationsAsCompanyTemplates) {
                        $templateId = $this->crmTemplateIdMap[$leadCampaignCrmIntegration->{LeadCampaignCrmIntegration::CRM_INTEGRATION_ID}]
                            ?? DB::table(self::CRM_MIGRATION_TABLE)
                                ->where(self::CRM_MIGRATION_LEGACY_ID, $leadCampaignCrmIntegration->{LeadCampaignCrmIntegration::CRM_INTEGRATION_ID})
                                ->first()
                                ?->{self::CRM_MIGRATION_TEMPLATE_ID}
                            ?? null;

                        if ($templateId === null) {
                            logger()->warning("Couldn't find parent integration " . $leadCampaignCrmIntegration->{LeadCampaignCrmIntegration::CRM_INTEGRATION_ID});

                            continue;
                        }

                        $companyCampaignDeliveryModuleCRMRows[CompanyCampaignDeliveryModuleCRM::TABLE][] = [
                            CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID    => $deliveryModuleId,
                            CompanyCampaignDeliveryModuleCRM::FIELD_CRM_TYPE     => 0,
                            CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE       => true,
                            CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => '',
                            CompanyCampaignDeliveryModuleCRM::FIELD_PAYLOAD      => json_encode([]),
                            CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID  => $templateId,
                        ];
                    }
                    // Transform as Standalone deliveries
                    else {
                        $crmIntegration = $leadCampaignCrmIntegration->{LeadCampaignCrmIntegration::RELATION_CRM_INTEGRATION};
                        $crm            = $crmIntegration->{CrmIntegration::RELATION_CRM};

                        if (in_array($crm->{Crm::NAME}, array_values(CRMType::slugs()), true)
                            && strtolower($crmIntegration->{CrmIntegration::PRODUCT}) === strtolower($product->value)) {

                            $newCrmDelivery = $this->integrationTransformer->transformAsDelivery($crmIntegration, $deliveryModuleId);
                            $companyCampaignDeliveryModuleCRMRows[CompanyCampaignDeliveryModuleCRM::TABLE][] = $newCrmDelivery;
                        }
                    }
                }
            }
        }

        $this->saveTransformedCampaignData($companyCampaignDeliveryModuleContactRows);
        $this->saveTransformedCampaignData($companyCampaignDeliveryModuleCRMRows);

        return true;
    }

    /**
     * @param array $data
     * @param int $chunkSize
     * @return bool
     */
    private function saveTransformedCampaignData(array $data, int $chunkSize = 500): bool
    {
        foreach($data as $table => $rows) {
            foreach(array_chunk($rows, $chunkSize) as $rowsChunk) {
                DB::table($table)->insert($rowsChunk);
            }
        }

        return true;
    }

    /**
     * @return bool
     */
    private function updateCompanyCampaignToLeadCampaignMap(): bool
    {
        $companyCampaignIdUuid = CompanyCampaign::query()
            ->whereIn(CompanyCampaign::FIELD_REFERENCE, collect($this->companyCampaignUuidLeadCampaignIdsMap)->collapse()->toArray())
            ->pluck(CompanyCampaign::FIELD_ID, CompanyCampaign::FIELD_REFERENCE)
            ->toArray();

        $insertRows = [];
        foreach($this->companyCampaignUuidLeadCampaignIdsMap as $leadCampaignId => $companyCampaignUuids) {
            foreach($companyCampaignUuids as $companyCampaignUuid) {
                $insertRows[] = [
                    CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID   => $companyCampaignIdUuid[$companyCampaignUuid],
                    CompanyCampaignRelation::FIELD_RELATION_ID           => $leadCampaignId,
                    CompanyCampaignRelation::FIELD_RELATION_TYPE         => CampaignExternalRelationType::LEGACY_LEAD_CAMPAIGN->value,
                    CompanyCampaignRelation::CREATED_AT                  => $this->now,
                    CompanyCampaignRelation::UPDATED_AT                  => $this->now,
                ];
            }
        }

        CompanyCampaignRelation::query()->insert($insertRows);

        $this->companyCampaignUuidLeadCampaignIdsMap = [];

        $this->companyCampaignInfoLeadCampaignIdsMap = CompanyCampaignRelation::query()
            ->join(CompanyCampaign::TABLE, function($join) {
                $join->on(
                    CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_ID,
                    '=',
                    CompanyCampaignRelation::TABLE.'.'.CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID
                );
            })
            ->join(ServiceProduct::TABLE, function($join) {
                $join
                    ->on(
                        ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_PRODUCT_ID,
                        '=',
                        CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_PRODUCT_ID
                    )
                    ->on(
                        ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID,
                        '=',
                        CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_SERVICE_ID
                    );
            })
            ->whereIn(CompanyCampaignRelation::TABLE.'.'.CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID, array_values($companyCampaignIdUuid))
            ->selectRaw(implode(',', [
                CompanyCampaignRelation::TABLE.'.'.CompanyCampaignRelation::FIELD_RELATION_ID.' AS '.self::LEAD_CAMPAIGN_ID,
                CompanyCampaignRelation::TABLE.'.'.CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID.' AS '.self::COMPANY_CAMPAIGN_ID,
                CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_REFERENCE.' AS '.self::COMPANY_CAMPAIGN_UUID,
                'MIN('.ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID.') AS '.self::SERVICE_PRODUCT_ID, //prevent duplicate service products from appearing
                CompanyCampaign::TABLE.'.'.CompanyCampaign::FIELD_PRODUCT_ID.' AS '.self::PRODUCT_ID
            ]))
            ->distinct()
            ->groupBy([
                self::COMPANY_CAMPAIGN_ID,
                self::COMPANY_CAMPAIGN_UUID,
                self::PRODUCT_ID
            ])
            ->get()
            ->groupBy(self::LEAD_CAMPAIGN_ID)
            ->toArray();

        $this->companyCampaignIds = collect($this->companyCampaignInfoLeadCampaignIdsMap)->flatten(1)->pluck(self::COMPANY_CAMPAIGN_ID)->toArray();

        return true;
    }

    /**
     * @return array
     */
    private function getLowBidCampaignIds(): array
    {
        return DB::table(DatabaseHelperService::readOnlyDatabase() . '.low_bids')
            ->distinct('campaign_id')
            ->pluck('campaign_id')
            ->toArray();
    }

    /**
     * Handle migrating CRM integrations as CompanyCRMTemplates
     *
     * @param array $legacyCompanyIds
     * @return void
     */
    private function transformCompanyCrmTemplates(array $legacyCompanyIds): void
    {
        $migrationRecord = [];
        $crmSlugMap = CRMType::slugs();

        $alreadyMigrated = DB::table(self::CRM_MIGRATION_TABLE)
            ->pluck(self::CRM_MIGRATION_LEGACY_ID)
            ->toArray();

        $integrations = CrmIntegration::query()
            ->join(Crm::TABLE, Crm::TABLE .'.'. Crm::ID, '=', CrmIntegration::TABLE .'.'. CrmIntegration::CRM_ID)
            ->select(CrmIntegration::TABLE .'.*')
            ->whereIn(CrmIntegration::TABLE .'.'. CrmIntegration::COMPANY_ID, $legacyCompanyIds)
            ->whereNotIn(CrmIntegration::TABLE .'.'. CrmIntegration::ID, $alreadyMigrated)
            ->whereIn(Crm::TABLE .'.'. Crm::NAME, $crmSlugMap);

        $integrations = $integrations->get();

        foreach($integrations as $integration) {
            $companyId = $this->companyIdMap[$integration->company_id];
            $newTemplate = $this->integrationTransformer->transform($integration, $companyId);
            if (!$newTemplate)
                continue;

            $newId = DB::table(CompanyCRMTemplate::TABLE)
                ->insertGetId($newTemplate);

            $migrationRecord[] = [
                self::CRM_MIGRATION_LEGACY_ID   => $integration->{CrmIntegration::ID},
                self::CRM_MIGRATION_TEMPLATE_ID => $newId,
                self::CRM_MIGRATION_COMPANY_ID  => $companyId,
            ];

            $this->crmTemplateIdMap[$integration->{CrmIntegration::ID}] = $newId;
        }

        DB::table(self::CRM_MIGRATION_TABLE)
            ->insert($migrationRecord);
    }
}
