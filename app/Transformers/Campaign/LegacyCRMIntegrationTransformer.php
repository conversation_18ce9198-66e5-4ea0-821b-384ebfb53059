<?php

namespace App\Transformers\Campaign;

use App\Campaigns\Delivery\CRM\BaseCRMDeliverer;
use App\Campaigns\Delivery\CRM\BaseInteractableCRMDeliverer;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Campaigns\Delivery\CRM\ParseableEmail\ParseableEmailCRMDeliveryService;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Legacy\Crm;
use App\Models\Legacy\CrmIntegration;
use App\Models\Legacy\EloquentIntegrationField;
use App\Services\Campaigns\CRMIntegrations\Pipedrive\PipedriveCRMService;
use Illuminate\Support\Collection;

class LegacyCRMIntegrationTransformer
{
    const string SYSTEM_FIELDS_KEY       = BaseCRMDeliverer::SYSTEM_FIELDS_KEY;
    const string ADDITIONAL_FIELDS_KEY   = BaseCRMDeliverer::ADDITIONAL_FIELDS_KEY;
    const string CUSTOM_FIELDS_KEY       = BaseCRMDeliverer::CUSTOM_FIELDS_KEY;
    const string JSON_FIELDS_KEY         = BaseCRMDeliverer::JSON_FIELDS_KEY;
    const string HEADERS_KEY             = BaseCRMDeliverer::HEADERS_KEY;
    const string INTERACTABLE_FIELDS_KEY = BaseInteractableCRMDeliverer::INTERACTABLE_FIELDS_KEY;

    protected array $crmSlugMap;
    protected array $crmLegacyIdMap;
    protected array $fieldConfigurations;

    /** Map of A2 payload keys => legacy payload keys, where different */
    protected array $fieldGroupKeyMap = [
        'default' => [
            BaseCRMDeliverer::ADDITIONAL_FIELDS_KEY => 'fields',
            BaseCRMDeliverer::HEADERS_KEY           => 'header_fields',
        ],
        CRMType::PARSEABLE_EMAIL->value => [
            BaseCRMDeliverer::ADDITIONAL_FIELDS_KEY => 'misc_fields',
        ],
    ];

    /** Valid field group keys for CRM-supplied interactable fields */
    protected array $interactableFieldsKeyMap = [
        CRMType::PIPEDRIVE->value => [
            'person_fields' => PipedriveCRMService::FIELD_CATEGORY_PERSON,
            'detail_fields' => PipedriveCRMService::FIELD_CATEGORY_DEAL,
            'misc_fields'   => PipedriveCRMService::FIELD_CATEGORY_OTHER,
        ],
    ];

    /**
     * Fields which have been remapped in system/additional fields
     */
    protected array $fieldReMappings = [
        CRMType::STANDARD_WEB_FORM->value => [
            self::SYSTEM_FIELDS_KEY => [
                'success_response' => 'success_keyword',
                'error_response'   => 'error_keyword',
                'sendformat'       => 'send_format',
            ],
        ],
        CRMType::LEAD_CONDUIT->value      => [
            self::ADDITIONAL_FIELDS_KEY => [
                'address_1'   => 'address',
                'num_buyers'  => 'number_of_buyers',
                'phone_1'     => 'phone',
                'postal_code' => 'zip_code',
            ],
        ],
        CRMType::INSIGHTLY->value         => [
            self::ADDITIONAL_FIELDS_KEY => [
                'PHONE_NUMBER'  => 'PHONE',
                'EMAIL_ADDRESS' => 'EMAIL',
            ]
        ],
        CRMType::JOB_NIMBUS->value         => [
            self::ADDITIONAL_FIELDS_KEY => [
                'mobile_phone'  => 'phone_number',
                'address_line1' => 'address_line_1',
                'address_line2' => 'address_line_2',
                'state_text'    => 'state',
                'zip'           => 'zip_code',
            ]
        ],
        CRMType::PARSEABLE_EMAIL->value   => [
            self::SYSTEM_FIELDS_KEY => [
                'recipient_email' => ParseableEmailCRMDeliveryService::SYSTEM_FIELD_EMAIL,
            ]
        ],
    ];

    /** Keys which need a default value in A2 if absent from payload */
    protected array $defaultValues = [
        CRMType::STANDARD_WEB_FORM->value => [
            self::SYSTEM_FIELDS_KEY => [
                'send_format' => 'POST',
            ],
        ],
        CRMType::INSIGHTLY->value => [
            self::SYSTEM_FIELDS_KEY => [
                'api_version' => 21
            ]
        ]
    ];

    /** There are still some very-legacy field replacer codes in the DB */
    protected array $shortcodeReMappings = [
        '[leadsource]'                 => '[lead_source]',
        '[firstname]'                  => '[first_name]',
        '[lastname]'                   => '[last_name]',
        '[accountname]'                => '[account_name]',
        '[fulladdress]'                => '[address]',
        '[state]'                      => '[state_abbr]',
        '[state_full]'                 => '[state]',
        '[zipcode]'                    => '[zip_code]',
        '[electric]'                   => '[electric_spend]',
        '[utility]'                    => '[utility_name]',
        '[leadid]'                     => '[lead_id]',
        '[universalleadid]'            => '[universal_lead_id]',
        '[leadcost]'                   => '[lead_price]',
        '[numberofcompanies]'          => '[lead_sale_count]',
        '[country_long]'               => '[country]',
        '[number_of_quotes_requested]' => '[lead_sale_count]',
    ];

    /** Do not run value remapping (for shortcodes) on these groups */
    protected array $disableValueReMapping = [
        BaseCRMDeliverer::SYSTEM_FIELDS_KEY,
    ];

    /**
     * Any fields which need their values mutated before storage
     */
    protected array $mutators = [];

    /** Has Interactable fields (is extended from BaseInteractableCRM) */
    protected array $hasInteractableFields = [
        CRMType::PIPEDRIVE,
    ];

    /** Has no custom fields */
    protected array $noCustomFields = [
        CRMType::PIPEDRIVE,
    ];

    /** Has custom JSON fields */
    protected array $hasCustomJsonFields = [
        CRMType::STANDARD_WEB_FORM,
    ];

    /** There's a pattern emerging here... */
    protected array $hasCustomHeaders = [
        CRMType::STANDARD_WEB_FORM,
    ];

    //TODO remove ids from constructor when done testing
    // these are for running the transformer from the console command
    public function __construct(
        protected bool $transformAsCompanyCrmTemplates = true,
        protected ?int $companyId = null,
        protected ?int $deliveryModuleId = null,
    )
    {
        $slugs = CRMType::slugs();
        foreach ($slugs as $crmType => $legacySlug) {
            $this->crmSlugMap[$legacySlug] = CRMType::from($crmType);
        }
        $this->crmLegacyIdMap = Crm::all()
            ->mapWithKeys(fn($crm) => [$crm->id => $crm->name])
            ->toArray();

        $this->mutators = [
            CRMType::STANDARD_WEB_FORM->value => [
                self::SYSTEM_FIELDS_KEY => [
                    'send_format' => fn(string $v) => preg_match("/json/i", $v) ? 'JSON' : 'POST',
                ],
            ],
        ];

        $this->getFieldKeysFromConfigurations();
    }

    /**
     * @param CrmIntegration $crmIntegration
     * @param int|null $companyId
     * @param int|null $moduleId
     * @return ?array
     */
    public function transform(CrmIntegration $crmIntegration, ?int $companyId = null, ?int $moduleId = null): ?array
    {
        return $this->transformAsCompanyCrmTemplates
            ? $this->transformAsTemplate($crmIntegration, $companyId)
            : $this->transformAsDelivery($crmIntegration, $moduleId);
    }

    /**
     * @param Collection<CrmIntegration> $crmIntegrations
     * @return Collection<array>
     */
    public function transformCollection(Collection $crmIntegrations, ?int $companyId = null, ?int $moduleId = null): Collection
    {
        return $crmIntegrations->map(fn($crmIntegration) => $this->transform($crmIntegration, $companyId, $moduleId));
    }

    /**
     * @param CrmIntegration $crmIntegration
     * @param int|null $moduleId
     * @return ?array
     */
    public function transformAsDelivery(CrmIntegration $crmIntegration, ?int $moduleId): ?array
    {
        $crmType = $this->getCrmType($crmIntegration);
        $payload = $this->transformPayload($crmIntegration, $crmType);
        if (!$payload) return null;

        return [
            CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID    => $moduleId ?? $this->deliveryModuleId,
            CompanyCampaignDeliveryModuleCRM::FIELD_CRM_TYPE     => $crmType->value,
            CompanyCampaignDeliveryModuleCRM::FIELD_DISPLAY_NAME => $crmIntegration->{CrmIntegration::NAME},
            CompanyCampaignDeliveryModuleCRM::FIELD_PAYLOAD      => $payload,
            CompanyCampaignDeliveryModuleCRM::FIELD_ACTIVE       => true,
            CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID  => null,
            CompanyCampaignDeliveryModuleCRM::CREATED_AT         => $crmIntegration->created_at ?? now(),
            CompanyCampaignDeliveryModuleCRM::UPDATED_AT         => now(),
        ];
    }

    /**
     * @param CrmIntegration $crmIntegration
     * @param int|null $companyId
     * @return ?array
     */
    public function transformAsTemplate(CrmIntegration $crmIntegration, ?int $companyId = null): ?array
    {
        $crmType = $this->getCrmType($crmIntegration);
        $payload = $this->transformPayload($crmIntegration, $crmType);
        if (!$payload) return null;

        return [
            CompanyCRMTemplate::FIELD_CRM_TYPE     => $crmType->value,
            CompanyCRMTemplate::FIELD_DISPLAY_NAME => $crmIntegration->{CrmIntegration::NAME},
            CompanyCRMTemplate::FIELD_PAYLOAD      => $payload,
            CompanyCRMTemplate::FIELD_COMPANY_ID   => $companyId ?? $this->companyId,
            CompanyCRMTemplate::CREATED_AT         => $crmIntegration->created_at ?? now(),
            CompanyCRMTemplate::UPDATED_AT         => now(),
        ];
    }

    /**
     * @param CrmIntegration $crmIntegration
     * @return CRMType
     */
    protected function getCrmType(CrmIntegration $crmIntegration): CRMType
    {
        $slug = $this->crmLegacyIdMap[$crmIntegration->crm_id] ?? null;
        return $this->crmSlugMap[$slug];
    }

    /**
     * Transform the integrations whole payload
     */
    protected function transformPayload(CrmIntegration $crmIntegration, CRMType $crmType): ?string
    {
        $basePayload = [];
        if (!in_array($crmType, $this->noCustomFields))
            $basePayload[self::CUSTOM_FIELDS_KEY] = [];
        if (in_array($crmType, $this->hasInteractableFields))
            $basePayload[self::INTERACTABLE_FIELDS_KEY] = [];
        if (in_array($crmType, $this->hasCustomJsonFields))
            $basePayload[self::JSON_FIELDS_KEY] = [];
        if (in_array($crmType, $this->hasCustomHeaders))
            $basePayload[self::HEADERS_KEY] = [];

        $orphanedAdditionalFields = [];

        $legacyFieldGroups = $crmIntegration->integrationFieldsMany()
            ->where(EloquentIntegrationField::REL_TYPE, EloquentIntegrationField::REL_TYPE_CRM_INTEGRATION_ID)
            ->get();

        // Process System Fields
        $systemFieldKey = $this->getLegacyFieldGroupKey(self::SYSTEM_FIELDS_KEY, $crmType);
        $systemFieldModel = $legacyFieldGroups->first(fn(EloquentIntegrationField $field) => $field->name === $systemFieldKey);
        if (!$systemFieldModel) {
            logger()->warning("Bad Integration, no system fields. Skipping migration for id ", [$crmIntegration->{CrmIntegration::ID}]);

            return null;
        }
        $basePayload[self::SYSTEM_FIELDS_KEY] = $this->transformFieldGroup(self::SYSTEM_FIELDS_KEY, $systemFieldModel, $crmType);

        // Process Additional Fields
        $additionalFieldKey   = $this->getLegacyFieldGroupKey(self::ADDITIONAL_FIELDS_KEY, $crmType);
        $additionalFieldModel = $legacyFieldGroups->first(fn(EloquentIntegrationField $field) => $field->name === $additionalFieldKey);
        if ($additionalFieldModel)
            $basePayload[self::ADDITIONAL_FIELDS_KEY] = $this->transformFieldGroup(self::ADDITIONAL_FIELDS_KEY, $additionalFieldModel, $crmType, false, $orphanedAdditionalFields);

        // Process Custom Fields
        if (array_key_exists(self::CUSTOM_FIELDS_KEY, $basePayload)) {
            $customFieldKey                       = $this->getLegacyFieldGroupKey(self::CUSTOM_FIELDS_KEY, $crmType);
            $customFieldModel                     = $legacyFieldGroups->first(fn(EloquentIntegrationField $field) => $field->name === $customFieldKey);
            $customFieldModel                     = $customFieldModel ?? new EloquentIntegrationField();
            $basePayload[self::CUSTOM_FIELDS_KEY] = $this->transformFieldGroup(self::CUSTOM_FIELDS_KEY, $customFieldModel, $crmType, true, $orphanedAdditionalFields, true);
        }

        // Process JSON fields
        if (array_key_exists(self::JSON_FIELDS_KEY, $basePayload)) {
            $jsonFieldKey   = $this->getLegacyFieldGroupKey(self::JSON_FIELDS_KEY, $crmType);
            $jsonFieldModel = $legacyFieldGroups->first(fn(EloquentIntegrationField $field) => $field->name === $jsonFieldKey);
            if ($jsonFieldModel)
                $basePayload[self::JSON_FIELDS_KEY] = $this->transformFieldGroup(self::JSON_FIELDS_KEY, $jsonFieldModel, $crmType, true);
        }

        // Process custom Headers
        if (array_key_exists(self::HEADERS_KEY, $basePayload)) {
            $headerFieldKey   = $this->getLegacyFieldGroupKey(self::HEADERS_KEY, $crmType);
            $headerFieldModel = $legacyFieldGroups->first(fn(EloquentIntegrationField $field) => $field->name === $headerFieldKey);
            if ($headerFieldModel)
                $basePayload[self::HEADERS_KEY] = $this->transformFieldGroup(self::HEADERS_KEY, $headerFieldModel, $crmType, true);
        }

        // Process Interactable fields
        if (array_key_exists(self::INTERACTABLE_FIELDS_KEY, $basePayload)) {
            $interactableGroupKeys = $this->interactableFieldsKeyMap[$crmType->value] ?? null;
            if ($interactableGroupKeys) {
                $validFieldGroups = $legacyFieldGroups->filter(fn(EloquentIntegrationField $field) => array_key_exists($field->name, $interactableGroupKeys));
                foreach ($validFieldGroups as $interactableFieldGroup) {
                    $mappedKeyName                                              = $interactableGroupKeys[$interactableFieldGroup->name];
                    $basePayload[self::INTERACTABLE_FIELDS_KEY][$mappedKeyName] = $this->transformFieldGroup($mappedKeyName, $interactableFieldGroup, $crmType, true);
                }
            }
        }

        return json_encode($basePayload);
    }

    /**
     * Transform a whole field group e.g. system_fields
     */
    protected function transformFieldGroup(string $fieldGroupKey, EloquentIntegrationField $fieldGroupModel, CRMType $crmType, ?bool $allowAllKeys = false, ?array &$orphanedFields = null, ?bool $consumeOrphanedFields = false): array
    {
        $output        = [];
        $defaultKeys   = $this->fieldConfigurations[$crmType->value][$fieldGroupKey] ?? [];
        $remappedKeys  = $this->getFieldRemap($fieldGroupKey, $crmType);
        $defaultValues = $this->getFieldDefaults($fieldGroupKey, $crmType);
        $mutators      = $this->getMutators($fieldGroupKey, $crmType);
        $legacyPayload = json_decode($fieldGroupModel[EloquentIntegrationField::VALUE], true) ?? [];

        foreach ($legacyPayload as $payloadKey => $payloadValue) {
            $key = $remappedKeys[$payloadKey] ?? $payloadKey;
            // Mutate value if required
            if ($mutators && array_key_exists($key, $mutators))
                $payloadValue = $mutators[$key]($payloadValue);
            // Check for very legacy shortcodes, replace if needed
            if (!in_array($fieldGroupKey, $this->disableValueReMapping))
                $payloadValue = $this->reMapVeryLegacyShortcodes($payloadValue ?? '');

            if ($allowAllKeys || in_array($key, $defaultKeys))
                $output[] = [
                    BaseCRMDeliverer::CRM_FIELD_KEY   => $key,
                    BaseCRMDeliverer::CRM_FIELD_VALUE => $payloadValue,
                ];
            else if ($orphanedFields !== null && !$consumeOrphanedFields)
                $orphanedFields[$payloadKey] = $payloadValue;
        }

        // This primarily appends orphaned Additional Fields from legacy to Custom Fields
        //  so they should still appear on the payload if field names change and are not remapped already
        if ($orphanedFields && $consumeOrphanedFields) {
            foreach ($orphanedFields as $fieldKey => $fieldValue) {
                if (!count(array_filter($output, fn($v) => $v[BaseCRMDeliverer::CRM_FIELD_KEY] === $fieldKey)))
                    $output[] = [
                        BaseCRMDeliverer::CRM_FIELD_KEY   => $fieldKey,
                        BaseCRMDeliverer::CRM_FIELD_VALUE => $fieldValue,
                    ];
            }

            $orphanedFields = [];
        }

        // Apply any default values still absent from output
        if ($defaultValues) {
            foreach ($defaultValues as $fieldKey => $defaultValue) {
                if (!count(array_filter($output, fn($v) => $v[BaseCRMDeliverer::CRM_FIELD_KEY] === $fieldKey))) {
                    $output[] = [
                        BaseCRMDeliverer::CRM_FIELD_KEY   => $fieldKey,
                        BaseCRMDeliverer::CRM_FIELD_VALUE => $defaultValue,
                    ];
                }
            }
        }

        return $output;
    }

    /**
     * @param string $originalValue
     * @return string
     */
    protected function reMapVeryLegacyShortcodes(string $originalValue): string
    {
        return array_key_exists($originalValue, $this->shortcodeReMappings)
            ? $this->shortcodeReMappings[$originalValue]
            : $originalValue;
    }

    /**
     * Get any remapped keys for this crm_type/field group
     */
    protected function getFieldRemap(string $fieldGroupKey, CRMType $crmType): array
    {
        if (array_key_exists($crmType->value, $this->fieldReMappings)) {
            if (array_key_exists($fieldGroupKey, $this->fieldReMappings[$crmType->value])) {
                return $this->fieldReMappings[$crmType->value][$fieldGroupKey];
            }
        }

        return [];
    }

    /**
     * Get any default values A2 needs if payload is missing the key
     */
    protected function getFieldDefaults(string $fieldGroupKey, CRMType $crmType): array
    {
        if (array_key_exists($crmType->value, $this->defaultValues)) {
            if (array_key_exists($fieldGroupKey, $this->defaultValues[$crmType->value])) {
                return $this->defaultValues[$crmType->value][$fieldGroupKey];
            }
        }

        return [];
    }

    /**
     * Get any required mutators for this crm_type / field group
     */
    protected function getMutators(string $fieldGroupKey, CRMType $crmType): ?array
    {
        if (array_key_exists($crmType->value, $this->mutators)) {
            if (array_key_exists($fieldGroupKey, $this->mutators[$crmType->value])) {
                return $this->mutators[$crmType->value][$fieldGroupKey];
            }
        }

        return null;
    }

    /**
     * Get the legacy field group key if not the same as A2
     */
    protected function getLegacyFieldGroupKey(string $fieldGroupKey, CRMType $crmType): string
    {
        $targetMap = $this->fieldGroupKeyMap[$crmType->value] ?? $this->fieldGroupKeyMap['default'];

        return $targetMap[$fieldGroupKey] ?? $fieldGroupKey;
    }

    protected function getFieldKeysFromConfigurations(): void
    {
        $allConfigurations = CRMType::getAllFieldConfigurations();
        foreach ($allConfigurations as $fieldConfiguration) {
            $this->fieldConfigurations[$fieldConfiguration['id']] = [
                self::SYSTEM_FIELDS_KEY       => $this->getFieldKeys($fieldConfiguration[self::SYSTEM_FIELDS_KEY] ?? null),
                self::ADDITIONAL_FIELDS_KEY   => $this->getFieldKeys($fieldConfiguration[self::ADDITIONAL_FIELDS_KEY] ?? null),
                self::CUSTOM_FIELDS_KEY       => $this->getFieldKeys($fieldConfiguration[self::CUSTOM_FIELDS_KEY] ?? null),
                self::HEADERS_KEY             => $this->getFieldKeys($fieldConfiguration[self::HEADERS_KEY] ?? null),
                self::JSON_FIELDS_KEY         => $this->getFieldKeys($fieldConfiguration[self::JSON_FIELDS_KEY] ?? null),
                self::INTERACTABLE_FIELDS_KEY => $this->getFieldKeys($fieldConfiguration[self::INTERACTABLE_FIELDS_KEY] ?? null),
            ];
        }
    }

    protected function getFieldKeys(?array $fieldConfiguration): ?array
    {
        return $fieldConfiguration
            ? array_map(fn($field) => $field[BaseCRMDeliverer::CRM_FIELD_KEY], $fieldConfiguration)
            : null;
    }
}
