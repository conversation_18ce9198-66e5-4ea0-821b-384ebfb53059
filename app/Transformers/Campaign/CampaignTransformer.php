<?php

namespace App\Transformers\Campaign;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLeadCategory;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Number;

class CampaignTransformer
{
    /**
     * Handles transforming a collection of lead campaigns to their client side representation.
     *
     * @param Collection $campaigns
     * @return array
     */
    public function transformCampaigns(Collection $campaigns): array
    {
        $filteredCampaigns = $campaigns->filter();

        $filteredCampaigns->loadMissing([
            LeadCampaign::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_PRODUCT,
            LeadCampaign::RELATION_PRODUCT_CAMPAIGN.'.'.ProductCampaign::RELATION_BUDGETS,
        ]);

        return $campaigns->map(function (LeadCampaign $campaign) {
            return $this->transformCampaign($campaign);
        })->values()->toArray();
    }

    /**
     * Handles transforming a single lead campaign to its client side representation.
     *
     * @param LeadCampaign|null $campaign
     * @return array
     */
    public function transformCampaign(?LeadCampaign $campaign): array
    {
        if($campaign === null) {
            return [];
        }

        $appointmentCampaign = $campaign->productCampaign()
            ->whereHas(ProductCampaign::RELATION_PRODUCT, fn($query) => $query->where(Product::FIELD_NAME, ProductEnum::APPOINTMENT->value))
            ->first();

        return [
            "id" => $campaign->{LeadCampaign::ID},
            "uuid" => $campaign->{LeadCampaign::UUID},
            "name" => $campaign->{LeadCampaign::NAME},
            "lead_status" => $campaign->{LeadCampaign::STATUS},
            "appointment_status" => $appointmentCampaign?->{ProductCampaign::FIELD_STATUS} ?? 0,
            "zipcode_count" => $campaign->leadCampaignZipLocations()->count(),
            "state_count" => $campaign->leadCampaignLocationLocations()->pluck(Location::STATE_KEY)->unique()->count(),
            "utility_count" => $campaign->leadCampaignUtilities()->count(),
            "lead_budget" => $campaign->formatBudget(),
            "appointment_budgets" => $appointmentCampaign?->{ProductCampaign::RELATION_BUDGETS}->map(function(ProductCampaignBudget $budget) use ($campaign) {
                $budgetUsageKey = strtolower("{$budget->{ProductCampaignBudget::FIELD_QUALITY_TIER}->name}_budget_usage") ?? '';
                $budgetUsagePercentageKey = strtolower("{$budget->{ProductCampaignBudget::FIELD_QUALITY_TIER}->name}_budget_usage_percentage") ?? 0;

                $budgetUsage = $campaign->{$budgetUsageKey} ?? 0;
                $budgetUsagePercentage = $campaign->{$budgetUsagePercentageKey} ?? '';

                return [
                    "type" => $budget->{ProductCampaignBudget::FIELD_QUALITY_TIER}->value,
                    "budget" => $budget->formatted_budget,
                    "status" => $budget->{ProductCampaignBudget::FIELD_STATUS},
                    "budget_usage" => $budgetUsage,
                    "budget_usage_percentage" => $budgetUsagePercentage,
                    "budget_start_time" => Carbon::parse($budget->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP}, 'UTC')->format('l, F j, Y H:i')." UTC"
                ];
            }),
            "sales_types" => $campaign->leadSalesTypeConfigurations()->where(LeadCampaignSalesTypeConfiguration::STATUS, 1)->get()
                ->map(function ($item) {
                    /** @var LeadCampaignSalesTypeConfiguration $item */
                    return [
                        'sales_type' => $item->lead_sales_type_display,
                        'limit'      => $item->formatBudget()
                    ];
                }),
            "lead_categories" => $campaign->leadCampaignLeadCategories->map(function ($item) {
                /** @var LeadCampaignLeadCategory $item */
                return $item->leadCategory()->pluck(LeadCategory::NAME);
            }),
            "appointments_active" => $campaign->{LeadCampaign::RELATION_COMPANY}?->{EloquentCompany::RELATION_MI_COMPANY}?->{Company::RELATION_CONFIGURATION}?->{CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE} ?? false,
            "industry" => $campaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN}?->{ProductCampaign::RELATION_SERVICE}?->{IndustryService::RELATION_INDUSTRY}?->name ?? ($campaign->company?->isTypeRoofer() ? 'Roofer' : 'Solar'),
            "service" => $campaign->{LeadCampaign::RELATION_PRODUCT_CAMPAIGN}?->{ProductCampaign::RELATION_SERVICE}?->name,
            "status" => $this->getLeadCampaignStatusString($campaign),
            "reactivate_at" => !empty($campaign->{LeadCampaign::REACTIVATE_DATE}) ? Carbon::parse($campaign->{LeadCampaign::REACTIVATE_DATE})->timezone('MST')->format('M d, Y h:i A (T)') : null
        ];
    }

    private function getLeadCampaignStatusString(LeadCampaign $campaign): string
    {
        if ($campaign->status === LeadCampaign::STATUS_ACTIVE) {
            $statusString = "Active";
        } else {
            if (!empty($campaign->{LeadCampaign::REACTIVATE_DATE})) {
                $statusString = "Paused Temporarily";
            } else {
                $statusString = "Paused Permanently";
            }
        }

        return $statusString;
    }
}
