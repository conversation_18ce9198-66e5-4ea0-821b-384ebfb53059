<?php

namespace App\Transformers\ContractorProfile;

use App\Builders\CompanyConsumerReviewBuilder;
use App\Enums\CompanyMediaAssetType;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\ReviewStatus;
use App\Http\Controllers\ContractorProfileController;
use App\Http\Resources\Reviews\ContractorProfileRatingsResource;
use App\Http\Resources\Reviews\ContractorProfileReviewResource;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyMediaAsset;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Repositories\CompanyRatingRepository;
use App\Repositories\ContractorProfileRepository;
use App\Repositories\Odin\CompanyMediaAssetRepository;
use App\Services\CompanyServiceAreaService;
use App\Services\IndustryServicesService;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Http\Request;


class ContractorProfileCompanyTransformer
{

    public function __construct(
        protected CompanyServiceAreaService   $serviceAreaService,
        protected ContractorProfileRepository $profileRepository,
        protected IndustryServicesService     $industryServicesService,
        protected CompanyMediaAssetRepository $mediaRepository,
        protected CompanyRatingRepository     $companyRatingRepository,
    ) {}

    /**
     * @param string $serviceKebab
     * @return string
     */
    private static function unKebab(string $serviceKebab): string
    {
        return ucwords(str_replace('-', ' ', $serviceKebab));
    }

    /**
     * @param Company $company
     * @return array
     */
    public function transform(Company $company, Request $request): array
    {
        $businessHours = $this->profileRepository->getBusinessHours($company->id);
        $media         = $this->media($company);

        return [
            'name'                  => $company->name,
            'id'                    => $company->id,
            'logo'                  => $company->link_to_logo,
            'office_location'       => $this->officeLocation($company),
            'updated_at'            => $company->updated_at,
            'timezone'              => $businessHours[ContractorProfileController::PARAM_TIMEZONE],
            'operating_hours'       => $businessHours[ContractorProfileController::PARAM_TIMES],
            'type'                  => $company->legacyCompany?->type ?? "",
            'in_business_since'     => $company->data->getDatumByGlobalConfigField(GlobalConfigurableFields::YEAR_STARTED_BUSINESS),
            'company_video_url'     => count($media['links']) > 0 ? $media['links'][0] : null,
            'verified'              => $this->companyVerified($company),
            'reviews'               => $this->reviewData($company, $request),
            'ratings'               => $this->ratingsData($company),
            'expert_rating'         => $company->contractorProfile?->rating,
            'expert_rating_factors' => $this->expertRatingFactors($company), // todo
            'images'                => count($media['images']) > 0 ? $media['images'] : [],
            'industries'            => $company->industries->pluck(Industry::FIELD_NAME)->toArray(),
            'services'              => $this->services($company),
            'brands_sold'           => $this->brandsSold($company),
            'service_areas'         => $this->serviceAreas($company),
            'certifications'        => $company->contractorProfile?->certifications ?? [],
            'licenses'              => $company->contractorProfile?->licenses ?? [],
            'introduction'          => $company->contractorProfile?->introduction,
            'customers_like'        => $company->contractorProfile?->customers_like,
            'customers_dislike'     => $company->contractorProfile?->customers_dislike,
            'website'               => $company->website ?? "",
            'prescreened'           => $company->prescreened() ?? false,
        ];
    }

    /**
     * @param Company $company
     * @return bool
     */
    private function companyVerified(Company $company): bool
    {
        return ($company->data?->getDatumByGlobalConfigField(GlobalConfigurableFields::EMAIL_VALIDATED) ||
                $company->data?->getDatumByGlobalConfigField(GlobalConfigurableFields::PHONE_VALIDATED));
    }

    /**
     * @param Company $company
     * @return ContractorProfileRatingsResource
     */
    public function ratingsData(Company $company): ContractorProfileRatingsResource
    {
        $ratings = $this->companyRatingRepository->getLatestCompanyRating($company->id) ?? collect();
        return new ContractorProfileRatingsResource($ratings);
    }

    /**
    * @param Company $company
    * @return LengthAwarePaginator
    */
    public function reviewData(Company $company, Request $request): LengthAwarePaginator
    {
        return ContractorProfileReviewResource::collection($this->getPaginatedProfileReviews($request, $company))->resource;
    }

    /**
     * @param Company $company
     * @return LengthAwarePaginator
     */
    protected function getPaginatedProfileReviews(Request $request, Company $company, ?int $perPage = 5): LengthAwarePaginator
    {
        $query = CompanyConsumerReviewBuilder::query()
            ->forCompanyId($company->id)
            ->forVerifiedStatus(true)
            ->forStatus(ReviewStatus::APPROVED->value);

        if ($request->filled('rating')) {
            $query->forScore((int) $request->input('rating'));
        }

        return $query
            ->getQuery()
            ->paginate($perPage)
            ->appends($request->query());
    }

    /**
     * @param Company $company
     * @return array[]
     */
    private function expertRatingFactors(Company $company): array
    {
        // todo
        return [
//            ['name' => 'Time in  business', 'score' => 5, 'possible_score' => 10],
//            ['name' => 'Verification of license and insurance', 'score' => 2, 'possible_score' => 10],
//            ['name' => 'Consumer reviews performance', 'score' => 6, 'possible_score' => 10],
//            ['name' => 'Company size and location', 'score' => 8, 'possible_score' => 10],
//            ['name' => 'Vertical integration', 'score' => 9, 'possible_score' => 10],
//            ['name' => 'Competitiveness of loan options', 'score' => 9, 'possible_score' => 10],
//            ['name' => 'Employee satisfaction and safety record', 'score' => 5, 'possible_score' => 10],
//            ['name' => 'Litigation and background', 'score' => 4, 'possible_score' => 10],
//            ['name' => 'Profitability of installer', 'score' => 3, 'possible_score' => 10],
//            ['name' => 'Transparency of pricing and sales process', 'score' => 2, 'possible_score' => 10],
//            ['name' => 'Quality of brands sold', 'score' => 1, 'possible_score' => 10],
//            ['name' => 'Transparency about reputation', 'score' => 6, 'possible_score' => 10],
//            ['name' => 'Sustainable pricing of systems', 'score' => 8, 'possible_score' => 10],
        ];
    }

    /**
     * @param Company $company
     * @return string[]
     */
    private function services(Company $company): array
    {
        return array_map(fn($serviceKebab) => self::unKebab($serviceKebab), $company->contractorProfile->services ?? []) ??
               $company->services->unique(IndustryService::FIELD_ID)->pluck(IndustryService::FIELD_NAME)->toArray();
    }

    /**
     * @param Company $company
     * @return string[]
     */
    private function brandsSold(Company $company): array
    {
        return $company->contractorProfile?->brands_sold ?? [];
    }

    /**
     * @param Company $company
     * @return array
     */
    private function serviceAreas(Company $company): array
    {
        $states = $this->serviceAreaService->getServicedStates($company);
        return $states->pluck(Location::STATE)->toArray();
    }

    /**
     * @param Company $company
     * @return array
     */
    private function media(Company $company): array
    {
        $media = $this->mediaRepository->getAllMediaAssets(
            $company->id,
            [CompanyMediaAssetType::MEDIA, CompanyMediaAssetType::LINK,],
            CompanyMediaAsset::FIELD_ID, 'asc'
        );
        return [
            'images' => $media->filter(fn(CompanyMediaAsset $asset) => $asset->type == CompanyMediaAssetType::MEDIA->value)->pluck(CompanyMediaAsset::FIELD_URL)->toArray(),
            'links'  => $media->filter(fn(CompanyMediaAsset $asset) => $asset->type == CompanyMediaAssetType::LINK->value)->pluck(CompanyMediaAsset::FIELD_URL)->toArray()
        ];
    }

    private function officeLocation(Company $company): array
    {
        $location = $company->locations()->where(CompanyLocation::FIELD_IS_PRIMARY)->first() ?? $company->locations()->first() ?? null;

        return [
            'full_address'             => $location?->address?->getFullAddress(),
            'google_maps_query_string' => $location?->address?->getGoogleMapsQueryString(),
            'city'                     => $location?->address?->city,
            'state'                    => $location?->address?->state,
            'zip_code'                 => $location?->address?->zip_code,
            'street_address'           => $location?->address?->address_1,
        ];
    }
}
