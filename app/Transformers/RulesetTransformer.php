<?php

namespace App\Transformers;

use App\Models\Ruleset;
use App\Services\Odin\Ruleset\Factories\RulesFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class RulesetTransformer
{
    const FIELD_ID          = 'id';
    const FIELD_NAME        = 'name';
    const FIELD_RULES       = 'rules';
    const FIELD_SOURCE      = 'source';
    const FIELD_FILTER      = 'filter';
    const FIELD_TYPE        = 'type';
    const FIELD_CREATED_AT  = 'created_at';
    const FIELD_UPDATED_AT  = 'updated_at';

    /**
     * @param Collection|null $rulesets
     * @return array
     */
    public function transformRulesets(?Collection $rulesets = null): array
    {
        if(!$rulesets) return [];

        return $rulesets->map(function (Ruleset $ruleset) {
            return $this->transformRuleset($ruleset);
        })->toArray();
    }

    /**
     * @param Ruleset $ruleset
     * @return array
     */
    public function transformRuleset(Ruleset $ruleset): array
    {
        $rulesData = array_map(fn ($rule) => $rule->toArray(), RulesFactory::getCompanyRules($ruleset));

        return [
            self::FIELD_ID           => $ruleset->{Ruleset::FIELD_ID},
            self::FIELD_SOURCE       => $ruleset->{Ruleset::FIELD_SOURCE},
            self::FIELD_FILTER       => $ruleset->{Ruleset::FIELD_FILTER},
            self::FIELD_TYPE         => $ruleset->{Ruleset::FIELD_TYPE},
            self::FIELD_NAME         => $ruleset->{Ruleset::FIELD_NAME}  ?? "Unknown",
            self::FIELD_RULES        => $rulesData,
            self::FIELD_CREATED_AT   => $ruleset->{Model::CREATED_AT}?->toDateTimeString(),
            self::FIELD_UPDATED_AT   => $ruleset->{Model::UPDATED_AT}?->toDateTimeString(),
        ];
    }
}
