<?php

namespace App\Transformers;

use App\Models\ActivityConversation;
use Illuminate\Support\Collection;

class ActivityConversationTransformer
{
    /**
     * @param UserTransformer $userTransformer
     */
    public function __construct(protected UserTransformer $userTransformer)
    {}

    /**
     * @param Collection|null $conversations
     * @return array
     */
    public function transformConversations(?Collection $conversations = null): array
    {
        if(!$conversations) return [];

        return $conversations->map(function(ActivityConversation $conversation) {
            return $this->transformConversation($conversation);
        })->toArray();
    }

    /**
     * @param ActivityConversation $conversation
     * @param bool                 $processChildren
     * @return array
     */
    public function transformConversation(ActivityConversation $conversation, bool $processChildren = true): array
    {
        $response = [
            'id'          => $conversation->{ActivityConversation::FIELD_ID},
            'activity_id' => $conversation->{ActivityConversation::FIELD_ACTIVITY_ID},
            'user'        => $conversation->{ActivityConversation::RELATION_USER} ? $this->userTransformer->transform($conversation->{ActivityConversation::RELATION_USER}) : null,
            'comment'     => $conversation->{ActivityConversation::FIELD_COMMENT},
            'created_at'  => $conversation->{ActivityConversation::FIELD_CREATED_AT}?->toDateTimeString(),
            'updated_at'  => $conversation->{ActivityConversation::FIELD_UPDATED_AT}?->toDateTimeString()
        ];

        if($processChildren) {
            $response['children'] = $conversation->{ActivityConversation::RELATION_CHILDREN}->map(function (ActivityConversation $child) {
                return $this->transformConversation($child, false);
            })->toArray();
        }

        return $response;
    }
}
