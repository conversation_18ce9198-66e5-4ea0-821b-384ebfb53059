<?php

namespace App\Transformers;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentUser;

class ContactCompanyTransformer
{
    public function transform(?EloquentCompany $company): array
    {
        if($company === null) return [];

        $contacts = $company->contacts->map(fn(EloquentCompanyContact $contact) => $this->transformContact($contact));
        $users = $company->users->map(fn(EloquentUser $user) => $this->transformUser($user));

        return [
            "company_id"   => $company->companyid,
            "company_name" => $company->companyname,
            "contacts"     => $contacts->toBase()->merge($users)->sortBy(fn($item) => strtolower($item['name']))->values(),
        ];
    }

    protected function transformContact(EloquentCompanyContact $contact): array
    {
        return [
            "id"     => $contact->contactid,
            "name"   => $contact->firstname." ".$contact->lastname,
            "firstname" => $contact->firstname,
            "lastname" => $contact->lastname,
            "title"  => $contact->title,
            "phone"  => $contact->phone,
            "mobile" => $contact->mobile,
            "email"  => $contact->email,
            "notes"  => $contact->notes,
            "is_contact" => true
        ];
    }

    protected function transformUser(EloquentUser $user): array
    {
        return [
            "id"     => $user->userid,
            "name"   => $user->firstname." ".$user->lastname,
            "firstname" => $user->firstname,
            "lastname" => $user->lastname,
            "title"  => "",
            "phone"  => $user->phone,
            "mobile" => $user->phone,
            "email"  => $user->email,
            "notes"  => "",
            "is_contact" => false
        ];
    }
}
