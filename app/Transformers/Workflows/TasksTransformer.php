<?php

namespace App\Transformers\Workflows;

use App\Models\Odin\Company;
use App\Models\Sales\Task;
use App\Models\User;
use App\Models\WorkflowAction;
use App\Transformers\TaskNotesTransformer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;

class TasksTransformer
{
    public function __construct(
        protected EventPayloadTransformer $eventPayloadTransformer,
        protected TaskNotesTransformer $notesTransformer
    ) {}

    /**
     * @param Collection $tasks
     * @return array
     */
    public function transformTasks(Collection $tasks): array
    {
        return $tasks->map(fn(Task $task) => $this->transform($task))->toArray();
    }

    /**
     * @param Task $task
     * @return array
     */
    public function transform(Task $task): array
    {
        $workflow = $task->runningWorkflow ? $task->runningWorkflow : $task->completedWorkflow;

        $event = null;
        $actionId = null;

        if ($workflow) {
            // Retrieve event and actionId if the workflow exists
            $event = $workflow->payload?->event;
            $workflowAction = WorkflowAction::where(WorkflowAction::FIELD_WORKFLOW_ID, $workflow->workflow_id)
                ->first();
            $actionId = $workflowAction->payload['action_id'] ?? null;
        }

        return [
            'id'                    => $task->{Task::FIELD_ID},
            'name'                  => $task->{Task::FIELD_SUBJECT},
            'completed'             => $task->completed === Task::TASK_COMPLETED,
            'completed_at'          => $task->{Task::FIELD_COMPLETED_AT} ? $task->{Task::FIELD_COMPLETED_AT}->toIso8601String() : null,
            'relation'              => '-',
            'queue'                 => 'default',
            'priority'              => $task->{Task::FIELD_PRIORITY},
            'due'                   => $task->{Task::FIELD_AVAILABLE_AT} ? $task->available_at->toIso8601String() : null,
            'event'                 => $this->eventPayloadTransformer->transform($event, $workflow),
            'manual'                => $task->manual,
            'payload'               => $this->transformTaskPayload($task),
            'category'              => $task->taskCategory?->name,
            'assigned_user_id'      => $task->{Task::FIELD_ASSIGNED_USER_ID},
            'assigned_user'         => $task->{Task::RELATION_ASSIGNED_USER}?->{User::FIELD_NAME} ?? '',
            'timezone'              => $task->timezone?->getLabel() ?? "-",
            'auth_user_is_assigned' => Auth::id() === $task->assigned_user_id,
            'action_id'             => $actionId,
            'muted'                 => $task->{Task::FIELD_MUTED},
        ];
    }

    /**
     * @param Task $task
     *
     * @return array
     */
    public function transformTaskPayload(Task $task): array
    {
        $payload = $task->payload ?? [];

        if (array_key_exists('company_id', $payload) && !empty($task->{Task::RELATION_COMPANY})) {
            $payload['company_name'] = $task->{Task::RELATION_COMPANY}?->{Company::FIELD_NAME};
        }

        return $payload;
    }
}
