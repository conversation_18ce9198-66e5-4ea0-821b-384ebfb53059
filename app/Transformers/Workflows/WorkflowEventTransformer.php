<?php

namespace App\Transformers\Workflows;

use App\Models\WorkflowEvent;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class WorkflowEventTransformer
{
    /**
     * Transforms a list of workflow events.
     *
     * @param Collection<WorkflowEvent> $events
     * @return array
     */
    public function transformEvents(Collection $events): array
    {
        return $events->map(fn(WorkflowEvent $event) => $this->transform($event))
                      ->toArray();
    }

    /**
     * Transforms a workflow event.
     *
     * @param WorkflowEvent $event
     * @return array
     */
    public function transform(WorkflowEvent $event): array
    {
        return [
            "id"             => $event->id,
            "event_category" => $event->event_category,
            "event_name"     => $event->event_name,
            "display_name"   => Str::headline($event->event_name),
        ];
    }
}
