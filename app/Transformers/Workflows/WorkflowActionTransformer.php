<?php

namespace App\Transformers\Workflows;

use App\Models\Workflow;
use App\Models\WorkflowAction;
use App\Repositories\Workflows\WorkflowRepository;
use Illuminate\Support\Collection;

class WorkflowActionTransformer
{
    public function __construct(protected WorkflowRepository $repository) { }

    /**
     * Handles transforming an action.
     *
     * @param WorkflowAction $action
     * @return array
     */
    public function transform(WorkflowAction $action): array
    {
        return [
            "id"           => $action->id,
            "parent_id"    => $action->previous_node_id,
            "display_name" => $action->display_name,
            "workflow_id"  => $action->workflow_id,
            "action_type"  => $action->action_type->value,
            "payload"      => $action->payload,
        ];
    }

    /**
     * Handles transforming a list of actions.
     *
     * @param Collection $actions
     * @return array
     */
    public function transformActions(Collection $actions): array
    {
        return $actions->map(fn(WorkflowAction $action) => $this->transform($action))
                       ->toArray();
    }

    /**
     * Transforms an action tree.
     *
     * @param Workflow $workflow
     * @return array
     */
    public function transformActionTree(Workflow $workflow): array
    {
        return [
            "actions" => $this->repository->getActionTree($workflow),
        ];
    }
}
