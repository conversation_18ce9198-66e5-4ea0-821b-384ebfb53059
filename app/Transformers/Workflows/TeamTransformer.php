<?php

namespace App\Transformers\Workflows;

use App\Enums\Team;
use App\Models\AccountManager;
use App\Models\Hunter;
use App\Models\SupportOfficer;
use App\Transformers\UserTransformer;

class TeamTransformer
{
    public function __construct(public UserTransformer $transformer) {}

    public function transformAll(): array
    {
        $teams = collect();

        foreach (Team::cases() as $team) {
            $teams->push($this->transformTeam($team));
        }

        return $teams->toArray();
    }

    public function transformTeam(Team $team): array
    {
        return match ($team) {
            Team::ACCOUNT_MANAGER => [
                'name' => 'Account Manager',
                'members' => AccountManager::query()->get()->map(function(AccountManager $accountManager) {
                    if ($accountManager->user) {
                        return $this->transformer->transform($accountManager->user);
                    }
                })->filter()->values()->toArray()
            ],
            Team::SUPPORT_OFFICER => [
                'name' => 'Support Officer',
                'members' => SupportOfficer::query()->get()->map(function(SupportOfficer $supportOfficer) {
                    if ($supportOfficer->user) {
                        return $this->transformer->transform($supportOfficer->user);
                    }
                })->filter()->values()->toArray()
            ],
            Team::HUNTER => [
                'name' => 'Hunter',
                'members' => Hunter::query()->get()->map(function(Hunter $hunter) {
                    if ($hunter->user) {
                        return $this->transformer->transform($hunter->user);
                    }
                })->filter()->values()->toArray()
            ],
            default => [],
        };
    }
}
