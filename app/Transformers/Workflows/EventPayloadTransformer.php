<?php

namespace App\Transformers\Workflows;

use App\Models\CompletedWorkflow;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\RunningWorkflow;
use App\Transformers\Campaign\CampaignTransformer;
use App\Transformers\Legacy\LegacyAddressTransformer;
use App\Transformers\Odin\CompanyUserTransformer;
use App\Workflows\WorkflowEvent;

class EventPayloadTransformer
{
    public function __construct(
        protected CompanyUserTransformer $companyUserTransformer,
        protected CampaignTransformer $campaignTransformer,
        protected LegacyAddressTransformer $addressTransformer
    ) { }

    const WORKFLOW_RELATION_LEGACY_LEAD = 'legacyLead';
    const WORKFLOW_RELATION_COMPANY     = 'company';

    /**     *
     * @param WorkflowEvent|null $event
     * @param RunningWorkflow|CompletedWorkflow|null $workflow
     * @return array
     */
    public function transform(?WorkflowEvent $event, RunningWorkflow|CompletedWorkflow|null $workflow): array
    {
        $data = [];

        if ($event === null)
            return $data;

        if ($workflow->{self::WORKFLOW_RELATION_COMPANY}) {

            $data["company"] = $this->transformCompanyData($workflow->{self::WORKFLOW_RELATION_COMPANY});

        } elseif ($event->has('company_reference') && $event->get('company_reference') !== null) {
            /** @var Company|null $company */
            $company = Company::query()->where(Company::FIELD_REFERENCE, $event->get('company_reference'))->first();
            if ($company) {
                $data["company"] = $this->transformCompanyData($company);
            }
        }

        if ($event->has('campaign_id') && $event->get('campaign_id') !== null)
            $data["campaign"] = $this->transformCampaignData($event->get('campaign_id'));

        if ($workflow->{self::WORKFLOW_RELATION_LEGACY_LEAD}) {
            $data['lead'] = $this->transformLeadData($workflow->{self::WORKFLOW_RELATION_LEGACY_LEAD});
        }

        return $data;
    }

    /**
     * @param Company $company
     * @return array
     */
    protected function transformCompanyData(Company $company): array
    {
        return [
            "id"   => $company->{Company::FIELD_ID},
            "name" => $company->{Company::FIELD_NAME}
        ];
    }

    /**
     * @param int $campaignId
     * @return array
     */
    protected function transformCampaignData(int $campaignId): array
    {
        $campaign = LeadCampaign::query()
            ->select(LeadCampaign::NAME)
            ->find($campaignId);

        if($campaign) {
            return [
                'name' => $campaign->{LeadCampaign::NAME}
            ];
        }

        return [];
    }

    /**
     * @param EloquentQuote $lead
     *
     * @return array
     */
    protected function transformLeadData(EloquentQuote $lead): array
    {
        return [
            'id'      => $lead->quoteid,
            'address' => $this->addressTransformer->transform($lead->address)
        ];
    }

}
