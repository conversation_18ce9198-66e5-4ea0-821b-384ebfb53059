<?php

namespace App\Transformers\Workflows;

use App\Models\Workflow;
use Illuminate\Support\Collection;

class WorkflowTransformer
{
    public function __construct(protected WorkflowActionTransformer $actionTransformer) { }

    /**
     * Transforms a workflow to it's client-safe model.
     *
     * @param Workflow $workflow
     * @return array
     */
    public function transform(Workflow $workflow): array
    {
        return [
            "id"              => $workflow->id,
            "name"            => $workflow->name,
            "event_id"        => $workflow->workflow_event_id,
            "entry_action_id" => $workflow->entry_action_id,
            "actions"         => $this->actionTransformer->transformActions($workflow->actions),
            "action_tree"     => $this->actionTransformer->transformActionTree($workflow),
        ];
    }

    /**
     * Transforms a collection of workflows to their client-safe models.
     *
     * @param Collection $workflows
     * @return array
     */
    public function transformWorkflows(Collection $workflows): array
    {
        return $workflows->map(fn(Workflow $workflow) => $this->transform($workflow))
                         ->toArray();
    }
}
