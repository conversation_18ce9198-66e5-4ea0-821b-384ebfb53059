<?php

namespace App\Transformers\Workflows;

use App\Enums\TaskModules;
use Illuminate\Support\Collection;

class TaskModuleTransformer
{
    /**
     * Handles transforming a collection of modules to their client side representation.
     *
     * @param Collection $items
     * @return array
     */
    public function transformModules(Collection $items): array
    {
        return $items->map(fn(TaskModules $module) => $this->transform($module))->toArray();
    }

    /**
     * Handles transforming a task module to it's client side representation.
     *
     * @param TaskModules $module
     * @return array
     */
    public function transform(TaskModules $module): array
    {
        return [
            'id'   => $module->value,
            'name' => $module->getDisplayName(),
            'columns' => $module->getMinColumns()
        ];
    }
}
