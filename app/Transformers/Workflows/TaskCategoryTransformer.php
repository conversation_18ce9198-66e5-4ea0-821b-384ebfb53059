<?php

namespace App\Transformers\Workflows;

use App\Models\TaskCategory;
use Illuminate\Support\Collection;

class TaskCategoryTransformer
{
    /**
     * @param Collection<TaskCategory> $taskCategories
     *
     * @return array
     */
    public function transformTaskCategories(Collection $taskCategories): array
    {
        return $taskCategories->map(fn(TaskCategory $taskCategory) => $this->transformTaskCategory($taskCategory))->toArray();
    }

    /**
     * @param TaskCategory $taskCategory
     *
     * @return array
     */
    public function transformTaskCategory(TaskCategory $taskCategory): array
    {
        return [
            'id' => $taskCategory->id,
            'name' => $taskCategory->name
        ];
    }
}
