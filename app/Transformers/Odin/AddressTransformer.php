<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Address;

class AddressTransformer
{
    /**
     * @param Address|null $address
     * @return array
     */
    public function transformAddress(?Address $address): array
    {
        if(!$address) return [];

        return [
            'id'             => $address->id,
            'address_1'      => $address->address_1,
            'address_2'      => $address->address_2,
            'city'           => $address->city,
            'state'          => $address->state,
            'zip_code'       => $address->zip_code,
            'full_address'   => $address->full_address,
            'longitude'      => $address->longitude,
            'latitude'       => $address->latitude
        ];
    }
}
