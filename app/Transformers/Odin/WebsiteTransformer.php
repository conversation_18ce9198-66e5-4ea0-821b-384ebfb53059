<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Website;
use Illuminate\Support\Collection;

class WebsiteTransformer
{
    /**
     * @param Collection<Website> $websites
     *
     * @return array
     */
    public function transformAll(Collection $websites): array
    {
        return $websites->map(fn(Website $website) => $this->transformWebsite($website))->toArray();
    }

    /**
     * @param Website $website
     *
     * @return array
     */
    public function transformWebsite(Website $website): array
    {
        return [
            'id'           => $website->id,
            'name'         => $website->name,
            'url'          => $website->url,
            'abbreviation' => $website->abbreviation,
            'cp_domain'    => $website->cp_domain,
            'created'      => $website->created_at?->toDateString()
        ];
    }
}
