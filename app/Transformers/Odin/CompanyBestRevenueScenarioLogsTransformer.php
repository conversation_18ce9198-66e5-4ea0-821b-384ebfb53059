<?php

namespace App\Transformers\Odin;

use App\Models\BestRevenueScenarioLog;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductCampaign;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;

class CompanyBestRevenueScenarioLogsTransformer
{
    /**
     * @param EloquentBuilder $query
     * @param int $page
     * @param int $perPage
     * @return array
     */
    public function transformLogsQuery(EloquentBuilder $query, int $page = 1, int $perPage = 5): array
    {
        $paginationDataQuery = clone $query;

        $total = $paginationDataQuery
                    ->distinct()
                    ->count(BestRevenueScenarioLog::FIELD_CONSUMER_PRODUCT_ID);

        $logs = [
            "pagination_data" => [
                "current_page" => $page,
                "from" => ($page - 1) * $perPage + 1,
                "to" => min( $total, $page * $perPage),
                "total" => $total,
                "last_page" => ceil($total / $perPage)
            ],
            "brs_logs" => []
        ];

        $consumerProductsRetrieved = [];

        $from = $logs['pagination_data']['from'];
        $to = $logs['pagination_data']['to'];

        $query->cursor()->each(function($brsLog) use (&$logs, &$consumerProductsRetrieved, $from, $to) {
            return $this->transformLogsQueryResults($brsLog, $logs, $consumerProductsRetrieved, $from, $to);
        });

        return $logs;
    }

    /**
     * @param BestRevenueScenarioLog $brsLog
     * @param array $logs
     * @param array $consumerProductsRetrieved
     * @param int $from
     * @param int $to
     * @return bool
     */
    private function transformLogsQueryResults(
        BestRevenueScenarioLog $brsLog,
        array &$logs,
        array &$consumerProductsRetrieved,
        int $from,
        int $to
    ): bool
    {
        $consumerProductId = $brsLog->{BestRevenueScenarioLog::FIELD_CONSUMER_PRODUCT_ID};
        $saleTypeId = $brsLog->{BestRevenueScenarioLog::FIELD_SALE_TYPE_ID};
        $runId = $brsLog->{BestRevenueScenarioLog::FIELD_RUN_ID};
        $productCampaignId = $brsLog->{BestRevenueScenarioLog::FIELD_PRODUCT_CAMPAIGN_ID};

        $consumerProductsRetrieved[] = $consumerProductId;
        $consumerProductsRetrieved = array_unique($consumerProductsRetrieved);

        if(count($consumerProductsRetrieved) > $to) {
            return false;
        }
        else if(count($consumerProductsRetrieved) < $from) {
            return true;
        }

        if(empty($logs["brs_logs"][$consumerProductId])) {
            $consumer = $brsLog->{BestRevenueScenarioLog::RELATION_CONSUMER_PRODUCT}->{ConsumerProduct::RELATION_CONSUMER};

            $logs["brs_logs"][$consumerProductId] = [
                "consumer_product_id" => $consumerProductId,
                "consumer" => [
                    "name" => $consumer->getFullName(),
                    "email" => $consumer->{Consumer::FIELD_EMAIL},
                    "phone" => $consumer->{Consumer::FIELD_PHONE}
                ],
                "run_logs" => []
            ];
        }
        if(empty($logs["brs_logs"][$consumerProductId]["run_logs"][$runId])) {
            $logs["brs_logs"][$consumerProductId]["run_logs"][$runId] = [];
        }
        if(empty($logs["brs_logs"][$consumerProductId]["run_logs"][$runId][$saleTypeId])) {
            $logs["brs_logs"][$consumerProductId]["run_logs"][$runId][$saleTypeId] = [];
        }
        if(empty($logs["brs_logs"][$consumerProductId]["run_logs"][$runId][$saleTypeId][$productCampaignId])) {
            $logs["brs_logs"][$consumerProductId]["run_logs"][$runId][$saleTypeId][$productCampaignId] = [
                "product_campaign_name" => $brsLog->{BestRevenueScenarioLog::RELATION_PRODUCT_CAMPAIGN}->{ProductCampaign::FIELD_NAME},
                "product_campaign_logs" => []
            ];
        }

        $logs["brs_logs"][$consumerProductId]["run_logs"][$runId][$saleTypeId][$productCampaignId]["product_campaign_logs"][] = [
            "message" => $brsLog->{BestRevenueScenarioLog::FIELD_MESSAGE}
        ];

        return true;
    }
}
