<?php

namespace App\Transformers\Odin;

use App\Models\Odin\ConsumerProduct;
use Illuminate\Support\Collection;

class ConsumerProductTaskviewTransformer
{

    public function __construct(protected AddressTransformer $addressTransformer)
    {}

    public function transform(Collection $consumerProducts): array
    {
        return $consumerProducts->map(fn(ConsumerProduct $consumerProduct) => $this->transformConsumerProduct($consumerProduct))
            ->values()
            ->toArray();
    }

    public function transformConsumerProduct(ConsumerProduct $consumerProduct): array
    {
        return [
            'id'                => $consumerProduct->legacy_id,
            'odin_id'           => $consumerProduct->id,
            'sold_status'       => $this->getSoldStatus($consumerProduct),
            'contact_method'    => '',
            'address'           => $this->addressTransformer->transformAddress($consumerProduct->address),
        ];
    }

    private function getSoldStatus(ConsumerProduct &$consumerProduct): string
    {
        return match(true) {
            $consumerProduct->product_assignment_count === 0 => 'Unsold',
            $consumerProduct->product_assignment_count > 0 && $consumerProduct->product_assignment_count < $consumerProduct->contact_requests => 'Undersold',
            default => 'Allocated'
        };
    }
}
