<?php

namespace App\Transformers\Odin;

use App\Enums\CompanyConsolidatedStatus;
use App\Models\Legacy\CompanyRanking;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyNabcepCertification;
use App\Models\Legacy\EloquentReview;
use App\Models\Legacy\Location;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Repositories\Legacy\CompanyRankingsRepository;
use App\Repositories\Odin\CompanyRepository;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class CompanyListTransformer
{
    public function __construct(
        protected CompanyRankingsRepository $legacyExpertRatingRepository,
        protected CompanyRepository $companyRepository
    )
    {
    }

    /**
     * @param Collection<Company> $companies
     * @param array $companyIds
     * @param bool $requireLatestReviews
     * @param bool $requireExpertRatingFactors
     * @param bool $requireOfficeLocations
     * @param Location|null $location
     * @param string|null $quoteRedirectLink
     * @return array
     */
    public function transformCompanies(
        Collection $companies,
        array      $companyIds,
        bool       $requireLatestReviews = false,
        bool       $requireExpertRatingFactors = false,
        bool       $requireOfficeLocations = false,
        ?Location  $location = null,
        ?string    $quoteRedirectLink = null,
    ): array
    {
        return $companies->map(fn(Company $company) => $this->transformCompany($company, $requireLatestReviews, $requireExpertRatingFactors, $requireOfficeLocations, $companyIds, $location, $quoteRedirectLink))->toArray();
    }

    /**
     * @param Company $company
     * @param bool $requireLatestReviews
     * @param bool $requireExpertRatingFactors
     * @param bool $requireOfficeLocations
     * @param array|null $companyIds
     * @param Location|null $location
     * @param string|null $quoteRedirectLink
     * @return array
     */
    public function transformCompany(
        Company   $company,
        bool      $requireLatestReviews = false,
        bool      $requireExpertRatingFactors = false,
        bool      $requireOfficeLocations = false,
        array     $companyIds = null,
        ?Location $location = null,
        ?string   $quoteRedirectLink = null,
    ): array
    {
        $data = [
            'id'                    => $company->{Company::FIELD_ID},
            'name'                  => $company->{Company::FIELD_NAME},
            'slug'                  => $company->activeCompanySlug?->slug,
            'fixr_profile_enabled'  => boolval($company->contractorProfile?->status),
            'description'           => html_entity_decode(strip_tags($company->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::DESCRIPTION})),
            'expert_description'    => $company->contractorProfile?->introduction,
            'logo_link'             => $company->{Company::RELATION_LEGACY_COMPANY}?->logoUrl(),
            'profile_logo_link'     => $company->link_to_logo,
            'profile_url'           => $this->getProfileUrl($company),
            'overall_expert_rating' => $company->contractorProfile?->rating ?? $this->companyRepository->getCompanyDataByKey($company, 'expert_rating_overall_score'),
            'consumer_rating'       => round($company->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::RELATION_COMPANY_RANKING}?->{CompanyRanking::BAYESIAN_ALL_TIME}, 2) ?? 0,
            'consumer_review_count' => $company->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::RELATION_COMPANY_RANKING}?->{CompanyRanking::REVIEW_COUNT} ?? 0,
            'services'              => $this->getServices($company),
            'certifications'        => $this->getCertifications($company),
            'additional_data'       => $company->{Company::RELATION_DATA}?->{CompanyData::FIELD_PAYLOAD},
            'office_locations'      => $requireOfficeLocations ? $this->getOfficeLocations($company, $location) : null,
            'latest_reviews'        => $requireLatestReviews ? $this->getLatestReviews($company) : null,
            'expert_rating_factors' => $requireExpertRatingFactors ? $this->legacyExpertRatingRepository->getRankingFactors($company->{Company::RELATION_LEGACY_COMPANY}) : null,
            'fallback_rating'       => $this->companyRepository->getCompanyDataByKey($company, 'google_rating'),
            'fallback_review_count' => $this->companyRepository->getCompanyDataByKey($company, 'google_review_count'),
            'headquarters_location' => $this->getHeadQuartersLocation($company),
            'quote_redirect_link'   => $quoteRedirectLink,
            'flag_as_partner'       => $company->consolidated_status === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
        ];

        if($companyIds) {
            $data['position'] = array_search($company->{Company::FIELD_ID}, $companyIds);
        }

        return $data;
    }

    /**
     * @param Company $company
     * @return array[]
     */
    private function getCertifications(Company $company): array
    {
        return [
            'nabcep_certifications' => $this->getDistinctNabcepCertificationsByType($company),
            // todo: add future certifications here
        ];
    }

    /**
     * @param Company $company
     * @return array
     */
    private function getDistinctNabcepCertificationsByType(Company $company): array
    {
        $certifications = $company->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::RELATION_COMPANY_NABCEP_CERTIFICATIONS};

        return $certifications->map(function ($certification) {
            if($certification->{EloquentCompanyNabcepCertification::EXPIRED_AT} > Carbon::now()) {
                return [
                    'type' => $certification->{EloquentCompanyNabcepCertification::CERTIFICATION_TYPE}
                ];
            }
        })->unique('type')->toArray();
    }

    /**
     * @param Company $company
     * @param Location|null $location
     * @return array
     */
    private function getOfficeLocations(Company $company, ?Location $location): array
    {
        $officeLocations = $company->locations->filter(function ($officeLocation) use ($location) {
            return $officeLocation->address?->{Address::FIELD_CITY} && $officeLocation->address?->{Address::FIELD_STATE};
        });

        if($location?->state_abbr) {
            $officeLocations = $officeLocations->filter(function ($officeLocation) use ($location) {
                return $officeLocation->address->{Address::FIELD_STATE} === $location->state_abbr;
            });
        }

        return $officeLocations->map(function ($officeLocation) {
            return [
                'phone'     => $officeLocation->{CompanyLocation::FIELD_PHONE},
                'address_1' => $officeLocation->address->{Address::FIELD_ADDRESS_1},
                'address_2' => $officeLocation->address->{Address::FIELD_ADDRESS_2},
                'city'      => $officeLocation->address->{Address::FIELD_CITY},
                'state'     => $officeLocation->address->{Address::FIELD_STATE},
                'zip_code'  => $officeLocation->address->{Address::FIELD_ZIP_CODE},
                'country'   => $officeLocation->address->{Address::FIELD_COUNTRY},
                'longitude' => $officeLocation->address->{Address::FIELD_LONGITUDE},
                'latitude'  => $officeLocation->address->{Address::FIELD_LATITUDE}
            ];
        })->toArray();
    }

    /**
     * @param Company $company
     * @return array
     */
    private function getServices(Company $company): array
    {
        return $company->services->map(function ($service) {
            return [
                'service_name'  => $service->{IndustryService::FIELD_NAME},
                'industry_name' => $service->industry->{Industry::FIELD_NAME}
            ];
        })->toArray();
    }

    /**
     * @param Company $company
     * @return string|null
     */
    private function getProfileUrl(Company $company): ?string
    {
        return match ($company->{Company::RELATION_LEGACY_COMPANY}->{EloquentCompany::TYPE}) {
            EloquentCompany::TYPE_INSTALLER => "/installers/" . $company->{Company::RELATION_LEGACY_COMPANY}->{EloquentCompany::LINK} . "-reviews",
            EloquentCompany::TYPE_MANUFACTURER => "/manufacturers/" . $company->{Company::RELATION_LEGACY_COMPANY}->{EloquentCompany::LINK},
            null => null,
            default => $company->{Company::RELATION_LEGACY_COMPANY}->{EloquentCompany::LINK},
        };
    }

    /**
     * @param Company $company
     * @return array[]|null[]
     */
    private function getLatestReviews(Company $company): array
    {
        /** @var EloquentReview $goodReview */
        $goodReview = $company->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::RELATION_REVIEWS}()
            ?->where(EloquentReview::FIELD_APPROVED, EloquentReview::REVIEW_APPROVED)
            ->where(EloquentReview::FIELD_OVERALL_SCORE, '>=', 3.5)
            ->orderBy(EloquentReview::FIELD_TIMESTAMP_ADDED, 'DESC')
            ->with(EloquentReview::RELATION_ADDRESS)
            ->first();

        /** @var EloquentReview $badReview */
        $badReview = $company->{Company::RELATION_LEGACY_COMPANY}?->{EloquentCompany::RELATION_REVIEWS}()
            ?->where(EloquentReview::FIELD_APPROVED, EloquentReview::REVIEW_APPROVED)
            ->where(EloquentReview::FIELD_OVERALL_SCORE, '<=', 3.4)
            ->orderBy(EloquentReview::FIELD_TIMESTAMP_ADDED, 'DESC')
            ->with(EloquentReview::RELATION_ADDRESS)
            ->first();

        return [
            'good' => $this->getTransformedReview($goodReview),
            'bad'  => $this->getTransformedReview($badReview)
        ];
    }

    /**
     * @param EloquentReview|null $review
     * @return ?array
     */
    private function getTransformedReview(?EloquentReview $review): ?array
    {
        return $review ? [
            'score'         => $review->{EloquentReview::FIELD_OVERALL_SCORE},
            'title'         => $review->{EloquentReview::FIELD_TITLE},
            'comments'      => $review->{EloquentReview::FIELD_COMMENTS},
            'reviewer_name' => $review->{EloquentReview::FIELD_USER_NAME},
            'date'          => Carbon::parse($review->{EloquentReview::FIELD_TIMESTAMP_ADDED})->format('m-d-Y'),
            'link'          => $review->{EloquentReview::FIELD_REVIEW_LINK},
            'id'            => $review->{EloquentReview::FIELD_REVIEW_ID},
            'location'      => $review->address ? "{$review->address->city}, {$review->address->state}" : null
        ] : null;
    }

    /**
     * @param Company $company
     * @return string|null
     */
    private function getHeadQuartersLocation(Company $company): ?string
    {
        $address = $company->primaryLocation?->address;
        return $address ? $address->city .', ' . $address->zipLocation?->state_abbr : null;
    }
}
