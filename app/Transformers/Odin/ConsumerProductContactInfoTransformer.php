<?php

namespace App\Transformers\Odin;

use App\Enums\Cookie;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\PermissionType;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\CommunicationRepository;
use Exception;
use Illuminate\Support\Facades\Auth;

class ConsumerProductContactInfoTransformer
{

    public function __construct(
        protected ConsumerProductDataTransformer $payloadTransformer,
        protected CommunicationRepository $communicationRepository
    ) {}

    /**
     * @param string $phone
     * @return string
     */
    protected function obfuscatePhone(string $phone): string
    {
        return preg_replace('/\d/', '*', $phone);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return array
     * @throws Exception
     */
    public function transform(ConsumerProduct $consumerProduct): array
    {

        $consumer = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER};
        $ownProperty = $this->payloadTransformer->getConsumerProductDataByKey($consumerProduct, GlobalConfigurableFields::OWN_PROPERTY->value);

        $user = Auth::user(); // Get the current user
        $hasPermission = $user->hasPermissionTo(PermissionType::VIEW_LEAD_PII->value);
        $hasPiiToken = request()->cookie(Cookie::PII_TOKEN->value);

        return [
            'name' => $consumer->getFullName(),
            'email' => $hasPermission || $hasPiiToken ? $consumer->{Consumer::FIELD_EMAIL} : obfuscateEmail($consumer->{Consumer::FIELD_EMAIL}),
            'address' => $hasPermission || $hasPiiToken ? $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}?->getFullAddress() : $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}?->getObfuscatedAddress(),
            'phone' => $hasPermission || $hasPiiToken ? $consumer->{Consumer::FIELD_PHONE} : $this->obfuscatePhone($consumer->{Consumer::FIELD_PHONE}),
            'phone_is_valid' => $consumer->{Consumer::ATTRIBUTE_IS_VALID_PHONE},
            'own_property' => strtolower($ownProperty ?? ""),
            'can_call' => $this->communicationRepository->canCallConsumer($consumer)
        ];
    }
}
