<?php

namespace App\Transformers\Odin;

use App\Models\Odin\CompanyReview;
use Illuminate\Support\Collection;

class CompanyReviewsTransformer
{
    /**
     * @param CompanyTransformer $companyTransformer
     */
    public function __construct(protected CompanyTransformer $companyTransformer){}

    /**
     * @param Collection<CompanyReview> $companyReviews
     * @param array|null $filteredByKeys - restrict the transform by keys
     * @return array
     */
    public function transform(Collection $companyReviews, array|null $filteredByKeys = null): array
    {
        return $companyReviews->map(fn(CompanyReview $companyReview) => $this
            ->transformCompanyReview($companyReview, $filteredByKeys))
            ->toArray();
    }

    /**
     * @param CompanyReview $companyReview
     * @param array|null $filteredByKeys - restrict the transform by keys
     * @return array
     */
    public function transformCompanyReview(CompanyReview $companyReview, array|null $filteredByKeys): array
    {
        $company = $companyReview->{CompanyReview::RELATION_COMPANY};

        $transform =
            [
                'id'              => $companyReview->{CompanyReview::FIELD_ID},
                'company'         => $company ? $this->companyTransformer->transformCompany($company) : [],
                'first_name'      => $companyReview->{CompanyReview::FIELD_FIRST_NAME},
                'last_name'       => $companyReview->{CompanyReview::FIELD_LAST_NAME},
                'email'           => $companyReview->{CompanyReview::FIELD_EMAIL},
                'email_validated' => $companyReview->{CompanyReview::FIELD_EMAIL_VALIDATED},
                'phone_validated' => $companyReview->{CompanyReview::FIELD_PHONE_VALIDATED},
                'title'           => $companyReview->{CompanyReview::FIELD_TITLE},
                'body'            => $companyReview->{CompanyReview::FIELD_BODY},
                'score'           => $companyReview->{CompanyReview::FIELD_OVERALL_SCORE},
                'status'          => CompanyReview::STATUS_NAMES[$companyReview->{CompanyReview::FIELD_STATUS}],
                'created_at'      => $companyReview->{CompanyReview::CREATED_AT}?->toIso8601String()
            ];
        return $filteredByKeys
            ? array_filter($transform, fn($key) => in_array($key, $filteredByKeys), ARRAY_FILTER_USE_KEY)
            : $transform;
    }
}

