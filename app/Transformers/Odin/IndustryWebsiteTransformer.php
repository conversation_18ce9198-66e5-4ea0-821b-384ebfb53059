<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryWebsite;
use App\Models\Odin\Website;
use Illuminate\Support\Collection;

class IndustryWebsiteTransformer
{
    public function __construct(protected WebsiteTransformer $websiteTransformer){}

    /**
     * @param Collection $industryWebsites
     * @return array
     */
    public function transform(Collection $industryWebsites): array
    {
        return $industryWebsites->map(fn(IndustryWebsite $industryWebsite) => $this->transformIndustryWebsite($industryWebsite))->toArray();
    }

    /**
     * @param IndustryWebsite $industryWebsite
     * @return array
     */
    public function transformIndustryWebsite(IndustryWebsite $industryWebsite): array
    {
        return
            [
                'id'         => $industryWebsite->{IndustryWebsite::FIELD_ID},
                'industry'   => $industryWebsite->{IndustryWebsite::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
                'website'    => $this->websiteTransformer->transformWebsite($industryWebsite->{IndustryWebsite::RELATION_WEBSITE}),
                'slug'       => $industryWebsite->{IndustryWebsite::FIELD_SLUG},
                'created_at' => $industryWebsite->{IndustryWebsite::CREATED_AT}?->toIso8601String()
            ];
    }
}
