<?php

namespace App\Transformers\Odin;

use App\Enums\Odin\VerificationServiceTypes;
use App\Services\Odin\ConsumerProductVerification\ConsumerProductVerificationResult;
use App\Services\Odin\ConsumerProductVerification\IpQuality\IpQualityScoreService;
use App\Services\Odin\ConsumerProductVerification\IPQualityScoreResult;

class ConsumerProductVerificationTransformer
{
    /**
     * @param VerificationServiceTypes $service
     * @param mixed                    $verifiedData
     * @return array
     */
    public function transform(VerificationServiceTypes $service, mixed $verifiedData): array
    {
        if(empty($verifiedData)) return [];

        return match ($service) {
            VerificationServiceTypes::IDENTITY_CHECK   => $this->transformProductVerification($verifiedData),
            VerificationServiceTypes::IP_QUALITY_SCORE => $this->transformIPQualityScore($verifiedData),
            default                                    => []
        };
    }

    /**
     * @param ConsumerProductVerificationResult $consumerProductVerification
     * @return array
     */
    public function transformProductVerification(ConsumerProductVerificationResult $consumerProductVerification): array
    {
        return [
            "phone"      => [
                "is_valid"          => $consumerProductVerification->phone_is_valid,
                "name_match"        => $consumerProductVerification->phone_name_match,
                "subscriber_name"   => $consumerProductVerification->phone_subscriber_name ?? null,
                "address_match"     => $consumerProductVerification->phone_address_match   ?? null,
                "line_type"         => $consumerProductVerification->phone_line_type       ?? null,
                "country_code"      => $consumerProductVerification->phone_country_code    ?? null,
                "carrier"           => $consumerProductVerification->phone_carrier         ?? null,
                "is_commercial"     => $consumerProductVerification->phone_is_commercial   ?? null,
                "is_prepaid"        => $consumerProductVerification->phone_is_prepaid      ?? null,
                "warnings"          => $consumerProductVerification->phone_warnings        ?? null
            ],
            "address"    => [
                "is_valid"          => $consumerProductVerification->address_is_valid,
                "name_match"        => $consumerProductVerification->address_name_match    ?? null,
                "resident_name"     => $consumerProductVerification->address_resident_name ?? null,
                "type"              => $consumerProductVerification->address_type          ?? null,
                "is_commercial"     => $consumerProductVerification->address_is_commercial ?? null,
                "is_forwarder"      => $consumerProductVerification->address_is_forwarder  ?? null,
                "warnings"          => $consumerProductVerification->address_warnings      ?? null
            ],
            "email"      => [
                "is_valid"          => $consumerProductVerification->email_is_valid,
                "name_match"        => $consumerProductVerification->email_name_match       ?? null,
                "registered_name"   => $consumerProductVerification->email_registered_name  ?? null,
                "age"               => $consumerProductVerification->email_age              ?? null,
                "is_autogenerated"  => $consumerProductVerification->email_is_autogenerated ?? null,
                "is_disposable"     => $consumerProductVerification->email_is_disposable    ?? null,
                "warnings"          => $consumerProductVerification->email_warnings         ?? null
            ],
            "ip"         => [
                "is_valid"          => $consumerProductVerification->ip_is_valid,
                "name_match"        => $consumerProductVerification->ip_name_match       ?? null,
                "geolocation"       => $consumerProductVerification->ip_geolocation      ?? null,
                "address_distance"  => $consumerProductVerification->ip_address_distance ?? null,
                "phone_distance"    => $consumerProductVerification->ip_phone_distance   ?? null,
                "is_proxy"          => $consumerProductVerification->ip_is_proxy         ?? null,
                "warning"           => $consumerProductVerification->ip_warnings         ?? null
            ],
            "confidence" => ((500 - $consumerProductVerification->confidence) / 500) * 100
        ];
    }

    /**
     * @param IPQualityScoreResult $ipQualityScore
     * @return array
     */
    public function transformIPQualityScore(IPQualityScoreResult $ipQualityScore): array
    {
        return [
            "email"       => collect($ipQualityScore->email)->only(IpQualityScoreService::IP_QUALITY_EMAIL_DATA_KEYS)->toArray(),
            "phone"       => collect($ipQualityScore->phone)->only(IpQualityScoreService::IP_QUALITY_PHONE_DATA_KEYS)->toArray(),
            "transaction" => collect($ipQualityScore->transaction)->toArray()
        ];
    }
}
