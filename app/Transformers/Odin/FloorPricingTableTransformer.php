<?php

namespace App\Transformers\Odin;

use App\Enums\Odin\Industry;
use App\Enums\Odin\Product;
use App\Models\Legacy\Location;
use App\Models\Odin\FloorPriceFormula;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\SaleType;
use App\Repositories\Odin\ProductProcessing\FloorPriceFormulaRepository;
use App\Services\Odin\FloorPriceFormulaService;
use Illuminate\Support\Collection;

class FloorPricingTableTransformer
{
    public function __construct(protected FloorPriceFormulaRepository $floorPriceFormulaRepository) {}

    /**
     * @param Collection $productStateFloorPrices
     * @param string $industry
     * @param string $product
     *
     * @return array
     */
    public function transformFloorStatePrices(Collection $productStateFloorPrices, string $industry, string $product): array
    {
        $useFormulaPrice = $product === Product::APPOINTMENT->value && in_array($industry, [Industry::SOLAR->value, Industry::ROOFING->value]);

        $states = $productStateFloorPrices->mapToGroups(function (ProductStateFloorPrice $productStateFloorPrice) use ($industry, $product, $useFormulaPrice) {
            /** @var Location $location */
            $location = Location::query()->findOrFail($productStateFloorPrice->{ProductStateFloorPrice::FIELD_STATE_LOCATION_ID});

            return [
                $location->{Location::STATE_ABBREVIATION} => [
                    'name' => $location->{Location::STATE},
                    'key' => $location->{Location::STATE_KEY},
                    'sales_types' => [
                        $productStateFloorPrice->saleType->{SaleType::FIELD_KEY} => [
                            "inherent_price" => null,
                            "formula" => $useFormulaPrice ? $this->getFormula($industry, $product, $location, $productStateFloorPrice) : $this->getDefaultFormula(),
                            "explicit_price" => $productStateFloorPrice->{ProductStateFloorPrice::FIELD_PRICE},
                            "price" => $productStateFloorPrice->{ProductStateFloorPrice::FIELD_PRICE},
                            "calculated_price" => $useFormulaPrice ? $this->getCalculatedPrice($industry, $product, $location, $productStateFloorPrice) : null
                        ]
                    ]
                ]
            ];
        })->map(function (Collection $collection) {
            return [
                'name' => $collection->pluck('name')->unique()->first(),
                'key' => $collection->pluck('key')->unique()->first(),
                'sales_types' => $collection->pluck('sales_types')->unique()->collapse()->toArray()
            ];
        });

        return [
            "states" => $states->all()
        ];
    }

    /**
     * @param Collection $productCountyFloorPrices
     * @param Location $stateLocation
     * @param string $industry
     * @param string $product
     *
     * @return array
     */
    public function transformFloorCountyPrices(Collection $productCountyFloorPrices, Location $stateLocation, string $industry, string $product): array
    {
        $useFormulaPrice = $product === Product::APPOINTMENT->value && in_array($industry, [Industry::SOLAR->value, Industry::ROOFING->value]);

        $counties = $productCountyFloorPrices->mapToGroups(function (ProductCountyFloorPrice $productCountyFloorPrice) use ($industry, $product, $useFormulaPrice) {
            /** @var Location $location */
            $location = Location::query()->findOrFail($productCountyFloorPrice->{ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID});

            return [
                $location->{Location::COUNTY} => [
                    'name' => $location->{Location::COUNTY},
                    'key' => $location->{Location::COUNTY_KEY},
                    'sales_types' => [
                        $productCountyFloorPrice->saleType->{SaleType::FIELD_KEY} => [
                            "inherent_price" => null,
                            "formula" => $useFormulaPrice ? $this->getFormula($industry, $product, $location, $productCountyFloorPrice) : $this->getDefaultFormula(),
                            "explicit_price" => $productCountyFloorPrice->{ProductStateFloorPrice::FIELD_PRICE},
                            "price" => $productCountyFloorPrice->{ProductStateFloorPrice::FIELD_PRICE},
                            "calculated_price" => $useFormulaPrice ? $this->getCalculatedPrice($industry, $product, $location, $productCountyFloorPrice) : null
                        ]
                    ]
                ]
            ];
        })->map(function (Collection $collection) {
            return [
                'name' => $collection->pluck('name')->unique()->first(),
                'key' => $collection->pluck('key')->unique()->first(),
                'sales_types' => $collection->pluck('sales_types')->unique()->collapse()->toArray()
            ];
        });

        return [
            "states" => [
                $stateLocation->{Location::STATE_ABBREVIATION} => [
                    'name' => $stateLocation->{Location::STATE},
                    'key' => $stateLocation->{Location::STATE_KEY},
                    'counties' => $counties->all()
                ]
            ]
        ];
    }

    /**
     * @param string $industry
     * @param string $product
     * @param Location $location
     * @param ProductStateFloorPrice|ProductCountyFloorPrice $floorPrices
     *
     * @return array
     */
    protected function getFormula(string $industry, string $product, Location $location, ProductStateFloorPrice|ProductCountyFloorPrice $floorPrices): array
    {
        return $this->floorPriceFormulaRepository->getFormula(
            $industry,
            $product,
            $location->id,
            $floorPrices->propertyType->name,
            $floorPrices->qualityTier->name,
            $floorPrices->saleType->name
        )?->toArray() ?? $this->getDefaultFormula();
    }

    /**
     * @return array
     */
    protected function getDefaultFormula(): array
    {
        return FloorPriceFormula::defaultAppointmentFormula()->toArray();
    }

    /**
     * @param string $industry
     * @param string $product
     * @param Location $location
     * @param ProductStateFloorPrice|ProductCountyFloorPrice $floorPrices
     *
     * @return float|null
     */
    protected function getCalculatedPrice(string $industry, string $product, Location $location, ProductStateFloorPrice|ProductCountyFloorPrice $floorPrices): ?float
    {
       $formula = $this->floorPriceFormulaRepository->getFormula(
            $industry,
            $product,
            $location->id,
            $floorPrices->propertyType->name,
            $floorPrices->qualityTier->name,
            $floorPrices->saleType->name
        );

       return $formula ? FloorPriceFormulaService::resolveFormulaForBasePrice($formula, $floorPrices->price) : null;
    }
}
