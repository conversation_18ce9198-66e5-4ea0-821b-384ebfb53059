<?php

namespace App\Transformers\Odin;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\IndustryConsumerField;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceConsumerField;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ConsumerConfigurableFieldCategoryRepository;
use App\Repositories\Odin\ConsumerProductDataRepository;
use App\Repositories\Odin\ConsumerRepository;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Models\Odin\Industry;

class ConsumerProductDataTransformer
{
    public function __construct(protected ConsumerRepository $consumerRepository) {}

    /**
     * Return a ConsumerProductData payload value by key
     *
     * @param ConsumerProduct $consumerProduct
     * @param string $key
     * @param mixed|null $default
     * @return mixed|null
     */
    public function getConsumerProductDataByKey(ConsumerProduct $consumerProduct, string $key, mixed $default = null): mixed
    {
        $consumerProductPayload = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}?->payload;
        if (!$consumerProductPayload) return null;
        else return $consumerProductPayload[$key] ?? $default;
    }

    /**
     * Return an array of ConsumerProductData field values by array of keys
     *
     * @param ConsumerProduct $consumerProduct
     * @param string[] $keys
     * @param mixed|null $default
     * @return array
     */
    public function getConsumerProductDataByKeysArray(ConsumerProduct $consumerProduct, array $keys, mixed $default = null): array
    {
        $payload = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->payload;

        if (!$payload) return [];

        return array_reduce($keys, fn($out, $keyName) => [ ...$out, $keyName => $payload[$keyName] ?? $default], []);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ConsumerConfigurableFieldCategoryRepository $fieldRepository
     * @param ConsumerProductDataRepository $consumerProductDataRepository
     * @return array|null
     */
    public function getPayloadDataToDisplay(
        ConsumerProduct                             $consumerProduct,
        ConsumerConfigurableFieldCategoryRepository $fieldRepository,
        ConsumerProductDataRepository               $consumerProductDataRepository
    ): ?array
    {
        $categories = $fieldRepository->getLeadDisplayCategories()->toArray();
        $category_ids = array_map(function ($key) {return $key['id'];}, $categories);
        $output_data = [];

        $display_fields = $this->getLeadDisplayFieldsByCategory($category_ids, $consumerProduct->{ConsumerProduct::FIELD_SERVICE_PRODUCT_ID})->toArray();
        $mapped_payload_data = $consumerProductDataRepository->mapPayloadDataToDisplayFields($display_fields, $consumerProduct->{ConsumerProduct::FIELD_ID});

        if (!$mapped_payload_data) {
            return null;
        }

        $output_data[GlobalConfigurableFields::ROOF_TYPE->value] = $mapped_payload_data?->{GlobalConfigurableFields::ROOF_TYPE->value};
        $mapped_payload_data                                     = json_decode($mapped_payload_data->result ?? "{}", true, JSON_THROW_ON_ERROR);

        /**
         * Sort matched payload fields into groups by category
         * Eg:
         *   ['Lead Information': ['System Size': 4546.84, 'System Type': 'Solar with battery storage'],
         *   ['Contact Information': ['phone': 041234578, 'email': '<EMAIL>', 'website': 'solarreviews.com' ]
         */
        foreach ($categories as $category) {
            $output_data[$category['name']] = [];
            foreach ($mapped_payload_data as $field_name => $payload_value) {
                if ($payload_value['cat_id'] === $category['id']) {
                    $output_data[$category['name']][] = [
                        'name' => $field_name,
                        'value' => $payload_value['value']
                    ];
                }
            }
        }

        return $output_data;
    }

    /**
     * Returns the Service & Industry Consumer Fields where:
     * - the category_id is within the passed $category_ids array
     * - the service_consumer_field's service_product_id is equal to the passed $service_product_id
     * - the industry_consumer_field's industry ID is equal to the passed $service_product_id's related Industry Service:Industry ID
     * - the service_consumer_field has 'Show On Dashboard' enabled
     *
     * @param array $category_ids
     * @param int $service_product_id
     * @return Collection
     */
    public function getLeadDisplayFieldsByCategory(array $category_ids, int $service_product_id): Collection
    {
        return ServiceConsumerField::query()
            ->select([
                ServiceConsumerField::TABLE.'.'.ServiceConsumerField::FIELD_ID,
                ServiceConsumerField::TABLE.'.'.ServiceConsumerField::FIELD_KEY,
                ServiceConsumerField::TABLE.'.'.ServiceConsumerField::FIELD_NAME,
                ServiceConsumerField::TABLE.'.'.ServiceConsumerField::FIELD_CATEGORY_ID,
            ])
            ->join(
                IndustryService::TABLE,
                ServiceConsumerField::TABLE.'.'.ServiceConsumerField::FIELD_INDUSTRY_SERVICE_ID,
                '=',
                IndustryService::TABLE.'.'.IndustryService::FIELD_ID
            )
            ->join(
                ServiceProduct::TABLE,
                ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID,
                '=',
                IndustryService::TABLE.'.'.IndustryService::FIELD_ID
            )
            ->where(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID, '=', $service_product_id)
            ->where(ServiceConsumerField::TABLE.'.'.ServiceConsumerField::FIELD_SHOW_ON_DASHBOARD, '=', 1)
            ->whereIn(ServiceConsumerField::TABLE.'.'.ServiceConsumerField::FIELD_CATEGORY_ID, $category_ids)
            ->union(
                // Select the industry_consumer_fields by using the Consumer Product's $service_product_id
                // Table flow: Service Product -> Industry Service -> Industry Consumer Fields
                ServiceProduct::query()
                    ->select([
                        IndustryConsumerField::TABLE.'.'.IndustryConsumerField::FIELD_ID,
                        IndustryConsumerField::TABLE.'.'.IndustryConsumerField::FIELD_KEY,
                        IndustryConsumerField::TABLE.'.'.IndustryConsumerField::FIELD_NAME,
                        IndustryConsumerField::TABLE.'.'.IndustryConsumerField::FIELD_CATEGORY_ID,
                    ])
                    ->join(
                        IndustryService::TABLE,
                        ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID,
                        '=',
                        IndustryService::TABLE.'.'.IndustryService::FIELD_ID,
                    )
                    ->join(
                        Industry::TABLE,
                        Industry::TABLE.'.'.Industry::FIELD_ID,
                        '=',
                        IndustryService::TABLE.'.'.IndustryService::FIELD_INDUSTRY_ID
                    )
                    ->join(
                        IndustryConsumerField::TABLE,
                        IndustryConsumerField::TABLE.'.'.IndustryConsumerField::FIELD_INDUSTRY_ID,
                        '=',
                        Industry::TABLE.'.'.Industry::FIELD_ID,
                    )
                    ->where(ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID, '=', $service_product_id)
                    ->whereIn(IndustryConsumerField::TABLE.'.'.IndustryConsumerField::FIELD_CATEGORY_ID, $category_ids)
            )
            ->get();
    }
}
