<?php

namespace App\Transformers\Odin;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Models\Odin\Company;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CompanyTransformer
{
    /**
     * @param Collection<Company> $companies
     *
     * @return array
     */
    public function transformCompanies(Collection $companies): array
    {
        return $companies->map(fn(Company $company) => $this->transformCompany($company))->toArray();
    }

    /**
     * @param Company $company
     *
     * @return array
     */
    public function transformCompany(Company $company): array
    {
        return [
            'id'           => $company->{Company::FIELD_ID},
            'name'         => $company->{Company::FIELD_NAME},
            'status'       => CompanyConsolidatedStatus::label($company->consolidated_status),
            'sales_status' => CompanySalesStatus::label($company->sales_status),
            'source'       => $company->{Company::FIELD_WEBSITE} ?: "Unknown",
            'created_at'   => $company->{Model::CREATED_AT}?->toIso8601String(),
        ];
    }
}
