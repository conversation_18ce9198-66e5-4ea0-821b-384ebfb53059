<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Product;
use Illuminate\Support\Collection;

class ProductTransformer
{
    /**
     * @param Collection<Product> $products
     *
     * @return array
     */
    public function transformAll(Collection $products): array
    {
        return $products->toArray();
    }

    /**
     * @param Product $product
     *
     * @return array
     */
    public function transformProduct(Product $product): array
    {
        return [
            'id' => $product->id,
            'name' => $product->name,
            'create' => $product->created_at?->toDateString()
        ];
    }
}
