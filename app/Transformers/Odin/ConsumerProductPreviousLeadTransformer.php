<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Industry;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;

class ConsumerProductPreviousLeadTransformer
{

    public function __construct(protected ConsumerProductDataTransformer $consumerProductDataTransformer) {}

    const CONSUMER_PRODUCT_NAME     = 'name';
    const CONSUMER_PRODUCT_SOURCE   = 'source';

    /**
     * Transform a ConsumerProduct for Pre-Raised Leads table
     *
     * @param ConsumerProduct $consumerProduct
     * @return array
     */
    public function transform(ConsumerProduct $consumerProduct): array
    {
        $consumer =  $consumerProduct->{ConsumerProduct::RELATION_CONSUMER};

        $leadStatusDisplayText  = ConsumerProduct::STATUS_TEXT[$consumerProduct->{ConsumerProduct::FIELD_STATUS}];
        $leadName               = $consumer?->getFullName();
        $leadSourceId           = $consumer?->{Consumer::FIELD_WEBSITE_ID};
        $leadSource             = Website::query()->find($leadSourceId)
                                    ?->{Website::FIELD_NAME}
                                    ?? 'Unknown';

        return [
            ConsumerProduct::FIELD_ID       => $consumerProduct->{ConsumerProduct::FIELD_ID},
            Model::CREATED_AT               => $consumerProduct->{Model::CREATED_AT},
            ConsumerProduct::FIELD_STATUS   => $leadStatusDisplayText ?? 'Unknown',
            self::CONSUMER_PRODUCT_NAME     => $leadName,
            self::CONSUMER_PRODUCT_SOURCE   => $leadSource
        ];
    }

    /**
     * @param Collection $consumerProducts
     * @return array
     */
    public function transformConsumerProducts(Collection $consumerProducts): array
    {
        return $consumerProducts->map((fn(ConsumerProduct $consumerProduct) => $this->transform($consumerProduct)))
            ->toArray();
    }
}
