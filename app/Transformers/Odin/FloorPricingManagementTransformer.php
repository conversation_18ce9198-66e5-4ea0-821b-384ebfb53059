<?php

namespace App\Transformers\Odin;

use App\Builders\Pricing\BasePricingBuilder;
use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\Location;
use App\Models\Odin\FloorPriceFormula;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Repositories\Odin\ProductProcessing\FloorPriceFormulaRepository;
use App\Services\Odin\FloorPriceFormulaService;
use App\Services\Odin\Pricing\FloorPricingManagementService;
use Illuminate\Support\Collection;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;

class FloorPricingManagementTransformer
{
    const string FLOOR_PRICING_KEY_INHERITED_PRICE  = 'inherited_price';
    const string FLOOR_PRICING_KEY_FORMULA          = 'formula';
    const string FLOOR_PRICING_KEY_EXPLICIT_PRICE   = 'explicit_price';
    const string FLOOR_PRICING_KEY_CALCULATED_PRICE = 'calculated_price';
    const string RETURN_KEY_PRICES                  = 'prices';
    const string RETURN_KEY_MISSING_STATE_PRICES    = 'missing_prices';

    protected array $defaultFloorPriceFormula = [];

    public function __construct(
        protected FloorPriceFormulaRepository $floorPriceFormulaRepository,
    )
    {
        $this->defaultFloorPriceFormula = FloorPriceFormula::defaultAppointmentFormula()->toArray();
    }

    /**
     * Transform State prices for Minimum Price Management
     * @param Collection $statePrices
     * @param int $serviceProductId
     * @param bool $checkMissingPrices
     * @return array
     */
    public function transformStatePrices(Collection $statePrices, int $serviceProductId, bool $checkMissingPrices = false): array
    {
        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()->findOrFail($serviceProductId);
        $product = ProductEnum::from($serviceProduct->product->name);
        $industry = IndustryEnum::from($serviceProduct->service->industry->name);

        $useFormulaPrice = FloorPricingManagementService::shouldUseFormulaPricing($serviceProductId);
        $statePrices = $statePrices->groupBy([
            ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
        ]);

        $stateMap = Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->get()
            ->mapWithKeys(fn(Location $state) => [$state->id => $state->state_key]);

        $saleTypeMap = $this->getValidSaleTypes($serviceProductId);

        $missingPrices = false;

        $priceArray = $statePrices->reduce(function(array $output, Collection $saleTypeGroup, int $stateLocationId) use (&$stateMap, &$saleTypeMap, &$useFormulaPrice, &$industry, &$product, &$missingPrices) {
            $output[$stateMap[$stateLocationId]] = $output[$stateMap[$stateLocationId]] ?? [
                ProductStateFloorPrice::FIELD_STATE_LOCATION_ID => $stateLocationId,
            ];

            foreach($saleTypeMap as $saleTypeId => $saleTypeKey) {
                $targetPrice = $saleTypeGroup->where(ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)->first();
                if (!$targetPrice || !$targetPrice->price) $missingPrices = true;

                $formula         = $targetPrice && $useFormulaPrice ? $this->getFormula($industry, $product, $targetPrice) : null;
                $calculatedPrice = $targetPrice && $useFormulaPrice ? $this->getCalculatedPrice($industry, $product, $targetPrice) : null;

                $output[$stateMap[$stateLocationId]][$saleTypeKey] = [
                    self::FLOOR_PRICING_KEY_FORMULA          => $formula,
                    self::FLOOR_PRICING_KEY_INHERITED_PRICE  => null,
                    self::FLOOR_PRICING_KEY_CALCULATED_PRICE => $calculatedPrice,
                    self::FLOOR_PRICING_KEY_EXPLICIT_PRICE   => $targetPrice?->price ?? null,
                ];
            }

            return $output;
        }, []);

        ksort($priceArray, SORT_STRING | SORT_FLAG_CASE);

        return $checkMissingPrices
            ? [
                self::RETURN_KEY_PRICES               => $priceArray,
                self::RETURN_KEY_MISSING_STATE_PRICES => $missingPrices,
            ]
            : $priceArray;
    }

    /**
     * @param int $stateLocationId
     * @return Collection
     */
    protected function getCountyIdMap(int $stateLocationId): Collection
    {
        $stateAbbreviation = Location::query()
            ->findOrFail($stateLocationId)
            ->state_abbr;

        return Location::query()
            ->select([Location::STATE_ABBREVIATION, Location::ID, Location::COUNTY_KEY])
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->where(Location::STATE_ABBREVIATION, $stateAbbreviation)
            ->get()
            ->groupBy(Location::STATE_ABBREVIATION);
    }

    /**
     * Transform County prices for Minimum Price Management
     *
     * @param Collection $statePrices
     * @param Collection $countyPrices
     * @param int $serviceProductId
     * @param int $stateLocationId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @return array
     */
    public function transformCountyPrices(Collection $statePrices, Collection $countyPrices, int $serviceProductId, int $stateLocationId, int $qualityTierId, int $propertyTypeId): array
    {
        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()->findOrFail($serviceProductId);
        $product = ProductEnum::from($serviceProduct->product->name);
        $industry = IndustryEnum::from($serviceProduct->service->industry->name);

        $useFormulaPrice = FloorPricingManagementService::shouldUseFormulaPricing($serviceProductId);
        $countyPrices = $countyPrices->reduce(function(array $output, ProductStateFloorPrice|ProductCountyFloorPrice $price) {
            $output[$price->county_location_id] = $output[$price->county_location_id] ?? [];
            $output[$price->county_location_id][$price->sale_type_id] = $price;

            return $output;
        }, []);

        $countyIdMap = $this->getCountyIdMap($stateLocationId);
        $saleTypeMap = $this->getValidSaleTypes($serviceProductId);

        $statePrices = $statePrices
          ->reduce(function(array $output, ProductStateFloorPrice|ProductCountyFloorPrice $price) {
                $output[$price->sale_type_id] = $price->price;
                return $output;
            }, []);

        $priceArray =  $countyIdMap->reduce(function(array $output, Collection $countyGroup) use (&$countyIdMap, &$saleTypeMap, &$useFormulaPrice, &$industry, &$product, &$statePrices, &$countyPrices, $stateLocationId, $serviceProductId) {
            foreach ($countyGroup as $county) {
                $output[$county->county_key] = $output[$county->county_key] ?? [
                    ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID => $county->id,
                ];

                foreach ($saleTypeMap as $saleTypeId => $saleTypeKey) {
                    $targetPrice = $this->getCountyPriceIfExists($countyPrices, $county->id, $saleTypeId);
                    $statePrice = $statePrices[$saleTypeId] ?? 0;

                    $formula         = $targetPrice && $useFormulaPrice ? $this->getFormula($industry, $product, $targetPrice) : null;
                    $calculatedPrice = $targetPrice && $useFormulaPrice ? $this->getCalculatedPrice($industry, $product, $targetPrice) : null;

                    $output[$county->county_key][$saleTypeKey] = [
                        self::FLOOR_PRICING_KEY_FORMULA          => $formula,
                        self::FLOOR_PRICING_KEY_INHERITED_PRICE  => $statePrice,
                        self::FLOOR_PRICING_KEY_CALCULATED_PRICE => $calculatedPrice,
                        self::FLOOR_PRICING_KEY_EXPLICIT_PRICE   => $targetPrice?->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE} ?? $statePrice ?? null,
                    ];
                }
            }

            return $output;
        }, []);

        ksort($priceArray, SORT_STRING | SORT_FLAG_CASE);

        return $priceArray;
    }

    /**
     * @param array $prices
     * @param int $countyLocationId
     * @param int $saleTypeId
     * @return ProductStateFloorPrice|ProductCountyFloorPrice|null
     */
    protected function getCountyPriceIfExists(array &$prices, int $countyLocationId, int $saleTypeId): ProductStateFloorPrice|ProductCountyFloorPrice|null
    {
        if (array_key_exists($countyLocationId, $prices)) {
            return $prices[$countyLocationId][$saleTypeId] ?? null;
        }

        return null;
    }

    /**
     * @param int $serviceProductId
     * @return array
     */
    protected function getValidSaleTypes(int $serviceProductId): array
    {
        return SaleType::query()
            ->select([SaleType::FIELD_ID, SaleType::FIELD_KEY])
            ->whereIn(SaleType::FIELD_NAME, SaleTypes::byServiceProductId($serviceProductId))
            ->get()
            ->mapWithKeys(fn(SaleType $saleType) => [$saleType->id => $saleType->key])
            ->toArray();
    }

    /**
     * @param IndustryEnum $industry
     * @param ProductEnum $product
     * @param ProductStateFloorPrice|ProductCountyFloorPrice $floorPrice
     *
     * @return array
     */
    protected function getFormula(IndustryEnum $industry, ProductEnum $product, ProductStateFloorPrice|ProductCountyFloorPrice $floorPrice): array
    {
        return $this->floorPriceFormulaRepository->getFormula(
            $industry->value,
            $product->value,
            $floorPrice->county_location_id ?? $floorPrice->state_location_id,
            $floorPrice->propertyType->name,
            $floorPrice->qualityTier->name,
            $floorPrice->saleType->name
        )?->toArray() ?? $this->defaultFloorPriceFormula;
    }

    /**
     * @param IndustryEnum $industry
     * @param ProductEnum $product
     * @param ProductStateFloorPrice|ProductCountyFloorPrice $floorPrice
     * @return float|null
     */
    protected function getCalculatedPrice(IndustryEnum $industry, ProductEnum $product, ProductStateFloorPrice|ProductCountyFloorPrice $floorPrice): ?float
    {
        $formula = $this->floorPriceFormulaRepository->getFormula(
            $industry->value,
            $product->value,
            $floorPrice->county_location_id ?? $floorPrice->state_location_id,
            $floorPrice->propertyType->name,
            $floorPrice->qualityTier->name,
            $floorPrice->saleType->name
        );

        return $formula ? FloorPriceFormulaService::resolveFormulaForBasePrice($formula, $floorPrice->price) : null;
    }
}
