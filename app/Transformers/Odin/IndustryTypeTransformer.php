<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryType;
use App\Transformers\ReferenceLists\GlobalTypeTransformer;
use Illuminate\Support\Collection;

class IndustryTypeTransformer
{
    /**
     * @param GlobalTypeTransformer $globalTypeTransformer
     */
    public function __construct(protected GlobalTypeTransformer $globalTypeTransformer){}

    /**
     * @param Collection<IndustryType> $industryTypes
     * @return array
     */
    public function transform(Collection $industryTypes): array
    {
        return $industryTypes->map(fn(IndustryType $industryType) => $this->transformIndustryType($industryType))->toArray();
    }

    /**
     * @param IndustryType $industryType
     * @return array
     */
    public function transformIndustryType(IndustryType $industryType): array
    {
        return
            [
                'id'           => $industryType->{IndustryType::FIELD_ID},
                'global_type'  => $industryType->{IndustryType::RELATION_GLOBAL_TYPE} ?
                                  $this->globalTypeTransformer->transformGlobalType($industryType->{IndustryType::RELATION_GLOBAL_TYPE}) : null,
                'industry'     => $industryType->{IndustryType::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
                'name'         => $industryType->{IndustryType::FIELD_NAME},
                'key'          => $industryType->{IndustryType::FIELD_KEY},
                'created_at'   => $industryType->{IndustryType::CREATED_AT}?->toIso8601String()
            ];
    }
}

