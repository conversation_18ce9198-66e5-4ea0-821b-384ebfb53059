<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyService;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Collection;

class CompanyServiceTransformer
{
    /**
     * @param Collection $companyServices
     * @return array
     */
    public function transform(Collection $companyServices): array
    {
        return $companyServices->map(fn(CompanyService $companyService) => $this->transformCompanyService($companyService))->toArray();
    }

    /**
     * @param CompanyService $companyService
     * @return array
     */
    public function transformCompanyService(CompanyService $companyService): array
    {
        return
            [
                'id'          => $companyService->{CompanyService::FIELD_ID},
                'industry'    => $companyService->{CompanyService::RELATION_SERVICE}?->{IndustryService::FIELD_NAME},
                'company'     => $companyService->{CompanyService::RELATION_COMPANY}?->{Company::FIELD_NAME},
                'created_at'  => $companyService->{CompanyService::CREATED_AT}?->toIso8601String()
            ];
    }
}
