<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\Odin\IndustryService;
use App\Transformers\EmailTemplates\TemplatesListTransformer;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class IndustryTransformer
{
    /**
     * @param IndustryServiceTransformer $serviceTransformer
     */
    public function __construct(protected IndustryServiceTransformer $serviceTransformer){}

    /**
     * @param Collection $industries
     * @return array
     */
    public function transform(Collection $industries): array
    {
        return $industries->map(fn(Industry $industry) => $this->transformIndustry($industry))->toArray();
    }

    /**
     * @param Industry $industry
     * @return array
     */
    public function transformIndustry(Industry $industry): array
    {
        /** @var TemplatesListTransformer $templateTransformer */
        $templateTransformer = app()->make(TemplatesListTransformer::class);

        return
            [
                'id'                        => $industry->{Industry::FIELD_ID},
                'num_id'                    => $industry->{Industry::FIELD_ID},
                'name'                      => $industry->{Industry::FIELD_NAME},
                Industry::FIELD_SLUG        => $industry->{Industry::FIELD_SLUG},
                Industry::FIELD_COLOR_LIGHT => $industry->{Industry::FIELD_COLOR_LIGHT},
                Industry::FIELD_COLOR_DARK  => $industry->{Industry::FIELD_COLOR_DARK},
                'services'                  => array_key_exists(Industry::RELATION_SERVICES, $industry->toArray()) ? $this->handleServices($industry->toArray()[Industry::RELATION_SERVICES]) : [],
                'created_at'                => $industry->{Industry::CREATED_AT} ? $industry->created_at->toIso8601String() : null,
                'configuration'             => $this->getConfiguration($industry),
                'deliveryEmailTemplate'     => $industry->deliveryEmailTemplate ? $templateTransformer->transformTemplate($industry->deliveryEmailTemplate) : null
            ];
    }

    /**
     * @param $industryServices
     * @return array
     */
    protected function handleServices($industryServices): array
    {
        return collect($industryServices)->map(function ($service) {
            $industryService = new IndustryService();
            $industryService->fill($service);
            if(isset($service['company_services'])) $industryService->setRelation('company_services', $service['company_services']);
            return $this->serviceTransformer->transformIndustryService($industryService);
        })->toArray();
    }

    /**
     * @param Industry $industry
     * @return ?array
     */
    protected function getConfiguration(Industry $industry): ?array
    {
        if (!$industry->industryConfiguration) return null;

        return array_filter(($industry->industryConfiguration?->toArray() ?? []), fn($key) => !in_array($key, [
            Model::CREATED_AT,
            Model::UPDATED_AT,
            IndustryConfiguration::FIELD_INDUSTRY_ID,
            IndustryConfiguration::FIELD_ID
        ]), ARRAY_FILTER_USE_KEY);
    }
}
