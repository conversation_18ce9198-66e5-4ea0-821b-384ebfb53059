<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Consumer;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\OptInCompany;
use App\Models\Odin\Website;
use App\Models\RecycledLeads\RecycledLead;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class ConsumerProductBasicInfoTransformer
{

    public function __construct(protected ConsumerProductDataTransformer $consumerProductDataTransformer, protected ProductAppointmentsTransformer $appointmentsTransformer) {}

    /**
     * Transform ConsumerProduct to basic info
     *
     * @param ConsumerProduct $consumerProduct
     * @return array
     */
    public function transform(ConsumerProduct $consumerProduct): array
    {
        $requiredDataFields  = [
            GlobalConfigurableFields::BEST_TIME_TO_CALL->value,
            GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value,
            GlobalConfigurableFields::ORIGIN->value
        ];
        $consumerProductData = $this->consumerProductDataTransformer->getConsumerProductDataByKeysArray($consumerProduct, $requiredDataFields);

        $origin              = Website::query()
            ->where(Website::FIELD_ID, $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::FIELD_WEBSITE_ID})
            ->first();

        return [
            ...$consumerProductData,
            Model::CREATED_AT                           => $consumerProduct->{Model::CREATED_AT},
            ConsumerProduct::FIELD_CONTACT_REQUESTS     => $consumerProduct->{ConsumerProduct::FIELD_CONTACT_REQUESTS},
            GlobalConfigurableFields::ORIGIN->value     => $origin->{Website::FIELD_URL} ?? $consumerProductData[GlobalConfigurableFields::ORIGIN->value] ?? "",
            GlobalConfigurableFields::COLOR_CODE->value => $origin->{Website::FIELD_ABBREVIATION} ?? "",
            'appointments'                              => $this->appointmentsTransformer->transform($consumerProduct->{ConsumerProduct::RELATION_LEAD_APPOINTMENTS} ?? []),
            'opt_in_companies'                          => $this->transformOptInCompanies($consumerProduct->optInCompanies),
            'sold_from_aged_queue'                      => $this->soldFromAgedQueue($consumerProduct),
            'channel'                                   => $consumerProduct->channel,
        ];
    }

    /**
     * @param Collection<OptInCompany> $optInCompanies
     *
     * @return array
     */
    public function transformOptInCompanies(Collection $optInCompanies): array
    {
        return $optInCompanies->map(fn(OptInCompany $optInCompany) => [
            'company_id' => $optInCompany->company->id,
            'company_name' => $optInCompany->company->name
        ])->toArray();
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return bool
     */
    protected function soldFromAgedQueue(ConsumerProduct $consumerProduct): bool
    {
        return $consumerProduct->cloned_from_id && $consumerProduct->clonedFrom?->recycledLeads()
                ->where(RecycledLead::FIELD_STATUS, RecycledLead::STATUS_SOLD)->first();
    }
}
