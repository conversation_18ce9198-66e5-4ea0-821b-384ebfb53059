<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;

class CompanyLocationTransformer
{
    public function __construct(protected AddressTransformer $addressTransformer) {}

    /**
     * @param Company $company
     *
     * @return array
     */
    public function transformCompanyLocations(Company $company): array
    {
        return $company->locations->map(fn(CompanyLocation $location) => $this->transformCompanyLocation($location))->toArray();
    }

    /**
     * @param CompanyLocation $companyLocation
     *
     * @return array
     */
    public function transformCompanyLocation(CompanyLocation $companyLocation): array
    {
        return [
            'id' => $companyLocation->id,
            'name' => $companyLocation->name,
            'phone' => $companyLocation->phone,
            'address' => $this->addressTransformer->transformAddress($companyLocation->address)
        ];
    }
}
