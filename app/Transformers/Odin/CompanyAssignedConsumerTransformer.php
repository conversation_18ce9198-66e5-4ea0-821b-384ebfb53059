<?php

namespace App\Transformers\Odin;

use App\Enums\Billing\BillingVersion;
use App\Enums\PermissionType;
use App\Models\Billing\InvoiceItem;
use App\Models\Legacy\CrmDeliveryLog;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Services\LeadRefundService;
use Illuminate\Support\Facades\Auth;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\LeadRefundItem;
use App\Repositories\Odin\ProductAssignmentRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class CompanyAssignedConsumerTransformer
{

    public function __construct(
        protected ProductAssignmentRepository $productAssignmentRepository,
        protected LeadRefundService $leadRefundService
    )
    {
    }

    /**
     * @param Consumer $consumer
     * @param int $companyId
     * @param int $consumerProductId
     * @param int $productAssignmentId
     * @return array
     * @throws BindingResolutionException
     */
    public function transform(
        Consumer $consumer,
        int $companyId,
        int $consumerProductId,
        int $productAssignmentId
    ): array
    {
        $consumerProduct = ConsumerProduct::query()->find($consumerProductId);

        $productAssignment = ProductAssignment::query()->find($productAssignmentId);

        $address = Auth::user()?->hasPermissionTo(PermissionType::VIEW_LEAD_PII->value) ? $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}?->getFullAddress() : $consumerProduct->{ConsumerProduct::RELATION_ADDRESS}?->getObfuscatedAddress();

        $campaignName = Str::replace(
            'admin_2_generated_',
            '',
            $productAssignment
                ?->leadCampaignSalesTypeConfiguration
                ?->{LeadCampaignSalesTypeConfiguration::RELATION_LEAD_CAMPAIGN}
                ?->{LeadCampaign::NAME}
            ?? "");

        $campaignName = $productAssignment?->budget?->budgetContainer?->campaign()->withTrashed()->first()?->name ?? $campaignName;

        $deliveredAt =  $productAssignment?->{ProductAssignment::FIELD_DELIVERED_AT}?->timestamp;

        $deliveredAt = ($deliveredAt < 1) ? 'Not Delivered' : $productAssignment?->{ProductAssignment::FIELD_DELIVERED_AT}?->format('Y-m-d');

        return [
            'id'                    => $consumer->{Consumer::FIELD_ID},
            'legacy_id'             => $consumer->{Consumer::FIELD_LEGACY_ID},
            'consumer_product_id'   => $consumerProduct->{ConsumerProduct::FIELD_ID},
            'product_assignment_id' => $productAssignment?->{ProductAssignment::FIELD_ID},
            'industry'              => $consumerProduct->{ConsumerProduct::RELATION_INDUSTRY_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME},
            'service'               => $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::FIELD_NAME},
            'date'                  => $consumer->{Consumer::FIELD_CREATED_AT}->format('Y-m-d') ?? null,
            'delivered_at'          => $deliveredAt,
            'chargeable'            => $productAssignment?->{ProductAssignment::FIELD_CHARGEABLE} ? 'Yes' : 'No',
            'delivered'             => $productAssignment?->{ProductAssignment::FIELD_DELIVERED} ? 'Yes' : 'No',
            'delivery_logs'         => $this->getDeliveryLogs($consumer, $companyId),
            'contact'               => $consumer->getFullName(),
            'address'               => $address,
            'cost'                  => $productAssignment?->{ProductAssignment::FIELD_COST},
            'rejection_expiry'      => $productAssignment?->{ProductAssignment::FIELD_REJECTION_EXPIRY}->format('Y-m-d'),
            'can_no_charge'         => $productAssignment && $this->productAssignmentRepository->canNoCharge($productAssignment),
            'campaign'              => $campaignName,
            'is_test_lead'          => (bool)$consumerProduct->testProduct,
            ...$this->getLeadRefundData($productAssignment),
        ];
    }

    /**
     * @return array{is_refundable: bool, refund_status: null|string, refund_lead_id: mixed}
     * @throws BindingResolutionException
     */
    private function getLeadRefundData(?ProductAssignment $productAssignment = null): array
    {
        if (empty($productAssignment)) {
            return [];
        }

        $billingVersion = $productAssignment->getBillingVersion();

        return [
            'billing_version' => $billingVersion,
            'is_refundable'   => $this->leadRefundService->checkIfProductAssignmentIsRefundable(
                productAssignment: $productAssignment,
            ),
            'refund_status'   => $productAssignment->consolidatedLeadRefundStatus(),
            'refund_lead_id'  => $productAssignment
                ?->{ProductAssignment::RELATION_LATEST_LEAD_REFUND_ITEM}
                ?->{LeadRefundItem::RELATION_REFUND}
                ?->id,
            'invoice_id' => $billingVersion === BillingVersion::V2
                ? $productAssignment->odinInvoiceItem?->{InvoiceItem::FIELD_INVOICE_ID}
                : $productAssignment->getEloquentInvoice()?->{EloquentInvoice::INVOICE_ID},
            'rejection' => $productAssignment->productRejections()->first() ?? false,
            'cancellation' => $productAssignment->productCancellations()->first() ?? false,
        ];
    }

    /**
     * @param Consumer $consumer
     * @param int $companyId
     * @return Collection|null
     */
    private function getDeliveryLogs(Consumer $consumer, int $companyId): ?Collection
    {
        $company = Company::find($companyId);

        if ($company->{Company::FIELD_LEGACY_ID}) {
            return $consumer->{Consumer::RELATION_LEGACY_LEAD}
                ?->{EloquentQuote::RELATION_CRM_DELIVERY_LOGS}
                ?->where(CrmDeliveryLog::COMPANY_ID, $company->{Company::FIELD_LEGACY_ID});
        }

        return null;
    }
}
