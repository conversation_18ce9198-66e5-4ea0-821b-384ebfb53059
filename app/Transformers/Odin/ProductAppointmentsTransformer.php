<?php

namespace App\Transformers\Odin;

use App\Models\Odin\ProductAppointment;
use Illuminate\Support\Collection;

class ProductAppointmentsTransformer
{
    /**
     * @param Collection<ProductAppointment> $appointments
     *
     * @return array
     */
    public function transform(Collection $appointments): array
    {
        return $appointments->map(fn(ProductAppointment $appointment) => $this->transformAppointment($appointment))->toArray();
    }

    /**
     * @param ProductAppointment $appointment
     *
     * @return array
     */
    public function transformAppointment(ProductAppointment $appointment): array
    {
        return [
            'id'   => $appointment->id,
            'type' => $appointment->{ProductAppointment::APPOINTMENT_TYPE}->value,
            'date' => $appointment->appointment_date,
            'time' => $appointment->appointment_time
        ];
    }
}
