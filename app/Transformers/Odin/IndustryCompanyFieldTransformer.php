<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryCompanyField;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class IndustryCompanyFieldTransformer
{
    /**
     * @param ConfigurableFieldTypeTransformer $fieldTypeTransformer
     */
    public function __construct(protected ConfigurableFieldTypeTransformer $fieldTypeTransformer){}

    /**
     * @param Collection $industryCompanyFields
     * @return array
     */
    public function transform(Collection $industryCompanyFields): array
    {
        return $industryCompanyFields->map(fn(IndustryCompanyField $industryCompanyField) => $this->transformIndustryCompanyField($industryCompanyField))->toArray();
    }

    /**
     * @param IndustryCompanyField $industryCompanyField
     * @return array
     */
    public function transformIndustryCompanyField(IndustryCompanyField $industryCompanyField): array
    {
        return
            [
                'id'                => $industryCompanyField->{IndustryCompanyField::FIELD_ID},
                'industry'          => $industryCompanyField->{IndustryCompanyField::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
                'name'              => $industryCompanyField->{IndustryCompanyField::FIELD_NAME},
                'key'               => $industryCompanyField->{IndustryCompanyField::FIELD_KEY},
                'type'              => $industryCompanyField->{IndustryCompanyField::RELATION_FIELD_TYPE} ?
                                       $this->fieldTypeTransformer->transformConfigurableFieldType($industryCompanyField->{IndustryCompanyField::RELATION_FIELD_TYPE}) : null,
                'show_on_profile'   => $industryCompanyField->{IndustryCompanyField::FIELD_SHOW_ON_PROFILE} === 1 ? 'Yes' : 'No',
                'show_on_dashboard' => $industryCompanyField->{IndustryCompanyField::FIELD_SHOW_ON_DASHBOARD} === 1 ? 'Yes' : 'No',
                'payload'           => $industryCompanyField->{IndustryCompanyField::FIELD_PAYLOAD},
                'created_at'        => $industryCompanyField->{Model::CREATED_AT}?->toIso8601String(),
                'category'          => $industryCompanyField->category
            ];
    }
}
