<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\Industry;
use Illuminate\Support\Collection;

class IndustryCompanyTransformer
{
    /**
     * @param CompanyTransformer $companyTransformer
     */
    public function __construct(protected CompanyTransformer $companyTransformer){}

    /**
     * @param Collection $industryCompanies
     * @return array
     */
    public function transform(Collection $industryCompanies): array
    {
        return $industryCompanies->map(fn(CompanyIndustry $industryCompany) => $this->transformIndustryCompany($industryCompany))->toArray();
    }

    /**
     * @param CompanyIndustry $industryCompany
     * @return array
     */
    public function transformIndustryCompany(CompanyIndustry $industryCompany): array
    {
        return
            [
                'id'          => $industryCompany->{CompanyIndustry::FIELD_ID},
                'industry'    => $industryCompany->{CompanyIndustry::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
                'company'     => $this->companyTransformer->transformCompany($industryCompany->{CompanyIndustry::RELATION_COMPANY}),
                'created_at'  => $industryCompany->{CompanyIndustry::CREATED_AT}?->toIso8601String()
            ];
    }
}
