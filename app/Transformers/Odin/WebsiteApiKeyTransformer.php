<?php

namespace App\Transformers\Odin;

use App\Http\Resources\Odin\WebsiteApiKeyOriginResource;
use App\Models\Odin\WebsiteApiKey;
use Illuminate\Support\Collection;

class WebsiteApiKeyTransformer
{
    /**
     * @param Collection<WebsiteApiKey> $apiKeys
     *
     * @return array
     */
    public function transformAll(Collection $apiKeys): array
    {
        return $apiKeys->map(fn(WebsiteApiKey $websiteApiKey) => $this->transformApiKey($websiteApiKey))->toArray();
    }

    /**
     * @param WebsiteApiKey $apiKey
     *
     * @return array
     */
    public function transformApiKey(WebsiteApiKey $apiKey): array
    {
        return [
            'id'      => $apiKey->id,
            'name'    => $apiKey->name,
            'key'     => $apiKey->key,
            'status'  => $apiKey->status,
            'created' => $apiKey->created_at?->toDateString(),
            'origins' => WebsiteApiKeyOriginResource::collection($apiKey->origins)
        ];
    }
}
