<?php

namespace App\Transformers\Odin;

use App\DataModels\Odin\CompanyFieldDataModel;
use App\Models\Odin\Company;
use App\Models\Odin\ConfigurableFieldType;
use App\Models\Odin\GlobalCompanyField;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryCompanyField;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceCompanyField;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Support\Collection;

class CompanyDataTransformer
{
    const DEFAULT_VALUE = '-';

    /**
     * @param CompanyRepository $companyRepository
     */
    public function __construct(protected CompanyRepository $companyRepository) {}

    /**
     * @param Company $company
     * @param ?array $filterByKeyValue - array of key => values to filter models by
     *
     * @return Collection<string, CompanyFieldDataModel>
     */
    public function transform(Company $company, ?array $filterByKeyValue = null): Collection
    {
        $companyFields = GlobalCompanyField::query()->get()->map(fn(GlobalCompanyField $globalCompanyField) => new CompanyFieldDataModel(

            $globalCompanyField->category,
            $globalCompanyField->name,
            $globalCompanyField->key,
            $globalCompanyField->fieldType->type,
            !! $globalCompanyField->show_on_profile,
            !! $globalCompanyField->show_on_dashboard,
            $this->getCompanyData($company, $globalCompanyField->key, $globalCompanyField->fieldType->type),
            'Global',
        ));

        $company->industries->unique()->every(fn(Industry $industry) => $industry->companyFields
            ->every(fn(IndustryCompanyField $industryCompanyField) => $companyFields->push(
                new CompanyFieldDataModel(
                    $industryCompanyField->category,
                    $industryCompanyField->name,
                    $industryCompanyField->key,
                    $industryCompanyField->fieldType->type,
                    !! $industryCompanyField->show_on_profile,
                    !! $industryCompanyField->show_on_dashboard,
                    $this->getCompanyData($company, $industryCompanyField->key, $industryCompanyField->fieldType->type),
                    'Industries',
                    $industry->name,
                )
            )));

        $company->services->unique()->every(fn(IndustryService $service) => $service->companyFields
            ->every(fn(ServiceCompanyField $serviceCompanyField) => $companyFields->push(
                new CompanyFieldDataModel(
                    $serviceCompanyField->category,
                    $serviceCompanyField->name,
                    $serviceCompanyField->key,
                    $serviceCompanyField->fieldType->type,
                    !! $serviceCompanyField->show_on_profile,
                    !! $serviceCompanyField->show_on_dashboard,
                    $this->getCompanyData($company, $serviceCompanyField->key, $serviceCompanyField->fieldType->type),
                    'Services',
                    "{$service->industry->name} {$service->name}"
                )
            )));

        if ($filterByKeyValue) {
            $companyFields = $companyFields->filter(function($field) use (&$filterByKeyValue) {
                foreach ($filterByKeyValue as $keyName => $value) {
                    if (!property_exists($field, $keyName) || $field->{$keyName} != $value) return false;
                }
                return true;
            });
        }

        return $companyFields
            ->groupBy(['scope', 'scopeCategory', 'category'], true);
    }

    /**
     * @param Company $company
     * @param string $key
     * @param string $type
     * @return mixed
     */
    protected function getCompanyData(Company $company, string $key, string $type): mixed
    {
        $data = $this->companyRepository->getCompanyDataByKey($company, $key, self::DEFAULT_VALUE);

        if($type === ConfigurableFieldType::TYPE_BOOLEAN) {
            if(!$data || $data === self::DEFAULT_VALUE) return false;
            return true;
        }

        return $data;
    }
}
