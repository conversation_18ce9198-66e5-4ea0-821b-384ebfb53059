<?php

namespace App\Transformers\Odin;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\CompanySocialMediaLinkTypeEnum;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Enums\Prospects\ProspectSource;
use App\Http\Resources\Odin\CompanyQualityScoreResource;
use App\Jobs\CalculateAndStoreGoogleRatingsJob;
use App\Models\CompanySlug;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentCompanyPaymentProfile;
use App\Models\Legacy\EloquentHistoryLog;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\RulesetScore;
use App\Models\Prospects\NewBuyerProspect;
use App\Repositories\ActionRepository;
use App\Repositories\CompanyAccountManagerRepository;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\DashboardShadowRepository;
use App\Repositories\Odin\QuoteRepository;
use App\Repositories\Odin\ReviewRepository;
use App\Services\Companies\CompanyManagerAssignmentService;
use App\Services\OutreachCadence\TimeZoneHelperService;
use App\Services\UserAuthorizationService;
use Illuminate\Support\Collection;

class CompanyProfileTransformer
{
    public function __construct(
        protected CompanyRepository                    $companyRepository,
        protected DashboardShadowRepository            $shadowRepository,
        protected CompanyDataTransformer               $dataTransformer,
        protected ReviewRepository                     $reviewRepository,
        protected QuoteRepository                      $quoteRepository,
        protected ActionRepository                     $actionRepository,
        protected ComputedRejectionStatisticRepository $computedRejectionStatisticRepository,
        protected CompanyAccountManagerRepository      $accountManagerRepository,
        protected IndustryTransformer                  $industryTransformer,
        protected UserAuthorizationService             $userAuthorizationService,
    )
    {
    }

    /**
     * @param Company $company
     *
     * @return array
     */
    public function transformCompanyProfileOverview(Company $company): array
    {
        $accountManager = $company->accountManager;
        $businessDevelopmentManager = $company->businessDevelopmentManager;
        $customerSuccessManager = $company->customerSuccessManager;
        $previousAccountManagerClient = $this->accountManagerRepository->getPreviousAccountManagerClient($company->{Company::FIELD_ID});
        $isFixr = !$company->industries->filter(fn(Industry $industry) => in_array($industry->name, [\App\Enums\Odin\Industry::SOLAR->value, \App\Enums\Odin\Industry::ROOFING->value]))->count() > 0;
        $latestQualityScores = $company->scores()->orderBy(RulesetScore::FIELD_CALCULATED_AT, 'desc')->get();
        $configuration = $company->configuration;
        $fistLocation = $company->locations->first();

        $companyData = $company->data?->payload ?? [];

        $googleRating = $companyData[CalculateAndStoreGoogleRatingsJob::GOOGLE_RATING] ?? 0;
        $googleReviewCount = $companyData[CalculateAndStoreGoogleRatingsJob::GOOGLE_REVIEW_COUNT] ?? 0;

        $leadRejection = $this->computedRejectionStatisticRepository->getCompanyLeadRejection($company);
        $directLeadsRejection = $this->computedRejectionStatisticRepository->getCompanyDirectLeadsRejection($company);

        return [
            'id' => $company->id,
            'legacy_id' => $company->legacy_id,
            'name' => $company->name,
            'website' => $company->website,
            'website_verified_url' => $company->website_verified_url,
            'sourced_from' => $company->sourced_from,
            'phone' => $fistLocation?->phone,
            'phone_location_id' => $fistLocation?->id,
            'image' => $company->link_to_logo,
            'type' => $company->servicesNames()?->join(', '),
            'active' => $company->consolidated_status === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
            'status' => CompanyConsolidatedStatus::label($company->consolidated_status),
            'can_receive_leads' => $company->consolidated_status === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
            'sales_status' => CompanySalesStatus::label($company->sales_status),
            'ranking' => 'Excellent', //todo
            'prescreened' => $company->prescreened(),
            'prescreened_at' => $company->{Company::FIELD_PRESCREENED_AT}?->timestamp ?? null,
            'lead_rejection_percentage' => number_format($this->computedRejectionStatisticRepository->getCompanyLeadRejectionPercentage($company), 1) . '%',
            'mainOfficeLocation' => $company->locations?->first()?->address?->full_address ?? '',
            'address' => [
                'location_id' => $company->locations?->first()?->id,
                'name'        => $company->locations?->first()?->name,
                'address_id'  => $company->locations?->first()?->address?->id,
                'address_1'   => $company->locations?->first()?->address?->address_1,
                'address_2'   => $company->locations?->first()?->address?->address_2,
                'city'        => $company->locations?->first()?->address?->city,
                'state'       => $company->locations?->first()?->address?->state,
                'zip_code'    => $company->locations?->first()?->address?->zip_code,
            ],
            'employees' => $this->companyRepository->getCompanyDataByKey($company, SolarConfigurableFields::EMPLOYEE_COUNT->value),
            'revenue' => $this->companyRepository->getCompanyDataByKey($company, SolarConfigurableFields::REVENUE_IN_THOUSANDS->value, 0) * 1000,
            'neverExceedBudget' => $this->transformNeverExceedBudget($company),
            'is_fixr' => $isFixr,
            'can_shadow' => (bool)$this->shadowRepository->getUserToShadow($company),
            'shadow_urls' => $this->shadowRepository->getAllShadowUrls($company),
            'can_edit_account_manager' => $this->userAuthorizationService->canEditAccountManager($company),
            'can_view_account_manager' => $this->userAuthorizationService->canViewAccountManager($company),
            'can_edit_success_manager' => $this->userAuthorizationService->canEditSuccessManager($company),
            'can_edit_business_development_manager' => $this->userAuthorizationService->canEditBusinessDevelopmentManager($company),
            'can_edit_onboarding_manager' => $this->userAuthorizationService->canEditOnboardingManager($company),
            'can_edit_sales_development_representative' => $this->userAuthorizationService->canEditSalesDevelopmentRepresentative($company),
            'account_manager' => [
                "id" => $accountManager?->id,
                "name" => $accountManager?->name,
                "active" => filled($accountManager),
                "user_id" => $accountManager?->id
            ],
            'last_account_manager' => [
                "id" => null,
                "name" => null,
                "updated_at" => null,
            ],
            'success_manager' => [
                "id" => $customerSuccessManager?->id,
                "name" => $customerSuccessManager?->name,
                "user_id" => $customerSuccessManager?->id
            ],
            'business_development_manager' => [
                "id" => $businessDevelopmentManager?->id,
                "name" => $businessDevelopmentManager?->name,
                "user_id" => $businessDevelopmentManager?->id
            ],
            'onboarding_manager' => [
                "id" => $company->onboardingManager?->id,
                "name" => $company->onboardingManager?->name,
                "user_id" => $company->onboardingManager?->id
            ],
            'sales_development_representative' => [
                "id" => $company->salesDevelopmentRepresentative?->id,
                "name" => $company->salesDevelopmentRepresentative?->name,
                "user_id" => $company->salesDevelopmentRepresentative?->id
            ],
            'pre_assigned_account_manager' => [
                'id' => $company->preassignedAccountManager?->id,
                'name' => $company->preassignedAccountManager?->name,
            ],
            'socialMediaLinks'      => $this->getSocialLinks($company)?->filter(fn($socialLink) => $socialLink->linkvalue )->unique(fn($value) => CompanySocialMediaLinkTypeEnum::tryFrom($value['linktype']) ?? $value['linkvalue']) ?? collect(),
            'industries'            => $this->industryTransformer->transform($company->industries),
            'consolidated_status_object' => $company->consolidated_status_object,
            'admin_approved' => $company->admin_approved,
            'admin_locked' => $company->admin_locked,
            'ruleset_scores' => CompanyQualityScoreResource::collection($latestQualityScores),
            'google_review_count' => $googleReviewCount,
            'google_rating' => $googleRating,
            'lead' => [
                'rejection_percentage' => [
                    'manual' => $leadRejection?->{ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE} ?? 0,
                    'crm' => $leadRejection?->{ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE} ?? 0,
                    'overall' => $leadRejection?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE} ?? 0,
                ]
            ],
            'direct_leads' => [
                'rejection_percentage' => [
                    'manual' => $directLeadsRejection?->{ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE} ?? 0,
                    'crm' => $directLeadsRejection?->{ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE} ?? 0,
                    'overall' => $directLeadsRejection?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE} ?? 0,
                ]
            ],
            'unrestricted_zip_code_targeting' => $configuration->unrestricted_zip_code_targeting,
            'basic_status' => $company->status,
            'admin_status' => $company->admin_status?->value,
            'admin_status_label' => $company->admin_status?->label(),
            'system_status' => $company->system_status->label(),
            'campaign_status' => $company->campaign_status->label(),
            GlobalConfigurableFields::QR_TOP_500_COMPANY->value => $company->data?->getDatumByGlobalConfigField(GlobalConfigurableFields::QR_TOP_500_COMPANY),
            'known_lead_buyer' => self::isKnownLeadBuyer($company),
            'timezone_offset' => TimeZoneHelperService::getCompanyTimezone($company)->value,
            'first_lead_date' => app(CompanyManagerAssignmentService::class)->getFirstLeadForCompany($company)?->delivered_at?->toIso8601String(),
            'registration_domain' => $company->registration_domain,
            'created_at' => $company->created_at?->format('F j, Y'),
        ];
    }

    /**
     * @param Company $company
     * @return bool
     */
    private static function isKnownLeadBuyer(Company $company): bool
    {
        return NewBuyerProspect::query()->where(NewBuyerProspect::FIELD_COMPANY_ID, $company->id)->where(NewBuyerProspect::FIELD_SOURCE, ProspectSource::ALIST)->exists();
    }

    protected function transformNeverExceedBudget(Company $company): bool|string
    {
        $default = 'Unknown';
        $value = $this->companyRepository->getCompanyDataByKey($company, SolarConfigurableFields::NEVER_EXCEED_BUDGET->value, $default);

        if ($value === $default) return $default;
        if ($value === 1 || $value === true) return true;

        return false;
    }

    /**
     * @param Company $company
     *
     * @return array
     */
    public function transformCompanyProfile(Company $company): array
    {
        $industries = $company->industries;

        return [
            'id'                   => $company->id,
            'legacy_id'            => $company->legacy_id,
            'name'                 => $company->name,
            'entity_name'          => $company->entity_name,
            'status'               => CompanyConsolidatedStatus::label($company->consolidated_status),
            'sales_status'         => CompanySalesStatus::label($company->sales_status),
            'industries'           => $industries,
            'services'             => $company->services->unique(IndustryService::FIELD_ID),
            'type'                 => $this->getMultiIndustryType($company),
            'companyFields'        => $this->dataTransformer->transform($company),
            'lastChanged'          => $company->updated_at?->timestamp,
            'lastQuote'            => $this->quoteRepository->getLastLead($company)?->created_at?->timestamp,
            'lastReview'           => $this->reviewRepository->getLastReview($company->id)?->created_at?->timestamp,
            'lastRevised'          => $this->actionRepository->getLastActionForCompany($company->id)?->created_at?->timestamp,
            'lastLogin'            => $this->getLastLogin($company),
            'logoFileSquare'       => $company->link_to_logo,
            'logoFileRectangle'    => $company->link_to_logo, //todo
            'mediaCount'           => $this->getMediaCount($company),
            'attachmentCount'      => $this->getAttachmentCount($company),
            'profilePageLink'      => null, //todo
            'website'              => $company->website,
            'dateRegistered'       => $company->created_at?->timestamp,
            'payingBy'             => 'Invoice', //todo
            'paymentSource'        => null, //todo
            'dateOfNextCharge'     => null, //todo
            'activePaymentGateway' => $this->getPaymentGateway($company),
            'licenses'             => $this->getCompanyLicenses($company) ?: collect(),
            'socialMediaLinks'     => $this->getSocialLinks($company)?->filter(fn($socialLink) => $socialLink->linkvalue )->unique(fn($link) => CompanySocialMediaLinkTypeEnum::tryFrom($link['linktype']) ?? $link['linkvalue']) ?? collect(),
            'prescreened'          => $company->prescreened(),
            'activeSlug'           => $company->{Company::RELATION_ACTIVE_COMPANY_SLUG}?->{CompanySlug::FIELD_SLUG},
            'sourced_from'         => $company->sourced_from,
        ];
    }

    /**
     * @param Company $company
     *
     * @return int|null
     */
    public function getLastLogin(Company $company): ?int
    {
        return $company->legacyCompany?->historyLog()
            ->where(EloquentHistoryLog::ACTIVITY, EloquentHistoryLog::ACTIVITY_LOGIN)
            ->latest(EloquentHistoryLog::TIMESTAMP_ADDED)
            ->first()?->timestampadded;
    }

    /**
     * @param Company $company
     *
     * @return int
     */
    public function getMediaCount(Company $company): int
    {
        return intval($company
            ->{Company::RELATION_MEDIA_ASSETS}
            ->count()
        );
    }

    /**
     * @param Company $company
     *
     * @return int
     */
    public function getAttachmentCount(Company $company): int
    {
        return intval($company->legacyCompany?->attachments()->count());
    }

    /**
     * @param Company $company
     *
     * @return string
     */
    public function getPaymentGateway(Company $company): string
    {
        /** @var EloquentCompanyPaymentProfile|null $paymentProfile */
        $paymentProfile = EloquentCompanyPaymentProfile::query()->where(EloquentCompanyPaymentProfile::COMPANY_ID, $company->legacy_id)->first();

        if (!$paymentProfile) return 'Not set';

        return match ($paymentProfile->provider_code) {
            EloquentCompanyPaymentProfile::SHORT_CODE_STRIPE => EloquentCompanyPaymentProfile::DISPLAY_NAME_STRIPE,
            EloquentCompanyPaymentProfile::SHORT_CODE_AUTHORIZE_NET => EloquentCompanyPaymentProfile::DISPLAY_NAME_AUTHORIZE_NET,
            default => 'Unknown'
        };
    }

    /**
     * @param Company $company
     *
     * @return Collection|null
     */
    public function getCompanyLicenses(Company $company): ?Collection
    {
        return $company->legacyCompany?->companyLicenses;
    }

    /**
     * @param Company $company
     *
     * @return Collection|null
     */
    public function getSocialLinks(Company $company): ?Collection
    {
        return $company->legacyCompany?->socialMediaLink->each(function ($link) {
            $link->append('url', 'svg');
        });
    }

    /**
     * @param  Company  $company
     * @return string|null
     */
    protected function getMultiIndustryType(Company $company): ?string
    {
        return $company->legacyCompany?->type;
    }
}
