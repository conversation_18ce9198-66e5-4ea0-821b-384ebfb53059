<?php

namespace App\Transformers\Odin;

use App\Models\Odin\ConsumerProduct;

class ConsumerProductTransformer
{
    public function __construct(protected ConsumerTransformer $consumerTransformer) {}

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return array
     */
    public function transform(ConsumerProduct $consumerProduct): array
    {
        return [
            'id'       => $consumerProduct->id,
            'consumer' => $this->consumerTransformer->transform($consumerProduct->consumer)
        ];
    }
}
