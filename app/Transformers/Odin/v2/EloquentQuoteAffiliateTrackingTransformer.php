<?php

namespace App\Transformers\Odin\v2;

use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadTrackingUrl;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;

class EloquentQuoteAffiliateTrackingTransformer
{
    /**
     * @param Consumer $consumer
     * @return array
     */
    public function transform(Consumer $consumer): array
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->first();
        $affiliate = $consumerProduct?->consumerProductAffiliateRecord;
        $tracking = $consumerProduct?->consumerProductTracking;

        $data = [];

        if ($tracking)
            $data['lead_tracking_url'] = [
                LeadTrackingUrl::LEAD_ID        => $consumer->legacy_id,
                LeadTrackingUrl::URL_START      => $tracking->url_start,
                LeadTrackingUrl::URL_CONVERT    => $tracking->url_convert,
                LeadTrackingUrl::SOURCE         => $tracking->calculator_source,
                LeadTrackingUrl::CREATED_AT     => $tracking->created_at->timestamp,
                LeadTrackingUrl::UPDATED_AT     => $tracking->updated_at->timestamp,
            ];

        if ($affiliate)
            $data['eloquent_quote'] = [
                EloquentQuote::ID           => $consumer->legacy_id,
                EloquentQuote::AFFILIATE_ID => $affiliate->affiliate_id,
                EloquentQuote::CAMPAIGN_ID  => $affiliate->campaign_id,
                EloquentQuote::TRACK_NAME   => $affiliate->track_name,
                EloquentQuote::TRACK_CODE   => $affiliate->track_code,
            ];

        return $data;
    }


}
