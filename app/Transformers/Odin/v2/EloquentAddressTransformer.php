<?php

namespace App\Transformers\Odin\v2;

use App\Models\Legacy\EloquentAddress;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;

class EloquentAddressTransformer
{
    /**
     * @return array[]
     */
    public function transform(Address $address, Consumer $consumer): array
    {
        return [
            EloquentAddress::ID         => $address->legacy_id,
            EloquentAddress::ADDRESS1   => $address->address_1,
            EloquentAddress::ADDRESS2   => $address->address_2,
            EloquentAddress::CITY       => $address->city,
            EloquentAddress::STATE_ABBR => $address->state,
            EloquentAddress::ZIP_CODE   => $address->zip_code,
            EloquentAddress::COUNTRY    => $address->country,
            EloquentAddress::LONGITUDE  => $address->longitude,
            EloquentAddress::LATITUDE   => $address->latitude,
            EloquentAddress::PHONE      => $consumer->phone ?? "",
        ];
    }
}
