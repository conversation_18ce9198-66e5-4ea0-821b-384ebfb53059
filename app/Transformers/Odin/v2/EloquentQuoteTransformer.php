<?php

namespace App\Transformers\Odin\v2;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Industry;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCategory;
use App\Models\Legacy\LeadRoofDetail;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\PropertyType;
use Illuminate\Contracts\Container\BindingResolutionException;

class EloquentQuoteTransformer
{
    const PAYLOAD_APPOINTMENTS = 'appointments';
    /**
     * @param Consumer $consumer
     * @param bool $withAddress
     * @return array
     * @throws BindingResolutionException
     */
    public function transform(Consumer $consumer, bool $withAddress = false): array
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $consumer->consumerProducts->first();
        $address = $consumerProduct->address;
        $data = $consumerProduct->consumerProductData->payload;
        $appointments = $consumerProduct->leadAppointments()->get()?->toArray();
        $tcpaTracking = $consumerProduct->consumerProductTcpaRecord;

        $isSolarOrRoofing = $this->isSolarOrRoofingLead($consumerProduct);

        $baseQuote = [
            EloquentQuote::QUOTE_ID                            => $consumer->legacy_id,
            EloquentQuote::REFERENCE                           => $consumer->reference,
            EloquentQuote::ADDRESS_ID                          => $address->legacy_id ?? null,
            EloquentQuote::USER_EMAIL                          => $consumer->email ?? "",
            EloquentQuote::FIRST_NAME                          => $consumer->first_name ?? "",
            EloquentQuote::LAST_NAME                           => $consumer->last_name ?? "",
            EloquentQuote::STATUS                              => $this->getLegacyStatus($consumerProduct),
            EloquentQuote::STATUS_REASON                       => $consumer->status_reason ?? "",
            EloquentQuote::CLASSIFICATION                      => $consumer->classification ? $this->getLegacyClassification($consumer->classification) : "",
            EloquentQuote::NUMBER_OF_QUOTES                    => $consumerProduct->contact_requests ?? 0,
            EloquentQuote::TIMESTAMP_ADDED                     => $consumer->created_at->getTimestamp(),
            EloquentQuote::BEST_TIME_TO_CALL                   => $data[GlobalConfigurableFields::BEST_TIME_TO_CALL->value] ?? "",
            EloquentQuote::BEST_TIME_TO_CALL_OTHER             => $data[GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value] ?? "",
            EloquentQuote::EMAIL_IS_VALIDATED_FLAG             => $consumer->classification === 1,
            EloquentQuote::PHONE_IS_VALIDATED_FLAG             => $consumer->classification >= 3,
            EloquentQuote::PHONE_VALIDATION_METHOD             => $this->getPhoneValidation($consumer->classification),
            EloquentQuote::SOLAR_LEAD                          => $isSolarOrRoofing === 'solar',
            EloquentQuote::ROOFING_LEAD                        => $isSolarOrRoofing === 'roofing',
            EloquentQuote::IP_ADDRESS                          => $data[GlobalConfigurableFields::IP_ADDRESS->value] ?? "",
            EloquentQuote::LEAD_CATEGORY_ID                    => $this->getLeadCategoryType($consumerProduct->property_type_id),
            EloquentQuote::MULTI_INDUSTRY_LEAD                 => true,
            EloquentQuote::UTC                                 => $consumerProduct->address->utc ?? '',
            EloquentQuote::OWN_PROPERTY                        => strtolower($data[GlobalConfigurableFields::OWN_PROPERTY->value] ?? ""),
        ];

        if($tcpaTracking !== null)
            $baseQuote["tcpa_id"] = $tcpaTracking->tcpa_id;

        if ($isSolarOrRoofing) {
            $baseQuote = [
                ...$baseQuote,
                EloquentQuote::ROOF_TYPE                           => $data[GlobalConfigurableFields::ROOF_TYPE->value] ?? "",
                EloquentQuote::ROOF_TYPE_OTHER                     => $data[GlobalConfigurableFields::ROOF_TYPE_OTHER->value] ?? "",
                EloquentQuote::SYSTEM_SIZE                         => $data[SolarConfigurableFields::SYSTEM_SIZE->value] ?? "",
                EloquentQuote::SYSTEM_SIZE_OTHER                   => $data[SolarConfigurableFields::SYSTEM_SIZE_OTHER->value] ?? "",
                EloquentQuote::STOREYS                             => $data[GlobalConfigurableFields::STOREYS->value] ?? "",
                EloquentQuote::PROPERTY_TYPE                       => $data[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? "",
                EloquentQuote::PROJECT_TYPE                        => $data[GlobalConfigurableFields::PROJECT_TYPE->value] ?? "",
                EloquentQuote::ELECTRIC_COST                       => $data[SolarConfigurableFields::ELECTRIC_COST->value] ?? "",
                EloquentQuote::ROOF_PITCH                          => $data[GlobalConfigurableFields::ROOF_PITCH->value] ?? "",
                EloquentQuote::ROOF_SHADING                        => $data[GlobalConfigurableFields::ROOF_SHADING->value] ?? "",
                EloquentQuote::UTILITY_ID                          => $data[SolarConfigurableFields::UTILITY_ID->value] ?? "",
                EloquentQuote::UTILITY_NAME                        => $data[SolarConfigurableFields::UTILITY_NAME->value] ?? "",
                EloquentQuote::PANEL_TIER                          => $data[SolarConfigurableFields::PANEL_TIER->value] ?? "",
                EloquentQuote::PAYMENT_TYPE                        => $data[GlobalConfigurableFields::PAYMENT_TYPE->value] ?? "",
                EloquentQuote::IP_ADDRESS                          => $data[GlobalConfigurableFields::IP_ADDRESS->value] ?? "",
                EloquentQuote::ORIGIN                              => $data[GlobalConfigurableFields::ORIGIN->value] ?? "",
                EloquentQuote::ORIGIN_KEY                          => $data[GlobalConfigurableFields::ORIGIN->value] ?? "",
                EloquentQuote::TRACK_LEAD_ID                       => $data[GlobalConfigurableFields::TRACK_LEAD_ID->value] ?? "",
                EloquentQuote::CONTACT_METHOD                      => $data[GlobalConfigurableFields::CONTACT_METHOD->value] ?? "",
                EloquentQuote::TCPA_DISCLOSURE_CAPTURED            => $data[GlobalConfigurableFields::TCPA_DISCLOSURE_CAPTURED->value] ?? "",
                EloquentQuote::AUTO_ASSIGNED_STATUS                => $data[GlobalConfigurableFields::AUTO_ASSIGNED_STATUS->value] ?? "",
                EloquentQuote::ROOF_DIRECTION                      => $data[GlobalConfigurableFields::ROOF_DIRECTION->value] ?? "",
                EloquentQuote::COMMENTS                            => $data[GlobalConfigurableFields::COMMENTS->value] ?? "",
                EloquentQuote::ROOF_CONDITION                      => $data[GlobalConfigurableFields::ROOF_CONDITION->value] ?? "",
            ];
        }

        $transformPayload = [
            'eloquent_quote'            => $baseQuote,
            self::PAYLOAD_APPOINTMENTS  => $appointments ?? [],
        ];

        if ($withAddress) {
            $transformer = app()->make(EloquentAddressTransformer::class);
            $addressTransform =  $transformer->transform($address, $consumer);

            $transformPayload['eloquent_address'] = $addressTransform;
        }

        if ($consumerProduct->industryService->industry->name === Industry::ROOFING->value) {
            $transformPayload['roof_details'] = [
                LeadRoofDetail::FIELD_PREDOMINANT_SLOPE => $data[LeadRoofDetail::FIELD_PREDOMINANT_SLOPE] ?? null,
                LeadRoofDetail::FIELD_COMPLEXITY => $data[GlobalConfigurableFields::ROOF_COMPLEXITY->value] ?? 0,
                LeadRoofDetail::FIELD_SKYLIGHTS => $data[GlobalConfigurableFields::ROOF_SKYLIGHTS->value] ?? 0,
                LeadRoofDetail::FIELD_VENTS => $data[GlobalConfigurableFields::ROOF_VENTS->value] ?? 0,
                LeadRoofDetail::FIELD_DORMERS => $data[GlobalConfigurableFields::ROOF_DORMERS->value] ?? 0,
                LeadRoofDetail::FIELD_CHIMNEYS => $data[GlobalConfigurableFields::ROOF_CHIMNEYS->value] ?? 0,
                LeadRoofDetail::FIELD_HVAC_UNITS => $data[GlobalConfigurableFields::ROOF_HVAC_UNITS->value] ?? 0,
                LeadRoofDetail::FIELD_OTHER_OBSTRUCTIONS => $data[GlobalConfigurableFields::ROOF_OTHER_OBSTRUCTIONS->value] ?? 0,
                LeadRoofDetail::FIELD_REQUESTED_NEW_GUTTERS => $data[LeadRoofDetail::FIELD_REQUESTED_NEW_GUTTERS] ?? 0,
                LeadRoofDetail::FIELD_REQUESTED_ROOF_TEAR_OFF => $data[LeadRoofDetail::FIELD_REQUESTED_ROOF_TEAR_OFF] ?? 0,
                LeadRoofDetail::FIELD_REQUESTED_ROOF_DISPOSAL => $data[LeadRoofDetail::FIELD_REQUESTED_ROOF_DISPOSAL] ?? 0,
                LeadRoofDetail::FIELD_ROOF_ESTIMATE_LOW => $data[LeadRoofDetail::FIELD_ROOF_ESTIMATE_LOW] ?? null,
                LeadRoofDetail::FIELD_ROOF_ESTIMATE_MEDIAN => $data[LeadRoofDetail::FIELD_ROOF_ESTIMATE_MEDIAN] ?? null,
                LeadRoofDetail::FIELD_ROOF_ESTIMATE_HIGH => $data[LeadRoofDetail::FIELD_ROOF_ESTIMATE_HIGH] ?? null,
                LeadRoofDetail::FIELD_ROOF_REPLACEMENT_AREA => $data[LeadRoofDetail::FIELD_ROOF_REPLACEMENT_AREA] ?? null,
                LeadRoofDetail::FIELD_ROOF_SUB_TYPE => $data[LeadRoofDetail::FIELD_ROOF_SUB_TYPE] ?? null,
            ];
        }

        return $transformPayload;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return string
     */
    private function getLegacyStatus(ConsumerProduct $consumerProduct): string
    {
        return match ($consumerProduct->status) {
            ConsumerProduct::STATUS_CANCELLED                                             => EloquentQuote::VALUE_STATUS_CANCELLED,
            ConsumerProduct::STATUS_INITIAL, ConsumerProduct::STATUS_PENDING_REVIEW       => EloquentQuote::VALUE_STATUS_INITIAL,
            ConsumerProduct::STATUS_UNDER_REVIEW, ConsumerProduct::STATUS_UNSOLD          => EloquentQuote::VALUE_STATUS_UNDER_REVIEW,
            ConsumerProduct::STATUS_ALLOCATED, ConsumerProduct::STATUS_PENDING_ALLOCATION => EloquentQuote::VALUE_STATUS_ALLOCATED,
        };
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return ?string
     */
    private function isSolarOrRoofingLead(ConsumerProduct $consumerProduct): ?string
    {
        $industry = $consumerProduct->industryService->industry->name;
        return match($industry) {
            'Solar'   => 'solar',
            'Roofing' => 'roofing',
            default   => null,
        };
    }

    /**
     * @param ?int $classification
     * @return string
     */
    private function getPhoneValidation(?int $classification): string
    {
        return match($classification) {
            3   => "sms",
            4   => "call",
            default => "",
        };
    }

    /**
     * @param int|null $propertyTypeId
     * @return int
     */
    private function getLeadCategoryType(?int $propertyTypeId): int
    {
        /** @var string $odinPropertyTypeName */
        $odinPropertyTypeName = PropertyType::query()->find($propertyTypeId)?->name;

        return $odinPropertyTypeName === "Commercial"
            ? LeadCategory::COMMERCIAL
            : LeadCategory::RESIDENTIAL;
    }

    /**
     * @param int $classification
     * @return string
     */
    private function getLegacyClassification(int $classification): string
    {
        if(in_array($classification, Consumer::VERIFIED_CLASSIFICATIONS)){
            return EloquentQuote::CLASSIFICATION_VERIFIED;
        }else{
            return EloquentQuote::CLASSIFICATION_UNVERIFIED;
        }
    }

}
