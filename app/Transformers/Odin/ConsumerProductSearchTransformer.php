<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ServiceProduct;
use Illuminate\Support\Collection;

class ConsumerProductSearchTransformer
{
    /**
     * @param Collection $consumerProducts
     * @return array
     */
    public function transform(Collection $consumerProducts): array
    {
        $consumerProducts->load([
            ConsumerProduct::RELATION_SERVICE_PRODUCT.'.'.ServiceProduct::RELATION_PRODUCT,
            ConsumerProduct::RELATION_CONSUMER
        ]);

        return $consumerProducts->map(function($cp) {
            $consumerProductId = $cp->{ConsumerProduct::FIELD_ID};

            $product = $cp->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{Product::FIELD_NAME};
            $consumer = $cp->{ConsumerProduct::RELATION_CONSUMER};

            $name = $consumer->getFullName();
            $email = $consumer->{Consumer::FIELD_EMAIL};
            $leadId = $consumer->{Consumer::FIELD_LEGACY_ID};

            $apptTime = $cp->{ConsumerProduct::RELATION_APPOINTMENT}?->{ProductAppointment::APPOINTMENT};
            $apptType = $cp->{ConsumerProduct::RELATION_APPOINTMENT}?->{ProductAppointment::APPOINTMENT_TYPE}->value;

            return [
                "id" => $cp->{ConsumerProduct::FIELD_ID},
                "name" => "$consumerProductId (Quote ID: $leadId) $product: $name / $email".($apptTime ? " / $apptType: $apptTime" : "")
            ];
        })->toArray();
    }
}
