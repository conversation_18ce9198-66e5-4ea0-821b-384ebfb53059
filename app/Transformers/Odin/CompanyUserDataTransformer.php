<?php

namespace App\Transformers\Odin;

use App\DataModels\Odin\CompanyUserFieldDataModel;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\CompanyUserField;
use App\Models\Odin\ConfigurableFieldType;
use App\Repositories\Odin\CompanyUserRepository;
use Illuminate\Support\Collection;

class CompanyUserDataTransformer
{
    const DEFAULT_VALUE = '-';

    public function __construct(protected CompanyUserRepository $companyUserRepository) {}

    /**
     * Handles transforming the configurable fields for a given company user.
     *
     * @param CompanyUser $user
     * @return Collection
     */
    public function transform(CompanyUser $user): Collection
    {
        $userFields = CompanyUserField::query()->get()->map(fn(CompanyUserField $field) => new CompanyUserFieldDataModel(
            $field->name,
            $field->key,
            $field->fieldType->type,
            $field->hidden,
            $this->getCompanyUserData($user, $field->key, $field->fieldType->type)
        ));

        return $userFields->groupBy(['scope'], true);
    }

    /**
     * Finds the configurable field in the data payload for a given company user.
     *
     * @param CompanyUser $user
     * @param string $key
     * @param string $type
     * @return mixed
     */
    protected function getCompanyUserData(CompanyUser $user, string $key, string $type): mixed
    {
        $data = $this->companyUserRepository->getCompanyUserDataByKey($user, $key, self::DEFAULT_VALUE);

        if($type === ConfigurableFieldType::TYPE_BOOLEAN) {
            if(!$data || $data === self::DEFAULT_VALUE) return false;
            return true;
        }

        return $data;
    }
}
