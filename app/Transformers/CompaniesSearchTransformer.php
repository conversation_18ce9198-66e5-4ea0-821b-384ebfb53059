<?php

namespace App\Transformers;

use App\Repositories\Legacy\CompanyRepository;
use Illuminate\Support\Collection;

class CompaniesSearchTransformer
{
    public function determineCampaignStatusName(string $key): string
    {
        return match($key) {
            CompanyRepository::CAMPAIGN_STATUS_ACTIVE => "Active",
            CompanyRepository::CAMPAIGN_STATUS_ONE_PAUSED => "One Paused",
            CompanyRepository::CAMPAIGN_STATUS_PAUSED => "Paused",
            CompanyRepository::CAMPAIGN_STATUS_OVER_BUDGET => "Over Budget",
            CompanyRepository::CAMPAIGN_STATUS_ONE_OVER_BUDGET => "One Over Budget"
        };
    }
}
