<?php

namespace App\Transformers\Prospects;

use App\Builders\Odin\CompanyCommunicationsBuilder;
use App\Enums\Prospects\ProspectResolution;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\Sales\Task;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class ProspectedCompanyTransformer
{

    /**
     * @param Collection<Company> $companies
     * @param User $user
     * @return array
     */
    public static function transformMany(Collection $companies, User $user): array
    {
        return $companies->map(fn(Company $company) => self::transform($company, $user))->toArray();
    }

    /**
     * @param Company $company
     * @param User $user
     * @return array
     */
    public static function transform(Company $company, User $user): array
    {
        $communications = app(CompanyCommunicationsBuilder::class);

        // todo: N+1 - major performance drain
        $lastContact = $communications->forUser($user)->forCompany($company)->first();

        /**
         * @var Task|null $mostRecentTask
         */
        $mostRecentTask = $company->tasks->sortByDesc('created_at')->first();

        $lastLeadDate = ProductAssignment::sold()->whereCompanyId($company->id)->latest()->first()->delivered_at ?? null;

        return [
            Company::FIELD_ID       => $company->id,
            Company::FIELD_NAME     => $company->name,
            'industries'            => $company->industries,
            'state'                 => $company->locations?->first()?->address?->state ?? '',
            'decision_maker'        => $company->users?->first()?->first_name . ' ' . $company->users?->first()?->last_name ?? '',
            'website'               => $company->website ?? '',
            'profile_link'          => $company->getAdminProfileUrl(),
            'sales_status'          => $company->sales_status,
            'source'                => self::determineCompanySource($company),
            'last_contact_date'     => $lastContact?->contact_date,
            'last_contact_type'     => str($lastContact?->contact_type)->headline(),
            'most_recent_task_date' => $mostRecentTask?->created_at,
            'most_recent_task_id'   => $mostRecentTask?->id,
            'days_since_last_lead'  => is_null($lastLeadDate) ? null : Carbon::parse($lastLeadDate)->diffInDays(),
        ];
    }

    /**
     * @param Company $company
     * @return string
     */
    public static function determineCompanySource(Company $company): string
    {
        if (!$company->latestProspect || $company->latestProspect->resolution === ProspectResolution::DUPLICATE_COMPANY) {
            return 'existing';
        }
        return 'prospecting';
    }
}
