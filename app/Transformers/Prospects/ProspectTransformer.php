<?php

namespace App\Transformers\Prospects;

use App\Models\Odin\Company;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectDuplicateMatchingService;
use App\Services\Prospects\ProspectIndustriesService;
use Illuminate\Support\Collection;

class ProspectTransformer
{
    /**
     * @param NewBuyerProspect|null $prospect
     * @param bool $checkForDupeCompanies
     * @return array|null
     */
    public static function transform(?NewBuyerProspect $prospect, bool $checkForDupeCompanies = true): ?array
    {
        return $prospect ? [
            NewBuyerProspect::FIELD_REFERENCE                 => $prospect->external_reference,
            NewBuyerProspect::FIELD_USER_ID                   => $prospect->user_id,
            NewBuyerProspect::FIELD_COMPANY_ID                => $prospect->company_id,
            NewBuyerProspect::FIELD_STATUS                    => $prospect->status,
            NewBuyerProspect::FIELD_RESOLUTION                => $prospect->resolution,
            'additional_data'                                 => $prospect->source_data,
            NewBuyerProspect::FIELD_COMPANY_NAME              => $prospect->company_name,
            NewBuyerProspect::FIELD_COMPANY_WEBSITE           => $prospect->company_website,
            NewBuyerProspect::FIELD_COMPANY_DESCRIPTION       => $prospect->company_description,
            NewBuyerProspect::FIELD_COMPANY_PHONE             => $prospect->company_phone,
            NewBuyerProspect::FIELD_ADDRESS_STREET            => $prospect->address_street,
            NewBuyerProspect::FIELD_ADDRESS_CITY_KEY          => $prospect->address_city_key,
            NewBuyerProspect::FIELD_ADDRESS_STATE_ABBR        => $prospect->address_state_abbr,
            NewBuyerProspect::FIELD_DECISION_MAKER_FIRST_NAME => $prospect->decision_maker_first_name,
            NewBuyerProspect::FIELD_DECISION_MAKER_LAST_NAME  => $prospect->decision_maker_last_name,
            NewBuyerProspect::FIELD_DECISION_MAKER_EMAIL      => $prospect->decision_maker_email,
            NewBuyerProspect::FIELD_DECISION_MAKER_PHONE      => $prospect->decision_maker_phone,
            NewBuyerProspect::FIELD_NOTES                     => $prospect->notes,
            'industry_ids'                                    => ProspectIndustriesService::getIndustryIds($prospect),
            'duplicate_companies'                             => $checkForDupeCompanies ? self::getPotentialDuplicates($prospect) : null,
            'source'                                          => $prospect->source?->value,
            'contacts'                                        => $prospect->contacts,
        ] : null;
    }

    /**
     * @param Collection $prospects
     * @return array
     */
    public static function transformMany(Collection $prospects): array
    {
        return $prospects->map(function ($prospect) {
            return self::transform($prospect, false);
        })->toArray();
    }

    /**
     * @param NewBuyerProspect $prospect
     * @return array
     */
    private static function getPotentialDuplicates(NewBuyerProspect $prospect): array
    {
        return ProspectDuplicateMatchingService::getAllPotentialMatches($prospect)->map(function (Company $company) {
            return [
                'company_id'   => $company->id,
                'company_name' => $company->name,
                'website'      => $company->website,
                'profile_link' => $company->getAdminProfileUrl(),
            ];
        })->toArray();
    }
}
