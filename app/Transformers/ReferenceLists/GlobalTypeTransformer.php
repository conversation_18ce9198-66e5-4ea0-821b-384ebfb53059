<?php

namespace App\Transformers\ReferenceLists;

use App\Models\Odin\GlobalType;
use Illuminate\Support\Collection;

class GlobalTypeTransformer
{
    /**
     * @param Collection $globalTypes
     * @return array
     */
    public function transformGlobalTypes(Collection $globalTypes): array
    {
        return $globalTypes->map(function($globalType) {
            return $this->transformGlobalType($globalType);
        })->values()->toArray();
    }

    /**
     * @param GlobalType $globalType
     * @return array
     */
    public function transformGlobalType(GlobalType $globalType): array
    {
        return [
            'id' => $globalType->{GlobalType::FIELD_KEY},
            'name' => $globalType->{GlobalType::FIELD_NAME},
            'num_id' => $globalType->{GlobalType::FIELD_ID}
        ];
    }
}
