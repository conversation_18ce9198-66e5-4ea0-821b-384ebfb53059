<?php

namespace App\Transformers\ReferenceLists;

use App\Models\Legacy\Location;
use Illuminate\Support\Collection;


class StateTransformer
{
    /**
     * Handles transforming a collection of states to their respective client side representation
     *
     * @param Collection $states
     * @return array
     */
    public function transformStates(Collection $states): array
    {
        return $states->map(function ($states) {
            return $this->transformState($states);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given state to its respective client side representation.
     *
     * @param $state
     * @return array
     */
    public function transformState($state): array
    {
        return [
            "id"   => $state->{Location::STATE_KEY},
            "name" => $state->{Location::STATE},
            'stateAbbr' => $state->{Location::STATE_ABBREVIATION},
            "num_id" => $state->{Location::ID}
        ];
    }
}
