<?php

namespace App\Transformers\ReferenceLists;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use Illuminate\Support\Collection;

class IndustryServiceTransformer
{
    /**
     * @param Collection $industryServices
     * @return array
     */
    public function transformIndustryServices(Collection $industryServices): array
    {
        return $industryServices->map(function($industryService) {
            return $this->transformIndustryService($industryService);
        })->values()->toArray();
    }

    /**
     * @param IndustryService $industryService
     * @return array
     */
    public function transformIndustryService(IndustryService $industryService): array
    {
        return [
            'id' => $industryService->{IndustryService::FIELD_SLUG},
            'industry' => $industryService->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME},
            'name' => $industryService->{IndustryService::FIELD_NAME},
            'slug' => $industryService->{IndustryService::FIELD_SLUG},
            'num_id' => $industryService->{IndustryService::FIELD_ID}
        ];
    }
}
