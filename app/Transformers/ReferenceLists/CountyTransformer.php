<?php

namespace App\Transformers\ReferenceLists;

use App\Models\Legacy\Location;
use Illuminate\Support\Collection;

class CountyTransformer
{
    /**
     * <PERSON>les transforming a collection of counties to their respective client side representation
     *
     * @param Collection $counties
     * @return array
     */
    public function transformCounties(Collection $counties, bool $includeId = false): array
    {
        return $counties->map(function ($county) use ($includeId) {
            return $includeId ? $this->transformCountyWithId($county) : $this->transformCounty($county);
        })->values()->toArray();
    }

    /**
     * <PERSON>les transforming a given county to its respective client side representation.
     *
     * @param $county
     * @return array
     */
    public function transformCounty($county): array
    {
        return [
            "id"   => $county->{Location::COUNTY_KEY},
            "name" => $county->{Location::COUNTY}
        ];
    }

    /**
     * <PERSON>les transforming a given county with ID and KEY fields set to the respective model attributes.
     *
     * @param $county
     * @return array
     */
    public function transformCountyWithId($county): array
    {
        return [
            "id"   => $county->{Location::ID},
            "name" => $county->{Location::COUNTY},
            "key" => $county->{Location::COUNTY_KEY},
            "state_abbr" => $county->{Location::STATE_ABBREVIATION},
        ];
    }

    /**
     * Sorts counties by the name key.
     *
     * @param array $counties
     * @return array
     */
    public function sortTransformedCounties(array $counties): array
    {
        usort($counties, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        return $counties;
    }
}
