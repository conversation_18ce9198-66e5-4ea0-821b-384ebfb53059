<?php

namespace App\Transformers\LeadProcessing;

use App\Models\LeadProcessingTimeZoneConfiguration;
use Illuminate\Support\Collection;

class TimeZoneConfigurationTransformer
{
    /**
     * Handles transforming lead processing timezones into their respective client side representations.
     *
     * @param Collection $timezones
     * @return array
     */
    public function transformTimeZones(Collection $timezones): array
    {
        return $timezones->map(function ($timezone) {
            return $this->transformTimeZone($timezone);
        })->values()->toArray();
    }

    /**
     * Handles transforming a lead processing timezone into its respective client side representation
     *
     * @param LeadProcessingTimeZoneConfiguration $timezone
     * @return array
     */
    public function transformTimeZone(LeadProcessingTimeZoneConfiguration $timezone): array
    {
        return [
            LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => $timezone->{LeadProcessingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET},
            LeadProcessingTimeZoneConfiguration::FIELD_NAME                => $timezone->{LeadProcessingTimeZoneConfiguration::FIELD_NAME},
            LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR     => $timezone->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR},
            LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR    => $timezone->{LeadProcessingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR}
        ];
    }
}
