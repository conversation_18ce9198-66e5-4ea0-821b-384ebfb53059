<?php

namespace App\Transformers\LeadProcessing;

use App\Models\Phone;
use App\Models\LeadProcessor;
use Illuminate\Support\Collection;

class LeadProcessorTransformer
{
    /**
     * Handles transforming a list of processors into their respective client side representations.
     *
     * @param Collection $processors
     * @return array
     */
    public function transformProcessors(Collection $processors): array
    {
        return $processors->map(function ($processor) {
            return $this->transformProcessor($processor);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given processor into its respective client side representation
     *
     * @param LeadProcessor $processor
     * @return array
     */
    public function transformProcessor(LeadProcessor $processor): array
    {
        return [
            "id" => $processor->id,
            "name" => $processor->user->name,
            "user_id" => $processor->user_id,
            "team" => $processor->team ? $processor->team->name : "No Team",
            "team_id" => $processor->lead_processing_team_id
        ];
    }

}
