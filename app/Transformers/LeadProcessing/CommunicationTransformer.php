<?php

namespace App\Transformers\LeadProcessing;

use App\Models\LeadProcessingCommunication;
use App\Models\Text;
use App\Models\Legacy\EloquentQuote;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class CommunicationTransformer
{
    /**
     * Handles transforming a lead to the expected client side values.
     *
     * @param array|null $data
     * @return array
     */
    public function transformLeadDetails(?array $data): array
    {
        return $data ? $this->transformLead($data["lead"], $data["communication"]) : $this->transformUnknown();
    }

    /**
     * Transforms a lead to the client side expected values.
     *
     * @param EloquentQuote $lead
     * @param LeadProcessingCommunication $communication
     * @return array
     */
    protected function transformLead(EloquentQuote $lead, LeadProcessingCommunication $communication): array
    {
        return [
            "name" => $lead->firstname . " " . $lead->lastname,
            "lead_id" => $lead->quoteid,
            "communication_id" => $communication->id
        ];
    }

    /**
     * Transforms an unknown lead.
     *
     * @return array
     */
    protected function transformUnknown(): array
    {
        return [
            "name" => "Unknown",
            "lead_id" => "Unknown",
            "communication_id" => null
        ];
    }

    /**
     * @param Text[]|Collection $leadProcessingCommunications
     * @return array
     */
    public function transformSMSHistory(Collection $texts): array
    {
        $data = [];

        /** @var Text $communication */
        foreach ($texts as $communication) {
            $data[] = $this->transformText($communication);
        }

        return $data;
    }

    public function transformText(Text $text): array
    {
        if($text->direction == Text::DIRECTION_INBOUND) {
            // TODO: Upgrade this to get leads first name.
            return [
                'direction' => Text::DIRECTION_INBOUND,
                'first_name' => "Lead",
                'last_name' => "",
                'timestamp' => $text->created_at->timestamp,
                'message_body' => $text?->message_body,
                'text_media_assets' => $text?->textMediaAssets ?? []
            ];
        } else {
            $elements = explode(" ", $text->phone->users->first()?->name ?? "");
            array_shift($elements);

            return [
                'direction' => Text::DIRECTION_OUTBOUND,
                'first_name' => explode(" ", $text->phone->users->first()?->name ?? "")[0],
                'last_name' => implode(" ", $elements),
                'timestamp' => $text->created_at->timestamp,
                'message_body' => $text?->message_body,
                'text_media_assets' => $text?->textMediaAssets ?? []
            ];
        }
    }

    /**
     * Handles transforming a single lead processing communication.
     *
     * @param LeadProcessingCommunication $communication
     * @return array
     */
    public function transformLeadProcessingCommunication(LeadProcessingCommunication $communication): array
    {
        if ($communication->text->direction === Text::DIRECTION_INBOUND) {
            return [
                'direction' => Text::DIRECTION_INBOUND,
                'first_name' => $communication->lead->firstname,
                'last_name' => $communication->lead->lastname,
                'timestamp' => $communication->created_at->timestamp,
                'message_body' => $communication->text?->message_body
            ];
        } else {
            $elements = explode(" ", $communication->leadProcessor->user?->name ?? "");
            array_shift($elements);
            return [
                'direction' => Text::DIRECTION_OUTBOUND,
                'first_name' => explode(" ", $communication->leadProcessor->user?->name ?? "")[0],
                'last_name' => implode(" ", $elements),
                'timestamp' => $communication->created_at->timestamp,
                'message_body' => $communication->text?->message_body
            ];
        }
    }
}
