<?php

namespace App\Transformers\LeadProcessing;

use App\Models\LeadProcessingCallingTimeZoneConfiguration;
use Illuminate\Support\Collection;

class CallingTimeZoneConfigurationTransformer
{
    /**
     * Handles transforming lead processing timezones into their respective client side representations.
     *
     * @param Collection $timezones
     * @return array
     */
    public function transformTimeZones(Collection $timezones): array
    {
        return $timezones->map(function ($timezone) {
            return $this->transformTimeZone($timezone);
        })->values()->toArray();
    }

    /**
     * Handles transforming a lead processing timezone into its respective client side representation
     *
     * @param LeadProcessingCallingTimeZoneConfiguration $timezone
     * @return array
     */
    public function transformTimeZone(LeadProcessingCallingTimeZoneConfiguration $timezone): array
    {
        return [
            LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET => $timezone->{LeadProcessingCallingTimeZoneConfiguration::FIELD_STANDARD_UTC_OFFSET},
            LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME                => $timezone->{LeadProcessingCallingTimeZoneConfiguration::FIELD_NAME},
            LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR     => $timezone->{LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_OPEN_HOUR},
            LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR    => $timezone->{LeadProcessingCallingTimeZoneConfiguration::FIELD_LOCAL_CLOSE_HOUR}
        ];
    }
}
