<?php

namespace App\Transformers\LeadProcessing;

use App\Models\User;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;

class UserTransformer
{
    /**
     * Transforms collection of user objects into client side format
     *
     * @param Collection $users
     * @return array
     */
    public function transformUsers(Collection $users): array
    {
        return $users->map(function ($user) {
            return $this->transformUser($user);
        })->values()->toArray();
    }

    /**
     * Transforms user object into expected client side format
     *
     * @param User $user
     * @return array
     */
    #[ArrayShape(["id" => "integer", "name" => "string"])]
    public function transformUser(User $user): array
    {
        return [
            "id" => $user->{User::FIELD_ID},
            "name" => $user->{User::FIELD_NAME}
        ];
    }
}
