<?php

namespace App\Transformers\LeadProcessing;

use App\Enums\VerificationService;
use App\Models\Legacy\EloquentComment;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentReview;
use App\Models\Legacy\EloquentVarStore;
use App\Repositories\Legacy\QuoteRepository;
use App\Repositories\Legacy\ReviewRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Services\LeadVerification\LeadVerificationResult;
use App\Transformers\Legacy\LegacyCommentsTransformer;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;

class LeadProcessingTransformer
{
    /** @var QuoteRepository $quoteRepository */
    protected QuoteRepository $quoteRepository;

    /** @var ReviewRepository $reviewRepository */
    protected ReviewRepository $reviewRepository;

    /**
     * @param QuoteRepository $quoteRepository
     * @param ReviewRepository $reviewRepository
     * @param LegacyCommentsTransformer $commentsTransformer
     * @param ConsumerProductRepository $consumerProductRepository
     */
    public function __construct(QuoteRepository $quoteRepository, ReviewRepository $reviewRepository, protected LegacyCommentsTransformer $commentsTransformer, protected ConsumerProductRepository $consumerProductRepository)
    {
        $this->quoteRepository  = $quoteRepository;
        $this->reviewRepository = $reviewRepository;
    }

    /**
     * Handles converting a lead into the format that is required on the client side.
     *
     * @param EloquentQuote $lead
     * @param string $queue
     * @return array
     */
    public function transformLead(EloquentQuote $lead, string $queue): array
    {
        return [
            "basic"             => $this->formatBasicData($lead, $queue),
            "related_activity"  => $this->formatRelatedActivity($lead),
            "lead_verification" => $this->formatLeadVerification(VerificationService::WHITEPAGES->value, $lead->lead_verification_details?->toArray() ?? (new LeadVerificationResult())->toArray()),
        ];
    }

    /**
     * Handles retrieving and formatting the basic data for a given lead.
     *
     * @param EloquentQuote $lead
     * @param string $queue
     *
     * @return array
     * @throws BindingResolutionException
     */
    public function formatBasicData(EloquentQuote $lead, string $queue): array
    {
        /** @var EloquentVarStore|null $phoneVarStore */
        $phoneVarStore = $lead->varStores()->getQuery()
            ->where(EloquentVarStore::CATEGORY, 'estimator_data')
            ->where(EloquentVarStore::NAME, 'phone_validated')
            ->first();

        /** @var LocationRepository $locationRepository */
        $locationRepository = app()->make(LocationRepository::class);

        return [
            "id"                        => $lead->quoteid,
            "consumer_product_id"       => $this->consumerProductRepository->getConsumerProductByLegacyLeadId($lead->quoteid)?->id,
            "name"                      => $lead->firstname . " " . $lead->lastname,
            "email"                     => $lead->useremail,
            "address"                   => [
                "street"         => $lead->address->address1,
                "city"           => $lead->address->city,
                "county"         => $locationRepository->getZipCode($lead->address->zipcode)?->county,
                "state_abbr"     => $lead->address->state,
                "zip_code"       => $lead->address->zipcode,
                "full_formatted" => $lead->address->getFullAddress(),
                "latitude"       => $lead->address->latitude,
                "longitude"      => $lead->address->longitude
            ],
            "utility"                   => [
                "name" => $lead->getUtilityName(),
                "abbr" => $lead->getEloquentUtility()?->abbreviation,
                "id"   => $lead->utilityid
            ],
            "electric_cost"             => $lead->electriccost,
            "own_property"              => $lead->ownproperty === true || (strtolower($lead->ownproperty) === "yes"),
            "date_added"                => Carbon::createFromTimestamp($lead->timestampadded)->format("m/d/Y H:i:s T"),
            "phone"                     => $lead->address->phone,
            "phone_verification_status" => (bool)$phoneVarStore,
            "companies_requested"       => $lead->numberofquotes,
            "appointments_requested"    => $lead->besttimetocallother,
            "best_time_to_contact"      => $lead->besttimetocall,
            "classification"            => $lead->classification ? ucwords($lead->classification) : "Unverified",
            "status"                    => $this->getLeadStatus($lead),
            "queue"                     => $queue,
            "status_reason"             => $this->getLeadStatusReason($lead),
            "review_reason"             => $lead->underReview?->existing_reason,
            "industry"                  => ucwords(implode(" / ", array_filter([$lead->solar_lead ? "Solar" : null, $lead->roofing_lead ? "Roofing" : null]))),
            "type"                      => $lead->leadCategory ? $lead->leadCategory->name : "Residential",
            "origin"                    => trim(EloquentQuote::QUOTE_ORIGIN_ADMIN[$lead->origin] ?? 'unknown'),
            "has_communicated"          => count($lead->leadCommunications ?? []) > 0,
            "comments"                  => $this->commentsTransformer->transformComments($lead->leadComments()->orderByDesc(EloquentComment::TIMESTAMP_ADDED)->get())
        ];
    }

    /**
     * Returns the lead's status.
     *
     * @param EloquentQuote $lead
     * @return string
     */
    protected function getLeadStatus(EloquentQuote $lead): string
    {
        if ($lead->status === EloquentQuote::VALUE_STATUS_INITIAL && $lead->pendingReview !== null)
            return "Pending Review";

        if ($lead->status === EloquentQuote::VALUE_STATUS_UNDER_REVIEW)
            return "Under Review";

        return ucwords($lead->status);
    }

    /**
     * Returns the lead's reason for having it's respective status.
     *
     * @param EloquentQuote $lead
     * @return string
     */
    protected function getLeadStatusReason(EloquentQuote $lead): ?string
    {
        if ($lead->pendingReview !== null)
            return $lead->pendingReview->reason;

        if ($lead->underReview !== null)
            return $lead->underReview->reason;

        return ucwords($lead->statusreason);
    }

    /**
     * Handles retrieving and formatting the related activity for a given lead.
     *
     * @param EloquentQuote $lead
     * @return array
     */
    public function formatRelatedActivity(EloquentQuote $lead): array
    {
        $relatedQuotes = $this->quoteRepository->getPaginatedRelatedQuotes(
            $lead->quoteid,
            $lead->useremail,
            $lead->ipaddress,
            $lead->address?->zipcode,
            null,
            5
        );

        $relatedReviews = $this->reviewRepository->getRelatedReviewsByUserPaginated(
            $lead->userid,
            $lead->useremail
        );

        return [
            "ip_address"       => $lead->ipaddress,
            "email"            => $lead->useremail,
            "email_registered" => $lead->userid != null,
            "user_id"          => $lead->userid,
            "leads"            => collect($relatedQuotes->items())->map(function (EloquentQuote $relatedLead) {
                return [
                    "id"           => $relatedLead->quoteid,
                    "date"         => Carbon::createFromTimestamp($relatedLead->timestampadded)->format("m/d/Y"),
                    "status"       => $relatedLead->status,
                    "is_delivered" => $relatedLead->isDelivered(),
                    "name"         => $relatedLead->firstname . " " . $relatedLead->lastname,
                    "source"       => EloquentQuote::QUOTE_ORIGIN_ADMIN[$relatedLead->origin] ?? "Unknown"
                ];
            }),
            "reviews"          => collect($relatedReviews->items())->map(function (EloquentReview $review) {
                return [
                    "id"      => $review->reviewid,
                    "date"    => Carbon::createFromTimestamp($review->timestampadded)->format("M d, Y"),
                    "company" => $review->company?->companyname
                ];
            }),
        ];
    }

    /**
     * Handles retrieving and formatting the lead verification data for a given lead.
     *
     * @param string $service
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function formatLeadVerification(string $service, array $data): array
    {
        if($service === VerificationService::WHITEPAGES->value
        || $service === VerificationService::DUMMY->value) {
            return [
                "phone"      => [
                    "is_valid"        => $data['phone_is_valid'],
                    "name_match"      => $data['phone_name_match'],
                    "subscriber_name" => $data['phone_subscriber_name'],
                    "address_match"   => $data['phone_address_match'],
                    "line_type"       => $data['phone_line_type'],
                    "carrier"         => $data['phone_carrier'],
                    "is_commercial"   => $data['phone_is_commercial'],
                    "is_prepaid"      => $data['phone_is_prepaid'],
                    "warnings"        => $data['phone_warnings'],
                ],
                "address"    => [
                    "is_valid"      => $data['address_is_valid'],
                    "name_match"    => $data['address_name_match'],
                    "resident_name" => $data['address_resident_name'],
                    "type"          => $data['address_type'],
                    "is_commercial" => $data['address_is_commercial'],
                    "is_forwarder"  => $data['address_is_forwarder'],
                    "warnings"      => $data['address_warnings'],
                ],
                "email"      => [
                    "is_valid"         => $data['email_is_valid'],
                    "name_match"       => $data['email_name_match'],
                    "registered_name"  => $data['email_registered_name'],
                    "age"              => $data['email_age'],
                    "is_autogenerated" => $data['email_is_autogenerated'],
                    "is_disposable"    => $data['email_is_disposable'],
                    "warnings"         => $data['email_warnings'],
                ],
                "ip"         => [
                    "is_valid"         => true,
                    "name_match"       => $data['ip_is_valid'],
                    "geolocation"      => $data['ip_geolocation'],
                    "address_distance" => $data['ip_address_distance'],
                    "phone_distance"   => $data['ip_phone_distance'],
                    "is_proxy"         => $data['ip_is_proxy'],
                    "warning"          => $data['ip_warnings'],
                ],
                "confidence" => ((500 - $data['confidence']) / 500) * 100
            ];
        }
        else if($service === VerificationService::IP_QUALITY_SCORE->value) {
            $emailKeys = [
                "deliverability",
                "fraud_score",
                "frequent_complainer",
                "user_activity",
                "associated_names",
                "associated_phone_numbers",
                "first_seen"
            ];

            $phoneKeys = [
                "valid",
                "name",
                "line_type",
                "user_activity",
                "associated_email_addresses"
            ];

            return [
                "email" => collect($data['email'])->only($emailKeys)->toArray(),
                "phone" => collect($data['phone'])->only($phoneKeys)->toArray(),
                "transaction" => collect($data['transaction'])->toArray()
            ];
        }
        else {
            throw new Exception(__METHOD__.": Invalid service - $service");
        }
    }
}
