<?php

namespace App\Transformers\LeadProcessing;

use App\Models\LeadProcessingQueueConfiguration;
use Illuminate\Support\Collection;

class QueueTransformer
{
    /**
     * Handles transforming an array of queues to the expected client side format.
     *
     * @param Collection $queues
     * @return array
     */
    public function transformQueueConfigurations(Collection $queues): array
    {
        return $queues->map(function($queue) {
            return $this->transformQueueConfiguration($queue);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given queue to the expected client side format.
     *
     * @param LeadProcessingQueueConfiguration $queue
     * @return array
     */
    public function transformQueueConfiguration(LeadProcessingQueueConfiguration $queue): array
    {
        return [
            "id" => $queue->id,
            "name" => $queue->name,
            "status" => $this->formatStatus($queue->primary_status),
            "status_value" => $queue->primary_status
        ];
    }

    /**
     * Formats the machine-readable status into human readable.
     *
     * @param string $status
     * @return string
     */
    protected function formatStatus(string $status): string
    {
        return match ($status) {
            "initial" => "Initial",
            "pending_review" => "Pending Review",
            "under_review" => "Under Review",
            "aged" => "Aged",
            "affiliate" => "Affiliate",
            default => "Not Set",
        };
    }
}
