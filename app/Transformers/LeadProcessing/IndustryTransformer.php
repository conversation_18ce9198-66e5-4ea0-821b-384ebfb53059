<?php

namespace App\Transformers\LeadProcessing;

use App\Models\Legacy\Industry;
use Illuminate\Support\Collection;

class IndustryTransformer
{
    /**
     * Handles transforming a collection of industries to their respective client side representation
     *
     * @param Collection $industries
     * @return array
     */
    public function transformIndustries(Collection $industries): array
    {
        return $industries->map(function ($industry) {
            return $this->transformIndustry($industry);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given industry to its respective client side representation.
     *
     * @param Industry $industry
     * @return array
     */
    public function transformIndustry(Industry $industry): array
    {
        return [
            "id"   => $industry->{Industry::FIELD_KEY},
            "name" => $industry->{Industry::FIELD_DISPLAY_NAME}
        ];
    }
}
