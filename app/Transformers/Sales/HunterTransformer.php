<?php

namespace App\Transformers\Sales;

use App\Models\Hunter;
use App\Models\HunterIndustry;
use App\Models\HunterState;
use Illuminate\Support\Collection;

class HunterTransformer
{
    /**
     * @param Collection $hunters
     *
     * @return array
     */
    public function transformHunters(Collection $hunters): array
    {
        return $hunters->map(fn(Hunter $hunter) => $this->transformHunter($hunter))->toArray();
    }

    /**
     * @param Hunter $hunter
     *
     * @return array
     */
    public function transformHunter(Hunter $hunter): array
    {
        return [
            'id'                     => $hunter->id,
            'name'                   => $hunter->user->name,
            'user_id'                => $hunter->user->id,
            'include_in_round_robin' => $hunter->include_in_round_robin,
            'states'                 => $hunter->states->map(fn(HunterState $hunterState) => $hunterState->state_abbr),
            'industries'             => $hunter->industries->map(fn(HunterIndustry $hunterIndustry) => $hunterIndustry->industry)
        ];
    }
}
