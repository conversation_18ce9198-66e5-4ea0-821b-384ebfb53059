<?php

namespace App\Transformers\Sales;

use App\Models\SuccessManager;
use App\Models\User;
use Illuminate\Support\Collection;

class SuccessManagerTransformer
{
    /**
     * @param  Collection  $successManagers
     * @return array
     */
    public function transformAll(Collection $successManagers): array
    {
        return $successManagers->map(fn(SuccessManager $successManager
        ) => $this->transform($successManager))->toArray();
    }

    /**
     * @param  SuccessManager  $successManager
     * @return array
     */
    public function transform(SuccessManager $successManager): array
    {
        return
            [
                "id" => $successManager->{SuccessManager::FIELD_ID},
                "type" => $successManager::TYPE_NAMES[$successManager->{SuccessManager::FIELD_TYPE}],
                "name" => $successManager->{SuccessManager::RELATION_USER}?->{User::FIELD_NAME} ?? 'User unavailable',
                "user_id" => $successManager->{SuccessManager::FIELD_USER_ID},
                "include_in_sales_round_robin" => $successManager->{SuccessManager::FIELD_INCLUDE_IN_SALES_ROUND_ROBIN} ? "Yes" : "No"
            ];
    }

    /**
     * @param  Collection  $successManagers
     * @return array
     */
    public function transformAllIntoFilterableOptionsArray(Collection $successManagers): array
    {
        $data = [];

        foreach ($successManagers as $successManager) {
            $successManagerId = !!$successManager->{SuccessManager::TABLE.'.'.SuccessManager::FIELD_ID} ?
                $successManager->{SuccessManager::TABLE.'.'.SuccessManager::FIELD_ID} : $successManager->{SuccessManager::FIELD_ID};

            if (!!$successManager->{User::TABLE.'.'.User::FIELD_NAME})
                $data[$successManager->{User::TABLE.'.'.User::FIELD_NAME}] = $successManagerId;
            else
                $data[$successManager->{SuccessManager::RELATION_USER}->{User::FIELD_NAME}] = $successManagerId;
        }

        return $data;
    }
}
