<?php

namespace App\Transformers\Sales;

use App\Models\SupportOfficer;
use Illuminate\Support\Collection;

class SupportOfficerTransformer
{
    /**
     * @param Collection $supportOfficers
     *
     * @return array
     */
    public function transformSupportOfficers(Collection $supportOfficers): array
    {
        return $supportOfficers->map(fn(SupportOfficer $supportOfficer) => $this->transformSupportOfficer($supportOfficer))->toArray();
    }

    /**
     * @param SupportOfficer $supportOfficer
     *
     * @return array
     */
    public function transformSupportOfficer(SupportOfficer $supportOfficer): array
    {
        return [
            'id'                     => $supportOfficer->id,
            'name'                   => $supportOfficer->user->name,
            'user_id'                => $supportOfficer->user->id,
            'include_in_round_robin' => $supportOfficer->include_in_round_robin
        ];
    }
}
