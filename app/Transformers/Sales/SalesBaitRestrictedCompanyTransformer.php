<?php

namespace App\Transformers\Sales;

use App\Models\SalesBaitRestrictedCompany;
use Illuminate\Support\Collection;

class SalesBaitRestrictedCompanyTransformer
{
    /**
     * Handles transforming a collection of sales bait restricted companies.
     *
     * @param Collection<SalesBaitRestrictedCompany> $companies
     * @return array
     */
    public function transformAll(Collection $companies): array
    {
        return $companies->map(fn(SalesBaitRestrictedCompany $company) => $this->transform($company))->toArray();
    }

    /**
     * Handles transforming a SalesBaitRestrictedCompany to the expected front end representation.
     *
     * @param SalesBaitRestrictedCompany $company
     * @return array
     */
    public function transform(SalesBaitRestrictedCompany $company): array
    {
        return [
            "company_id" => $company->company_id,
            "name" => $company->company->companyname
        ];
    }
}
