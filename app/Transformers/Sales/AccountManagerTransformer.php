<?php

namespace App\Transformers\Sales;

use App\Models\AccountManager;
use App\Models\SuccessManager;
use App\Models\User;
use Illuminate\Support\Collection;

class AccountManagerTransformer
{
    /**
     * Handles transforming a collection of account managers to their respective client side representation
     *
     * @param Collection $accountManagers
     * @return array
     */
    public function transformAccountManagers(Collection $accountManagers): array
    {
        return $accountManagers->map(function ($accountManager) {
            return $this->transformAccountManager($accountManager);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given industry to its respective client side representation.
     *
     * @param AccountManager $accountManager
     * @return array
     */
    public function transformAccountManager(AccountManager $accountManager): array
    {
        return [
            "id" => $accountManager->{AccountManager::FIELD_ID},
            "type" => AccountManager::TYPES[$accountManager->{AccountManager::FIELD_TYPE}],
            "name" => $accountManager->{AccountManager::RELATION_USER}?->{User::FIELD_NAME} ?? 'User unavailable',
            "user_id" => $accountManager->{AccountManager::FIELD_USER_ID},
            "include_in_sales_round_robin" => $accountManager->include_in_sales_round_robin
        ];
    }
}
