<?php

namespace App\Transformers;

use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCategory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class CompanyLeadsTransformer
{
    /**
     * @param Collection $companyLeads
     * @return array
     */
    public function transformCompanyLeads(Collection $companyLeads): array
    {
        return $companyLeads->map(fn(EloquentQuoteCompany $companyLead) => $this->transform($companyLead))->toArray();
    }

    /**
     * @param EloquentQuoteCompany $companyLead
     * @return array
     */
    public function transform(EloquentQuoteCompany $companyLead): array
    {
        return [
            'id'         => $companyLead->{EloquentQuote::QUOTE_ID},
            'category'   => $companyLead->{LeadCategory::NAME},
            'date'       => $companyLead->{EloquentQuote::TIMESTAMP_ADDED} ?
                            Carbon::parse($companyLead->{EloquentQuote::TIMESTAMP_ADDED})->format('Y-m-d') : null,
            'chargeable' => ((int) $companyLead->{EloquentQuoteCompany::CHARGEABLE}) ? 'Yes' : 'No',
            'delivered'  => ((int) $companyLead->{EloquentQuoteCompany::DELIVERED}) ? 'Yes' : 'No',
            'contact'    => $companyLead->{EloquentQuote::FIRST_NAME}.' '.$companyLead->{EloquentQuote::LAST_NAME},
            'address'    => $companyLead->{EloquentAddress::ADDRESS1}.', '.$companyLead->{EloquentAddress::CITY}.', '.
                            $companyLead->{EloquentAddress::STATE}.', '.$companyLead->{EloquentAddress::ZIP_CODE},
            'cost'       => $companyLead->{EloquentQuoteCompany::COST}
        ];
    }
}
