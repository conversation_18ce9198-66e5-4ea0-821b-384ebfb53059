<?php

namespace App\Transformers\Legacy;

use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompanyAddress;
use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;

class CompanyLocationToLegacyAddressTransformer
{
    public function transformCompanyLocationToLegacyAddress(CompanyLocation $companyLocation): array
    {
        return [
            EloquentCompanyAddress::FIELD_NAME        => $companyLocation->{CompanyLocation::FIELD_NAME},
            EloquentAddress::PHONE                    => $companyLocation->{CompanyLocation::FIELD_PHONE},
            EloquentAddress::ADDRESS1                 => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_ADDRESS_1},
            EloquentAddress::ADDRESS2                 => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_ADDRESS_2},
            EloquentAddress::CITY                     => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_CITY},
            EloquentAddress::STATE                    => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_STATE},
            EloquentAddress::ZIP_CODE                 => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_ZIP_CODE},
            EloquentAddress::LATITUDE                 => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_LATITUDE},
            EloquentAddress::LONGITUDE                => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_LONGITUDE},
            'odin_address_id'                         => $companyLocation->{CompanyLocation::RELATION_ADDRESS}->{Address::FIELD_ID},
        ];
    }
}
