<?php

namespace App\Transformers\Legacy;

use App\Models\Legacy\EloquentUser;
use Illuminate\Support\Collection;

class LegacyUserTransformer
{
    /**
     * @param Collection $users
     *
     * @return array
     */
    public function transformUsers(Collection $users): array
    {
        return $users->map(fn(EloquentUser $user) => $this->transformUser($user))->toArray();
    }

    /**
     * @param EloquentUser $user
     *
     * @return array
     */
    public function transformUser(EloquentUser $user): array
    {
        return [
            'id' => $user->userid,
            'name' => $user->name,
            'first_name' => $user->firstname,
            'last_name' => $user->lastname,
            'email' => $user->email,
            'status' => $user->status,
            'security_level' => $user->securitylevel,
            'date_registered' => $user->dateregistered,
            'phone' => $user->phone
        ];
    }
}
