<?php

namespace App\Transformers\Legacy;

use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\Odin\ConsumerProductRepository;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class QuoteTransformer
{
    public function __construct(
        protected LegacyAddressTransformer $addressTransformer,
        protected ConsumerProductRepository $consumerProductRepository
    ){

    }

    /**
     * @param Collection<EloquentQuote> $leads
     *
     * @return array
     */
    public function transformUnsoldLeads(Collection $leads): array
    {
        return $leads->map(fn(EloquentQuote $lead) => array_merge($this->transformLead($lead), [
            'sold_status' => 'Unsold',
            'address' => $this->addressTransformer->transform($lead->address)
        ]))->toArray();
    }

    /**
     * @param Collection<EloquentQuote> $leads
     *
     * @return array
     */
    public function transformUndersoldLeads(Collection $leads): array
    {
        return $leads->map(fn(EloquentQuote $lead) => array_merge($this->transformLead($lead), [
            'sold_status' => 'Undersold',
            'totalSold' => $lead->total,
            'address' => $this->addressTransformer->transform($lead->address)
        ]))->toArray();
    }

    /**
     * @param EloquentQuote $lead
     *
     * @return array
     */
    public function transformLead(EloquentQuote $lead): array
    {
        return [
            'id'                        => $lead->quoteid,
            'number_of_quotes'          => $lead->numberofquotes,
            'contact_method'            => $lead->contact_method,
            'home_appointment_times'    => $lead->besttimetocallother,
            'online_consultation_times' => $lead->besttimetocall,
            'name'                      => $lead->{EloquentQuote::FIRST_NAME} . ' ' . $lead->{EloquentQuote::LAST_NAME},
            'email'                     => $lead->{EloquentQuote::USER_EMAIL},
            'phone'                     => $lead->address->{EloquentAddress::PHONE},
            'utility_cost'              => $lead->{EloquentQuote::ELECTRIC_COST},
            'utility'                   => $lead->getUtilityName(),
            'date_added'                => Carbon::createFromTimestamp($lead->timestampadded)->format("m/d/Y H:i:s T"),
            'consumer_product_id'       => $this->consumerProductRepository->getConsumerProductByLegacyLeadId($lead->quoteid)?->id
        ];
    }
}
