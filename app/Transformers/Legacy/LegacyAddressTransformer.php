<?php

namespace App\Transformers\Legacy;

use App\Models\Legacy\EloquentAddress;

class LegacyAddressTransformer
{
    /**
     * @param EloquentAddress $address
     *
     * @return array
     */
    public function transform(EloquentAddress $address): array
    {
        return [
            'id'             => $address->addressid,
            'address_1'      => $address->address1,
            'address_2'      => $address->address2,
            'city'           => $address->city,
            'state'          => $address->state,
            'zip_code'       => $address->zipcode,
            'longitude'      => $address->longitude,
            'latitude'       => $address->latitude
        ];
    }
}
