<?php

namespace App\Transformers\Legacy;

use App\Models\Legacy\EloquentUtility;
use App\Models\Legacy\SEUtility;
use Illuminate\Support\Collection;

class UtilityTransformer
{

    /**
     * @param Collection|SEUtility $utilities
     * @return array
     */
    public function transformUtilities(Collection|SEUtility $utilities): array
    {
        $utilities->load(SEUtility::RELATION_LEGACY_UTILITY);

        $transformed = [];
        foreach ($utilities as $utility){
            $transformed[] = $this->transformUtility($utility->eloquentUtility);
        }
        return $transformed;
    }

    /**
     * @param EloquentUtility $utility
     * @return array
     */
    public function transformUtility(EloquentUtility $utility): array
    {
        return [
            'id'                        => $utility->utilityid,
            EloquentUtility::FIELD_NAME => $utility->name,
            EloquentUtility::FIELD_UUID => $utility->uuid,
            EloquentUtility::FIELD_SLUG => $utility->slug,
            // TODO: Make this not bad
            "picture" => "https://d3skrlqoqnneot.cloudfront.net/main/images/UtilityLogos/" . $utility->eia_id . ".jpg"
        ];
    }


}
