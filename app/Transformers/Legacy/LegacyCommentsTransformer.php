<?php

namespace App\Transformers\Legacy;

use App\Models\Legacy\EloquentComment;
use Carbon\Carbon;
use Illuminate\Support\Collection;

class LegacyCommentsTransformer
{
    /**
     * @param Collection $comments
     *
     * @return array
     */
    public function transformComments(Collection $comments): array
    {
        return $comments->map(fn(EloquentComment $comment) => $this->transformComment($comment))->toArray();
    }

    /**
     * @param EloquentComment $comment
     *
     * @return array
     */
    public function transformComment(EloquentComment $comment): array
    {
        return [
            'id' => $comment->commentid,
            'comment' => $comment->comment,
            'added_by' => $comment->user?->name,
            'added_at' => $comment->timestampadded ? Carbon::createFromTimestamp($comment->timestampadded)->format('m/d/Y H:i:s T') : null
        ];
    }
}
