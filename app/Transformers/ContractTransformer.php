<?php

namespace App\Transformers;

use App\Models\Contract;
use App\Repositories\ContractsRepository;
use Illuminate\Support\Collection;

class ContractTransformer
{
    public function __construct(protected ContractsRepository $contractRepository) {}

    /**
     * Handles transforming a collection of templates from a third party provider
     *
     * @param Collection $contracts
     * @return array
     */
    public function transformContracts(Collection $contracts): array
    {
        return $contracts->map(function ($contract) {
            return $this->transformContract($contract);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given campaign to its respective client side representation.
     *
     * @param Contract $contract
     * @return array
     */
    public function transformContract(Contract $contract): array
    {
        return [
            'id'                    => $contract->id,
            'contract_provider_id'  => $contract->contract_provider_id,
            'website_id'            => $contract->website_id,
            'website'               => $contract->website,
            'active'                => $contract->active,
            'description'           => $contract->description,
            'contract_key_id'       => $contract->contract_key_id,
            'contract_key'          => $contract->contractKey,
            'created_at'            => $contract->created_at->format("Y-m-d H:i:s"),
        ];
    }

}