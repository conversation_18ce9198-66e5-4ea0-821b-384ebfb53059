<?php

namespace App\Observers;

use App\Jobs\RecordMonitoringLog;
use App\Models\Odin\ProductAssignment;
use App\Repositories\LogMonitoringRepository;
use Exception;
use Illuminate\Support\Facades\App;

class ProductAssignmentObserver
{

    /**
     * @param ProductAssignment $productAssignment
     * @return void
     */
    public function deleting(ProductAssignment $productAssignment)
    {
        $this->writeLog(
            "Deleting product assignment: {$productAssignment->{ProductAssignment::FIELD_ID}}",
            [
                ProductAssignment::FIELD_ID => $productAssignment->{ProductAssignment::FIELD_ID},
                ProductAssignment::FIELD_LEGACY_ID => $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                ProductAssignment::FIELD_COMPANY_ID => $productAssignment->{ProductAssignment::FIELD_COMPANY_ID},
                ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $productAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID},
                ProductAssignment::FIELD_CHARGEABLE => $productAssignment->{ProductAssignment::FIELD_CHARGEABLE},
                ProductAssignment::FIELD_DELIVERED => $productAssignment->{ProductAssignment::FIELD_DELIVERED},
                ProductAssignment::FIELD_DELIVERED_AT => $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT}?->timestamp ?? 0,
                ProductAssignment::FIELD_COST => $productAssignment->{ProductAssignment::FIELD_COST},
                ProductAssignment::FIELD_REJECTION_EXPIRY => $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}?->timestamp ?? 0
            ]
        );
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return void
     */
    public function saving(ProductAssignment $productAssignment)
    {
        $id = $productAssignment->{ProductAssignment::FIELD_ID} ?? "new";

        $this->writeLog(
            "Saving product assignment: $id",
            [
                ProductAssignment::FIELD_ID => $productAssignment->{ProductAssignment::FIELD_ID} ?? 0,
                ProductAssignment::FIELD_LEGACY_ID => $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                ProductAssignment::FIELD_COMPANY_ID => $productAssignment->{ProductAssignment::FIELD_COMPANY_ID},
                ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $productAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID},
                ProductAssignment::FIELD_CHARGEABLE => $productAssignment->{ProductAssignment::FIELD_CHARGEABLE},
                ProductAssignment::FIELD_DELIVERED => $productAssignment->{ProductAssignment::FIELD_DELIVERED},
                ProductAssignment::FIELD_DELIVERED_AT => $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT}?->timestamp ?? 0,
                ProductAssignment::FIELD_COST => $productAssignment->{ProductAssignment::FIELD_COST},
                ProductAssignment::FIELD_REJECTION_EXPIRY => $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}?->timestamp ?? 0
            ]
        );
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return void
     */
    public function saved(ProductAssignment $productAssignment)
    {
        $id = $productAssignment->{ProductAssignment::FIELD_ID} ?? "new";

        $this->writeLog(
            "Saved product assignment: $id",
            [
                ProductAssignment::FIELD_ID => $productAssignment->{ProductAssignment::FIELD_ID} ?? 0,
                ProductAssignment::FIELD_LEGACY_ID => $productAssignment->{ProductAssignment::FIELD_LEGACY_ID},
                ProductAssignment::FIELD_COMPANY_ID => $productAssignment->{ProductAssignment::FIELD_COMPANY_ID},
                ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $productAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID},
                ProductAssignment::FIELD_CHARGEABLE => $productAssignment->{ProductAssignment::FIELD_CHARGEABLE},
                ProductAssignment::FIELD_DELIVERED => $productAssignment->{ProductAssignment::FIELD_DELIVERED},
                ProductAssignment::FIELD_DELIVERED_AT => $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT}?->timestamp ?? 0,
                ProductAssignment::FIELD_COST => $productAssignment->{ProductAssignment::FIELD_COST},
                ProductAssignment::FIELD_REJECTION_EXPIRY => $productAssignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}?->timestamp ?? 0
            ]
        );
    }

    /**
     * @return string
     */
    private function getTrace(): string
    {
        ob_start();

        debug_print_backtrace();

        $trace = ob_get_contents();

        ob_end_clean();

        return base64_encode(gzcompress($trace, 9));
    }

    /**
     * @param string $message
     * @param array|null $payload
     * @return void
     */
    private function writeLog(string $message, ?array $payload = []): void
    {
        try {
            $payload['env'] = App::environment();
            $payload['trace'] = $this->getTrace();

            RecordMonitoringLog::dispatch($message, $payload, LogMonitoringRepository::LOGGER_MODEL_EVENTS);
        }
        catch(Exception $e) {
            logger()->error($e->getMessage());
        }
    }
}
