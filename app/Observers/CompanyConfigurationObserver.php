<?php

namespace App\Observers;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Services\PubSub\PubSubService;

class CompanyConfigurationObserver
{
    /**
     * @param PubSubService $pubSubService
     */
    public function __construct(
        private readonly PubSubService $pubSubService
    )
    {

    }

    /**
     * @param CompanyConfiguration $companyConfiguration
     * @return void
     */
    public function saved(CompanyConfiguration $companyConfiguration): void
    {
        $changes = $companyConfiguration->getChanges();

        if(!empty(array_intersect_key(
            $changes,
            [
                CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE,
                CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR,
                CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE
            ]
        ))) {
            $this->pubSubService->handle(
                EventCategory::COMPANIES->value,
                EventName::APPOINTMENTS_STATUS_UPDATED->value,
                [
                    'company_reference' => $companyConfiguration->{CompanyConfiguration::RELATION_COMPANY}->{Company::FIELD_REFERENCE},
                    'old_statuses' => [
                        CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE => $changes[CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE] ?? $companyConfiguration->{CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE},
                        CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR => $changes[CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR] ?? $companyConfiguration->{CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR},
                        CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE => $changes[CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE] ?? $companyConfiguration->{CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE}
                    ],
                    'new_statuses' => [
                        CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE => $companyConfiguration->{CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE},
                        CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR => $companyConfiguration->{CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR},
                        CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE => $companyConfiguration->{CompanyConfiguration::FIELD_MI_APPOINTMENTS_ACTIVE}
                    ]
                ]
            );
        }
    }
}
