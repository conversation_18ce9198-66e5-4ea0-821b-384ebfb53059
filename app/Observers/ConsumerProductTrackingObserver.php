<?php

namespace App\Observers;

use App\Jobs\RecordMonitoringLog;
use App\Models\Odin\ConsumerProductTracking;
use App\Repositories\LogMonitoringRepository;
use Exception;
use Illuminate\Support\Facades\App;

class ConsumerProductTrackingObserver
{
    /**
     * @param ConsumerProductTracking $consumerProductTracking
     * @return void
     */
    public function deleting(ConsumerProductTracking $consumerProductTracking): void
    {
        $id = $consumerProductTracking->{ConsumerProductTracking::ID};

        $this->writeLog(
            "Deleting consumer product tracking: $id",
            [
                ConsumerProductTracking::ID => $id,
                ConsumerProductTracking::CONSUMER_PRODUCT_ID => $consumerProductTracking->{ConsumerProductTracking::CONSUMER_PRODUCT_ID} ?? 0
            ]
        );
    }

    /**
     * @return string
     */
    private function getTrace(): string
    {
        ob_start();

        debug_print_backtrace();

        $trace = ob_get_contents();

        ob_end_clean();

        return base64_encode(gzcompress($trace, 9));
    }

    /**
     * @param string $message
     * @param array|null $payload
     * @return void
     */
    private function writeLog(string $message, ?array $payload = []): void
    {
        try {
            $payload['env'] = App::environment();
            $payload['trace'] = $this->getTrace();

            RecordMonitoringLog::dispatch($message, $payload, LogMonitoringRepository::LOGGER_MODEL_EVENTS);
        }
        catch(Exception $e) {
            logger()->error($e->getMessage());
        }
    }
}
