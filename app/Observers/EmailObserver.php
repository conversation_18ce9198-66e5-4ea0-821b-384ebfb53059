<?php

namespace App\Observers;

use App\Enums\ActivityType;
use App\Models\Email;
use App\Models\Odin\Company;
use App\Repositories\ActivityFeedRepository;

class EmailObserver
{

    public function __construct(
        protected ActivityFeedRepository $activityFeedRepository,
    ) {}

    public bool $afterCommit = true;

    /**
     * Handle the Email "created" event.
     *
     * @param Email $email
     * @return void
     */
    public function created(Email $email): void
    {

        $company = $email->toCompanyUser()->withTrashed()->first()?->company;
        $user    = $email->fromUser()->withTrashed()->first();

        if ($company) {
            $newActivity = $this->activityFeedRepository->createActivity(
                $email->{Email::FIELD_ID},
                ActivityType::EMAIL,
                $company->{Company::FIELD_ID},
                $user?->id ?? 0,
                $email->company_cadence_group_action_id,
            );

            if (!$newActivity) {
                logger()->error("Error creating new ActivityFeed item from Email model - id '{$email->id}'.");
            }
        }

    }
}
