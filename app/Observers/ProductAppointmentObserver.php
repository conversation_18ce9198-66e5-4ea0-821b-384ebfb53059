<?php

namespace App\Observers;

use App\Jobs\RecordMonitoringLog;
use App\Models\Odin\ProductAppointment;
use App\Repositories\LogMonitoringRepository;
use Exception;
use Illuminate\Support\Facades\App;

class ProductAppointmentObserver
{
    /**
     * Handle the ProductAppointment "deleting" event.
     *
     * @param ProductAppointment $productAppointment
     * @return void
     */
    public function deleting(ProductAppointment $productAppointment)
    {
        $this->writeLog(
            "Deleting product appointment: {$productAppointment->{ProductAppointment::ID}}",
            [
                ProductAppointment::ID => $productAppointment->{ProductAppointment::ID} ?? 0,
                ProductAppointment::CONSUMER_PRODUCT_ID => $productAppointment->{ProductAppointment::CONSUMER_PRODUCT_ID},
                ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => $productAppointment->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID},
                ProductAppointment::LEGACY_ID => $productAppointment->{ProductAppointment::LEGACY_ID},
                ProductAppointment::ORIGINAL_APPOINTMENT_ID => $productAppointment->{ProductAppointment::ORIGINAL_APPOINTMENT_ID} ?? 0
            ]
        );
    }

    /**
     * @param ProductAppointment $productAppointment
     * @return void
     */
    public function saving(ProductAppointment $productAppointment)
    {
        $id = $productAppointment->{ProductAppointment::ID} ?? "new";

        $this->writeLog(
            "Saving product appointment: {$id}",
            [
                ProductAppointment::ID => $productAppointment->{ProductAppointment::ID} ?? 0,
                ProductAppointment::CONSUMER_PRODUCT_ID => $productAppointment->{ProductAppointment::CONSUMER_PRODUCT_ID},
                ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => $productAppointment->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID},
                ProductAppointment::LEGACY_ID => $productAppointment->{ProductAppointment::LEGACY_ID},
                ProductAppointment::ORIGINAL_APPOINTMENT_ID => $productAppointment->{ProductAppointment::ORIGINAL_APPOINTMENT_ID} ?? 0
            ]
        );
    }

    /**
     * @param ProductAppointment $productAppointment
     * @return void
     */
    public function saved(ProductAppointment $productAppointment)
    {
        $id = $productAppointment->{ProductAppointment::ID} ?? "new";

        $this->writeLog(
            "Saved product appointment: {$id}",
            [
                ProductAppointment::ID => $productAppointment->{ProductAppointment::ID} ?? 0,
                ProductAppointment::CONSUMER_PRODUCT_ID => $productAppointment->{ProductAppointment::CONSUMER_PRODUCT_ID},
                ProductAppointment::LEAD_CONSUMER_PRODUCT_ID => $productAppointment->{ProductAppointment::LEAD_CONSUMER_PRODUCT_ID},
                ProductAppointment::LEGACY_ID => $productAppointment->{ProductAppointment::LEGACY_ID},
                ProductAppointment::ORIGINAL_APPOINTMENT_ID => $productAppointment->{ProductAppointment::ORIGINAL_APPOINTMENT_ID} ?? 0
            ]
        );
    }

    /**
     * @param string $message
     * @param array|null $payload
     * @return void
     */
    private function writeLog(string $message, ?array $payload = []): void
    {
        try {
            $payload['env'] = App::environment();

            RecordMonitoringLog::dispatch($message, $payload, LogMonitoringRepository::LOGGER_APPOINTMENTS);
        }
        catch(Exception $e) {
            logger()->error($e->getMessage());
        }
    }
}
