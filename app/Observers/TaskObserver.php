<?php

namespace App\Observers;

use App\Enums\ActivityType;
use App\Models\Odin\Company;
use App\Models\Sales\Task;
use App\Repositories\ActivityFeedRepository;

class TaskObserver
{
    public function __construct(
        protected ActivityFeedRepository $activityFeedRepository,
    ) {}

    public bool $afterCommit = true;

    /**
     * Handle the Task "created" event.
     *
     * @param Task $task
     * @return void
     */
    public function created(Task $task): void
    {
        $companyId = $task->{Task::FIELD_PAYLOAD}['company_id'] ?? null; // Virtual field for company_id not available on created event
        if ($companyId) {
            $company = Company::query()->findOrFail($companyId);
            $userId = $task->{Task::FIELD_ASSIGNED_USER_ID} ?? 0;

            $newActivity = $this->activityFeedRepository->createActivity(
                $task->{Task::FIELD_ID},
                ActivityType::TASK,
                $company->{Company::FIELD_ID},
                $userId
            );

            if (!$newActivity) {
                logger()->error("Error creating new ActivityFeed item from Task model - id '{$task->{Task::FIELD_ID}}'.");
            }
        }
    }
}
