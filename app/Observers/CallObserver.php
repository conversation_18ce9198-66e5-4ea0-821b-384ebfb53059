<?php

namespace App\Observers;

use App\Enums\ActivityType;
use App\Enums\CommunicationRelationTypes;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Events\ConsumerProcessing\ConsumerProcessingActivityEvent;
use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\UserPhone;
use App\Repositories\ActivityFeedRepository;
use App\Services\HelperService;
use App\Services\Odin\ContactIdentification\CompanyLocationContactIdentificationService;
use App\Services\Odin\ContactIdentification\CompanyUserContactIdentificationService;
use App\Services\Odin\ContactIdentification\ContactIdentificationService;
use Exception;
use Illuminate\Support\Facades\Auth;

class CallObserver
{

    public bool $afterCommit = true;

    const AVAILABLE_RELATION_TYPES_TO_CREATE_ACTIVITIES = [
        CompanyLocationContactIdentificationService::RELATION_SUBTYPE,
        CompanyUserContactIdentificationService::RELATION_SUBTYPE,
    ];

    protected int $solarReviewsCompanyId;

    public function __construct(
        protected ActivityFeedRepository $activityFeedRepository,
        protected ContactIdentificationService $contactIdentificationService
    )
    {
        $this->solarReviewsCompanyId = config('app.solar_reviews_company_id');
    }

    /**
     * Check need to create an activity feed
     * @param Call $call
     * @return bool
     */
    private function checkCallActivity(Call $call): bool
    {
        return in_array($call->{Call::FIELD_RELATION_TYPE}, self::AVAILABLE_RELATION_TYPES_TO_CREATE_ACTIVITIES) ||
            $call->{Call::FIELD_NOTE};
    }

    private function dispatchConsumerProcessingEvent(Call $call): void
    {
        if ($call->relation_type === CommunicationRelationTypes::CONSUMER_PRODUCT->value) {
            $consumerProduct = ConsumerProduct::query()
                ->find($call->relation_id);

            if ($consumerProduct) {
                event(new ConsumerProcessingActivityEvent(
                    type: ConsumerProcessingActivityType::CALL,
                    consumerProduct: $consumerProduct,
                    user: Auth::user(),
                    relatedActivity: $call)
                );
            }
        }
    }

    /**
     * Get company by relation id
     * @param Call $call
     * @return Company|null
     * @throws Exception
     */
    private function getActivityCompany(Call $call): Company|null
    {
        if (!empty($call->{Call::FIELD_RELATION_TYPE})){
            $contactIdentificationService = $this->contactIdentificationService
                ->generateContactIdentificationServiceByType($call->{Call::FIELD_RELATION_TYPE});

            if (!$contactIdentificationService) return null;

            return $contactIdentificationService->getCompanyByContactId($call->{Call::FIELD_RELATION_ID});
        }else return Company::query()->find($this->solarReviewsCompanyId)->first();
    }

    /**
     * @param Call $call
     * @return void
     */
    public function created(Call $call): void
    {
        if ($this->checkCallActivity($call))
            $this->findOrCreateActivityItem($call);

        $this->dispatchConsumerProcessingEvent($call);
    }

    /**
     * @param Call $call
     * @return void
     */
    private function findOrCreateActivityItem(Call $call): void
    {
        $company = $this->getActivityCompany($call);

        if (!$company) return;

        $existingActivity = $this->activityFeedRepository->findActivityByItemIdAndType($call->{Call::FIELD_ID}, ActivityType::CALL);
        if ($existingActivity) return;

        $userId      = $this->getPhoneOwnerId($call);

        $newActivity = $this->activityFeedRepository->createActivity(
            $call->{Call::FIELD_ID},
            ActivityType::CALL,
            $company->{Company::FIELD_ID},
            $userId
            );

        if (!$newActivity) {
            logger()->error("Error creating new ActivityFeed item from Call model - id '{$call->{Call::FIELD_ID}}'.");
        }
    }

    /**
     * @param Call $call
     * @return void
     */
    public function updated(Call $call): void
    {
        if ($this->checkCallActivity($call))
            $this->findOrCreateActivityItem($call);
        if ($call->wasChanged(Call::FIELD_RELATION_TYPE)) {
            $this->dispatchConsumerProcessingEvent($call);
        }
    }

    /**
     * @param Call $call
     * @return void
     */
    public function saving(Call $call): void
    {
        $call->{Call::FIELD_FORMATTED_OTHER_NUMBER} = $call->{Call::FIELD_OTHER_NUMBER} ? intval(HelperService::removeNonNumbers($call->{Call::FIELD_OTHER_NUMBER})) : null;
    }

    /**
     * @param Call $call
     * @return ?int
     */
    private function getPhoneOwnerId(Call $call): ?int
    {
        $owner = UserPhone::withTrashed()
            ->where(UserPhone::FIELD_PHONE_ID, $call->{Call::FIELD_PHONE_ID})
            ->where(UserPhone::FIELD_CREATED_AT, '<=', $call->{Call::FIELD_CREATED_AT})
            ->orderBy(UserPhone::FIELD_CREATED_AT, 'DESC')
            ->first();

        return $owner?->{UserPhone::FIELD_USER_ID} ?? 0;
    }

}
