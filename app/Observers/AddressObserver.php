<?php

namespace App\Observers;

use App\Models\Locations\USLocation;
use App\Models\Odin\Address;
use App\Repositories\LocationRepository;

class AddressObserver
{
    public function __construct(protected LocationRepository $locationRepository) {}

    /**
     * Handle the Address "saving" event.
     */
    public function saving(Address $address): void
    {
        if (!$address->zip_code || !$address->isDirty(Address::FIELD_ZIP_CODE)) {
            return;
        }

        $zipCodeLocation = $this->locationRepository->getZipCode($address->zip_code);
        if ($zipCodeLocation) {
            $this->addLocationIds($address, $zipCodeLocation);
            $previous = $address->getOriginal(Address::FIELD_ZIP_CODE);
            if ($previous) { // ignore new addresses, as CompanyLocationObserver handles model creation
                $this->handleCompanyLocationAddressUpdate($address);
            }
        }
    }

    /**
     * @param Address $address
     * @param USLocation $zipCodeLocation
     * @return void
     */
    private function addLocationIds(Address $address, USLocation $zipCodeLocation): void
    {
        $address->zip_code_location_id = $zipCodeLocation->id;
        $address->county = $zipCodeLocation->county;
        $address->county_location_id = $this->locationRepository->getCounty($zipCodeLocation->state_key, $zipCodeLocation->county_key)?->id;
        $address->state_location_id = $this->locationRepository->getState($zipCodeLocation->state_key)?->id;
    }

    /**
     * @param Address $address
     * @return void
     */
    private function handleCompanyLocationAddressUpdate(Address $address): void
    {
        $companyLocation = $address->companyLocation;
        if ($companyLocation)
            CompanyLocationObserver::handleCompanyLocationZipCodeChange($companyLocation);
    }
}
