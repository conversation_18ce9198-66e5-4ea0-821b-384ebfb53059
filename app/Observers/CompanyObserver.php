<?php

namespace App\Observers;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Events\Company\CompanySuspendedEvent;
use App\Events\CompanyChangedPurchasingStatusEvent;
use App\Jobs\CreateLegacyCompanyJob;
use App\Jobs\GetCompanyMetricsJob;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Services\CompanySlugService;
use App\Services\PubSub\PubSubService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Random\RandomException;

class CompanyObserver implements ShouldHandleEventsAfterCommit
{
    public $afterCommit = true;

    /**
     * @param PubSubService $pubSubService
     * @param CompanySlugService $companySlugService
     */
    public function __construct(
        private readonly PubSubService $pubSubService,
        private readonly CompanySlugService $companySlugService
    ){}

    /**
     * @param Company $company
     *
     * @return void
     */
    public function creating(Company $company): void
    {
        if (is_null($company->admin_status)) {
            $company->admin_status = CompanyAdminStatus::ADMIN_LOCKED;
        }

        if (is_null($company->consolidated_status)) {
            $company->consolidated_status = CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS;
        }
    }

    /**
     * @param Company $company
     * @return void
     * @throws RandomException
     */
    public function created(Company $company): void
    {
        if(CompanyConfiguration::query()->where(CompanyConfiguration::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})->doesntExist()) {
            CompanyConfiguration::create([
                CompanyConfiguration::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                CompanyConfiguration::FIELD_ALLOW_LEADS_NO_CC => false,
                CompanyConfiguration::FIELD_ENABLE_TCPA_PLAYBACK => false,
                CompanyConfiguration::FIELD_NEVER_EXCEED_BUDGET => false,
                CompanyConfiguration::FIELD_DISALLOW_RANKING => false,
                CompanyConfiguration::FIELD_RECEIVE_OFF_HOUR_LEADS => false,
                CompanyConfiguration::FIELD_APPOINTMENTS_ACTIVE => false,
                CompanyConfiguration::FIELD_REQUIRE_APPOINTMENTS_CALENDAR => false,
                CompanyConfiguration::FIELD_MISSED_PRODUCTS_ACTIVE => false,
                CompanyConfiguration::FIELD_REVIEWS_ENABLED => false,
            ]);
        }

        if ($company->{Company::FIELD_WEBSITE} !== '') {
            GetCompanyMetricsJob::dispatch(collect([$company]));
        }

        if (!$company->reference) {
            $company->saveReference();
        }

        if (!$company->uuid) {
            $company->saveUuid();
        }

        $this->companySlugService->setSlugForNewCompany($company, true);
        $company->recalculateCampaignStatus();
        $company->recalculateSystemStatus();

        $delayInSeconds = 60;
        CreateLegacyCompanyJob::dispatch($company)
            ->delay($delayInSeconds);
    }

    /**
     * @param Company $company
     * @return void
     * @throws BindingResolutionException
     */
    public function saved(Company $company): void
    {
        if (
            $company->isDirty(Company::FIELD_SYSTEM_STATUS)
            && in_array($company->system_status, [CompanySystemStatus::SUSPENDED_PAYMENT_METHOD, CompanySystemStatus::SUSPENDED_PAYMENT, CompanySystemStatus::SUSPENDED_CRM_REJECTION_THRESHOLD])
        ) {
            CompanySuspendedEvent::dispatch($company);
        }

        if (
            $company->isDirty(Company::FIELD_SYSTEM_STATUS) ||
            $company->isDirty(Company::FIELD_ADMIN_STATUS) ||
            $company->isDirty(Company::FIELD_CAMPAIGN_STATUS)
        ) {
            $originalConsolidated = $company->getOriginal(Company::FIELD_CONSOLIDATED_STATUS);

            $company->recalculateConsolidatedStatus();

            if ($originalConsolidated)
                CompanyChangedPurchasingStatusEvent::dispatch($company->id, $company->consolidated_status, $originalConsolidated);
        }
    }

    /**
     * @param Company $company
     *
     * @return void
     * @throws Exception
     */
    public function saving(Company $company): void
    {
        $company->{Company::FIELD_CAMPAIGNS_ARE_PARTIALLY_SUSPENDED} = $company->determineCampaignsArePartiallySuspended();

        if ($company->wasChanged(Company::FIELD_ADMIN_STATUS) && !in_array($company->admin_status->value, array_column(CompanyAdminStatus::cases(), 'value'))) {
            throw new Exception("Invalid admin status: {$company->admin_status->value}");
        }

        if ($company->wasChanged(Company::FIELD_CAMPAIGN_STATUS) && !in_array($company->campaign_status->value, array_column(CompanyCampaignStatus::cases(), 'value'))) {
            throw new Exception("Invalid campaign status: {$company->campaign_status->value}");
        }

        if ($company->wasChanged(Company::FIELD_SYSTEM_STATUS) && !in_array($company->system_status->value, array_column(CompanySystemStatus::cases(), 'value'))) {
            throw new Exception("Invalid system status: {$company->system_status->value}");
        }

        if ($company->wasChanged(Company::FIELD_CONSOLIDATED_STATUS) && !in_array($company->consolidated_status->value, [
                CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value,
                CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS->value
            ])) {
            throw new Exception("Invalid consolidated status: {$company->consolidated_status->value}");
        }
    }
}
