<?php

namespace App\Observers;

use App\Models\LeadProcessingCommunication;
use App\Repositories\LeadProcessing\LeadCommunicationRepository;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class LeadProcessingCommunicationObserver implements ShouldHandleEventsAfterCommit
{
    public $afterCommit = true;

    /**
     * @param LeadCommunicationRepository $leadCommunicationRepository
     */
    public function __construct(
        private readonly LeadCommunicationRepository $leadCommunicationRepository
    )
    {

    }

    /**
     * @param LeadProcessingCommunication $model
     * @return void
     */
    public function saved(LeadProcessingCommunication $model): void
    {
        /**
         * This will not cause the saved() handler to run again since updateMostRecentCommunication() uses
         * the query builder instead of calling the save() method on the model
         */
        $this->leadCommunicationRepository->updateMostRecentCommunication($model->consumer_product_id);
    }
}
