<?php

namespace App\Observers;

use App\Enums\ContactIdentification\SearchableFieldType;
use App\Jobs\ContactIdentification\ContactIdentificationSyncDataJob;
use App\Models\User;
use App\Models\UserPhone;

class UserObserver
{
    public bool $afterCommit = true;
    private const DELIMITER = '--';

    /**
     * Cascade the deletion of associated user rows.
     *
     * @param User $user
     * @return void
     */
    public function deleted(User $user): void
    {
        $user->accountManager?->delete();
        $user->successManager?->delete();
        $user->leadProcessor?->delete();
        $user->supportOfficer?->delete();
        $user->hunter?->delete();
        $user->teamLeaderRoles()->delete();
        $user->teamMemberRoles()->delete();
        $user->presets()->delete();
        UserPhone::query()->where(UserPhone::FIELD_USER_ID, $user->{User::FIELD_ID})->delete();
        //The user email column has a unique constraint -- appending the deleted at time to avoid violations
        $user->{User::FIELD_EMAIL} = time().self::DELIMITER.$user->{User::FIELD_EMAIL};
        $user->save();

        ContactIdentificationSyncDataJob::dispatch(ContactIdentificationSyncDataJob::OPERATION_DELETE, $user);
    }

    /**
     * @param User $user
     * @return void
     */
    public function restored(User $user): void
    {
        $user->accountManager()->withTrashed()->restore();
        $user->successManager()->withTrashed()->restore();
        $user->leadProcessor()->withTrashed()->restore();
        $user->supportOfficer()->withTrashed()->restore();
        $user->hunter()->withTrashed()->restore();
        $user->teamLeaderRoles()->withTrashed()->restore();
        $user->teamMemberRoles()->withTrashed()->restore();
        $user->presets()->withTrashed()->restore();
        UserPhone::withTrashed()
            ->where(UserPhone::FIELD_USER_ID, $user->{User::FIELD_ID})
            ->update([
                UserPhone::FIELD_DELETED_AT => null
            ]);

        $user->{User::FIELD_EMAIL} = explode(self::DELIMITER, $user->{User::FIELD_EMAIL})[1] ?? null;
        $user->save();
    }

    public function updated(User $user): void
    {
        if ($user->isDirty(User::FIELD_EMAIL)) {
            ContactIdentificationSyncDataJob::dispatch(
                ContactIdentificationSyncDataJob::OPERATION_SAVE,
                $user,
                User::FIELD_EMAIL,
                SearchableFieldType::EMAIL,
                $user->{User::FIELD_EMAIL}
            );
        }
    }
}
