<?php

namespace App\Observers;

use App\Enums\ActivityType;
use App\Enums\CommunicationRelationTypes;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Events\ConsumerProcessing\ConsumerProcessingActivityEvent;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ConsumerProduct;
use App\Models\Text;
use App\Models\User;
use App\Repositories\ActivityFeedRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class TextObserver
{

    public function __construct(
        protected ActivityFeedRepository $activityFeedRepository,
    ) {}

    public bool $afterCommit = true;

    /**
     * Handle the Text "created" event.
     *
     * @param Text  $text
     * @return void
     */
    public function created(Text $text): void
    {
        $this->dispatchConsumerProcessingEvent($text);
        $this->createCompanyActivity($text);
    }

    /**
     * @param Text $text
     * @return void
     */
    public function updated(Text $text): void
    {
        if ($text->wasChanged(Text::FIELD_RELATION_TYPE))
            $this->dispatchConsumerProcessingEvent($text);
    }

    /**
     * @param Text $text
     * @return void
     */
    private function dispatchConsumerProcessingEvent(Text $text): void
    {
        if ($text->relation_type === CommunicationRelationTypes::CONSUMER_PRODUCT->value) {
            $consumerProduct = ConsumerProduct::query()
                ->find($text->relation_id);

            if ($consumerProduct) {
                event(new ConsumerProcessingActivityEvent(
                        type: ConsumerProcessingActivityType::TEXT,
                        consumerProduct: $consumerProduct,
                        user: Auth::user(),
                        relatedActivity: $text
                ));
            }
        }
    }

    /**
     * Try to find a CompanyUser with a matching number to the Text model's outbound number
     * No consistency in storage of phone numbers, so a bit of regex faffing is required
     * @param Text $text
     * @return void
     */
    private function createCompanyActivity(Text $text): void
    {
        $targetNumber = preg_replace("/\D/", "", $text->{Text::FIELD_OTHER_NUMBER});
        if (preg_match("/\d{10,11}/", $targetNumber)) {
            $companyContact = CompanyUser::query()
                ->select([
                    CompanyUser::TABLE.'.'.CompanyUser::FIELD_ID,
                    CompanyUser::TABLE.'.'.CompanyUser::FIELD_COMPANY_ID
                ])
                ->with([CompanyUser::RELATION_COMPANY])
                ->where(DB::raw('REGEXP_REPLACE(TRIM('.CompanyUser::FIELD_CELL_PHONE.'), "^\+1|^1|[^0-9]*", "")'), $targetNumber)
                ->orWhere(DB::raw('REGEXP_REPLACE(TRIM('.CompanyUser::FIELD_OFFICE_PHONE.'), "^\+1|^1|[^0-9]*", "")'), $targetNumber)
                ->latest()
                ->first();

            if ($companyContact) {
                $company = $companyContact->{CompanyUser::RELATION_COMPANY};
                if ($company) {

                    $userId = $text->{Text::RELATION_PHONE}?->primaryUser()?->{User::FIELD_ID} ?? 0;
                    $newActivity = $this->activityFeedRepository->createActivity(
                        $text->{Text::FIELD_ID},
                        ActivityType::TEXT,
                        $company->{Company::FIELD_ID},
                        $userId,
                        $text->company_cadence_group_action_id,
                    );

                    if (!$newActivity) {
                        logger()->error("Error creating new ActivityFeed item from Text model - id '{$text->{Text::FIELD_ID}}'.");
                    }
                }
            }
        }
    }
}
