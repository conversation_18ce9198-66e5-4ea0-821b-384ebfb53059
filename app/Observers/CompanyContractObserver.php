<?php

namespace App\Observers;

use App\Events\CompanyContractSigned;
use App\Models\CompanyContract;

class CompanyContractObserver
{
    /**
     * Handle the CompanyContract "created" event.
     */
    public function created(CompanyContract $companyContract): void
    {
        //
    }

    /**
     * Handle the CompanyContract "updated" event.
     */
    public function updated(CompanyContract $companyContract): void
    {
        //signed
        if (!empty($companyContract->agreed_at)) {
            CompanyContractSigned::dispatch($companyContract->company_id);
        }
    }

    /**
     * Handle the CompanyContract "deleted" event.
     */
    public function deleted(CompanyContract $companyContract): void
    {
        //
    }

    /**
     * Handle the CompanyContract "restored" event.
     */
    public function restored(CompanyContract $companyContract): void
    {
        //
    }

    /**
     * Handle the CompanyContract "force deleted" event.
     */
    public function forceDeleted(CompanyContract $companyContract): void
    {
        //
    }
}
