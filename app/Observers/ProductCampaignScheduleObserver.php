<?php

namespace App\Observers;

use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignSchedule;
use App\Services\PubSub\PubSubService;
use App\Enums\EventCategory;
use App\Enums\EventName;

class ProductCampaignScheduleObserver
{
    /**
     * @param PubSubService $pubSubService
     */
    public function __construct(
        private readonly PubSubService $pubSubService
    )
    {

    }

    /**
     * @param ProductCampaignSchedule $productCampaignSchedule
     * @return void
     */
    public function saved(ProductCampaignSchedule $productCampaignSchedule): void
    {
        $this->pubSubService->handle(
            EventCategory::CAMPAIGNS->value,
            EventName::CAMPAIGN_SCHEDULE_UPDATED->value,
            [
                "product_campaign_id" => $productCampaignSchedule->{ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID},
                "schedule_id" => $productCampaignSchedule->{ProductCampaignSchedule::FIELD_SCHEDULE_ID}
            ]
        );
    }

    /**
     * @param ProductCampaignSchedule $productCampaignSchedule
     * @return void
     */
    public function deleted(ProductCampaignSchedule $productCampaignSchedule): void
    {
        $this->pubSubService->handle(
            EventCategory::CAMPAIGNS->value,
            EventName::CAMPAIGN_SCHEDULE_DELETED->value,
            [
                "product_campaign_id" => $productCampaignSchedule->{ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID},
                "schedule_id" => $productCampaignSchedule->{ProductCampaignSchedule::FIELD_SCHEDULE_ID}
            ]
        );
    }
}
