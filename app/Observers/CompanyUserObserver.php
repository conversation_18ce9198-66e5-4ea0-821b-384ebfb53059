<?php

namespace App\Observers;

use App\Enums\Odin\CompanyChangeLogType;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Odin\Company;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Jobs\ContactIdentification\ContactIdentificationSyncDataJob;
use App\Models\Odin\CompanyUser;
use App\Services\Odin\CompanyChangeLogService;
use Exception;
use Illuminate\Support\Facades\Auth;

class CompanyUserObserver
{
    public $afterCommit = true;

    /**
     * @param CompanyChangeLogService $companyChangeLogService
     */
    public function __construct(
        private readonly CompanyChangeLogService $companyChangeLogService
    )
    {

    }

    /**
     * @param  CompanyUser  $companyUser
     * @return void
     */
    public function creating(CompanyUser $companyUser): void
    {
        $loggedInUserId = Auth::id();
        $companyUser->{CompanyUser::FIELD_CREATED_BY_ID} = $loggedInUserId;
    }

    /**
     * @param CompanyUser $companyUser
     * @return void
     */
    public function updating(CompanyUser $companyUser): void
    {
        if (!$companyUser->wasRecentlyCreated) {
            $loggedInUserId = Auth::id();
            $companyUser->{CompanyUser::FIELD_UPDATED_BY_ID} = $loggedInUserId;
        }
    }

    public function updated(CompanyUser $companyUser): void
    {
        if ($companyUser->isDirty(CompanyUser::FIELD_EMAIL)) {
            ContactIdentificationSyncDataJob::dispatch(
                ContactIdentificationSyncDataJob::OPERATION_SAVE,
                $companyUser,
                CompanyUser::FIELD_EMAIL,
                SearchableFieldType::EMAIL,
                $companyUser->{CompanyUser::FIELD_EMAIL}
            );
        }
    }

    /**
     * @param CompanyUser $companyUser
     *
     * @return void
     * @throws Exception
     */
    public function saving(CompanyUser $companyUser): void
    {
        if ($companyUser->isDirty(CompanyUser::FIELD_STATUS) && $companyUser->status === CompanyUser::STATUS_INACTIVE && $companyUser->isCampaignDeliveryContact()) {
            throw new Exception('Cannot inactivate the Company User as they are configured as a campaign delivery contact');
        }

        if($companyUser->isDirty(CompanyUser::FIELD_IS_DECISION_MAKER)) {
            $hasDecisionMaker = $companyUser
                    ->{CompanyUser::RELATION_COMPANY}
                    ?->{Company::RELATION_USERS}
                    ?->where(CompanyUser::FIELD_IS_DECISION_MAKER, true)
                    ?->count() > 0;

            $hasDecisionMaker = $hasDecisionMaker || $companyUser->{CompanyUser::FIELD_IS_DECISION_MAKER};

            $this->companyChangeLogService->log(
                $companyUser->{CompanyUser::RELATION_COMPANY},
                CompanyChangeLogType::DECISION_MAKER_CHANGE,
                [
                    'has_decision_maker' => $hasDecisionMaker
                ]
            );
        }
    }

    public function deleted(CompanyUser $companyUser): void
    {
        ContactIdentificationSyncDataJob::dispatch(ContactIdentificationSyncDataJob::OPERATION_DELETE, $companyUser);
        CompanyCampaignDeliveryModuleContact::query()->where(CompanyCampaignDeliveryModuleContact::FIELD_CONTACT_ID, $companyUser->id)->delete();
    }

    /**
     * @param CompanyUser $companyUser
     *
     * @return void
     * @throws Exception
     */
    public function deleting(CompanyUser $companyUser): void
    {
        if ($companyUser->isCampaignDeliveryContact()) {
            throw new Exception('Cannot delete the Company User as they are configured as a campaign delivery contact');
        }
    }
}
