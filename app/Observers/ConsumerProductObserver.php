<?php

namespace App\Observers;


use App\Jobs\RecordMonitoringLog;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\LogMonitoringRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use Exception;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;
use Illuminate\Support\Facades\App;

class ConsumerProductObserver implements ShouldHandleEventsAfterCommit
{

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public function created(ConsumerProduct $consumerProduct): void
    {
        ConsumerProductLifecycleTrackingService::created($consumerProduct);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public function updated(ConsumerProduct $consumerProduct): void
    {
        if($consumerProduct->wasChanged(ConsumerProduct::FIELD_STATUS)){
            ConsumerProductLifecycleTrackingService::statusUpdated($consumerProduct);
        }
        if ($consumerProduct->wasChanged([ConsumerProduct::FIELD_GOOD_TO_SELL, ConsumerProduct::FIELD_STATUS])
            && $consumerProduct->consumer->hasSecondaryServices()) {
            $this->syncSecondaryProducts($consumerProduct);
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public function saving(ConsumerProduct $consumerProduct)
    {
        $id = $consumerProduct->{ConsumerProduct::FIELD_ID} ?? "new";

        $changes = $consumerProduct->getOriginal();

        $this->writeLog(
            "Saving consumer product: $id",
            [
                "new" => [
                    ConsumerProduct::FIELD_ID => $consumerProduct->{ConsumerProduct::FIELD_ID} ?? 0,
                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID} ?? 0
                ],
                "old" => [
                    ConsumerProduct::FIELD_ID => $consumerProduct->{ConsumerProduct::FIELD_ID} ?? 0,
                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $changes[ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID] ?? $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID} ?? 0
                ],
                "changed" => [
                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => ($changes[ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID] ?? 0) != ($consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID} ?? 0)
                ]
            ]
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public function saved(ConsumerProduct $consumerProduct)
    {
        $id = $consumerProduct->{ConsumerProduct::FIELD_ID} ?? "new";

        $this->writeLog(
            "Saved consumer product: $id",
            [
                ConsumerProduct::FIELD_ID => $consumerProduct->{ConsumerProduct::FIELD_ID} ?? 0,
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $consumerProduct->{ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID} ?? 0
            ]
        );
    }

    /**
     * @return string
     */
    private function getTrace(): string
    {
        ob_start();

        debug_print_backtrace();

        $trace = ob_get_contents();

        ob_end_clean();

        return base64_encode(gzcompress($trace, 9));
    }

    /**
     * @param string $message
     * @param array|null $payload
     * @return void
     */
    private function writeLog(string $message, ?array $payload = []): void
    {
        try {
            $payload['env'] = App::environment();
            $payload['trace'] = $this->getTrace();

            RecordMonitoringLog::dispatch($message, $payload, LogMonitoringRepository::LOGGER_MODEL_EVENTS);
        }
        catch(Exception $e) {
            logger()->error($e->getMessage());
        }
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    private function syncSecondaryProducts(ConsumerProduct $consumerProduct): void
    {
        $consumerProduct->consumer->consumerProducts()
            ->where(ConsumerProduct::FIELD_IS_SECONDARY_SERVICE, true)
            ->update([
                ConsumerProduct::FIELD_STATUS       => $consumerProduct->status,
                ConsumerProduct::FIELD_GOOD_TO_SELL => $consumerProduct->good_to_sell
            ]);
    }
}
