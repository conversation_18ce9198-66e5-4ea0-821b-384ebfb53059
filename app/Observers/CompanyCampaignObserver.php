<?php

namespace App\Observers;

use App\Enums\Campaigns\CampaignStatus;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Jobs\Billing\AttachCampaignToBillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use App\Repositories\MissedProducts\MissedProductReasonEventRepository;
use App\Services\Companies\CompanyService;
use Illuminate\Contracts\Events\ShouldHandleEventsAfterCommit;

class CompanyCampaignObserver implements ShouldHandleEventsAfterCommit
{
    public function __construct(
        protected CompanyService $companyService
    )
    {
    }

    /**
     * @param CompanyCampaign $campaign
     * @return void
     */
    public function saved(CompanyCampaign $campaign): void
    {
        if ($campaign->isDirty(CompanyCampaign::FIELD_STATUS)) {
            $campaign->company->recalculateCampaignStatus();
        }

        if ($campaign->wasChanged([CompanyCampaign::FIELD_STATUS])) {
            $event = $campaign->status === CampaignStatus::ACTIVE
                ? MissedProductReasonEventType::CAMPAIGN_ACTIVATED
                : MissedProductReasonEventType::CAMPAIGN_PAUSED;
            /** @var MissedProductReasonEventRepository $eventService */
            $eventService = app(MissedProductReasonEventRepository::class);
            $eventService->handleNewCampaignEvent($campaign, $event);
        }
    }

    public function created(CompanyCampaign $campaign): void
    {
        AttachCampaignToBillingProfile::dispatch($campaign->id);
    }

    /**
     * @param CompanyCampaign $campaign
     * @return void
     */
    public function deleted(CompanyCampaign $campaign): void
    {
        $campaign->company->recalculateCampaignStatus();
    }

    /**
     * @param CompanyCampaign $campaign
     * @return void
     */
    public function restored(CompanyCampaign $campaign): void
    {
        $campaign->company->recalculateCampaignStatus();
    }
}
