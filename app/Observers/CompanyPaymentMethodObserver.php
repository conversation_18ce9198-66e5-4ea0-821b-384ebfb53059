<?php

namespace App\Observers;

use App\Enums\Billing\PaymentMethodServices;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Billing\CompanyPaymentMethod;

class CompanyPaymentMethodObserver
{
    /**
     * Handle the CompanyPaymentMethod "saved" event.
     */
    public function saved(CompanyPaymentMethod $companyPaymentMethod): void
    {
        $companyPaymentMethod->company->recalculateSystemStatus();
    }

    /**
     * Handle the CompanyPaymentMethod "created" event.
     */
    public function created(CompanyPaymentMethod $companyPaymentMethod): void
    {
        if ($companyPaymentMethod->type === PaymentMethodServices::STRIPE->value) {
            DispatchPubSubEvent::dispatch(EventCategory::BILLING, EventName::CARD_ADDED, [
                'company_id' => $companyPaymentMethod->company->id,
                'company_reference' => $companyPaymentMethod->company->reference
            ]);
        }
    }

    /**
     * Handle the CompanyPaymentMethod "updated" event.
     */
    public function updated(CompanyPaymentMethod $companyPaymentMethod): void
    {
        //
    }

    /**
     * Handle the CompanyPaymentMethod "deleted" event.
     */
    public function deleted(CompanyPaymentMethod $companyPaymentMethod): void
    {
        $companyPaymentMethod->company->recalculateSystemStatus();
    }

    /**
     * Handle the CompanyPaymentMethod "restored" event.
     */
    public function restored(CompanyPaymentMethod $companyPaymentMethod): void
    {
        $companyPaymentMethod->company->recalculateSystemStatus();
    }

    /**
     * Handle the CompanyPaymentMethod "force deleted" event.
     */
    public function forceDeleted(CompanyPaymentMethod $companyPaymentMethod): void
    {
        //
    }
}
