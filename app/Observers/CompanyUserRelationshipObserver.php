<?php

namespace App\Observers;

use App\Models\CompanyUserRelationship;

class CompanyUserRelationshipObserver
{
    /**
     * Handle the CompanyUserRelationship "created" event.
     */
    public function created(CompanyUserRelationship $companyUserRelationship): void
    {
        $companyUserRelationship->setCommissionableAt();
    }

    /**
     * Handle the CompanyUserRelationship "updated" event.
     */
    public function updated(CompanyUserRelationship $companyUserRelationship): void
    {
        //
    }

    /**
     * Handle the CompanyUserRelationship "deleted" event.
     */
    public function deleted(CompanyUserRelationship $companyUserRelationship): void
    {
        if (!$companyUserRelationship->commissionable_to) {
            CompanyUserRelationship::withTrashed()
                ->find($companyUserRelationship->id)
                ->update([
                    CompanyUserRelationship::FIELD_COMMISIONABLE_TO => $companyUserRelationship->deleted_at
                ]);
        }
    }

    /**
     * Handle the CompanyUserRelationship "restored" event.
     */
    public function restored(CompanyUserRelationship $companyUserRelationship): void
    {
        //
    }

    /**
     * Handle the CompanyUserRelationship "force deleted" event.
     */
    public function forceDeleted(CompanyUserRelationship $companyUserRelationship): void
    {
        //
    }
}
