<?php

namespace App\Observers;

use App\Models\Odin\ProductCampaign;
use App\Services\PubSub\PubSubService;
use App\Enums\EventCategory;
use App\Enums\EventName;

class ProductCampaignObserver
{
    /**
     * @param PubSubService $pubSubService
     */
    public function __construct(
        private readonly PubSubService $pubSubService
    )
    {

    }

    /**
     * @param ProductCampaign $productCampaign
     * @return void
     */
    public function saved(ProductCampaign $productCampaign): void
    {
        $changes = $productCampaign->getChanges();

        if(!empty(array_intersect(
            array_keys($changes),
            [
                ProductCampaign::FIELD_STATUS
            ]
        ))) {
            $this->pubSubService->handle(
                EventCategory::CAMPAIGNS->value,
                EventName::PRODUCT_CAMPAIGN_UPDATED->value,
                [
                    "product_campaign_id" => $productCampaign->{ProductCampaign::FIELD_ID},
                    "new_status" => $productCampaign->{ProductCampaign::FIELD_STATUS}
                ]
            );
        }
    }
}
