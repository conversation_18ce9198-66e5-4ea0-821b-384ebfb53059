<?php

namespace App\Observers;

use App\Enums\EventType;
use App\Models\Odin\ProductCampaignBudget;
use App\Services\PubSub\PubSubService;
use App\Enums\EventCategory;
use App\Enums\EventName;

class ProductCampaignBudgetObserver
{
    /**
     * @param PubSubService $pubSubService
     */
    public function __construct(
        private readonly PubSubService $pubSubService
    )
    {

    }

    /**
     * @param ProductCampaignBudget $productCampaignBudget
     * @return void
     */
    public function saved(ProductCampaignBudget $productCampaignBudget): void
    {
        $changes = $productCampaignBudget->getChanges();

        if(!empty(array_intersect(
            array_keys($changes),
            [
                ProductCampaignBudget::FIELD_VALUE,
                ProductCampaignBudget::FIELD_VALUE_TYPE,
                ProductCampaignBudget::FIELD_STATUS,
                ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP,
                ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE
            ]
        ))) {
            $this->pubSubService->handle(
                EventCategory::CAMPAIGNS->value,
                EventName::PRODUCT_CAMPAIGN_BUDGET_UPDATED->value,
                [
                    "product_campaign_budget_id" => $productCampaignBudget->{ProductCampaignBudget::FIELD_ID},
                    ProductCampaignBudget::FIELD_VALUE => $productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE},
                    ProductCampaignBudget::FIELD_VALUE_TYPE => $productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE_TYPE},
                    ProductCampaignBudget::FIELD_STATUS => $productCampaignBudget->{ProductCampaignBudget::FIELD_STATUS},
                    ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP => $productCampaignBudget->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP}->toString(),
                    ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE => $productCampaignBudget->{ProductCampaignBudget::FIELD_MAX_BUDGET_USAGE}
                ]
            );
        }
    }
}
