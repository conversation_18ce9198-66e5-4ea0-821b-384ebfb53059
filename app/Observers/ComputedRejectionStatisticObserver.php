<?php

namespace App\Observers;

use App\Models\ComputedRejectionStatistic;

class ComputedRejectionStatisticObserver
{
    /**
     * Handle the ComputedRejectionStatistic "created" event.
     */
    public function created(ComputedRejectionStatistic $computedRejectionStatistic): void
    {
        //
    }

    /**
     * Handle the ComputedRejectionStatistic "updated" event.
     */
    public function updated(ComputedRejectionStatistic $computedRejectionStatistic): void
    {
        //
    }

    /**
     * Handle the ComputedRejectionStatistic "saved" event.
     */
    public function saved(ComputedRejectionStatistic $computedRejectionStatistic): void
    {
        if ($computedRejectionStatistic->isDirty(ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE)) {
            $computedRejectionStatistic->company->recalculateSystemStatus();
        }
    }

    /**
     * Handle the ComputedRejectionStatistic "deleted" event.
     */
    public function deleted(ComputedRejectionStatistic $computedRejectionStatistic): void
    {
        //
    }

    /**
     * Handle the ComputedRejectionStatistic "restored" event.
     */
    public function restored(ComputedRejectionStatistic $computedRejectionStatistic): void
    {
        //
    }

    /**
     * Handle the ComputedRejectionStatistic "force deleted" event.
     */
    public function forceDeleted(ComputedRejectionStatistic $computedRejectionStatistic): void
    {
        //
    }
}
