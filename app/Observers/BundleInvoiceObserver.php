<?php

namespace App\Observers;

use App\Models\BundleInvoice;
use App\Models\User;
use App\Repositories\BundleManagement\BundleInvoiceHistoryRepository;
use Carbon\Carbon;
use DateTime;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Support\Str;

class BundleInvoiceObserver
{
    public function __construct(
        protected BundleInvoiceHistoryRepository $bundleInvoiceHistoryRepository,
    ) {}

    public bool $afterCommit = true;

    /**
     * Handle the BundleInvoice "created" event.
     *
     * @param BundleInvoice $bundleInvoice
     * @return void
     */
    public function created(BundleInvoice $bundleInvoice): void
    {
        $this->createInvoiceHistoryRecord($bundleInvoice);
    }

    /**
     * Handle the BundleInvoice "updated" event.
     *
     * @param BundleInvoice $bundleInvoice
     * @return void
     */
    public function updated(BundleInvoice $bundleInvoice): void
    {
        $this->createInvoiceHistoryRecord($bundleInvoice);
    }

    /**
     * @param BundleInvoice $bundleInvoice
     * @return void
     */
    protected function createInvoiceHistoryRecord(BundleInvoice $bundleInvoice): void
    {
        $newInvoice = $bundleInvoice->wasRecentlyCreated;
        $starterMessage = $newInvoice ? 'Invoice created with fields: ' : 'Invoice fields were updated: ';
        $historyDescription = [$starterMessage];

        foreach ($bundleInvoice->getChanges() as $field => $value) {

            /**
             *  Change any user relationship fields to camelcase and
             *  update the $value to be their name instead of ID
             */
            $camelField = Str::camel($field);
            if (isset($bundleInvoice->$camelField) && $bundleInvoice->$camelField instanceof User) {
                $value = $bundleInvoice->$camelField?->name;
            }
            if ($value instanceof DateTime) {
                $value = Carbon::parse($value)->format(DateTime::RFC7231);
            }

            $historyDescription[] = sprintf(
                '%s: "%s"',
                ucwords(str_ireplace('_', ' ', $field)),
                $field === BundleInvoice::FIELD_STATUS ? $bundleInvoice->status->getStatusString() : $value
            );
        }

        $this->bundleInvoiceHistoryRepository->createInvoiceHistory(
            $bundleInvoice->id,
            implode('<br>', $historyDescription)
        );
    }
}
