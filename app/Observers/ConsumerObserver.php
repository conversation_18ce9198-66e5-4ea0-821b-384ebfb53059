<?php

namespace App\Observers;

use App\Enums\ContactIdentification\SearchableFieldType;
use App\Jobs\ContactIdentification\ContactIdentificationSyncDataJob;
use App\Models\Odin\Consumer;

class ConsumerObserver
{

    /**
     * Handle "created" event
     *
     * @param Consumer $consumer
     *
     * @return void
     */
    public function created(Consumer $consumer): void
    {
        if (!$consumer->auth) {
            $consumer->auth()->create();
        }
    }

    public function updated(Consumer $consumer): void
    {
        if ($consumer->isDirty(Consumer::FIELD_EMAIL)) {
            ContactIdentificationSyncDataJob::dispatch(
                ContactIdentificationSyncDataJob::OPERATION_SAVE,
                $consumer,
                Consumer::FIELD_EMAIL,
                SearchableFieldType::EMAIL,
                $consumer->{Consumer::FIELD_EMAIL}
            );
        }
    }

    public function deleted(Consumer $consumer): void
    {
        ContactIdentificationSyncDataJob::dispatch(ContactIdentificationSyncDataJob::OPERATION_DELETE, $consumer);
    }
}
