<?php

namespace App\Aggregates;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Events\Billing\StoredEvents\Credit\CreditAppliedToInvoice;
use App\Models\Billing\Credit;
use App\Models\Billing\Invoice;
use App\Repositories\Credit\CreditRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Spatie\EventSourcing\AggregateRoots\AggregatePartial;
use Spatie\EventSourcing\AggregateRoots\AggregateRoot;

class InvoiceCreditPartialAggregate extends AggregatePartial
{
    protected CreditRepository $creditRepository;

    /**
     * @param AggregateRoot $aggregateRoot
     * @throws BindingResolutionException
     */
    public function __construct(
        AggregateRoot $aggregateRoot
    )
    {
        parent::__construct($aggregateRoot);

        $this->creditRepository = app()->make(CreditRepository::class);
    }

    /**
     * @param Invoice $invoice
     * @param Carbon $expireDateReference
     * @param int $amountToApply
     * @param string $authorType
     * @param int|null $authorId
     * @param string|null $creditType
     * @return int
     */
    public function checkAvailableAndApplyCreditsToInvoice(
        Invoice $invoice,
        Carbon $expireDateReference,
        int $amountToApply,
        string $authorType,
        ?int $authorId = null,
        ?string $creditType = null,
    ): int
    {
        $credits = $this->creditRepository->getEligibleCompanyCredits(
            companyId          : $invoice->{Invoice::FIELD_COMPANY_ID},
            billingProfileId   : $invoice->{Invoice::FIELD_BILLING_PROFILE_ID},
            type               : $creditType,
            expireDateReference: $expireDateReference
        );

        foreach ($credits as $credit) {
            if (empty($amountToApply)) continue;

            $creditUsed = $credit->{Credit::FIELD_REMAINING_VALUE};

            if ($credit->{Credit::FIELD_REMAINING_VALUE} > $amountToApply) {
                $creditUsed = $amountToApply;
            }

            $this->applyCreditToInvoice(
                creditUuid : $credit->uuid,
                invoiceUuid: $invoice->uuid,
                companyId  : $invoice->company->id,
                amount     : $creditUsed,
                creditType : $credit->{Credit::FIELD_CREDIT_TYPE},
                authorType : $authorType,
                authorId   : $authorId,
            );

            $amountToApply = $amountToApply - $creditUsed;
        }

        return $amountToApply;
    }

    /**
     * @param string $creditUuid
     * @param string $invoiceUuid
     * @param int $companyId
     * @param float $amount
     * @param string $creditType
     * @param string $authorType
     * @param string|null $notes
     * @param int|null $authorId
     * @return $this
     */
    public function applyCreditToInvoice(
        string $creditUuid,
        string $invoiceUuid,
        int $companyId,
        float $amount,
        string $creditType,
        string $authorType,
        ?string $notes = null,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new CreditAppliedToInvoice(
            creditUuid : $creditUuid,
            invoiceUuid: $invoiceUuid,
            companyId  : $companyId,
            amount     : $amount,
            creditType : $creditType,
            authorType : $authorType,
            authorId   : $authorId,
            notes      : $notes
        ));

        return $this;
    }
}
