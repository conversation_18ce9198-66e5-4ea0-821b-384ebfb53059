<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\Invoice\WriteOff\InvoiceWrittenOff;
use Spatie\EventSourcing\AggregateRoots\AggregatePartial;

class InvoiceWriteOffsPartialAggregate extends AggregatePartial
{
    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param string $authorType
     * @param string $date
     * @param float $amount
     * @param int|null $authorId
     * @return InvoiceWriteOffsPartialAggregate
     */
    public function writeOffInvoice(
        string $uuid,
        string $invoiceUuid,
        string $authorType,
        string $date,
        float $amount,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new InvoiceWrittenOff(
            invoiceUuid: $invoiceUuid,
            uuid       : $uuid,
            date       : $date,
            amount     : $amount,
            authorType : $authorType,
            authorId   : $authorId,
        ));

        return $this;
    }
}
