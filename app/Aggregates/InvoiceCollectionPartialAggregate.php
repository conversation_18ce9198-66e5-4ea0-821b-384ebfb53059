<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\Invoice\Collections\IssueInvoiceToCollections;
use App\Events\Billing\StoredEvents\Invoice\Collections\UpdateInvoiceCollections;
use Spatie\EventSourcing\AggregateRoots\AggregatePartial;

class InvoiceCollectionPartialAggregate extends AggregatePartial
{
    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param string $authorType
     * @param string $sentDate
     * @param string $recoveryStatus
     * @param int $amountCollected
     * @param int|null $authorId
     * @return InvoiceCollectionPartialAggregate
     */
    public function issueInvoiceToCollections(
        string $uuid,
        string $invoiceUuid,
        string $authorType,
        string $sentDate,
        string $recoveryStatus,
        int $amountCollected,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new IssueInvoiceToCollections(
            invoiceUuid    : $invoiceUuid,
            uuid           : $uuid,
            authorType     : $authorType,
            sentDate       : $sentDate,
            recoveryStatus : $recoveryStatus,
            amountCollected: $amountCollected,
            authorId       : $authorId,
        ));

        return $this;
    }

    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param string $authorType
     * @param string $recoveryStatus
     * @param int $amountRecovered
     * @param int $amountLost
     * @param string|null $recoveryDate
     * @param int|null $authorId
     * @return InvoiceCollectionPartialAggregate
     */
    public function updateInvoiceCollections(
        string $uuid,
        string $invoiceUuid,
        string $authorType,
        string $recoveryStatus,
        int $amountRecovered,
        int $amountLost,
        ?string $recoveryDate = null,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new UpdateInvoiceCollections(
            invoiceUuid    : $invoiceUuid,
            uuid           : $uuid,
            authorType     : $authorType,
            recoveryStatus : $recoveryStatus,
            amountRecovered: $amountRecovered,
            amountLost     : $amountLost,
            recoveryDate   : $recoveryDate,
            authorId       : $authorId,
        ));

        return $this;
    }
}
