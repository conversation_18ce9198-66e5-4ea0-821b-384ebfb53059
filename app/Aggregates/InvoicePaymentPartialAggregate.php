<?php

namespace App\Aggregates;

use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionScope;
use App\Enums\Billing\InvoiceTransactionType;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeClosed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeCreated;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeFailed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequest;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestAttempted;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestFailed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestMaxAttemptsExceeded;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestSuccess;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeScheduled;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeSuccess;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentCanceled;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentDue;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentFailed;
use App\Exceptions\Billing\InvoicePaymentOverchargeException;
use App\Services\Billing\InvoicePaymentService;
use Exception;
use Illuminate\Support\Collection;
use Spatie\EventSourcing\AggregateRoots\AggregatePartial;
use Spatie\EventSourcing\AggregateRoots\AggregateRoot;

class InvoicePaymentPartialAggregate extends AggregatePartial
{
    protected InvoicePaymentService $invoicePaymentService;

    public function __construct(AggregateRoot $aggregateRoot)
    {
        parent::__construct($aggregateRoot);
        $this->invoicePaymentService = app()->make(InvoicePaymentService::class);
    }

    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param int $total
     * @param int $maxAttempts
     * @param int $billingProfileId
     * @param string $authorType
     * @param string|null $invoicePaymentDate
     * @param int|null $authorId
     * @param string|null $attemptChargeDate
     * @param Collection $paymentMethodsAttempts
     * @return InvoicePaymentPartialAggregate
     * @throws InvoicePaymentOverchargeException
     */
    public function requestInvoiceCharge(
        string $uuid,
        string $invoiceUuid,
        int $total,
        int $maxAttempts,
        int $billingProfileId,
        string $authorType,
        ?string $invoicePaymentDate = null,
        ?int $authorId = null,
        ?string $attemptChargeDate = null,
        Collection $paymentMethodsAttempts = new Collection()
    ): self
    {
        if (empty($invoicePaymentDate)) {
            $invoicePaymentDate = now()->toISOString();
        }

        $this->invoicePaymentService->validatePaymentForChargeAttempt(
            invoiceUuid       : $invoiceUuid,
            invoicePaymentUuid: $uuid,
            total             : $total,
        );

        $this->recordThat(new InvoiceChargeRequest(
            uuid                  : $uuid,
            invoiceUuid           : $invoiceUuid,
            total                 : $total,
            maxAttempts           : $maxAttempts,
            billingProfileId      : $billingProfileId,
            authorType            : $authorType,
            invoicePaymentDate    : $invoicePaymentDate,
            authorId              : $authorId,
            attemptChargeDate     : $attemptChargeDate,
            paymentMethodsAttempts: $paymentMethodsAttempts,
        ));

        return $this;
    }

    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param int $total
     * @param int $maxAttempts
     * @param int $billingProfileId
     * @param string $authorType
     * @param int|null $authorId
     * @param string|null $attemptChargeDate
     * @param Collection $paymentMethodsAttempts
     * @return $this
     */
    public function scheduleInvoiceCharge(
        string $uuid,
        string $invoiceUuid,
        int $total,
        int $maxAttempts,
        int $billingProfileId,
        string $authorType,
        ?int $authorId = null,
        ?string $attemptChargeDate = null,
        Collection $paymentMethodsAttempts = new Collection()
    ): self
    {
        $this->recordThat(new InvoiceChargeScheduled(
            uuid                  : $uuid,
            invoiceUuid           : $invoiceUuid,
            total                 : $total,
            maxAttempts           : $maxAttempts,
            billingProfileId      : $billingProfileId,
            authorType            : $authorType,
            authorId              : $authorId,
            attemptChargeDate     : $attemptChargeDate,
            paymentMethodsAttempts: $paymentMethodsAttempts,
        ));

        return $this;
    }


    /**
     * @param string $invoicePaymentUuid
     * @param string $invoicePaymentChargeUuid
     * @param string $invoiceUuid
     * @param string $authorType
     * @param string $paymentMethodType
     * @param int $paymentMethodId
     * @param int $amount
     * @param string $attemptedAt
     * @param int|null $authorId
     * @return $this
     */
    public function invoiceChargeRequestAttempt(
        string $invoicePaymentUuid,
        string $invoicePaymentChargeUuid,
        string $invoiceUuid,
        string $authorType,
        string $paymentMethodType,
        int $paymentMethodId,
        int $amount,
        string $attemptedAt,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new InvoiceChargeRequestAttempted(
            invoicePaymentUuid      : $invoicePaymentUuid,
            invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
            invoiceUuid             : $invoiceUuid,
            authorType              : $authorType,
            paymentMethodType       : $paymentMethodType,
            paymentMethodId         : $paymentMethodId,
            amount                  : $amount,
            attemptedAt             : $attemptedAt,
            authorId                : $authorId
        ));

        return $this;
    }

    /**
     * @param string $invoicePaymentChargeUuid
     * @param string $invoicePaymentUuid
     * @param string $invoiceUuid
     * @param string $authorType
     * @param string $paymentMethodType
     * @param int $paymentMethodId
     * @param int $amount
     * @param string $requestedAt
     * @param int|null $authorId
     * @return $this
     */
    public function invoiceChargeRequestSuccess(
        string $invoicePaymentChargeUuid,
        string $invoicePaymentUuid,
        string $invoiceUuid,
        string $authorType,
        string $paymentMethodType,
        int $paymentMethodId,
        int $amount,
        string $requestedAt,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new InvoiceChargeRequestSuccess(
            invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
            invoicePaymentUuid      : $invoicePaymentUuid,
            invoiceUuid             : $invoiceUuid,
            authorType              : $authorType,
            paymentMethodType       : $paymentMethodType,
            paymentMethodId         : $paymentMethodId,
            amount                  : $amount,
            requestedAt             : $requestedAt,
            authorId                : $authorId,
        ));

        return $this;
    }

    /**
     * @param string $invoicePaymentChargeUuid
     * @param string $invoicePaymentUuid
     * @param string $invoiceUuid
     * @param string $errorMessage
     * @param string $errorCode
     * @param string $authorType
     * @param string $paymentMethodType
     * @param int $paymentMethodId
     * @param int $amount
     * @param string $failedAt
     * @param int|null $authorId
     * @return $this
     */
    public function invoiceChargeRequestFailed(
        string $invoicePaymentChargeUuid,
        string $invoicePaymentUuid,
        string $invoiceUuid,
        string $errorMessage,
        string $errorCode,
        string $authorType,
        string $paymentMethodType,
        int $paymentMethodId,
        int $amount,
        string $failedAt,
        ?int $authorId = null
    ): self
    {
        $this->recordThat(new InvoiceChargeRequestFailed(
            invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
            invoicePaymentUuid      : $invoicePaymentUuid,
            invoiceUuid             : $invoiceUuid,
            errorMessage            : $errorMessage,
            errorCode               : $errorCode,
            authorType              : $authorType,
            paymentMethodType       : $paymentMethodType,
            paymentMethodId         : $paymentMethodId,
            amount                  : $amount,
            failedAt                : $failedAt,
            authorId                : $authorId
        ));

        return $this;
    }

    /**
     * @param string $invoicePaymentChargeUuid
     * @param string $invoicePaymentUuid
     * @param string $invoiceUuid
     * @param string $paymentMethodType
     * @param int $paymentMethodId
     * @param int $amount
     * @param string $exceededAt
     * @return $this
     */
    public function invoiceChargeRequestMaxAttemptsExceeded(
        string $invoicePaymentChargeUuid,
        string $invoicePaymentUuid,
        string $invoiceUuid,
        string $paymentMethodType,
        int $paymentMethodId,
        int $amount,
        string $exceededAt
    ): self
    {
        $this->recordThat(new InvoiceChargeRequestMaxAttemptsExceeded(
            invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
            invoicePaymentUuid      : $invoicePaymentUuid,
            invoiceUuid             : $invoiceUuid,
            paymentMethodType       : $paymentMethodType,
            paymentMethodId         : $paymentMethodId,
            amount                  : $amount,
            exceededAt              : $exceededAt
        ));

        return $this;
    }


    /**
     * @param string $externalTransactionId
     * @param string $invoiceUuid
     * @param int $amount
     * @param string $currency
     * @param string $source
     * @param InvoiceTransactionType $type
     * @param string $date
     * @param InvoiceTransactionScenario|null $scenario
     * @param InvoiceTransactionScope|null $scope
     * @param string|null $invoicePaymentChargeUuid
     * @param string|null $externalPaymentMethodId
     * @return $this
     * @throws Exception
     */
    public function invoiceChargeSuccess(
        string $externalTransactionId,
        string $invoiceUuid,
        int $amount,
        string $currency,
        string $source,
        InvoiceTransactionType $type,
        string $date,
        ?InvoiceTransactionScenario $scenario = null,
        ?InvoiceTransactionScope $scope = null,
        ?string $invoicePaymentChargeUuid = null,
        ?string $externalPaymentMethodId = null,
    ): self
    {
        if ($type !== InvoiceTransactionType::PAYMENT) {
            throw new Exception('Scenario should be either full or partial payment');
        }

        $this->recordThat(
            new InvoiceChargeSuccess(
                externalTransactionId   : $externalTransactionId,
                invoiceUuid             : $invoiceUuid,
                amount                  : $amount,
                currency                : $currency,
                source                  : $source,
                type                    : $type->value,
                date                    : $date,
                scenario                : $scenario?->value,
                scope                   : $scope?->value,
                invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
                externalPaymentMethodId : $externalPaymentMethodId
            )
        );

        return $this;
    }

    /**
     * @param string $externalTransactionId
     * @param string $invoiceUuid
     * @param string $errorMessage
     * @param int $amount
     * @param string $currency
     * @param string $source
     * @return $this
     */
    public function invoiceChargeFailed(
        string $externalTransactionId,
        string $invoiceUuid,
        string $errorMessage,
        int $amount,
        string $currency,
        string $source,
    ): self
    {
        $this->recordThat(new InvoiceChargeFailed(
            externalTransactionId: $externalTransactionId,
            invoiceUuid          : $invoiceUuid,
            errorMessage         : $errorMessage,
            amount               : $amount,
            currency             : $currency,
            source               : $source,
        ));

        return $this;
    }


    /**
     * @param string $externalTransactionId
     * @param string $chargeId
     * @param int $amount
     * @param string $reason
     * @param string $status
     * @param string $currency
     * @param string $source
     * @return InvoicePaymentPartialAggregate
     */
    public function openChargeDispute(
        string $externalTransactionId,
        string $chargeId,
        int $amount,
        string $reason,
        string $status,
        string $currency,
        string $source,
    ): self
    {
        $this->recordThat(new InvoiceChargeDisputeCreated(
            externalTransactionId: $externalTransactionId,
            chargeId             : $chargeId,
            amount               : $amount,
            reason               : $reason,
            status               : $status,
            currency             : $currency,
            source               : $source,
        ));

        return $this;
    }

    /**
     * @param string $externalTransactionId
     * @param string $chargeId
     * @param string $status
     * @param int $amount
     * @param string $currency
     * @param string $source
     * @param string $reason
     * @return InvoicePaymentPartialAggregate
     */
    public function closeChargeDispute(
        string $externalTransactionId,
        string $chargeId,
        string $status,
        int $amount,
        string $currency,
        string $source,
        string $reason,
    ): self
    {
        $this->recordThat(new InvoiceChargeDisputeClosed(
            externalTransactionId: $externalTransactionId,
            chargeId             : $chargeId,
            status               : $status,
            amount               : $amount,
            currency             : $currency,
            source               : $source,
            reason               : $reason,
        ));

        return $this;
    }


    /**
     * @param string $invoicePaymentUuid
     * @param string $invoiceUuid
     * @param string $errorMessage
     * @param string $authorType
     * @param int $amount
     * @param int|null $authorId
     * @return $this
     */
    public function invoicePaymentFailed(
        string $invoicePaymentUuid,
        string $invoiceUuid,
        string $errorMessage,
        string $authorType,
        int $amount,
        ?int $authorId = null,
    ): static
    {
        $this->recordThat(new InvoicePaymentFailed(
            invoicePaymentUuid: $invoicePaymentUuid,
            invoiceUuid       : $invoiceUuid,
            errorMessage      : $errorMessage,
            authorType        : $authorType,
            amount            : $amount,
            authorId          : $authorId,
        ));

        return $this;
    }

    /**
     * @param string $invoicePaymentUuid
     * @param string $authorType
     * @param int $invoicePaymentAttemptNumber
     * @param int $invoicePaymentMaxAttemptsPerPaymentMethod
     * @param int $invoicePaymentTotal
     * @param int|null $authorId
     * @return $this
     */
    public function cancelInvoicePayment(
        string $invoicePaymentUuid,
        string $authorType,
        int $invoicePaymentAttemptNumber,
        int $invoicePaymentMaxAttemptsPerPaymentMethod,
        int $invoicePaymentTotal,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new InvoicePaymentCanceled(
            invoicePaymentUuid                       : $invoicePaymentUuid,
            authorType                               : $authorType,
            invoicePaymentAttemptNumber              : $invoicePaymentAttemptNumber,
            invoicePaymentMaxAttemptsPerPaymentMethod: $invoicePaymentMaxAttemptsPerPaymentMethod,
            invoicePaymentTotal                      : $invoicePaymentTotal,
            authorId                                 : $authorId,
        ));

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param string $paymentMethod
     * @param string $dueDate
     * @param string $issuedDate
     * @param int $total
     * @return $this
     */
    public function notifyInvoicePaymentDue(
        string $invoiceUuid,
        string $paymentMethod,
        string $dueDate,
        string $issuedDate,
        int $total,
    ): self
    {
        $this->recordThat(new InvoicePaymentDue(
            invoiceUuid  : $invoiceUuid,
            paymentMethod: $paymentMethod,
            total        : $total,
            dueDate      : $dueDate,
            issuedDate   : $issuedDate,
        ));

        return $this;
    }
}
