<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\Credit\CreditAdded;
use App\Events\Billing\StoredEvents\Credit\CreditAppliedToInvoice;
use App\Events\Billing\StoredEvents\Credit\CreditDeducted;
use App\Events\Billing\StoredEvents\Credit\CreditExpired;
use App\Events\Billing\StoredEvents\Credit\CreditExtended;
use App\Events\Billing\StoredEvents\Credit\CreditTypeDeducted;
use App\Models\Odin\Company;
use App\Services\Billing\BillingLogService;
use Exception;

class CreditAggregateRoot extends BillingWorkflowAggregateRoot
{
    /**
     * @param string $uuid
     * @param string $companyReference
     * @param float $amount
     * @param string $type
     * @param string $authorType
     * @param int|null $authorId
     * @param string|null $notes
     * @return CreditAggregateRoot
     * @throws Exception
     */
    public function addCredit(
        string $uuid,
        string $companyReference,
        float $amount,
        string $type,
        string $authorType,
        ?int $authorId = null,
        ?string $notes = null,
        array $billingProfileIds = []
    ): self
    {
        if ($amount <= 0) {
            $exception = new Exception("Cannot add negative credit to company. Type $type CompanyRef $companyReference Uuid $uuid");

            BillingLogService::logException(
                exception: $exception,
                namespace: 'credit_aggregate_root',
                context  : [
                    "uuid"             => $uuid,
                    "companyReference" => $companyReference,
                    "amount"           => $amount,
                    "type"             => $type,
                    "authorType"       => $authorType,
                    "authorId"         => $authorId,
                    "notes"            => $notes,
                    "billingProfileIds"=> $billingProfileIds
                ]
            );

            throw $exception;
        }

        $this->recordThat(new CreditAdded(
            uuid            : $uuid,
            companyReference: $companyReference,
            amount          : $amount,
            type            : $type,
            authorType      : $authorType,
            authorId        : $authorId,
            notes           : $notes,
            billingProfileIds: $billingProfileIds
        ));

        return $this;
    }

    /**
     * @param int $creditId
     * @param string $authorType
     * @param string|null $oldDate
     * @param int|null $authorId
     * @return CreditAggregateRoot
     */
    public function expireCredit(
        int $creditId,
        string $authorType,
        ?string $oldDate = null,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new CreditExpired(
            creditId  : $creditId,
            authorType: $authorType,
            authorId  : $authorId,
            oldDate   : $oldDate,
        ));

        return $this;
    }

    /**
     * @param int $creditId
     * @param string $newDate
     * @param string $authorType
     * @param int|null $authorId
     * @return CreditAggregateRoot
     */
    public function extendCredit(
        int $creditId,
        string $newDate,
        string $authorType,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new CreditExtended(
            creditId  : $creditId,
            newDate   : $newDate,
            authorType: $authorType,
            authorId  : $authorId,
        ));

        return $this;
    }


    /**
     * @param Company $company
     * @param float $amount
     * @param string $type
     * @param string $authorType
     * @param string|null $notes
     * @param int|null $authorId
     * @return $this
     */
    public function deductCreditType(
        Company $company,
        float $amount,
        string $type,
        string $authorType,
        ?string $notes = null,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new CreditTypeDeducted(
            companyId : $company->{Company::FIELD_ID},
            amount    : $amount,
            type      : $type,
            authorType: $authorType,
            authorId  : $authorId,
            notes     : $notes
        ));

        return $this;
    }

    /**
     * @return $this
     */
    public function applyCreditToInvoice(
        string $creditUuid,
        string $invoiceUuid,
        int $companyId,
        float $amount,
        string $creditType,
        string $authorType,
        ?string $notes = null,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new CreditAppliedToInvoice(
            creditUuid : $creditUuid,
            invoiceUuid: $invoiceUuid,
            companyId  : $companyId,
            amount     : $amount,
            creditType : $creditType,
            authorType : $authorType,
            authorId   : $authorId,
            notes      : $notes
        ));

        return $this;
    }

    public function deductCreditById(
        int $creditId,
        float $amount,
        string $authorType,
        ?int $authorId = null,
    ): self
    {
        $this->recordThat(new CreditDeducted(
            creditId  : $creditId,
            amount    : $amount,
            authorType: $authorType,
            authorId  : $authorId,
        ));

        return $this;
    }

}
