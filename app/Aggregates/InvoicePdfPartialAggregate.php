<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\Invoice\Pdf\CreateInvoicePdf;
use App\Events\Billing\StoredEvents\Invoice\Pdf\InvoicePdfCreated;
use App\Events\Billing\StoredEvents\Invoice\Pdf\InvoicePdfFailed;
use Spatie\EventSourcing\AggregateRoots\AggregatePartial;

class InvoicePdfPartialAggregate extends AggregatePartial
{
    /**
     * @param string $invoiceUuid
     * @return InvoicePdfPartialAggregate
     */
    public function createInvoicePdf(string $invoiceUuid): self
    {
        $this->recordThat(new CreateInvoicePdf(
            invoiceUuid: $invoiceUuid,
        ));

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param string $filepath
     * @return InvoicePdfPartialAggregate
     */
    public function invoicePdfCreated(string $invoiceUuid, string $filepath): self
    {
        $this->recordThat(new InvoicePdfCreated(
            invoiceUuid: $invoiceUuid,
            filepath   : $filepath
        ));

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param string $errorMessage
     * @return InvoicePdfPartialAggregate
     */
    public function invoicePdfFailed(string $invoiceUuid, string $errorMessage): self
    {
        $this->recordThat(new InvoicePdfFailed(
            invoiceUuid : $invoiceUuid,
            errorMessage: $errorMessage
        ));

        return $this;
    }
}
