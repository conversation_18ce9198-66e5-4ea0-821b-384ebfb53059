<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\Invoice\InvoiceSnapshotCreated;
use App\Services\Billing\BillingLogService;
use Exception;

class InvoiceSnapshotAggregateRoot extends BillingWorkflowAggregateRoot
{
    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param int $companyId
     * @param string $status
     * @param float $totalValue
     * @param float $totalOutstanding
     * @param float $totalRefunded
     * @param float $totalPaid
     * @param float $totalCollections
     * @param float $totalCollectionsRecovered
     * @param float $totalCollectionsLost
     * @param float $totalCreditsApplied
     * @param float $totalChargebackWon
     * @param float $totalChargebackLost
     * @param float $totalChargeback
     * @param float $totalWrittenOff
     * @param string|null $scenario
     * @param int|null $accountManagerId
     * @param int|null $successManagerId
     * @param int|null $businessDevelopmentManagerId
     * @param string|null $date
     * @return InvoiceSnapshotAggregateRoot
     * @throws Exception
     */
    public function createInvoiceSnapshot(
        string $uuid,
        string $invoiceUuid,
        int $companyId,
        string $status,
        float $totalValue,
        float $totalOutstanding,
        float $totalRefunded,
        float $totalPaid,
        float $totalCollections,
        float $totalCollectionsRecovered,
        float $totalCollectionsLost,
        float $totalCreditsApplied,
        float $totalChargebackWon,
        float $totalChargebackLost,
        float $totalChargeback,
        float $totalWrittenOff,
        ?string $scenario = null,
        ?int $accountManagerId = null,
        ?int $successManagerId = null,
        ?int $businessDevelopmentManagerId = null,
        ?string $date = null
    ): self
    {
        $totals = collect([
            "totalValue"                => $totalValue,
            "totalOutstanding"          => $totalOutstanding,
            "totalRefunded"             => $totalRefunded,
            "totalPaid"                 => $totalPaid,
            "totalCollections"          => $totalCollections,
            "totalCollectionsRecovered" => $totalCollectionsRecovered,
            "totalCollectionsLost"      => $totalCollectionsLost,
            "totalCreditsApplied"       => $totalCreditsApplied,
            "totalChargebackWon"        => $totalChargebackWon,
            "totalChargebackLost"       => $totalChargebackLost,
            "totalChargeback"           => $totalChargeback,
            "totalWrittenOff"           => $totalWrittenOff,
        ]);

        if ($totals->values()->min() < 0) {
            $exception = new Exception("Cannot save snapshot with negative value. Invoice $invoiceUuid" . json_encode($totals));

            BillingLogService::logException(
                exception: $exception,
                namespace: 'invoice_snapshot_aggregate_root',
                context  : [
                    "uuid"                         => $uuid,
                    "invoiceUuid"                  => $invoiceUuid,
                    "companyId"                    => $companyId,
                    "status"                       => $status,
                    "scenario"                     => $scenario,
                    "accountManagerId"             => $accountManagerId,
                    "successManagerId"             => $successManagerId,
                    "businessDevelopmentManagerId" => $businessDevelopmentManagerId,
                    "date"                         => $date,
                    ...$totals->toArray()
                ]
            );

            throw $exception;
        }

        $this->recordThat(new InvoiceSnapshotCreated(
            uuid                        : $uuid,
            invoiceUuid                 : $invoiceUuid,
            companyId                   : $companyId,
            status                      : $status,
            totalValue                  : $totalValue,
            totalOutstanding            : $totalOutstanding,
            totalRefunded               : $totalRefunded,
            totalPaid                   : $totalPaid,
            totalCollections            : $totalCollections,
            totalCollectionsRecovered   : $totalCollectionsRecovered,
            totalCollectionsLost        : $totalCollectionsLost,
            totalCreditsApplied         : $totalCreditsApplied,
            totalChargebackWon          : $totalChargebackWon,
            totalChargebackLost         : $totalChargebackLost,
            totalChargeback             : $totalChargeback,
            totalWrittenOff             : $totalWrittenOff,
            scenario                    : $scenario,
            accountManagerId            : $accountManagerId,
            successManagerId            : $successManagerId,
            businessDevelopmentManagerId: $businessDevelopmentManagerId,
            date                        : $date,
        ));

        return $this;
    }
}
