<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\Invoice\Charge\BillingWorkflowStoredEvent;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Spatie\EventSourcing\AggregateRoots\AggregateRoot;

class BillingWorkflowAggregateRoot extends AggregateRoot
{
    /**
     * This function overrides the parent function bypassing the check for event overlap.
     * @return void
     */
    protected function ensureNoOtherEventsHaveBeenPersisted(): void
    {
        //
    }

    /**
     * @return bool
     */
    private function checkEventsOverlap(): bool
    {
        try {
            parent::ensureNoOtherEventsHaveBeenPersisted();
        } catch (Exception $exception) {
            return true;
        }

        return false;
    }

    /**
     * Since concurrency is a problem in event sourcing, and we don’t replay events in A20,
     * we decided to create logic to log overlaps and fix them whenever they’re detected.
     *
     * The problem is: Aggregate A starts for aggregate root UUID X at version 1.
     * It dispatches a few events, incrementing the version each time an event is dispatched.
     * But before it can persist to the database, another process starts Aggregate B for the same aggregate root UUID X,
     * also starting at version 1 (because A hasn’t persisted yet).
     *
     * Aggregate B persists first with version 2.
     * When Aggregate A tries to persist, it fails because the database version doesn’t match the version recorded in Aggregate A.
     *
     * This function recalculates the version right before saving it, ensuring the version is correct.
     * @param array $events
     * @return void
     */
    private function fixAggregateVersionOverlap(array $events): void
    {
        $lastVersion = $this->getStoredEventRepository()->getLatestAggregateVersion($this->uuid());
        collect($events)->map(fn($item, $idx) => $item->setAggregateRootVersion($idx + $lastVersion + 1));
    }

    /**
     * @param array $events
     * @return void
     */
    private function logPersistedEvents(array $events): void
    {
        foreach ($events as $event) {
            BillingLogService::log(
                message  : $event::class . ' persisted',
                namespace: 'billing_aggregate_root',
                context  : [
                    'data' => json_decode(json_encode($event))
                ]
            );
        }
    }

    /**
     * @return $this
     * @throws BindingResolutionException|Exception
     */
    public function persist(): static
    {
        $events = $this->getRecordedEvents();
        $this->logPersistedEvents($events);

        $fixNeeded = $this->checkEventsOverlap();

        if ($fixNeeded) {
            $this->fixAggregateVersionOverlap($events);
        }

        try {
            $persisted = parent::persist();
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception: $exception,
                namespace: 'billing_aggregate_root',
                context  : [
                    'events' => json_decode(json_encode($events))
                ]
            );

            throw $exception;
        }

        $this->dispatchWorkflowEvents($events);

        return $persisted;
    }

    /**
     * @param array $events
     * @return void
     */
    protected function dispatchWorkflowEvents(array $events): void
    {
        collect($events)
            ->filter(fn($event) => $event instanceof BillingWorkflowStoredEvent)
            ->map(function (BillingWorkflowStoredEvent $event) {
                event($event);
            });
    }

    /**
     * Since we don't replay events, and we need to add fields to stored events.
     * In order to prevent MissingConstructorArgumentsException we need to override this method
     * and skip the part that they parse the stored events and calculate the aggregate root version.
     *
     * This has no impacts from what we need to do.
     * @return $this
     */
    protected function reconstituteFromEvents(): static
    {
        if ($snapshot = $this->getSnapshotRepository()->retrieve(parent::uuid())) {
            $this->aggregateVersion = $snapshot->aggregateVersion;
            $this->useState($snapshot->state);
        }

        $this->aggregateVersionAfterReconstitution = $this->aggregateVersion;

        return $this;
    }
}
