<?php

namespace App\Aggregates;

use App\DTO\Billing\InvoiceItemTaxData;
use App\DTO\Billing\InvoiceTaxData;
use App\Events\Billing\StoredEvents\Invoice\InvoiceUpdated;
use App\Events\Billing\StoredEvents\Invoice\InvoiceInitialized;
use App\Events\Billing\StoredEvents\Invoice\InvoiceItemTaxApplied;
use App\Events\Billing\StoredEvents\Invoice\InvoiceStatusUpdated;
use App\Events\Billing\StoredEvents\Invoice\InvoiceTaxCalculationRequestFailed;
use App\Events\Billing\StoredEvents\Invoice\RequestInvoiceTax;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemAdded;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemDeleted;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemUpdated;
use App\Models\Billing\Invoice;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;

class InvoiceAggregateRoot extends BillingWorkflowAggregateRoot
{
    public InvoicePaymentPartialAggregate     $charge;
    public InvoiceRefundPartialAggregate      $refund;
    public InvoicePdfPartialAggregate         $pdf;
    public InvoiceCollectionPartialAggregate  $collection;
    public InvoiceCreditPartialAggregate      $credit;
    public InvoiceTransactionPartialAggregate $transaction;
    public InvoiceWriteOffsPartialAggregate   $writeOffs;

    /**
     * @throws BindingResolutionException
     */
    public function __construct()
    {
        $this->charge = new InvoicePaymentPartialAggregate($this);
        $this->refund = new InvoiceRefundPartialAggregate($this);
        $this->pdf = new InvoicePdfPartialAggregate($this);
        $this->collection = new InvoiceCollectionPartialAggregate($this);
        $this->credit = new InvoiceCreditPartialAggregate($this);
        $this->transaction = new InvoiceTransactionPartialAggregate($this);
        $this->writeOffs = new InvoiceWriteOffsPartialAggregate($this);
    }

    /**
     * @param string $invoiceUuid
     * @param int $quantity
     * @param float $unitPrice
     * @param string $billableType
     * @param string $description
     * @param string $authorType
     * @param int|null $billableId
     * @param int|null $authorId
     * @param string|null $notes
     * @return $this
     */
    public function addInvoiceItem(
        string $invoiceUuid,
        int $quantity,
        float $unitPrice,
        string $billableType,
        string $description,
        string $authorType,
        ?int $billableId = null,
        int $authorId = null,
        string $notes = null
    ): self
    {
        $this->recordThat(new InvoiceItemAdded(
            invoiceUuid : $invoiceUuid,
            quantity    : $quantity,
            unitPrice   : $unitPrice,
            billableType: $billableType,
            authorType  : $authorType,
            billableId  : $billableId,
            description : $description,
            authorId    : $authorId,
            notes       : $notes,
        ));

        return $this;
    }

    /**
     * @param int $invoiceItemId
     * @param string $authorType
     * @param int|null $authorId
     * @return $this
     */
    public function deleteInvoiceItem(
        int $invoiceItemId,
        string $authorType,
        ?int $authorId,
    ): self
    {
        $this->recordThat(new InvoiceItemDeleted(
            invoiceItemId: $invoiceItemId,
            authorType   : $authorType,
            authorId     : $authorId,
        ));

        return $this;
    }

    /**
     * @param int $invoiceItemId
     * @param array $changes
     * @param string $authorType
     * @param int|null $authorId
     * @return $this
     */
    public function updateInvoiceItem(
        int $invoiceItemId,
        array $changes,
        string $authorType,
        ?int $authorId,
    ): self
    {
        $this->recordThat(new InvoiceItemUpdated(
            invoiceItemId: $invoiceItemId,
            changes      : $changes,
            authorType   : $authorType,
            authorId     : $authorId,
        ));

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param string $companyReference
     * @param Carbon $dueDate
     * @param Carbon $issueDate
     * @param string $authorType
     * @param int $billingProfileId
     * @param ?int $authorId
     * @param string|null $notes
     * @return $this
     */
    public function createInvoice(
        string $invoiceUuid,
        string $companyReference,
        Carbon $dueDate,
        Carbon $issueDate,
        string $authorType,
        int $billingProfileId,
        ?int $authorId = null,
        ?string $notes = null,
    ): self
    {
        $this->recordThat(new InvoiceInitialized(
            invoiceUuid     : $invoiceUuid,
            companyReference: $companyReference,
            dueDate         : $dueDate,
            issueDate       : $issueDate,
            authorId        : $authorId,
            authorType      : $authorType,
            billingProfileId: $billingProfileId,
            notes           : $notes
        ));

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param array $changes
     * @param string $authorType
     * @param int|null $authorId
     * @return $this
     */
    public function updateInvoice(
        string $invoiceUuid,
        array $changes,
        string $authorType,
        ?int $authorId,
    ): self
    {
        $this->recordThat(new InvoiceUpdated(
            invoiceUuid: $invoiceUuid,
            changes    : $changes,
            authorType : $authorType,
            authorId   : $authorId
        ));

        return $this;
    }

    /**
     * @param Invoice $invoice
     * @param string $newStatus
     * @param string $authorType
     * @param string|null $newScenario
     * @param ?int $authorId
     * @return InvoiceAggregateRoot
     */
    public function updateInvoiceStatus(
        Invoice $invoice,
        string $newStatus,
        string $authorType,
        ?string $newScenario = null,
        ?int $authorId = null,
        ?string $date = null
    ): self
    {
        if ($invoice->status->status() !== $newStatus) {
            $this->recordThat(new InvoiceStatusUpdated(
                invoiceUuid: $invoice->uuid,
                newStatus  : $newStatus,
                authorType : $authorType,
                newScenario: $newScenario,
                authorId   : $authorId,
                oldStatus  : $invoice->status->status(),
                date       : $date,
            ));
        }

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param string $authorType
     * @param int|null $authorId
     * @return $this
     */
    public function dispatchCalculateInvoiceTax(
        string $invoiceUuid,
        string $authorType,
        ?int $authorId = null,
    ): InvoiceAggregateRoot
    {
        $this->recordThat(new RequestInvoiceTax(
            invoiceUuid: $invoiceUuid,
            authorId   : $authorId,
            authorType : $authorType,
        ));

        return $this;
    }

    /**
     * @param InvoiceTaxData $invoiceTaxData
     * @param string $authorType
     * @param int|null $authorId
     * @return InvoiceAggregateRoot
     */
    public function applyInvoiceTax(
        InvoiceTaxData $invoiceTaxData,
        string $authorType,
        ?int $authorId = null,
    ): InvoiceAggregateRoot
    {
        /** @var array<InvoiceItemTaxData> $invoiceItemTaxDataArray */
        $invoiceItemTaxDataArray = $invoiceTaxData->getInvoiceItemTaxData();

        foreach ($invoiceItemTaxDataArray as $invoiceItemTaxData) {
            $this->applyInvoiceItemTax(
                invoiceItemTaxData: $invoiceItemTaxData,
                authorType        : $authorType,
                authorId          : $authorId,
            );
        }

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param int $authorId
     * @param string $message
     * @return InvoiceAggregateRoot
     */
    public function calculateInvoiceTaxFailed(string $invoiceUuid, int $authorId, string $message): InvoiceAggregateRoot
    {
        $this->recordThat(new InvoiceTaxCalculationRequestFailed(
            invoiceUuid: $invoiceUuid,
            authorId   : $authorId,
            message    : $message,
        ));

        return $this;
    }

    /**
     * @param InvoiceItemTaxData $invoiceItemTaxData
     * @param string $authorType
     * @param int|null $authorId
     * @return $this
     */
    public function applyInvoiceItemTax(
        InvoiceItemTaxData $invoiceItemTaxData,
        string $authorType,
        ?int $authorId = null,
    ): InvoiceAggregateRoot
    {
        $this->recordThat(new InvoiceItemTaxApplied(
            invoiceItemId: $invoiceItemTaxData->getInvoiceItemId(),
            tax          : $invoiceItemTaxData->getTax(),
            authorType   : $authorType,
            authorId     : $authorId,
        ));

        return $this;
    }
}
