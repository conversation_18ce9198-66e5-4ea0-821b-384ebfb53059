<?php

namespace App\Aggregates;

use App\DTO\Billing\Refund\InvoiceRefundDTO;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceChargeRefundUpdated;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceRefundRequestAttempted;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceRefundRequestFailed;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceRefundRequestMaxAttemptsExceeded;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceRefundRequestSuccess;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceChargeRefundSuccess;
use App\Events\Billing\StoredEvents\Invoice\Refund\RequestRefund;
use Exception;
use Spatie\EventSourcing\AggregateRoots\AggregatePartial;

class InvoiceRefundPartialAggregate extends AggregatePartial
{
    /**
     * @param string $invoiceUuid
     * @param string $externalTransactionId
     * @param float $amount
     * @param string $currency
     * @param string $type
     * @param float $amountCaptured
     * @param bool $fullyRefunded
     * @param string $source
     * @param string|null $reason
     * @param string|null $scenario
     * @param string|null $scope
     * @return InvoiceRefundPartialAggregate
     * @throws Exception
     */
    public function refundInvoice(
        string $invoiceUuid,
        string $externalTransactionId,
        float $amount,
        string $currency,
        string $type,
        float $amountCaptured,
        bool $fullyRefunded,
        string $source,
        string $reason = null,
        string $scenario = null,
        string $scope = null,
    ): self
    {
        $this->recordThat(new InvoiceChargeRefundSuccess(
            invoiceUuid          : $invoiceUuid,
            externalTransactionId: $externalTransactionId,
            amount               : $amount,
            currency             : $currency,
            amountCaptured       : $amountCaptured,
            fullyRefunded        : $fullyRefunded,
            source               : $source,
            type                 : $type,
            scenario             : $scenario,
            scope                : $scope,
            reason               : $reason,
        ));

        return $this;
    }

    /**
     * @param string $externalRefundId
     * @param string $externalChargeId
     * @param string $currency
     * @param int $amount
     * @param string|null $invoiceRefundChargeUuid
     * @param string|null $invoiceUuid
     * @return $this
     */
    public function updateInvoiceCharge(
        string $externalRefundId,
        string $externalChargeId,
        string $currency,
        int $amount,
        ?string $invoiceRefundChargeUuid = null,
        ?string $invoiceUuid = null,
    ): self
    {
        $this->recordThat(new InvoiceChargeRefundUpdated(
            externalRefundId       : $externalRefundId,
            externalChargeId       : $externalChargeId,
            currency               : $currency,
            amount                 : $amount,
            invoiceRefundChargeUuid: $invoiceRefundChargeUuid,
            invoiceUuid            : $invoiceUuid,
        ));

        return $this;
    }

    /**
     * @param string $companyInvoiceUuid
     * @return $this
     */
    public function refundInvoiceRequestAttempt(string $companyInvoiceUuid): self
    {
        $this->recordThat(new InvoiceRefundRequestAttempted($companyInvoiceUuid));

        return $this;
    }

    /**
     * @param string $refundUuid
     * @return $this
     */
    public function refundInvoiceRequestSuccess(
        string $refundUuid,
    ): self
    {
        $this->recordThat(new InvoiceRefundRequestSuccess(
            refundUuid: $refundUuid,
        ));

        return $this;
    }

    /**
     * @param string $companyInvoiceUuid
     * @param string $errorMessage
     * @return $this
     */
    public function refundInvoiceRequestFailed(string $companyInvoiceUuid, string $errorMessage): self
    {
        $this->recordThat(new InvoiceRefundRequestFailed(
            invoiceUuid : $companyInvoiceUuid,
            errorMessage: $errorMessage
        ));

        return $this;
    }

    /**
     * @param string $companyInvoiceUuid
     * @return $this
     */
    public function refundInvoiceRequestMaxAttemptsExceeded(string $companyInvoiceUuid): self
    {
        $this->recordThat(new InvoiceRefundRequestMaxAttemptsExceeded(
            invoiceUuid: $companyInvoiceUuid,
        ));

        return $this;
    }

    /**
     * @param string $invoiceUuid
     * @param InvoiceRefundDTO $invoiceRefundDTO
     * @param string $authorType
     * @param int|null $authorId
     * @return $this
     */
    public function requestRefund(
        string $invoiceUuid,
        InvoiceRefundDTO $invoiceRefundDTO,
        string $authorType,
        ?int $authorId,
    ): self
    {
        $this->recordThat(new RequestRefund(
            invoiceUuid        : $invoiceUuid,
            invoiceRefundObject: $invoiceRefundDTO->toArray(),
            authorType         : $authorType,
            authorId           : $authorId,
        ));

        return $this;
    }
}
