<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\Invoice\InvoiceTransactionCreated;
use App\Services\Billing\BillingLogService;
use Exception;
use Spatie\EventSourcing\AggregateRoots\AggregatePartial;

class InvoiceTransactionPartialAggregate extends AggregatePartial
{
    /**
     * @param string $uuid
     * @param string $invoiceUuid
     * @param string $externalReference
     * @param float $amount
     * @param string $currency
     * @param string $type
     * @param string $origin
     * @param array|null $payload
     * @param string|null $scenario
     * @param string|null $scope
     * @param string|null $date
     * @return $this
     * @throws Exception
     */
    public function createInvoiceTransaction(
        string $uuid,
        string $invoiceUuid,
        string $externalReference,
        float $amount,
        string $currency,
        string $type,
        string $origin,
        ?array $payload = [],
        ?string $scenario = null,
        ?string $scope = null,
        ?string $date = null
    ): InvoiceTransactionPartialAggregate
    {
        if ($amount < 0) {
            $exception = new Exception("Cannot save negative transaction. Invoice $invoiceUuid Type $type Origin $origin");

            BillingLogService::logException(
                exception: $exception,
                namespace: 'invoice_transaction_aggregate_root',
                context  : [
                    "uuid"              => $uuid,
                    "invoiceUuid"       => $invoiceUuid,
                    "externalReference" => $externalReference,
                    "amount"            => $amount,
                    "currency"          => $currency,
                    "type"              => $type,
                    "origin"            => $origin,
                    "payload"           => $payload,
                    "scenario"          => $scenario,
                    "scope"             => $scope,
                ]
            );

            throw $exception;
        }

        $this->recordThat(new InvoiceTransactionCreated(
            uuid             : $uuid,
            invoiceUuid      : $invoiceUuid,
            externalReference: $externalReference,
            amount           : $amount,
            currency         : $currency,
            type             : $type,
            origin           : $origin,
            payload          : $payload,
            scenario         : $scenario,
            scope            : $scope,
            date             : $date
        ));

        return $this;
    }
}
