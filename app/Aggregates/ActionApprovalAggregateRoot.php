<?php

namespace App\Aggregates;

use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalCancelled;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalRequested;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalReviewed;
use Illuminate\Support\Str;

class ActionApprovalAggregateRoot extends BillingWorkflowAggregateRoot
{
    /**
     * @param string $actionApprovalUuid
     * @param int $modelId
     * @param string $modelClass
     * @param string $actionRequested
     * @param string $authorType
     * @param int|null $authorId
     * @param array $arguments
     * @param string|null $note
     * @return $this
     */
    public function requestAction(
        string $actionApprovalUuid,
        int $modelId,
        string $modelClass,
        string $actionRequested,
        string $authorType,
        ?int $authorId = null,
        array $arguments = [],
        ?string $note = null
    ): ActionApprovalAggregateRoot
    {
        $this->recordThat(new ActionApprovalRequested(
            actionApprovalUuid: $actionApprovalUuid,
            modelId           : $modelId,
            modelClass        : $modelClass,
            actionRequested   : $actionRequested,
            authorType        : $authorType,
            authorId          : $authorId,
            arguments         : $arguments,
            note              : $note
        ));

        return $this;
    }

    public function cancel(
        string $actionApprovalUuid,
        int $modelId,
        string $modelClass,
        string $actionRequested,
        string $authorType,
        ?int $authorId = null,
        array $arguments = [],
        ?string $note = null
    ): ActionApprovalAggregateRoot
    {
        $this->recordThat(new ActionApprovalCancelled(
            actionApprovalUuid: $actionApprovalUuid,
            modelId           : $modelId,
            modelClass        : $modelClass,
            actionRequested   : $actionRequested,
            authorType        : $authorType,
            authorId          : $authorId,
            arguments         : $arguments,
            note              : $note
        ));

        return $this;
    }

    /**
     * @param string $actionApprovalUuid
     * @param int $modelId
     * @param string $modelClass
     * @param int $authorId
     * @param string $status
     * @param string|null $reason
     * @return $this
     */
    public function reviewAction(
        string $actionApprovalUuid,
        int $modelId,
        string $modelClass,
        int $authorId,
        string $status,
        ?string $reason = null,
    ): ActionApprovalAggregateRoot
    {
        $this->recordThat(new ActionApprovalReviewed(
            actionApprovalUuid: $actionApprovalUuid,
            modelId           : $modelId,
            modelClass        : $modelClass,
            authorId          : $authorId,
            status            : $status,
            reason            : $reason,
        ));

        return $this;
    }
}
