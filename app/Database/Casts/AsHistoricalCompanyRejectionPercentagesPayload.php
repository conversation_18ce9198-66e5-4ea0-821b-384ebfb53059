<?php

namespace App\Database\Casts;

use App\DataModels\HistoricalCompanyRejectionPercentagesDataModel;
use Exception;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsHistoricalCompanyRejectionPercentagesPayload implements CastsAttributes
{
    /**
     * @inheritDoc
     */
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return HistoricalCompanyRejectionPercentagesDataModel::fromJson($value);
    }

    /**
     * @inheritDoc
     */
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (!$value instanceof HistoricalCompanyRejectionPercentagesDataModel) {
            throw new Exception("Rejection percentages must be of type: " . HistoricalCompanyRejectionPercentagesDataModel::class);
        }

        return $value->toJson();
    }
}
