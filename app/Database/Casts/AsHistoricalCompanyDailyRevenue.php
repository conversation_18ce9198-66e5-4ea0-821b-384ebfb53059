<?php

namespace App\Database\Casts;

use App\DataModels\HistoricalCompanyDailyRevenueDataModel;
use Exception;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsHistoricalCompanyDailyRevenue implements CastsAttributes
{
    /**
     * @inheritDoc
     */
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return HistoricalCompanyDailyRevenueDataModel::fromJson($value);
    }

    /**
     * @inheritDoc
     */
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (!$value instanceof HistoricalCompanyDailyRevenueDataModel) {
            throw new Exception("Daily revenue must be of type: " . HistoricalCompanyDailyRevenueDataModel::class);
        }

        return $value->to<PERSON>son();
    }
}
