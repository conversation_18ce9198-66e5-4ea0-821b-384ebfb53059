<?php

namespace App\Database\Casts;

use App\DataModels\HistoricalCompanySalesStatusDataModel;
use Exception;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsHistoricalCompanySalesStatus implements CastsAttributes
{
    /**
     * @inheritDoc
     */
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return HistoricalCompanySalesStatusDataModel::fromJson($value);
    }

    /**
     * @inheritDoc
     */
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (!$value instanceof HistoricalCompanySalesStatusDataModel) {
            throw new Exception("Sales status must be of type: " . HistoricalCompanySalesStatusDataModel::class);
        }

        return $value->toJson();
    }
}
