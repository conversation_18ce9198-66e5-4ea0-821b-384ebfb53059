<?php

namespace App\Database\Casts;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsConfigurableFieldPayload implements CastsAttributes
{
    /**
     * @param Model $model
     * @param string $key
     * @param mixed $value
     * @param array $attributes
     * @return ConfigurableFieldDataModel
     */
    public function get($model, string $key, $value, array $attributes): ConfigurableFieldDataModel
    {
        $jsonData = json_decode($value) ? $value : '{ "data": null }';
        return ConfigurableFieldDataModel::fromJson($jsonData);
    }


    /**
     * @param $model
     * @param string $key
     * @param $value
     * @param array $attributes
     * @return string
     */
    public function set($model, string $key, $value, array $attributes): string
    {
        if (!$value instanceof ConfigurableFieldDataModel)
            throw new \RuntimeException("The payload must be of type: " . ConfigurableFieldDataModel::class);

        return $value->to<PERSON>son();
    }
}
