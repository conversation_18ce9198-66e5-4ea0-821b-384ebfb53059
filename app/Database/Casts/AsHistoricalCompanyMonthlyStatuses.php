<?php

namespace App\Database\Casts;

use App\DataModels\HistoricalCompanyDailyStatusesDataModel;
use App\DataModels\HistoricalCompanyMonthlyStatusesDataModel;
use Exception;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsHistoricalCompanyMonthlyStatuses implements CastsAttributes
{
    /**
     * @inheritDoc
     */
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return HistoricalCompanyMonthlyStatusesDataModel::fromJson($value);
    }

    /**
     * @inheritDoc
     */
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (!$value instanceof HistoricalCompanyMonthlyStatusesDataModel) {
            throw new Exception("Company statuses must be of type: " . HistoricalCompanyMonthlyStatusesDataModel::class);
        }

        return $value->toJson();
    }
}
