<?php

namespace App\Database\Casts;

use App\DataModels\HistoricalCompanyDailyStatusesDataModel;
use Exception;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsHistoricalCompanyDailyStatuses implements CastsAttributes
{
    /**
     * @inheritDoc
     */
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return HistoricalCompanyDailyStatusesDataModel::fromJson($value);
    }

    /**
     * @inheritDoc
     */
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (!$value instanceof HistoricalCompanyDailyStatusesDataModel) {
            throw new Exception("Company statuses must be of type: " . HistoricalCompanyDailyStatusesDataModel::class);
        }

        return $value->toJson();
    }
}
