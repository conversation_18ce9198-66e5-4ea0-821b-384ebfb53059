<?php

namespace App\Database\Casts;

use App\DataModels\HistoricalCompanyMonthlyRevenueDataModel;
use Exception;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsHistoricalCompanyMonthlyRevenue implements CastsAttributes
{
    /**
     * @inheritDoc
     */
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return HistoricalCompanyMonthlyRevenueDataModel::fromJson($value);
    }

    /**
     * @inheritDoc
     */
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (!$value instanceof HistoricalCompanyMonthlyRevenueDataModel) {
            throw new Exception("Daily revenue must be of type: " . HistoricalCompanyMonthlyRevenueDataModel::class);
        }

        return $value->to<PERSON><PERSON>();
    }
}
