<?php

namespace App\Database\Casts;

use App\Workflows\WorkflowPayload;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

class AsWorkflowPayload implements CastsAttributes
{
    /**
     * Handles getting the value of the payload.
     *
     * @param $model
     * @param string $key
     * @param $value
     * @param array $attributes
     * @return WorkflowPayload
     */
    public function get($model, string $key, $value, array $attributes)
    {
        return WorkflowPayload::from<PERSON>son($value);
    }

    /**
     * Handles setting the value of the payload.
     *
     * @param $model
     * @param string $key
     * @param $value
     * @param array $attributes
     * @return string
     */
    public function set($model, string $key, $value, array $attributes)
    {
        if (!$value instanceof WorkflowPayload)
            throw new \RuntimeException("Workflow payload must be of type: ".WorkflowPayload::class);

        return $value->to<PERSON><PERSON>();
    }
}
