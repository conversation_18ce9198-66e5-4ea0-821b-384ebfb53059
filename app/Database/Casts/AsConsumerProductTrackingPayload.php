<?php

namespace App\Database\Casts;

use App\DataModels\Odin\ConsumerProductTrackingPayloadDataModel;
use Exception;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Database\Eloquent\Model;

class AsConsumerProductTrackingPayload implements CastsAttributes
{
    /**
     * @inheritDoc
     */
    public function get(Model $model, string $key, mixed $value, array $attributes)
    {
        return ConsumerProductTrackingPayloadDataModel::fromJson($value);
    }

    /**
     * @inheritDoc
     */
    public function set(Model $model, string $key, mixed $value, array $attributes)
    {
        if (!$value instanceof ConsumerProductTrackingPayloadDataModel) {
            throw new Exception("Consumer product tracking payload must be of type: " . ConsumerProductTrackingPayloadDataModel::class);
        }

        return $value->to<PERSON>son();
    }
}
