<?php

namespace App\Database\Casts;

use App\DataModels\Workflows\TaskResultDataModel;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Illuminate\Support\Collection;

class AsTaskResultPayload implements CastsAttributes
{
    /**
     * @param \Illuminate\Database\Eloquent\Model $model
     * @param string $key
     * @param mixed $value
     * @param array $attributes
     * @return Collection<TaskResultDataModel>
     */
    public function get($model, string $key, $value, array $attributes): Collection
    {
       return collect(json_decode($value))->map(fn ($result) => TaskResultDataModel::fromJson(json_encode($result)));
    }

    /**
     * @param \Illuminate\Database\Eloquent\Model $model
     * @param string $key
     * @param Collection $value
     * @param array $attributes
     * @return string
     */
    public function set($model, string $key, $value, array $attributes): string
    {
       return $value->map(fn(TaskResultDataModel $result) => $result->toArray())->toJson();
   }
}
