<?php

namespace App\BillingWorkflows\Workflows;

use App\DTO\BillingWorkflow\ActivityArgument;
use App\Models\Billing\BillingProfilePolicy;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\BillingProfilePolicyService;
use Generator;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;
use Workflow\ActivityStub;
use Workflow\Workflow;

class BillingEventHandlerWorkflow extends Workflow
{
    public int $tries = 1;

    public $queue = 'billing';

    /**
     * TODO - Implement Error handling
     * If an activity fails should we stop the whole workflow
     * How about retry ? Skip the ones successfully executed ?
     * @param string $eventClassTrigger
     * @param array $context
     * @param BillingProfilePolicyService $profilePolicyService
     * @return Generator
     */
    public function execute(string $eventClassTrigger, array $context, BillingProfilePolicyService $profilePolicyService): Generator
    {
        // TODO - Create a dual access helper
        $billingProfileId = Arr::get($context, 'billingProfileId') ?? Arr::get($context, 'billing_profile_id') ?? null;

        $policies = $profilePolicyService->getPoliciesForEventClass($eventClassTrigger, $billingProfileId);

        $activityArgument = new ActivityArgument(
            eventClassTrigger: $eventClassTrigger,
            context          : $context,
        );

        foreach ($policies as $policy) {
            $activityArgument->setActionData($policy->{BillingProfilePolicy::FIELD_ACTION_DATA} ?? []);

            $activityResult = yield ActivityStub::make(
                $policy->{BillingProfilePolicy::FIELD_ACTION_CLASS},
                $activityArgument
            );

            $activityArgument->setContext(array_merge($activityResult ?? [], $context));
        }
    }
}
