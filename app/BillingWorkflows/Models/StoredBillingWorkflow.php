<?php

declare(strict_types=1);

namespace App\BillingWorkflows\Models;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\ModelStates\HasStates;
use Workflow\Models\StoredWorkflow;

class StoredBillingWorkflow extends StoredWorkflow
{
    use HasStates;

    /**
     * @var string
     */
    protected $table = 'billing_workflows';

    /**
     * @return HasMany
     */
    public function logs(): HasMany
    {
        return $this->hasMany(config('workflows.stored_workflow_log_model', StoredBillingWorkflowLog::class));
    }

    /**
     * @return BelongsToMany
     */
    public function parents(): BelongsToMany
    {
        return $this->belongsToMany(
            config('workflows.stored_workflow_model', self::class),
            config('workflows.workflow_relationships_table', 'workflow_relationships'),
            'child_billing_workflow_id',
            'parent_billing_workflow_id'
        )->withPivot(['parent_index', 'parent_now']);
    }

    /**
     * @return BelongsToMany
     */
    public function children(): BelongsToMany
    {
        return $this->belongsToMany(
            config('workflows.stored_workflow_model', self::class),
            config('workflows.workflow_relationships_table', 'workflow_relationships'),
            'parent_billing_workflow_id',
            'child_billing_workflow_id'
        )->withPivot(['parent_index', 'parent_now']);
    }
}
