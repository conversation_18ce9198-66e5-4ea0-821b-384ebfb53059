<?php

namespace App\BillingWorkflows\Activities;

use App\DTO\BillingWorkflow\ActivityArgument;
use App\Enums\Billing\BillingLogLevel;
use App\Services\Billing\BillingLogService;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Workflow\Activity;
use Workflow\Models\StoredWorkflow;

abstract class BaseActivity extends Activity
{
    protected string $logNamespace = 'billing_workflows';

    public function __construct(int $index, string $now, StoredWorkflow $storedWorkflow, ...$arguments)
    {
        parent::__construct($index, $now, $storedWorkflow, ...$arguments);

        $this->tries = 1;
        $this->queue = 'billing';
    }

    /**
     * @param mixed ...$args
     * @return array
     */
    protected function execute(...$args): array
    {
        $activityArgument = collect($args)->flatten()->first();
        return $this->run($activityArgument);
    }

    /**
     * @param ActivityArgument $activityArgument
     * @return array
     */
    abstract public function run(ActivityArgument $activityArgument): array;

    /**
     * @return string
     */
    abstract static function getId(): string;

    /**
     * @return string
     */
    abstract static function getName(): string;

    /**
     * @return string
     */
    static function getDescription(): string
    {
        return '';
    }

    /**
     * @param string $property
     * @param array $context
     * @return mixed
     */
    function getPropertyFromContext(string $property, array $context): mixed
    {
        return Arr::get($context, $property)
            ?? Arr::get($context, Str::snake($property))
            ?? Arr::get($context, Str::camel($property));
    }

    /**
     * @param string $message
     * @param BillingLogLevel $level
     * @param array $context
     * @return void
     */
    protected function log(
        string $message,
        BillingLogLevel $level = BillingLogLevel::INFO,
        array $context = []
    ): void
    {
        BillingLogService::log(
            message  : $message,
            level    : $level,
            namespace: $this->logNamespace,
            context  : $context
        );
    }
}
