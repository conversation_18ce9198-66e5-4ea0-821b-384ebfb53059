<?php

namespace App\BillingWorkflows\Activities;

use App\DTO\BillingWorkflow\ActivityArgument;
use App\Enums\Billing\BillingPolicyActionType;

class NotifyUser extends BaseActivity
{
    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyActionType::NOTIFY_USER->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Notify User';
    }

    /**
     * @param ActivityArgument $activityArgument
     * @return array
     */
    public function run(ActivityArgument $activityArgument): array
    {
        // TODO - Implement
        return [];
    }
}
