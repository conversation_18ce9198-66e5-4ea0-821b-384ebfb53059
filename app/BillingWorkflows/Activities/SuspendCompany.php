<?php

namespace App\BillingWorkflows\Activities;


use App\DTO\BillingWorkflow\ActivityArgument;
use App\Enums\Billing\BillingLogLevel;
use App\Enums\Billing\BillingPolicyActionType;
use App\Models\Billing\Invoice;
use App\Services\CompanySuspensionService;
use Illuminate\Contracts\Container\BindingResolutionException;

class SuspendCompany extends BaseActivity
{
    protected string $logNamespace = 'suspend_company';

    /**
     * @param array $context
     * @return int|null
     */
    private function getCompanyId(array $context): ?int
    {
        $companyId = $this->getPropertyFromContext('company_id', $context);

        if (!empty($companyId)) {
            return $companyId;
        };

        $invoiceUuid = $this->getPropertyFromContext('invoice_uuid', $context);

        if (empty($invoiceUuid)) {
            return null;
        }

        return Invoice::findByUuid($invoiceUuid)?->company_id;
    }

    /**
     * @param ActivityArgument $activityArgument
     * @return array
     * @throws BindingResolutionException
     */
    public function run(ActivityArgument $activityArgument): array
    {
        $companyId = $this->getCompanyId($activityArgument->getContext());

        // TODO - Improve this
        if (!$companyId) {
            $this->log(
                message: 'Company id not found when suspending company',
                level  : BillingLogLevel::ERROR,
                context: $activityArgument->getContext()
            );

            return [];
        }

        $this->log(
            message: 'Suspending company id ' . $companyId,
            context: [
                'company_id' => $companyId,
                'reason'     => $activityArgument->getEventClassTrigger()
            ]
        );

        /** @var CompanySuspensionService $companySuspensionService */
        $companySuspensionService = app()->make(CompanySuspensionService::class);

        $companySuspensionService->suspend(
            companyId: $companyId
        );

        return [];
    }

    /**
     * @param int $companyId
     * @return void
     */

    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyActionType::SUSPEND_COMPANY->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Suspend Company';
    }

    /**
     * @return string
     */
    public static function getDescription(): string
    {
        return 'Company will be suspended';
    }
}
