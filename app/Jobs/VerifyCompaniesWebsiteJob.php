<?php

namespace App\Jobs;

use App\Models\Odin\Company;
use App\Repositories\LeadProcessing\LeadProcessingQueueConstraintsRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Services\QueueHelperService;
use App\Services\WebsiteVerificationService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Throwable;

class VerifyCompaniesWebsiteJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const BATCH_SIZE = 500;
    const PROCESS_MONTHLY = "process_monthly";
    const PROCESS_RECORD = "process_record";

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected          $command = self::PROCESS_MONTHLY,
        protected ?Company $company = null,
    )
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * Execute the job.
     * @param CompanyRepository $companyRepository
     * @return void
     */
    public function handle(CompanyRepository $companyRepository): void
    {
        try {
            switch ($this->command) {
                case self::PROCESS_MONTHLY:
                    $data = [];
                    $companies = $companyRepository->getAllUnverifiedCompaniesWebsite();
                    foreach ($companies->chunk(self::BATCH_SIZE) as $company) {
                        foreach ($company as $company_data) {
                            $websiteVerificationService = new WebsiteVerificationService($company_data->website);
                            $data[] = [
                                Company::FIELD_ID => $company_data->{Company::FIELD_ID},
                                Company::FIELD_WEBSITE_VERIFIED_URL => $websiteVerificationService->validateWebsiteUrl(),
                                Company::FIELD_WEBSITE_VERIFIED_AT => now()
                            ];
                        }
                    }
                    $companyRepository->updateBatchCompanyRecords($data);
                    break;
                case self::PROCESS_RECORD:
                    if (!is_null($this->company) && $this->company->website) {
                        $websiteVerificationService = new WebsiteVerificationService($this->company->website);
                        $this->company->{Company::FIELD_WEBSITE_VERIFIED_URL} = $websiteVerificationService->validateWebsiteUrl();
                        $this->company->{Company::FIELD_WEBSITE_VERIFIED_AT} = now();
                        $this->company->save();
                    }
                    break;
            }
        } catch (Throwable $e) {
            logger()->error("Companies website verify error: " . $e->getMessage());
        }
    }
}
