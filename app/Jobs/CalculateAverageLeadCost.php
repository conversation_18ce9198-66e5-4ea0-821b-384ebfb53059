<?php

namespace App\Jobs;

use App\DTO\CompanyCampaignData\CompanyCampaignLeadAggregatesDTO;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\CompanyCampaignData;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Campaigns\CompanyCampaignDataRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

class CalculateAverageLeadCost implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $companyCampaignId,
        protected ?int $leadCount = 50
    )
    {}

    /**
     * Execute the job.
     */
    public function handle(CompanyCampaignDataRepository $campaignDataRepository): void
    {
        /** @var CompanyCampaign $companyCampaign */
        $companyCampaign = CompanyCampaign::query()->where(CompanyCampaign::FIELD_ID, $this->companyCampaignId)->first();

        if (!$companyCampaign) {
            logger()->error("Could not retrieve company campaign with id: $this->companyCampaignId");
            return;
        }

        $soldProductAssignments = $companyCampaign->soldProductAssignmentsQuery()->limit($this->leadCount)->get();

        $leadCount = $soldProductAssignments?->count();

        if (!empty($leadCount) && $leadCount > 0) {
            $averageCost = $soldProductAssignments->average(ProductAssignment::FIELD_COST);
        } else {
            $averageCost = null;
        }

        $companyCampaignData = $campaignDataRepository->firstOrNew($this->companyCampaignId);

        $campaignDataRepository->saveData(
            campaignData            : $companyCampaignData,
            averageCost             : $averageCost,
            leadCostLastCalculatedAt: now(),
            leadCount               : $leadCount,
        );
    }
}
