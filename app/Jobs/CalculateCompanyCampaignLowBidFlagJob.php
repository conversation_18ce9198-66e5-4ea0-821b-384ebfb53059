<?php

namespace App\Jobs;

use App\Builders\Pricing\CustomFloorPricingBuilder;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CustomCampaignCountyFloorPrice;
use App\Models\Campaigns\CustomCampaignStateFloorPrice;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

/**
 * Update the low bids flag on a Campaign
 * A campaign will be flagged if:
 *  - it uses custom floor pricing and
 *      - it has an explicit bid below the standard state/county floor
 *      - or has no explicit bid, and the custom state/floor price is lower than the standard floor
 */
class CalculateCompanyCampaignLowBidFlagJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int CHUNK_SIZE = 500;

    public function __construct(
        protected ?CompanyCampaign $companyCampaign = null,
        protected ?Industry $industry = null,
        protected ?bool $recentCampaigns = false,
        protected ?bool $allCampaigns = false,
    ) {}

    /**
     * @return void
     */
    public function handle(): void
    {
        if ($this->companyCampaign) {
            $this->updateLowBidFlag($this->companyCampaign);
        }
        else if ($this->industry) {
            $campaigns = CompanyCampaign::query()
                ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::FIELD_SERVICE_ID)
                ->where(IndustryService::FIELD_INDUSTRY_ID, $this->industry->id)
                ->select(CompanyCampaign::TABLE . '.*')
                ->get();

            $campaigns->each(fn($campaign) => $this->updateLowBidFlag($campaign));
        }
        else if ($this->recentCampaigns) {
            CompanyCampaign::query()
                ->where(CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES)
                ->orWhere(CompanyCampaign::UPDATED_AT, '>', now()->subWeek())
                ->orWhere(CompanyCampaign::CREATED_AT, '>', now()->subWeek())
                ->chunk(self::CHUNK_SIZE, function(Collection $campaigns) {
                    $campaigns->each(fn($campaign) => $this->updateLowBidFlag($campaign));
                });
        }
        else if ($this->allCampaigns) {
            CompanyCampaign::query()
                ->chunk(self::CHUNK_SIZE, function(Collection $campaigns) {
                    $campaigns->each(fn($campaign) => $this->updateLowBidFlag($campaign));
                });
        }
        else {
            logger()->warning(self::class . " was dispatched with no valid arguments. No CompanyCampaigns were updated.");
        }
    }

    /**
     * @param CompanyCampaign $campaign
     * @return void
     */
    protected function updateLowBidFlag(CompanyCampaign $campaign): void
    {
        $campaign->update([
            CompanyCampaign::FIELD_HAS_LOW_BIDS => $this->companyCampaignHasLowBids($campaign),
        ]);
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @return bool
     */
    protected function companyCampaignHasLowBids(CompanyCampaign $companyCampaign): bool
    {
        $industry = $this->industry ?? $companyCampaign->service->industry;
        $industryUsesCustomPricing = $industry->industryConfiguration?->allow_custom_floor_prices ?? false;

        if ($industryUsesCustomPricing && $companyCampaign->uses_custom_floor_prices) {
            $lowStateBids = CustomFloorPricingBuilder::query()
                ->forCompanyCampaign($companyCampaign->id)
                ->customPricesOnly()
                ->withBids()
                ->select()
                ->aliasPriceColumns()
                ->getQuery()
                ->where(fn(Builder $query) =>
                    $query->orWhere(fn(Builder $query) =>
                        $query->whereNull(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE)
                            ->whereColumn(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE, '<', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE)
                    )->orWhere(fn(Builder $query) =>
                        $query->whereNotNull(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE)
                            ->whereColumn(ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_PRICE, '<', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE)
                    )
                );

            if ($lowStateBids->exists())
                return true;

            $lowCountyBids = CustomFloorPricingBuilder::query()
                ->forCompanyCampaign($companyCampaign->id)
                ->customPricesOnly()
                ->withCounties()
                ->withBids()
                ->select()
                ->aliasPriceColumns()
                ->getQuery()
                ->where(fn(Builder $query) =>
                    $query->orWhere(fn(Builder $query) =>
                        // Where no explicit bid exists...
                        $query->whereNull(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE)
                            ->where(fn(Builder $query) =>
                                $query->orWhere(fn(Builder $query) =>
                                    // If there's a custom county floor price
                                    $query->whereNotNull(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE)
                                        ->where(fn(Builder $query) =>
                                            $query->orWhereColumn(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE, '<', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE)
                                                ->orWhereColumn(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE, '<', ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE)
                                        )
                                )->orWhere(fn(Builder $query) =>
                                    // If there is no custom county floor price, check the custom state floor
                                    $query->whereNull(CustomCampaignCountyFloorPrice::TABLE .'.'. CustomCampaignCountyFloorPrice::FIELD_PRICE)
                                        ->whereColumn(CustomCampaignStateFloorPrice::TABLE .'.'. CustomCampaignStateFloorPrice::FIELD_PRICE, '<', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE)
                                )
                            )
                        )->orWhere(fn(Builder $query) =>
                            // Where an explicit county bid exists, check if its lower than the state OR county floor
                            $query->whereNotNull(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE)
                                ->where(fn(Builder $query) =>
                                    $query->orWhereColumn(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE, '<', ProductStateFloorPrice::TABLE .'.'. ProductStateFloorPrice::FIELD_PRICE)
                                        ->orWhereColumn(ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_PRICE, '<', ProductCountyFloorPrice::TABLE .'.'. ProductCountyFloorPrice::FIELD_PRICE)
                                )
                        )
                    );

            return $lowCountyBids->exists();
        }

        return false;
    }
}
