<?php

namespace App\Jobs;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Bidding\CompanyCampaignBidPriceModule;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Services\DatabaseHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Query\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

/**
 * Cleans up company bid prices after a floor price increase.
 * The job will delay until the end of the day, so if a mistake is made in saving new floor prices,
 *   we won't immediately lose any campaign bids - there is time to reverse the changes if required
 */
class CleanUpRedundantBidPricesJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable;

    protected array $stateFloorPrices = [];

    /**
     * @param int $serviceProductId
     * @param int $propertyTypeId
     * @param int $qualityTierId
     * @param array|null $stateLocationIds
     */
    public function __construct(
        protected int $serviceProductId,
        protected int $propertyTypeId,
        protected int $qualityTierId,
        protected ?array $stateLocationIds = [],
    )
    {
        $this->onQueue('long_running');
        $this->delay = now()->endOfDay();
    }

    /**
     * @return string
     */
    public function uniqueId(): string
    {
        $id = "$this->serviceProductId-$this->propertyTypeId-$this->qualityTierId";
        if (!$this->stateLocationIds)
            return $id;


        sort($this->stateLocationIds);
        return "$id-" . implode('_', $this->stateLocationIds);
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        $removedStateBids = $this->cleanUpStateBids();
        $removedCountyBids = $this->cleanUpCountyBids();
        logger()->debug(self::class . " removed $removedStateBids redundant state bids and $removedCountyBids redundant county bids");
    }

    /**
     * @return int
     */
    protected function cleanUpCountyBids(): int
    {
        $stateIdAbbreviationMap = DB::table(Location::TABLE)
            ->select([
                Location::STATE_ABBREVIATION,
                Location::ID,
            ])->where(Location::TYPE, Location::TYPE_STATE)
            ->get()
            ->reduce(function (array $output, $state) {
                $output[$state->id] = $state->state_abbr;
                return $output;
            }, []);

        $removedRows = 0;

        foreach ($this->stateFloorPrices as $stateLocationId => $stateLocationGroup) {
            $countyLocationIds = DB::table(Location::TABLE)
                ->where([
                    Location::STATE_ABBREVIATION    =>  $stateIdAbbreviationMap[$stateLocationId],
                    Location::TYPE => Location::TYPE_COUNTY
                ])->pluck(Location::ID)
                ->toArray();

            $countyFloorPrices = DB::table(ProductCountyFloorPrice::TABLE)->select([
                ProductCountyFloorPrice::FIELD_SALE_TYPE_ID,
                ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID,
                ProductCountyFloorPrice::FIELD_PRICE,
            ])->where([
                ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID  => $stateLocationId,
                ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID => $this->serviceProductId,
                ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeId,
                ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierId,
            ])->get()
            ->reduce(function (array $output, $floorPrice) {
                $output[$floorPrice->county_location_id] = $output[$floorPrice->county_location_id] ?? [];
                $output[$floorPrice->county_location_id][$floorPrice->sale_type_id] = $floorPrice->price;
                return $output;
            }, []);

            foreach ($countyLocationIds as $countyLocationId) {
                $countyFloorPrices[$countyLocationId] = $countyFloorPrices[$countyLocationId] ?? [];

                foreach ($stateLocationGroup as $saleTypeId => $statePrice) {
                    $countyPrice = $countyFloorPrices[$countyLocationId][$saleTypeId] ?? 0;
                    $targetPrice = max($countyPrice, $statePrice);

                    $removedRows += DB::table(ProductCountyBidPrice::TABLE)
                        ->join(CompanyCampaignBidPriceModule::TABLE, CompanyCampaignBidPriceModule::TABLE .'.'. CompanyCampaignBidPriceModule::FIELD_ID, '=', ProductCountyBidPrice::TABLE .'.'. ProductCountyBidPrice::FIELD_MODULE_ID)
                        ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignBidPriceModule::TABLE .'.'. CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID)
                        ->where(CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES, false)
                        ->where([
                            ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID => $countyLocationId,
                            ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID => $this->serviceProductId,
                            ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeId,
                            ProductCountyBidPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierId,
                            ProductCountyBidPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                            CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES => false,
                        ])->where(ProductCountyBidPrice::FIELD_PRICE, '<=', $targetPrice)
                        ->delete();
                }
            }
        }

        return $removedRows;
    }

    /**
     * @return int
     */
    protected function cleanUpStateBids(): int
    {
        $this->stateFloorPrices = DB::table(ProductStateFloorPrice::TABLE)
            ->select([
                ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
                ProductStateFloorPrice::FIELD_SALE_TYPE_ID,
                ProductStateFloorPrice::FIELD_PRICE,
            ])->where([
                ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $this->serviceProductId,
                ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $this->propertyTypeId,
                ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $this->qualityTierId,
            ])->when($this->stateLocationIds, fn(Builder $query) =>
                $query->whereIn(ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $this->stateLocationIds))
            ->get()
            ->reduce(function (array $output, $floorPrice) {
                $output[$floorPrice->state_location_id] = $output[$floorPrice->state_location_id] ?? [];
                $output[$floorPrice->state_location_id][$floorPrice->sale_type_id] = $floorPrice->price;
                return $output;
            }, []);

        $removedBids = 0;

        foreach ($this->stateFloorPrices as $stateLocationId => $stateLocationGroup) {
            foreach ($stateLocationGroup as $saleTypeId => $floorPrice) {
                $removedBids += DB::table(ProductStateBidPrice::TABLE)
                    ->join(CompanyCampaignBidPriceModule::TABLE, CompanyCampaignBidPriceModule::TABLE .'.'. CompanyCampaignBidPriceModule::FIELD_ID, '=', ProductStateBidPrice::TABLE .'.'. ProductStateBidPrice::FIELD_MODULE_ID)
                    ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignBidPriceModule::TABLE .'.'. CompanyCampaignBidPriceModule::FIELD_CAMPAIGN_ID)
                    ->where(CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES, false)
                    ->where([
                        ProductStateBidPrice::FIELD_STATE_LOCATION_ID   => $stateLocationId,
                        ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID  => $this->serviceProductId,
                        ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID    => $this->propertyTypeId,
                        ProductStateBidPrice::FIELD_QUALITY_TIER_ID     => $this->qualityTierId,
                        ProductStateBidPrice::FIELD_SALE_TYPE_ID        => $saleTypeId,
                        CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES => false,
                    ])->where(
                        ProductStateBidPrice::FIELD_PRICE, '<=', $floorPrice
                    )->delete();
            }
        }

        return $removedBids;
    }
}
