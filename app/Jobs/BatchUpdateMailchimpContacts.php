<?php

namespace App\Jobs;

use App\Contracts\Services\MarketingCampaign\MailchimpEmailMarketingService;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingLogRepository;
use App\Services\MarketingCampaign\MarketingCampaignService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Illuminate\Database\Eloquent\Collection;

class BatchUpdateMailchimpContacts implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Collection $consumers)
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * Execute the job.
     */
    public function handle(MarketingCampaignService $service, MailchimpEmailMarketingService $mailchimpEmailMarketingService): void
    {
        $transformed = $service->transformConsumersToEmailCampaignConsumers($this->consumers);

        $response = $mailchimpEmailMarketingService->batchAddUsersToMailchimp($transformed);

        $createdAndUpdated = array_merge(Arr::get($response, 'new_members', []),Arr::get($response, 'updated_members', []));

        $mailchimpIds = array_column($createdAndUpdated, 'id');

        MarketingCampaignConsumer::query()
            ->whereIn(MarketingCampaignConsumer::FIELD_EXTERNAL_REFERENCE, $mailchimpIds)
            ->update(
                [MarketingCampaignConsumer::FIELD_STATUS => MarketingCampaignConsumerStatus::UPLOADED]
            );

        MarketingLogRepository::log(
            message: "Mailchimp Batch Update Complete",
            namespace: MarketingLogType::MAILCHIMP_BATCH_UPDATE,
            level: LogLevel::INFO,
            context: [
                'data' => [
                    'email_updated_count' => count($createdAndUpdated)
                ]
            ],
        );
    }
}
