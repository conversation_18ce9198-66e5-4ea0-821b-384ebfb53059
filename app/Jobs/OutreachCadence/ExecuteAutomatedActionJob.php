<?php

namespace App\Jobs\OutreachCadence;

use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Services\OutreachCadence\ActionLogger;
use App\Services\OutreachCadence\CompanyCadenceRoutineService;
use App\Services\OutreachCadence\EmailService;
use App\Services\OutreachCadence\SmsService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ExecuteAutomatedActionJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public CompanyCadenceScheduledGroupAction $action)
    {
        $this->onQueue(config('queue.named_queues.default'));
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle(CompanyCadenceRoutineService $service)
    {
        // handle deleted routine
        if (!$this->action->group?->routine) {
            ActionLogger::routineDeleted($this->action);
            return;
        }

        // handle skip
        if ($this->action->group->skip) {
            $service->skipGroup($this->action->group);
            return;
        }

        // handle action
        try {
            switch ($this->action->action_type) {
                case CompanyCadenceScheduledGroupAction::ACTION_TYPE_EMAIL:
                    $this->handleEmail();
                    break;
                case  CompanyCadenceScheduledGroupAction::ACTION_TYPE_SMS:
                    $this->handleSms();
                    break;
            }
        } catch (Exception $e) {
            ActionLogger::unexpectedError($this->action, "issue handling delivery", $e);
        }

        // mark action/group complete and proceed
        $service->concludeAction($this->action);
    }

    /**
     * @return void
     */
    private function handleEmail(): void
    {
        /** @var EmailService $emailService */
        $emailService = app(EmailService::class);
        $emailService->execute($this->action);
    }

    /**
     * @return void
     */
    private function handleSms(): void
    {
        /** @var SmsService $smsService */
        $smsService = app(SmsService::class);
        $smsService->execute($this->action);
    }
}
