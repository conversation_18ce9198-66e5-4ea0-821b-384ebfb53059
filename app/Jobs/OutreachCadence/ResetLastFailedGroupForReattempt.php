<?php

namespace App\Jobs\OutreachCadence;

use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ResetLastFailedGroupForReattempt implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @return void
     */
    public function __construct(private readonly int $companyCadenceRoutineId)
    {
        $this->onQueue(config('queue.named_queues.default'));
    }

    /**
     * @param CompanyCadenceRoutineRepository $repository
     * @return void
     */
    public function handle(CompanyCadenceRoutineRepository $repository): void
    {
        $routine = $repository->find($this->companyCadenceRoutineId);
        if ($routine) {
            $lastFailedGroup = $repository->getLastFailedGroup($routine);
            if ($lastFailedGroup)
                $repository->resetGroup($lastFailedGroup);
        }
    }
}
