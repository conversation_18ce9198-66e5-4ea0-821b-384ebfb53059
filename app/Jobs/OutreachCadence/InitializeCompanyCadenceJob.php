<?php

namespace App\Jobs\OutreachCadence;

use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use App\Services\OutreachCadence\CompanyCadenceRoutineService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InitializeCompanyCadenceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @return void
     */
    public function __construct(
        public int  $companyId,
        public int  $routineConfigId,
        public bool $useAccountManager,
        public int  $userId,
    )
    {
        $this->onQueue(config('queue.named_queues.default'));
    }

    /**
     * @param CompanyCadenceRoutineRepository $repository
     * @param CompanyCadenceRoutineService $service
     * @return void
     * @throws Exception
     */
    public function handle(CompanyCadenceRoutineRepository $repository, CompanyCadenceRoutineService $service): void
    {
        // exit if there is already an active routine for this company
        if($repository->companyHasOngoingRoutine($this->companyId))
            return;

        $companyRoutine = $repository->createCompanyRoutineFromConfig($this->companyId, $this->routineConfigId, $this->useAccountManager, $this->userId);
        $service->scheduleNextGroup($companyRoutine);
    }
}
