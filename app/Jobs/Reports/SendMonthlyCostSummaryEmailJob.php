<?php

namespace App\Jobs\Reports;

use App\Mail\Reports\MonthlyCostSummaryEmail;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\BaseCompanyCampaignModule;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendMonthlyCostSummaryEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $maxExceptions = 3;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $companyId,
        public ?int $companyUserId = null
    ) {
        $this->onQueue(config('queue.named_queues.email_notification'));
    }


    public function handle(): void
    {
        try {
            $company = Company::find($this->companyId);
            if (!$company) {
                return;
            }

            $costSummaryData = $this->getMonthlyCostSummaryData();
            if ($costSummaryData->isEmpty()) {
                return;
            }

            $contacts = $this->getCompanyContacts($company, $this->companyUserId);
            if ($contacts->isEmpty()) {
                return;
            }
            $this->sendEmailToContacts($contacts, $costSummaryData, $company);
        } catch (\Exception $e) {
            logger()->error('Failed to send monthly cost summary email: ' . $e->getMessage());
        }
    }
    /**
     * Get monthly cost summary data grouped by company campaign
     * Includes ProductAssignments from both active and soft deleted CompanyCampaigns
     */
    private function getMonthlyCostSummaryData(): Collection
    {
        $startOfMonth = Carbon::now()->startOfMonth();
        $endOfMonth = Carbon::now();

        $campaignSubquery = CompanyCampaign::withTrashed()
            ->select([CompanyCampaign::FIELD_ID, CompanyCampaign::FIELD_NAME])
            ->toSql();

        return ProductAssignment::query()
            ->select([
                'campaigns.' . CompanyCampaign::FIELD_ID . ' as campaign_id',
                'campaigns.' . CompanyCampaign::FIELD_NAME . ' as campaign_name',
                DB::raw('SUM(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COST . ') as total_cost'),
                DB::raw('COUNT(' . ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID . ') as lead_count')
            ])
            ->join(Budget::TABLE, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_BUDGET_ID, '=', Budget::TABLE . '.' . Budget::FIELD_ID)
            ->join(BudgetContainer::TABLE, Budget::TABLE . '.' . Budget::FIELD_BUDGET_CONTAINER_ID, '=', BudgetContainer::TABLE . '.' . BaseCompanyCampaignModule::FIELD_ID)
            ->leftJoin(DB::raw("({$campaignSubquery}) as campaigns"),
                BudgetContainer::TABLE . '.' . BudgetContainer::FIELD_COMPANY_CAMPAIGN_ID, '=', 'campaigns.' . CompanyCampaign::FIELD_ID)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $this->companyId)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_CHARGEABLE, true)
            ->whereBetween(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_DELIVERED_AT, [$startOfMonth, $endOfMonth])
            ->groupBy([
                'campaigns.' . CompanyCampaign::FIELD_ID,
                'campaigns.' . CompanyCampaign::FIELD_NAME
            ])
            ->get()
            ->map(function ($item) {
                return [
                    'campaign_name' => $item->campaign_name ?? 'Unknown Campaign',
                    'total_cost' => number_format($item->total_cost, 2),
                    'lead_count' => $item->lead_count,
                ];
            });
    }

    /**
     * Get company contacts for email sending
     * If companyUserId is provided, returns that specific user (if valid)
     * Otherwise returns all active company contacts
     */
    private function getCompanyContacts(Company $company, ?int $companyUserId = null): Collection
    {
        $query = CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company->id)
            ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
            ->whereNotNull(CompanyUser::FIELD_EMAIL)
            ->where(CompanyUser::FIELD_EMAIL, '!=', '');

        if ($companyUserId) {
            // Get specific company user
            $query->where(CompanyUser::FIELD_ID, $companyUserId);
        } else {
            // Get all active contacts
            $query->where(CompanyUser::FIELD_IS_CONTACT, CompanyUser::USER_IS_CONTACT);
        }

        return $query->get();
    }

    /**
     * Send email to all active contacts
     */
    private function sendEmailToContacts(Collection $contacts, Collection $costSummaryData, Company $company): void
    {
        foreach ($contacts as $contact) {
            try {
                Mail::to($contact->email)->send(
                    new MonthlyCostSummaryEmail($costSummaryData, $company, $contact)
                );
            } catch (\Exception $e) {
                \Log::error('Failed to send monthly cost summary email', [
                    'company_id' => $this->companyId,
                    'contact_email' => $contact->email,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }
}
