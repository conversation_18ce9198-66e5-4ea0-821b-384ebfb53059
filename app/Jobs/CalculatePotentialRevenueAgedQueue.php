<?php

namespace App\Jobs;

use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\RecycledLeads\LeadProcessingAged;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculatePotentialRevenueAgedQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected int $agedQueueId) {}

    /**
     * Execute the job.
     */
    public function handle(ConsumerProjectProcessingService $service, MultiProductAssignmentStrategyContract $strategyContract): void
    {
        /** @var LeadProcessingAged $queue */
        $queue = LeadProcessingAged::query()->find($this->agedQueueId);

        if ($queue) {
            $consumerProject = $service->prepareConsumerProject(
                $queue->consumerProduct->consumer,
                $queue->consumerProduct->address,
                now()
            );
            $campaigns = $service->getAvailableCampaigns($consumerProject);
            $proposedAssignments = $strategyContract->calculate(
                $consumerProject,
                $campaigns,
                $service->getPotentialProductTypes($consumerProject),
                []
            );

            $queue->possible_revenue = $proposedAssignments->sum(ProposedProductAssignment::DATA_FIELD_PRICE);
            $queue->save();
        }
    }
}
