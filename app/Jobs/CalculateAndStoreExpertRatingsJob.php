<?php

namespace App\Jobs;

use App\Models\Legacy\EloquentCompany;
use App\Repositories\Legacy\CompanyRankingsRepository;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class CalculateAndStoreExpertRatingsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     *
     * @param CompanyRankingsRepository $expertRatingRepository
     * @param CompanyRepository $companyRepository
     * @return void
     */
    public function handle(CompanyRankingsRepository $expertRatingRepository, CompanyRepository $companyRepository): void
    {
        // Performance.
        DB::disableQueryLog();

        EloquentCompany::query()
            ->with(EloquentCompany::RELATION_MI_COMPANY)
            ->whereHas(EloquentCompany::RELATION_COMPANY_RANKING_VALUE)
            ->chunk(100, function($legacyCompanies) use ($expertRatingRepository, $companyRepository) {
                DB::beginTransaction();

                /** @var EloquentCompany $legacyCompany */
                foreach($legacyCompanies as $legacyCompany) {
                    $score = $expertRatingRepository->getRankingScore($legacyCompany);
                    $companyRepository->updateCompanyData($legacyCompany->miCompany, [
                        'expert_rating_overall_score' => round($score, 2)
                    ]);
                }

                DB::commit();
            });
    }
}
