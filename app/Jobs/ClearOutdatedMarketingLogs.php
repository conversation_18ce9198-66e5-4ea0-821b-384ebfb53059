<?php

namespace App\Jobs;

use App\Enums\GlobalConfigurationKey;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\MarketingCampaign\MarketingLogRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Fluent;

class ClearOutdatedMarketingLogs implements ShouldQueue
{
    const int DEFAULT_MARKETING_LOG_LIFESPAN_DAYS = 60;

    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {}

    /**
     * Execute the job.
     */
    public function handle(GlobalConfigurationRepository $globalConfigurationRepository, MarketingLogRepository $logRepository): void
    {
        $payload = new Fluent($globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::MARKETING)?->toArray() ?? []);

        $lifespan = $payload->get("data." . GlobalConfigurationMarketingField::MARKETING_LOG_LIFESPAN_DAYS->value, self::DEFAULT_MARKETING_LOG_LIFESPAN_DAYS);
        $lifespan = filter_var($lifespan, FILTER_VALIDATE_INT) !== false ? (int)$lifespan : self::DEFAULT_MARKETING_LOG_LIFESPAN_DAYS;

        $cutoffDate = now()->subDays($lifespan)->startOfDay();

        $logRepository->clearLogs($cutoffDate);
    }
}
