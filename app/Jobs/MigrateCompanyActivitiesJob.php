<?php

namespace App\Jobs;

use App\Enums\ActivityType;
use App\Models\Action;
use App\Models\ActivityFeed;
use App\Models\Call;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Models\User;
use App\Repositories\ActivityFeedRepository;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateCompanyActivitiesJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    private ActivityFeedRepository $activityFeedRepository;

    public function __construct(
        private Collection $companyIds,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION);
        $this->activityFeedRepository = app(ActivityFeedRepository::class);
    }

    public function handle() {
        EloquentCompany::query()
            ->with([

            ])
            ->whereIn(EloquentCompany::ID, $this->companyIds)
            ->chunk(500, function (Collection $companies) {
                DB::beginTransaction();
                foreach ($companies as $company) {
                    $this->migrateCompanyActivities($company);
                }
                DB::commit();
            });
    }

    protected function migrateCompanyActivities(EloquentCompany $company) {
        $calls = $company->calls()->whereDoesntHave('activity')->get();
        /** @var Call $call */
        foreach($calls as $call) {
            $userId = $call->{Call::RELATION_PHONE}?->primaryUser()?->{User::FIELD_ID} ?? 0;

            $this->createActivityWithTimestamp(
                $call->{Call::FIELD_ID},
                ActivityType::CALL,
                $company->{EloquentCompany::ID},
                $userId,
                $call->{Call::CREATED_AT}
            );
        }

        //todo add company texts

        $tasks = Task::query()
            ->where(function ($query) use ($company) {
                $query->whereHas(Task::RELATION_RUNNING_WORKFLOW, function ($query) use ($company) {
                    $query->where(RunningWorkflow::VIRTUAL_FIELD_EVENT_COMPANY_ID, $company->{EloquentCompany::ID});
                    })
                ->orWhere(Task::VIRTUAL_FIELD_MANUAL_COMPANY_ID, $company->{EloquentCompany::ID});
            })
            ->whereDoesntHave('activity')->get();
        /** @var Task $task */
        foreach($tasks as $task) {
            $userId = $task->{Task::FIELD_ASSIGNED_USER_ID} ?? 0;
            $this->createActivityWithTimestamp(
                $task->{Task::FIELD_ID},
                ActivityType::TASK,
                $company->{EloquentCompany::ID},
                $userId,
                $task->{Task::CREATED_AT}
            );
        }

        Action::query()
            ->where(Action::FIELD_FOR_ID, $company->{EloquentCompany::ID})
            ->where(Action::FIELD_FOR_RELATION_TYPE, Action::RELATION_TYPE_COMPANY)
            ->whereDoesntHave('activity')
            ->get()
            ->each(fn(Action $action) =>
                $this->createActivityWithTimestamp(
                    $action->{Action::FIELD_ID},
                    ActivityType::ACTION,
                    $company->{EloquentCompany::ID},
                    $action->{Action::FIELD_FROM_USER_ID} ?? 0,
                    $action->{Action::CREATED_AT}
                )
            );

    }

    /**
     * Same as repository method, but allows forcing created_at and modified_at
     * @param int $itemId
     * @param ActivityType $itemType
     * @param int $companyId
     * @param int $userId
     * @param Carbon|null $createdAt
     * @return void
     */
    protected function createActivityWithTimestamp(int $itemId, ActivityType $itemType, int $companyId, int $userId, ?Carbon $createdAt = null): void {
        $createdAt = $createdAt ?? Carbon::now();
        ActivityFeed::query()->create([
            ActivityFeed::FIELD_ITEM_ID         => $itemId,
            ActivityFeed::FIELD_ITEM_TYPE       => $itemType->value,
            ActivityFeed::FIELD_COMPANY_ID      => $companyId,
            ActivityFeed::FIELD_USER_ID         => $userId,
            ActivityFeed::CREATED_AT            => $createdAt,
            ActivityFeed::UPDATED_AT            => $createdAt,
        ]);
    }

}
