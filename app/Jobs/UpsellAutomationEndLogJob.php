<?php

namespace App\Jobs;

use App\Services\Odin\UpsellAutomationService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class UpsellAutomationEndLogJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param int $logId
     * @param array $config
     * @param int $totalWorkerJobs
     * @param int $failedWorkerJobs
     */
    public function __construct(
        protected int $logId,
        protected array $config,
        protected int $totalWorkerJobs = 0,
        protected int $failedWorkerJobs = 0,
    )
    {
        $this->queue = QueueHelperService::QUEUE_NAME_LONG_RUNNING_SINGLE;
    }

    /**
     * @param UpsellAutomationService $upsellAvailableLegsService
     * @return void
     */
    public function handle(UpsellAutomationService $upsellAvailableLegsService): void
    {
        $upsellAvailableLegsService->setConfig($this->config);
        $upsellAvailableLegsService->endAutomationLog($this->logId, $this->totalWorkerJobs, $this->failedWorkerJobs);
    }
}
