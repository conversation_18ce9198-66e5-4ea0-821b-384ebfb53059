<?php

namespace App\Jobs;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Http\Requests\Odin\v2\StoreConsumerRequest;
use App\Models\Odin\Consumer;
use App\Models\Odin\IndustryService;
use App\Models\TestProduct;
use App\Services\EmailTemplates\EmailTemplateService;
use App\Services\EnginesService;
use App\Services\Odin\API\OdinAuthoritativeAPIService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class SendEmailCalculatorResultToConsumer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param Consumer|TestProduct $consumer
     * @param array $payload
     * @param string $engineName
     */
    public function __construct(protected Consumer|TestProduct $consumer, protected array $payload, protected string $engineName) {}

    /**
     * @param EmailTemplateService $service
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(EmailTemplateService $service): void
    {
        $service->sendConsumerCalculatorResult(
            consumer: $this->consumer,
            engineName: $this->engineName,
            calcOutputs: Arr::get($this->payload, StoreConsumerRequest::CONSUMER_KEY .'.'. $this->engineName, [])
        );

        if($this->consumer instanceof TestProduct)
            $this->consumer->delete();
    }

}