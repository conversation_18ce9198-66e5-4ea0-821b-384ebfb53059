<?php

namespace App\Jobs;

use App\Models\Legacy\EloquentQuote;
use App\Repositories\LeadProcessing\LeadProcessingQueueConstraintsRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AddConstraintsToLeadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public int $leadId)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function handle(LeadProcessingQueueConstraintsRepository $repository)
    {
        /** @var EloquentQuote $lead */
        $lead = EloquentQuote::query()->find($this->leadId);

        if($lead)
            $repository->saveLeadProcessingQueueConstraintsBucketFlags($lead);
    }
}
