<?php

namespace App\Jobs\LegacyMigrations;

use App\Contracts\Services\AddressIdentificationServiceInterface;
use App\Models\Legacy\EloquentAddress;
use App\Models\Odin\Address;
use App\Services\AddressIdentification\AddressIdentificationServiceFactory;
use App\Services\QueueHelperService;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateLegacyAddressesJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @var AddressIdentificationServiceInterface
     */
    private AddressIdentificationServiceInterface $addressIdentificationService;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(private Collection $addressIds)
    {
        $this->connection = QueueHelperService::QUEUE_CONNECTION;
        $this->queue = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
        $this->addressIdentificationService = AddressIdentificationServiceFactory::make();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $addresses = EloquentAddress::query()
            ->whereIn(EloquentAddress::ID, $this->addressIds)
            ->get();

        DB::beginTransaction();
        foreach ($addresses as $address) {
            $this->migrateAddress($address);
        }
        DB::commit();
    }

    /**
     * @param EloquentAddress $address
     * @throws Exception
     */
    protected function migrateAddress(EloquentAddress $address): void
    {
        $placeId = $this->addressIdentificationService->getIdFromAddressComponents(
            $address->{EloquentAddress::ADDRESS1},
            $address->{EloquentAddress::CITY},
            $address->{EloquentAddress::STATE_ABBR},
            $address->{EloquentAddress::ZIP_CODE},
            $address->{EloquentAddress::ADDRESS2},
        );
        try {
            Address::query()->updateOrCreate(
                [
                    Address::FIELD_LEGACY_ID => $address->{EloquentAddress::ID}
                ],
                [
                    Address::FIELD_ADDRESS_1 => $address->{EloquentAddress::ADDRESS1},
                    Address::FIELD_ADDRESS_2 => $address->{EloquentAddress::ADDRESS2},
                    Address::FIELD_CITY => $address->{EloquentAddress::CITY},
                    Address::FIELD_STATE => $address->{EloquentAddress::STATE_ABBR},
                    Address::FIELD_ZIP_CODE => $address->{EloquentAddress::ZIP_CODE},
                    Address::FIELD_COUNTRY => $address->{EloquentAddress::COUNTRY},
                    Address::FIELD_LATITUDE => $address->{EloquentAddress::LATITUDE},
                    Address::FIELD_LONGITUDE => $address->{EloquentAddress::LONGITUDE},
                    Address::FIELD_PLACE_ID => $placeId
                ]
            );
        }
        catch(Exception $e) {
            logger()->error("Error migrating legacy address {$address->{EloquentAddress::ID}}: Line ".$e->getLine().'. '.$e->getMessage());

            throw $e;
        }
    }
}
