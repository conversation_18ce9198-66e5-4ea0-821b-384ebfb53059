<?php

namespace App\Jobs\LegacyMigrations;

use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\CompanyUser;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Collection;

class MigrateLegacyCompanyContactsNotesJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private Collection $contactIds
    )
    {
        $this->connection = QueueHelperService::QUEUE_CONNECTION;
        $this->queue = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        EloquentCompanyContact::withTrashed()
            ->whereIn(EloquentCompanyContact::FIELD_CONTACT_ID, $this->contactIds)
            ->chunk(500, function (Collection $contacts) {
                foreach ($contacts as $legacyContact) {
                    try {
                        $companyUser = CompanyUser::query()->where(CompanyUser::FIELD_LEGACY_ID, $legacyContact->{EloquentCompanyContact::FIELD_CONTACT_ID})->firstOrFail();
                        $companyUser->{CompanyUser::FIELD_NOTES} = $legacyContact->{EloquentCompanyContact::FIELD_NOTES};
                        $companyUser->save();
                    }
                    catch(\Exception $e) {
                        logger()->error("Error migrating legacy company contacts notes: ".$e->getMessage());

                        throw $e;
                    }
                }
            });
    }
}