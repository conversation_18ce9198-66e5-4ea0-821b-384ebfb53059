<?php

namespace App\Jobs\LegacyMigrations;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentQuoteLog;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ProductRepository;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Bus\Batchable;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateLegacyQuoteCompaniesJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param Collection $quoteCompanyIds
     * @param array $saleTypes
     * @param bool $runMigratedCheck
     */
    public function __construct(
        private Collection     $quoteCompanyIds,
        private readonly array $saleTypes,
        private readonly bool $runMigratedCheck = true
    )
    {
        $this->connection = QueueHelperService::QUEUE_CONNECTION;
        $this->queue = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle(ProductRepository $productRepository)
    {
        if($this->runMigratedCheck) {
            $this->migratedCheck();
            if ($this->quoteCompanyIds->isEmpty()) return;
        }

        $this->getEloquentQuotesQuery($productRepository)->chunk(500, function (Collection $quoteCompanies) {
            $this->migrateProductAssignments($quoteCompanies);
            $this->migrateRejections($quoteCompanies);
        });
    }

    /**
     * @return void
     */
    private function migratedCheck(): void
    {
        $migratedQuoteCompanyIds = ProductAssignment::query()
            ->select(ProductAssignment::FIELD_LEGACY_ID)
            ->whereIn(ProductAssignment::FIELD_LEGACY_ID, $this->quoteCompanyIds)
            ->pluck(ProductAssignment::FIELD_LEGACY_ID)
            ->toArray();

        $migratedQuoteCompanyIds = array_flip($migratedQuoteCompanyIds);

        $this->quoteCompanyIds = $this->quoteCompanyIds->filter(fn($id) => !array_key_exists($id, $migratedQuoteCompanyIds));
    }

    /**
     * @param $productRepository
     * @return Builder
     */
    private function getEloquentQuotesQuery($productRepository): Builder
    {
        $leadProductId = $productRepository->getLeadProductId();

        return EloquentQuoteCompany::query()
            ->select([
                EloquentQuoteCompany::TABLE.'.*',
                DatabaseHelperService::database().'.'.ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID .' as consumer_product_id',
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_ID .' as odin_company_id',
            ])
            ->with([
                EloquentQuoteCompany::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION.'.'.LeadCampaignSalesTypeConfiguration::RELATION_LEAD_SALES_TYPE
            ])->join(
                DatabaseHelperService::database().'.'.Consumer::TABLE,
                DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID,
                '=',
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID,
            )->join(
                DatabaseHelperService::database().'.'.ConsumerProduct::TABLE,
                DatabaseHelperService::database().'.'.ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID,
                '=',
                DatabaseHelperService::database().'.'.Consumer::TABLE.'.'.Consumer::FIELD_ID
            )->join(
                DatabaseHelperService::database().'.'.ServiceProduct::TABLE,
                DatabaseHelperService::database().'.'.ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_ID,
                '=',
                DatabaseHelperService::database().'.'.ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
            )->join(
                DatabaseHelperService::database().'.'.Company::TABLE,
                DatabaseHelperService::database().'.'.Company::TABLE.'.'.Company::FIELD_LEGACY_ID,
                '=',
                EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID
            )
            ->where(DatabaseHelperService::database().'.'.ServiceProduct::TABLE.'.'.ServiceProduct::FIELD_PRODUCT_ID, $leadProductId)
            ->whereIn(EloquentQuoteCompany::ID, $this->quoteCompanyIds);
    }

    /**
     * @param Collection $quoteCompanies
     * @return void
     */
    private function migrateProductAssignments(Collection $quoteCompanies): void
    {
        DB::beginTransaction();

        $data = [];

        foreach ($quoteCompanies as $quoteCompany) {
            if ($quoteCompany->{'consumer_product_id'}) {
                $data[] = $this->getProductAssignmentMigrationArray($quoteCompany, $quoteCompany->{'consumer_product_id'});
            } else {
                // Should only fall through here with Dev data
                $quoteCompanyId   = $quoteCompany->{EloquentQuoteCompany::ID};
                $attemptedQuoteId = $quoteCompany->{EloquentQuoteCompany::QUOTE_ID};
                logger()->warning("QuoteCompany {$quoteCompanyId} - could not find linked Quote/ConsumerProduct with id {$attemptedQuoteId}");
            }
        }

        ProductAssignment::insert($data);

        DB::commit();
    }

    /**
     * @param Collection $quoteCompanies
     * @return void
     */
    private function migrateRejections(Collection $quoteCompanies): void
    {
        DB::beginTransaction();

        $newProductAssignments = ProductAssignment::query()
            ->select([
                ProductAssignment::TABLE.'.*',
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGE_STATUS,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID .' as legacy_company_id',
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::REJECT_NOTES,

            ])
            ->whereIn(ProductAssignment::FIELD_LEGACY_ID, $quoteCompanies->pluck(EloquentQuoteCompany::QUOTE_COMPANY_ID)->toArray())
            ->join(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_COMPANY_ID,
                '=',
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_LEGACY_ID
            )
            ->get();

        $data = [];

        foreach($newProductAssignments as $newProductAssignment) {
            if ($newProductAssignment->{EloquentQuoteCompany::CHARGE_STATUS} === EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED) {
                $data[] = [
                    ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID => $newProductAssignment->{ProductAssignment::FIELD_ID},
                    ProductRejection::FIELD_REASON                => $newProductAssignment->{EloquentQuoteCompany::REJECT_NOTES},
                    ProductRejection::FIELD_COMPANY_USER_ID       => $this->retrieveCompanyUserId(
                        $newProductAssignment->{EloquentQuoteCompany::QUOTE_ID},
                        $newProductAssignment->{'legacy_company_id'}
                    )
                ];
            }
        }

        ProductRejection::insert($data);

        DB::commit();
    }

    /**
     * @param EloquentQuoteCompany $quoteCompany
     * @param int $consumerProductId
     * @return array
     */
    private function getProductAssignmentMigrationArray(EloquentQuoteCompany $quoteCompany, int $consumerProductId): array
    {
        return [
            ProductAssignment::FIELD_COMPANY_ID          => $quoteCompany->{'odin_company_id'},
            ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $consumerProductId,
            ProductAssignment::FIELD_LEGACY_ID           => $quoteCompany->{EloquentQuoteCompany::QUOTE_COMPANY_ID},
            ProductAssignment::FIELD_COST                => $quoteCompany->{EloquentQuoteCompany::COST},
            ProductAssignment::FIELD_CHARGEABLE          => $quoteCompany->{EloquentQuoteCompany::CHARGEABLE},
            ProductAssignment::FIELD_DELIVERED           => $quoteCompany->{EloquentQuoteCompany::DELIVERED},
            ProductAssignment::FIELD_EXCLUDE_BUDGET      => $this->determineExcludeBudget($quoteCompany),
            ProductAssignment::FIELD_SALE_TYPE_ID        => $this->determineSaleTypeId($quoteCompany),
            ProductAssignment::FIELD_DELIVERED_AT        => $quoteCompany->{EloquentQuoteCompany::TIMESTAMP_DELIVERED},
            ProductAssignment::FIELD_REJECTION_EXPIRY    => $quoteCompany->{EloquentQuoteCompany::TIMESTAMP_REJECTION_EXPIRY},
            ProductAssignment::CREATED_AT                => Carbon::createFromTimestamp($quoteCompany->{EloquentQuoteCompany::TIMESTAMP_ADDED}),
            ProductAssignment::FIELD_OFF_HOUR_SALE       => $quoteCompany->{EloquentQuoteCompany::OFF_HOUR_SALE}
        ];
    }

    /**
     * @param EloquentQuoteCompany $quoteCompany
     * @return int
     */
    protected function determineExcludeBudget(EloquentQuoteCompany $quoteCompany): int
    {
        if(empty($quoteCompany->{EloquentQuoteCompany::INCLUDE_IN_BUDGET})
            && $quoteCompany->{EloquentQuoteCompany::NON_BUDGET_PREMIUM_LEAD} == 1) {
            return ProductAssignment::EXCLUDE_BUDGET_SUPER_PREMIUM;
        }
        else if(empty($quoteCompany->{EloquentQuoteCompany::INCLUDE_IN_BUDGET})) {
            return ProductAssignment::EXCLUDE_BUDGET_TRIAL;
        }

        return 0;
    }

    /**
     * @param EloquentQuoteCompany $quoteCompany
     * @return int
     */
    protected function determineSaleTypeId(EloquentQuoteCompany $quoteCompany): int
    {
        $legacySaleTypeKey = $quoteCompany
            ->{EloquentQuoteCompany::RELATION_LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION}
            ?->{LeadCampaignSalesTypeConfiguration::RELATION_LEAD_SALES_TYPE}
            ?->{LeadSalesType::KEY_VALUE};

        if($legacySaleTypeKey) {
            $legacySaleTypeKey = ucwords(strtolower(str_replace('_', ' ', $legacySaleTypeKey)));

            return $this->saleTypes[$legacySaleTypeKey];
        }
        else {
            $numSoldTo = EloquentQuoteCompany::query()
                ->where(EloquentQuoteCompany::QUOTE_ID, $quoteCompany->{EloquentQuoteCompany::QUOTE_ID})
                ->where(EloquentQuoteCompany::CHARGEABLE, true)
                ->where(EloquentQuoteCompany::DELIVERED, true)
                ->count();

            return match($numSoldTo) {
                1 => $this->saleTypes["Exclusive"],
                2 => $this->saleTypes["Duo"],
                3 => $this->saleTypes["Trio"],
                4 => $this->saleTypes["Quad"],
                default => 0
            };
        }
    }

    /**
     * @param int $quoteId
     * @param int $legacyCompanyId
     * @return int
     */
    protected function retrieveCompanyUserId(int $quoteId, int $legacyCompanyId): int
    {
        $rejectLog = EloquentQuoteLog::query()
            ->where(EloquentQuoteLog::QUOTE_ID, $quoteId)
            ->where(EloquentQuoteLog::DATA, 'LIKE', "%($legacyCompanyId) marked as rejected%")
            ->orderBy(EloquentQuoteLog::TIMESTAMP_ADDED, 'DESC')
            ->first();

        if($rejectLog) {
            return CompanyUser::query()
                ->where(CompanyUser::FIELD_LEGACY_ID, $rejectLog->{EloquentQuoteLog::ADDED_BY_USER_ID} ?? 0)
                ->where(CompanyUser::FIELD_IS_CONTACT, '!=', true)
                ->first()?->{CompanyUser::FIELD_ID} ?? 0;
        }

        return 0;
    }
}
