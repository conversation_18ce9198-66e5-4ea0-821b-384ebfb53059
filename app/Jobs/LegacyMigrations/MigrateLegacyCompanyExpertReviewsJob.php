<?php

namespace App\Jobs\LegacyMigrations;

use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyExpertReview;
use App\Models\User;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Exception;

class MigrateLegacyCompanyExpertReviewsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private Collection $reviewIds
    )
    {
        $this->connection   = QueueHelperService::QUEUE_CONNECTION;
        $this->queue        = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        $reviews = DB::connection('readonly')
            ->table('company_expert_reviews')
            ->whereIn('id', $this->reviewIds)
            ->get();

        DB::beginTransaction();
        forEach($reviews as $review) {
            $this->migrateCompanyExpertReview(collect($review));
        }
        DB::commit();
    }

    /**
     * @param Collection $review
     * @throws Exception
     */
    private function migrateCompanyExpertReview(Collection $review): void
    {
        try {
            $userId = User::query()->select(User::FIELD_ID)->where(User::FIELD_LEGACY_USER_ID, $review->get('user_id'))->first()?->pluck(User::FIELD_ID);
            $companyId = Company::query()->select(Company::FIELD_ID)->where(Company::FIELD_LEGACY_ID, $review->get('company_id'))->firstOrFail()->pluck(Company::FIELD_ID);
            CompanyExpertReview::query()->create([
                CompanyExpertReview::FIELD_COMPANY_ID       => $companyId,
                CompanyExpertReview::FIELD_BODY             => $review->get('review'),
                CompanyExpertReview::FIELD_LEGACY_ID        => $review->get('id'),
                CompanyExpertReview::FIELD_USER_ID          => $userId,
                CompanyExpertReview::FIELD_LEGACY_USER_ID   => $review->get('user_id'),
                CompanyExpertReview::CREATED_AT             => $review->get('created_at'),
                CompanyExpertReview::UPDATED_AT             => $review->get('updated_at'),
                CompanyExpertReview::FIELD_DELETED_AT       => $review->get('deleted_at'),
            ]);
        }
        catch(Exception $e) {
            logger()->error("Error migrating legacy expert review {$review->get('id')}: Line ".$e->getLine().'. '.$e->getMessage());
            throw $e;
        }
    }
}