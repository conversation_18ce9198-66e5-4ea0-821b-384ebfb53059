<?php

namespace App\Jobs\LegacyMigrations;

use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyAddress;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyData;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyService;
use App\Models\PaymentCollection;
use App\Repositories\Legacy\CompanyRepository;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use Throwable;

class MigrateLegacyCompaniesJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    private CompanyRepository $companyRepository;

    /**
     * @param Collection $companyIds
     * @param array $industries
     * @param array $industryServices
     */
    public function __construct(
        private Collection $companyIds,
        private array $industries,
        private array $industryServices,
        private bool $syncLegacyId = true
    )
    {
        $this->onConnection(QueueHelperService::QUEUE_CONNECTION);
        $this->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION);
        $this->companyRepository = app(CompanyRepository::class);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        EloquentCompany::query()
            ->with([
                EloquentCompany::RELATION_CAMPAIGNS,
                EloquentCompany::RELATION_COMPANY_ADDRESSES,
                EloquentCompany::RELATION_COMPANY_ADDRESSES.'.'.EloquentCompanyAddress::RELATION_ADDRESS,
                EloquentCompany::RELATION_QUOTE_COMPANIES => function($relation) {
                    $relation
                        ->where(EloquentQuoteCompany::SOLD_STATUS, EloquentQuoteCompany::VALUE_SOLD_STATUS_SOLD)
                        ->where(EloquentQuoteCompany::CHARGE_STATUS, EloquentQuoteCompany::CHARGEABLE)
                        ->where(EloquentQuoteCompany::DELIVERED, EloquentQuoteCompany::IS_DELIVERED);
                },
                EloquentCompany::RELATION_INVOICES => function($relation) {
                    $relation->whereNotIn(
                        EloquentInvoice::STATUS,
                        [
                            EloquentInvoice::VALUE_STATUS_PAID,
                            EloquentInvoice::VALUE_STATUS_CANCELLED,
                            EloquentInvoice::VALUE_STATUS_BANK_REFUND,
                            EloquentInvoice::VALUE_STATUS_REFUND,
                            EloquentInvoice::VALUE_STATUS_CHARGEBACK_WON
                        ]
                    );
                },
                EloquentCompany::RELATION_INVOICES.'.'.EloquentInvoice::RELATION_INVOICE_ITEMS
            ])
            ->whereIn(EloquentCompany::ID, $this->companyIds)
            ->chunk(500, function (Collection $companies) {
                $paymentMethodValidities = [];
                try {
                    $paymentMethodValidities = $this->companyRepository->getCompaniesPaymentMethodStatuses($companies->pluck(EloquentCompany::ID)->toArray());
                }
                catch(Throwable $e) {
                    logger()->error(__METHOD__.": ".$e->getMessage());
                }

                $newAddressIds = Address::query()
                    ->whereIn(Address::FIELD_LEGACY_ID, $companies->pluck(EloquentCompany::RELATION_COMPANY_ADDRESSES.'.*.'.EloquentCompanyAddress::FIELD_ADDRESS_ID)->flatten()->toArray())
                    ->pluck(Address::FIELD_ID, Address::FIELD_LEGACY_ID)
                    ->toArray();

                DB::beginTransaction();

                foreach ($companies as $company) {
                    $this->migrateCompany($company, $paymentMethodValidities[$company[EloquentCompany::ID]] ?? false, $newAddressIds);
                }

                DB::commit();
            });
    }

    /**
     * @param EloquentCompany $company
     * @param bool $hasValidPaymentMethod
     * @param array $newAddressIds
     * @throws Exception
     */
    protected function migrateCompany(EloquentCompany $company, bool $hasValidPaymentMethod, array $newAddressIds): void
    {
        try {
            Company::unguard(); // allow Company::FIELD_ID to be updated
            $adminLocked = $company->{EloquentCompany::BUYING_LEADS} === EloquentCompany::BUYING_LEADS_STATUS_ADMIN_LOCKED;
            $adminApproved = !in_array($company->{EloquentCompany::STATUS}, [EloquentCompany::STATUS_PENDING, EloquentCompany::STATUS_REGISTERING]);
            $archived = in_array($company->{EloquentCompany::STATUS}, [EloquentCompany::STATUS_ARCHIVED, EloquentCompany::STATUS_ARCHIVE, EloquentCompany::STATUS_HIDDEN, EloquentCompany::STATUS_INACTIVE]);

            if ($this->syncLegacyId) {
                if (Company::query()->find($company->companyid)) {
                    //Check if we try to migrate the same company twice then log error to double-check the company details.
                    logger()->error("Skipping company id: {$company->companyid} as it has already been migrated");

                    return;
                }
            }

            $companyData = [
                Company::FIELD_ID => $company->{EloquentCompany::COMPANY_ID},
                Company::FIELD_LEGACY_ID => $company->{EloquentCompany::COMPANY_ID},
                Company::FIELD_REFERENCE => $company->{EloquentCompany::REFERENCE},
                Company::FIELD_NAME => $company->{EloquentCompany::COMPANY_NAME},
                Company::FIELD_ENTITY_NAME => $company->{EloquentCompany::COMPANY_LEGAL_ENTITY_NAME} ?? '',
                Company::FIELD_WEBSITE => $company->{EloquentCompany::WEBSITE},
                Company::FIELD_STATUS => self::determineCompanyStatus($company, $adminLocked, $adminApproved, $hasValidPaymentMethod),
                Company::FIELD_ADMIN_STATUS => $this->determineAdminStatus($company, $adminLocked, $archived),
                Company::FIELD_ADMIN_LOCKED => $adminLocked,
                Company::FIELD_ADMIN_APPROVED => $adminApproved,
                Company::FIELD_PRESCREENED_AT => Carbon::createFromTimestamp($company->{EloquentCompany::TIMESTAMP_LAST_PRESCREENED})->toDateTimeString(),
                Company::FIELD_ARCHIVED => $archived,
                Company::FIELD_LINK_TO_LOGO => $company->{EloquentCompany::LINK_TO_LOGO},
            ];

            if (!$this->syncLegacyId) unset($companyData[Company::FIELD_ID]);

            $newCompany = Company::query()->create($companyData);

            if ($newCompany->{Company::FIELD_ADMIN_STATUS} === Company::ADMIN_STATUS_COLLECTIONS) {
                $this->createCompanyCollectionsEntry($company, $newCompany->{Company::FIELD_ID});
            }

            CompanyIndustry::query()->insert([
                CompanyIndustry::FIELD_COMPANY_ID => $newCompany->{Company::FIELD_ID},
                CompanyIndustry::FIELD_INDUSTRY_ID => $company->{EloquentCompany::TYPE} === EloquentCompany::TYPE_ROOFER ? $this->industries['Roofing'] : $this->industries['Solar']
            ]);

            if ($company->{EloquentCompany::TYPE} === EloquentCompany::TYPE_ROOFER) {
                CompanyService::query()->insert([
                    [
                        CompanyService::FIELD_COMPANY_ID => $newCompany->{Company::FIELD_ID},
                        CompanyService::FIELD_INDUSTRY_SERVICE_ID => $this->industryServices['roof-installation']
                    ],
                    [
                        CompanyService::FIELD_COMPANY_ID => $newCompany->{Company::FIELD_ID},
                        CompanyService::FIELD_INDUSTRY_SERVICE_ID => $this->industryServices['roof-repair']
                    ],
                    [
                        CompanyService::FIELD_COMPANY_ID => $newCompany->{Company::FIELD_ID},
                        CompanyService::FIELD_INDUSTRY_SERVICE_ID => $this->industryServices['gutter-replacement']
                    ]
                ]);
            }
            else { //must be solar if it's not roofing
                CompanyService::query()->insert([
                    CompanyService::FIELD_COMPANY_ID => $newCompany->{Company::FIELD_ID},
                    CompanyService::FIELD_INDUSTRY_SERVICE_ID => $this->determineIndustryService($company)
                ]);
            }

            $insertAddresses = [];
            foreach ($company->{EloquentCompany::RELATION_COMPANY_ADDRESSES} as $companyAddress) {
                $insertAddresses[] = [
                    CompanyLocation::FIELD_COMPANY_ID => $newCompany->{Company::FIELD_ID},
                    CompanyLocation::FIELD_ADDRESS_ID => $newAddressIds[$companyAddress->{EloquentCompanyAddress::FIELD_ADDRESS_ID}] ?? 0,
                    CompanyLocation::FIELD_NAME => $companyAddress->{EloquentCompanyAddress::FIELD_NAME},
                    CompanyLocation::FIELD_REFERENCE => Uuid::uuid4(),
                    CompanyLocation::FIELD_PHONE => $companyAddress->{EloquentCompanyAddress::RELATION_ADDRESS}?->{EloquentAddress::PHONE},
                    CompanyLocation::FIELD_IMPORTED => !empty($companyAddress->{EloquentCompanyAddress::FIELD_GOOGLE_PLACE_ID})
                        || !empty($companyAddress->{EloquentCompanyAddress::FIELD_GOOGLE_REVIEW_COUNT})
                        || !empty($companyAddress->{EloquentCompanyAddress::FIELD_GOOGLE_RATING})
                ];
            }

            if(!empty($insertAddresses)) {
                CompanyLocation::query()->insert($insertAddresses);
            }

            CompanyData::query()->create([
                CompanyData::FIELD_COMPANY_ID => $newCompany->{Company::FIELD_ID},
                CompanyData::FIELD_PAYLOAD => [
                    SolarConfigurableFields::YEAR_STARTED_BUSINESS->value => $company->{EloquentCompany::YEAR_STARTED_BUSINESS},
                    SolarConfigurableFields::YEAR_STARTED_SOLAR->value => $company->{EloquentCompany::YEAR_STARTED_SOLAR},
                    SolarConfigurableFields::FAMILY_BUSINESS->value => $company->{EloquentCompany::FAMILY_BUSINESS},
                    SolarConfigurableFields::ENABLE_WATCHDOG_COMPLIANCE_LINKS->value => $company->{EloquentCompany::ENABLE_WATCHDOG_COMPLIANCE_LINKS},
                    SolarConfigurableFields::ALLOW_LEAD_SALES_WITHOUT_CC->value => $company->{EloquentCompany::ALLOW_LEAD_SALES_WITHOUT_CC},
                    SolarConfigurableFields::NEVER_EXCEED_BUDGET->value => $company->{EloquentCompany::NEVER_EXCEED_BUDGET},
                    SolarConfigurableFields::DISALLOW_RANKING->value => $company->{EloquentCompany::DISALLOW_RANKING},
                    SolarConfigurableFields::REVENUE_IN_THOUSANDS->value => $company->{EloquentCompany::REVENUE_IN_THOUSANDS},
                    SolarConfigurableFields::EMPLOYEE_COUNT->value => $company->{EloquentCompany::EMPLOYEE_COUNT},
                    SolarConfigurableFields::OFFICE_IN_USA->value => $company->{EloquentCompany::OFFICE_IN_USA},
                    SolarConfigurableFields::ENABLE_LEAD_COMPLIANCE_JORNAYA->value => $company->{EloquentCompany::ENABLE_LEAD_COMPLIANCE_JORNAYA}
                ]
            ]);
        }
        catch(Exception $e) {
            logger()->error("Error migrating legacy company {$company->{EloquentCompany::ID}}: Line ".$e->getLine().'. '.$e->getMessage());

            throw $e;
        }
        Company::reguard();
    }

    /**
     * NOTE - Moved to a static function so identical logic can be used for Company Sync events when a legacy change is made
     *
     * @param EloquentCompany $company
     * @param bool $adminLocked
     * @param bool $adminApproved
     * @param bool $hasValidPaymentMethod
     * @return int
     */
    static function determineCompanyStatus(EloquentCompany $company, bool $adminLocked, bool $adminApproved, bool $hasValidPaymentMethod): int
    {
        $activeCampaignsCount = $company->{EloquentCompany::RELATION_CAMPAIGNS}->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)->count();
        $reactivateCampaignsCount = $company
            ->{EloquentCompany::RELATION_CAMPAIGNS}
            ->where(LeadCampaign::STATUS, LeadCampaign::STATUS_INACTIVE)
            ->where(LeadCampaign::REACTIVATE_DATE, '>=', Carbon::now('UTC'))
            ->count();

        $validPaymentMethod = $company->{EloquentCompany::ALLOW_LEAD_SALES_WITHOUT_CC} || $hasValidPaymentMethod;

        $leadsPurchasedCount = $company->{EloquentCompany::RELATION_QUOTE_COMPANIES}->count();

        $imported = $company->{EloquentCompany::IMPORTED};

        if(
            $activeCampaignsCount > 0
            && $validPaymentMethod
            && !$adminLocked
            && $adminApproved
        ) {
            return Company::STATUS_LEADS_ACTIVE;
        }
        else if(
            $activeCampaignsCount === 0
            && $reactivateCampaignsCount > 0
            && $validPaymentMethod
            && !$adminLocked
            && $adminApproved
        ) {
            return Company::STATUS_LEADS_PAUSED;
        }
        else if(
            $activeCampaignsCount === 0
            && $leadsPurchasedCount > 0
            && $validPaymentMethod
            && !$adminLocked
            && $adminApproved
        ) {
            return Company::STATUS_LEADS_OFF;
        }
        else if(
            $activeCampaignsCount === 0
            && $leadsPurchasedCount === 0
            && $validPaymentMethod
            && !$adminLocked
            && $adminApproved
        ) {
            return Company::STATUS_LEADS_OFF_NEVER_PURCHASED;
        }
        else if(
            !$adminApproved
            && !$validPaymentMethod
        ) {
            return Company::STATUS_REGISTERING;
        }
        else if(
            !$adminApproved
            && $validPaymentMethod
        ) {
            return Company::STATUS_PENDING_APPROVAL;
        }
        else if(
            !$validPaymentMethod
            && (
                $adminApproved
                || $imported
            )
        ) {
            return Company::STATUS_PROFILE_ONLY;
        }

        return 0;
    }

    /**
     * @param EloquentCompany $company
     * @param bool $adminLocked
     * @param bool $archived
     * @return int|null
     */
    protected function determineAdminStatus(EloquentCompany $company, bool $adminLocked, bool $archived): ?int
    {
        //Legacy logic does not have a concept of partially suspended so it's not in this list
        if($archived) {
            return Company::ADMIN_STATUS_ARCHIVED;
        }
        else if($company->{EloquentCompany::STATUS} === EloquentCompany::STATUS_COLLECTION) {
            return Company::ADMIN_STATUS_COLLECTIONS;
        }
        else if($company->{EloquentCompany::STATUS} === EloquentCompany::STATUS_SUSPENDED) {
            return Company::ADMIN_STATUS_ALL_SUSPENDED;
        }
        else if($adminLocked) {
            return Company::ADMIN_STATUS_ADMIN_LOCKED;
        }

        return null;
    }

    /**
     * @param EloquentCompany $company
     * @return int
     */
    protected function determineIndustryService(EloquentCompany $company): int
    {
        //TODO: determine exact service logic
        if($company->{EloquentCompany::TYPE} === EloquentCompany::TYPE_INSTALLER) {
            return $this->industryServices['solar-installation'];
        }
        else if($company->{EloquentCompany::TYPE} === EloquentCompany::TYPE_AGGREGATOR
            || $company->{EloquentCompany::TYPE} === EloquentCompany::TYPE_PING_POST_AGGREGATOR) {
            return $this->industryServices['solar-aggregator'];
        }

        return 0;
    }

    /**
     * @param EloquentCompany $company
     * @param int $newCompanyId
     * @return int
     */
    protected function createCompanyCollectionsEntry(EloquentCompany $company, int $newCompanyId): int
    {
        $totalAmount = 0;
        foreach($company->{EloquentCompany::RELATION_INVOICES} as $invoice) {
            foreach($invoice->{EloquentInvoice::RELATION_INVOICE_ITEMS} as $invoiceItem) {
                $totalAmount += ($invoiceItem->{EloquentInvoiceItem::ITEM_EX_TAX_PRICE} * $invoiceItem->{EloquentInvoiceItem::QUANTITY});
            }
        }

        $paymentCollectionId = PaymentCollection::query()->insertGetId([
            PaymentCollection::FIELD_COMPANY_ID => $newCompanyId,
            PaymentCollection::FIELD_AMOUNT => $totalAmount,
            PaymentCollection::FIELD_PAID => false
        ]);

        return $paymentCollectionId;
    }
}
