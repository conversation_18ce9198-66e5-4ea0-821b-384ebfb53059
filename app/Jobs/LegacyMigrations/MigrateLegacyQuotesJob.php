<?php

namespace App\Jobs\LegacyMigrations;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\LeadProcessingInitial;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentVarStore;
use App\Models\Legacy\LeadTrackingUrl;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Models\Odin\ConsumerProductTracking;
use Exception;
use Illuminate\Bus\Batchable;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;

class MigrateLegacyQuotesJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param Collection $quoteIds
     * @param array $websites
     * @param array $serviceProducts
     * @param array $industries
     * @param int $mayFirstTwoThousandTwentyTwo
     * @param int $ninetyDaysAgo
     * @param int $badAddressId
     */
    public function __construct(
        private Collection $quoteIds,
        private array $websites,
        private array $serviceProducts,
        private array $industries,
        private int $mayFirstTwoThousandTwentyTwo,
        private int $ninetyDaysAgo,
        private int $badAddressId
    )
    {
        $this->connection = QueueHelperService::QUEUE_CONNECTION;
        $this->queue = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $migratedQuoteIds = Consumer::query()
            ->select(Consumer::FIELD_LEGACY_ID)
            ->whereIn(Consumer::FIELD_LEGACY_ID, $this->quoteIds)
            ->pluck(Consumer::FIELD_LEGACY_ID)
            ->toArray();

        $migratedQuoteIds = array_flip($migratedQuoteIds);

        $this->quoteIds = $this->quoteIds->filter(fn($id) => !array_key_exists($id, $migratedQuoteIds));

        if ($this->quoteIds->isEmpty()) return;

        EloquentQuote::query()
            ->with([
                EloquentQuote::RELATION_ADDRESS,
                EloquentQuote::RELATION_TRACKING_URL
            ])
            ->whereIn(EloquentQuote::ID, $this->quoteIds)
            ->chunk(500, function (Collection $quotes) {
                $newAddressIds = Address::query()
                                    ->whereIn(Address::FIELD_LEGACY_ID, $quotes->pluck(EloquentQuote::ADDRESS_ID))
                                    ->pluck(Address::FIELD_ID, Address::FIELD_LEGACY_ID)
                                    ->toArray();

                $initialQueueLeadIds = LeadProcessingInitial::query()
                                            ->whereIn(LeadProcessingInitial::FIELD_LEAD_ID, $quotes->pluck(EloquentQuote::ID))
                                            ->pluck(LeadProcessingInitial::FIELD_LEAD_ID)
                                            ->toArray();

                DB::beginTransaction();

                foreach ($quotes as $quote) {
                    $this->migrateQuote($quote, $newAddressIds[$quote->{EloquentQuote::ADDRESS_ID}] ?? $this->badAddressId, in_array($quote->{EloquentQuote::ID}, $initialQueueLeadIds));
                }

                DB::commit();
            });
    }

    /**
     * @param EloquentQuote $quote
     * @param int $addressId
     * @param bool $inInitialQueue
     * @throws Exception
     */
    protected function migrateQuote(EloquentQuote $quote, int $addressId, bool $inInitialQueue): void
    {
        try {
            //Make new Consumer

            $newConsumerId = Consumer::query()->insertGetId([
                Consumer::FIELD_REFERENCE => Uuid::uuid4(),
                Consumer::FIELD_WEBSITE_ID => $this->determineWebsite($quote),
                Consumer::FIELD_EMAIL => $quote->{EloquentQuote::USER_EMAIL},
                Consumer::FIELD_PHONE => $quote->{EloquentQuote::RELATION_ADDRESS}?->{EloquentAddress::PHONE},
                Consumer::FIELD_FIRST_NAME => $quote->{EloquentQuote::FIRST_NAME},
                Consumer::FIELD_LAST_NAME => $quote->{EloquentQuote::LAST_NAME},
                Consumer::FIELD_STATUS => $this->determineConsumerStatus($quote),
                Consumer::FIELD_CLASSIFICATION => $this->determineClassification($quote),
                Consumer::FIELD_STATUS_REASON => '', //TODO
                Consumer::FIELD_MAX_CONTACT_REQUESTS => $quote->{EloquentQuote::NUMBER_OF_QUOTES},
                Consumer::CREATED_AT => Carbon::createFromTimestamp($quote->{EloquentQuote::TIMESTAMP_ADDED})->format("Y-m-d H:i:s"),
                Consumer::FIELD_LEGACY_ID => $quote->{EloquentQuote::ID}
            ]);

            //End make new Consumer

            // Determine Lead type
            if ($quote->{EloquentQuote::ROOFING_LEAD}) {
                $serviceProductId = $this->serviceProducts['roof-repair'];
            }
            else {
                $serviceProductId = $this->serviceProducts['solar-installation'];
            }

            //Make Consumer Product Data

            if ($serviceProductId === $this->serviceProducts['roof-repair']) {
                $consumerProductDataPayload = [
                    GlobalConfigurableFields::STOREYS->value => $quote->{EloquentQuote::STOREYS},
                    GlobalConfigurableFields::ROOF_DIRECTION->value => $quote->{EloquentQuote::ROOF_DIRECTION},
                    GlobalConfigurableFields::ROOF_SHADING->value => $quote->{EloquentQuote::ROOF_SHADING},
                    GlobalConfigurableFields::ROOF_PITCH->value => $quote->{EloquentQuote::ROOF_PITCH},
                    GlobalConfigurableFields::ROOF_TYPE->value => $quote->{EloquentQuote::ROOF_TYPE},
                    GlobalConfigurableFields::ROOF_TYPE_OTHER->value => $quote->{EloquentQuote::ROOF_TYPE_OTHER},
                    GlobalConfigurableFields::ROOF_CONDITION->value => $quote->{EloquentQuote::ROOF_CONDITION},
                    GlobalConfigurableFields::BEST_TIME_TO_CALL->value => $quote->{EloquentQuote::BEST_TIME_TO_CALL},
                    GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value => $quote->{EloquentQuote::BEST_TIME_TO_CALL_OTHER},
                    GlobalConfigurableFields::OWN_PROPERTY->value => $quote->{EloquentQuote::OWN_PROPERTY},
                    GlobalConfigurableFields::IP_ADDRESS->value => $quote->{EloquentQuote::IP_ADDRESS}
                ];
            }

            else {
                $consumerProductDataPayload = [
                    SolarConfigurableFields::SYSTEM_TYPE->value => $quote->{EloquentQuote::SYSTEM_TYPE},
                    SolarConfigurableFields::SYSTEM_SIZE->value => $quote->{EloquentQuote::SYSTEM_SIZE},
                    SolarConfigurableFields::SYSTEM_SIZE_OTHER->value => $quote->{EloquentQuote::SYSTEM_SIZE_OTHER},
                    GlobalConfigurableFields::STOREYS->value => $quote->{EloquentQuote::STOREYS},
                    SolarConfigurableFields::ELECTRIC_COST->value => $quote->{EloquentQuote::ELECTRIC_COST},
                    GlobalConfigurableFields::ROOF_DIRECTION->value => $quote->{EloquentQuote::ROOF_DIRECTION},
                    GlobalConfigurableFields::ROOF_SHADING->value => $quote->{EloquentQuote::ROOF_SHADING},
                    GlobalConfigurableFields::ROOF_PITCH->value => $quote->{EloquentQuote::ROOF_PITCH},
                    GlobalConfigurableFields::ROOF_TYPE->value => $quote->{EloquentQuote::ROOF_TYPE},
                    GlobalConfigurableFields::ROOF_TYPE_OTHER->value => $quote->{EloquentQuote::ROOF_TYPE_OTHER},
                    GlobalConfigurableFields::ROOF_CONDITION->value => $quote->{EloquentQuote::ROOF_CONDITION},
                    SolarConfigurableFields::PANEL_TIER->value => $quote->{EloquentQuote::PANEL_TIER},
                    GlobalConfigurableFields::BEST_TIME_TO_CALL->value => $quote->{EloquentQuote::BEST_TIME_TO_CALL},
                    GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value => $quote->{EloquentQuote::BEST_TIME_TO_CALL_OTHER},
                    SolarConfigurableFields::UTILITY_NAME->value => $quote->{EloquentQuote::UTILITY},
                    SolarConfigurableFields::UTILITY_ID->value => $quote->{EloquentQuote::UTILITY_ID},
                    GlobalConfigurableFields::OWN_PROPERTY->value => $quote->{EloquentQuote::OWN_PROPERTY},
                    GlobalConfigurableFields::IP_ADDRESS->value => $quote->{EloquentQuote::IP_ADDRESS}
                ];
            }

            $newConsumerProductData = ConsumerProductData::query()->create([
                ConsumerProductData::FIELD_PAYLOAD => $consumerProductDataPayload
            ]);
            $consumerProductDataId = $newConsumerProductData->{ConsumerProductData::FIELD_ID};

            //End make Consumer Product Data

            //Make Consumer Product Tracking

            $consumerProductTrackingId = null;
            $consumerProductTrackingData = [];

            if($quote->{EloquentQuote::RELATION_TRACKING_URL}) {
                $trackingUrl = $quote->{EloquentQuote::RELATION_TRACKING_URL};

                $consumerProductTrackingData = [
                    ConsumerProductTracking::LEGACY_ID => $trackingUrl->{LeadTrackingUrl::ID},
                    ConsumerProductTracking::URL_START => $trackingUrl->{LeadTrackingUrl::URL_START} ?? '',
                    ConsumerProductTracking::URL_CONVERT => $trackingUrl->{LeadTrackingUrl::URL_CONVERT} ?? '',
                    ConsumerProductTracking::CALCULATOR_SOURCE => $trackingUrl->{LeadTrackingUrl::SOURCE} ?? ''
                ];
            }

            //TODO: add advertising tracking fields to Consumer resource creation API endpoint
            if(!empty($quote->{EloquentQuote::TRACK_CODE})
            && !empty($quote->{EloquentQuote::TRACK_NAME})) {
                $consumerProductTrackingData = [
                    ConsumerProductTracking::LEGACY_ID => $consumerProductTrackingData[ConsumerProductTracking::LEGACY_ID] ?? null,
                    ConsumerProductTracking::URL_START => $consumerProductTrackingData[ConsumerProductTracking::URL_START] ?? '',
                    ConsumerProductTracking::URL_CONVERT => $consumerProductTrackingData[ConsumerProductTracking::URL_CONVERT] ?? '',
                    ConsumerProductTracking::CALCULATOR_SOURCE => $consumerProductTrackingData[ConsumerProductTracking::CALCULATOR_SOURCE] ?? '',
                    ConsumerProductTracking::AD_TRACK_TYPE => $quote->{EloquentQuote::TRACK_NAME},
                    ConsumerProductTracking::AD_TRACK_CODE => $quote->{EloquentQuote::TRACK_CODE}
                ];
            }

            if(!empty($consumerProductTrackingData)) {
                $consumerProductTrackingId = ConsumerProductTracking::query()->insertGetId($consumerProductTrackingData);
            }

            //End make Consumer Product Tracking

            //Make Consumer Product

            ConsumerProduct::query()->insert([
                ConsumerProduct::FIELD_CONSUMER_ID        => $newConsumerId,
                ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                ConsumerProduct::FIELD_ADDRESS_ID       => $addressId,
                ConsumerProduct::FIELD_GOOD_TO_SELL     => $this->determineConsumerGoodToSell($quote),
                ConsumerProduct::FIELD_STATUS           => $this->determineConsumerProductStatus($quote, $inInitialQueue),
                ConsumerProduct::FIELD_CONTACT_REQUESTS => $quote->{EloquentQuote::NUMBER_OF_QUOTES},
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_DATA_ID => $consumerProductDataId,
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $consumerProductTrackingId,
                ConsumerProduct::CREATED_AT                         => Carbon::createFromTimestamp($quote->{EloquentQuote::TIMESTAMP_ADDED})->format("Y-m-d H:i:s"),
            ]);

            //End make Consumer Product
        }
        catch(Exception $e) {
            logger()->error("Error migrating legacy quote {$quote->{EloquentQuote::ID}}: Line ".$e->getLine().'. '.$e->getMessage());

            throw $e;
        }
    }

    /**
     * @param EloquentQuote $quote
     * @return int
     */
    protected function determineConsumerStatus(EloquentQuote $quote): int
    {
        if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_INITIAL) {
            return Consumer::STATUS_INITIAL;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED
            || $quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_SOLD) {
            return Consumer::STATUS_COMPLETED;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_CANCELLED) {
            return Consumer::STATUS_CANCELLED;
        }
        else {
            return Consumer::STATUS_IN_PROGRESS;
        }
    }

    /**
     * A consumer is considered good to sell if:
     * 1) The lead came in on or before 5/1/2022 and is allocated
     * 2) The lead came in after 5/1/2022 and has classification verified or is allocated
     *
     * @param EloquentQuote $quote
     * @return bool
     */
    protected function determineConsumerGoodToSell(EloquentQuote $quote): bool
    {
        if($quote->{EloquentQuote::TIMESTAMP_ADDED} <= $this->mayFirstTwoThousandTwentyTwo) {
            return $quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED;
        }
        else {
            return $quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED || $quote->{EloquentQuote::CLASSIFICATION} === EloquentQuote::CLASSIFICATION_VERIFIED;
        }
    }

    /**
     * @param EloquentQuote $quote
     * @param bool $inInitialQueue
     * @return int
     */
    protected function determineConsumerProductStatus(EloquentQuote $quote, bool $inInitialQueue): int
    {
        if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_ALLOCATED) {
            return ConsumerProduct::STATUS_ALLOCATED;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_CANCELLED) {
            return ConsumerProduct::STATUS_CANCELLED;
        }
        else if($quote->{EloquentQuote::TIMESTAMP_ADDED} <= $this->ninetyDaysAgo
            && !in_array($quote->{EloquentQuote::STATUS}, [EloquentQuote::VALUE_STATUS_ALLOCATED, EloquentQuote::VALUE_STATUS_SOLD, EloquentQuote::VALUE_STATUS_NO_COMPANIES, EloquentQuote::VALUE_STATUS_CANCELLED])) {
            return ConsumerProduct::STATUS_UNSOLD;
        }
        else if($quote->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_INITIAL) {
            return $inInitialQueue ? ConsumerProduct::STATUS_INITIAL : ConsumerProduct::STATUS_PENDING_REVIEW;
        }
        else {
            return ConsumerProduct::STATUS_UNDER_REVIEW;
        }
    }

    /**
     * @param EloquentQuote $quote
     * @return int
     */
    protected function determineWebsite(EloquentQuote $quote): int
    {
        return match($quote->{EloquentQuote::ORIGIN}) {
            'sr' => $this->websites['www.solarreviews.com'],
            'se' => $this->websites['www.solar-estimate.org'],
            'rc' => $this->websites['www.roofingcalculator.com'],
            'spr' => $this->websites['www.solarpowerrocks.com'],
            'sn' => $this->websites['www.sunnumber.com'],
            'cmb' => $this->websites['www.cutmybill.com'],
            default => 0
        };
    }

    /**
     * @param EloquentQuote $quote
     * @return int
     */
    protected function determineClassification(EloquentQuote $quote): int
    {
        $hasPhone = !empty($quote->{EloquentQuote::RELATION_ADDRESS}?->{EloquentAddress::PHONE});
        $phoneValidated = (bool) $quote
            ->varStores()
            ->getQuery()
            ->where(EloquentVarStore::CATEGORY, 'estimator_data')
            ->where(EloquentVarStore::NAME, 'phone_validated')
            ->first();

        if(!$hasPhone) {
            return Consumer::CLASSIFICATION_EMAIL_ONLY;
        }
        else if(!$phoneValidated) {
            return Consumer::CLASSIFICATION_UNVERIFIED_PHONE;
        }
        else {
            return Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS;
        }
    }
}
