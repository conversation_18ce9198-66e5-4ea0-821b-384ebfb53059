<?php

namespace App\Jobs\LegacyMigrations;

use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentHistoryLog;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\CompanyUserLog;
use App\Services\QueueHelperService;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateLegacyCompanyContactsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private Collection $contactIds
    )
    {
        $this->connection = QueueHelperService::QUEUE_CONNECTION;
        $this->queue = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        EloquentCompanyContact::withTrashed()
            ->whereIn(EloquentCompanyContact::FIELD_CONTACT_ID, $this->contactIds)
            ->chunk(500, function (Collection $contacts) {
                try {

                    $insertContacts = [];
                    foreach ($contacts as $contact) {
                        $insertContacts[] = $this->migrateCompanyContact($contact);
                    }

                    DB::beginTransaction();

                    CompanyUser::query()->insert($insertContacts);

                    DB::commit();
                }
                catch(Exception $e) {
                    logger()->error("Error migrating legacy company contacts: ".$e->getMessage());

                    throw $e;
                }
            });
    }

    /**
     * @param EloquentCompanyContact $companyContact
     * @return array
     */
    private function migrateCompanyContact(EloquentCompanyContact $companyContact): array
    {
        return [
            CompanyUser::FIELD_LEGACY_ID => $companyContact->{EloquentCompanyContact::FIELD_CONTACT_ID},
            CompanyUser::FIELD_COMPANY_ID => $companyContact->{EloquentCompanyContact::FIELD_COMPANY_ID},
            CompanyUser::FIELD_FIRST_NAME => $companyContact->{EloquentCompanyContact::FIELD_FIRST_NAME},
            CompanyUser::FIELD_LAST_NAME => $companyContact->{EloquentCompanyContact::FIELD_LAST_NAME},
            CompanyUser::FIELD_TITLE => $companyContact->{EloquentCompanyContact::FIELD_TITLE},
            CompanyUser::FIELD_DEPARTMENT => '', //TODO
            CompanyUser::FIELD_EMAIL => $companyContact->{EloquentCompanyContact::FIELD_EMAIL},
            CompanyUser::FIELD_CELL_PHONE => $companyContact->{EloquentCompanyContact::FIELD_MOBILE},
            CompanyUser::FIELD_OFFICE_PHONE => $companyContact->{EloquentCompanyContact::FIELD_PHONE},
            CompanyUser::FIELD_ZOOM_INFO_ID => $companyContact->{EloquentCompanyContact::FIELD_ZOOM_INFO_ID},
            CompanyUser::FIELD_CAN_LOG_IN => false,
            CompanyUser::FIELD_STATUS => $companyContact->{EloquentCompanyContact::FIELD_STATUS},
            CompanyUser::CREATED_AT => Carbon::createFromTimestamp($companyContact->{EloquentCompanyContact::FIELD_TIMESTAMP_ADDED})->format('Y-m-d H:i:s'),
            CompanyUser::UPDATED_AT => Carbon::createFromTimestamp($companyContact->{EloquentCompanyContact::FIELD_TIMESTAMP_UPDATED})->format('Y-m-d H:i:s'),
            CompanyUser::FIELD_DELETED_AT => $companyContact->{EloquentCompanyContact::FIELD_DELETED_AT},
            CompanyUser::FIELD_IS_CONTACT => true
        ];
    }
}
