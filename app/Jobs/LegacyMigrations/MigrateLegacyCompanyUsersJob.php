<?php

namespace App\Jobs\LegacyMigrations;

use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentHistoryLog;
use App\Models\Legacy\EloquentUser;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\CompanyUserLog;
use App\Services\QueueHelperService;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class MigrateLegacyCompanyUsersJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private Collection $userIds
    )
    {
        $this->connection = QueueHelperService::QUEUE_CONNECTION;
        $this->queue = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        EloquentUser::query()
            ->with(EloquentUser::RELATION_HISTORY_LOG)
            ->whereIn(EloquentUser::ID, $this->userIds)
            ->chunk(500, function (Collection $users) {

                DB::beginTransaction();

                foreach ($users as $user) {
                    $this->migrateCompanyUser($user);
                }

                DB::commit();
            });
    }

    /**
     * @param EloquentUser $user
     * @throws Exception
     */
    private function migrateCompanyUser(EloquentUser $user): void
    {
        try {
            $newUser = CompanyUser::query()->create([
                CompanyUser::FIELD_LEGACY_ID => $user->{EloquentUser::ID},
                CompanyUser::FIELD_COMPANY_ID => $user->{EloquentUser::COMPANY_ID},
                CompanyUser::FIELD_FIRST_NAME => $user->{EloquentUser::FIRST_NAME},
                CompanyUser::FIELD_LAST_NAME => $user->{EloquentUser::LAST_NAME},
                CompanyUser::FIELD_TITLE => $user->{EloquentUser::TITLE},
                CompanyUser::FIELD_DEPARTMENT => '', //TODO
                CompanyUser::FIELD_EMAIL => $user->{EloquentUser::EMAIL},
                CompanyUser::FIELD_PASSWORD => $user->{EloquentUser::PASSWORD},
                CompanyUser::FIELD_CELL_PHONE => $user->{EloquentUser::PHONE},
                CompanyUser::FIELD_OFFICE_PHONE => $user->{EloquentUser::PHONE},
                CompanyUser::FIELD_ZOOM_INFO_ID => '',
                CompanyUser::FIELD_CAN_LOG_IN => $this->determineCanLogin($user),
                CompanyUser::FIELD_STATUS => $user->{EloquentUser::STATUS},
                CompanyUser::FIELD_FAILED_LOGIN_COUNT => $user->{EloquentUser::FAILED_LOGIN_COUNT},
                CompanyUser::FIELD_DELETED_AT => $user->{EloquentUser::DELETED_AT},
                CompanyUser::FIELD_IS_CONTACT => false
            ]);

            $loginRecords = $user->{EloquentUser::RELATION_HISTORY_LOG}->where(EloquentHistoryLog::ACTIVITY, EloquentHistoryLog::ACTIVITY_LOGIN);

            $companyUserLogs = [];
            foreach ($loginRecords as $loginRecord) {
                $companyUserLogs[] = [
                    CompanyUserLog::FIELD_COMPANY_USER_ID => $newUser->{CompanyUser::FIELD_ID},
                    CompanyUserLog::FIELD_IP_ADDRESS => $loginRecord->{EloquentHistoryLog::IP_ADDRESS},
                    CompanyUserLog::FIELD_TYPE => CompanyUserLog::TYPE_LOGIN,
                    CompanyUserLog::CREATED_AT => Carbon::createFromTimestamp($loginRecord->{EloquentHistoryLog::TIMESTAMP_ADDED})
                ];
            }

            CompanyUserLog::query()->insert($companyUserLogs);
        }
        catch(Exception $e) {
            logger()->error("Error migrating legacy user {$user->{EloquentUser::ID}}: Line ".$e->getLine().'. '.$e->getMessage());

            throw $e;
        }
    }

    /**
     * @param EloquentUser $user
     * @return bool
     */
    private function determineCanLogin(EloquentUser $user): bool
    {
        return $user->{EloquentUser::STATUS} === EloquentUser::FIELD_STATUS_VALUE_ACTIVE;
    }
}
