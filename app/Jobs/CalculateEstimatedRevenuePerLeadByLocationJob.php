<?php

namespace App\Jobs;

use App\Services\EstimatedRevenuePerLeadByLocationCalculationService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * Class CalculateEstimatedRevenuePerLeadByLocationJob
 * @package App\Jobs
 */
class CalculateEstimatedRevenuePerLeadByLocationJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 1; // scheduled every ten mins

    public function __construct() {
        $this->onQueue('long_running_single_attempt');
    }

    /**
     * @param EstimatedRevenuePerLeadByLocationCalculationService $service
     * @return void
     * @throws Exception
     */
    public function handle(EstimatedRevenuePerLeadByLocationCalculationService $service): void
    {
        ini_set('memory_limit','-1');
        $service->calculateAndStoreEstimatedRevenuePerLeadByLocation();
    }
}
