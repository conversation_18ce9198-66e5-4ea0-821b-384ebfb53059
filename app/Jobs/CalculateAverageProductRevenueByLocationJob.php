<?php

namespace App\Jobs;

use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product as ProductEnum;
use App\Services\AverageProductRevenueByLocationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class CalculateAverageProductRevenueByLocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    public function __construct(
        protected ?int $daysAverage = null,
    ) {}

    /**
     * @return void
     */
    public function handle(): void
    {
        // For now, only calculate for active products and industries
        //  can be made dynamic if needed.
        /** @var ProductEnum[] $activeProducts */
        $activeProducts = [
            ProductEnum::LEAD
        ];
        /** @var IndustryEnum[] $activeIndustries */
        $activeIndustries = [
            IndustryEnum::SOLAR,
            IndustryEnum::ROOFING,
            IndustryEnum::SIDING,
            IndustryEnum::WINDOWS,
            IndustryEnum::HVAC,
        ];

        /** @var AverageProductRevenueByLocationService $service */
        $service = app(AverageProductRevenueByLocationService::class);

        foreach ($activeProducts as $product) {
            $productId = $product->model()->id;
            foreach ($activeIndustries as $industry) {
                $industryId = $industry->model()->id;

                $service->calculateAverageRevenueByCounty($productId, $industryId, $this->daysAverage);
            }
        }
    }
}
