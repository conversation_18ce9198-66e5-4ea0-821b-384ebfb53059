<?php

namespace App\Jobs\AgedQueue;

use App\Services\AgedQueueService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class RemoveDuplicateLeadsFromAgedQueueJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct() {}

    /**
     * Execute the job.
     */
    public function handle(AgedQueueService $agedQueueService): void
    {
        $agedQueueService->removeDuplicateLeads();
    }
}
