<?php

namespace App\Jobs;

use App\Repositories\TaskRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculateTaskTimezoneJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public int $taskId)
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(TaskRepository $repository)
    {
        $task = $repository->findByTaskIdOrFail($this->taskId);
        $repository->storeTimezoneForTask($task);
    }
}
