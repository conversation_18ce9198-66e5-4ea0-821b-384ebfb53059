<?php

namespace App\Jobs;

use App\Events\RejectionStatisticsUpdatedEvent;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ProductRejectionRepository;
use App\Repositories\Odin\ProductRepository;
use App\Services\ProductRejectionCalculationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class CalculateRejectionStatisticsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    public function __construct(protected int $timeframeInDays = 32)
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * @param ProductRepository $productRepository
     * @param ProductRejectionCalculationService $productRejectionCalculationService
     * @param ProductAssignmentRepository $productAssignmentRepository
     * @param ComputedRejectionStatisticRepository $computedRejectionStatisticRepository
     * @param ProductRejectionRepository $productRejectionRepository
     * @return void
     */
    public function handle(
        ProductRepository                    $productRepository,
        ProductRejectionCalculationService   $productRejectionCalculationService,
        ProductAssignmentRepository          $productAssignmentRepository,
        ComputedRejectionStatisticRepository $computedRejectionStatisticRepository,
        ProductRejectionRepository $productRejectionRepository
    ): void
    {
        // Todo: We only calculate rejection percentage for leads at this point. Add new logic if other products considered later

        ini_set('memory_limit','-1');

        $companyIdsWithAssignments = $productAssignmentRepository->getCompanyIdsWithProductAssignmentsInTimeframeInDays($this->timeframeInDays);
        $companyIdsWithFailedLogs  = $productRejectionRepository->getCompanyIdsWithFailedLogsInLast30Days();

        $companyIds = array_unique(array_merge($companyIdsWithAssignments, $companyIdsWithFailedLogs));

        $this->handleCompaniesWithAssignments($companyIds, $productRepository, $productRejectionCalculationService, $computedRejectionStatisticRepository);
        $this->handleCompaniesWithoutAssignments($companyIds, $productRepository, $computedRejectionStatisticRepository);

        RejectionStatisticsUpdatedEvent::dispatch();
    }

    /**
     * @param array $companyIds
     * @param ProductRepository $productRepository
     * @param ProductRejectionCalculationService $productRejectionCalculationService
     * @param ComputedRejectionStatisticRepository $computedRejectionStatisticRepository
     * @return void
     */
    private function handleCompaniesWithAssignments(
        array                                $companyIds,
        ProductRepository                    $productRepository,
        ProductRejectionCalculationService   $productRejectionCalculationService,
        ComputedRejectionStatisticRepository $computedRejectionStatisticRepository
    ): void
    {
        $productId = $productRepository->getLeadProductId();

        // Performance.
        DB::disableQueryLog();

        Company::query()
            ->whereIn(Company::FIELD_ID, $companyIds)
            ->select([Company::FIELD_ID, Company::FIELD_LEGACY_ID])
            ->chunk(500, function($companies) use ($productId, $productRejectionCalculationService, $computedRejectionStatisticRepository) {
                $data = [];

                foreach($companies as $company) {
                    // Rejection percentage will never exceed threshold as a result of time, so we don't need to observe / notify for this
                    $rejectionStatistics = $productRejectionCalculationService->computeRejectionStatistics($company, $productId);

                    $data[] = [
                        ComputedRejectionStatistic::FIELD_COMPANY_ID                   => $company->{Company::FIELD_ID},
                        ComputedRejectionStatistic::FIELD_LEGACY_COMPANY_ID            => $company->{Company::FIELD_LEGACY_ID},
                        ComputedRejectionStatistic::FIELD_PRODUCT_ID                   => $productId,
                        ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST),
                        ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE  => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE),
                        ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST    => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST),
                        ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE     => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE),
                        ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE),
                        ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST        => Arr::get($rejectionStatistics, ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST),
                    ];
                }

                $computedRejectionStatisticRepository->upsertRejectionStatistics($data);
            });
    }

    /**
     * @param array $companyIds
     * @param ProductRepository $productRepository
     * @param ComputedRejectionStatisticRepository $computedRejectionStatisticRepository
     * @return void
     */
    private function handleCompaniesWithoutAssignments(
        array                                $companyIds,
        ProductRepository                    $productRepository,
        ComputedRejectionStatisticRepository $computedRejectionStatisticRepository
    ): void
    {
        $productId = $productRepository->getLeadProductId();

        // Performance.
        DB::disableQueryLog();

        Company::query()
            ->whereNotIn(Company::FIELD_ID, $companyIds)
            ->select([Company::FIELD_ID, Company::FIELD_LEGACY_ID])
            ->chunk(1000, function($companies) use ($productId, $computedRejectionStatisticRepository) {
                $data = [];

                foreach($companies as $company) {
                    $data[] = [
                        ComputedRejectionStatistic::FIELD_COMPANY_ID                   => $company->{Company::FIELD_ID},
                        ComputedRejectionStatistic::FIELD_LEGACY_COMPANY_ID            => $company->{Company::FIELD_LEGACY_ID},
                        ComputedRejectionStatistic::FIELD_PRODUCT_ID                   => $productId,
                        ComputedRejectionStatistic::FIELD_MANUAL_REJECTED_PRODUCT_COST => 0,
                        ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE  => 0,
                        ComputedRejectionStatistic::FIELD_CRM_REJECTED_PRODUCT_COST    => 0,
                        ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE     => 0,
                        ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE => 0,
                        ComputedRejectionStatistic::FIELD_ASSIGNED_PRODUCT_COST        => 0
                    ];
                }

                $computedRejectionStatisticRepository->upsertRejectionStatistics($data);
            });

    }
}
