<?php

namespace App\Jobs\OpportunityNotifications;

use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Models\Campaigns\CompanyCampaign;
use App\Repositories\MissedProducts\MissedProductReasonEventRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateCampaignOutbidEventsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param int[] $campaignIds
     * @return void
     */
    public function __construct(protected array $campaignIds)
    {
        $this->onQueue('default');
    }

    /**
     * @param MissedProductReasonEventRepository $repository
     * @return void
     */
    public function handle(MissedProductReasonEventRepository $repository): void
    {
        CompanyCampaign::query()
            ->findMany($this->campaignIds)
            ->each(fn(CompanyCampaign $campaign) => $repository->handleNewCampaignEvent($campaign, MissedProductReasonEventType::OUTBID));
    }
}