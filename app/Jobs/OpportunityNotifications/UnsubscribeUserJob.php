<?php

namespace App\Jobs\OpportunityNotifications;

use App\Services\OpportunityNotifications\OpportunityNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UnsubscribeUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct(protected string $contactRef)
    {
        $this->onQueue('default');
    }


    /**
     * Execute the job.
     * @param OpportunityNotificationService $notificationService
     */
    public function handle(OpportunityNotificationService $notificationService): void
    {
        $notificationService->unsubscribeUser($this->contactRef);
    }

}
