<?php

namespace App\Jobs\OpportunityNotifications;

use App\Repositories\MissedProductRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteExpiredMissedProductsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @return void
     */
    public function __construct()
    {
        $this->onQueue('default');
    }

    /**
     * @param MissedProductRepository $missedProductRepository
     * @return void
     */
    public function handle(MissedProductRepository $missedProductRepository): void
    {
        $missedProductRepository->cleanupTable();
    }
}
