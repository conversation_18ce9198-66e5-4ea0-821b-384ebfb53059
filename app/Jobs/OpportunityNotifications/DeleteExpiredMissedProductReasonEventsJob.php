<?php

namespace App\Jobs\OpportunityNotifications;

use App\Repositories\MissedProducts\MissedProductReasonEventRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteExpiredMissedProductReasonEventsJob implements ShouldQueue {
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct()
    {
        $this->onQueue('default');
    }

    /**
     * @param MissedProductReasonEventRepository $repository
     * @return void
     */
    public function handle(MissedProductReasonEventRepository $repository): void
    {
        $repository->cleanupTable();
    }
}
