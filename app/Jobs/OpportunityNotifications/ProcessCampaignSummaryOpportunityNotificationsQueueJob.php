<?php

namespace App\Jobs\OpportunityNotifications;

use App\Enums\OpportunityNotifications\OpportunityNotificationConfigType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MissedProducts\OpportunityNotificationConfig;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use App\Repositories\MissedProductRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Services\OpportunityNotifications\OpportunityNotificationCompanyFilterableService;
use App\Services\OpportunityNotifications\OpportunityNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;

/**
 * Process all Campaign Summary type opportunity notification configs and queue notification jobs
 * for qualifying companies
 */
class ProcessCampaignSummaryOpportunityNotificationsQueueJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private int $chunkSize = 500;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct(
        protected ?Collection $notificationConfigs = null,
        protected ?bool $sendTest = false,
    )
    {
        //TODO: previous queue does not exist - this will need to be updated?
        $this->onQueue('default');
    }


    /**
     * Execute the job.
     * @param CompanyRepository $companyRepository
     * @param OpportunityNotificationCompanyFilterableService $opportunityNotificationCompanyFilterableService
     * @param MissedProductRepository $missedProductRepository
     * @param OpportunityNotificationService $opportunityNotificationService
     */
    public function handle(
        CompanyRepository $companyRepository,
        OpportunityNotificationCompanyFilterableService $opportunityNotificationCompanyFilterableService,
        MissedProductRepository $missedProductRepository,
        OpportunityNotificationService $opportunityNotificationService,
    ): void
    {
        if (!$this->sendTest)
            $this->getNotificationConfigs();

        /** @var OpportunityNotificationConfig $config */
        foreach ($this->notificationConfigs as $config) {
            if ($this->configExpired($config) && !$this->sendTest)
                continue;

            $opportunityNotificationCompanyFilterableService
                ->query($config->{OpportunityNotificationConfig::RELATION_FILTER_PRESET})
                ->whereHas(Company::RELATION_PRODUCT_ASSIGNMENTS, fn(Builder $query) =>
                    $query->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_DELIVERED_AT, '>', now()->subDays($config->maximum_days_since_last_lead ?? 90))
                )->orderBy(Company::FIELD_ID)
                ->chunkById($this->chunkSize, function ($companies) use ($config, $companyRepository, $missedProductRepository, $opportunityNotificationCompanyFilterableService, $opportunityNotificationService) {
                    /** @var Company $company */
                    foreach ($companies as $company) {
                        $recipients = $opportunityNotificationService->getAvailableEmailRecipients($company, true)
                            ->pluck(CompanyUser::FIELD_ID)
                            ->toArray();
                        if (!$recipients)
                            continue;

                        if ($this->sendTest) {
                            SendCampaignSummaryOpportunityNotificationJob::dispatch($recipients, $company->id, $config->id, $this->sendTest);
                            continue;
                        }

                        if (!$this->companyExceedsMissedProductThresholds($company, $config, $missedProductRepository))
                            continue;

                        if (!$opportunityNotificationService->companyCanBeNotified($company, $config))
                            continue;

                        $jobDelayInSeconds = $this->calculateJobDelay($companyRepository, $config, $company);

                        SendCampaignSummaryOpportunityNotificationJob::dispatch($recipients, $company->id, $config->id)->delay($jobDelayInSeconds);
                    }
                });
        }
    }

    /**
     * @param CompanyRepository $companyRepository
     * @param OpportunityNotificationConfig $config
     * @param Company $company
     * @return float|int
     */
    private function calculateJobDelay(CompanyRepository $companyRepository, OpportunityNotificationConfig $config, Company $company): float|int
    {
        if ($this->sendTest)
            return 0;

        $timeZone = $companyRepository->getTimezone($company);
        $sendTime = Carbon::createFromFormat('H:i:s', $config->send_time, $timeZone)
            ->setDay(now()->day); //ensure send date is the same date as 'today' on the server

        $delay = (int) now($timeZone)->diffInSeconds($sendTime, false);

        return max($delay, 0); //if the send time is set earlier than the job schedule, send immediately
    }

    /**
     * @return void
     */
    protected function getNotificationConfigs(): void
    {
        $dayOfWeek = Carbon::now()->dayOfWeek;

        $this->notificationConfigs = OpportunityNotificationConfig::query()
            ->where(OpportunityNotificationConfig::FIELD_ACTIVE, true)
            ->where(OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS, 'like', "%$dayOfWeek%")
            ->where(OpportunityNotificationConfig::FIELD_FILTER_PRESET_ID, '>', 0)
            ->where(OpportunityNotificationConfig::FIELD_TYPE, OpportunityNotificationConfigType::CAMPAIGN_SUMMARY)
            ->get();
    }

    /**
     * Check if the configuration has expired. Disable it if so.
     *
     * @param OpportunityNotificationConfig $config
     * @return bool
     */
    protected function configExpired(OpportunityNotificationConfig $config): bool
    {
        if ($config->expires_at > Carbon::createFromFormat('H:i:s', $config->send_time))
            return false;

        $config->update([OpportunityNotificationConfig::FIELD_ACTIVE => false]);
        return true;
    }

    /**
     * @param Company $company
     * @param OpportunityNotificationConfig $config
     * @param MissedProductRepository $missedProductRepository
     * @return bool
     */
    protected function companyExceedsMissedProductThresholds(Company $company, OpportunityNotificationConfig $config, MissedProductRepository $missedProductRepository): bool
    {
        $missedProductCount = $missedProductRepository->getMissedProductsForExistingCompanyQuery($company->id, $config->getStartDate())
            ->count();
        if ($missedProductCount < $config->lead_threshold)
            return false;

        $ids = $company->futureCampaigns()->pluck(CompanyCampaign::FIELD_ID);
        foreach($ids as $id) {
            if ($missedProductRepository->getMissedProductsForCompanyCampaignQuery($id, $config->getStartDate())->count() >= $config->campaign_threshold)
                return true;
        }

        return false;
    }
}
