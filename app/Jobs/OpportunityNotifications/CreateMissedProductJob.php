<?php

namespace App\Jobs\OpportunityNotifications;

use App\Enums\Odin\Product as ProductEnum;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ServiceProduct;
use App\Services\MissedProducts\MissedProductService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateMissedProductJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param Consumer $consumer
     */
    public function __construct(protected Consumer $consumer)
    {
        $this->onQueue('default');
    }

    /**
     * @return string
     */
    public function uniqueId(): string
    {
        return $this->consumer->id;
    }

    /**
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        /** @var MissedProductService $missedProductService */
        $missedProductService = app(MissedProductService::class);
        $leadProductId = ProductEnum::LEAD->model()->id;

        $this->consumer
            ->consumerProducts()
            ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->where(ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_PRODUCT_ID, $leadProductId)
            ->select(ConsumerProduct::TABLE .'.*')
            ->get()
            ->each(fn($consumerProduct) => $missedProductService->createNewMissedProduct($consumerProduct));
    }
}