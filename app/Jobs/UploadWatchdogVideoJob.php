<?php

namespace App\Jobs;

use App\Actions\ForceUploadWatchdogVideo;
use App\Exceptions\WatchdogException;
use App\Models\Odin\ProductAssignment;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class UploadWatchdogVideoJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(public int $productAssignmentId) {}

    /**
     * Execute the job.
     * @throws WatchdogException
     */
    public function handle(ForceUploadWatchdogVideo $forceUploadWatchdogVideo): void
    {
        $productAssignment = ProductAssignment::query()->findOrFail($this->productAssignmentId);

        foreach ($productAssignment->consumerProduct->consumer->watchdogVideos as $watchdogVideo) {
            try {
                $forceUploadWatchdogVideo->handle($watchdogVideo->watchdog_video_id);
            } catch (Exception $e) {
                throw new WatchdogException("Watchdog video upload was failed for consumer: {$productAssignment->consumerProduct->consumer->id}, error: {$e->getMessage()}");
            }
        }
    }
}
