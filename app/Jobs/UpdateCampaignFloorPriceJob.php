<?php

namespace App\Jobs;

use App\Enums\LoweredFloorPricePolicy;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Services\Campaigns\CustomCampaignFloorPricingService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Message;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class UpdateCampaignFloorPriceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected Collection $campaigns,
        protected array $updatedPrices,
        protected bool $useCustomFloorPrices,
        protected ?LoweredFloorPricePolicy $loweredFloorPricePolicy = null,
        protected ?string $notifyEmail = null
    )
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     * @throws Exception
     */
    public function handle(CustomCampaignFloorPricingService $service): void
    {
        $campaignsUpdated = $service->handleCampaignFloorPricesUpdate(
            campaigns: $this->campaigns,
            updatedPrices: $this->updatedPrices,
            useCustomFloorPrices: $this->useCustomFloorPrices,
            loweredFloorPricePolicy: $this->loweredFloorPricePolicy,
        );

        if ($this->notifyEmail) {
            $this->notify($campaignsUpdated);
        }
    }

    /**
     * @param int $campaignsUpdated
     *
     * @return void
     */
    protected function notify(int $campaignsUpdated): void
    {
        /** @var Company|null $company */
        $company = $this->campaigns->first()?->{CompanyCampaign::RELATION_COMPANY};
        $subject = "Custom price update status for company {$company?->name}";
        $message = $campaignsUpdated === 0 ? 'There was an error updating custom floor pricing.' :
            ($campaignsUpdated === $this->campaigns->count() ? 'Custom prices were successfully updated.' : 'Some of the campaigns failed to update.');

        Mail::html("<p style='padding: 10px'>$message</p>", fn(Message $mail) => $mail->to($this->notifyEmail)->subject($subject));
    }
}
