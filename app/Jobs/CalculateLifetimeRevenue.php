<?php

namespace App\Jobs;

use App\Models\Odin\Company;
use App\Repositories\Odin\RevenueRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class CalculateLifetimeRevenue implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $companyId,
    )
    {
    }

    /**
     * Execute the job.
     */
    public function handle(RevenueRepository $repository): void
    {
        $company = Company::query()->findOrFail($this->companyId);

        $lifetimeRevenue = $repository->getRevenueOfAllTime(company: $company) * 100;

        $company->legacy_lifetime_revenue_cent = $lifetimeRevenue;

        $company->save();
    }
}
