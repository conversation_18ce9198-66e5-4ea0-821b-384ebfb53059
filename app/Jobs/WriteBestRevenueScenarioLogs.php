<?php

namespace App\Jobs;

use App\Models\BestRevenueScenarioLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class WriteBestRevenueScenarioLogs implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private int $runId,
        private int $consumerProductId,
        private int $step,
        private Collection $campaignMessages
    )
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach($this->campaignMessages as $saleTypeId => $saleTypeCampaignMessages) {
            $insertRows = [];

            foreach($saleTypeCampaignMessages as $campaignId => $messageId) {
                $insertRows[] = [
                    BestRevenueScenarioLog::FIELD_RUN_ID => $this->runId,
                    BestRevenueScenarioLog::FIELD_CONSUMER_PRODUCT_ID => $this->consumerProductId,
                    BestRevenueScenarioLog::FIELD_SALE_TYPE_ID => $saleTypeId,
                    BestRevenueScenarioLog::FIELD_PRODUCT_CAMPAIGN_ID => $campaignId,
                    BestRevenueScenarioLog::FIELD_STEP => $this->step,
                    BestRevenueScenarioLog::FIELD_MESSAGE => BestRevenueScenarioLog::MESSAGES[$messageId]
                ];

                if(count($insertRows) >= 100) {
                    BestRevenueScenarioLog::query()->insert($insertRows);

                    $insertRows = [];
                }
            }

            if(!empty($insertRows)) {
                BestRevenueScenarioLog::query()->insert($insertRows);
            }
        }
    }

    /**
     * @return string
     */
    public function uniqueId()
    {
        return "{$this->runId}-{$this->step}-{$this->consumerProductId}";
    }
}
