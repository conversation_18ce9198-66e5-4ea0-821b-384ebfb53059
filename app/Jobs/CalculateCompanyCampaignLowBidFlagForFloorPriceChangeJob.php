<?php

namespace App\Jobs;


use App\Models\Odin\Industry;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * This dispatches a unique, delayed job for Minimum Price changes to avoid constantly running it
 *  when multiple state & county price changes are being implemented
 */
class CalculateCompanyCampaignLowBidFlagForFloorPriceChangeJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected Industry $industry,
    )
    {
        // Delay for an hour
        $this->delay = 60 * 60 * 1;
    }

    public function uniqueId(): string
    {
        return $this->industry->slug;
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        CalculateCompanyCampaignLowBidFlagJob::dispatch(null, $this->industry);
    }
}
