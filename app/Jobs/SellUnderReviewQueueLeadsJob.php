<?php

namespace App\Jobs;

use App\ConsumerProcessing\Repositories\AvailableCompanyCampaignRepository;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\LeadProcessingUnderReview;
use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\Odin\ConsumerProductService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;

class SellUnderReviewQueueLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    public int $tries = 1;

    public function __construct(
        public ConsumerProduct $consumerProduct,
        public ?LeadProcessor $processor = null,
        public ?string $reason = null,
        public ?string $comment = null,
        public ?bool $publicComment = null,
    )
    {
        $this->onQueue(config('queue.named_queues.lead_allocation_queue'));
    }

    /**
     * @param AvailableCompanyCampaignRepository $availableCompanyCampaignRepository
     * @param ConsumerProductLifecycleTrackingService $tracker
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function handle(
        AvailableCompanyCampaignRepository $availableCompanyCampaignRepository,
        ConsumerProductLifecycleTrackingService $tracker
    ): void
    {
        $consumerProject = new ConsumerProject(
            consumer         : $this->consumerProduct->consumer,
            address          : $this->consumerProduct->address,
            last_qualified_at: null,
            underReview      : true
        );

        $tracker->beginAllocationAttempt(
            consumerProduct  : $consumerProject->leadConsumerProduct(),
            allocationJobUuid: $this->job?->uuid(),
            attemptNumber    : 1
        );

        $campaigns = $availableCompanyCampaignRepository->getAvailableCampaignsForProject(
            project: $consumerProject
        );

        $tracker->campaigns(
            campaignIds: $campaigns->pluck(CompanyCampaign::FIELD_ID)->toArray()
        );

        if ($campaigns->count() < 1) {
            $this->handleNoCompanies();
            return;
        }

        /** @var MultiProductAssignmentStrategyContract $assignmentStrategy */
        $assignmentStrategy = app(MultiProductAssignmentStrategyContract::class);

        /** @var ConsumerProjectProcessingService $processingService */
        $processingService = app()->make(ConsumerProjectProcessingService::class);

        $proposedAssignments = $assignmentStrategy->calculate(
            project     : $consumerProject,
            campaigns   : $campaigns,
            productTypes: $processingService->getPotentialProductTypes($consumerProject),
            payload     : []
        )->map(fn (ProposedProductAssignment $assignment) => $assignment->soldWhileUnderReview = true);

        $tracker->proposedAssignments(proposedAssignments: $proposedAssignments);

        $isFullySold = $consumerProject->max_contact_requests === $proposedAssignments->count();

        $notEnoughCampaignsAvailable = !$consumerProject->had_budget_coverage && !$isFullySold;

        if ($notEnoughCampaignsAvailable) {
            if ($proposedAssignments->count() < 1) {
                $this->handleNoCompaniesWithBudget();
            } else {
                $this->handleUndersold();
            }
        }

        $newAssignments = $this->getNewAssignments($proposedAssignments);
        $updatedAssignments = $this->getUpdatedAssignments($proposedAssignments);
        $processingService->updateExistingProductAssignments($updatedAssignments);

        if (!$processingService->companiesEligibleToReceiveAssignments($consumerProject, $newAssignments)) {
            $this->handleCompaniesNotEligible();
            return;
        }

        /** @var CampaignMediator $campaignMediator */
        $campaignMediator = app()->make(CampaignMediator::class);

        $allocationData = $campaignMediator->allocate($newAssignments);
        $campaignMediator->postAllocation($consumerProject, $allocationData->getProductAssignments());
        $tracker->allocationData($allocationData);
        $tracker->concludeAttempt($allocationData->hasAssignments() ? ConsumerProductLifecycleTrackingService::CONCLUSION_ALLOCATED : ConsumerProductLifecycleTrackingService::CONCLUSION_UNSOLD);

        $this->handleConsumerProductStatusUpdates($consumerProject);

        if ($isFullySold) {
            $this->removeLeadFromTheUnderReviewQueue();
        }
    }

    public function removeLeadFromTheUnderReviewQueue(): void
    {
        LeadProcessingUnderReview::query()
            ->where(LeadProcessingUnderReview::FIELD_CONSUMER_PRODUCT_ID, $this->consumerProduct->id)
            ->delete();
    }

    /**
     * @param ConsumerProject $consumerProject
     * @return void
     * @throws BindingResolutionException
     */
    private function handleConsumerProductStatusUpdates(ConsumerProject $consumerProject): void
    {
        /** @var ConsumerProductService $consumerProductService */
        $consumerProductService = app()->make(ConsumerProductService::class);

        $consumerProject->consumer->consumerProducts()
            ->select(ConsumerProduct::FIELD_ID)
            ->get()
            ->each(fn(ConsumerProduct $consumerProduct) => $consumerProductService->updateConsumerProductStatus($consumerProduct->id));
    }

    /**
     * @param Collection $proposedAssignments
     * @return Collection
     */
    private function getNewAssignments(Collection $proposedAssignments): Collection
    {
        return $proposedAssignments->filter(function (ProposedProductAssignment $assignment) {
            return !$assignment->isExistingAssignment;
        });
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return Collection<int, ProposedProductAssignment>
     */
    private function getUpdatedAssignments(Collection $proposedAssignments): Collection
    {
        return $proposedAssignments->filter(function (ProposedProductAssignment $assignment) {
            return $assignment->salesTypeAndPriceUpdated;
        });
    }

    /**
     * @return void
     */
    private function handleCompaniesNotEligible(): void
    {

    }

    /**
     * @return void
     */
    private function handleNoCompanies(): void
    {

    }

    /**
     * @return void
     */
    private function handleNoCompaniesWithBudget(): void
    {

    }

    /**
     * @return void
     */
    private function handleUndersold(): void
    {

    }
}
