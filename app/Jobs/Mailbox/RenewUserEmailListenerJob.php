<?php

namespace App\Jobs\Mailbox;

use App\Enums\AppFeature;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Mailbox\MailboxUserEmailListener;
use App\Models\User;
use App\Services\AppLogger;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RenewUserEmailListenerJob implements ShouldQueue
{
    const int CHUNK_SIZE = 100;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AppLogger $appLogger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->queue = 'mailbox_renew_user_email_listener';
        $this->appLogger = AppLogger::make(
            feature : AppFeature::MAILBOX,
            function: 'renew_user_email_listener',
        );
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        MailboxUserEmailListener::query()
            ->where(MailboxUserEmailListener::FIELD_EXPIRES_AT, '>=', now()->startOfDay())
            ->where(MailboxUserEmailListener::FIELD_EXPIRES_AT, '<=', now()->endOfDay())
            ->chunk(self::CHUNK_SIZE, function ($expiredEmailListeners) {
                foreach ($expiredEmailListeners as $emailListener) {
                    try {
                        SetupUserEmailsListenerJob::dispatch($emailListener->{MailboxUserEmailListener::RELATION_USER});

                        DispatchPubSubEvent::dispatch(
                            EventCategory::MAILBOX,
                            EventName::MAILBOX_RENEW_MAILBOX_LISTENER_SUCCESS,
                            [
                                'user_id' => $emailListener->{MailboxUserEmailListener::RELATION_USER}->{User::FIELD_ID}
                            ]
                        );
                    } catch (Exception $exception) {
                        $this->appLogger->exception(
                            exception: $exception,
                            message  : 'Failed to renew user email listener',
                            relations: [
                                $emailListener
                            ],
                        );

                        DispatchPubSubEvent::dispatch(
                            EventCategory::MAILBOX,
                            EventName::MAILBOX_RENEW_MAILBOX_LISTENER_ERROR,
                            [
                                'user_id'           => $emailListener->{MailboxUserEmailListener::RELATION_USER}->{User::FIELD_ID},
                                'exception'         => $exception,
                                'exception_message' => $exception->getMessage(),
                            ]
                        );
                    }
                }
            });
    }
}
