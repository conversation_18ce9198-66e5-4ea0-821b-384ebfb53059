<?php

namespace App\Jobs\Mailbox;

use App\Enums\Mailbox\EmailModificationAction;
use App\Models\Mailbox\MailboxUserEmail;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\Mailbox\Mail\MailProviderFactory;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ModifyEmailInMailProviderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected MailboxUserEmail $email, protected EmailModificationAction $action)
    {
        $this->queue = 'mailbox_modify_email_in_mail_provider';
    }

    /**
     * Execute the job.
     *
     * @param MailboxUserTokenRepository $mailboxUserTokenRepository
     * @return void
     * @throws Exception
     */
    public function handle(MailboxUserTokenRepository $mailboxUserTokenRepository): void
    {
        $mailboxUserToken = $mailboxUserTokenRepository->getLatestUserEmailToken($this->email->{MailboxUserEmail::RELATION_USER});

        $mailProvider = MailProviderFactory::make();

        match ($this->action) {
            EmailModificationAction::READ        => $mailProvider->readEmail($mailboxUserToken, $this->email),
            EmailModificationAction::UNREAD      => $mailProvider->unreadEmail($mailboxUserToken, $this->email),
            EmailModificationAction::STAR        => $mailProvider->starEmail($mailboxUserToken, $this->email),
            EmailModificationAction::UNSTAR      => $mailProvider->unstarEmail($mailboxUserToken, $this->email),
            EmailModificationAction::ARCHIVE     => $mailProvider->archiveEmail($mailboxUserToken, $this->email),
            EmailModificationAction::UNARCHIVE   => $mailProvider->unarchiveEmail($mailboxUserToken, $this->email),
            EmailModificationAction::IMPORTANT   => $mailProvider->importantEmail($mailboxUserToken, $this->email),
            EmailModificationAction::UNIMPORTANT => $mailProvider->unimportantEmail($mailboxUserToken, $this->email),
            EmailModificationAction::DELETE      => $mailProvider->deleteEmail($mailboxUserToken, $this->email),
            default                              => throw new Exception('EmailModificationAction not found')
        };
    }
}
