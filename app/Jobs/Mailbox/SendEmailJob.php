<?php

namespace App\Jobs\Mailbox;

use App\DTO\Mail\SendEmailParam;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\Mailbox\EmailRecipientType;
use App\Enums\Mailbox\EmailType;
use App\Jobs\DispatchPubSubEvent;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailRecipient;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\User;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\Mailbox\Mail\MailProviderFactory;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use JetBrains\PhpStorm\ArrayShape;

class SendEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public MailboxUserEmail $mailboxUserEmail,
        public ?MailboxUserEmail $replyTo = null,
    )
    {
        $this->queue = 'mailbox_send_email';
    }

    /**
     * @param null $exception
     * @return void
     */
    public function failed($exception = null): void
    {
        DispatchPubSubEvent::dispatch(
            EventCategory::MAILBOX,
            EventName::MAILBOX_EMAIL_SEND_ERROR,
            [
                'email_id'          => $this->mailboxUserEmail->{MailboxEmail::FIELD_ID},
                'exception_message' => $exception?->getMessage(),
                'exception'         => $exception,
            ]
        );
    }

    /**
     * Execute the job.
     *
     * @param MailboxUserTokenRepository $mailboxUserRepository
     * @return void
     * @throws Exception
     */
    public function handle(MailboxUserTokenRepository $mailboxUserRepository): void
    {
        $mailboxEmail = $this->mailboxUserEmail->{MailboxUserEmail::RELATION_EMAIL};

        $from = $mailboxEmail->{MailboxEmail::RELATION_FROM_USER};

        $token = $mailboxUserRepository->getLatestUserEmailToken($from);

        $mailer = MailProviderFactory::make();

        $emailsTarget = $this->getEmailsTarget($mailboxEmail);

        $sendEmailParamDTO = new SendEmailParam(
            Arr::get($emailsTarget, 'to', []),
            $from->{User::FIELD_EMAIL},
            $mailboxEmail->{MailboxEmail::FIELD_SUBJECT},
            $mailboxEmail->{MailboxEmail::FIELD_CONTENT},
            Arr::get($emailsTarget, 'bcc', []),
            Arr::get($emailsTarget, 'cc', []),
        );

        if ($mailboxEmail->{MailboxEmail::FIELD_TYPE} === EmailType::REPLY) {
            if (!isset($this->replyTo)) throw new Exception('Email to reply not set');
            $sentEmail = $mailer->replyEmail($token, $this->replyTo, $sendEmailParamDTO);
        } else {
            $sentEmail = $mailer->sendEmail($token, $sendEmailParamDTO);
        }

        $this->mailboxUserEmail->update([
            MailboxUserEmail::FIELD_EXTERNAL_ID         => $sentEmail->getId(),
            MailboxUserEmail::FIELD_EXTERNAL_HISTORY_ID => $sentEmail->getHistoryId(),
            MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID  => $sentEmail->getThreadId(),
            MailboxUserEmail::FIELD_EXTERNAL_REFERENCES => $sentEmail->getMeta()->getReferences(),
            MailboxUserEmail::FIELD_SENT_AT             => now(),
        ]);

        $mailboxEmail->update([
            MailboxEmail::FIELD_EXTERNAL_MESSAGE_ID => $sentEmail->getMeta()->getMessageId(),
            MailboxEmail::FIELD_SENT_AT             => now(),
        ]);

        DispatchPubSubEvent::dispatch(
            EventCategory::MAILBOX,
            EventName::MAILBOX_EMAIL_SEND_SUCCESS,
            [
                'user_email_id' => $this->mailboxUserEmail->{MailboxEmail::FIELD_ID}
            ]
        );
    }

    /**
     * Return all targets emails split by type
     * @return array[]
     */
    #[ArrayShape(['to' => 'array', 'cc' => 'array', 'bcc' => 'array'])]
    private function getEmailsTarget(MailboxEmail $email): array
    {
        $emailRecipients = $email->{MailboxEmail::RELATION_RECIPIENTS};

        $to = [];
        $bcc = [];
        $cc = [];

        foreach ($emailRecipients as $emailRecipient) {
            $type = $emailRecipient->{MailboxEmailRecipient::FIELD_TYPE};
            $emailAddress = $emailRecipient->{MailboxEmailRecipient::RELATION_IDENTIFIED_CONTACT}->{IdentifiedContact::FIELD_IDENTIFIER_VALUE};

            switch ($type) {
                case EmailRecipientType::TO->value:
                    $to[] = $emailAddress;
                    break;
                case EmailRecipientType::BCC->value:
                    $bcc[] = $emailAddress;
                    break;
                case EmailRecipientType::CC->value:
                    $cc[] = $emailAddress;
                    break;
            }
        }

        return [
            'to'  => $to,
            'bcc' => $bcc,
            'cc'  => $cc,
        ];
    }
}
