<?php

namespace App\Jobs\Mailbox;

use App\DTO\Mail\Email;
use App\Enums\ActivityType;
use App\Enums\AppFeature;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Repositories\ActivityFeedRepository;
use App\Services\AppLogger;
use App\Services\ContactIdentification\ContactIdentificationService;
use App\Services\Mailbox\MailboxEmailImportValidator;
use App\Services\Mailbox\SyncMailboxService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class SyncUserEmailsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected SyncMailboxService           $syncMailboxService;
    protected ContactIdentificationService $contactIdentificationService;
    protected ActivityFeedRepository       $activityFeedRepository;
    protected AppLogger                    $appLogger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected string|Email $email,
        protected User $user,
        protected bool $dispatchEmailReceivedEvent = false
    )
    {
        $this->queue = 'mailbox_sync_user_emails';
    }

    /**
     * @return Email|null
     * @throws Exception
     */
    protected function getEmail(): ?Email
    {
        return gettype($this->email) === 'string' ? $this->syncMailboxService->getMessage(
            user     : $this->user,
            messageId: $this->email
        ) : $this->email;
    }

    /**
     * @param string $email
     * @return IdentifiedContact
     */
    private function identifyContact(string $email): IdentifiedContact
    {
        return $this->contactIdentificationService->createIdentifiedContactAndDispatchJob(
            identifierValue: $email,
            type           : SearchableFieldType::EMAIL,
            syncronous     : true
        );
    }

    /**
     * Execute the job.
     * TODO - We could perform one query with union all and where in all emails?
     * @param SyncMailboxService $syncMailboxService
     * @param ContactIdentificationService $contactIdentificationService
     * @param ActivityFeedRepository $activityFeedRepository
     * @param MailboxEmailImportValidator $mailboxEmailImportValidator
     * @return void
     * @throws Exception
     */
    public function handle(
        SyncMailboxService $syncMailboxService,
        ContactIdentificationService $contactIdentificationService,
        ActivityFeedRepository $activityFeedRepository,
        MailboxEmailImportValidator $mailboxEmailImportValidator
    ): void
    {
        $this->appLogger = AppLogger::make(
            relations: [$this->user],
            feature  : AppFeature::MAILBOX,
            function : 'sync_user_emails_job'
        );
        $this->syncMailboxService = $syncMailboxService;
        $this->contactIdentificationService = $contactIdentificationService;
        $this->activityFeedRepository = $activityFeedRepository;

        $email = $this->getEmail();

        if (empty($email)) {
            $this->appLogger->info(
                message: 'Email not found',
            );
            return;
        }

        $this->email = $email;

        $isValid = $mailboxEmailImportValidator->checkIfEmailSatisfiesStoredRules($this->email);

        if (!$isValid) {
            $this->email->setContent('REDACTED');
            $this->email->setSnippet('REDACTED');
            $meta = $this->email->getMeta();
            $meta->setSubject('REDACTED');
            $this->email->setMeta($meta);
        }

        $fromIdentifiedContact = $this->identifyContact($this->email->getMeta()->getFrom());

        $recipientEmails = collect([
            $this->email->getMeta()->getTo(),
            $this->email->getMeta()->getCc(),
            $this->email->getMeta()->getBcc(),
        ])
            ->filter()
            ->flatten()
            ->unique();

        $recipientIdentifiedContacts = $recipientEmails->map(fn($email) => $this->identifyContact($email));

        // Should remove spams and non-relevant emails
        $isEmailRelevant = $fromIdentifiedContact->isKnown()
            && $recipientIdentifiedContacts->some(fn(IdentifiedContact $recipientIdentified) => $recipientIdentified->isKnown());

        $this->appLogger->debug(
            message: 'Importing email',
            context: [
                'is_relevant' => $isEmailRelevant,
                'subject'     => $email->getMeta()->getSubject(),
                'to'          => $email->getMeta()->getTo(),
                'cc'          => $email->getMeta()->getCc(),
                'bcc'         => $email->getMeta()->getBcc(),
                'from'        => $email->getMeta()->getfrom(),
            ],
        );

        if ($isEmailRelevant) {
            $mailboxEmail = $syncMailboxService->syncEmail(
                email                     : $this->email,
                user                      : $this->user,
                dispatchEmailReceivedEvent: $this->dispatchEmailReceivedEvent,
            );

            $companyUserEmails = $this->getCompanyUserEmails(
                fromContact: $fromIdentifiedContact,
                recipients : $recipientIdentifiedContacts,
            );

            if ($companyUserEmails->isNotEmpty()) {
                $this->createCompanyActivityFeed(
                    mailboxEmail     : $mailboxEmail,
                    companyUserEmails: $companyUserEmails
                );
            }
        }
    }

    /**
     * @param IdentifiedContact $fromContact
     * @param Collection $recipients
     * @return Collection
     */
    public function getCompanyUserEmails(IdentifiedContact $fromContact, Collection $recipients): Collection
    {
        $emails = User::query()
            ->select([User::FIELD_EMAIL, User::FIELD_EMAIL_ALIASES])
            ->get();

        $emails = $emails->flatMap(function ($user) {
            return [
                $user->email,
                ...($user->email_aliases ?? [])
            ];
        })->unique();

        $allCompanyUserEmails = collect([
            $fromContact,
            ...$recipients
        ])->flatten()
            ->filter(function (IdentifiedContact $identifiedContact) {
                return $identifiedContact->possibleContacts?->some(fn(PossibleContact $contact) => $contact->isCompanyUser());
            })
            ->map(fn(IdentifiedContact $identifiedContact) => $identifiedContact->identifier_value)
            ->unique()
            ->filter()
            ->values();

        return $allCompanyUserEmails->diff($emails);
    }

    /**
     * @param MailboxEmail $mailboxEmail
     * @param Collection $companyUserEmails
     * @return void
     */
    public function createCompanyActivityFeed(MailboxEmail $mailboxEmail, Collection $companyUserEmails): void
    {
        $companyIds = CompanyUser::query()
            ->whereIn(CompanyUser::FIELD_EMAIL, $companyUserEmails)
            ->get()
            ->pluck(CompanyUser::FIELD_COMPANY_ID)
            ->unique();

        $this->appLogger->info(
            message  : 'Creating email activity feed for companies',
            context  : [
                'company_ids' => $companyIds,
            ],
            relations: [$mailboxEmail]
        );

        foreach ($companyIds as $companyId) {
            $this->activityFeedRepository->firstOrCreateByItemAndCompany(
                itemId   : $mailboxEmail->id,
                itemType : ActivityType::MAILBOX_EMAIL,
                companyId: $companyId,
                userId   : $this->user->id,
                createdAt: Carbon::parse($mailboxEmail->{MailboxEmail::FIELD_SENT_AT})
            );
        }
    }
}
