<?php

namespace App\Jobs\Mailbox;

use App\DTO\Mail\ListEmailQueryDTO;
use App\Models\Odin\CompanyUser;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class ImportCompanyEmailsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    CONST int MAX_QUERY_LENGTH = 2048;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public int $companyId,
    )
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    /**
     * @param Collection $emails
     * @return Collection
     */
    protected function buildQueries(Collection $emails): Collection
    {
        $queries = collect();
        $currentQuery = '';

        foreach ($emails as $email) {
            $orCondition = $email;

            if (!empty($currentQuery) && strlen($currentQuery . " OR " . $orCondition) > self::MAX_QUERY_LENGTH) {
                $queries[] = $currentQuery;
                $currentQuery = $orCondition;
            } else {
                $currentQuery .= (empty($currentQuery) ? '' : ' OR ') . $orCondition;
            }
        }

        if (!empty($currentQuery)) {
            $queries[] = $currentQuery;
        }

        return $queries;
    }

    /**
     * Execute the job.
     *
     * @param MailboxUserTokenRepository $mailboxUserRepository
     * @return void
     * @throws Exception
     */
    public function handle(MailboxUserTokenRepository $mailboxUserRepository): void
    {
        logger()->info('Importing emails from company id ' . $this->companyId);
        $mailboxUsers = $mailboxUserRepository->getAllMailboxUsers();

        logger()->info('Mailbox users count ' . $mailboxUsers->count());

        $companyUserEmails = $this->getUniqueCompanyEmails($this->companyId);

        if ($companyUserEmails->isEmpty()) {
            logger()->info('No company user emails found');
            return;
        }

        $queries = $this->buildQueries($companyUserEmails);

        $this->dispatchSearchJobs($mailboxUsers, $queries);
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getUniqueCompanyEmails(int $companyId): Collection
    {
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $companyId)
            ->get()
            ->pluck(CompanyUser::FIELD_EMAIL)
            ->filter()
            ->unique();
    }

    /**
     * @param Collection $mailboxUsers
     * @param Collection $companyUserEmailQueries
     * @return void
     */
    protected function dispatchSearchJobs(Collection $mailboxUsers, Collection $companyUserEmailQueries): void
    {
        foreach ($mailboxUsers as $user) {
            foreach ($companyUserEmailQueries as $query) {
                $listingQuery = new ListEmailQueryDTO(
                    rawQuery: $query
                );

                SyncUserMailboxJob::dispatch(
                    $user,
                    null,
                    null,
                    $listingQuery->toArray()
                );
            }
        }
    }
}
