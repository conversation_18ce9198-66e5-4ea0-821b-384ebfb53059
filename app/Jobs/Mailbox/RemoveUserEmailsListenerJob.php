<?php

namespace App\Jobs\Mailbox;

use App\Models\User;
use App\Services\Mailbox\SyncMailboxService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RemoveUserEmailsListenerJ<PERSON> implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public User $user)
    {
        $this->queue = 'mailbox_setup_emails_listener';
    }

    /**
     * Execute the job.
     *
     * @param SyncMailboxService $syncMailboxService
     * @return void
     * @throws Exception
     */
    public function handle(SyncMailboxService $syncMailboxService): void
    {
        $syncMailboxService->removeUserEmailsListener($this->user);
    }
}
