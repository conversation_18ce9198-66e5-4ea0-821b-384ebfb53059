<?php

namespace App\Jobs\Mailbox;

use App\DTO\Mail\ListEmailQueryDTO;
use App\Enums\AppFeature;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Models\User;
use App\Services\AppLogger;
use App\Services\Mailbox\SyncMailboxService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncUserMailboxJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int EMAILS_PER_PAGE = 50;

    protected SyncMailboxService $syncMailboxService;

    protected ListEmailQueryDTO $emailListingQuery;
    protected AppLogger         $appLogger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected User $user,
        protected ?string $externalMessageId = null,
        protected ?string $nextPageToken = null,
        ?array $query = []
    )
    {
        $this->queue = 'mailbox_sync_user_emails';
        $this->emailListingQuery = ListEmailQueryDTO::fromArray($query ?? []);
        $this->appLogger = AppLogger::make(
            relations: [$user],
            feature  : AppFeature::MAILBOX,
            function : 'sync_user_mailbox'
        );
    }

    /**
     * @param string $messageId
     * @param User $user
     * @return void
     */
    public function syncSingleEmail(
        string $messageId,
        User $user
    ): void
    {
        try {
            SyncUserEmailsJob::dispatchSync(
                $messageId,
                $user,
            );
        } catch (Exception $exception) {
            $this->appLogger->exception(
                exception: $exception,
                message  : 'Failed to sync email',
                context  : [
                    'message_id' => $messageId,
                ],
            );
        }
    }

    /**
     * Execute the job.
     *
     * @param SyncMailboxService $syncMailboxService
     * @return void
     * @throws Exception
     */
    public function handle(SyncMailboxService $syncMailboxService): void
    {
        $this->syncMailboxService = $syncMailboxService;

        if (filled($this->externalMessageId)) {
            $this->syncSingleEmail(
                messageId: $this->externalMessageId,
                user     : $this->user
            );
        } else {
            $this->syncAllEmails(
                emailQuery: $this->emailListingQuery
            );
        }
    }

    /**
     * @throws Exception
     */
    public function syncAllEmails(ListEmailQueryDTO $emailQuery = new ListEmailQueryDTO()): void
    {
        [
            'email_ids'       => $emailIds,
            'next_page_token' => $nextPageToken,
        ] = $this->syncMailboxService->getMessageIds(
            user         : $this->user,
            perPage      : self::EMAILS_PER_PAGE,
            nextPageToken: $this->nextPageToken,
            query        : $emailQuery
        );

        foreach ($emailIds as $emailId) {
            $this->syncSingleEmail(
                messageId: $emailId,
                user     : $this->user
            );
        }

        if (filled($nextPageToken)) {
            SyncUserMailboxJob::dispatch($this->user, null, $nextPageToken, $this->emailListingQuery->toArray());
            return;
        }

        DispatchPubSubEvent::dispatch(
            EventCategory::MAILBOX,
            EventName::MAILBOX_SYNC_FINISHED,
            [
                'user_id' => $this->user->{User::FIELD_ID},
            ]
        );
    }

    /**
     * @param null $exception
     * @return void
     */
    public function failed($exception = null): void
    {
        if ($exception) {
            $this->appLogger->exception(
                exception: $exception,
                message  : 'Job failed'
            );
        }

        DispatchPubSubEvent::dispatch(
            EventCategory::MAILBOX,
            EventName::MAILBOX_SYNC_ERROR,
            [
                'user_id'           => $this->user->{User::FIELD_ID},
                'exception'         => $exception,
                'exception_message' => $exception?->getMessage(),
            ]
        );
    }
}
