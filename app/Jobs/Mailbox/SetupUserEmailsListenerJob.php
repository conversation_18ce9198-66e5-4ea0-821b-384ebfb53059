<?php

namespace App\Jobs\Mailbox;

use App\Enums\AppFeature;
use App\Models\User;
use App\Services\AppLogger;
use App\Services\Mailbox\SyncMailboxService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SetupUserEmailsListenerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AppLogger $appLogger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public User $user)
    {
        $this->queue = 'mailbox_setup_emails_listener';
        $this->appLogger = AppLogger::make(
            relations: [$user],
            feature  : AppFeature::MAILBOX,
            function : 'mailbox_setup_emails_listener'
        );
    }

    /**
     * Execute the job.
     *
     * @param SyncMailboxService $syncMailboxService
     * @return void
     * @throws Exception
     */
    public function handle(SyncMailboxService $syncMailboxService): void
    {
        try {
            $syncMailboxService->setupUserNewEmailsListener($this->user);
        } catch (Exception $exception) {
            $this->appLogger->exception(
                exception: $exception,
                message: 'Failed to set up user email listener'
            );
        }
    }
}
