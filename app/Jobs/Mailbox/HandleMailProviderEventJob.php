<?php

namespace App\Jobs\Mailbox;

use App\Enums\AppFeature;
use App\Models\Mailbox\MailboxUserEmailHistory;
use App\Models\User;
use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\AppLogger;
use App\Services\Mailbox\Mail\MailProviderFactory;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleMailProviderEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AppLogger $appLogger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public User $user, public int $historyId)
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
        $this->appLogger = AppLogger::make(
            context  : [
                'history_id' => $historyId,
            ],
            relations: [$user],
            feature  : AppFeature::MAILBOX,
            function : 'handle_mail_provider_event',
        );
    }

    /**
     * Execute the job.
     *
     * @param MailboxUserTokenRepository $mailboxUserRepository
     * @return void
     * @throws Exception
     */
    public function handle(MailboxUserTokenRepository $mailboxUserRepository): void
    {
        $mailboxUserToken = $mailboxUserRepository->getLatestUserEmailToken($this->user);

        if (!$mailboxUserToken) {
            $this->appLogger->error(
                message  : 'No email token found for user ' . $this->user->id,
                context  : [
                    'user_id' => $this->user->id
                ],
                relations: [
                    $this->user
                ],
            );
            return;
        }

        $previousHistory = MailboxUserEmailHistory::query()
            ->where(MailboxUserEmailHistory::FIELD_USER_ID, $this->user->{User::FIELD_ID})
            ->first();

        if (!$previousHistory) {
            $mailboxUserEmailHistory = new MailboxUserEmailHistory();

            $mailboxUserEmailHistory->fill([
                MailboxUserEmailHistory::FIELD_USER_ID    => $this->user->{User::FIELD_ID},
                MailboxUserEmailHistory::FIELD_HISTORY_ID => $this->historyId,
                MailboxUserEmailHistory::FIELD_CREATED_AT => now(),
                MailboxUserEmailHistory::FIELD_UPDATED_AT => now(),
            ]);

            $mailboxUserEmailHistory->save();

            return;
        }

        // TODO - Paginate
        MailProviderFactory::make()
            ->getMailboxChangesSinceHistoryId($mailboxUserToken, $previousHistory->{MailboxUserEmailHistory::FIELD_HISTORY_ID})
            ->filter()
            ->unique()
            ->map(function (string $email) {
                SyncUserEmailsJob::dispatch(
                    $email,
                    $this->user,
                    true,
                );
            });

        $previousHistory->update([
            MailboxUserEmailHistory::FIELD_HISTORY_ID => $this->historyId,
        ]);
    }
}
