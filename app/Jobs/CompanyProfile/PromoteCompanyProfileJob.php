<?php

namespace App\Jobs\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Services\CompanyProfile\CompanyProfilePromotionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class PromoteCompanyProfileJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(protected CompanyProfile $companyProfile)
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     *
     * @param CompanyProfilePromotionService $companyProfilePromotionService
     * @throws Throwable
     */
    public function handle(CompanyProfilePromotionService $companyProfilePromotionService): void
    {
        $companyProfilePromotionService->promote($this->companyProfile);
    }
}
