<?php

namespace App\Jobs;

use App\DTO\CompanyCampaignData\CompanyCampaignLeadAggregatesDTO;
use App\Models\CompanyCampaignData;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class IdentifyUpdatedCampaigns implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $campaignIds = CompanyCampaignData::query()
            ->where(
                DB::raw("JSON_EXTRACT(".CompanyCampaignData::FIELD_PAYLOAD.", '$.".CompanyCampaignLeadAggregatesDTO::LEAD_LAST_SOLD_AT."')"),
                '>',
                DB::raw("JSON_EXTRACT(".CompanyCampaignData::FIELD_PAYLOAD.", '$.".CompanyCampaignLeadAggregatesDTO::AVERAGE_LEAD_COST_LAST_CALCULATED_AT."')")
            )
            ->pluck(CompanyCampaignData::FIELD_CAMPAIGN_ID);

        foreach ($campaignIds as $campaignId) {
            CalculateAverageLeadCost::dispatch($campaignId);
        }
    }
}
