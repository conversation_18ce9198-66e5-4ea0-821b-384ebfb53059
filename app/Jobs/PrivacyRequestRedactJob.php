<?php

namespace App\Jobs;

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use App\Events\PrivacyRequestConsumerRedacted;
use App\Models\Odin\Consumer;
use App\Models\PrivacyRequest;
use App\Models\PrivacyRequestRedactedRecords;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use HeapsGoodServices\PrivacyRequest\Services\PrivacyRequestService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class PrivacyRequestRedactJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int  $maxExceptions = 1;
    public bool $failOnTimeout = true;
    public int $timeout = Carbon::SECONDS_PER_MINUTE * 3;
    public int $tries = 1;

    protected string $website;
    protected string $model;
    protected array $object;

    protected array $payload;

    protected int $userId;

    public function __construct(public PrivacyRequest $privacyRequest, string $website, string $model, array $object, int $userId)
    {
        $this->website = $website;
        $this->model = $model;
        $this->object = $object;
        $this->userId = $userId;

        $this->onQueue(QueueHelperService::QUEUE_NAME_PRIVACY);
        $this->onConnection(QueueHelperService::QUEUE_CONNECTION);

        $this->getRedactPayload();
    }

    /**
     * @throws ConnectionException
     */
    public function handle(): void
    {
        $response = Http::withToken(config('privacy-request.access-token'))
            ->put($this->website . config('privacy-request.uri-prefix') . '/redact', $this->payload);

        if($response->getStatusCode() >= 200 && $response->getStatusCode() < 300) {
            $this->createPrivacyRedactRecord();
            $this->redactSearchPayload();
            if($this->model === Consumer::class)
                PrivacyRequestConsumerRedacted::dispatch($this->object['key']);
        }

        $this->privacyRequest->status = PrivacyRequestStatuses::PROCESSING;
        $this->privacyRequest->save();
    }

    protected function getRedactPayload(): void
    {
        $relations = array();

        if(array_key_exists($this->model, config('privacy-request.redact_model_relations'))) {
            $relatedModelsArray = config('privacy-request.redact_model_relations')[$this->model];
            foreach($relatedModelsArray as $relatedModelFunction => $relatedModel) {
                $relations['model'] = $relatedModel;
                $relationModel = app($this->model)::find($this->object['key'])?->{$relatedModelFunction};

                $relations['records'] = [
                    array_merge(['key' => $relationModel->getKey()], $relationModel->toArray())
                ];
            }

            $this->object['relations'] = array($relations);
        }

        $this->payload = [
            'redact' => [
                [
                    'model' => $this->model,
                    'records' => [
                        $this->object
                    ],
                ]
            ]
        ];
    }

    protected function redactSearchPayload(): void
    {
        $privacyRequestService = app(PrivacyRequestService::class);
        $scan_response = $this->privacyRequest->scan_response;

        $models = array_column($scan_response[$this->website], 'model');
        $key = array_search($this->model, $models);

        $modelKeys = array_column($scan_response[$this->website][$key]['records'], 'key');
        $modelKey = array_search($this->object['key'], $modelKeys);

        $record = $scan_response[$this->website][$key]['records'][$modelKey];
        $scan_response[$this->website][$key]['records'][$modelKey] = $privacyRequestService->redactRecord(Arr::except($record, 'relations'));

        if(array_key_exists($this->model, config('privacy-request.redact_model_relations'))) {
            foreach(config('privacy-request.redact_model_relations')[$this->model] as $relatedModelFunction => $relatedModel) {
                if (array_key_exists($relatedModelFunction, $record)) {
                    $scan_response[$this->website][$key]['records'][$modelKey][$relatedModelFunction] = $privacyRequestService->redactRecord($record[$relatedModelFunction]);
                }
            }
        }

        $this->privacyRequest->scan_response = $scan_response;
        $this->privacyRequest->save();
    }

    protected function createPrivacyRedactRecord(): void
    {
        $privacyRedactRecord = new PrivacyRequestRedactedRecords([
            PrivacyRequestRedactedRecords::FIELD_PRIVACY_REQUEST_ID => $this->privacyRequest->id,
            PrivacyRequestRedactedRecords::FIELD_STATUS => 'Success',
            PrivacyRequestRedactedRecords::FIELD_MODEL_TYPE => $this->model,
            PrivacyRequestRedactedRecords::FIELD_MODEL_ID => $this->object['key'],
            PrivacyRequestRedactedRecords::FIELD_WEBSITE => $this->website,
            PrivacyRequestRedactedRecords::FIELD_INITIATOR_ID => $this->userId,
        ]);
        $privacyRedactRecord->save();
    }
}
