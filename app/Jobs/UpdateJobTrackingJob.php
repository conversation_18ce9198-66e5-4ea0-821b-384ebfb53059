<?php

namespace App\Jobs;

use App\Enums\Odin\JobTrackingRelation;
use App\Repositories\Odin\JobTrackingRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateJobTrackingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected JobTrackingRelation $relation, protected string $jobUuid, protected array $payload) {}

    /**
     * Execute the job.
     *
     * @param JobTrackingRepository $jobTrackingRepository
     */
    public function handle(JobTrackingRepository $jobTrackingRepository): void
    {
        $jobTrackingRepository->updateJobTrackingByRelationAndUuid(
            relation: $this->relation,
            jobUuid: $this->jobUuid,
            payload: $this->payload
        );
    }
}
