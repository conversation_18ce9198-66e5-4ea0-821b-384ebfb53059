<?php

namespace App\Jobs;


use App\Enums\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Repositories\AvailableBudgetRepository;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Services\Campaigns\CalculateBudgetService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Class ReviewLeadJob
 * @package App\Jobs
 */
class CalculateAvailableBudgetByZipCodeJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param CalculateBudgetService $service
     * @param AvailableBudgetRepository $availableBudgetRepository
     * @return void
     * @throws BindingResolutionException|Exception
     */
    public function handle(CalculateBudgetService $service, AvailableBudgetRepository $availableBudgetRepository): void
    {
        // todo: In AvailableBudgets table we use 'roofer' but should be using 'roofing'. 'roofer' needs to be updated to 'roofing' system wide as the latter is what is defined in industries table
        // Performance.
        DB::disableQueryLog();

        $this->handleFutureCampaignIndustries($service, $availableBudgetRepository);
    }

    /**
     * @param CalculateBudgetService $calculateBudgetService
     * @param AvailableBudgetRepository $availableBudgetRepository
     *
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    protected function handleFutureCampaignIndustries(CalculateBudgetService $calculateBudgetService, AvailableBudgetRepository $availableBudgetRepository): void
    {
        /** @var CompanyCampaignRepository $campaignRepository */
        $campaignRepository = app()->make(CompanyCampaignRepository::class);

        /** @var Collection<\App\Models\Odin\Industry> $industries */
        $industries = IndustryConfiguration::query()
            ->where(IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
            ->get()
            ->map(fn(IndustryConfiguration $configuration) => $configuration->industry);

        foreach ($industries as $industry) {
            $industryEnum = Industry::tryFrom($industry->name);

            if (!$industryEnum) {
                logger()->error("Enum is missing for industry: $industry");
                continue;
            }

            $availableBudgetRepository->processAndInsertBudgetData(
                $calculateBudgetService->calculateVerifiedBudgetsForFutureCampaign(
                    $campaignRepository->getAvailableCampaignsByIndustry($industryEnum, true, true)
                ),
                $industry->slug
            );
        }
    }
}
