<?php

namespace App\Jobs;

use App\Models\Legacy\EloquentQuote;
use App\Models\LeadRevenueScenario;
use App\Services\Legacy\LeadRevenueScenarioService;
use App\Repositories\Legacy\QuoteRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class CalculateRevenueScenariosJob implements ShouldQueue
{
    use InteractsWithQueue;
    use SerializesAndRestoresModelIdentifiers;
    use Queueable;

    /**
     * @var int
     */
    protected $leadId;

    /**
     * @var string
     */
    protected $type;

    /**
     * @param int $leadId
     * @param string $type
     */
    public function __construct(int $leadId, string $type = LeadRevenueScenario::TYPE_UNVERIFIED)
    {
        $this->leadId = $leadId;
        $this->type = $type;
    }

    /**
     * Handles the calculation of the best revenue scenarios for a given lead.
     *
     * First we calculate the current revenue scenario, followed by the maximum revenue scenario if budget
     * utilization is not taken into account. This is then stored in a dedicated model and will be used for
     * prioritization of queues, and further reporting.
     *
     * @param LeadRevenueScenarioService $service
     * @param QuoteRepository $leadRepository
     * @return void
     */
    public function handle(LeadRevenueScenarioService $service, QuoteRepository $leadRepository)
    {
        $lead = $leadRepository->findOrFail($this->leadId);

        $service->calculateAndStoreRevenueScenarioForLead($lead->{EloquentQuote::REFERENCE}, $this->type);
    }
}
