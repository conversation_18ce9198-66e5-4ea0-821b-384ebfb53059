<?php

namespace App\Jobs;

use App\Services\HistoricalAvailableBudgetCalculationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * Class CalculateHistoricalAvailableBudgetsJob
 * @package App\Jobs
 */
class CalculateHistoricalAvailableBudgetsJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param HistoricalAvailableBudgetCalculationService $service
     * @return void
     */
    public function handle(HistoricalAvailableBudgetCalculationService $service): void
    {
        ini_set('memory_limit','-1');
        $service->calculateAndStoreHistoricalAvailableBudget();
    }
}
