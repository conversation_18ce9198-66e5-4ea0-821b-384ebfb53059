<?php

namespace App\Jobs;

use App\Models\CompanyProfileCallNumber;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class ReleaseCompanyProfileCallNumberJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_LONG_RUNNING);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        CompanyProfileCallNumber::inUse()
            ->where(CompanyProfileCallNumber::FIELD_IN_USE_SINCE, '<=', now()->subHour())
            ->chunk(100, function (Collection $numbers) {
                $updates = $numbers->map(fn(CompanyProfileCallNumber $number) => [
                    CompanyProfileCallNumber::FIELD_ID => $number->id,
                    CompanyProfileCallNumber::FIELD_IN_USE => false,
                    CompanyProfileCallNumber::FIELD_IN_USE_SINCE => null,
                    CompanyProfileCallNumber::UPDATED_AT => now()
                ]);

                CompanyProfileCallNumber::query()->upsert(
                    values: $updates->toArray(),
                    uniqueBy: CompanyProfileCallNumber::FIELD_ID,
                    update: [
                        CompanyProfileCallNumber::FIELD_IN_USE,
                        CompanyProfileCallNumber::FIELD_IN_USE_SINCE,
                        CompanyProfileCallNumber::UPDATED_AT
                    ]
                );
            });
    }
}
