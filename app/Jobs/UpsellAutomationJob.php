<?php

namespace App\Jobs;

use App\Services\Odin\UpsellAutomationService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class UpsellAutomationJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param array|null $config
     */
    public function __construct(
        protected ?array $config = [],
    )
    {
        $this->queue = QueueHelperService::QUEUE_NAME_LONG_RUNNING_SINGLE;
    }

    /**
     * @param UpsellAutomationService $upsellAvailableLegsService
     * @return void
     */
    public function handle(UpsellAutomationService $upsellAvailableLegsService): void
    {
        $upsellAvailableLegsService->upsellAvailableLegs($this->config);
    }
}
