<?php

namespace App\Jobs;

use App\Services\Odin\API\CompanyLocationSiloService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Cache;

class RefreshLocationSiloResultsCache implements ShouldQueue
{
    use Queueable;

    public function __construct(public int $locationSiloPageId, public array $dataRequests) {}

    public function handle(CompanyLocationSiloService $service): void
    {
        $cacheKey = 'location_silo_data_response_' . $this->locationSiloPageId;
        Cache::forget($cacheKey);
        Cache::rememberForever($cacheKey, fn() => $service->getDataFromRepositories($this->locationSiloPageId, $this->dataRequests, false));
    }
}
