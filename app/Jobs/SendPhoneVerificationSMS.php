<?php

namespace App\Jobs;

use App\Models\Odin\CompanyUser;
use App\Models\SmsVerification;
use App\Services\Communication\TwilioCommunicationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendPhoneVerificationSMS implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected CompanyUser $user)
    {
        $this->onQueue(config('queue.named_queues.default'));
    }

    /**
     * Execute the job.
     *
     * @param TwilioCommunicationService $communicationService
     *
     * @return void
     */
    public function handle(TwilioCommunicationService $communicationService): void
    {
        $code = mt_rand(1000, 9999);
        $reference = $communicationService->sendSMS(config('services.twilio.from_phone_number'), $this->user->cell_phone, "Phone verification code: {$code}");

        SmsVerification::query()->create([
            SmsVerification::REFERENCE => $this->user->reference,
            SmsVerification::CODE => $code,
            SmsVerification::SENT_REFERENCE => $reference,
            SmsVerification::EXPIRES_AT => now()->addHour()
        ]);
    }
}
