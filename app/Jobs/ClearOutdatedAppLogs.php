<?php

namespace App\Jobs;

use App\Enums\Log\LogLevel;
use App\Models\AppLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ClearOutdatedAppLogs implements ShouldQueue
{
    use Queueable;

    /**
     * TODO - Add custom lifespan by level/feature ?
     * Execute the job.
     */
    public function handle(): void
    {
        foreach (LogLevel::cases() as $level) {
            $cutoffDate = now()
                ->subDays($level->getLifespanInDays())
                ->startOfDay();

            AppLog::query()
                ->where(AppLog::FIELD_LEVEL, $level)
                ->where(AppLog::FIELD_CREATED_AT, '<=', $cutoffDate)
                ->delete();
        }
    }
}
