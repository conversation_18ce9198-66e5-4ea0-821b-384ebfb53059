<?php

namespace App\Jobs;

use App\DTO\SMS;
use App\Exceptions\Text\SendTextFailedException;
use App\Services\Text\TextService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendSMS implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected SMS $SMS,
        protected ?bool $store = true,
    )
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(
        TextService $service,
    ): void
    {
        try {
            $service->send(sms: $this->SMS, store: $this->store);
        } catch (SendTextFailedException $e) {
            logger()->error("Failed to send SMS: " . json_encode($e->getSMS()->toArray()));
            logger()->error($e->getMessage());
        }
    }
}
