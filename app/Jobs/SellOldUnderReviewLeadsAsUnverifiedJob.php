<?php

namespace App\Jobs;

use App\Models\LeadProcessingUnderReview;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueRepository;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Services\QueueHelperService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Collection;

class SellOldUnderReviewLeadsAsUnverifiedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    const int AGE_IN_HOURS_LOWER_LIMIT = 24;
    const int AGE_IN_HOURS_UPPER_LIMIT = 72;

    // To prevent an influx of API calls, we will only check jobs for unverified budget if they haven't been checked in the past X hours
    const int HOURS_UNTIL_NEXT_CHECK = 18;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 1;

    public function __construct()
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_PRIORITY_LONG_RUNNING);
    }

    /**
     * Review all leads in 'Under Review' queue, and attempt to allocate as 'Unverified
     * if they've been in UR from 24-72 hours
     *
     * @param ProductProcessingQueueRepository $queueRepository
     *
     * @return void
     */
    public function handle(
        ProductProcessingQueueRepository $queueRepository
    ): void
    {
        if(!ProductProcessingService::ALLOW_UNVERIFIED_ALLOCATION){return;}

        // Get all leads that have been in UR between 24-72 hours
        $underReviews = $queueRepository->getUnderReviewConsumerProductsByAge(self::AGE_IN_HOURS_LOWER_LIMIT, self::AGE_IN_HOURS_UPPER_LIMIT);

        // Filter out leads that have already been checked for budget recently
        $underReviews->filter(function($underReview){
            /** @var LeadProcessingUnderReview $underReview */
            return !$underReview->checked_unverified_budget_at || ( Carbon::now()->diffInHours($underReview->checked_unverified_budget_at, true) ) > self::HOURS_UNTIL_NEXT_CHECK;
        })
            ->unique(fn(LeadProcessingUnderReview $underReview) => $underReview->consumer_product_id)
            ->pluck(LeadProcessingUnderReview::FIELD_ID)
            ->chunk(50)->each(fn(Collection $chunk) => SellUnderReviewLeadChunkAsUnverifiedJob::dispatch($chunk->toArray()));
    }
}
