<?php

namespace App\Jobs;

use App\Services\Odin\UpsellAutomationService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Database\Eloquent\Collection;

class UpsellAutomationWorkerJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param Collection $consumerProducts
     * @param int $logId
     * @param int $startIndex
     * @param int $totalLeads
     * @param array $config
     * @param array $excludedCompanies
     */
    public function __construct(
        protected Collection $consumerProducts,
        protected int $logId,
        protected int $startIndex,
        protected int $totalLeads,
        protected array $config,
        protected array $excludedCompanies,
    )
    {
        $this->queue = QueueHelperService::QUEUE_NAME_LONG_RUNNING_SINGLE;
    }

    /**
     * @param UpsellAutomationService $upsellAvailableLegsService
     * @return void
     */
    public function handle(UpsellAutomationService $upsellAvailableLegsService): void
    {
        $upsellAvailableLegsService->setConfig($this->config);
        $upsellAvailableLegsService->processUndersoldLeadCollection($this->consumerProducts, $this->logId, $this->startIndex, $this->totalLeads, $this->excludedCompanies);
    }
}
