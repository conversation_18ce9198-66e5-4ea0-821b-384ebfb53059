<?php

namespace App\Jobs;

use App\Models\LeadProcessingReservedLead;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class DeleteOldSystemLockedProductsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $date = Carbon::now()->subDays(10);

        LeadProcessingReservedLead::query()
            ->where(LeadProcessingReservedLead::FIELD_PROCESSOR_ID, LeadProcessingReservedLead::SYSTEM_ID)
            ->where(LeadProcessingReservedLead::CREATED_AT, "<", $date)
            ->delete();
    }
}
