<?php

namespace App\Jobs\SalesIntel;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\SalesIntel\SalesIntel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportContacts implements ShouldQueue
{
    use Queueable;

    public NewBuyerProspect $prospect;

    public function __construct(NewBuyerProspect $prospect)
    {
        $this->onQueue('imports');

        $this->prospect = $prospect;
    }

    public function handle(SalesIntel $api): void
    {
       $api->importContacts($this->prospect);
    }

    public function tags(): array
    {
        return ['SalesIntel\ImportContacts', 'prospect:'.$this->prospect->id];
    }
}
