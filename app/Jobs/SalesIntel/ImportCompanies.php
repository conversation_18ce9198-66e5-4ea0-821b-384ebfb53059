<?php

namespace App\Jobs\SalesIntel;

use App\Services\SalesIntel\SalesIntel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportCompanies implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public string $filter,
        public string $value,
        public ?array $naics = []
    ) {
        $this->onQueue('imports');
    }

    public function handle(SalesIntel $api): void
    {
        if (empty($this->naics)) {
            $api->importCompanies($this->filter, $this->value);
        } else {
            $api->importCompanies($this->filter, $this->value, $this->naics);
        }
    }

    public function tags(): array
    {
        return [
            'SalesIntel\ImportCompanies',
            "{$this->filter}:{$this->value}",
        ];
    }
}
