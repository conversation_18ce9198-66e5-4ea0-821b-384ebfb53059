<?php

namespace App\Jobs\SalesIntel;

use App\Models\Odin\Company;
use App\Services\SalesIntel\SalesIntel;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ImportUsers implements ShouldQueue
{
    use Queueable;

    public Company $company;

    public function __construct(Company $company)
    {
        $this->onQueue('imports');

        $this->company = $company;
    }

    public function handle(SalesIntel $api): void
    {
        if ($this->company->failedImportRecords->isNotEmpty() || $this->company->userImportRecords->isNotEmpty()) {
            return;
        }

        $api->importUsers($this->company);
    }

    public function tags(): array
    {
        return ['SalesIntel\ImportUsers', 'company:'.$this->company->id];
    }
}
