<?php

namespace App\Jobs;

use App\Repositories\TaskRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculateTaskTimezoneInBulkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public string $batch)
    {
        $this->onQueue(config('queue.named_queues.workflows'));
    }

    /**
     * Execute the job.
     *
     * @param TaskRepository $taskRepository
     * @return void
     */
    public function handle(TaskRepository $taskRepository): void
    {
        $tasks = $taskRepository->findCompanyTaskByBatch($this->batch);
        $taskRepository->storeTimezoneForTaskInBulk($tasks);
    }
}
