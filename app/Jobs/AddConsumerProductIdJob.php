<?php

namespace App\Jobs;

use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessingFailedLead;
use App\Models\LeadProcessingHeartbeat;
use App\Models\LeadProcessingHistory;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class AddConsumerProductIdJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected Collection $models)
    {
        $this->queue = QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        logger()->debug("Processing Table: {$this->models->first()?->getTable()}");

        /** @var LeadProcessingHeartbeat|LeadProcessingFailedLead|LeadProcessingHistory|LeadProcessingAllocation $model */
        foreach ($this->models as $model) {
            $consumerProductId = $model->lead->consumer?->consumerProducts->first()?->id;

            if ($consumerProductId) {
                $model->consumer_product_id = $consumerProductId;
                $model->save();
            } else {
                logger()->error("Consumer/Consumer Product does not exist for Lead: {$model->lead->quoteid}");
            }
        }
    }
}
