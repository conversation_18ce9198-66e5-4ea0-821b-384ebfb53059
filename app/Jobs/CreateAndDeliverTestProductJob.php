<?php

namespace App\Jobs;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\TestProductStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaign;
use App\Models\TestProduct;
use App\Services\NotificationService;
use App\Services\QueueHelperService;
use App\Services\TestProducts\TestProductService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;

class CreateAndDeliverTestProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected Company $company,
        protected IndustryService $industryService,
        protected Carbon $revealAt,
        protected ?int $userId = null,
        protected ProductCampaign|CompanyCampaign|LeadCampaign|null $campaign = null
    ) {
        $this->queue = QueueHelperService::QUEUE_NAME_TEST_LEADS;
    }

    /**
     * Execute the job.
     *
     * @param TestProductService $testProductService
     * @param NotificationService $notificationService
     * @return void
     * @throws BindingResolutionException
     * @params NotificationService $notificationService
     */
    public function handle(TestProductService $testProductService, NotificationService $notificationService): void
    {

        /** @var TestProduct|null $testProduct */
        $testProduct = $testProductService->generateProduct(
            company: $this->company,
            service: $this->industryService,
            revealAt: $this->revealAt,
            campaign: $this->campaign,
            userId: $this->userId
        );

        if (gettype($testProduct) === 'string') {
            $notificationSubject = "Test Lead Creation Failed";
            $notificationBody = "Failed to generate test product for company: {$this->company->name} and service: {$this->industryService->name}. Reason: " . $testProduct;

            if (isset($this->userId)) {
                $notificationService->createNotificationForUser($this->userId, null, $notificationSubject, $notificationBody, null);
            }
            logger()->error($notificationBody);
        }
        else {
            if (isset($this->userId)) {
                $notificationSubject = "Test Lead Creation Succeeded";
                $notificationBody = "Test product for company: {$this->company->name} and service: {$this->industryService->name} was created successfully.";
                $notificationService->createNotificationForUser($this->userId, null, $notificationSubject, $notificationBody, null);
            }

            $result = $testProductService->deliverProduct($testProduct, $this->campaign);

            if (!$result || !Arr::get($result, 'delivered', false)) {
                $this->markAsFailedDelivery($testProduct);

                return;
            }

            DispatchPubSubEvent::dispatch(EventCategory::TEST_LEADS, EventName::TEST_LEAD_DELIVERED, $testProductService->getEventData($testProduct));
        }
    }

    /**
     * @param TestProduct $testProduct
     *
     * @return void
     */
    protected function markAsFailedDelivery(TestProduct $testProduct): void
    {
        $testProduct->status = TestProductStatus::FAILED_DELIVERY;

        $testProduct->save();
    }
}
