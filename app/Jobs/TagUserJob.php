<?php

namespace App\Jobs;

use App\Enums\NotificationLinkType;
use App\Models\Action;
use App\Models\Notification;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\Delivery\Email;
use App\Services\NotificationService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TagUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct(protected Action $action, protected User $user)
    {
    }

    /**
     * Execute the job.
     *
     * @param NotificationService $notificationService
     *
     * @return void
     * @throws Exception
     */
    public function handle(NotificationService $notificationService): void
    {
        $fromUser = User::query()->find($this->action->{Action::FIELD_FROM_USER_ID});
        $previewSubject = $this->action->{Action::FIELD_SUBJECT};
        $link = url("/companies/{$this->getCompanyId()}");

        $notificationService->createNotificationForUser(
            $this->user->{User::FIELD_ID},
            $this->action->{Action::FIELD_FROM_USER_ID},
            'You have been tagged in an action',
            strlen($previewSubject) > 80 ? substr($previewSubject,0 , 80) . '...' : $previewSubject,
            Notification::TYPE_ACTIONS,
            $link,
            NotificationLinkType::TASK
        );

        if ($this->action->{Action::FIELD_TAG_BY_EMAIL}) {
            $mail = (new Mailable())->from($fromUser->{User::FIELD_EMAIL})
                ->subject('You have been tagged in an action by ' . $fromUser->{User::FIELD_NAME})
                ->markdown('emails.workflows.tag-notification')
                ->with([
                    'recipientName' => $this->user->{User::FIELD_NAME},
                    'link' => $link,
                    'message' => sprintf(
                        'You have been tagged in an action with the subject "%s". Follow this link to view details:',
                        $this->action->{Action::FIELD_SUBJECT},
                    )
                ]);

            Email::send($this->user->{User::FIELD_EMAIL}, $mail);
        }
    }

    /**
     * @return int
     * @throws Exception
     */
    protected function getCompanyId(): int
    {
        return match ($this->action->for_relation_type) {
            Action::RELATION_TYPE_COMPANY => $this->action->for_id,
            Action::RELATION_TYPE_COMPANY_CONTACT, Action::RELATION_TYPE_COMPANY_USER => CompanyUser::query()->findOrFail($this->action->for_id)->company_id,
            default => throw new Exception("invalid action relation type"),
        };
    }
}
