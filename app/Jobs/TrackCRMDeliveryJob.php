<?php

namespace App\Jobs;

use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TrackCRMDeliveryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public CRMDeliveryResponse              $response,
        public CompanyCampaign                  $campaign,
        public CompanyCampaignDeliveryModule    $module,
        public CompanyCampaignDeliveryModuleCRM $crm,
        public ConsumerProduct                  $product
    )
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        CompanyCampaignDeliveryLog::query()->create([
            CompanyCampaignDeliveryLog::FIELD_CAMPAIGN_ID         => $this->campaign->id,
            CompanyCampaignDeliveryLog::FIELD_MODULE_ID           => $this->module->id,
            CompanyCampaignDeliveryLog::FIELD_CRM_ID              => $this->crm->id,
            CompanyCampaignDeliveryLog::FIELD_CONSUMER_PRODUCT_ID => $this->product->id,
            CompanyCampaignDeliveryLog::FIELD_SUCCESS             => $this->response->success,
            CompanyCampaignDeliveryLog::FIELD_PAYLOAD             => $this->response->payload
        ]);
    }
}
