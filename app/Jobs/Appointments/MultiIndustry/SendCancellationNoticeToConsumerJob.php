<?php

namespace App\Jobs\Appointments\MultiIndustry;

use App\Enums\Odin\Industry;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\Odin\Appointments\MultiIndustryAppointmentService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class SendCancellationNoticeToConsumerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param ConsumerProduct $consumerProduct
     * @param string $companyName
     * @param Industry $industry
     * @param string $appointmentDateTime
     */
    public function __construct(
        private readonly ConsumerProduct $consumerProduct,
        private readonly string $companyName,
        private readonly string $industry,
        private readonly string $appointmentDateTime
    )
    {
        $this->onQueue(config('queue.named_queues.appointment_delivery_queue'));
    }

    /**
     * @param AppointmentService $appointmentService
     * @return void
     * @throws Exception
     */
    public function handle(MultiIndustryAppointmentService $appointmentService): void
    {
        $appointmentService->sendConsumerCancellationConfirmation(
            $this->consumerProduct,
            $this->consumerProduct->{ConsumerProduct::RELATION_CONSUMER},
            $this->companyName,
            $this->industry,
            $this->appointmentDateTime,
            false
        );
    }
}
