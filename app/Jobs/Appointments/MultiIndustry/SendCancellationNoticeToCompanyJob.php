<?php

namespace App\Jobs\Appointments\MultiIndustry;

use App\Enums\Odin\Industry;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Services\Odin\Appointments\AppointmentService;
use App\Services\Odin\Appointments\MultiIndustryAppointmentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class SendCancellationNoticeToCompanyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param ConsumerProduct $consumerProduct
     * @param ProductCampaign $productCampaign
     * @param ProductAssignment $productAssignment
     * @param string $companyName
     * @param Industry $industry
     * @param string $appointmentDateTime
     */
    public function __construct(
        private readonly ConsumerProduct $consumerProduct,
        private readonly ProductCampaign $productCampaign,
        private readonly ProductAssignment $productAssignment,
        private readonly string $companyName,
        private readonly string $industry,
        private readonly string $appointmentDateTime
    )
    {
        $this->onQueue(config('queue.named_queues.appointment_delivery_queue'));
    }

    /**
     * @param AppointmentService $appointmentService
     * @return void
     */
    public function handle(MultiIndustryAppointmentService $appointmentService): void
    {
        $appointmentService->sendCompanyCancellationNotice(
            $this->productAssignment,
            $this->productCampaign,
            $this->consumerProduct,
            $this->consumerProduct->{ConsumerProduct::RELATION_CONSUMER},
            $this->consumerProduct->{ConsumerProduct::RELATION_ADDRESS},
            $this->companyName,
            $this->industry,
            $this->appointmentDateTime
        );
    }
}
