<?php

namespace App\Jobs\Appointments;

use App\Campaigns\Delivery\Contacts\Strategies\Email\AppointmentEmailDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\Email\BaseEmailDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\SMS\AppointmentSMSDeliveryStrategy;
use App\Campaigns\Delivery\Contacts\Strategies\SMS\BaseSMSDeliveryStrategy;
use App\Models\AppointmentDelivery;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Odin\ProductAssignment;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

/**
 * Attempts redelivery of cancellations and rescheduled appointments for companies and consumers
 * This does not currently handle reattempting delivery of the primary appointment product for the company
 * If this is required it should be handled in the CampaignMediator lifecycle/Delivery Module, not here
 */
class AttemptAppointmentDeliveryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    const string CANCELLATION_DELIVERED_COMPANY  = 'company';
    const string CANCELLATION_DELIVERED_CONSUMER = 'consumer';

    public int $tries = 1;
    public int $maxExceptions = 1;

    private AppointmentEmailDeliveryStrategy $emailDeliveryStrategy;
    private AppointmentSMSDeliveryStrategy $smsDeliveryStrategy;

    /**
     * @param int $productAssignmentId
     * @param int|null $appointmentDeliveryId
     * @param array|null $cancellationPayload
     */
    public function __construct(
        protected int $productAssignmentId,
        protected ?int $appointmentDeliveryId = null,
        protected ?array $cancellationPayload = null,
    )
    {
        $this->onQueue(config('queue.named_queues.appointment_delivery_queue'));
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        $this->emailDeliveryStrategy = app(AppointmentEmailDeliveryStrategy::class);
        $this->smsDeliveryStrategy = app(AppointmentSMSDeliveryStrategy::class);

        /** @var ProductAssignment $productAssignment */
        $productAssignment = ProductAssignment::query()->findOrFail($this->productAssignmentId);

        if ($this->cancellationPayload)
            $this->deliverCancellation($productAssignment);
        else
            $this->deliverNotification($productAssignment);
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return void
     */
    private function deliverCancellation(ProductAssignment $productAssignment): void
    {
        $companyDelivered = $this->cancellationPayload[self::CANCELLATION_DELIVERED_COMPANY] ?? false;
        $consumerDelivered = $this->cancellationPayload[self::CANCELLATION_DELIVERED_CONSUMER] ?? false;
        $companyCampaign = $productAssignment->budget->budgetContainer->campaign;

        try {
            if (!$companyDelivered)
                $this->deliverToCompany($productAssignment, $companyCampaign, true);
            if (!$consumerDelivered)
                $this->deliverToConsumer($productAssignment, $companyCampaign, true);
        }
        catch (Throwable $e) {
            $this->handleFailedDelivery($e->getMessage());
        }
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return void
     */
    private function deliverNotification(ProductAssignment $productAssignment): void
    {
        /** @var AppointmentDelivery $appointmentDelivery */
        $appointmentDelivery = AppointmentDelivery::query()
            ->find($this->appointmentDeliveryId);

        if ($appointmentDelivery) {
            $companyCampaign = $productAssignment->budget->budgetContainer->campaign;
            try {
                if (!$appointmentDelivery->company_delivered)
                    $this->deliverToCompany($productAssignment, $companyCampaign, false);
                if (!$appointmentDelivery->consumer_delivered)
                    $this->deliverToConsumer($productAssignment, $companyCampaign, false);

            }
            catch(Throwable $e) {
                if (!$appointmentDelivery->company_delivered || !$appointmentDelivery->consumer_delivered)
                    $this->handleFailedDelivery($e->getMessage());

                return;
            }
        }

        if (!$appointmentDelivery?->consumer_delivered || !$appointmentDelivery?->company_delivered)
            $this->handleFailedDelivery(" delivery attempt failed.");
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param CompanyCampaign $companyCampaign
     * @param bool $cancellation
     * @return void
     * @throws Exception
     */
    private function deliverToCompany(ProductAssignment $productAssignment, CompanyCampaign $companyCampaign, bool $cancellation): void
    {
        $companyCampaign->deliveryModule->contacts->each(function (CompanyCampaignDeliveryModuleContact $contact) use ($productAssignment, $companyCampaign, $cancellation) {
            if ($contact->active) {
                $companyUser = $contact->contact;

                if ($contact->sms_active) {
                    $payload = [
                        BaseSMSDeliveryStrategy::FIELD_PHONE               => $companyUser->cell_phone,
                        BaseEmailDeliveryStrategy::FIELD_COMPANY_USER_NAME => $companyUser->completeName(),
                    ];
                    if ($cancellation)
                        $this->smsDeliveryStrategy->deliverCancellationToCompanyUser($productAssignment->consumerProduct, $companyCampaign, $payload);
                    else
                        $this->smsDeliveryStrategy->deliver($productAssignment->consumerProduct, $companyCampaign, $payload);
                }

                if ($contact->email_active) {
                    $payload = [
                        BaseEMailDeliveryStrategy::FIELD_EMAIL             => $companyUser->email,
                        BaseEmailDeliveryStrategy::FIELD_COMPANY_USER_NAME => $companyUser->completeName(),
                    ];
                    if ($cancellation)
                        $this->emailDeliveryStrategy->deliverCancellationToCompanyUser($productAssignment->consumerProduct, $companyCampaign, $payload);
                    else
                        $this->emailDeliveryStrategy->deliver($productAssignment->consumerProduct, $companyCampaign, $payload);
                }
            }
        });
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param CompanyCampaign $companyCampaign
     * @param bool $cancellation
     * @return void
     * @throws Exception
     */
    private function deliverToConsumer(ProductAssignment $productAssignment, CompanyCampaign $companyCampaign, bool $cancellation): void
    {
        if ($cancellation) {
            $this->emailDeliveryStrategy->deliverCancellationToConsumer($productAssignment->consumerProduct, $companyCampaign);
            $this->smsDeliveryStrategy->deliveryCancellationToConsumer($productAssignment->consumerProduct, $companyCampaign);
        }
        else {
            $this->emailDeliveryStrategy->deliverToConsumer($productAssignment->consumerProduct, $companyCampaign);
            $this->smsDeliveryStrategy->deliverToConsumer($productAssignment->consumerProduct, $companyCampaign);
        }
    }

    private function handleFailedDelivery(string $error): void
    {
        logger()->error("Error delivering Appointment for ProductAssignment " . $this->productAssignmentId . " - $error");
    }
}
