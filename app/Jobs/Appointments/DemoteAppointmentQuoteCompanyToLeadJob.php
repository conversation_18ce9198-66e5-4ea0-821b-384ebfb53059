<?php

namespace App\Jobs\Appointments;

use App\Enums\HttpMethod;
use App\Jobs\SendLegacyAdminRequest;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\User;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Services\Legacy\LeadDeliveryService;
use App\Services\Odin\Appointments\AppointmentService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class DemoteAppointmentQuoteCompanyToLeadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    const QUOTE_COMPANY_ID = 'quoteCompanyId';
    const LEAD_PRICE = 'leadPrice';
    const LEAD_CAMPAIGN_ID = 'leadCampaignId';
    const CAN_REJECT = 'canReject';
    const IS_REJECTION = 'isRejection';
    const REJECTION_NOTES = 'rejectionNotes';
    const REJECTION_EXPIRY = 'rejectionExpiry';
    const LEGACY_USER_ID = 'legacyUserId';
    const LEAD_PROCESSING_SCENARIO = 'processingScenario';

    /**
     * @param ProductAssignment $apptProductAssignment
     * @param float $leadPrice
     * @param bool $canReject
     * @param bool $isRejection
     * @param int $rejectionExpiry
     * @param string $rejectionNotes
     * @param User $leadProcessingUser
     * @param string $leadProcessingScenario
     */
    public function __construct(
        private readonly ProductAssignment $apptProductAssignment,
        private readonly float $leadPrice,
        private readonly bool $canReject,
        private readonly bool $isRejection,
        private readonly int $rejectionExpiry,
        private readonly string $rejectionNotes,
        private readonly User $leadProcessingUser,
        private readonly string $leadProcessingScenario
    )
    {
        //
    }

    /**
     * @param QuoteCompanyRepository $quoteCompanyRepository
     * @return void
     */
    public function handle()
    {
        $leadCampaignId = $this->apptProductAssignment->{ProductAssignment::RELATION_CAMPAIGN}->{ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID};

        $params = [
            self::QUOTE_COMPANY_ID => $this->apptProductAssignment->{ProductAssignment::FIELD_LEGACY_ID},
            self::LEAD_PRICE => $this->leadPrice,
            self::LEAD_CAMPAIGN_ID => $leadCampaignId,
            self::CAN_REJECT => $this->canReject,
            self::IS_REJECTION => $this->isRejection,
            self::REJECTION_NOTES => $this->rejectionNotes,
            self::REJECTION_EXPIRY => $this->rejectionExpiry,
            self::LEGACY_USER_ID => $this->leadProcessingUser->{User::FIELD_LEGACY_USER_ID},
            self::LEAD_PROCESSING_SCENARIO => $this->leadProcessingScenario
        ];

        SendLegacyAdminRequest::dispatch(
            HttpMethod::METHOD_PATCH,
            LeadDeliveryService::API_DEMOTE_APPOINTMENT_QUOTE_COMPANY_TO_LEAD,
            $params
        );
    }
}
