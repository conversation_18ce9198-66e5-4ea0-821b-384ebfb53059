<?php

namespace App\Jobs\Appointments;

use App\Services\Odin\Appointments\AppointmentService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

class ReactivateAppointmentCampaignsJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * If the job fails we'd still want to remove the atomic lock on this job such that it can
     * be re-attempted in the case of a failure.
     *
     * This is currently set to two minutes.
     */
    const EXPIRE_TIME_SECONDS = Carbon::SECONDS_PER_MINUTE * 2;

    public $failOnTimeout = true;

    public $timeout = self::EXPIRE_TIME_SECONDS;

    public $tries = 1;

    public $maxExceptions = 1;

    public $uniqueFor = self::EXPIRE_TIME_SECONDS;

    public function __construct()
    {

    }

    /**
     * @param AppointmentService $appointmentService
     * @throws Throwable
     */
    public function handle(AppointmentService $appointmentService)
    {
        $appointmentService->reactivateCampaigns();
    }
}
