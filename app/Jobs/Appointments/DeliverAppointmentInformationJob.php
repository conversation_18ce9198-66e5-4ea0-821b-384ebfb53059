<?php

namespace App\Jobs\Appointments;

use App\Services\Odin\Appointments\AppointmentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

class DeliverAppointmentInformationJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    const EXPIRE_TIME_SECONDS = 180;

    public $failOnTimeout = true;

    public $timeout = self::EXPIRE_TIME_SECONDS;

    public $tries = 1;

    public $maxExceptions = 1;

    public $uniqueFor = self::EXPIRE_TIME_SECONDS;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->onQueue(config('queue.named_queues.appointment_delivery_queue'));
    }

    /**
     * @param AppointmentService $appointmentService
     * @return void
     * @throws Throwable
     */
    public function handle(AppointmentService $appointmentService)
    {
        $appointmentService->deliverAppointments();
    }
}
