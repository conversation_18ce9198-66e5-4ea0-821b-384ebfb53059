<?php

namespace App\Jobs\Appointments;

use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

class AllocateMaxedOutAppointmentOfferingsAsLeadsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    const EXPIRE_TIME_SECONDS = 480;

    public $timeout = self::EXPIRE_TIME_SECONDS;

    public $tries = 3;

    public $uniqueFor = self::EXPIRE_TIME_SECONDS;

    /**
     * @param array $consumerProductIds
     */
    public function __construct(private readonly array $consumerProductIds)
    {
        $this->onQueue(config('queue.named_queues.lead_allocation_queue'));
    }

    /**
     * @param ProductProcessingService $productProcessingService
     * @return void
     * @throws Throwable
     */
    public function handle(ProductProcessingService $productProcessingService)
    {
        $productProcessingService->allocateMaxedOutAppointmentOfferingsAsLeads($this->consumerProductIds);
    }
}
