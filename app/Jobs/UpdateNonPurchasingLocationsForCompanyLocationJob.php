<?php

namespace App\Jobs;

use App\Models\Odin\CompanyLocation;
use App\Repositories\Odin\NonPurchasingCompanyLocationRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class UpdateNonPurchasingLocationsForCompanyLocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    public function __construct(protected int $companyLocationId) {}

    /**
     * Execute the job.
     *
     */
    public function handle(NonPurchasingCompanyLocationRepository $repository): void
    {
        $companyLocation = CompanyLocation::query()->findOrFail($this->companyLocationId);

        $repository->updateLocationsForCompanyLocation($companyLocation);
    }
}