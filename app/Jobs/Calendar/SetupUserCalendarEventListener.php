<?php

namespace App\Jobs\Calendar;

use App\Models\GoogleUserToken;
use App\Services\Calendar\CalendarService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SetupUserCalendarEventListener implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected GoogleUserToken $token)
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    /**
     * @throws Exception
     */
    public function handle(CalendarService $calendarService): void
    {
        $calendarService->setupWatch($this->token);
    }
}
