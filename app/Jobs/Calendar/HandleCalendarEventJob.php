<?php

namespace App\Jobs\Calendar;

use App\Exceptions\Calendar\CalendarSyncTokenExpiredException;
use App\Models\Calendar\Calendar;
use App\Services\Calendar\CalendarService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleCalendarEventJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected string $channelId)
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    /**
     * @throws Exception
     */
    public function handle(
        CalendarService $calendarService
    ): void
    {
        $calendar = Calendar::query()
            ->where(Calendar::FIELD_USER_ID, $this->channelId)
            ->firstOrFail();

        try {
            $calendarService->importLatestEvents(
                calendar: $calendar
            );
        } catch (Exception $exception) {
            if ($exception instanceof CalendarSyncTokenExpiredException) {
                $this->handleExpiredToken(
                    calendarService: $calendarService,
                    calendar       : $calendar,
                );
            }

            throw $exception;
        }
    }

    /**
     * @param CalendarService $calendarService
     * @param Calendar $calendar
     * @return void
     * @throws Exception
     */
    protected function handleExpiredToken(
        CalendarService $calendarService,
        Calendar $calendar
    ): void
    {
        $calendar->update([
            Calendar::FIELD_SYNC_TOKEN => null
        ]);

        $calendar->refresh();

        $calendarService->importLatestEvents(
            calendar: $calendar,
            timeMin : Carbon::now()->subMonth()
        );
    }
}
