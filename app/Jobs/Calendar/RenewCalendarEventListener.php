<?php

namespace App\Jobs\Calendar;

use App\Enums\GoogleServiceType;
use App\Models\Calendar\UserCalendarListener;
use App\Models\GoogleUserToken;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RenewCalendarEventListener implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int CHUNK_SIZE = 100;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        UserCalendarListener::query()
            ->where(UserCalendarListener::FIELD_EXPIRES_AT, '<=', now()->endOfDay())
            ->chunk(self::CHUNK_SIZE, function ($expiredEmailListeners) {
                foreach ($expiredEmailListeners as $listener) {
                    $token = GoogleUserToken::query()
                        ->where(GoogleUserToken::FIELD_USER_ID, $listener->{UserCalendarListener::FIELD_USER_ID})
                        ->where(GoogleUserToken::FIELD_SERVICE, GoogleServiceType::CALENDAR)
                        ->first();

                    if ($token) {
                        SetupUserCalendarEventListener::dispatch($token);
                    }
                }
            });
    }
}
