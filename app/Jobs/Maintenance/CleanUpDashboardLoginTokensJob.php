<?php

namespace App\Jobs\Maintenance;

use App\Services\Dashboard\DashboardLoginTokenService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class CleanUpDashboardLoginTokensJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;


    public function __construct(
        protected int $maxTokenAgeInDays = 7
    )
    {
        $this->onQueue('default');
    }

    /**
     * @param DashboardLoginTokenService $service
     * @return void
     */
    public function handle(DashboardLoginTokenService $service): void
    {
        $service->cleanUpTokens($this->maxTokenAgeInDays);
    }
}
