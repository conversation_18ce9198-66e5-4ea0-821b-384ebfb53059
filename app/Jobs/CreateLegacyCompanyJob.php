<?php

namespace App\Jobs;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Services\CompanyRegistration\CompanyRegistrationSyncService;
use App\Services\CompanyRegistration\CompanyRegistrationV3Service;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;

/**
 * Ensure companies have a legacy company to temporarily fix issues
 */
class CreateLegacyCompanyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Company $company)
    {

    }

    /**
     * Execute the job.
     */
    public function handle(
        CompanyRegistrationV3Service $service,
        CompanyRegistrationSyncService $syncService
    ): void
    {
        if (empty($this->company->legacy_id)) {
            $user = $this->company->users()
                ->whereNull(CompanyUser::FIELD_LEGACY_ID)
                ->first();

            if (filled($user)) {
                $service->syncToLegacy(
                    company    : $this->company,
                    companyUser: $user
                );
            } else {
                $legacyData = $syncService->transformForLegacy(Company::class, $this->company->toArray());

                $response = $syncService->post(
                    route        : $syncService::CREATE_COMPANY_API_ROUTE,
                    data         : $legacyData,
                    retries      : 1,
                    retryInterval: 100,
                    timeout      : 10
                )->throw()->json();

                $legacyId = Arr::get($response, 'legacy_company_id');

                if (!$legacyId) {
                    throw new Exception('Error creating legacy company');
                }

                Company::query()
                    ->where(Company::FIELD_ID, $this->company->id)
                    ->update([Company::FIELD_LEGACY_ID => $legacyId]);
            }
        }
    }
}
