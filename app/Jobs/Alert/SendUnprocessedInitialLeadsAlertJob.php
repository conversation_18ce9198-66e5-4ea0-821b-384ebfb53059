<?php

namespace App\Jobs\Alert;

use App\Enums\Alert\AlertType;
use App\Models\Alert;
use App\Models\AlertRecipient;
use App\Models\LeadProcessingInitial;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Mail\Message;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class SendUnprocessedInitialLeadsAlertJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $alert = $this->getQueueAlert();

        if (!$alert) {
            return;
        }

        $alertDurationInMinutes = $alert->getPayloadByKey('duration');

        if (!$alertDurationInMinutes) {
            return;
        }

        $this->getUnprocessedLead()
            ->filter(fn(LeadProcessingInitial $leadProcessingInitial) => now()->subMinutes($alertDurationInMinutes)->gte($leadProcessingInitial->created_at))
            ->each(fn(LeadProcessingInitial $leadProcessingInitial) => $this->sendAlert($alert, $leadProcessingInitial));
    }

    /**
     * @return Collection<LeadProcessingInitial>
     */
    protected function getUnprocessedLead(): Collection
    {
        return LeadProcessingInitial::query()
            ->selectRaw(LeadProcessingInitial::TABLE . '.*')
            ->join(
                ConsumerProduct::TABLE,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID,
                LeadProcessingInitial::TABLE . '.' . LeadProcessingInitial::FIELD_CONSUMER_PRODUCT_ID
            )
            ->join(
                Consumer::TABLE,
                Consumer::TABLE . '.' . Consumer::FIELD_ID,
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_CONSUMER_ID
            )
            ->where(LeadProcessingInitial::TABLE . '.' . LeadProcessingInitial::CREATED_AT, '>=', now()->subHours(24))
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_STATUS, ConsumerProduct::STATUS_INITIAL)
            ->whereNotIn(Consumer::FIELD_CLASSIFICATION, [Consumer::CLASSIFICATION_EMAIL_ONLY, Consumer::CLASSIFICATION_VERIFIED_EMAIL_ONLY])
            ->get();
    }

    /**
     * @return Alert|null
     */
    protected function getQueueAlert(): ?Alert
    {
        $queue = LeadProcessingQueueConfiguration::query()
            ->where(LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS, LeadProcessingQueueConfiguration::STATUS_INITIAL)
            ->first();

        if (!$queue) {
            return null;
        }

        /** @var Alert|null */
        return $queue->alerts()
            ->where(Alert::FIELD_TYPE, AlertType::UNPROCESSED_LEADS)
            ->where(Alert::FIELD_ACTIVE, true)
            ->first();
    }

    /**
     * @param Alert $alert
     * @param LeadProcessingInitial $leadProcessingInitial
     *
     * @return void
     */
    protected function sendAlert(Alert $alert, LeadProcessingInitial $leadProcessingInitial): void
    {
        if ($this->alreadyAlerted($leadProcessingInitial)) {
            return;
        }

        $alert->recipients->each(fn(AlertRecipient $alertRecipient) => Mail::html($this->getEmailContent($leadProcessingInitial), fn(Message $message) => $message->to($alertRecipient->notifiable->email)
            ->subject("Unprocessed Lead: $leadProcessingInitial->consumer_product_id")
        ));
    }

    /**
     * @param LeadProcessingInitial $leadProcessingInitial
     *
     * @return string
     */
    protected function getEmailContent(LeadProcessingInitial $leadProcessingInitial): string
    {
        $hoursInQueue = now()->diffInHours($leadProcessingInitial->created_at);
        $leadPage     = config('app.url') . "/consumer-product/?consumer_product_id=$leadProcessingInitial->consumer_product_id";

        return <<<CONTENT
<p style="margin-top: 3rem; margin-left: 3rem">Hours in the Initial Queue: $hoursInQueue</p>
<a href="$leadPage" style="margin-left: 3rem">Lead Page</a>
CONTENT;
    }

    /**
     * @param LeadProcessingInitial $leadProcessingInitial
     *
     * @return bool
     */
    protected function alreadyAlerted(LeadProcessingInitial $leadProcessingInitial): bool
    {
        $key = "unprocessed_initial_consumer_product_{$leadProcessingInitial->consumer_product_id}_alert";

        if (Cache::has($key)) {
            return true;
        }

        Cache::put($key, [
            'consumer_product_id' => $leadProcessingInitial->consumer_product_id,
            'timestamp'           => $leadProcessingInitial->created_at->timestamp
        ], now()->addHours(24));

        return false;
    }
}
