<?php

namespace App\Jobs;

use App\Models\PingPostAffiliates\PingPostAffiliate;
use App\Models\PingPostAffiliates\PingPostAffiliateRequest;
use App\Services\Odin\PingPostAffiliateService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\RequestException;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

/**
 * Class ProcessPingPostAffiliateLeadJob
 * @package App\Jobs
 */
class ProcessPingPostAffiliateLeadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /** @var array $leadDetails */
    private array $leadDetails;

    /** @var PingPostAffiliate $pingPostAffiliate */
    private PingPostAffiliate $pingPostAffiliate;

    /** @var PingPostAffiliateRequest $request */
    private PingPostAffiliateRequest $request;

    /**
     * @param array $leadDetails
     * @param PingPostAffiliate $pingPostAffiliate
     * @param PingPostAffiliateRequest $request
     */
    public function __construct(
        array $leadDetails,
        PingPostAffiliate $pingPostAffiliate,
        PingPostAffiliateRequest $request,
    )
    {
        $this->onQueue('default');

        $this->leadDetails = $leadDetails;
        $this->pingPostAffiliate = $pingPostAffiliate;
        $this->request = $request;
    }

    /**
     * @param PingPostAffiliateService $pingPostAffiliateService
     * @return void
     * @throws RequestException
     */
    public function handle(PingPostAffiliateService $pingPostAffiliateService): void
    {
        $pingPostAffiliateService->processNewLead($this->leadDetails, $this->pingPostAffiliate, $this->request);
    }
}
