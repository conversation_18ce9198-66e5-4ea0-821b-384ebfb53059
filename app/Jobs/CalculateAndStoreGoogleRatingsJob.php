<?php

namespace App\Jobs;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Repositories\Odin\CompanyRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class CalculateAndStoreGoogleRatingsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    const GOOGLE_REVIEWS_NAME = 'places';

    const GOOGLE_RATING       = 'google_rating';
    const GOOGLE_REVIEW_COUNT = 'google_review_count';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Execute the job.
     *
     * @param CompanyRepository $companyRepository
     * @return void
     */
    public function handle(CompanyRepository $companyRepository): void
    {
        // Performance.
        DB::disableQueryLog();

        Company::query()
            ->with([Company::RELATION_LOCATIONS, Company::RELATION_LOCATIONS.'.'.CompanyLocation::RELATION_EXTERNAL_REVIEWS])
            ->whereHas(Company::RELATION_LOCATIONS)
            ->whereHas(Company::RELATION_LOCATIONS.'.'.CompanyLocation::RELATION_EXTERNAL_REVIEWS)
            ->chunk(100, function ($companies) use ($companyRepository) {
                DB::beginTransaction();

                /** @var Company $company */
                foreach($companies as $company) {
                    $totalCount = 0;
                    $totalRating = 0;

                    foreach($company->locations as $location) {
                        foreach($location->externalReviews as $externalReview) {

                            if($externalReview->name === self::GOOGLE_REVIEWS_NAME) {
                                $totalCount  += $externalReview->agg_count;
                                $totalRating += $externalReview->agg_value * $externalReview->agg_count;
                            }
                        }
                    }

                    if($totalCount > 0) {
                        $companyRepository->updateCompanyData($company, [
                            self::GOOGLE_RATING       => round($totalRating / $totalCount, 2),
                            self::GOOGLE_REVIEW_COUNT => $totalCount,
                        ]);
                    }
                }

                DB::commit();
            });
    }
}
