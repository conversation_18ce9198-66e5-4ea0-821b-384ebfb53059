<?php

namespace App\Jobs;

use App\Enums\PhoneType;
use App\Models\Odin\ProductAssignment;
use App\Models\Phone;
use App\Services\QueueHelperService;
use App\Services\Twilio\TwilioPhoneNumberService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Twilio\Exceptions\ConfigurationException;
use Twilio\Exceptions\TwilioException;

class ReleaseConsumerProxyPhone implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_LONG_RUNNING);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Select proxy phone numbers that are active, older than 30 days, but not older than 3 months.
        // The job runs daily, so anything older than 3 months should have already been processed.
        // Limiting the range improves query performance.
        ProductAssignment::query()
            ->where(ProductAssignment::FIELD_PROXY_PHONE_ACTIVE, true)
            ->whereNotNull(ProductAssignment::FIELD_PROXY_PHONE)
            ->where(ProductAssignment::CREATED_AT, '<', now()->subDays(30))
            ->where(ProductAssignment::CREATED_AT, '>', now()->subMonths(3))
            ->chunk(100, fn(Collection $productAssignments) => $this->releasePhones($productAssignments));
    }

    /**
     * @param Collection<ProductAssignment> $productAssignments
     *
     * @return void
     * @throws ConfigurationException
     * @throws TwilioException
     */
    protected function releasePhones(Collection $productAssignments): void
    {
        $service = app(TwilioPhoneNumberService::class);

        foreach ($productAssignments as $productAssignment) {
            /** @var Phone|null $phone */
            $phone = Phone::query()
                ->where(Phone::FIELD_TYPE, PhoneType::CONSUMER_PROXY_PHONE)
                ->where(Phone::FIELD_PHONE, $productAssignment->proxy_phone)
                ->first();

            if ($phone && $service->releasePhone($phone->external_reference)) {
                $productAssignment->update([
                    ProductAssignment::FIELD_PROXY_PHONE_ACTIVE => false
                ]);

                $phone->delete();
            }
        }
    }
}
