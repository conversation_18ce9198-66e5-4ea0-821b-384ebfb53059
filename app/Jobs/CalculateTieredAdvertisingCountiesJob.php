<?php

namespace App\Jobs;

use App\Services\TieredAdvertisingCountyCalculationService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

/**
 * Class CalculateTieredAdvertisingCountiesJob
 * @package App\Jobs
 */
class CalculateTieredAdvertisingCountiesJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 1;

    public function __construct() {
        $this->onQueue('long_running_single_attempt');
    }

    /**
     * @param TieredAdvertisingCountyCalculationService $service
     * @return void
     * @throws Exception
     */
    public function handle(TieredAdvertisingCountyCalculationService $service): void
    {
        ini_set('memory_limit','-1');
        $service->calculateTieredAdvertisingCounties();
    }
}
