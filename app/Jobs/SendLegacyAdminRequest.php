<?php

namespace App\Jobs;

use App\Enums\HttpMethod;
use App\Services\Legacy\APIConsumer;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class SendLegacyAdminRequest implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public $failOnTimeout = true;

    public $timeout = Carbon::SECONDS_PER_MINUTE * 3;

    public $tries = 3;

    public $maxExceptions = 3;

    /**
     * @param HttpMethod $method
     * @param string $route
     * @param array $data
     * @param string $callbackFunc
     * @param string $callbackClass
     * @param array $callbackParams
     * @param bool $retryOnFalse
     * @throws Exception
     */
    public function __construct(
        private readonly HttpMethod $method,
        private readonly string $route,
        private readonly array $data,
        private readonly string $callbackFunc = '',
        private readonly string $callbackClass = '',
        private readonly array $callbackParams = [],
        private readonly bool $retryOnFalse = false
    )
    {
        if(!in_array($this->method, [HttpMethod::METHOD_GET, HttpMethod::METHOD_POST, HttpMethod::METHOD_PATCH, HttpMethod::METHOD_DELETE], true)) {
            throw new Exception("Invalid method");
        }
    }

    /**
     * @param APIConsumer $consumer
     * @throws Exception
     */
    public function handle(APIConsumer $consumer)
    {
        switch($this->method) {
            case HttpMethod::METHOD_GET:
                $return = $consumer->get($this->route, $this->data)->json(APIConsumer::RESPONSE_RESULT);
                break;
            case HttpMethod::METHOD_POST:
                $return = $consumer->post($this->route, $this->data, 60)->json(APIConsumer::RESPONSE_RESULT);
                break;
            case HttpMethod::METHOD_PATCH:
                $return = $consumer->patch($this->route, $this->data, 60)->json(APIConsumer::RESPONSE_RESULT);
                break;
            case HttpMethod::METHOD_DELETE:
                $return = $consumer->delete($this->route, $this->data)->json(APIConsumer::RESPONSE_RESULT);
                break;
        }

        if(
            $this->retryOnFalse
            && (
                (isset($return["status"]) && empty($return["status"]))
                || $return === false
            )
        ) {
            $this->release();
        }

        if(!empty($this->callbackFunc)) {
            if(!empty($this->callbackClass)) {
                $callbackClassObj = app($this->callbackClass);

                $func = [
                    $callbackClassObj,
                    $this->callbackFunc
                ];
            }
            else {
                $func = $this->callbackFunc;
            }

            if(isset($return)) {
                $params = array_merge(
                    $this->callbackParams,
                    [
                        $return
                    ]
                );
            }
            else {
                $params = $this->callbackParams;
            }

            call_user_func_array($func, $params);
        }
    }
}
