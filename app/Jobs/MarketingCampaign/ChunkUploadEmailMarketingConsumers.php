<?php

namespace App\Jobs\MarketingCampaign;

use App\Contracts\Services\MarketingCampaign\EmailMarketingServiceFactory;
use App\DTO\MarketingCampaign\AddUserToEmailCampaignResponse;
use App\DTO\MarketingCampaign\EmailCampaignUser;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\MarketingCampaign;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;

class ChunkUploadEmailMarketingConsumers implements ShouldQueue
{
    use Queueable;

    /**
     * @param int $campaignId
     * @param Collection<EmailCampaignUser> $users
     * Create a new job instance.
     */
    public function __construct(
        protected int $campaignId,
        protected Collection $users,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(
        MarketingCampaignService $marketingCampaignService,
    ): void
    {
        /** @var ?MarketingCampaign $campaign */
        $campaign = MarketingCampaign::query()->find($this->campaignId);

        if(empty($campaign)) {
            MarketingLogService::log(
                message: "Could not identify marketing campaign",
                namespace: MarketingLogType::ADD_USERS,
                level: LogLevel::ERROR,
                context: [
                    'campaign_id' => $this->campaignId,
                    'consumers_affected' => $this->users->count(),
                ],
            );
            return;
        }

        if (empty($campaign->{MarketingCampaign::FIELD_CODE})) {
            MarketingLogService::log(
                message: "Could not identify the external code for marketing campaign",
                namespace: MarketingLogType::ADD_USERS,
                level: LogLevel::ERROR,
                context: [
                    'campaign_id' => $this->campaignId,
                    'consumers_affected' => $this->users->count(),
                ],
                relations: [$campaign]
            );
            return;
        }

        $marketingService = EmailMarketingServiceFactory::make($campaign->{MarketingCampaign::FIELD_TYPE});

        /** @var ?Collection<AddUserToEmailCampaignResponse> $response */
        $response = $marketingService->addUsersToMarketingCampaign($campaign->{MarketingCampaign::FIELD_CODE}, $this->users);

        if (empty($response) || $response?->isEmpty()) {
            MarketingLogService::log(
                message: "Received no response adding users to marketing campaign",
                namespace: MarketingLogType::ADD_USERS,
                level: LogLevel::ERROR,
                context: [
                    'campaign_id' => $this->campaignId,
                    'consumers_affected' => $this->users->count(),
                ],
                relations: [$campaign]
            );
            return;
        }

        $marketingCampaignService->updateMarketingCampaignConsumers(
            $response,
            $this->campaignId
        );

        $marketingCampaignService->syncCampaignProcessing($campaign);
    }
}
