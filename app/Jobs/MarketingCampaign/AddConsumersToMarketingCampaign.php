<?php

namespace App\Jobs\MarketingCampaign;

use App\Models\MarketingCampaign;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AddConsumersToMarketingCampaign implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    const int CHUNK_SIZE = 200; //mailchimp max batch upload is 500

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $campaignId,
        protected array $consumerFilters
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(
        MarketingCampaignService $marketingCampaignService,
    ): void
    {
        /** @var ?MarketingCampaign $campaign */
        $campaign = MarketingCampaign::query()->find($this->campaignId);

        if(empty($campaign)) {
            logger()->error("Could not identify marketing campaign with id: $this->campaignId");
            return;
        }

        if (empty($campaign->{MarketingCampaign::FIELD_CODE})) {
            logger()->error("Could not identify the external code for marketing campaign id: $this->campaignId");
            return;
        }

        $campaign->{MarketingCampaign::FIELD_PROCESSING} = true;
        $campaign->save();

        $data = $marketingCampaignService->initialiseMarketingCampaignConsumers($this->consumerFilters, $this->campaignId);

        $data->chunk(self::CHUNK_SIZE)->each(function ($chunk) {
            ChunkUploadEmailMarketingConsumers::dispatch($this->campaignId, $chunk);
        });
    }
}
