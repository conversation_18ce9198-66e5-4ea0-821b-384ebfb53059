<?php

namespace App\Jobs\MarketingCampaign;

use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Queue\Queueable;

class ProcessMarketingCampaignResponse implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected string $marketingCampaignConsumerReference,
        protected array $response = [],
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     * @throws BindingResolutionException
     */
    public function handle(
        MarketingCampaignConsumerRepository $marketingCampaignConsumerRepository,
        MarketingCampaignService $marketingCampaignService,
    ): void
    {
        try {
            $marketingCampaignConsumer = $marketingCampaignConsumerRepository
                ->getByMarketingCampaignConsumerReference(
                    $this->marketingCampaignConsumerReference
                );
        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return;
        }

        $campaign = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN};

        $payload = $marketingCampaignService->getMarketingCampaignPayload(campaign: $campaign);

        $payload->revalidate(
            marketingCampaignConsumer: $marketingCampaignConsumer,
            responseData: $this->response,
        );
    }
}
