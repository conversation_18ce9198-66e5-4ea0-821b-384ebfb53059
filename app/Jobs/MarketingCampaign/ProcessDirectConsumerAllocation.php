<?php

namespace App\Jobs\MarketingCampaign;

use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Queue\Queueable;

class ProcessDirectConsumerAllocation implements ShouldQueue
{
    use Queueable;

    const int AUTO_SELL_MAX = 2;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $marketingConsumerId
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     * @throws BindingResolutionException
     */
    public function handle(
        MarketingCampaignConsumerRepository $marketingCampaignConsumerRepository,
        MarketingCampaignService            $marketingCampaignService,
    ): void
    {
        try {
            $marketingCampaignConsumer = $marketingCampaignConsumerRepository->findOrFail($this->marketingConsumerId);
        } catch (ModelNotFoundException $exception) {
            logger()->error("Tried to allocate Marketing Consumer ID: $this->marketingConsumerId but failed to retrieve model.");
            return;
        }

        $campaign = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN};

        $payload = $marketingCampaignService->getMarketingCampaignPayload(campaign: $campaign);

        $payload->revalidate(marketingCampaignConsumer: $marketingCampaignConsumer);
    }
}
