<?php

namespace App\Jobs\MarketingCampaign;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\MarketingCampaign;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendMarketingCampaign implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $campaignId,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $campaign = MarketingCampaign::query()->find($this->campaignId);

        if (empty($campaign)) {
            logger()->error("Could not find marketing campaign of id: $this->campaignId");
            return;
        }

        /** @var MarketingCampaignType $type */
        $type = $campaign->{MarketingCampaign::FIELD_TYPE};

        $campaignType = $type->getClass();

        $campaignType->send(campaign: $campaign);

        MarketingLogService::log(
            message: 'Marketing Campaign send dispatched',
            namespace: MarketingLogType::MARKETING_CAMPAIGN_SENT,
            level: LogLevel::INFO,
            context: [
                'marketing_campaign_id' => $campaign->id,
            ],
            relations: [$campaign]
        );
    }
}
