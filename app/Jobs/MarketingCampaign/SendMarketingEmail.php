<?php

namespace App\Jobs\MarketingCampaign;

use App\Contracts\Services\MarketingCampaign\InternalEmailMarketingService;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\GlobalConfigurationKey;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\GlobalConfigurationMarketingField;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\MarketingCampaigns\SendingStrategy;
use App\Exceptions\MarketingCampaign\EmailBatchSendFailedException;
use App\Models\EmailTemplate;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\EmailAddress\EmailAddressService;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\QueueHelperService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Fluent;
use Throwable;

class SendMarketingEmail implements ShouldQueue
{
    use Queueable;

    const bool FALLBACK_ACTIVE_EMAIL_VALIDATION = false;

    protected InternalEmailMarketingService $emailMarketingService;
    protected MarketingCampaignRepository $marketingCampaignRepository;
    protected MarketingCampaignService $marketingCampaignService;
    protected MarketingCampaignConsumerRepository $mccRepository;
    protected EmailAddressService $emailAddressService;
    protected GlobalConfigurationRepository $globalConfigurationRepository;

    /**
     * Create a new job instance.
     *
     * @param int $campaignId
     * @param OutgoingEmailDTO $target
     * @param int $emailTemplateId
     * @param string $fromEmail
     * @param string $fromName
     */
    public function __construct(
        protected int              $campaignId,
        protected OutgoingEmailDTO $target,
        protected int              $emailTemplateId,
        protected string           $fromEmail,
        protected string           $fromName,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(
        InternalEmailMarketingService       $emailMarketingService,
        MarketingCampaignRepository         $marketingCampaignRepository,
        MarketingCampaignService            $marketingCampaignService,
        MarketingCampaignConsumerRepository $mccRepository,
        EmailAddressService                 $emailAddressService,
        GlobalConfigurationRepository       $globalConfigurationRepository,
    ): void
    {
        $this->emailMarketingService = $emailMarketingService;
        $this->marketingCampaignRepository = $marketingCampaignRepository;
        $this->marketingCampaignService = $marketingCampaignService;
        $this->mccRepository = $mccRepository;
        $this->emailAddressService = $emailAddressService;
        $this->globalConfigurationRepository = $globalConfigurationRepository;

        $emailTemplate = EmailTemplate::query()->findOrFail($this->emailTemplateId);
        $campaign = MarketingCampaign::query()->findOrFail($this->campaignId);

        if (!$campaign->type->sendable()) {
            logger()->info("campaign id $this->campaignId is not sendable");
            return;
        }

        try {
            $valid = $this->validateOutgoingDTO(
                outgoingEmailDTO: $this->target,
                campaign: $campaign
            );

            if ($valid) {
                $strategy = SendingStrategy::fromCampaign($campaign)->strategy();

                $strategy->sendEmail(
                    target: $this->target,
                    template: $emailTemplate,
                    campaign: $campaign,
                );

                $this->marketingCampaignService->updateCampaignMetrics(
                    campaign: $campaign,
                    incrementSendCount: 1,
                );

                $emailAddressService->markEmailContacted(email: $this->target->getToEmail());
                $mccRepository->updateToSent(mccIds: [$this->target->getRelationId()]);
            }

        } catch (Throwable $throwable) {
            $context = ['marketing_campaign_id' => $this->campaignId];

            if ($throwable instanceof EmailBatchSendFailedException) {
                $context = [...$context, ...$throwable->getContext()];
            }

            MarketingLogService::log(
                message: "Failed to send email batch . " . $throwable->getMessage(),
                namespace: MarketingLogType::MARKETING_INTERNAL_SEND,
                level: LogLevel::ERROR,
                context: $context,
                relations: [
                    $campaign,
                    [
                        'id'    => $this->target->getRelationId(),
                        'class' => MarketingCampaignConsumer::class
                    ]
                ],
            );

            if ($throwable instanceof Exception) {
                throw $throwable;
            }
        }
    }

    public function validateOutgoingDTO(
        OutgoingEmailDTO  $outgoingEmailDTO,
        MarketingCampaign $campaign,
    ): bool
    {
        $valid = true;

        $payload = new Fluent($this->globalConfigurationRepository->getConfigurationPayload(GlobalConfigurationKey::MARKETING)?->toArray() ?? []);

        $validateEmail = $payload->get("data." . GlobalConfigurationMarketingField::ACTIVE_EMAIL_VALIDATION->value, self::FALLBACK_ACTIVE_EMAIL_VALIDATION);
        $validateEmail = filter_var($validateEmail, FILTER_VALIDATE_BOOLEAN);

        $validationError = $validateEmail ? $this->emailAddressService->authenticateEmail(email: $outgoingEmailDTO->getToEmail()) : [];

        $recentlyContacted = $this->emailAddressService->recentlyContacted(email: $outgoingEmailDTO->getToEmail());

        if ($recentlyContacted || filled($validationError)) {
            $valid = false;

            MarketingLogService::log(
                message: 'Email address failed validation',
                namespace: MarketingLogType::MARKETING_INTERNAL_SEND,
                level: LogLevel::ERROR,
                context: [
                    'email_address' => $outgoingEmailDTO->getToEmail(),
                    'error'         => array_filter([
                        $recentlyContacted ? 'recently contacted' : null,
                        ...$validationError
                    ]),
                ],
                relations: [
                    $campaign,
                    [
                        'id'    => $outgoingEmailDTO->getRelationId(),
                        'class' => MarketingCampaignConsumer::class
                    ],
                ],
            );

            $this->mccRepository->updateStatus(
                [$outgoingEmailDTO->getRelationId()],
                filled($validationError)
                    ? MarketingCampaignConsumerStatus::ERROR
                    : MarketingCampaignConsumerStatus::UPLOADED,
            );
        }

        return $valid;
    }
}
