<?php

namespace App\Jobs\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Models\MarketingCampaign;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class DispatchMarketingCampaigns implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $dueMarketingCampaignIds = MarketingCampaign::query()
            ->whereIn(MarketingCampaign::FIELD_TYPE, [MarketingCampaignType::INTERNAL_EMAIL, MarketingCampaignType::SMS])
            ->where(MarketingCampaign::FIELD_STATUS,'=', MarketingCampaignStatus::ACTIVE)
            ->whereNotNull(MarketingCampaign::FIELD_SENT_AT)
            ->where(MarketingCampaign::FIELD_SENT_AT, '<=', now())
            ->pluck(MarketingCampaign::FIELD_ID);

        foreach ($dueMarketingCampaignIds as $marketingCampaignId) {
            SendMarketingCampaign::dispatch($marketingCampaignId);
        }
    }
}
