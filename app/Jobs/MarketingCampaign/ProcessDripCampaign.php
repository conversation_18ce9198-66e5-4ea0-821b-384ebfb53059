<?php

namespace App\Jobs\MarketingCampaign;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\MarketingCampaign;
use App\Services\MarketingCampaign\DripCampaignService;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Arr;

class ProcessDripCampaign implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $dripCampaignId,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     * @throws \Exception
     */
    public function handle(
        DripCampaignService $dripCampaignService,
        MarketingCampaignService $marketingCampaignService,
    ): void
    {
        $campaign = MarketingCampaign::query()->findOrFail($this->dripCampaignId);

        $configuration = $campaign->{MarketingCampaign::FIELD_CONFIGURATION};

        $filters = Arr::get($configuration, 'filters', []);

        $spanType = Arr::get($configuration, 'span_type');
        $spanValue = Arr::get($configuration, 'span_value');

        $targetDay = $dripCampaignService->getTargetDay(
            span: $spanType,
            number: $spanValue
        );

        $startPeriod = $targetDay->clone()->startOfDay();
        $endPeriod = $targetDay->clone()->endOfDay();

        $targets = $dripCampaignService->getTargets(
            $filters,
            $startPeriod,
            $endPeriod,
            $campaign->id,
        );

        MarketingLogService::log(
            message: "Identified Drip Campaign #{$campaign->id} Targets",
            namespace: MarketingLogType::MARKETING_DRIP_CAMPAIGN,
            level: LogLevel::INFO,
            context: [
                'from' => $startPeriod,
                'to' => $endPeriod,
                'count' => $targets->count(),
            ],
            relations: [$campaign]
        );

        SendMarketingCampaign::dispatch($this->dripCampaignId);

        $marketingCampaignService->updateCampaignMetrics(
            campaign: $campaign,
            incrementTargetCount: $targets->count(),
        );
    }
}
