<?php

namespace App\Jobs;

use App\Models\LeadProcessingCommunication;
use App\Repositories\CommunicationRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Facades\DB;

class UpdateMostRecentLeadProcessingCommunications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public int $timeout = Carbon::SECONDS_PER_MINUTE * 3;

    public int $tries = 2;

    /**
     * @param int|null $leadId
     */
    public function __construct(
        private readonly ?int $consumerProductId = null,
        private readonly ?Carbon $fromTime = null,
    ) {}

    /**
     * Execute the job.
     */
    public function handle(CommunicationRepository $repository): void
    {
        if ($this->consumerProductId)
            $repository->updateMostRecentCommunication($this->consumerProductId);
        else if ($this->fromTime) {
            $recentConsumerProductIds = DB::table(LeadProcessingCommunication::TABLE)
                ->select(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID)
                ->where(LeadProcessingCommunication::CREATED_AT, '>', $this->fromTime)
                ->pluck(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID)
                ->toArray();

            DB::table(LeadProcessingCommunication::TABLE)
                ->whereIn(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, $recentConsumerProductIds)
                ->update([LeadProcessingCommunication::FIELD_MOST_RECENT => false]);

            $subQuery = DB::table(LeadProcessingCommunication::TABLE)
                ->selectRaw("MAX(" . LeadProcessingCommunication::FIELD_ID . ") AS `most_recent_communication_id`")
                ->whereIn(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, $recentConsumerProductIds)
                ->groupBy(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID);

            DB::table(LeadProcessingCommunication::TABLE)
                ->whereIn(LeadProcessingCommunication::FIELD_CONSUMER_PRODUCT_ID, $recentConsumerProductIds)
                ->joinSub($subQuery, 'most_recent_communications', fn($join)
                    => $join->on(LeadProcessingCommunication::TABLE.'.'.LeadProcessingCommunication::FIELD_ID, 'most_recent_communications.most_recent_communication_id')
                )->update([LeadProcessingCommunication::FIELD_MOST_RECENT => true]);
        }

    }
}
