<?php

namespace App\Jobs\Prospects;

use App\Services\SlackDirectMessageService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class SendDirectSlackMessage implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public string $message,
        public int $userId,
    ) {}

    /**
     * @throws ConnectionException
     */
    public function handle(): void
    {
        SlackDirectMessageService::sendMessage($this->message, $this->userId);
    }
}
