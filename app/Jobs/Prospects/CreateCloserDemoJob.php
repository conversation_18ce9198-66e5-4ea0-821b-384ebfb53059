<?php

namespace App\Jobs\Prospects;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\CloserDemoService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Queue\SerializesModels;

class CreateCloserDemoJob implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        public int $userId,
        public string $calendlyEventUrl,
        public NewBuyerProspect $prospect,
    ) {}

    public function handle(): void
    {
        CloserDemoService::create($this->userId, $this->calendlyEventUrl, $this->prospect);
    }
}
