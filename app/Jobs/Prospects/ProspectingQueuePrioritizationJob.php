<?php

namespace App\Jobs\Prospects;

use App\Services\Prospects\ProspectingQueuePrioritizationService;
use Illuminate\Contracts\Queue\ShouldQueue;

/**
 * Update ordinal values of NewBuyerProspects to reflect queue priority
 */
class ProspectingQueuePrioritizationJob implements ShouldQueue
{
    public function handle(): void
    {
        ProspectingQueuePrioritizationService::prioritizeQueue();
    }
}
