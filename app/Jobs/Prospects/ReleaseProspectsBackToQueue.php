<?php

namespace App\Jobs\Prospects;

use App\Enums\Prospects\ProspectStatus;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ReleaseProspectsBackToQueue implements ShouldQueue
{
    use Queueable;

    public function handle(): void
    {
        NewBuyerProspect::whereStatus(ProspectStatus::ACTIVE)
            ->whereDate('user_assigned_at', '<=', now()->subWeek())
            ->update([
                'user_id' => 0,
                'user_assigned_at' => null,
                'status' => ProspectStatus::INITIAL,
            ]);
    }
}
