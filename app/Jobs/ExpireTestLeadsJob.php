<?php

namespace App\Jobs;

use App\Enums\TestProductStatus;
use App\Models\TestProduct;
use App\Services\TestProducts\TestProductService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;

class ExpireTestLeadsJob implements ShouldQueue
{
    const CHUNK_SIZE = 1000;

    use Dispatchable, Queueable;

    protected Carbon $expireDate;

    public function __construct(Carbon $expireDate = null)
    {
        $this->queue = config('queue.named_queues.long_running');
        $this->expireDate = $expireDate ?: now();
    }

    /**
     * @param TestProductService $testProductService
     */
    public function handle(TestProductService $testProductService): void
    {
        $startDate = $this->expireDate->startOfDay(); // 00:00:00 of the current day
        $endDate = $this->expireDate->copy()->endOfDay();   // 23:59:59 of the current day

        TestProduct::query()
            ->where(TestProduct::FIELD_EXPIRE_AT, '>=', $startDate)
            ->where(TestProduct::FIELD_EXPIRE_AT, '<=', $endDate)
            ->whereNot(function (Builder $query) {
                $query->where(TestProduct::FIELD_STATUS, TestProductStatus::EXPIRED)
                    ->orWhere(TestProduct::FIELD_CONTACTED, true);
            })
            ->chunk(self::CHUNK_SIZE, function ($products) use($testProductService) {
                foreach ($products as $product) {
                    try {
                        $testProductService->expireProduct($product);
                    } catch (\Exception $exception) {
                        logger()->error($exception);
                    }
                }
            });
    }
}
