<?php

namespace App\Jobs;

use App\DTO\Meet\ConferenceDTO;
use App\DTO\Meet\ConferenceParticipantDTO;
use App\Enums\AppFeature;
use App\Enums\Calendar\DemoStatus;
use App\Exceptions\GoogleMeet\GoogleMeetTokenNotFoundException;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\Demo;
use App\Models\User;
use App\Services\AppLogger;
use App\Services\Conference\ConferenceSyncService;
use App\Services\GoogleMeetService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class SyncConferenceData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected AppLogger $appLogger;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected CalendarEvent $calendarEvent)
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    /**
     * @throws Exception
     */
    public function handle(
        GoogleMeetService $googleMeetService,
        ConferenceSyncService $conferenceService
    ): void
    {
        $this->appLogger = AppLogger::make(
            relations: [$this->calendarEvent],
            feature  : AppFeature::CALENDAR,
            function : 'syncConferenceData'
        );

        try {
            $eventConferences = $googleMeetService->getConferenceData(
                calendarEvent: $this->calendarEvent
            );

            $relevantConferences = $this->filterOutIrrelevantConferences($eventConferences);

            if ($relevantConferences->isEmpty()) {
                $this->appLogger->warn(
                    message: 'No relevant conferences found',
                );
                return;
            }

            foreach ($relevantConferences as $conference) {
                $conferenceService->importConference(
                    userId         : $this->calendarEvent->user_id,
                    calendarEventId: $this->calendarEvent->id,
                    conference     : $conference
                );
            }

            $shouldMarkDemoAsCompleted = $this->determineIfShouldUpdateDemoStatus(
                relevantConferences: $relevantConferences
            );

            if ($shouldMarkDemoAsCompleted) {
                Demo::query()
                    ->where(Demo::FIELD_CALENDAR_EVENT_ID, $this->calendarEvent->id)
                    ->update([
                        Demo::FIELD_STATUS => DemoStatus::COMPLETED
                    ]);
            }

            // Try tomorrow to ensure that all data has been saved
            if (empty($this->calendarEvent->{CalendarEvent::FIELD_LAST_CONFERENCE_DATA_SYNC_AT})) {
                SyncConferenceData::dispatch($this->calendarEvent)
                    ->delay(now()->addDay());
            }

            $this->calendarEvent->update([
                CalendarEvent::FIELD_LAST_CONFERENCE_DATA_SYNC_AT => now(),
            ]);
        } catch (GoogleMeetTokenNotFoundException $exception) {
            $this->appLogger->exception(
                exception: $exception,
            );
        }
    }

    /**
     * Only mark demo as completed if external users (company users) have joined the conference
     * @param Collection $relevantConferences
     * @return bool
     */
    public function determineIfShouldUpdateDemoStatus(Collection $relevantConferences): bool
    {
        $userNames = User::query()
            ->select([
                User::TABLE . '.' . User::FIELD_NAME,
            ])
            ->get()
            ->map(fn($user) => Str::of($user->name)->trim());

        /** @var Collection<string> $allParticipantNames */
        $allParticipantNames = $relevantConferences
            ->map(fn(ConferenceDTO $conference) => $conference->getParticipants())
            ->flatten()
            ->map(fn(ConferenceParticipantDTO $participant) => $participant->getUserDisplayName());

        $externalUsers = $allParticipantNames
            ->filter(fn(string $participantName) => !$userNames->contains($participantName));

        $hasExternalUsers = $externalUsers->isNotEmpty();

        $this->appLogger->debug(
            message: 'Has external users ? ' . ($hasExternalUsers ? 'true' : 'false'),
            context: [
                'has_external_users'  => $hasExternalUsers,
                'external_user_names' => $externalUsers,
                'participants'        => $allParticipantNames
            ]
        );

        return $hasExternalUsers;
    }

    /**
     * @param Collection<ConferenceDTO> $conferences
     * @return Collection
     */
    public function filterOutIrrelevantConferences(Collection $conferences): Collection
    {
        return $conferences
            ->filter(function (ConferenceDTO $conference) {
                return $conference->getParticipants()->count() > 1
                    && $conference->getDurationInSeconds() > 0;
            });
    }
}
