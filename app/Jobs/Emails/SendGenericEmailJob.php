<?php

namespace App\Jobs\Emails;

use App\Mail\SendGenericEmail;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class SendGenericEmailJ<PERSON> implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected string $toEmail,
        protected string $subject,
        protected ?User $user,
        protected string $content,
        protected array $cc = [],
        protected array $bcc = []
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Mail::to($this->toEmail)
            ->cc($this->cc)
            ->bcc($this->bcc)
            ->sendNow(new SendGenericEmail(
                emailSubject: $this->subject,
                user: $this->user,
                emailContent: $this->content
            ));
    }
}
