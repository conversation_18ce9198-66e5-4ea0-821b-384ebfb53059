<?php

namespace App\Jobs\ConsumerProcessing;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Jobs\PingPostPublishJob;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueConstraintsRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\Odin\DuplicateProductService;
use App\Services\Odin\PingPostPublishing\PingPostPublishService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class CreateInitialProductJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public int $tries = 10;
    public int $maxExceptions = 1;

    /**
     * @param int $consumerProductId
     */
    public function __construct(
        protected int $consumerProductId
    )
    {
        $this->onQueue(config('queue.named_queues.appointment_allocation_queue'));
    }

    /**
     * @param ProductProcessingService $productProcessingService
     * @param ProductProcessingQueueConstraintsRepository $productProcessingQueueConstraintsRepository
     * @param DuplicateProductService $duplicateProductService
     * @param PingPostPublishService $pingPostPublishService
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(
        ProductProcessingService $productProcessingService,
        ProductProcessingQueueConstraintsRepository $productProcessingQueueConstraintsRepository,
        DuplicateProductService $duplicateProductService,
        PingPostPublishService $pingPostPublishService,
    ): void
    {
        $consumerProduct = ConsumerProduct::query()->findOrFail($this->consumerProductId);

        if ($consumerProduct->status === ConsumerProduct::STATUS_INITIAL) {
            if($duplicateProductService->cancelIfProductIsDuplicate($this->consumerProductId))
                return;

            // Check for buyers and industry, ping post publish lead if no available buyers
            if ($pingPostPublishService->checkPingPostValidity($this->consumerProductId)) {
                PingPostPublishJob::dispatch($this->consumerProductId);
            } else {
                $productProcessingQueueConstraintsRepository->saveProductProcessingQueueConstraintsBucketFlags($this->consumerProductId);
                $productProcessingService->createInitialProduct($this->consumerProductId);
            }
        }
    }

    /**
     * @return void
     */
    protected function handleDirectLeads(): void
    {
        //Allocate direct leads without timezone delay
        /** @var ConsumerProductRepository $repository */
        $repository = app(ConsumerProductRepository::class);

        /** @var ConsumerProjectProcessingService $service */
        $service = app(ConsumerProjectProcessingService::class);

        $consumerProduct = $repository->findOrFail($this->consumerProductId);

        $consumerProduct->update([ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_PENDING_ALLOCATION]);

        ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($consumerProduct);
        AttemptConsumerProjectAllocationJob::dispatch(
            $service->prepareConsumerProject(
                consumer: $consumerProduct->consumer,
                address: $consumerProduct->address,
                lastQualifiedAt: now(),
                skipTimezoneDelay: true,
            )
        );
    }
}