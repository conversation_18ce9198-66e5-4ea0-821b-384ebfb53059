<?php

namespace App\Jobs\ConsumerProcessing;

use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class CheckProductHeartbeatJob implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    const int HEARTBEAT_INACTIVE_BUFFER_MINUTES = 5;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param int $consumerProductId
     * @param int $processorId
     */
    public function __construct(
        protected int $consumerProductId,
        protected int $processorId
    )
    {
        $this->onQueue(config('queue.named_queues.heartbeat'));
    }

    public function handle(ProductProcessingService $service, ProductProcessingRepository $repository): void
    {
        /** @var LeadProcessor|null $processor */
        $processor = LeadProcessor::query()->findOrFail($this->processorId);
        $consumerProduct = ConsumerProduct::query()->findOrFail($this->consumerProductId);

        $heartbeat = $repository->getHeartbeat($consumerProduct, $processor);
        if($heartbeat) {
            if(now()->subMinutes(self::HEARTBEAT_INACTIVE_BUFFER_MINUTES)->greaterThan(Carbon::createFromTimestamp($heartbeat->last_heartbeat)))
                $service->releaseProduct($consumerProduct, $processor);
            else
                dispatch((new CheckProductHeartbeatJob($this->consumerProductId, $this->processorId))
                    ->delay(self::HEARTBEAT_INACTIVE_BUFFER_MINUTES));
        }
    }

    /**
     * Returns the unique id for this job.
     *
     * @return string
     */
    public function uniqueId(): string
    {
        return "heartbeat_" . $this->consumerProductId;
    }
}
