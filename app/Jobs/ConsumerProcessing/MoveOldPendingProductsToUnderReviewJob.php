<?php

namespace App\Jobs\ConsumerProcessing;

use App\Models\LeadProcessingPendingReview;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Collection;

class MoveOldPendingProductsToUnderReviewJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @param ProductProcessingQueueRepository $queueRepository
     * @return void
     */
    public function handle(ProductProcessingQueueRepository $queueRepository, ProductProcessingRepository $processingRepository): void
    {
        $pendingReviewTimeframeLimit = time() - (60 * 60 * 24 * ProductProcessingQueueRepository::MAX_DAYS_DISPLAY[ProductProcessingQueueRepository::STATUS_PENDING_REVIEW]);

        /** @var Collection<LeadProcessingPendingReview> $oldPendingProducts */
        LeadProcessingPendingReview::query()
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, LeadProcessingPendingReview::TABLE . '.' . LeadProcessingPendingReview::FIELD_CONSUMER_PRODUCT_ID)
            ->where(ConsumerProduct::TABLE . '.' . ConsumerProduct::CREATED_AT, '<=', $pendingReviewTimeframeLimit)
            ->get()
            ->each(function (LeadProcessingPendingReview $pending) use ($queueRepository, $processingRepository) {
                $queueRepository->changeQueues(
                    $pending->consumerProduct,
                    $pending->leadProcessor,
                    ProductProcessingQueueRepository::STATUS_UNDER_REVIEW,
                    "Aged out of Pending Review",
                );
                $queueRepository->logStatus("Automatically moved old lead from Pending to Under Review: " . $pending->consumerProduct->id, [ "consumer_product_id" => $pending->consumerProduct->id ]);

                $processingRepository->releaseProduct($pending->consumerProduct->id);
                $processingRepository->removeHeartbeat($pending->consumerProduct, $pending->leadProcessor);
            });
    }
}
