<?php

namespace App\Jobs;

use App\Campaigns\Delivery\Contacts\Enums\ContactDeliveryLogType;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class TrackContactDeliveryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected CompanyCampaign $campaign,
        protected int $consumer_product_id,
        protected int $contact_module_id,
        protected ContactDeliveryLogType $type,
        protected bool $success,
        protected array $payload = []
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        CompanyCampaignContactDeliveryLog::query()->create([
            CompanyCampaignContactDeliveryLog::FIELD_CONSUMER_PRODUCT_ID => $this->consumer_product_id,
            CompanyCampaignContactDeliveryLog::FIELD_CONTACT_MODULE_ID => $this->contact_module_id,
            CompanyCampaignContactDeliveryLog::FIELD_CAMPAIGN_ID => $this->campaign->id,
            CompanyCampaignContactDeliveryLog::FIELD_MODULE_ID => $this->campaign->deliveryModule?->id,
            CompanyCampaignContactDeliveryLog::FIELD_TYPE => $this->type,
            CompanyCampaignContactDeliveryLog::FIELD_SUCCESS => $this->success,
            CompanyCampaignContactDeliveryLog::FIELD_PAYLOAD => $this->payload
        ]);
    }
}
