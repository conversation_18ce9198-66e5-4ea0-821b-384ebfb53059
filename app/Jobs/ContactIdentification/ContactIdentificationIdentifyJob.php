<?php

namespace App\Jobs\ContactIdentification;

use App\DTO\ContactIdentification\Contact;
use App\Enums\ContactIdentification\IdentificationStatus;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\ContactIdentification\PossibleContact;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\Odin\Consumer;
use App\Services\ContactIdentification\ContactIdentificationService;
use App\Services\ContactIdentification\ContactSortingService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class ContactIdentificationIdentifyJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int SAVE_IDENTIFIED_CONTACTS_CHUNK_SIZE = 1000;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected IdentifiedContact $identifiedContact,
    )
    {
        $this->queue = 'contact_identification_identify';
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        $possibleContactsIdentified = $this->identifyContacts();

        $status = match ($possibleContactsIdentified->count()) {
            0       => IdentificationStatus::NO_RESULT,
            1       => IdentificationStatus::SINGLE_RESULT,
            default => IdentificationStatus::MULTIPLE_RESULTS,
        };

        [
            'remove'  => $toRemove,
            'restore' => $toRestore,
            'create'  => $toCreate,
        ] = $this->calculateSyncData(
            possibleContactsIdentified: $possibleContactsIdentified,
        );

        $this->syncContacts(
            remove : $toRemove,
            restore: $toRestore,
            create : $toCreate,
        );

        $nominationId = $this->calculateNomination(
            status: $status
        );

        $this->identifiedContact->update([
            IdentifiedContact::FIELD_IDENTIFICATION_STATUS => $status->value,
            IdentifiedContact::FIELD_NOMINATED_CONTACT_ID  => $nominationId
        ]);
    }

    /**
     * @param Collection $remove
     * @param Collection $restore
     * @param Collection $create
     * @return void
     */
    protected function syncContacts(
        Collection $remove,
        Collection $restore,
        Collection $create,
    ): void
    {
        if ($remove->isNotEmpty()) {
            PossibleContact::query()
                ->where(PossibleContact::FIELD_IDENTIFIED_CONTACT_ID, $this->identifiedContact->id)
                ->whereIn(PossibleContact::FIELD_ID, $remove)
                ->delete();
        }

        if ($restore->isNotEmpty()) {
            PossibleContact::withTrashed()
                ->where(PossibleContact::FIELD_IDENTIFIED_CONTACT_ID, $this->identifiedContact->id)
                ->whereIn(PossibleContact::FIELD_ID, $restore)
                ->update([PossibleContact::FIELD_DELETED_AT => null]);
        }

        if ($create->isNotEmpty()) {
            $chunks = $create->chunk(self::SAVE_IDENTIFIED_CONTACTS_CHUNK_SIZE);

            foreach ($chunks as $chunk) {
                PossibleContact::query()->upsert($chunk->all(), [
                    PossibleContact::FIELD_RELATION_ID,
                    PossibleContact::FIELD_RELATION_TYPE,
                    PossibleContact::FIELD_IDENTIFIED_CONTACT_ID,
                ]);
            }
        }
    }


    /**
     * @return Collection
     * @throws Exception
     */
    protected function identifyContacts(): Collection
    {
        $contactIdentificationService = new ContactIdentificationService();

        $possibleContactsIdentified = $contactIdentificationService->identify(
            value: $this->identifiedContact->identifier_value,
            type : SearchableFieldType::tryFrom($this->identifiedContact->identifier_field_type)
        );

        return collect($possibleContactsIdentified)->map(function (Contact $contact) {
            return [
                PossibleContact::FIELD_RELATION_ID           => $contact->getId(),
                PossibleContact::FIELD_RELATION_TYPE         => $contact->getType(),
                PossibleContact::FIELD_IDENTIFIED_CONTACT_ID => $this->identifiedContact->{IdentifiedContact::FIELD_ID}
            ];
        })->unique();
    }

    /**
     * @param IdentificationStatus $status
     * @return int|null
     * @throws Exception
     */
    public function calculateNomination(IdentificationStatus $status): ?int
    {
        $currentNominationId = $this->identifiedContact->{IdentifiedContact::FIELD_NOMINATED_CONTACT_ID};

        if ($this->isNominationValid($currentNominationId)) {
            return $currentNominationId;
        }

        return match ($status) {
            IdentificationStatus::SINGLE_RESULT,
            IdentificationStatus::MULTIPLE_RESULTS => $this->getNominationForMultipleResults(),
            default                                => null,
        };
    }

    /**
     * @param int|null $nominationId
     * @return bool
     */
    private function isNominationValid(?int $nominationId): bool
    {
        if (empty($nominationId)) {
            return false;
        }

        $distinctTypeCount = $this->identifiedContact->possibleContacts
            ->map(fn (PossibleContact $contact) => $contact->{PossibleContact::FIELD_RELATION_TYPE})
            ->unique()
            ->count();

        $isNominatedConsumer = $this->identifiedContact?->nominatedContact?->{PossibleContact::FIELD_RELATION_TYPE} === Consumer::class;

        if ($isNominatedConsumer && $distinctTypeCount > 1) {
            return false;
        }

        if (PossibleContact::query()->find($nominationId) !== null) {
            return true;
        }

        return false;
    }


    /**
     * @return int|null
     */
    private function getNominationForSingleResult(): ?int
    {
        return PossibleContact::query()
            ->where(PossibleContact::FIELD_IDENTIFIED_CONTACT_ID, $this->identifiedContact->{IdentifiedContact::FIELD_ID})
            ->value(PossibleContact::FIELD_ID);
    }

    /**
     * @return int|null
     * @throws Exception
     */
    private function getNominationForMultipleResults(): ?int
    {
        $possibleContacts = $this->identifiedContact
            ->possibleContacts()
            ->with(PossibleContact::RELATION_IDENTIFIABLE)
            ->get();

        if ($possibleContacts->isEmpty()) {
            return null;
        }

        /** @var ContactSortingService $contactSortingService */
        $contactSortingService = app(ContactSortingService::class);

        $sortedContacts = $contactSortingService->sortListByPriority($possibleContacts);

        return $sortedContacts->first()?->{PossibleContact::FIELD_ID};
    }

    /**
     * @param Collection $possibleContactsIdentified
     * @return array
     */
    protected function calculateSyncData(
        Collection $possibleContactsIdentified
    ): array
    {
        $databaseContacts = PossibleContact::withTrashed()
            ->select([
                PossibleContact::FIELD_ID,
                PossibleContact::FIELD_RELATION_TYPE,
                PossibleContact::FIELD_RELATION_ID,
                PossibleContact::FIELD_IDENTIFIED_CONTACT_ID,
                PossibleContact::FIELD_DELETED_AT
            ])
            ->where(PossibleContact::FIELD_IDENTIFIED_CONTACT_ID, $this->identifiedContact->{IdentifiedContact::FIELD_ID})
            ->get();

        $toRestore = collect();
        $toDelete = collect();

        $existingContactsSet = $databaseContacts->mapWithKeys(fn($contact) => [
            $this->makeUniqueKey($contact) => $contact
        ]);

        $possibleContactsIdentifiedSet = $possibleContactsIdentified->mapWithKeys(fn($contact) => [
            $this->makeUniqueKey($contact) => $contact
        ]);

        $newContacts = $possibleContactsIdentified->reject(fn($contact) => $existingContactsSet->has($this->makeUniqueKey($contact)));

        foreach ($databaseContacts as $storedContact) {
            $uniqueKey = $this->makeUniqueKey($storedContact);

            if ($possibleContactsIdentifiedSet->has($uniqueKey) && $storedContact->{PossibleContact::FIELD_DELETED_AT}) {
                $toRestore->push($storedContact->id);
            } elseif (!$possibleContactsIdentifiedSet->has($uniqueKey)) {
                $toDelete->push($storedContact->id);
            }
        }

        return [
            'remove'  => $toDelete,
            'restore' => $toRestore,
            'create'  => $newContacts,
        ];
    }


    /**
     * @param $contact
     * @return string
     */
    private function makeUniqueKey($contact): string
    {
        return "{$contact[PossibleContact::FIELD_RELATION_TYPE]}_{$contact[PossibleContact::FIELD_RELATION_ID]}_{$contact[PossibleContact::FIELD_IDENTIFIED_CONTACT_ID]}";
    }
}
