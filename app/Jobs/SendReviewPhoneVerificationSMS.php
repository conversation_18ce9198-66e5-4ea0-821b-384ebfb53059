<?php

namespace App\Jobs;

use App\Models\ConsumerReviews\Reviewer;
use App\Repositories\SmsVerificationRepository;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\Twilio\TwilioVerificationService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Twilio\Exceptions\TwilioException;

class SendReviewPhoneVerificationSMS implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param Reviewer $reviewer
     */
    public function __construct(protected Reviewer $reviewer)
    {
        $this->onQueue(config('queue.named_queues.default'));
    }

    /**
     * @param SmsVerificationRepository $smsVerificationRepository
     * @param TwilioVerificationService $twilioVerificationService
     * @return void
     * @throws TwilioException
     */
    public function handle(SmsVerificationRepository $smsVerificationRepository, TwilioVerificationService $twilioVerificationService): void
    {
        $reviewerReference = $this->reviewer->reference;
        $twilioVerificationService->setPhoneNumber($this->reviewer->phone)->sendCode($reviewerReference);
    }
}
