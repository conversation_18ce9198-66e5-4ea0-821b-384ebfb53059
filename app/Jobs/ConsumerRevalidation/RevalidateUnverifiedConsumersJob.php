<?php

namespace App\Jobs\ConsumerRevalidation;

use App\Models\Odin\Consumer;
use App\Repositories\Odin\ConsumerRepository;
use App\Services\ConsumerRevalidationService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RevalidateUnverifiedConsumersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Execute the job.
     *
     * @param ConsumerRevalidationService $consumerRevalidationService
     * @param ConsumerRepository $consumerRepository
     *
     * @throws Exception
     */
    public function handle(ConsumerRevalidationService $consumerRevalidationService, ConsumerRepository $consumerRepository): void
    {
        $consumerRepository->getUnverifiedConsumers(
            now()->subHours(5),
            now()->subHours(2)
        )->each(fn(Consumer $consumer) => $this->handleSendingSMS($consumer, $consumerRevalidationService));
    }

    /**
     * @param Consumer $consumer
     * @param ConsumerRevalidationService $consumerRevalidationService
     *
     * @return void
     * @throws Exception
     */
    protected function handleSendingSMS(Consumer $consumer, ConsumerRevalidationService $consumerRevalidationService): void
    {
        $delay = $consumerRevalidationService->getConsumerTimezoneOpenDelay($consumer);

        if ($delay > 0) {
            $this->setRevalidationRequestedAt($consumer, $delay);
            SendConsumerRevalidationSMS::dispatch($consumer)->delay($delay);
            return;
        }

        $this->setRevalidationRequestedAt($consumer);
        SendConsumerRevalidationSMS::dispatchSync($consumer);
    }

    /**
     * @param Consumer $consumer
     * @param int $delay
     *
     * @return void
     */
    protected function setRevalidationRequestedAt(Consumer $consumer, int $delay = 0): void
    {
        if ($delay > 0) {
            $consumer->revalidation_requested_at = now()->addSeconds($delay);
        } else {
            $consumer->revalidation_requested_at = now();
        }

        $consumer->save();
    }
}
