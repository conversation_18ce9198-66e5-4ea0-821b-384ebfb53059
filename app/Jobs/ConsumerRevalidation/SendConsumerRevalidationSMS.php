<?php

namespace App\Jobs\ConsumerRevalidation;

use App\Models\Odin\Consumer;
use App\Services\ConsumerRevalidationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendConsumerRevalidationSMS implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Consumer $consumer) {}

    /**
     * Execute the job.
     */
    public function handle(ConsumerRevalidationService $consumerRevalidationService): void
    {
        $consumerRevalidationService->sendRevalidationSMS($this->consumer);
    }
}
