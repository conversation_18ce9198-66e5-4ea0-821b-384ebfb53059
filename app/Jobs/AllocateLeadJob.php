<?php

namespace App\Jobs;

use App\Models\AppointmentProcessingAllocation;
use App\Models\LeadProcessingAllocation;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Services\LeadProcessing\LeadProcessingService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

class AllocateLeadJob implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    /**
     * If the job fails we'd still want to remove the atomic lock on this job such that it can
     * be re-attempted in the case of a failure.
     *
     * This is currently set to three minutes.
     */
    const EXPIRE_TIME_SECONDS = 180;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /** @var LeadProcessingAllocation $leadProcessingAllocation */
    protected LeadProcessingAllocation $leadProcessingAllocation;

    // If true, this allocation attempt should only sell to 'Unverified' budgets
    protected bool $unverified;

    protected ?int $remainingLegs;

    protected array $ignoreCompanyIds;

    /**
     * @param LeadProcessingAllocation $leadProcessingAllocation
     * @param bool $unverified
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     */
    public function __construct(
        LeadProcessingAllocation $leadProcessingAllocation,
        bool $unverified = false,
        array $ignoreCompanyIds = [],
        ?int $remainingLegs = null
    )
    {
        $this->queue = 'lead_allocation_queue';
        $this->leadProcessingAllocation = $leadProcessingAllocation;
        $this->unverified = $unverified;
        $this->remainingLegs = $remainingLegs;
        $this->ignoreCompanyIds = array_filter(array_unique($ignoreCompanyIds));;
    }

    /**
     * @param ProductProcessingService $productProcessingService
     * @throws Throwable
     */
    public function handle(ProductProcessingService $productProcessingService)
    {
        $this->leadProcessingAllocation->refresh();
        $this->leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->refresh();

        if ($this->leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_CANCELLED) {
            logger()->error("Lead allocation was failed as the lead status is canceled: {$this->leadProcessingAllocation->lead_id}");

            return;
        }

        throw new Exception("Legacy lead allocation job should not be used");
    }

    /**
     * The middleware this job should run through.
     *
     * @return array
     */
    public function middleware(): array
    {
        return [
            (new WithoutOverlapping($this->uniqueId()))
                ->dontRelease()
                ->expireAfter(self::EXPIRE_TIME_SECONDS)
        ];
    }

    /**
     * The unique id of this job.
     *
     * @return int
     */
    public function uniqueId()
    {
        return $this->leadProcessingAllocation->lead_id;
    }
}
