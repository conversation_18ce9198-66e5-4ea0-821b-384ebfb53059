<?php

namespace App\Jobs;

use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\DuplicateProductService;
use App\Services\Odin\PingPostAffiliateService;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

/**
 * Class CreateInitialLead
 * @package App\Jobs
 */
class AddAffiliateLeadToQueue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public int $tries = 1;

    public int $maxExceptions = 1;

    /**
     * @param int $consumerProductId
     */
    public function __construct(protected int $consumerProductId)
    {
        $this->onQueue(config('queue.named_queues.appointment_allocation_queue'));
    }

    /**
     * @param DuplicateProductService $duplicateProductService
     * @param PingPostAffiliateService $pingPostAffiliateService
     * @return void
     */
    public function handle(
        DuplicateProductService $duplicateProductService,
        PingPostAffiliateService $pingPostAffiliateService,
    ): void
    {
        $consumerProduct = ConsumerProduct::find($this->consumerProductId);

        if(!empty($consumerProduct)) {
            $pingPostAffiliateService->addPingPostLeadToAffiliateQueue($this->consumerProductId);

            /** @var ProductProcessingService $service */
            $service = app(ProductProcessingService::class);
            $service->releaseProduct($consumerProduct, LeadProcessor::systemProcessor());
        }
        else {
            $pingPostAffiliateService::sendNotificationEmails(
                'Ping Post Queue Allocation Error',
                "AddAffiliateLeadToQueue Job called without valid consumer product id.");
        }
    }
}
