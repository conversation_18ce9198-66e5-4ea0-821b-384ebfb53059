<?php

namespace App\Jobs\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Services\Advertising\AdvertisingCampaignService;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Campaigns\AdvertisingServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Throwable;

class UpdateAdvertisingCampaignLocationsJob implements ShouldQueue, ShouldBeUnique
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public $failOnTimeout = true;

    public $timeout = Carbon::SECONDS_PER_MINUTE * 8;

    public $tries = 1;

    public $uniqueFor = Carbon::SECONDS_PER_MINUTE * 8;

    /**
     * @param string $platform
     * @param array $retryCampaigns
     */
    public function __construct(private string $platform, private array $retryCampaigns = [])
    {
        $this->queue = 'long_running_single_attempt';
    }

    /**
     * @return string
     */
    public function uniqueId()
    {
        return $this->platform;
    }

    /**
     * @param AdvertisingCampaignService $advertisingCampaignService
     * @throws Exception
     */
    public function handle(AdvertisingCampaignService $advertisingCampaignService)
    {
        $start = microtime(true);
        $logId = (int) $start;

        try {
            AdvertisingLoggingService::writeLog("Updating advertising campaign locations", ['id' => $logId, 'platform' => $this->platform]);

            $advertisingService = AdvertisingServiceFactory::make($this->platform);

            $accountCampaigns = $advertisingService->getAutomatedCampaignsWithLocations($logId, $this->retryCampaigns);

            if($accountCampaigns) {
                $advertisingService->updateCampaignLocations(
                    $logId,
                    $advertisingCampaignService->determineCampaignLocationsToUpdate($logId, $accountCampaigns, $this->platform),
                    !empty($this->retryCampaigns)
                );
            }
            else {
                AdvertisingLoggingService::writeLog("No {$this->platform} campaigns scheduled for update", ['id' => $logId]);
            }

            $elapsed = (int) floor(microtime(true) - $start);

            AdvertisingLoggingService::writeLog("Finished updating advertising campaign locations: $elapsed seconds elapsed", ['id' => $logId, 'platform' => $this->platform]);
        }
        catch(Throwable $e) {
            $errorFileParts = explode('/', $e->getFile());
            $errorFile = array_pop($errorFileParts);

            $errorLocation = $errorFile.": Line ".$e->getLine().". ";

            AdvertisingLoggingService::writeLog(
                $errorLocation.substr($e->getMessage(), 0, 255),
                [
                    'id' => $logId,
                    'platform' => $this->platform,
                    'trace' => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            logger()->error($errorLocation.$e->getMessage());
            logger()->error($e->getTraceAsString());

            // Send the Email Notification
            AdvertisingLoggingService::sendNotificationEmails(
                explode(',', config("services.ads.notification_emails")),
                "SolarReviews: ".AdvertisingPlatform::displayName($this->platform)." Automation Error",
                "There was an error during the ads locations update"
            );

        }
    }
}
