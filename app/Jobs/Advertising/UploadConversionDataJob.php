<?php

namespace App\Jobs\Advertising;

use App\Abstracts\Advertising\AdvertisingConversionsServiceAbstract;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\Advertiser;
use App\Models\AdvertisingAccount;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Conversions\AdvertisingConversionsServiceFactory;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;
use Carbon\Carbon;

class UploadConversionDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public int $tries = 2;

    public int $maxExceptions = 1;

    public int $timeout = Carbon::SECONDS_PER_MINUTE * 10;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        private readonly AdvertisingPlatform $advertisingPlatform,
        private readonly AdvertiserEnum $advertiser,
        private readonly bool $onlySold
    )
    {

    }

    /**
     * @throws Exception
     */
    public function handle()
    {
        try {
            $displayName = AdvertisingPlatform::displayName($this->advertisingPlatform->value);

            AdvertisingLoggingService::writeLog(
                "Starting offline conversion upload for {$displayName}",
                [
                    'platform' => $this->advertisingPlatform->value
                ]
            );

            $accountsToUpload = AdvertisingAccount::query()
                ->where(AdvertisingAccount::FIELD_PLATFORM, $this->advertisingPlatform->value)
                ->where(AdvertisingAccount::FIELD_TRACKS_CONVERSIONS, true)
                ->whereHas(AdvertisingAccount::RELATION_ADVERTISER, function($has) {
                    $has->where(Advertiser::TABLE.'.'.Advertiser::FIELD_KEY, AdvertiserEnum::getKey($this->advertiser));
                })
                ->get()
                ->keyBy(fn($account) => (string) $account->{AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID});

            $conversionService = AdvertisingConversionsServiceFactory::make($this->advertisingPlatform->value);

            $successfulAccountIds = [];
            foreach($accountsToUpload as $accountId => $account) {
                if(!$conversionService->checkIfTimeToUploadConversions($account, $this->advertisingPlatform)) {
                    continue;
                }

                AdvertisingLoggingService::writeLog(
                    "Offline conversion upload - {$displayName} account $accountId",
                    [
                        'platform' => $this->advertisingPlatform->value,
                        'account_id' => $accountId
                    ]
                );

                $leadConversionInfo = $conversionService->getSoldLeadsWithTrackingIdAndRevenue($account, $this->advertisingPlatform);

                if(!$this->onlySold) {
                    $leadConversionInfo = $leadConversionInfo->concat($conversionService->getUnsoldGoodToSellLeadsWithTrackingId($account, $this->advertisingPlatform));
                }

                [
                    AdvertisingConversionsServiceAbstract::SUCCESSFUL_PRODUCT_ASSIGNMENT_IDS => $successfulProductAssignmentIds,
                    AdvertisingConversionsServiceAbstract::SUCCESSFUL_PRODUCT_REJECTION_IDS => $successfulProductRejectionIds,
                    AdvertisingConversionsServiceAbstract::SUCCESSFUL_CONSUMER_PRODUCT_TRACKING_IDS => $successfulConsumerProductTrackingIds
                ] = $conversionService->uploadConversions(
                    $accountId,
                    $leadConversionInfo,
                    $this->advertiser
                );

                $success = !empty($successfulProductAssignmentIds)
                        || !empty($successfulProductRejectionIds)
                        || !empty($successfulConsumerProductTrackingIds);

                if($success) {
                    $conversionService->markConversionsUploaded($successfulProductAssignmentIds, $successfulProductRejectionIds, $successfulConsumerProductTrackingIds);

                    $successfulAccountIds[] = $accountId;
                }

                AdvertisingLoggingService::writeLog(
                    $success ? "Uploaded conversions for {$displayName} account $accountId" : "Failed to upload conversions for {$displayName} account $accountId",
                    [
                        'platform' => $this->advertisingPlatform->value,
                        'account_id' => $accountId
                    ]
                );
            }

            $conversionService->updateUploadConversionsLastRunTimestamp($successfulAccountIds);

            AdvertisingLoggingService::writeLog(
                "Finished offline conversion upload for {$displayName}",
                [
                    'platform' => $this->advertisingPlatform->value
                ]
            );
        }
        catch(Throwable $e) {
            $errorFileParts = explode('/', $e->getFile());
            $errorFile = array_pop($errorFileParts);

            $errLocation = $errorFile.": Line ".$e->getLine().". ";

            AdvertisingLoggingService::writeLog(
                "Offline conversions upload error. ".$errLocation.substr($e->getMessage(), 0, 255),
                [
                    'platform' => $this->advertisingPlatform->value,
                    'trace' => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            $displayName = AdvertisingPlatform::displayName($this->advertisingPlatform->value);

            AdvertisingLoggingService::sendNotificationEmails(
                explode(',', config("services.ads.notification_emails")),
                "SolarReviews: {$displayName} Conversions Upload Error",
                "There was an error during the conversions upload"
            );

            logger()->error($errLocation.$e->getMessage());
        }
    }
}
