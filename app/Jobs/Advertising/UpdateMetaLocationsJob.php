<?php

namespace App\Jobs\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Services\Advertising\Locations\AdvertisingLocationsServiceFactory;
use App\Services\Advertising\Locations\MetaAdsLocationsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class UpdateMetaLocationsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle(MetaAdsLocationsService $metaAdsLocationsService)
    {
        $metaAdsLocationsService->storePlatformLocations();
    }
}
