<?php

namespace App\Jobs\Advertising;

use App\Services\Advertising\Locations\GoogleAdsGeoTargetService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class UpdateGoogleAdsGeoTargetsJob implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public function __construct(protected bool $force = false)
    {

    }

    /**
     * @param GoogleAdsGeoTargetService $googleAdsGeoTargetService
     * @throws Exception
     */
    public function handle(GoogleAdsGeoTargetService $googleAdsGeoTargetService)
    {
        try {
            $googleAdsGeoTargetService->storePlatformLocations($this->force);
        }
        catch(Exception $e) {
            if(!($e instanceof FileNotFoundException)) {
                $this->release();
            }
        }
    }
}
