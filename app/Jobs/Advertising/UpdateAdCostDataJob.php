<?php

namespace App\Jobs\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Services\Advertising\AdvertisingCostService;
use App\Services\QueueHelperService;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class UpdateAdCostDataJob implements ShouldQueue, ShouldBeUnique
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param CarbonPeriod $period
     * @param AdvertisingPlatform $adSource
     * @param array $resolutions
     * @param array $industries
     * @param array $advertisers
     * @param string|null $filePath
     * @param bool $noDb
     * @param bool $noDelete
     */
    public function __construct(
        private CarbonPeriod $period,
        private AdvertisingPlatform $adSource,
        private array $resolutions,
        private array $industries,
        private array $advertisers,
        private ?string $filePath = null,
        private bool $noDb = false,
        private bool $noDelete = false,
    ) {
        $this->queue = QueueHelperService::QUEUE_NAME_LONG_RUNNING;
    }

    /**
     * @param AdvertisingCostService $advertisingCostService
     * @return void
     * @throws Exception
     */
    public function handle(AdvertisingCostService $advertisingCostService): void
    {
        $advertisingCostService->getDailyAdCostData(
            period: $this->period,
            adSource: $this->adSource,
            resolutions: $this->resolutions,
            industries: $this->industries,
            advertisers: $this->advertisers,
            filePath: $this->filePath,
            noDb: $this->noDb,
            noDelete: $this->noDelete);
    }
}
