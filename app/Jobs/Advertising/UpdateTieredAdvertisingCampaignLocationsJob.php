<?php

namespace App\Jobs\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\Odin\Industry;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Models\TieredAdvertisingConfiguration;
use App\Models\TieredAdvertisingInstance;
use App\Services\Advertising\AdvertisingLoggingService;
use App\Services\Advertising\Campaigns\AdvertisingServiceFactory;
use App\Services\Advertising\TieredAdvertisingService;
use App\Services\TieredAdvertisingCountyCalculationService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

class UpdateTieredAdvertisingCampaignLocationsJob implements ShouldQueue, ShouldBeUnique
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public $failOnTimeout = true;

    public $timeout = Carbon::SECONDS_PER_MINUTE * 8;

    public $tries = 1;

    public $uniqueFor = Carbon::SECONDS_PER_MINUTE * 8;

    /**
     * @param string $platform
     * @param string $industry
     * @param bool $forceUpdate
     * @param int|null $instanceId
     */
    public function __construct(
        private readonly string $platform,
        private readonly string $industry = '',
        private readonly bool $forceUpdate = false,
        private readonly ?int $instanceId = null,
    )
    {
        $this->queue = 'long_running_single_attempt';
    }

    /**
     * @return string
     */
    public function uniqueId(): string
    {
        return $this->platform;
    }

    /**
     * @param TieredAdvertisingCountyCalculationService $countyCalculationService
     * @param TieredAdvertisingService $tieredAdsService
     * @return void
     * @throws Exception
     */
    public function handle(
        TieredAdvertisingCountyCalculationService $countyCalculationService,
        TieredAdvertisingService $tieredAdsService,
    ): void
    {
        ini_set('memory_limit','-1');
        $start = microtime(true);
        $logId = (int) $start;

        $countyCalculationService->calculateTieredAdvertisingCounties($this->platform, $this->industry, $this->instanceId);

        if ($this->industry === '') {
            $industries = Industry::all();
        } else {
            $industries = Industry::query()->where(Industry::FIELD_SLUG, $this->industry)->get();
        }

        if ($this->instanceId === null) {
            $instanceIds = TieredAdvertisingInstance::query()
                ->where(TieredAdvertisingInstance::FIELD_PLATFORM, $this->platform)
                ->whereIn(TieredAdvertisingInstance::FIELD_INDUSTRY_ID, $industries->pluck(Industry::FIELD_ID))
                ->get([TieredAdvertisingInstance::FIELD_ID])->pluck(TieredAdvertisingInstance::FIELD_ID);

            // Add null for default instances
            $instanceIds->push(null);
        } else {
            $instanceIds = [$this->instanceId];
        }

        // TODO: Remove this when Tiered Advertising enabled for Microsoft
        if ($this->platform === AdvertisingPlatform::MICROSOFT->value)
            return;

        $advertisingService = AdvertisingServiceFactory::make($this->platform);

        foreach ($industries as $industry) {
            foreach ($instanceIds as $instanceId) {
                try {
                    // Get config for platform and industry
                    $config = $tieredAdsService->getConfigModel($this->platform, $industry->id, $instanceId);

                    // If no config defined, continue
                    if (!$config)
                        continue;

                    AdvertisingLoggingService::writeLog("Tiered ads locations update start", ['id' => $logId, 'platform' => $this->platform]);

                    // Check enabled status
                    if ($config->{TieredAdvertisingConfiguration::FIELD_ENABLED}) {
                        $frequencyMinutes = $config->{TieredAdvertisingConfiguration::FIELD_UPDATE_FREQUENCY_MINUTES};
                        $lastUpdated      = $config->{TieredAdvertisingConfiguration::FIELD_LAST_LOCATION_UPDATE};

                        if (!$lastUpdated || Carbon::parse($lastUpdated)->diffInMinutes(Carbon::now()) > $frequencyMinutes || $this->forceUpdate) {
                            $advertisingService->updateTieredAdsCampaignLocations($industry->{Industry::FIELD_ID}, $instanceId);
                            $config->update([
                                TieredAdvertisingConfiguration::FIELD_LAST_LOCATION_UPDATE => now()
                            ]);
                        }
                    }

                    $elapsed = (int)floor(microtime(true) - $start);

                    AdvertisingLoggingService::writeLog("Tiered ads location update complete: $elapsed seconds elapsed", ['id' => $logId, 'platform' => $this->platform]);
                } catch (Throwable $e) {
                    $errorFileParts = explode('/', $e->getFile());
                    $errorFile      = array_pop($errorFileParts);

                    $errorLocation = $errorFile . ": Line " . $e->getLine() . ". ";

                    AdvertisingLoggingService::writeLog(
                        $errorLocation . substr($e->getMessage(), 0, 255),
                        [
                            'id'       => $logId,
                            'platform' => $this->platform,
                            'trace'    => base64_encode(gzcompress($e->getTraceAsString(), 9))
                        ]
                    );

                    logger()->error($errorLocation . $e->getMessage());
                    logger()->error($e->getTraceAsString());

                    // Send the Email Notification
                    AdvertisingLoggingService::sendNotificationEmails(
                        explode(',', config("services.ads.notification_emails")),
                        "SolarReviews: " . AdvertisingPlatform::displayName($this->platform) . " Tiered Advertising Automation Error",
                        "There was an error during the tiered ads locations update. Error: ".$e->getMessage()." Trace: ".$e->getTraceAsString()
                    );
                }
            }
        }
    }
}
