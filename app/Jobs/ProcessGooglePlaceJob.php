<?php

namespace App\Jobs;

use App\Services\CompanyDiscoveryService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

/**
 * Class ProcessGooglePlaceJob
 * @package App\Jobs
 */
class ProcessGooglePlaceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /** @var array $companyDetails */
    private array $companyDetails;

    /**
     * @param array $companyDetails
     */
    public function __construct(array $companyDetails)
    {
        $this->onQueue(config('queue.named_queues.long_running'));

        $this->companyDetails = $companyDetails;
    }

    /**
     * @param CompanyDiscoveryService $companyDiscoveryService
     * @return void
     */
    public function handle(CompanyDiscoveryService $companyDiscoveryService)
    {
        $address = $companyDiscoveryService->processCompany($this->companyDetails);
        $companyDiscoveryService->processPlaceByAddress($address);
    }
}
