<?php

namespace App\Jobs\Workflows;

use App\Models\RunningWorkflow;
use App\Services\Workflows\WorkflowPipelineService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RunWorkflowPipeline implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected RunningWorkflow $runningWorkflow){
        $this->onQueue('workflows');
    }

    /**
     * Execute the job.
     *
     * @param WorkflowPipelineService $workflowPipelineService
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(WorkflowPipelineService $workflowPipelineService): void
    {
        $workflowPipelineService->execute($this->runningWorkflow);
    }
}
