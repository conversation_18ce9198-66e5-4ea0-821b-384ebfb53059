<?php

namespace App\Jobs\Billing;

use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Mail\SendCardExpiryEmail;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class NotifyCardExpiryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(protected int $companyId, protected PaymentMethodDTO $paymentMethod, protected bool $expired)
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $company = Company::query()->findOrFail($this->companyId);

        $this->notifyCompanyManagers($company);
        $this->notifyCompany($company);
    }

    /**
     * @param Company $company
     *
     * @return void
     */
    protected function notifyCompany(Company $company): void
    {
        $company->users()
            ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
            ->where(CompanyUser::FIELD_CAN_LOG_IN, true)
            ->get()
            ->each(function (CompanyUser $companyUser) use ($company){
                Mail::to($companyUser->email)->send(new SendCardExpiryEmail(
                    markdownTemplate: 'emails.card-expiry.card-expiry-company-user',
                    emailSubject: "Card ending in {$this->paymentMethod->getLast4()} " . ($this->expired ? 'has expired' : 'is expiring soon'),
                    emailData: [
                        'company' => $company,
                        'companyUser' => $companyUser,
                        'paymentMethod' => $this->paymentMethod,
                        'expired' => $this->expired
                    ],
                    fromEmail:  $this->getUser($company)?->email ?? '<EMAIL>',
                    fromName: $this->getUser($company)?->name,
                ));
            });
    }

    /**
     * @param Company $company
     *
     * @return User|null
     */
    protected function getUser(Company $company): ?User
    {
        return $company->accountManager ?? $company->onboardingManager ?? $company->businessDevelopmentManager ?? $company->customerSuccessManager;
    }

    /**
     * @param Company $company
     *
     * @return void
     */
    protected function notifyCompanyManagers(Company $company): void
    {
        collect([
            $company->customerSuccessManager,
            $company->accountManager,
            $company->onboardingManager,
            $company->businessDevelopmentManager
        ])
            ->filter()
            ->each(fn(User $user) => Mail::to($user->email)->send(new SendCardExpiryEmail(
                markdownTemplate: 'emails.card-expiry.card-expiry-rm',
                emailSubject: "Card ending in {$this->paymentMethod->getLast4()} of $company->name ($company->id) " . ($this->expired ? 'has expired' : 'is expiring soon'),
                emailData: [
                    'company' => $company,
                    'rm' => $user,
                    'paymentMethod' => $this->paymentMethod,
                    'expired' => $this->expired
                ]
            )));
    }
}
