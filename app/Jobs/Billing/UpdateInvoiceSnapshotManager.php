<?php

namespace App\Jobs\Billing;

use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateInvoiceSnapshotManager implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected int $companyId)
    {
        $this->onQueue('long_running');
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        $company = Company::query()->with(['accountManager', 'customerSuccessManager', 'businessDevelopmentManager'])->findOrFail($this->companyId);

        if (!($company->accountManager || $company->customerSuccessManager || $company->businessDevelopmentManager)) {
            BillingLogService::log(
                message: "0 rows updated for company id $this->companyId",
                namespace: "invoice_snapshot_manager_update",
                context: [
                    'company_id'    => $this->companyId,
                    'updated_count' => 0,
                    'managers'      => collect([
                        'account-manager'              => $company->accountManager?->id,
                        'business-development-manager' => $company->businessDevelopmentManager?->id,
                        'customer-success-manager'     => $company->customerSuccessManager?->id
                    ])
                ]
            );
            return;
        }

        $updated = InvoiceSnapshot::query()
            ->where(InvoiceSnapshot::FIELD_COMPANY_ID, $this->companyId)
            ->update([
                InvoiceSnapshot::FIELD_ACCOUNT_MANAGER_ID              => $company->accountManager?->id,
                InvoiceSnapshot::FIELD_BUSINESS_DEVELOPMENT_MANAGER_ID => $company->businessDevelopmentManager?->id,
                InvoiceSnapshot::FIELD_SUCCESS_MANAGER_ID              => $company->customerSuccessManager?->id,
            ]);

        BillingLogService::log(
            message: "$updated rows updated for company id $this->companyId",
            namespace: "invoice_snapshot_manager_update",
            context: [
                'company_id'    => $this->companyId,
                'updated_count' => $updated,
                'managers'      => collect([
                    'account-manager'              => $company->accountManager?->id,
                    'business-development-manager' => $company->businessDevelopmentManager?->id,
                    'customer-success-manager'     => $company->customerSuccessManager?->id
                ])
            ]
        );
    }
}
