<?php

namespace App\Jobs\Billing;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\DTO\Billing\CompanyPaymentProfile;
use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\PaymentMethodServices;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Odin\Company;
use App\Services\Billing\CompanyBillingServiceV4;
use App\Services\PaymentGateway\PaymentGatewayServiceFactory;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class ValidateCompanyPaymentMethodsExpiryJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param Collection<CompanyPaymentMethod> $companyPaymentMethods
     */
    public function __construct(protected Collection $companyPaymentMethods)
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        /** @var CompanyBillingServiceV4 $service */
        $service = app(CompanyBillingServiceV4::class);

        $this->companyPaymentMethods->each(fn(CompanyPaymentMethod $companyPaymentMethod) => $this->validateExpiry($companyPaymentMethod, $service));
    }

    /**
     * @param CompanyPaymentMethod $companyPaymentMethod
     * @param CompanyBillingServiceV4 $service
     *
     * @return void
     */
    protected function validateExpiry(CompanyPaymentMethod $companyPaymentMethod, CompanyBillingServiceV4 $service): void
    {
        try {
            $service->getAllCompanyStripePaymentMethods($companyPaymentMethod->company_id)->each(fn(PaymentMethodDTO $paymentMethod) => $this->notifyIfExpiredOrExpiringSoon($companyPaymentMethod, $paymentMethod));
        } catch (Exception $exception) {
            logger()->error("Failed to get payment method for company: $companyPaymentMethod->company_id. Error: {$exception->getMessage()}");
        }
    }

    /**
     * @param CompanyPaymentMethod $companyPaymentMethod
     * @param PaymentMethodDTO $paymentMethod
     *
     * @return void
     */
    protected function notifyIfExpiredOrExpiringSoon(CompanyPaymentMethod $companyPaymentMethod, PaymentMethodDTO $paymentMethod): void
    {
        $expiryDate = Carbon::create($paymentMethod->getExpiryFullYear(), $paymentMethod->getExpiryMonth());

        // Check if the payment method expired more than 3 months ago
        if ($expiryDate->copy()->endOfMonth()->lte(now()->subMonths(3))) {
            return;
        }

        // Check if the payment method has expired
        if ($expiryDate->copy()->endOfMonth()->isPast()) {
            $companyPaymentMethod->company->recalculateSystemStatus();
            $this->notify($companyPaymentMethod, $paymentMethod, true);
            return;
        }

        // Check if the payment method is expiring within the next month
        if ($expiryDate->copy()->endOfMonth()->lte(now()->addMonth()->endOfMonth())) {
            $this->notify($companyPaymentMethod, $paymentMethod);
        }
    }

    /**
     * @param CompanyPaymentMethod $companyPaymentMethod
     * @param PaymentMethodDTO $paymentMethod
     * @param bool $expired
     *
     * @return void
     */
    protected function notify(CompanyPaymentMethod $companyPaymentMethod, PaymentMethodDTO $paymentMethod, bool $expired = false): void
    {
        if ($expired) {
            $this->dispatchWorkflowEvents($companyPaymentMethod, EventName::CARD_EXPIRED);
        } else {
            $this->dispatchWorkflowEvents($companyPaymentMethod, EventName::CARD_EXPIRING_SOON);
        }

        NotifyCardExpiryJob::dispatch($companyPaymentMethod->company_id, $paymentMethod, $expired);
    }

    /**
     * @param CompanyPaymentMethod $companyPaymentMethod
     * @param EventName $eventName
     *
     * @return void
     */
    protected function dispatchWorkflowEvents(CompanyPaymentMethod $companyPaymentMethod, EventName $eventName): void
    {
        DispatchPubSubEvent::dispatch(EventCategory::BILLING, $eventName, [
            'company_id' => $companyPaymentMethod->company_id,
            'company_reference' => Company::query()->find($companyPaymentMethod->company_id)?->reference
        ]);
    }
}
