<?php

namespace App\Jobs\Billing;

use App\Models\Campaigns\CompanyCampaign;
use App\Repositories\Billing\BillingProfileRepository;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AttachCampaignToBillingProfile implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected int $campaignId)
    {
        $this->onQueue('billing');
    }

    /**
     * @throws Exception
     */
    public function handle(BillingProfileRepository $billingProfileRepository): void
    {
        $companyCampaign = CompanyCampaign::query()->findOrFail($this->campaignId);

        $billingProfile = $billingProfileRepository->getCompanyDefaultBillingProfile(
            companyId: $companyCampaign->{CompanyCampaign::FIELD_COMPANY_ID},
        );

        if (filled($billingProfile)) {
            $billingProfileRepository->attachCompanyCampaignToBillingProfile(
                billingProfile: $billingProfile,
                campaignId    : $companyCampaign->{CompanyCampaign::FIELD_ID}
            );
        }
    }
}
