<?php

namespace App\Jobs\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\DTO\Billing\MakeChangeRequestInvoice\PaymentMethodAttemptDTO;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\InvoicePaymentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;

class MakeChargeRequestInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    protected InvoiceAggregateRoot $invoiceAggregateRoot;

    /**
     * @param string $paymentUuid
     * @param string $invoiceUuid
     * @param float $total
     * @param string $authorType
     * @param int $billingProfileId
     * @param int $maxAttempts
     * @param string $date
     * @param int|null $authorId
     * @param Collection $paymentMethodsAttempts
     */
    public function __construct(
        protected string $paymentUuid,
        protected string $invoiceUuid,
        protected float $total,
        protected string $authorType,
        protected int $billingProfileId,
        protected int $maxAttempts,
        protected string $date,
        protected ?int $authorId = null,
        protected Collection $paymentMethodsAttempts = new Collection()
    )
    {
        $this->invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($this->invoiceUuid);

        $this->onQueue('billing');
    }

    /**
     * @return void
     * @throws Exception
     */
    public function validate(): void
    {
        /** @var InvoicePaymentService $invoicePaymentService */
        $invoicePaymentService = app()->make(InvoicePaymentService::class);

        $invoicePaymentService->validatePaymentForChargeAttempt(
            invoiceUuid       : $this->invoiceUuid,
            invoicePaymentUuid: $this->paymentUuid,
            total             : $this->total,
        );
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            $this->paymentMethodsAttempts = $this->mountPaymentMethodsAttempts();

            if ($this->paymentMethodsAttempts->isEmpty()) {
                $this->invoiceAggregateRoot->charge->invoicePaymentFailed(
                    invoicePaymentUuid: $this->paymentUuid,
                    invoiceUuid       : $this->invoiceUuid,
                    errorMessage      : 'No payment method found',
                    authorType        : $this->authorType,
                    amount            : $this->total,
                    authorId          : $this->authorId,
                );

                $this->invoiceAggregateRoot->charge->cancelInvoicePayment(
                    invoicePaymentUuid                       : $this->paymentUuid,
                    authorType                               : $this->authorType,
                    invoicePaymentAttemptNumber              : 0,
                    invoicePaymentMaxAttemptsPerPaymentMethod: $this->maxAttempts,
                    invoicePaymentTotal                      : $this->total,
                    authorId                                 : $this->authorId,
                );

                $this->invoiceAggregateRoot->persist();
                return;
            }

            /** @var Invoice $invoice * */
            $invoice = Invoice::findByUuid($this->invoiceUuid);

            try {
                $this->validate();
            } catch (Exception $exception) {
                logger()->error($exception);
                $this->invoiceAggregateRoot->charge->cancelInvoicePayment(
                    invoicePaymentUuid                       : $this->paymentUuid,
                    authorType                               : $this->authorType,
                    invoicePaymentAttemptNumber              : 0,
                    invoicePaymentMaxAttemptsPerPaymentMethod: $this->maxAttempts,
                    invoicePaymentTotal                      : $this->total,
                    authorId                                 : $this->authorId,
                );

                $this->invoiceAggregateRoot->persist();
                return;
            }

            $paymentSuccessful = $this->iterateOverCardsAttemptingPayment($invoice);

            $shouldSchedule = $this->doesStillHaveAttempts();

            if (!$paymentSuccessful && !$shouldSchedule) {
                $this->invoiceAggregateRoot->charge->invoicePaymentFailed(
                    invoicePaymentUuid: $this->paymentUuid,
                    invoiceUuid       : $this->invoiceUuid,
                    errorMessage      : 'Max attempts exceeded in all payment methods',
                    authorType        : $this->authorType,
                    amount            : $this->total,
                    authorId          : $this->authorId,
                );
            } else if (!$paymentSuccessful && $shouldSchedule) {
                $this->rescheduleAttempt();
            }
        } catch (Exception $exception) {
            BillingLogService::logException($exception);
        }

        $this->invoiceAggregateRoot->persist();
    }

    /**
     * @param string $message
     * @param array $context
     * @param string|null $trace
     * @return void
     */
    protected function log(
        string $message,
        array $context = [],
        string $trace = null
    ): void
    {
        BillingLogService::log(
            message    : $message,
            namespace  : self::class,
            relatedType: Invoice::class,
            relatedId  : $this->invoiceUuid,
            context    : array_merge(get_object_vars($this), $context),
            trace      : $trace,
        );
    }

    /**
     * @param int|BillingProfile $billingProfile
     * @return Collection<CompanyPaymentMethod>
     */
    protected function getPaymentMethodsByBillingProfile(int|BillingProfile $billingProfile): Collection
    {
        if (is_int($billingProfile)) {
            $billingProfile = BillingProfile::query()->findOrFail($billingProfile);
        }

        $paymentMethodIds = $this->paymentMethodsAttempts
            ->map(
                fn(PaymentMethodAttemptDTO $paymentMethodAttemptDTO) => $paymentMethodAttemptDTO->getCompanyPaymentMethodId()
            );

        $paymentMethodsQuery = CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $billingProfile->{BillingProfile::FIELD_COMPANY_ID})
            ->where(CompanyPaymentMethod::FIELD_TYPE, $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD})
            ->whereNotIn(CompanyPaymentMethod::FIELD_ID, $paymentMethodIds);

        // If billing profile has a preferred payment method we will not try other credit cards
        if ($billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD_ID}) {
            $paymentMethodsQuery
                ->orderByRaw("id = ? DESC", [$billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD_ID}])
                ->limit(1);
        } else {
            $paymentMethodsQuery->orderByDesc(CompanyPaymentMethod::FIELD_IS_DEFAULT);
        }

        return $paymentMethodsQuery->get();
    }

    /**
     * @param Invoice $invoice
     * @param string $invoicePaymentChargeUuid
     * @param CompanyPaymentMethod $paymentMethod
     * @param int $atomicAmount Amount in cents
     * @param string $attemptedAt
     * @return bool
     */
    function attemptPayment(
        Invoice $invoice,
        string $invoicePaymentChargeUuid,
        CompanyPaymentMethod $paymentMethod,
        int $atomicAmount,
        string $attemptedAt
    ): bool
    {
        $this->invoiceAggregateRoot->charge->invoiceChargeRequestAttempt(
            invoicePaymentUuid      : $this->paymentUuid,
            invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
            invoiceUuid             : $this->invoiceUuid,
            authorType              : $this->authorType,
            paymentMethodType       : $paymentMethod->type,
            paymentMethodId         : $paymentMethod->id,
            amount                  : $atomicAmount,
            attemptedAt             : $attemptedAt,
            authorId                : $this->authorId,
        );

        $description = 'Invoice ' . $invoice->{Invoice::FIELD_ID};

        return $paymentMethod->getPaymentGatewayService()->makeChargeRequest(
            invoicePaymentChargeUuid: $invoicePaymentChargeUuid,
            invoiceUuid             : $this->invoiceUuid,
            total                   : $atomicAmount,
            metadata                : [
                'companyId' => $invoice->{Invoice::FIELD_COMPANY_ID},
                'date'      => $this->date
            ],
            description             : $description,
            customerCode            : $paymentMethod->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_CLIENT_CODE},
            paymentMethod           : $paymentMethod->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE}
        );
    }

    /**
     * @return bool
     */
    public function doesStillHaveAttempts(): bool
    {
        $payment = InvoicePayment::findByUuid($this->paymentUuid);

        return $payment->{InvoicePayment::FIELD_ATTEMPT_NUMBER} < $this->maxAttempts;
    }

    /**
     * @return void
     */
    public function rescheduleAttempt(): void
    {
        $rescheduledDate = now()->addDay()->toISOString();

        $this->invoiceAggregateRoot->charge->scheduleInvoiceCharge(
            uuid                  : $this->paymentUuid,
            invoiceUuid           : $this->invoiceUuid,
            total                 : $this->total,
            maxAttempts           : $this->maxAttempts,
            billingProfileId      : $this->billingProfileId,
            authorType            : InvoiceEventAuthorTypes::SYSTEM->value,
            attemptChargeDate     : $rescheduledDate,
            paymentMethodsAttempts: $this->paymentMethodsAttempts
        );
    }

    /**
     * @param Invoice $invoice
     * @return bool
     */
    public function iterateOverCardsAttemptingPayment(Invoice $invoice): bool
    {
        $paymentSuccessful = false;

        /** @var PaymentMethodAttemptDTO $paymentMethodAttempt */
        foreach ($this->paymentMethodsAttempts as $paymentMethodAttempt) {
            $chargeUuidInvoicePaymentChargeUuid = Str::uuid()->toString();

            try {
                BillingLogService::log(
                    message: 'Attempting payment for invoice uuid ' . $this->invoiceUuid,
                    context: [
                        'billingProfileId'         => $this->billingProfileId,
                        'paymentMethodsAttempts'   => $this->paymentMethodsAttempts,
                        'attemptsPerCard'          => $this->maxAttempts,
                        'invoicePaymentChargeUuid' => $chargeUuidInvoicePaymentChargeUuid,
                        'paymentMethodId'          => $paymentMethodAttempt->getCompanyPaymentMethodId(),
                    ]
                );

                $paymentMethodAttempt->decrementAttempt();

                $paymentSuccessful = $this->attemptPayment(
                    invoice                 : $invoice,
                    invoicePaymentChargeUuid: $chargeUuidInvoicePaymentChargeUuid,
                    paymentMethod           : $paymentMethodAttempt->getCompanyPaymentMethod(),
                    atomicAmount            : $this->total,
                    attemptedAt             : $this->date,
                );

                if ($paymentSuccessful) {
                    $this->invoiceAggregateRoot->charge->invoiceChargeRequestSuccess(
                        invoicePaymentChargeUuid: $chargeUuidInvoicePaymentChargeUuid,
                        invoicePaymentUuid      : $this->paymentUuid,
                        invoiceUuid             : $this->invoiceUuid,
                        authorType              : $this->authorType,
                        paymentMethodType       : $paymentMethodAttempt->getCompanyPaymentMethod()->{CompanyPaymentMethod::FIELD_TYPE},
                        paymentMethodId         : $paymentMethodAttempt->getCompanyPaymentMethod()->{CompanyPaymentMethod::FIELD_ID},
                        amount                  : $this->total,
                        requestedAt             : $this->date,
                        authorId                : $this->authorId,
                    );

                    break;
                }
            } catch (Exception $exception) {
                $this->handlePaymentChargeAttemptError(
                    chargeUuidInvoicePaymentChargeUuid: $chargeUuidInvoicePaymentChargeUuid,
                    exception                         : $exception,
                    paymentMethodAttempt              : $paymentMethodAttempt,
                    failedAt                          : $this->date,
                );
            }

            if (!$paymentSuccessful && $paymentMethodAttempt->getAttempts() === 0) {
                $this->invoiceAggregateRoot->charge->invoiceChargeRequestMaxAttemptsExceeded(
                    invoicePaymentChargeUuid: $chargeUuidInvoicePaymentChargeUuid,
                    invoicePaymentUuid      : $this->paymentUuid,
                    invoiceUuid             : $this->invoiceUuid,
                    paymentMethodType       : $paymentMethodAttempt->getCompanyPaymentMethod()->{CompanyPaymentMethod::FIELD_TYPE},
                    paymentMethodId         : $paymentMethodAttempt->getCompanyPaymentMethod()->{CompanyPaymentMethod::FIELD_ID},
                    amount                  : $this->total,
                    exceededAt              : $this->date
                );
            }
        }

        return $paymentSuccessful;
    }

    /**
     * @param string $chargeUuidInvoicePaymentChargeUuid
     * @param Exception $exception
     * @param PaymentMethodAttemptDTO $paymentMethodAttempt
     * @param string $failedAt
     * @return void
     */
    public function handlePaymentChargeAttemptError(
        string $chargeUuidInvoicePaymentChargeUuid,
        Exception $exception,
        PaymentMethodAttemptDTO $paymentMethodAttempt,
        string $failedAt
    ): void
    {
        $this->log(
            message: $exception->getMessage(),
            context: [
                '$failedAt' => $failedAt
            ],
            trace  : $exception->getTraceAsString()
        );

        $this->invoiceAggregateRoot->charge->invoiceChargeRequestFailed(
            invoicePaymentChargeUuid: $chargeUuidInvoicePaymentChargeUuid,
            invoicePaymentUuid      : $this->paymentUuid,
            invoiceUuid             : $this->invoiceUuid,
            errorMessage            : $exception->getMessage(),
            errorCode               : $exception->getCode(),
            authorType              : $this->authorType,
            paymentMethodType       : $paymentMethodAttempt->getCompanyPaymentMethod()->{CompanyPaymentMethod::FIELD_TYPE},
            paymentMethodId         : $paymentMethodAttempt->getCompanyPaymentMethod()->{CompanyPaymentMethod::FIELD_ID},
            amount                  : $this->total,
            failedAt                : $failedAt,
            authorId                : $this->authorId
        );
    }

    /**
     * @return Collection
     */
    public function mountPaymentMethodsAttempts(): Collection
    {
        $paymentMethods = $this->getPaymentMethodsByBillingProfile($this->billingProfileId);

        // No payment methods found
        if ($paymentMethods->isEmpty() && $this->paymentMethodsAttempts->count() === 0) {
            $this->invoiceAggregateRoot->charge->invoicePaymentFailed(
                invoicePaymentUuid: $this->paymentUuid,
                invoiceUuid       : $this->invoiceUuid,
                errorMessage      : 'No payment methods available',
                authorType        : $this->authorType,
                amount            : $this->total,
                authorId          : $this->authorId,
            );
        }

        // The payment methods will carry the billing profile max attempts or the min remaining
        $maxAttempts = $this->paymentMethodsAttempts->min(function (PaymentMethodAttemptDTO $paymentMethodAttempt) {
            return $paymentMethodAttempt->getAttempts();
        }) ?? $this->maxAttempts;

        return $this->paymentMethodsAttempts->merge(
            $paymentMethods->map(fn(CompanyPaymentMethod $paymentMethod) => new PaymentMethodAttemptDTO(
                attempts              : $maxAttempts,
                companyPaymentMethodId: $paymentMethod->{CompanyPaymentMethod::FIELD_ID},
                companyPaymentMethod  : $paymentMethod
            )))
            ->filter(function (PaymentMethodAttemptDTO $paymentMethodAttempt) {
                return $paymentMethodAttempt->getCompanyPaymentMethod();
            });
    }
}
