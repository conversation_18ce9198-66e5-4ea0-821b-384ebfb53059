<?php

namespace App\Jobs\Billing;

use App\DTO\Billing\InvoicePayload;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceItemTypes;
use App\Enums\Billing\InvoiceStates;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CampaignBillingProfile;
use App\Models\Campaigns\CompanyCampaign;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\CompanyInvoiceService;
use App\Services\Billing\InvoiceService;
use Exception;
use Illuminate\Support\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateUninvoicedProductAssignmentsInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected int $billingProfileId,
        protected ?string $reason = null
    )
    {
        $this->onQueue('billing');
    }

    /**
     * @param CompanyInvoiceService $companyInvoiceService
     * @param InvoiceService $invoiceService
     * @return void
     * @throws Exception
     */
    public function handle(
        CompanyInvoiceService $companyInvoiceService,
        InvoiceService $invoiceService
    ): void
    {
        $billingProfile = BillingProfile::query()->findOrFail($this->billingProfileId);

        $company = $billingProfile->company;

        // Should we pass the date reference ?
        if ($billingProfile->{BillingProfile::FIELD_BILLING_FREQUENCY_CRON} === 'monthly') {
            $deliveredBefore = Carbon::now()->startOfMonth()->startOfDay();
        } else {
            $deliveredBefore = Carbon::now()->startOfDay();
        }

        $campaignIds = CampaignBillingProfile::query()
            ->where(CampaignBillingProfile::FIELD_BILLING_PROFILE_ID, $this->billingProfileId)
            ->get()
            ->pluck(CampaignBillingProfile::FIELD_CAMPAIGN_ID)
            ->toArray();

        $invoiceItems = $companyInvoiceService->getUninvoicedInvoiceItemsForCompany(
            company        : $company,
            deliveredBefore: $deliveredBefore,
            campaignIds    : $campaignIds
        );

        if ($invoiceItems->isEmpty()) {
            BillingLogService::log(
                message    : "No uninvoiced products found for company $billingProfile?->company?->name billing profile id $billingProfile?->id",
                namespace  : 'generate_uninvoiced_product_assignments_invoice_job',
                relatedType: BillingProfile::class,
                relatedId  : $billingProfile?->id,
                context    : [
                    "billing_profile"  => $billingProfile,
                    "delivered_before" => $deliveredBefore
                ]
            );
            return;
        }

        // Set the type instead of item class to reuse function
        foreach ($invoiceItems as $invoiceItem) {
            $invoiceItem->setBillableType(InvoiceItemTypes::fromClass($invoiceItem->getBillableType())->value);
        }

        $tags = ['system_generated'];

        if (!empty($this->reason)) {
            $tags[] = $this->reason;
        }

        $issueDate = now();

        $invoicePayload = InvoicePayload::fromArray([
            InvoicePayload::FIELD_COMPANY_ID         => $company->id,
            InvoicePayload::FIELD_ITEMS              => $invoiceItems->toArray(),
            InvoicePayload::FIELD_DUE_DATE           => $issueDate->clone()->addDays($billingProfile->due_in_days),
            InvoicePayload::FIELD_ISSUE_DATE         => $issueDate,
            InvoicePayload::FIELD_NOTE               => null,
            InvoicePayload::FIELD_STATUS             => InvoiceStates::ISSUED->value,
            InvoicePayload::FIELD_TAGS               => collect($tags),
            InvoicePayload::FIELD_BILLING_PROFILE_ID => $billingProfile->id
        ]);

        BillingLogService::log(
            message  : 'Creating invoice automatically',
            namespace: 'generate_uninvoiced_product_assignments_invoice_job',
            context  : [
                "invoice_data" => $invoicePayload->toArray()
            ]
        );

        $invoiceService->createUpdateInvoice(
            invoicePayload: $invoicePayload,
            authorType    : InvoiceEventAuthorTypes::SYSTEM
        );

        $billingProfile->update([
            BillingProfile::FIELD_LAST_BILLED_AT => now()
        ]);
    }
}
