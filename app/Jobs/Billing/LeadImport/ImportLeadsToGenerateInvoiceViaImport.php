<?php

namespace App\Jobs\Billing\LeadImport;

use App\DTO\Billing\InvoicePayload;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceItemTypes;
use App\Enums\Billing\InvoiceStates;
use App\Models\Odin\ProductAssignment;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\InvoiceService;
use App\Services\Odin\ProductAssignmentService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class ImportLeadsToGenerateInvoiceViaImport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        protected array $deliveredLeads,
        protected int $companyId,
        protected int $userId,
    )
    {
        $this->onQueue('billing');
    }

    /**
     * @param ProductAssignmentService $productAssignmentService
     * @param InvoiceService $invoiceService
     * @return void
     * @throws Exception
     */
    public function handle(
        ProductAssignmentService $productAssignmentService,
        InvoiceService $invoiceService
    ): void
    {
        $deliveredLeadsCollection = collect($this->deliveredLeads);

        $deliveredProductAssigmentIds = $deliveredLeadsCollection
            ->map(fn($item) => Arr::get($item, 'product_assignment_id'));

        // Return only products which their prices are higher in the csv
        $discrepantProductAssignments = $deliveredLeadsCollection
            ->filter(fn(array $item) => Arr::get($item, 'abs_discrepancy') > 0);

        if (filled($discrepantProductAssignments)) {
            $this->updateProductAssignments(
                discrepantProductAssignments: $discrepantProductAssignments,
            );
        }

        $productAssignments = ProductAssignment::query()
            ->where(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID, $this->companyId)
            ->whereIn(ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID, $deliveredProductAssigmentIds)
            ->get();

        $invoiceItems = $productAssignmentService->transformToInvoiceItems(
            productAssignments: $productAssignments
        );

        // Set the type instead of item class to reuse function
        foreach ($invoiceItems as $invoiceItem) {
            $invoiceItem->setBillableType(InvoiceItemTypes::fromClass($invoiceItem->getBillableType())->value);
        }

        $invoicePayload = new InvoicePayload(
            companyId: $this->companyId,
            dueDate  : now(),
            issueDate: now(),
            status   : InvoiceStates::ISSUED->value,
            items    : $invoiceItems,
            tags     : collect([
                'generated_via_csv'
            ]),
        );

        $invoiceService->createUpdateInvoice(
            invoicePayload: $invoicePayload,
            authorType    : InvoiceEventAuthorTypes::USER,
            authorId      : $this->userId
        );
    }

    /**
     * @param Collection $discrepantProductAssignments
     * @return void
     * @throws BindingResolutionException
     */
    public function updateProductAssignments(Collection $discrepantProductAssignments): void
    {
        /** @var ProductAssignmentRepository $productAssignmentRepository */
        $productAssignmentRepository = app()->make(ProductAssignmentRepository::class);
        foreach ($discrepantProductAssignments as $assignment) {
            [
                'billable_price'        => $billablePrice,
                'product_assignment_id' => $productAssignmentId,
            ] = $assignment;

            try {
                $productAssignment = ProductAssignment::query()->findOrFail($productAssignmentId);

                if ($productAssignment->{ProductAssignment::FIELD_COST} == $billablePrice) {
                    continue;
                }

                $oldCost = $productAssignment->{ProductAssignment::FIELD_COST};
                $productAssignmentRepository->updateProductAssignment(
                    productAssignment: $productAssignment,
                    data             : [
                        ProductAssignment::FIELD_COST => $billablePrice
                    ],
                );
                BillingLogService::log(
                    message  : 'Product assignment cost updated',
                    namespace: 'import_leads_to_generate_invoice',
                    context  : [
                        'product_assignment_id' => $productAssignmentId,
                        'old_cost'              => $oldCost,
                        'new_cost'              => $billablePrice,
                        'assignment'            => $assignment,
                    ]
                );
            } catch (Exception $exception) {
                BillingLogService::logException(
                    exception: $exception,
                    namespace: 'import_leads_to_generate_invoice',
                    context  : [
                        'assignment' => $assignment,
                    ]
                );
            }
        }
    }
}
