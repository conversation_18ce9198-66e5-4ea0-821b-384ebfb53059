<?php

namespace App\Jobs\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Exceptions\Billing\InvoicePaymentOverchargeException;
use App\Models\Billing\InvoicePayment;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

class ScheduleChargeRequestInvoiceJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public function __construct(
        protected string $paymentUuid,
        protected string $invoiceUuid,
        protected float $total,
        protected string $authorType,
        protected int $billingProfileId,
        protected int $maxAttempts,
        protected ?int $authorId = null,
        protected Collection $paymentMethodsAttempts = new Collection()
    )
    {
        $this->onQueue('billing');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $shouldStopCharge = $this->checkIfShouldStopCharge();

            if ($shouldStopCharge) {
                BillingLogService::log(
                    message: 'Invoice payment was canceled. Exiting job successfully',
                    context: [
                        'payment_uuid' => $this->paymentUuid,
                    ]
                );
                return;
            }

            $this->requestInvoiceCharge();
        } catch (Exception $exception) {
            BillingLogService::logException($exception);
        }
    }

    /**
     * @return bool
     * @throws Exception
     */
    public function checkIfShouldStopCharge(): bool
    {
        /** @var InvoicePayment $invoicePayment */
        $invoicePayment = InvoicePayment::findByUuid($this->paymentUuid);

        if (!$invoicePayment) {
            throw new Exception('Payment not found');
        }

        return $invoicePayment->isCancelled();
    }

    /**
     * @throws BindingResolutionException
     */
    public function requestInvoiceCharge(): void
    {
        $root = InvoiceAggregateRoot::retrieve($this->invoiceUuid);

        try {
            $root->charge->requestInvoiceCharge(
                uuid                  : $this->paymentUuid,
                invoiceUuid           : $this->invoiceUuid,
                total                 : $this->total,
                maxAttempts           : $this->maxAttempts,
                billingProfileId      : $this->billingProfileId,
                authorType            : $this->authorType,
                authorId              : $this->authorId,
                paymentMethodsAttempts: $this->paymentMethodsAttempts
            );
        } catch (Exception $exception) {
            $paymentMethodAttempt = $this->paymentMethodsAttempts->first();

            $attemptNumber = Arr::get($paymentMethodAttempt, 'attempts');

            $root->charge->cancelInvoicePayment(
                invoicePaymentUuid                       : $this->paymentUuid,
                authorType                               : InvoiceEventAuthorTypes::SYSTEM->value,
                invoicePaymentAttemptNumber              : $attemptNumber ?? 0,
                invoicePaymentMaxAttemptsPerPaymentMethod: $this->maxAttempts,
                invoicePaymentTotal                      : $this->total,
            );

            BillingLogService::logException(
                exception: $exception,
                namespace: 'schedule_charge_request',
            );
        }

        $root->persist();

    }
}
