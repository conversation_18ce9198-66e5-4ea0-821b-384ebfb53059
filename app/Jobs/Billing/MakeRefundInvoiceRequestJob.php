<?php

namespace App\Jobs\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Contracts\Services\PaymentGatewayServiceContract;
use App\Enums\Billing\BillingLogLevel;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePaymentCharge;
use App\Models\Billing\InvoiceRefundCharge;
use App\Models\Billing\InvoiceTransaction;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class MakeRefundInvoiceRequestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    public function __construct(
        protected string $invoiceUuid,
        protected string $invoiceRefundChargeUuid,
        protected float $amount,
        protected string $chargeId,
        protected string $authorType,
        protected int $billingProfileId,
        protected ?int $authorId = null,
        protected int $maxAttempts = 1,
        protected int $attempts = 1,
    )
    {
        $this->onQueue('billing');
    }

    /**
     * @param string $message
     * @param BillingLogLevel $level
     * @param array $context
     * @param string|null $trace
     * @return void
     */
    protected function log(
        string $message,
        BillingLogLevel $level = BillingLogLevel::INFO,
        array $context = [],
        string $trace = null
    ): void
    {
        BillingLogService::log(
            message    : $message,
            level      : $level,
            namespace  : self::class,
            relatedType: Invoice::class,
            relatedId  : $this->invoiceUuid,
            context    : array_merge(get_object_vars($this), $context),
            trace      : $trace,
        );
    }

    /**
     * @return mixed
     */
    public function getPaymentGatewayService(): PaymentGatewayServiceContract
    {
        $invoiceRefundCharge = InvoiceRefundCharge::query()
            ->where(InvoiceRefundCharge::FIELD_UUID, $this->invoiceRefundChargeUuid)
            ->first();

        $invoiceRefundedPaymentTransaction = $invoiceRefundCharge->refundedPayment;

        $invoicePaymentCharge = InvoicePaymentCharge::query()->where(
            InvoicePaymentCharge::FIELD_TRANSACTION_UUID,
            $invoiceRefundedPaymentTransaction->{InvoiceTransaction::FIELD_UUID}
        )->first();

        $paymentMethod = $invoicePaymentCharge->paymentMethod;

        return $paymentMethod->getPaymentGatewayService();
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(): void
    {
        $paymentGatewayService = $this->getPaymentGatewayService();

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($this->invoiceUuid);

        if ($this->attempts > $this->maxAttempts) {
            $invoiceAggregateRoot->refund->refundInvoiceRequestMaxAttemptsExceeded($this->invoiceRefundChargeUuid);
            $invoiceAggregateRoot->persist();

            $this->log(
                message: 'Invoice refund request max attempts reached',
            );

            return;
        }

        try {
            $invoiceAggregateRoot->refund->refundInvoiceRequestAttempt($this->invoiceRefundChargeUuid);

            $this->log(
                message: 'Requesting a refund of ' . $this->amount . ' for charge id ' . $this->chargeId,
            );

            $status = $paymentGatewayService->makeRefundRequest(
                invoiceRefundChargeUuid: $this->invoiceRefundChargeUuid,
                total                  : $this->amount,
                chargeId               : $this->chargeId,
                invoiceUuid            : $this->invoiceUuid,
            );

            if ($status) {
                $invoiceAggregateRoot->refund->refundInvoiceRequestSuccess(
                    refundUuid: $this->invoiceRefundChargeUuid,
                );
            } else {
                throw new Exception("Invoice Refund Request Unsuccessful");
            }
        } catch (Exception $exception) {
            $this->log(
                message: $exception->getMessage(),
                trace  : $exception->getTraceAsString()
            );

            $invoiceAggregateRoot->refund->refundInvoiceRequestFailed($this->invoiceRefundChargeUuid, $exception->getMessage());

            MakeRefundInvoiceRequestJob::dispatch(
                $this->invoiceUuid,
                $this->invoiceRefundChargeUuid,
                $this->amount,
                $this->chargeId,
                $this->authorType,
                $this->billingProfileId,
                $this->authorId,
                $this->maxAttempts,
                $this->attempts + 1,
            );
        }

        $invoiceAggregateRoot->persist();
    }
}
