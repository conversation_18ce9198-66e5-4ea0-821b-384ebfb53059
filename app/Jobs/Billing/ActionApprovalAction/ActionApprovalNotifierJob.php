<?php

namespace App\Jobs\Billing\ActionApprovalAction;

use App\Enums\Billing\ApprovalStatus;
use App\Enums\Billing\InvoiceEvents;
use App\Enums\PermissionType;
use App\Models\Billing\ActionApproval;
use App\Services\Billing\Notification\BillingEmailNotificationService;
use App\Services\Billing\Notification\BillingInternalNotificationService;
use App\Services\Billing\Notification\BillingNotificationContract;
use App\Services\Odin\UserService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class ActionApprovalNotifierJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const string TYPE_NOTIFY_REQUESTER = 'notify_requester';
    const string TYPE_NOTIFY_REVIEWERS = 'notify_reviewers';
    protected Collection $notifiables;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected string $actionApprovalUuid,
        protected string $type,
    )
    {
        $this->onQueue('billing');

        $this->notifiables = collect([
            BillingEmailNotificationService::class,
            BillingInternalNotificationService::class,
        ]);
    }


    /**
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        /** @var ActionApproval $approvalAction */
        $approvalAction = ActionApproval::findByUuid($this->actionApprovalUuid);

        if (!$approvalAction) {
            throw new Exception('Action approval not found for uuid ' . $this->actionApprovalUuid);
        }

        foreach ($this->notifiables as $notifiable) {
            /** @var BillingNotificationContract $notifiable */
            $notifiable = app($notifiable);

            $actionType = match ($this->type) {
                self::TYPE_NOTIFY_REVIEWERS => InvoiceEvents::INVOICE_ACTION_REQUESTED,
                self::TYPE_NOTIFY_REQUESTER => InvoiceEvents::INVOICE_ACTION_REQUEST_REVIEWED,
                default                     => throw new Exception('Unknown type ' . $this->type)
            };

            $recipients = $this->getRecipients(approval: $approvalAction);

            $notifiable->notify(
                recipients: $recipients,
                actionApproval: $approvalAction,
                subject: $this->getSubject(approval: $approvalAction, type: $actionType),
                message: $this->getMessage(approval: $approvalAction, type: $actionType),
                eventType: $actionType,
            );
        }
    }

    public function getRecipients(ActionApproval $approval): Collection
    {
        return match ($this->type) {
            self::TYPE_NOTIFY_REVIEWERS => app(UserService::class)->getUsersByRolePermissions([PermissionType::BILLING_ACTION_APPROVALS_REVIEW]),
            self::TYPE_NOTIFY_REQUESTER => collect([$approval->requestedBy])
        };
    }

    public function getSubject(ActionApproval $approval, InvoiceEvents $type): string
    {
        $action = $approval->requested_action->getTitle();

        return match ($type) {
            InvoiceEvents::INVOICE_ACTION_REQUESTED        => "$action Action has been requested",
            InvoiceEvents::INVOICE_ACTION_REQUEST_REVIEWED => "$action Action has been reviewed",
        };
    }

    public function getMessage(ActionApproval $approval, InvoiceEvents $type): string
    {
        $requester = $approval->requestedBy;
        $reviewer = $approval->reviewedBy;
        $requestedAction = $approval->requested_action;
        $action = Str::lower($requestedAction->getTitle());
        $entity = $requestedAction->getRelatedNotificationString();
        $entityId = $approval->approvable_id;
        $outcome = $approval->status === ApprovalStatus::APPROVED ? 'approved' : 'denied';

        return match ($type) {
            InvoiceEvents::INVOICE_ACTION_REQUESTED        => "$requester->name has requested to $action for $entity ($entityId)",
            InvoiceEvents::INVOICE_ACTION_REQUEST_REVIEWED => "$reviewer->name has $outcome the requested $action for $entity ($entityId)",
        };
    }
}
