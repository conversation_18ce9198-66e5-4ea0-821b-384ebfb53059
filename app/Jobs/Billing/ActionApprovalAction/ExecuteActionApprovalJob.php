<?php

namespace App\Jobs\Billing\ActionApprovalAction;

use App\Enums\Billing\ApprovalStatus;
use App\Models\Billing\ActionApproval;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\ReviewableAction\ReviewableAction;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class ExecuteActionApprovalJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected string $actionApprovalUuid)
    {
        $this->onQueue('billing');
    }

    /**
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function handle(): void
    {
        /** @var ?ActionApproval $actionApproval */
        $actionApproval = ActionApproval::findByUuid($this->actionApprovalUuid);

        if (empty($actionApproval)) {
            throw new Exception('Action approval not found for uuid ' . $this->actionApprovalUuid);
        }

        $actionApproval->update([
            ActionApproval::FIELD_IS_PROCESSING => true
        ]);

        /** @var ReviewableAction $action */
        $action = app()->make($actionApproval->requested_action->getActionClass());

        try {
            switch ($actionApproval->status) {
                case ApprovalStatus::APPROVED;
                    $action->onApproval($actionApproval->getActionArguments());
                    break;
                case ApprovalStatus::REJECTED;
                    $action->onRefusal($actionApproval->getActionArguments());
                    break;
                case ApprovalStatus::PENDING:
                    return;
            }
        } catch (Throwable $exception) {
            $action->onError($exception);
            BillingLogService::logException(
                exception  : $exception,
                namespace  : 'execute_action_approval_job',
                relatedType: $actionApproval::class, relatedId: $actionApproval->id
            );
        }

        $actionApproval->update([
            ActionApproval::FIELD_IS_PROCESSING => false
        ]);

        ActionApprovalNotifierJob::dispatch(
            $this->actionApprovalUuid,
            ActionApprovalNotifierJob::TYPE_NOTIFY_REQUESTER
        );
    }
}
