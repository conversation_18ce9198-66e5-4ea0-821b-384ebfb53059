<?php

namespace App\Jobs\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Exceptions\Billing\PdfGenerationFailedException;
use App\Models\Billing\Invoice;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\InvoicePdfService\InvoicePdfService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateInvoicePdfJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int RETRY_PDF_GENERATION_DELAY_IN_SECONDS = 10 * 60;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected Invoice $invoice,
        protected bool $shouldPersistEvent = true,
        protected bool $retry = true
    )
    {
        $this->onQueue('billing');
    }

    /**
     * @throws Exception
     */
    public function handle(
        InvoicePdfService $invoicePdfService,
    ): void
    {
        $root = InvoiceAggregateRoot::retrieve($this->invoice->{Invoice::FIELD_UUID});

        // Exit job if invoice has pdf
        if ($this->invoice->invoice_url && $this->retry) {
            return;
        }

        try {
            $filepath = $invoicePdfService->createPdf($this->invoice);

            if (!$this->shouldPersistEvent) {
                $this->invoice->update([
                    Invoice::FIELD_INVOICE_URL => $filepath,
                    Invoice::FIELD_PDF_FAILED  => false,
                ]);
            }

            $root->pdf->invoicePdfCreated(
                invoiceUuid: $this->invoice->{Invoice::FIELD_UUID},
                filepath   : $filepath
            );
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception  : $exception,
                namespace  : 'create_invoice_pdf',
                relatedType: Invoice::class,
                relatedId  : $this->invoice->id,
            );

            $root->pdf->invoicePdfFailed(
                invoiceUuid : $this->invoice->{Invoice::FIELD_UUID},
                errorMessage: $exception->getMessage()
            );

            if ($exception instanceof PdfGenerationFailedException && $this->retry) {
                CreateInvoicePdfJob::dispatch(
                    $this->invoice,
                    $this->shouldPersistEvent,
                    false
                )->delay(self::RETRY_PDF_GENERATION_DELAY_IN_SECONDS);
            }
        }

        if ($this->shouldPersistEvent) {
            $root->persist();
        }
    }
}
