<?php

namespace App\Jobs\Billing;

use App\Models\Billing\BillingProfile;
use App\Repositories\Billing\BillingProfileRepository;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\BillingProfile\BillingProfileFrequencyService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculateEligibleProfilesToBillJobByFrequency implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected BillingProfileFrequencyService $billingProfileFrequencyService;

    const int CHUNK_SIZE = 1000;

    public function __construct()
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * @param BillingProfileRepository $billingProfileRepository
     * @param BillingProfileFrequencyService $billingProfileFrequencyService
     * @return void
     */
    public function handle(
        BillingProfileRepository $billingProfileRepository,
        BillingProfileFrequencyService $billingProfileFrequencyService
    ): void
    {
        $this->billingProfileFrequencyService = $billingProfileFrequencyService;

        BillingProfile::active()
            ->select(BillingProfile::TABLE . '.*')
            ->joinSub(
                $billingProfileRepository->getTotalChargeableUninvoicedLeadsGroupedByBillingProfile(
                    greaterOrEqualValue: 1
                ),
                'sub',
                'sub.billing_profile_id',
                '=',
                BillingProfile::TABLE . '.' . BillingProfile::FIELD_ID
            )
            ->chunk(self::CHUNK_SIZE, function ($billingProfiles) {
                foreach ($billingProfiles as $billingProfile) {
                    try {
                        if ($this->billingProfileFrequencyService->isDue($billingProfile)) {
                            BillingLogService::log(
                                message    : 'Dispatching job to create invoice for billing profile',
                                namespace  : 'calculate_eligible_profiles_to_bill_by_frequency',
                                relatedType: BillingProfile::class,
                                relatedId  : $billingProfile->id,
                            );
                            GenerateUninvoicedProductAssignmentsInvoiceJob::dispatchSync(
                                $billingProfile->id,
                                'frequency_reached'
                            );
                        }
                    } catch (Exception $exception) {
                        BillingLogService::logException(
                            exception  : $exception,
                            namespace  : 'calculate_eligible_profiles_to_bill_by_frequency',
                            relatedType: BillingProfile::class,
                            relatedId  : $billingProfile->id,
                        );
                    }
                }
            });
    }
}
