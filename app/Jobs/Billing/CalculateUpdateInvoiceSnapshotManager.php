<?php

namespace App\Jobs\Billing;

use App\Models\Billing\InvoiceSnapshot;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class CalculateUpdateInvoiceSnapshotManager implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int BATCH_SIZE = 100;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->onQueue('long_running');
    }

    /**
     * @throws Exception
     */
    public function handle(): void
    {
        InvoiceSnapshot::query()
            ->select(InvoiceSnapshot::FIELD_COMPANY_ID)
            ->distinct()
            ->chunk(self::BATCH_SIZE, function (Collection $snapshots) {
                foreach ($snapshots as $snapshot) {
                    UpdateInvoiceSnapshotManager::dispatch($snapshot->{InvoiceSnapshot::FIELD_COMPANY_ID});
                }
            });
    }
}
