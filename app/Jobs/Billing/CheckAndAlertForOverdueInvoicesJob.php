<?php

namespace App\Jobs\Billing;

use App\Aggregates\InvoiceAggregateRoot;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CreditType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Bundle;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class CheckAndAlertForOverdueInvoicesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    const string LOG_NAMESPACE = 'suspend_company_for_unpaid_invoices';
    const int DAYS_UNTIL_SUSPENSION = 3;
    public function __construct()
    {
        $this->onQueue('billing');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $dueUnpaidInvoices = $this->getDueUnpaidInvoices();

        if ($dueUnpaidInvoices->isEmpty()) {
            BillingLogService::log(
                message  : 'No due unpaid invoices found',
                namespace: self::LOG_NAMESPACE,
            );

            return;
        }

        $this->dispatchEvents(
            dueUnpaidInvoices: $dueUnpaidInvoices
        );
    }


    /**
     * @param Collection<Invoice> $dueUnpaidInvoices
     * @return void
     */
    protected function dispatchEvents(Collection $dueUnpaidInvoices): void
    {
        foreach ($dueUnpaidInvoices as $invoice) {
            try {
                $company = $invoice->company;
                $aggregateRoot = InvoiceAggregateRoot::retrieve($invoice->{Invoice::FIELD_UUID});

                $aggregateRoot->charge->notifyInvoicePaymentDue(
                    invoiceUuid  : $invoice->{Invoice::FIELD_UUID},
                    paymentMethod: $invoice->{Invoice::RELATION_BILLING_PROFILE}
                        ->{BillingProfile::FIELD_PAYMENT_METHOD}->value,
                    dueDate      : $invoice->{Invoice::FIELD_DUE_AT},
                    issuedDate   : $invoice->{Invoice::FIELD_ISSUE_AT},
                    total        : $invoice->getTotalOutstanding(),
                );
                $aggregateRoot->persist();

                BillingLogService::log(
                    message  : "{$company->name} has an overdue invoice {$invoice->id}. Due at {$invoice->due_at}",
                    namespace: self::LOG_NAMESPACE,
                );

                DispatchPubSubEvent::dispatch(EventCategory::BILLING, EventName::INVOICE_OVERDUE, [
                    'company_id' => $invoice->company->id,
                    'company_reference' => $invoice->company->reference,
                    'invoice_id' => $invoice->id
                ]);
            } catch (Exception $exception) {
                BillingLogService::logException(
                    exception  : $exception,
                    relatedType: Invoice::class,
                    relatedId  : $invoice->id
                );
            }
        }
    }

    /**
     * TODO - Is there any status we want to exclude ?
     * @return Collection
     */
    protected function getDueUnpaidInvoices(): Collection
    {
        $graceDate = now()->copy()->subWeekdays(self::DAYS_UNTIL_SUSPENSION);
        return Invoice::unpaid()
            ->overdue($graceDate)
            ->whereDoesntHave(Invoice::RELATION_INVOICE_ITEMS, function ($query) {
                $query->whereIn(InvoiceItem::FIELD_BILLABLE_TYPE, [
                    Bundle::class,
                    CreditType::class,
                ]);
            })
            ->get();
    }
}
