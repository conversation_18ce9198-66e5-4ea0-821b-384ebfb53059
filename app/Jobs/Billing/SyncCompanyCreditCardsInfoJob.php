<?php

namespace App\Jobs\Billing;

use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\BillingLogLevel;
use App\Models\Billing\CompanyPaymentMethod;
use App\Services\Billing\BillingLogService;
use App\Services\Billing\CompanyBillingServiceV4;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncCompanyCreditCardsInfoJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param int $companyId
     */
    public function __construct(protected int $companyId)
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     */
    public function handle(CompanyBillingServiceV4 $companyBillingServiceV4): void
    {
        $this->log(
            message: "Syncing credit cards info of company id $this->companyId",
        );

        $companyCards = $companyBillingServiceV4->getAllCompanyStripePaymentMethods(
            companyId: $this->companyId
        );

        $companyCardsCount = $companyCards->count();

        $this->log(
            message: "Total of $companyCardsCount cards found for company id $this->companyId",
        );

        /** @var PaymentMethodDTO $card */
        foreach ($companyCards as $card) {
            $paymentMethod = CompanyPaymentMethod::query()
                ->where(CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE, $card->getId())
                ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $this->companyId)
                ->firstOrFail();

            $paymentMethodCode = $paymentMethod->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE};

            $newData = [
                CompanyPaymentMethod::FIELD_EXPIRY_MONTH => $card->getExpiryMonth(),
                CompanyPaymentMethod::FIELD_EXPIRY_YEAR  => $card->getExpiryYear(),
                CompanyPaymentMethod::FIELD_NUMBER       => $card->getLast4(),
            ];

            $this->log(
                message: "Updating company payment method code $paymentMethodCode",
                context: [
                    'new_data' => $newData
                ]
            );

            $paymentMethod->update($newData);
        }
    }

    /**
     * @param string $message
     * @param BillingLogLevel $level
     * @param array $context
     * @return void
     */
    public function log(
        string $message,
        BillingLogLevel $level = BillingLogLevel::INFO,
        array $context = []
    ): void
    {
        BillingLogService::log(
            message  : $message,
            level    : $level,
            namespace: 'sync_company_credit_cards_info',
            context  : $context
        );
    }
}
