<?php

namespace App\Jobs;

use App\Services\Odin\PingPostPublishing\PingPostPublishService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class PingPostPublishJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * @param int $consumerProductId
     * @param array|null $config
     */
    public function __construct(
        protected int $consumerProductId,
        protected ?array $config = [],
    )
    {
        $this->queue = QueueHelperService::QUEUE_NAME_DEFAULT;
    }

    /**
     * @param PingPostPublishService $pingPostPublishService
     * @return void
     */
    public function handle(PingPostPublishService $pingPostPublishService): void
    {
        $pingPostPublishService->pingPostPublishLead($this->consumerProductId, $this->config);
    }
}
