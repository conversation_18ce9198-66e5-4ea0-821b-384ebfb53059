<?php

namespace App\Jobs\Tasks;

use App\Repositories\TaskRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Console\Command;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreateTasksJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public int $assignedUserId,
        public string $subject,
        public int $typeId,
        public string $priority,
        public int $categoryId,
        public string $availableAt,
        public array $allCompanyIds
    )
    {
        $this->onQueue(config('queue.named_queues.workflows'));
    }

    /**
     * <PERSON>les creating bulk tasks in the background.
     *
     * @param TaskRepository $repository
     * @return int
     */
    public function handle(TaskRepository $repository): int
    {
        try {
            $repository->createTasks(
                $this->subject,
                $this->typeId,
                $this->priority,
                $this->categoryId,
                $this->availableAt,
                $this->allCompanyIds,
                $this->assignedUserId
            );
        } catch (\Exception $e) {
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }
}
