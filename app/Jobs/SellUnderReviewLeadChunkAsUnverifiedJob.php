<?php

namespace App\Jobs;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\ConsumerProcessing\Services\AllocationSchedulingService;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\LeadProcessingUnderReview;
use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\QueueHelperService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SellUnderReviewLeadChunkAsUnverifiedJob implements ShouldQueue
{
    use Queueable;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 1;

    /**
     * Create a new job instance.
     */
    public function __construct(protected array $underReviewLeadIds)
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_PRIORITY_LONG_RUNNING);
    }

    /**
     * Execute the job.
     *
     * @param ProductProcessingQueueRepository $queueRepository
     * @param ConsumerProjectProcessingService $projectProcessingService
     * @param AllocationSchedulingService $schedulingService
     *
     * @throws Exception
     */
    public function handle(
        ProductProcessingQueueRepository $queueRepository,
        ConsumerProjectProcessingService $projectProcessingService,
        AllocationSchedulingService      $schedulingService
    ): void
    {
        LeadProcessingUnderReview::query()
            ->whereIn(LeadProcessingUnderReview::FIELD_ID, $this->underReviewLeadIds)
            ->update([
                LeadProcessingUnderReview::FIELD_CHECKED_UNVERIFIED_BUDGET_AT => now(),
                LeadProcessingUnderReview::UPDATED_AT => now()
            ]);

        $underReviewLeads =  LeadProcessingUnderReview::query()
            ->with([LeadProcessingUnderReview::RELATION_CONSUMER_PRODUCT, LeadProcessingUnderReview::RELATION_CONSUMER_PRODUCT . '.' . ConsumerProduct::RELATION_ADDRESS])
            ->whereIn(LeadProcessingUnderReview::FIELD_ID, $this->underReviewLeadIds)
            ->get();

        $unverifiedBudgets = $this->getUnverifiedBudget(
            $underReviewLeads->map(fn(LeadProcessingUnderReview $underReview) => $underReview->consumerProduct->address->zip_code_location_id)
                ->filter()
                ->unique()
                ->toArray()
        )->keyBy('location_id')->toArray();


        $underReviewLeads->each(function (LeadProcessingUnderReview $underReview) use($unverifiedBudgets, $projectProcessingService, $schedulingService, $queueRepository) {
            $budget = $unverifiedBudgets[$underReview->consumerProduct->address->zip_code_location_id] ?? null;

            if (!$budget || $budget->service_id !== $underReview->consumerProduct->serviceProduct->industry_service_id || $budget->budget_usage >= 115) {
                return;
            }

            $consumerProject = $projectProcessingService->prepareConsumerProject(
                consumer: $underReview->consumerProduct->consumer,
                address: $underReview->consumerProduct->address,
                skipTimezoneDelay: false,
                unverifiedOnly: true
            );

            $delay = $schedulingService->delayToTimeZoneOpenForConsumerProject($consumerProject);
            $leadProcessingAllocation = $queueRepository->addProductToAllocationQueue(
                consumerProductId: $underReview->consumer_product_id,
                processor: LeadProcessor::systemProcessor(),
                reason: $underReview->reason,
                deliverAt: now()->addSeconds($delay),
            );

            if ($queueRepository->changeQueues(
                consumerProduct: $underReview->consumerProduct,
                processor: $leadProcessingAllocation->leadProcessor,
                newProcessingStatus: ProductProcessingQueueRepository::STATUS_ALLOCATED,
                reason: ''
            )) {
                ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($consumerProject->leadConsumerProduct(), $delay);
                AttemptConsumerProjectAllocationJob::dispatch($consumerProject, true)->delay($delay);
            }
        });
    }

    /**
     * @param array $locationIds
     *
     * @return Collection
     */
    protected function getUnverifiedBudget(array $locationIds): Collection
    {
        $locations = collect($locationIds)->join(',');

        $query = "SELECT c.id as company_id,
       cc.id as campaign_id,
       cc.service_id,
       cc.product_id,
       lml.location_id,
       pa.total_cost,
       b.id as budget_id,
       b.value as budget,
       b.type as budget_type,
       NULLIF(
               DATEDIFF(
                       NOW(),
                       FROM_UNIXTIME(
                               GREATEST(
                                       UNIX_TIMESTAMP(b.last_modified_at),
                                       UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 29 DAY))
                               )
                       )
               ), 0
       )                                                                      as num_days,
       CASE
           WHEN b.type = 1 THEN
                   ((pa.total_count / NULLIF(
                           DATEDIFF(
                                   NOW(),
                                   FROM_UNIXTIME(
                                           GREATEST(
                                                   UNIX_TIMESTAMP(b.last_modified_at),
                                                   UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 29 DAY))
                                           )
                                   )
                           ), 0
                                      )) / b.value) * 100
           WHEN b.type = 2 THEN
                   ((pa.total_cost / NULLIF(
                           DATEDIFF(
                                   NOW(),
                                   FROM_UNIXTIME(
                                           GREATEST(
                                                   UNIX_TIMESTAMP(b.last_modified_at),
                                                   UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 29 DAY))
                                           )
                                   )
                           ), 0
                                     )) / b.value) * 100
           ELSE 0
           END                                                                AS budget_usage
FROM company_campaigns cc
         JOIN budget_containers bc
              ON cc.id = bc.company_campaign_id
         JOIN budgets b
              ON bc.id = b.budget_container_id
         JOIN companies c
              ON c.id = cc.company_id
         JOIN company_campaign_location_modules lm on cc.id = lm.company_campaign_id
         JOIN company_campaign_location_module_locations lml on lm.id = lml.module_id
         JOIN (SELECT pa.budget_id,
                      COUNT(pa.id) AS total_count,
                      SUM(pa.cost) AS total_cost
               FROM product_assignments pa
                        JOIN budgets b
                             ON pa.budget_id = b.id
               WHERE pa.chargeable = 1
                 AND pa.delivered = 1
                 AND UNIX_TIMESTAMP(pa.delivered_at) >= GREATEST(
                       UNIX_TIMESTAMP(b.last_modified_at),
                       UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 29 DAY))
                                                        )
               GROUP BY budget_id) AS pa
              ON pa.budget_id = b.id
WHERE b.product_configuration = 1
  AND cc.deleted_at IS NULL
  AND cc.status = 2
  AND c.consolidated_status=1
  AND lml.location_id in ($locations)
ORDER BY budget_usage DESC";


        return collect(DB::select($query));
    }
}
