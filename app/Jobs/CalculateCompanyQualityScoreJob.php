<?php

namespace App\Jobs;

use App\Models\Odin\RulesetScore;
use App\Models\Ruleset;
use App\Services\Odin\Ruleset\Factories\RulesFactory;
use App\Services\Odin\Ruleset\RulesetService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class CalculateCompanyQualityScoreJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct(protected string $source, protected Collection $entries, protected Ruleset $ruleset)
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     * @return void
     */
    public function handle(): void
    {
        $rulesetService = new RulesetService($this->ruleset, RulesFactory::getCompanyRules($this->ruleset));

        $results = [];

        foreach ($this->entries as $entry) {
            try {
                $calculationResult = $rulesetService->calculateScore($entry);

                $results[] = [
                    RulesetScore::FIELD_MODEL_TYPE    => $this->source,
                    RulesetScore::FIELD_MODEL_ID      => $entry->id,
                    RulesetScore::FIELD_RULESET_ID    => $this->ruleset->id,
                    RulesetScore::FIELD_CALCULATED_AT => now(),
                    RulesetScore::FIELD_UPDATED_AT    => now(),
                    RulesetScore::FIELD_CREATED_AT    => now(),
                    RulesetScore::FIELD_SCORE         => $calculationResult[RulesetService::FIELD_TOTAL_SCORE_IN_PERCENTAGE],
                    RulesetScore::FIELD_DATA          => json_encode($calculationResult),
                ];
            } catch (\Exception $exception) {
                logger()->error($exception);
            }
        }


        DB::beginTransaction();

        RulesetScore::query()->insert($results);

        DB::commit();
    }
}
