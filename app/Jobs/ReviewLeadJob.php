<?php

namespace App\Jobs;

use App\Models\LeadProcessingAllocation;
use App\Models\Legacy\EloquentQuote;
use App\Services\LeadProcessing\LeadProcessingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

/**
 * Class ReviewLeadJob
 * @package App\Jobs
 */
class ReviewLeadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /** @var LeadProcessingAllocation $leadProcessingAllocation */
    private LeadProcessingAllocation $leadProcessingAllocation;

    private array $ignoreCompanyIds = [];
    private ?int $remainingLegs = null;
    private bool $isApptLead = false;

    /**
     * @param leadProcessingAllocation $leadProcessingAllocation
     * @param array $ignoreCompanyIds
     * @param int|null $remainingLegs
     * @param bool $isApptLead
     */
    public function __construct(
        LeadProcessingAllocation $leadProcessingAllocation,
        array                    $ignoreCompanyIds = [],
        ?int                     $remainingLegs = null,
        bool                     $isApptLead = false
    )
    {
        $this->leadProcessingAllocation = $leadProcessingAllocation;
        $this->ignoreCompanyIds = $ignoreCompanyIds;
        $this->remainingLegs = $remainingLegs;
        $this->isApptLead = $isApptLead;
    }

    /**
     * @param LeadProcessingService $leadProcessingService
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(LeadProcessingService $leadProcessingService)
    {
        $leadProcessingService->logStatus(
            "Begin ReviewLeadJob for lead: {$this->leadProcessingAllocation->lead_id}",
            ["lead_id" => $this->leadProcessingAllocation->lead_id]
        );

        $this->leadProcessingAllocation->refresh();
        $this->leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD}->refresh();

        $lead = $this->leadProcessingAllocation->{LeadProcessingAllocation::RELATION_LEAD};

        $leadProcessingService->logStatus(
            "Current status for lead: {$lead->status}",
            [
                "lead_id" => $this->leadProcessingAllocation->lead_id,
                "isApptLead" => $this->isApptLead,
                "timezoneOpenForLead" => $leadProcessingService->timezoneOpenForLead($lead)
            ]
        );

        // Don't try to allocate if already allocated
        // This scenario would occur if someone manually allocates a lead that had a pending ReviewLeadJob
        if ($leadProcessingService->timezoneOpenForLead($lead) &&
            (
                ($lead->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_UNDER_REVIEW)
                || ($lead->{EloquentQuote::STATUS} === EloquentQuote::VALUE_STATUS_NO_COMPANIES)
                || $this->isApptLead
            )
        ) {
            $leadProcessingService->allocateLead($this->leadProcessingAllocation, $this->ignoreCompanyIds, $this->remainingLegs);
        }
    }
}
