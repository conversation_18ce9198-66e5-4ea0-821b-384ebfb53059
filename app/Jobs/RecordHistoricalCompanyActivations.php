<?php

namespace App\Jobs;

use App\Repositories\HistoricalCompanyActivationsRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordHistoricalCompanyActivations implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {

    }

    public function handle(HistoricalCompanyActivationsRepository $repository): void
    {
        $repository->determineReactivations();
    }
}
