<?php

namespace App\Jobs\Odin;

use App\Models\LeadProcessingInitial;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateLeadProcessingInitials implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 5;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected int $leadId, protected int $consumerProductId) {}

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        /** @var LeadProcessingInitial $leadProcessingInitials */
        $leadProcessingInitials = LeadProcessingInitial::query()
            ->where(LeadProcessingInitial::FIELD_LEAD_ID, $this->leadId)
            ->firstOrFail();

        $leadProcessingInitials->consumer_product_id = $this->consumerProductId;
        $leadProcessingInitials->save();
    }
}
