<?php

namespace App\Jobs\Odin;

use App\Repositories\Odin\ConsumerProductRepository;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ConsumerProductVerificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected int $consumerProductId) {}

    /**
     * Execute the job.
     *
     * @param ConsumerProductRepository $consumerProductRepository
     *
     * @return void
     * @throws Exception
     */
    public function handle(ConsumerProductRepository $consumerProductRepository): void
    {
        $consumerProductRepository->createIPQualityScore(
            $consumerProductRepository->findOrFail($this->consumerProductId)
        );
    }
}
