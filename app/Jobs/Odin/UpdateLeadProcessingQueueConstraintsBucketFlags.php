<?php

namespace App\Jobs\Odin;

use App\Models\LeadProcessingQueueConstraintsBucketFlags;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateLeadProcessingQueueConstraintsBucketFlags implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 5;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected int $leadId, protected int $consumerProductId) {}

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        /** @var LeadProcessingQueueConstraintsBucketFlags $bucketFlag */
        $bucketFlag = LeadProcessingQueueConstraintsBucketFlags::query()
            ->where(LeadProcessingQueueConstraintsBucketFlags::FIELD_LEAD_ID, $this->leadId)
            ->firstOrFail();

        $bucketFlag->consumer_product_id = $this->consumerProductId;
        $bucketFlag->save();
    }
}
