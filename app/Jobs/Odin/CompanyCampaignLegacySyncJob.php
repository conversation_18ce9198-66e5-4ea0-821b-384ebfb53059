<?php

namespace App\Jobs\Odin;

use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Services\Legacy\APIConsumer;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

/**
 * This should only be used for non-critical syncing where the response is not important
 */
class CompanyCampaignLegacySyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    const string SYNC_TYPE_STATUS_CHANGE = 'status_change';
    const string SYNC_TYPE_UPDATE        = 'update';
    const string SYNC_TYPE_DELETE        = 'delete';

    protected LegacyModule $legacyModule;

    public function __construct(
        protected string $syncType,
        protected CompanyCampaign $campaign,
        protected int $userId = 0,
    )
    {
        $this->legacyModule = app(LegacyModule::class);
    }

    /**
     * @throws BindingResolutionException
     */
    public function handle(): void
    {
        if ($this->syncType === self::SYNC_TYPE_STATUS_CHANGE)
            $this->updateLegacyCampaignStatus();
        else if ($this->syncType === self::SYNC_TYPE_DELETE)
            $this->deleteCampaign();
        else if ($this->syncType === self::SYNC_TYPE_UPDATE)
            $this->updateLegacyCampaignData();
    }

    /**
     * Non-critical back-syncing, really only used by a few filters in Companies Search
     * @return void
     */
    protected function updateLegacyCampaignStatus(): void
    {
        $leadCampaignReference = $this->legacyModule->getLegacyCampaignReference($this->campaign);

        if ($leadCampaignReference) {
            $integrationApiConsumer = app(APIConsumer::class);
            $status = $this->campaign->status === CampaignStatus::ACTIVE ? 1 : 0;
            /** @var CampaignReactivation|null $reactivation */
            $reactivation = $status ? null : $this->campaign->reactivation()->first();

            $legacyTransform = [
                'campaigns' => [
                    [
                        'id' => $leadCampaignReference,
                        'status' => $status
                    ],
                ],
                'options'   => [
                    'reactivate_at' => $reactivation?->reactivate_at?->timestamp ?? null,
                    'reason'        => $reactivation?->reason ?? "not specified",
                ],
                'user_id'   => $this->userId,
            ];
            /** @var Company $company */
            $company = Company::query()
                ->findOrFail($this->campaign->company_id);

            $response = $integrationApiConsumer->patch(
                $this->legacyModule->getDashboardIntegrationBaseRoute($company, 'campaigns/bulk-update-status'),
                $legacyTransform,
            )->json()['data'] ?? [];

            if (!($response['status'] ?? false)) {
                $this->handleSyncError("failed to sync Status change to Lead Campaign reference - ". $leadCampaignReference);
            }
        }
    }

    /**
     * This is primarily keeping legacy campaigns updated for Company filters based on campaign data
     *
     * @throws BindingResolutionException
     */
    protected function updateLegacyCampaignData(): void
    {
        $leadCampaignReference = $this->legacyModule->getLegacyCampaignReference($this->campaign);

        if ($leadCampaignReference) {
            $integrationApiConsumer = app()->make(APIConsumer::class);
            /** @var Company $company */
            $company = Company::query()
                ->findOrFail($this->campaign->company_id);

            $legacyTransform = $this->legacyModule->transformForLegacy($this->campaign);

            $response = $integrationApiConsumer->patch(
                $this->legacyModule->getDashboardIntegrationBaseRoute($company, "campaigns/$leadCampaignReference/update"),
                $legacyTransform
            )->json()['data'] ?? [];

            if (!($response['status'] ?? false)) {
                $this->handleSyncError("failed to sync campaign update to Lead Campaign reference - ". $leadCampaignReference);
            }
        }
    }

    /**
     * @return void
     * @throws BindingResolutionException
     */
    protected function deleteCampaign(): void
    {
        $leadCampaignReference = $this->legacyModule->getLegacyCampaignReference($this->campaign);


        /** @var APIConsumer $integrationApiConsumer */
        $integrationApiConsumer = app()->make(APIConsumer::class);

        $response = $integrationApiConsumer->delete(
            $this->legacyModule->getDashboardIntegrationBaseRoute($this->campaign->company, "campaigns/$leadCampaignReference/delete")
        )->json()['data'] ?? [];

        if (!($response['status'] ?? false)) {
            $this->handleSyncError("failed to sync campaign deletion to Lead Campaign reference - ". $leadCampaignReference);
        }
    }

    public function handleSyncError(string $errorMessage): void
    {
        logger()->warning("Non-critical CompanyCampaign sync error: ".$errorMessage);
    }
}
