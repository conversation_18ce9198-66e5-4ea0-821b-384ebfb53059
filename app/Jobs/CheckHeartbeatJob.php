<?php

namespace App\Jobs;

use App\Models\LeadProcessor;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\Legacy\QuoteRepository;
use App\Services\LeadProcessing\LeadProcessingService;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUniqueUntilProcessing;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;

class CheckHeartbeatJob implements ShouldQueue, ShouldBeUniqueUntilProcessing
{
    const HEARTBEAT_INACTIVE_BUFFER_MINUTES = 5;

    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /** @var int $leadId */
    protected int $leadId;

    /** @var int $processorId */
    protected int $processorId;

    /**
     * @param int $leadId
     * @param int $processorId
     */
    public function __construct(int $leadId, int $processorId)
    {
        $this->leadId = $leadId;
        $this->processorId = $processorId;

        $this->onQueue(config('queue.named_queues.heartbeat'));
    }

    /**
     * Handles processing if a heartbeat is still active.
     *
     * @param LeadProcessingService    $service
     * @param LeadProcessingRepository $repository
     * @param QuoteRepository          $quoteRepository
     * @return void
     */
    public function handle(LeadProcessingService $service, LeadProcessingRepository $repository, QuoteRepository $quoteRepository)
    {
        /** @var LeadProcessor|null $processor */
        $processor = LeadProcessor::query()->where(LeadProcessor::FIELD_ID, $this->processorId)->first();
        $lead = $quoteRepository->find($this->leadId);

        if(!$lead || !$processor || !$repository->hasHeartbeat($lead, $processor))
            return;

        $heartbeat = $repository->getHeartbeat($lead, $processor);

        if($heartbeat) {
            if(Carbon::now()->subMinutes(self::HEARTBEAT_INACTIVE_BUFFER_MINUTES)->greaterThan(Carbon::createFromTimestamp($heartbeat->last_heartbeat)))
                $service->releaseLead($lead, $processor);
             else
                 dispatch((new CheckHeartbeatJob($lead->quoteid, $processor->id))->delay(LeadProcessingService::DELAY_5_MINUTES));
        }
    }

    /**
     * Returns the unique id for this job.
     *
     * @return string
     */
    public function uniqueId(): string
    {
        return "heartbeat_" . $this->leadId;
    }
}
