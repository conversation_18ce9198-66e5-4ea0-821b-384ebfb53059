<?php

namespace App\Jobs;

use App\Repositories\CompanyRatingRepository;
use App\Repositories\CompanyConsumerReviewRepository\CompanyConsumerReviewRepository;
use App\Services\CompanyConsumerReviews\CompanyConsumerReviewService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculateCompanyRatingsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param array $companyIds
     */
    public function __construct(protected array $companyIds)
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_DEFAULT);
    }

    /**
     * @param CompanyConsumerReviewRepository $companyConsumerReviewRepository
     * @param CompanyConsumerReviewService $companyConsumerReviewService
     * @param CompanyRatingRepository $companyRatingRepository
     * @return void
     */
    public function handle(
        CompanyConsumerReviewRepository $companyConsumerReviewRepository,
        CompanyConsumerReviewService    $companyConsumerReviewService,
        CompanyRatingRepository         $companyRatingRepository,
    ): void
    {
        // For each company in collection
        foreach ($this->companyIds as $id) {
            // Get reviews for given company
            $reviews = $companyConsumerReviewRepository->getApprovedConsumerReviews($id)->get();

            if (!$reviews->isEmpty()) {
                // Calculate score with bayesian algorithm
                $average_score = $companyConsumerReviewService->calculateBayesianAvgCompanyRating($reviews);

                // Store score in company average ratings table
                $companyRatingRepository->createCompanyRating($id, $average_score, $reviews);
            }
        }
    }
}
