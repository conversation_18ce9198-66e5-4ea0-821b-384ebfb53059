<?php

namespace App\Jobs;

use App\Contracts\Services\Communication\CommunicationContract;
use App\Models\Phone;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class GetAvailableNumbers implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * Handles updating our list of available phone numbers.
     *
     * @return void
     */
    public function handle(CommunicationContract $communicationContract)
    {
        $allAvailableNumbers = $communicationContract->getAvailableNumbers();

        foreach ($allAvailableNumbers as $reference => $number)
            Phone::query()->updateOrCreate(
                [Phone::FIELD_EXTERNAL_REFERENCE => $reference],
                [
                    Phone::FIELD_PHONE              => $this->formatPhone($number["number"]),
                    Phone::FIELD_EXTERNAL_REFERENCE => $reference,
                    Phone::FIELD_STATUS             => Phone::STATUS_ACTIVE,
                    Phone::FIELD_FRIENDLY_NAME      => $number['name']
                ]
            );
    }

    /**
     * Returns the formatted phone number.
     *
     * @param string $number
     * @return string
     */
    protected function formatPhone(string $number): string
    {
        return Str::replace("+1", "", $number);
    }
}
