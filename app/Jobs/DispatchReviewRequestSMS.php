<?php

namespace App\Jobs;

use App\DTO\SMS;
use App\Enums\GlobalConfigurationKey;
use App\Enums\GlobalConfigurationPhoneNumberField;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Phone;
use App\Models\Text;
use App\Repositories\GlobalConfiguration\GlobalConfigurationRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\TextRepository;
use App\Services\DelayService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Fluent;
use Throwable;

class DispatchReviewRequestSMS implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(
        GlobalConfigurationRepository $configurationRepository,
        ConsumerRepository            $consumerRepository,
        TextRepository                $textRepository,
        DelayService                  $delayService,
    ): void
    {
        // retrieve phone number from global config
        $phoneNumbers = new Fluent($configurationRepository->getConfigurationPayload(GlobalConfigurationKey::PHONE_NUMBERS)?->toArray() ?? []);
        $reviewRequesterPhoneNumber = $phoneNumbers->get("data." . GlobalConfigurationPhoneNumberField::REVIEW_REQUESTER->value);

        if (empty($reviewRequesterPhoneNumber)) {
            logger()->error("unable to retrieve review requester phone number");
            return;
        }

        $reviewRequesterPhone = Phone::query()->where(Phone::FIELD_PHONE, $reviewRequesterPhoneNumber)->first();

        if (empty($reviewRequesterPhone)) {
            logger()->error("unable to find phone model for phone number $reviewRequesterPhoneNumber");
            return;
        }

        $consumers = $consumerRepository->getConsumers(
            dateFrom: now()->subDays(2)->startOfDay(),
            dateTo: now()->subDays(2)->endOfDay(),
            consumerProductStatuses: [ConsumerProduct::STATUS_UNSOLD],
            consumerClassifications: [
                Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_SMS,
                Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_CALL,
                Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_LEAD_PROCESSING,
                Consumer::CLASSIFICATION_VERIFIED_PHONE_VIA_REVALIDATION_SMS,
            ],
            hasValidPhone: true,
        )->groupBy(Consumer::TABLE .'.'. Consumer::FIELD_FORMATTED_PHONE)
            ->with([Consumer::RELATION_WEBSITE, Consumer::RELATION_CONSUMER_PRODUCT])
            ->get();

        $consumerPhones = $consumers->pluck(Consumer::FIELD_FORMATTED_PHONE);

        $recentlyTextedNumbers = $textRepository->list(
            otherNumbers: $consumerPhones->toArray(),
            phoneId: $reviewRequesterPhone->id,
        )->distinct()->pluck(Text::FIELD_OTHER_NUMBER);

        $uncontactedConsumers = $consumers->filter(function (Consumer $consumer) use ($recentlyTextedNumbers) {
            return !$recentlyTextedNumbers->contains($consumer->formatted_phone);
        });

        foreach ($uncontactedConsumers as $consumer) {
            ['start' => $start, 'end' => $end] = $delayService::businessHours();

            try {
                $delay = $delayService->getNextConsumerContactTime(
                    consumer: $consumer,
                    startDate: $start,
                    endDate: $end,
                );

                $dto = $this->buildReviewRequestDTO(
                    fromPhone: $reviewRequesterPhone,
                    consumer: $consumer,
                );

                SendSMS::dispatch($dto)->delay($delay);
            } catch (Throwable $e) {
                logger()->error($e->getMessage());
            }
        }
    }

    public function buildReviewRequestDTO(
        Phone    $fromPhone,
        Consumer $consumer,
    ): SMS
    {
        $website = $consumer->website?->name ?? 'SolarReviews';
        $createdAt = $consumer->created_at->dayName;

        $message = "Hey there! I’m Geoffrey (quality assurance) with $website! \n\nI see that you used our online estimation tool on $createdAt, and I wanted to know if you found it to be easy to use and generally helpful. \n\nPlease answer YES if our service was helpful or NO if it was not helpful. Additional comments are welcome!";

        $phone = $consumer->phone;

        return new SMS(
            toPhone: $phone,
            from: $fromPhone->phone,
            message: $message,
            fromPhoneId: $fromPhone->id,
        );
    }
}
