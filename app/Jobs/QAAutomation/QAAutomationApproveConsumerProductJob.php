<?php

namespace App\Jobs\QAAutomation;

use App\Models\QAAutomation\QAAutomationLog;
use App\Services\QAAutomation\QAAutomationService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesAndRestoresModelIdentifiers;
use Throwable;

class QAAutomationApproveConsumerProductJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesAndRestoresModelIdentifiers;

    /**
     * The number of seconds after which the job's unique lock will be released.
     * This delay it to mitigate the race condition for the system to reserve and allocate the same product
     *
     * @var int
     */
    public $uniqueFor = 10;

    /**
     * Get the unique ID for the job.
     */
    public function uniqueId(): string
    {
        return $this->consumerProductId;
    }

    /**
     * @param int|null $consumerProductId
     */
    public function __construct(
        protected ?int $consumerProductId,
    )
    {
        $this->queue = QueueHelperService::QUEUE_NAME_QA_AUTOMATION;
    }

    /**
     * @param QAAutomationService $service
     * @return void
     * @throws Throwable
     * @throws BindingResolutionException
     */
    public function handle(
        QAAutomationService $service,
    ): void
    {
        if (!$this->consumerProductId)
            return;

        $service->processLead($this->consumerProductId);
    }

    /**
     * @param Throwable|null $exception
     * @return void
     * @throws Throwable
     */
    public function failed(?Throwable $exception): void
    {
        QAAutomationLog::create([
            QAAutomationLog::FIELD_CONSUMER_PRODUCT_ID => $this->consumerProductId,
            QAAutomationLog::FIELD_LOG_LEVEL => QAAutomationLog::LEVEL_ERROR,
            QAAutomationLog::FIELD_ENTRY => "QA Automation Allocation Job Failure. Error: " . $exception?->getMessage() ?? '',
        ]);
        throw $exception;
    }
}
