<?php

namespace App\Jobs;

use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\LeadProcessingUnderReview;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CalculatePotentialRevenue implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public int $underReviewId)
    {
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        /** @var LeadProcessingUnderReview $underReview */
        $underReview = LeadProcessingUnderReview::query()
            ->with([LeadProcessingUnderReview::RELATION_CONSUMER_PRODUCT])
            ->find($this->underReviewId);

        if ($underReview) {
            /** @var ConsumerProjectProcessingService $processingService */
            $processingService = app(ConsumerProjectProcessingService::class);
            /** @var MultiProductAssignmentStrategyContract $assignmentStrategy */
            $assignmentStrategy = app(MultiProductAssignmentStrategyContract::class);

            $consumerProject = $processingService->prepareConsumerProject(
                $underReview->consumerProduct->consumer,
                $underReview->consumerProduct->address,
                now()
            );
            $campaigns = $processingService->getAvailableCampaigns($consumerProject);
            $proposedAssignments = $assignmentStrategy->calculate(
                $consumerProject,
                $campaigns,
                $processingService->getPotentialProductTypes($consumerProject),
                []
            );

            $underReview->possible_revenue = $proposedAssignments->sum(ProposedProductAssignment::DATA_FIELD_PRICE);
            $underReview->save();
        }
    }
}
