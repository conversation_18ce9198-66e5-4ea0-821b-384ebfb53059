<?php

namespace App\Jobs;

use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Services\MarketingCampaign\Events\CampaignSentEvent;
use App\Services\MarketingCampaign\Events\MarketingEvent;
use App\Services\MarketingCampaign\Events\MarketingCampaignConsumerMarketingEvent;
use App\Services\MarketingCampaign\MarketingCampaignService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class HandleEmailEvent implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected MarketingEvent $event,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     * @throws BindingResolutionException
     */
    public function handle(
        MarketingCampaignService $marketingCampaignService,
    ): void
    {
        if ($this->event instanceof CampaignSentEvent) {
            $marketingCampaignService->updateMarketingCampaignByExternalReference(
                externalReference: $this->event->externalReference,
                sentAt: $this->event->sentAt,
                status: MarketingCampaignStatus::SENT,
            );
        } else if ($this->event instanceof MarketingCampaignConsumerMarketingEvent) {
            $this->event->trigger();
        } else {
            logger()->error('Unknown Email Marketing Event');
        }
    }
}
