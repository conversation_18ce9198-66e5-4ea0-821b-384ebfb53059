<?php

namespace App\Jobs;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Models\Odin\Company;
use App\Services\PubSub\PubSubService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CheckCompanyLeftRegistration implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const EVENT_DATA_COMPANY_ID         = 'company_id';
    const EVENT_DATA_COMPANY_REFERENCE  = 'company_reference';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public int $companyId,
        public string $companyReference
    )
    {
        $this->queue = 'long_running';
    }

    /**
     * Execute the job.
     * @param PubSubService $pubSubService
     * @return void
     */
    public function handle(PubSubService $pubSubService): void
    {
        /** @var Company $company */
        $company = Company::query()->find($this->companyId);
        if ($company && $company->status === Company::STATUS_REGISTERING) {
            $pubSubService->handle(
                EventCategory::COMPANIES->value,
                EventName::LEFT_REGISTRATION->value,
                [
                    self::EVENT_DATA_COMPANY_ID         => $company->id,
                    self::EVENT_DATA_COMPANY_REFERENCE  => $company->reference,
                ]
            );
        }
    }
}
