<?php

namespace App\Jobs\CompanyMetrics;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Models\Odin\Company;
use App\Repositories\CompanyMetricsRepository\CompanyMetricsRepository;
use App\Services\CompanyMetrics\SemrushMetricsService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GetCompanyDomainMetricsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * todo take in collection of companies
     * todo handle the date that we want to run the report from
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected Company $company)
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_DATA_ENRICHMENT);
    }

    public function handle(
        SemrushMetricsService $semrushMetricsService,
        CompanyMetricsRepository $companyMetricsRepository,
    ): void
    {

        //todo we can batch the api req to handle 200 companies at a time
        $response = $semrushMetricsService->getCompanyMetrics($this->company);

        if(!$response)
            throw new \Exception("No response payload was returned");

        // Store new company metric
        $newMetric = $companyMetricsRepository->createCompanyMetric(
            $response,
            CompanyMetricRequestTypes::TRAFFIC_SUMMARY,
            $this->company,
            $semrushMetricsService->getRequestUrl(),
            $semrushMetricsService->getServiceType()
        );

        //todo or deal with this data being updated
    }

}