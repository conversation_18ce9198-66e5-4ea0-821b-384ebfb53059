<?php

namespace App\Jobs;

use App\Models\ConsumerReviews\Reviewer;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\ReviewRepository;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class AssociateLeadWithReviewJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public int $tries = 2;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Collection $reviews)
    {
         $this->onQueue(QueueHelperService::QUEUE_NAME_REVIEWS);
    }

    /**
     * @param ReviewRepository $reviewRepository
     * @param ConsumerRepository $consumerRepository
     * @return void
     */
    public function handle(
        ReviewRepository $reviewRepository,
        ConsumerRepository $consumerRepository,
    ): void
    {
        foreach ($this->reviews as $review) {
            $reviewer = $review->reviewer;
            $consumers = $consumerRepository->getAssociatedConsumers($reviewer[Reviewer::FIELD_EMAIL] ?? null, $reviewer[Reviewer::FIELD_PHONE] ?? null);
            if ($consumers) $reviewRepository->associateReviewerWithConsumers($reviewer, $consumers);
        }
    }
}
