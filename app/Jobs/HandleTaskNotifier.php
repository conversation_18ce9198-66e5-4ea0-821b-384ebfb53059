<?php

namespace App\Jobs;

use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Models\Workflow;
use App\Services\Workflows\WorkflowProcessingService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class HandleTaskNotifier implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(public int $currentWorkflowId, public int $taskId, public int $templateId)
    {
        $this->onQueue('workflows');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(WorkflowProcessingService $processingService)
    {
        /** @var Task|null $task */
        $task = Task::query()->find($this->taskId);
        $template = Workflow::query()->find($this->templateId);
        /** @var RunningWorkflow $workflow */
        $workflow = RunningWorkflow::query()->find($this->currentWorkflowId);

        if($template && $workflow && !$task?->completed) {
            $workflow->payload->set('task_id', $this->taskId);
            $workflow->payload->set('task_assigned_user_id', $task->assigned_user_id);
            $processingService->dispatchNewRunningWorkflow($this->templateId, $workflow->payload);
        }
    }
}
