<?php

namespace App\Jobs;

use App\Models\Odin\Consumer;
use App\Services\Odin\API\OdinAuthoritativeAPIService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SaveLegacyAffiliateAndTracking implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @param Consumer $consumer
     * @param array $payload
     */
    public function __construct(protected Consumer $consumer, protected array $payload) {}

    /**
     * Execute the job.
     *
     * @param OdinAuthoritativeAPIService $service
     *
     * @return void
     */
    public function handle(OdinAuthoritativeAPIService $service): void
    {
        $service->saveAffiliateAndTracking($this->consumer, $this->payload);
    }
}
