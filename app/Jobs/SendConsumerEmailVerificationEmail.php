<?php

namespace App\Jobs;

use App\Enums\TemplateManagement\TemplatePurposeKey;
use App\Enums\TemplateManagement\TemplateRelation;
use App\Models\EmailTemplate;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\TemplateSelector;
use App\Repositories\TemplateRepository;
use App\Services\ConfigurableEmailSenderService;
use App\Services\ConsumerService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendConsumerEmailVerificationEmail implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected Consumer $consumer)
    {
        $this->onQueue('long_running');
    }

    /**
     * Execute the job.
     *
     * @param ConfigurableEmailSenderService $emailSenderService
     * @param TemplateRepository $templateRepository
     * @param ConsumerService $consumerService
     *
     * @throws BindingResolutionException
     */
    public function handle(ConfigurableEmailSenderService $emailSenderService, TemplateRepository $templateRepository, ConsumerService $consumerService): void
    {
        $template = $this->getEmailTemplate($templateRepository);

        if (!$template) {
            logger()->error('An email template is required to send email verification email');
            return;
        }

        $emailSenderService->sendWithTemplate(
            template: $template,
            toEmail: $this->consumer->email,
            fromEmail: 'no-reply@' . $consumerService->getConsumerDomain($this->consumer),
            fromUserName: '',
            data: [
                'consumer_reference' => $this->consumer->reference
            ]
        );
    }

    /**
     * @param TemplateRepository $templateRepository
     *
     * @return EmailTemplate|null
     */
    protected function getEmailTemplate(TemplateRepository $templateRepository): ?EmailTemplate
    {
        $industryTemplate = $templateRepository->getTemplateSelectors(
            templateRelation: TemplateRelation::CONSUMER,
            templatePurposeKey: TemplatePurposeKey::CONSUMER_EMAIL_VERIFICATION,
            industryId: $this->getIndustry()->id,
            templateType: EmailTemplate::class
        )->first();

        if ($industryTemplate) {
            return $industryTemplate->{TemplateSelector::RELATION_TEMPLATE};
        }

        return $templateRepository->getTemplateSelectors(
            templateRelation: TemplateRelation::CONSUMER,
            templatePurposeKey: TemplatePurposeKey::CONSUMER_EMAIL_VERIFICATION,
            templateType: EmailTemplate::class
        )
            ->filter(fn(TemplateSelector $selector) => !$selector->industry_id)
            ->first()?->{TemplateSelector::RELATION_TEMPLATE};
    }

    /**
     * @return Industry
     */
    protected function getIndustry(): Industry
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = $this->consumer->consumerProducts()->first();

        return $consumerProduct->industryService->industry;
    }
}
