<?php

namespace App\Jobs;

use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Services\Odin\ProductStatistics\ProductStatisticsService;
use App\Services\QueueHelperService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

/**
 * Pre-cache statistics for very large active companies to reduce dashboard load times
 * This is currently only around 20 companies
 */
class PreCacheCampaignStatisticsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->queue = QueueHelperService::QUEUE_NAME_LONG_RUNNING;
    }

    /**
     * @param int $campaignLocationLimit
     * @return void
     */
    public function handle(int $campaignLocationLimit = 12000): void
    {
        /** @var ProductStatisticsService $statisticsService */
        $statisticsService = app(ProductStatisticsService::class);

        $companies = CompanyCampaignLocationModuleLocation::query()
            ->select(DB::raw(Company::TABLE .'.'. Company::FIELD_ID . ", COUNT(DISTINCT " . CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_ID . ") AS campaign_locations_count"))
            ->join(CompanyCampaignLocationModule::TABLE, CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_ID, CompanyCampaignLocationModuleLocation::TABLE .'.'. CompanyCampaignLocationModuleLocation::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, CompanyCampaignLocationModule::TABLE .'.'. CompanyCampaignLocationModule::FIELD_CAMPAIGN_ID)
            ->join(Company::TABLE, Company::TABLE .'.'. Company::FIELD_ID, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID)
            ->where(function (Builder $query) {
                $query->where(Company::TABLE  .'.'. Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
                    ->orWhereIn(Company::TABLE . '.' . Company::FIELD_CAMPAIGN_STATUS, [
                        CompanyCampaignStatus::CAMPAIGNS_PAUSED,
                        CompanyCampaignStatus::CAMPAIGNS_OFF
                    ]);
            })
            ->groupBy(Company::TABLE .'.'. Company::FIELD_ID)
            ->having('campaign_locations_count', '>', $campaignLocationLimit)
            ->orderBy('campaign_locations_count', 'DESC')
            ->limit(50)
            ->get();

        foreach($companies as $company) {
            $primaryServiceProduct = $this->getPrimaryServiceProduct($company->id);
            if ($primaryServiceProduct)
                $statisticsService->preCacheStatistics($company->id, $primaryServiceProduct);
        }
    }

    /**
     * @param int $companyId
     * @return int|null
     */
    protected function getPrimaryServiceProduct(int $companyId): ?int
    {
        $primaryProducts = ProductAssignment::query()
            ->select(DB::raw(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID . ", COUNT(" . ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_ID . ") as product_count"))
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID, ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->where(ProductAssignment::TABLE .'.'. ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::CREATED_AT, '>', now()->subDays(60))
            ->groupBy(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
            ->orderBy('product_count', 'DESC')
            ->first();

        return $primaryProducts->{ConsumerProduct::FIELD_SERVICE_PRODUCT_ID} ?? null;
    }
}
