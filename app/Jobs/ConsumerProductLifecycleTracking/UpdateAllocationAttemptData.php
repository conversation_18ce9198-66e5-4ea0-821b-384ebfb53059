<?php

namespace App\Jobs\ConsumerProductLifecycleTracking;

use App\Repositories\ConsumerProductLifecycleTrackingRepository;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use Illuminate\Queue\SerializesModels;

class UpdateAllocationAttemptData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $consumerProductId,
        public string $allocationJobUuid,
        public int $allocationJobAttemptNumber,
        public array $attemptData,
        public Carbon $updatedAt,
    ) {}

    public function handle(ConsumerProductLifecycleTrackingRepository $repository): void
    {
        $repository->updateAttemptData($this->consumerProductId, $this->allocationJobUuid, $this->allocationJobAttemptNumber, $this->attemptData, $this->updatedAt);
    }

    public function middleware(): array
    {
        return [(new WithoutOverlapping($this->consumerProductId))->releaseAfter(1)->expireAfter(30)];
    }
}
