<?php

namespace App\Jobs\ConsumerProductLifecycleTracking;

use App\Repositories\ConsumerProductLifecycleTrackingRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PruneConsumerProductLifecycleTrackers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function handle(ConsumerProductLifecycleTrackingRepository $repository): void
    {
        $repository->pruneOldTrackers();
    }

}
