<?php

namespace App\Jobs;

use App\Repositories\LogMonitoringRepository;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\App;

class RecordMonitoringLog implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        public string $message,
        public array $payload = [],
        public ?string $logger = LogMonitoringRepository::LOGGER_MONITORING)
    {
        $this->onQueue(config('queue.named_queues.logging'));
    }

    /**
     * Execute the job.
     *
     * @param LogMonitoringRepository $repository
     * @return void
     * @throws Exception
     */
    public function handle(LogMonitoringRepository $repository): void
    {
        $repository->setLogger($this->logger);
        $repository->record($this->message, $this->payload);

        if(config('services.google.logging.debug_monitoring_logs')) {
            logger()->debug($this->message);
            logger()->debug($this->payload);
        }
    }
}
