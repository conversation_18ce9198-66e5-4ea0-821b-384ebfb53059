<?php

namespace App\Jobs\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use App\Models\Conference\ConferenceTranscriptEntry;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecord;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecordSentiment;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecordTopic;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AnalyzeConferenceTranscriptWithDeepgram implements ShouldQueue, ShouldBeUnique
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected ConferenceTranscript $conferenceTranscript,
    ) {
    }

    /**
     * Execute the job.
     *
     * @throws ConnectionException
     * @throws \Throwable
     */
    public function handle(): void
    {
        if (blank(config('services.deepgram.api_key'))) {
            $this->fail('You must set the DEEPGRAM_API_KEY environment variable to run this job.');

            return;
        }

        $entries = $this->conferenceTranscript->entries->filter(fn(ConferenceTranscriptEntry $entry) => $entry->text);

        if ($entries->isEmpty()) {
            $this->fail("There are no entries to send for conference transcript {$this->conferenceTranscript->id}.");

            return;
        }

        $text = $this->conferenceTranscript->text;

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Token '.trim(config('services.deepgram.api_key')),
                'Content-Type' => 'application/json',
            ])
                ->post(self::getUrl(), compact('text'))
                ->throw();
        } catch (RequestException $e) {
            $this->fail($e->getMessage());

            return;
        }

        try {
            DB::transaction(function () use ($response) {
                $deepgram_request_id = $response->json('metadata.request_id');

                $deepgram_requested_at = $response->json('metadata.created');

                $deepgram_language = $response->json('metadata.language');

                $deepgram_average_sentiment = $response->json('results.sentiments.average.sentiment');

                $deepgram_average_sentiment_score = $response->json('results.sentiments.average.sentiment_score');

                $deepgram_summary = $response->json('results.summary.text');

                Log::debug('Debug', [
                    'data' => [
                        'metadata.request_id' => $deepgram_request_id,
                        'metadata.created' => $deepgram_requested_at,
                        'metadata.language' => $deepgram_language,
                        'results.sentiments.average.sentiment' => $deepgram_average_sentiment,
                        'results.sentiments.average.sentiment_score' => $deepgram_average_sentiment_score,
                        'results.summary.text' => $deepgram_summary,
                    ],
                    'class' => self::class,
                    'conference_transcript_id' => $this->conferenceTranscript->id,
                    'model' => ConferenceTranscriptDeepgramRecord::class,
                ]);

                /** @var ConferenceTranscriptDeepgramRecord $deepgramRecord */
                $deepgramRecord = ConferenceTranscriptDeepgramRecord::updateOrCreate([
                    'conference_transcript_id' => $this->conferenceTranscript->id,
                ], compact('deepgram_request_id', 'deepgram_requested_at', 'deepgram_language',
                    'deepgram_average_sentiment',
                    'deepgram_average_sentiment_score', 'deepgram_summary'),
                );

                $topicSegments = $response->json('results.topics.segments');

                $values = [];

                $deepgramRecord->topics()->delete();

                foreach ($topicSegments as $topicSegment) {
                    $topics = $topicSegment['topics'];

                    foreach ($topics as $topic) {
                        $values[] = [
                            'conference_transcript_deepgram_record_id' => $deepgramRecord->id,
                            'conference_transcript_id' => $this->conferenceTranscript->id,
                            'topic' => $topic['topic'],
                            'confidence_score' => $topic['confidence_score'],
                            'text' => $topicSegment['text'],
                        ];
                    }
                }

                Log::debug('Debug', [
                    'values' => $values,
                    'class' => self::class,
                    'conference_transcript_id' => $this->conferenceTranscript->id,
                    'model' => ConferenceTranscriptDeepgramRecordTopic::class,
                ]);

                ConferenceTranscriptDeepgramRecordTopic::insert($values);

                $sentimentSegments = $response->json('results.sentiments.segments');

                $values = [];

                $deepgramRecord->sentiments()->delete();

                foreach ($sentimentSegments as $sentimentSegment) {
                    $values[] = [
                        'conference_transcript_deepgram_record_id' => $deepgramRecord->id,
                        'conference_transcript_id' => $this->conferenceTranscript->id,
                        'text' => $sentimentSegment['text'],
                        'sentiment' => $sentimentSegment['sentiment'],
                        'sentiment_score' => $sentimentSegment['sentiment_score'],
                    ];
                }

                Log::debug('Debug', [
                    'values' => $values,
                    'class' => self::class,
                    'conference_transcript_id' => $this->conferenceTranscript->id,
                    'model' => ConferenceTranscriptDeepgramRecordSentiment::class,
                ]);

                ConferenceTranscriptDeepgramRecordSentiment::insert($values);
            });
        } catch (\Exception $e) {
            $this->fail($e->getMessage());
        }
    }

    public static function getUrl(): string
    {
        $query = http_build_query([
            'sentiment' => 'true',
            'intents' => 'true',
            'summarize' => 'true',
            'topics' => 'true',
            'language' => 'en',
        ]);

        return "https://api.deepgram.com/v1/read?$query";
    }

    public function uniqueId(): string
    {
        return 'AnalyzeConferenceTranscriptWithDeepgram-'.$this->conferenceTranscript->id;
    }

    public function tags(): array
    {
        return ['AnalyzeConferenceTranscriptWithDeepgram', 'conference_transcript:'.$this->conferenceTranscript->id];
    }
}
