<?php

namespace App\Jobs;

use App\Contracts\Services\CompanyMetricsServiceContract;
use App\DTO\PPCResponse;
use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Http\Controllers\API\CompaniesController;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Repositories\CompanyMetricsRepository\CompanyMetricsRepository;
use App\Services\Workflows\WorkflowEventService;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class GetCompanyMetricsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const string EVENT_KEY = 'event';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected Collection $companies)
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * Execute the job.
     *
     * @param CompanyMetricsServiceContract $companyMetricsServiceContract
     * @param CompanyMetricsRepository $companyMetricsRepository
     * @return void
     * @throws GuzzleException
     */
    public function handle(
        CompanyMetricsServiceContract $companyMetricsServiceContract,
        CompanyMetricsRepository $companyMetricsRepository,
        WorkflowEventService $workflowEventService,
    ): void
    {
        //get the latest complete month
        $currentDate = Carbon::now();
        $startDate = $currentDate->subMonth()->startOfMonth()->toDateTime();
        $endDate = $currentDate->subMonth()->endOfMonth()->toDateTime();

        /** @var Company $company */
        foreach ($this->companies as $company) {
            if (!empty($company->website)) {
                // Get data from API
                $response = $companyMetricsServiceContract->getCompanyMetrics($company, $startDate, $endDate);

                if (is_null($response)) {
                    return;
                }

                // Get old company metric for workflow event {old_value} data
                $previousMetric = $companyMetricsRepository->getLatestCompanyMetric($company->id, CompanyMetricRequestTypes::PPC_SPEND);


                // Store new company metric
                $newMetric = $companyMetricsRepository->createCompanyMetric(
                    $response->toArray(),
                    CompanyMetricRequestTypes::PPC_SPEND,
                    $company,
                    $response->getRequest(),
                    $companyMetricsServiceContract->getServiceType()
                );

                // Grab {old_value} from old company metric if exists
                $oldValue = 0;
                if (!is_null($previousMetric)) {
                    $oldValue = $previousMetric[CompanyMetric::FIELD_REQUEST_RESPONSE][PPCResponse::FIELD_MONTHLY_SPEND];
                }
                // Grab workflow {new_value} from new metric
                $newValue = $newMetric[CompanyMetric::FIELD_REQUEST_RESPONSE][PPCResponse::FIELD_MONTHLY_SPEND];
                // Build workflow event
                $workflowEventData =
                    [
                        CompaniesController::REQUEST_TYPE                       => EventCategory::COMPANIES->value,
                        self::EVENT_KEY                                         => EventName::MONTHLY_AD_SPEND_UPDATED->value,
                        CompaniesController::REQUEST_COMPANY_ID                 => $company->id,
                        CompaniesController::REQUEST_COMPANY_REFERENCE          => $company->reference,
                        EventName::MONTHLY_AD_SPEND_UPDATED->getOldValueKey()   => $oldValue,
                        EventName::MONTHLY_AD_SPEND_UPDATED->getNewValueKey()   => $newValue,
                    ];
                // Fire workflow event
                $workflowEventService->handle(
                    EventCategory::COMPANIES->value,
                    EventName::MONTHLY_AD_SPEND_UPDATED->value,
                    $workflowEventData
                );
            }
        }
    }
}
