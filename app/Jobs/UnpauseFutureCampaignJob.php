<?php

namespace App\Jobs;

use App\Enums\Campaigns\CampaignStatus;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Services\Campaigns\CompanyCampaignService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class UnpauseFutureCampaignJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    protected array $errors = [];

    /**
     * Create a new job instance.
     */
    public function __construct(protected int $campaignId) {}

    /**
     * Execute the job.
     * @throws BindingResolutionException
     */
    public function handle(CompanyCampaignService $companyCampaignService): void
    {
        /** @var ?CompanyCampaign $campaign */
        $campaign = CompanyCampaign::query()->where(CompanyCampaign::FIELD_ID, $this->campaignId)->first();

        $hasError = $this->checkIfIsValid($campaign);

        if ($hasError) {
            Log::error($hasError);
            return;
        }

        $companyCampaignService->unpauseCampaign(
            campaign : $campaign,
            oldStatus: $campaign->status
        );
    }

    private function checkIfIsValid(?CompanyCampaign $campaign = null): ?string
    {
        if (!$campaign) return "Company Campaign with ID: $this->campaignId  could not be found.";

        if ($campaign->status !== CampaignStatus::PAUSED_TEMPORARILY) return 'Campaign status not in paused temporarily';

        $reactivation = $campaign->reactivation;

        if ($reactivation === null) {
            return "Company Campaign with ID: $this->campaignId  could not find a related reactivation.";
        }

        if ($reactivation->reactivate_at > now()) {
            $now = now()->format('M d, Y h:i A');
            return "Campaign not due to be reactivated until $reactivation->reactivate_at , while current time is $now";
        }

        if ($reactivation->deleted_at !== null) {
            return "Campaign reactivation ID: $reactivation->id has been deleted";
        }

        if ($reactivation->reactivate_at === null) {
            return "No reactivation time given";
        }

        return null;
    }
}
