<?php

namespace App\Jobs\Google;

use App\Models\GoogleUserToken;
use App\Services\Calendar\Google\GoogleServiceProviderFactory;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PostUserGrantGoogleAccessJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(protected GoogleUserToken $token)
    {
        $this->queue = 'mailbox_handle_mail_provider_event';
    }

    public function handle(): void
    {
        $service = GoogleServiceProviderFactory::make(
            type : $this->token->service,
            token: $this->token
        );

        $service->postUserHasGivenAccess($this->token);
    }
}
