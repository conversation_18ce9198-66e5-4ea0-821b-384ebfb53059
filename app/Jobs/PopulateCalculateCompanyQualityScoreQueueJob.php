<?php

namespace App\Jobs;

use App\Enums\RulesetType;
use App\Models\Odin\CompanyQualityScoreRule;
use App\Models\Odin\RulesetScore;
use App\Models\Ruleset;
use App\Services\Odin\Ruleset\SourceFilters\RulesetSourceFilterBuilderFactory;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PopulateCalculateCompanyQualityScoreQueueJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    const DELAY_PER_RULE_IN_SECONDS = 5;
    protected int $chunkSize = 100;


    /**
     * @param CompanyQualityScoreRule[] $configurations
     */
    public function __construct(protected array $configurations = [])
    {
        $this->onQueue('long_running');
    }


    /**
     * TODO - Check whether this should be generic or not
     * Execute the job.
     * @return void
     * @throws Exception
     */
    public function handle(): void
    {
        $configurations = $this->getConfigurations();

        $delay = now();

        foreach ($configurations as $config) {
            $countActiveRules = count(collect($config->ruleset->{Ruleset::FIELD_RULES})->filter(function ($item){
                return $item['is_active'];
            }));
            $increment = $countActiveRules * self::DELAY_PER_RULE_IN_SECONDS;

            $rulesetFilterBuilderService = RulesetSourceFilterBuilderFactory::generate($config->ruleset->{Ruleset::FIELD_SOURCE});

            if (!$rulesetFilterBuilderService) throw new Exception('Source Filter Builder not found for ' . $config->ruleset->{Ruleset::FIELD_SOURCE});

            // DELETE ALL OLD RESULTS
            RulesetScore::query()->where(RulesetScore::FIELD_RULESET_ID, $config->ruleset->id)->delete();

            $rulesetFilterBuilderService
                ->getQuery($config->ruleset->{Ruleset::FIELD_FILTER})
                ->orderBy('id')
                ->chunk($this->chunkSize, function ($entries) use ($config, $increment, $delay) {
                    CalculateCompanyQualityScoreJob::dispatch($config->ruleset->{Ruleset::FIELD_SOURCE}, $entries, $config->ruleset)->delay($delay);
                    $delay->add('seconds', $increment);
                });
        }
    }

    private function getConfigurations(): array
    {
        if (count($this->configurations) > 0) return $this->configurations;

        /** @var CompanyQualityScoreRule[] $configurations */
        $configurations = CompanyQualityScoreRule::query()
                ->where(CompanyQualityScoreRule::FIELD_IS_PRODUCTION, true)
                ->whereHas(CompanyQualityScoreRule::RELATION_RULESET, function (Builder $query) {
                    $query->whereNull(Ruleset::FIELD_DELETED_AT);
                })
                ->with(CompanyQualityScoreRule::RELATION_RULESET)
                ->whereRelation(CompanyQualityScoreRule::RELATION_RULESET, Ruleset::FIELD_TYPE, RulesetType::RANKING)
                ->get();

        return $configurations;
    }
}
