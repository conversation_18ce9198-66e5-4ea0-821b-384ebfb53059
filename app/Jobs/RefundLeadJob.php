<?php

namespace App\Jobs;

use App\DTO\LeadRefund\LeadRefundItemFormattedData;
use App\Enums\LeadRefundItemChargeRefundStatus;
use App\Enums\LeadRefundItemType;
use App\Enums\LeadRefundStatus;
use App\Models\LeadRefund;
use App\Models\LeadRefundItem;
use App\Models\LeadRefundItemRefund;
use App\Services\Billing\BillingLogService;
use App\Services\RefundLeadService\RefundLeadService;
use App\Services\RefundLeadService\RefundLeadServiceFactory;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class RefundLeadJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const string LOG_NAMESPACE = 'refund_lead_job';

    public function __construct(
        protected LeadRefund $leadRefund
    )
    {
    }

    /**
     * Execute the job.
     *
     * @throws Exception
     */
    public function handle(): void
    {
        try {
            $this->validateLeadRefundStatus();

            $leadRefundItemsGroupedByVersion = $this->groupLeadRefundItemsByBillingVersion();

            $this->processLeadRefundsByVersion($leadRefundItemsGroupedByVersion);

            $this->updateLeadRefundStatus(LeadRefundStatus::REFUND_ISSUED->value);
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception: $exception,
                namespace: self::LOG_NAMESPACE,
                context  : [
                    'lead_refund_id' => $this->leadRefund->id,
                ]
            );
        }
    }

    /**
     * @throws Exception
     */
    protected function validateLeadRefundStatus(): void
    {
        if (!in_array($this->leadRefund->status, [
            LeadRefundStatus::APPROVED,
            LeadRefundStatus::APPROVED_WITH_REJECTIONS,
        ])) {
            throw new Exception('Invalid lead refund status.');
        }
    }

    /**
     * @return Collection<string, Collection<LeadRefundItem>>
     */
    protected function groupLeadRefundItemsByBillingVersion(): Collection
    {
        return $this->leadRefund->getRefundableItems()->mapToGroups(fn(LeadRefundItem $item) => [
            $item->productAssignment->getBillingVersion()->value => $item,
        ]);
    }

    /**
     * @param Collection<string, Collection<LeadRefundItem>> $leadRefundItemsGroupedByVersion
     * @throws Exception
     */
    protected function processLeadRefundsByVersion(Collection $leadRefundItemsGroupedByVersion): void
    {
        foreach ($leadRefundItemsGroupedByVersion as $billingVersion => $leadRefundItems) {
            $formattedLeadRefundsGroupedByType = $leadRefundItems->mapToGroups(fn(LeadRefundItem $item) => [
                $item->{LeadRefundItem::FIELD_REFUND_TYPE}->value => $item,
            ]);

            $this->processLeadRefundsByType(
                formattedLeadRefundsGroupedByType: $formattedLeadRefundsGroupedByType,
                billingVersion                   : $billingVersion
            );
        }
    }

    /**
     * @param Collection<string, Collection<LeadRefundItem>> $formattedLeadRefundsGroupedByType
     * @param string $billingVersion
     * @throws Exception
     */
    protected function processLeadRefundsByType(
        Collection $formattedLeadRefundsGroupedByType,
        string $billingVersion
    ): void
    {
        foreach ($formattedLeadRefundsGroupedByType as $type => $groupedLeadRefundItems) {
            $refundLeadService = RefundLeadServiceFactory::make($billingVersion);

            $formattedLeadRefundsGroupedByInvoiceId = $refundLeadService
                ->formatLeadRefundItems($groupedLeadRefundItems)
                ->mapToGroups(fn(LeadRefundItemFormattedData $item) => [
                    $item->getInvoiceId() => $item,
                ]);

            foreach ($formattedLeadRefundsGroupedByInvoiceId as $invoiceId => $items) {
                try {
                    $this->processRefundByType(
                        type             : $type,
                        refundLeadService: $refundLeadService,
                        items            : $items,
                        invoiceId        : $invoiceId
                    );
                } catch (Exception $exception) {
                    BillingLogService::logException(
                        exception: $exception,
                        namespace: self::LOG_NAMESPACE,
                        context  : [
                            'billing_version'                              => $billingVersion,
                            'type'                                         => $type,
                            'invoice_id'                                   => $invoiceId,
                            'formatted_lead_refunds_grouped_by_invoice_id' => $formattedLeadRefundsGroupedByInvoiceId,
                        ]
                    );
                }
            }
        }
    }

    /**
     * @param string $type
     * @param RefundLeadService $refundLeadService
     * @param Collection<LeadRefundItemFormattedData> $items
     * @param string $invoiceId
     * @throws Exception
     */
    protected function processRefundByType(
        string $type,
        RefundLeadService $refundLeadService,
        Collection $items,
        string $invoiceId
    ): void
    {
        BillingLogService::log(
            message  : 'Refunding lead items',
            namespace: self::LOG_NAMESPACE,
            context  : [
                'type'      => $type,
                'items'     => $items,
                'invoiceId' => $invoiceId,
            ]
        );
        match ($type) {
            LeadRefundItemType::CASH->value   => $this->refundCashItems(
                refundLeadService: $refundLeadService,
                items            : $items,
                invoiceId        : $invoiceId
            ),
            LeadRefundItemType::CREDIT->value => $refundLeadService->refundCreditItems(
                leadRefundItemFormattedData: $items,
                invoiceId                  : $invoiceId
            ),
            default                           => throw new Exception('Unsupported lead refund type: ' . $type),
        };
    }

    /**
     * @param RefundLeadService $refundLeadService
     * @param Collection $items
     * @param int $invoiceId
     * @return void
     */
    protected function refundCashItems(
        RefundLeadService $refundLeadService,
        Collection $items,
        int $invoiceId,
    ): void
    {
        $status = LeadRefundItemChargeRefundStatus::REFUND_ISSUED;
        $errorMessage = null;
        $response = null;

        try {
            $response = $refundLeadService->refundCashItems(
                leadRefundItemFormattedData: $items,
                invoiceId                  : $invoiceId
            );
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception: $exception,
                namespace: self::LOG_NAMESPACE,
                context  : [
                    'items'     => $items,
                    'invoiceId' => $invoiceId,
                ],
            );
            $status = LeadRefundItemChargeRefundStatus::STRIPE_ERROR;
            $errorMessage = $exception->getMessage();
        }

        collect($items)->map(fn(LeadRefundItemFormattedData $leadRefundItem) => LeadRefundItemRefund::query()->create([
            LeadRefundItemRefund::FIELD_LEGACY_INVOICE_ID    => $invoiceId,
            LeadRefundItemRefund::FIELD_LEAD_REFUND_ITEM_ID  => $leadRefundItem->getLeadRefundItemId(),
            LeadRefundItemRefund::FIELD_LEGACY_CHARGE_ID     => $response?->chargeId,
            LeadRefundItemRefund::FIELD_EXTERNAL_CHARGE_ID   => $response?->externalChargeId,
            LeadRefundItemRefund::FIELD_LEAD_REFUND_ID       => $leadRefundItem->getLeadRefundId(),
            LeadRefundItemRefund::FIELD_EXTERNAL_REFUND_ID   => $response?->externalRefundId,
            LeadRefundItemRefund::FIELD_INTERNAL_REFUND_UUID => $response?->internalRefundUuid,
            LeadRefundItemRefund::FIELD_STATUS               => $status->value,
            LeadRefundItemRefund::FIELD_ERROR_MESSAGE        => $errorMessage,
        ]));
    }

    /**
     * @param string $status
     * @return void
     */
    protected function updateLeadRefundStatus(string $status): void
    {
        $this->leadRefund->update([
            LeadRefund::FIELD_STATUS => $status,
        ]);
    }
}
