<?php

namespace App\DataModels\ProductAssignment;

use App\Contracts\ProductAssignment\ProductPayload;
use App\Enums\Odin\Product;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\QualityTier;
use App\Models\SaleType;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ProductAssignmentRepository;
use App\Repositories\Odin\ProductRejectionRepository;
use App\Repositories\Odin\QualityTierRepository;
use App\Repositories\Odin\SaleTypeRepository;
use App\Services\Odin\Campaigns\CompanyCampaignAppointmentService;
use App\Services\Odin\ConsumerProxyPhoneService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Throwable;

class ProposedProductAssignment
{
    const string DATA_FIELD_PRICE = 'price';

    public function __construct(
        public int             $companyId,
        public int             $campaignId,
        public int             $budgetId,
        public float           $price,
        public int             $productId,
        public int             $salesTypeId,
        public int             $consumerProductId,
        public bool            $isAsserted = false,
        public bool            $isExistingAssignment = false,
        public ?int            $existingProductAssignmentId = null,
        public bool            $salesTypeAndPriceUpdated = false,
        public ?ProductPayload $payload = null,
        public ?int            $qualityTierId = null,
        public ?int            $scheduleId = null,
        public bool            $chargeable = true,
        public bool            $soldWhileUnderReview = false,
    ) {}

    /**
     * @return ConsumerProduct
     */
    public function consumerProduct(): ConsumerProduct
    {
        /** @var ConsumerProductRepository $consumerProductRepository */
        $consumerProductRepository = app(ConsumerProductRepository::class);
        return $consumerProductRepository->getConsumerProductById($this->consumerProductId);
    }

    public function existingProductAssignment(): ?ProductAssignment
    {
        if(!$this->existingProductAssignmentId)
            return null;
        /** @var ProductAssignmentRepository $productAssignmentRepository */
        $productAssignmentRepository = app(ProductAssignmentRepository::class);
        return $productAssignmentRepository->find($this->existingProductAssignmentId);
    }

    /**
     * @return CompanyCampaign
     */
    public function campaign(): CompanyCampaign
    {
        /** @var CompanyCampaignRepository $campaignRepository */
        $campaignRepository = app(CompanyCampaignRepository::class);
        return $campaignRepository->find($this->campaignId);
    }

    /**
     * @return Company
     */
    public function company(): Company
    {
        /** @var CompanyRepository $companyRepository */
        $companyRepository = app(CompanyRepository::class);
        return $companyRepository->find($this->companyId);
    }

    /**
     * @return SaleType|null
     * @throws BindingResolutionException
     */
    public function saleType(): ?SaleType
    {
        /** @var SaleTypeRepository $repository */
        $repository = app()->make(SaleTypeRepository::class);

        return $repository->find($this->salesTypeId);
    }

    /**
     * @return QualityTier|null
     */
    public function qualityTier(): ?QualityTier
    {
        /** @var QualityTierRepository $repository */
        $repository = app(QualityTierRepository::class);

        return $this->qualityTierId ? $repository->findById($this->qualityTierId) : null;
    }

    /**
     * @return ProductAssignment|null
     * @throws Throwable
     */
    public function createProductAssignment(): ?ProductAssignment
    {
        if($this->isExistingAssignment)
            return null;

        /** @var ProductAssignmentRepository $productAssignmentRepository */
        $productAssignmentRepository = app(ProductAssignmentRepository::class);
        /** @var ProductRejectionRepository $productRejectionRepository */
        $productRejectionRepository = app(ProductRejectionRepository::class);

        $consumerProduct = $this->consumerProduct();
        $product = Product::tryFrom($consumerProduct->serviceProduct->product->name ?? "") ?? Product::LEAD;
        $proxyPhone = app(ConsumerProxyPhoneService::class)->getProxyPhone($this->companyId, $consumerProduct->address->state);

        $newProductAssignment = $productAssignmentRepository->createProductAssignment([
            ProductAssignment::FIELD_COMPANY_ID                 => $this->companyId,
            ProductAssignment::FIELD_CONSUMER_PRODUCT_ID        => $this->consumerProductId,
            ProductAssignment::FIELD_COST                       => $this->price,
            ProductAssignment::FIELD_CHARGEABLE                 => $this->chargeable,
            ProductAssignment::FIELD_DELIVERED                  => false,
            ProductAssignment::FIELD_EXCLUDE_BUDGET             => false,
            ProductAssignment::FIELD_SALE_TYPE_ID               => $this->salesTypeId,
            ProductAssignment::FIELD_CAMPAIGN_ID                => 0, // todo: this must either be 0 or null until removed
            ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID => 0, // todo: this must either be 0 or null until removed
            ProductAssignment::FIELD_BUDGET_ID                  => $this->budgetId,
            ProductAssignment::FIELD_REJECTION_EXPIRY           => $productRejectionRepository->getRejectionExpiry($product),
            ProductAssignment::FIELD_QUALITY_TIER_ID            => $this->qualityTierId,
            ProductAssignment::FIELD_SOLD_WHILE_UNDER_REVIEW    => $this->soldWhileUnderReview,
            ProductAssignment::FIELD_PROXY_PHONE                => $proxyPhone,
            ProductAssignment::FIELD_PROXY_PHONE_ACTIVE         => !! $proxyPhone,
        ]);

        if ($this->scheduleId)
            $this->handleScheduledAppointment($newProductAssignment);

        return $newProductAssignment;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return void
     * @throws Throwable
     */
    protected function handleScheduledAppointment(ProductAssignment $productAssignment): void
    {
        /** @var CompanyCampaignAppointmentService $appointmentService */
        $appointmentService = app(CompanyCampaignAppointmentService::class);
        /** @var CompanyCampaign $campaign */
        $campaign = CompanyCampaign::query()
            ->find($this->campaignId);

        $success = $appointmentService->createNewAppointmentDelivery($campaign, $productAssignment)
            && $appointmentService->addAppointmentToSchedule($this->scheduleId, $productAssignment);
        if (!$success)
            logger()->error("Failed to create/update schedule for AppointmentDelivery, ProductAssignment - " . $productAssignment->id);
    }
}
