<?php

namespace App\DataModels\FloorPrices;

use App\Models\Legacy\Location;
use App\Models\PricingMargin;
use Illuminate\Contracts\Support\Arrayable;

class SuggestedPriceDataModel implements Arrayable
{
    const float MINIMUM_EXCLUSIVE_PRICE = 35;
    const float MINIMUM_DUO_PRICE = 30;
    const float MINIMUM_TRIO_PRICE = 25;
    const float MINIMUM_QUAD_PRICE = 20;

    const float MAXIMUM_EXCLUSIVE_PRICE = 175;
    const float MAXIMUM_DUO_PRICE = 145;
    const float MAXIMUM_TRIO_PRICE = 125;
    const float MAXIMUM_QUAD_PRICE = 105;

    const float MINIMUM_EXCLUSIVE_PRICE_NO_ADD_COST = 60;
    const float MINIMUM_DUO_PRICE_NO_ADD_COST = 50;
    const float MINIMUM_TRIO_PRICE_NO_ADD_COST = 40;
    const float MINIMUM_QUAD_PRICE_NO_ADD_COST = 30;

    const string LOCATION = 'location';
    const string SUGGESTED_PRICE_EXCLUSIVE = 'suggested_price_exclusive';
    const string SUGGESTED_PRICE_DUO = 'suggested_price_duo';
    const string SUGGESTED_PRICE_TRIO = 'suggested_price_trio';
    const string SUGGESTED_PRICE_QUAD = 'suggested_price_quad';

    protected ?float $costPerLead = 0;

    protected ?float $suggestedPriceExclusive = null;
    protected ?float $suggestedPriceDuo = null;
    protected ?float $suggestedPriceTrio = null;
    protected ?float $suggestedPriceQuad = null;

    public function __construct(
        protected PricingMargin $pricingMargin,
        protected Location $location,
        protected float $adCost,
        protected int $goodToSellLeads
    ) {}

    /**
     * @return $this
     */
    public function calculatePrices(): static
    {
        $this->calculateBasePrice();
        $this->calculateExclusivePrice();
        $this->calculateDuoPrice();
        $this->calculateTrioPrice();
        $this->calculateQuadPrice();

        return $this;
    }

    /**
     * @return void
     */
    protected function calculateBasePrice(): void
    {
        if ($this->goodToSellLeads <= 0) {
            $this->costPerLead = $this->adCost;
            return;
        }

        $this->costPerLead = $this->adCost / $this->goodToSellLeads;
    }

    /**
     * @return void
     */
    protected function calculateExclusivePrice(): void
    {
        if ($this->adCost <= 0) {
            $this->suggestedPriceExclusive = self::MINIMUM_EXCLUSIVE_PRICE_NO_ADD_COST;
            return;
        }

        $suggestedPrice = $this->costPerLead * $this->calculateMargin($this->pricingMargin->exclusive_margin);
        $this->suggestedPriceExclusive = $this->processMaxMinPrice(self::MINIMUM_EXCLUSIVE_PRICE, $suggestedPrice, self::MAXIMUM_EXCLUSIVE_PRICE);
    }

    /**
     * @return void
     */
    protected function calculateDuoPrice(): void
    {
        if ($this->adCost <= 0) {
            $this->suggestedPriceDuo = self::MINIMUM_DUO_PRICE_NO_ADD_COST;
            return;
        }

        $suggestedPrice = $this->costPerLead * $this->calculateMargin($this->pricingMargin->duo_margin);
        $this->suggestedPriceDuo = $this->processMaxMinPrice(self::MINIMUM_DUO_PRICE, $suggestedPrice, self::MAXIMUM_DUO_PRICE);
    }

    /**
     * @return void
     */
    protected function calculateTrioPrice(): void
    {
        if ($this->adCost <= 0) {
            $this->suggestedPriceTrio = self::MINIMUM_TRIO_PRICE_NO_ADD_COST;
            return;
        }

        $suggestedPrice = $this->costPerLead * $this->calculateMargin($this->pricingMargin->trio_margin);
        $this->suggestedPriceTrio = $this->processMaxMinPrice(self::MINIMUM_TRIO_PRICE, $suggestedPrice, self::MAXIMUM_TRIO_PRICE);
    }

    /**
     * @return void
     */
    protected function calculateQuadPrice(): void
    {
        if ($this->adCost <= 0) {
            $this->suggestedPriceQuad = self::MINIMUM_QUAD_PRICE_NO_ADD_COST;
            return;
        }

        $suggestedPrice = $this->costPerLead * $this->calculateMargin($this->pricingMargin->quad_margin);
        $this->suggestedPriceQuad = $this->processMaxMinPrice(self::MINIMUM_QUAD_PRICE, $suggestedPrice, self::MAXIMUM_QUAD_PRICE);
    }

    /**
     * @param float $minPrice
     * @param float $price
     * @param float $maxPrice
     *
     * @return float
     */
    protected function processMaxMinPrice(float $minPrice, float $price, float $maxPrice): float
    {
        if ($price < $minPrice) {
            return $minPrice;
        }

        if ($price > $maxPrice) {
            return $maxPrice;
        }

        return $price;
    }

    /**
     * @param float $percentMargin
     *
     * @return float
     */
    protected function calculateMargin(float $percentMargin): float
    {
        return $percentMargin / 100;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::LOCATION                  => $this->location->toArray(),
            self::SUGGESTED_PRICE_EXCLUSIVE => round($this->suggestedPriceExclusive),
            self::SUGGESTED_PRICE_DUO       => round($this->suggestedPriceDuo),
            self::SUGGESTED_PRICE_TRIO      => round($this->suggestedPriceTrio),
            self::SUGGESTED_PRICE_QUAD      => round($this->suggestedPriceQuad),
        ];
    }
}
