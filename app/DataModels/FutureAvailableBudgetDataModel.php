<?php

namespace App\DataModels;


use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use Exception;
use Illuminate\Support\Collection;

class FutureAvailableBudgetDataModel
{
    const string COMPANY_ID        = 'companyId';
    const string LEGACY_COMPANY_ID = 'legacyCompanyId';
    const string CAMPAIGN_ID       = 'campaignId';
    const string TYPE              = 'type';
    const string VALUE             = 'value';

    public Collection $campaigns;

    public function __construct(
        public int $campaignCount,
        public int $unlimitedCampaignCount
    )
    {
        $this->campaigns = collect();
    }

    /**
     * @param EloquentCompany $company
     * @param int $campaignId
     * @return $this
     */
    public function addUnlimitedCampaign(EloquentCompany $company, int $campaignId): FutureAvailableBudgetDataModel
    {
        $this->campaignCount++;
        $this->unlimitedCampaignCount++;

        $legacyCompanyId = $company->{EloquentCompany::ID} ?? null;
        $odinCompanyId   = $company->{EloquentCompany::RELATION_MI_COMPANY}?->{Company::FIELD_ID} ?? null;

        $this->campaigns->push(new CampaignValueModel($campaignId, CampaignValueModel::TYPE_UNLIMITED, 0, $odinCompanyId, $legacyCompanyId));

        return $this;
    }
}
