<?php

namespace App\DataModels;

use App\Enums\CompanyConsolidatedStatus;
use ArrayIterator;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Exception;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use IteratorAggregate;

class HistoricalCompanyDailyStatusesDataModel implements Jsonable, IteratorAggregate
{
    const DATE_FORMAT = 'Y-m-d';

    private Collection $data;

    public function __construct(

    ) {
        $this->data = collect();
    }

    public function setDayStatus(Carbon|CarbonImmutable $date, CompanyConsolidatedStatus $status): bool
    {
        $this->data->put($date->format(self::DATE_FORMAT), $status->value);

        return true;
    }

    public function getDayStatus(Carbon|CarbonImmutable $date): ?int
    {
        return $this->data->get($date->format(self::DATE_FORMAT));
    }

    /**
     * @return Collection
     */
    public function toCollection(): Collection
    {
        return $this->data;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->data->toArray();
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * @param string $json
     * @return HistoricalCompanyDailyStatusesDataModel
     * @throws Exception
     */
    public static function fromJson(string $json): HistoricalCompanyDailyStatusesDataModel
    {
        return self::fromArray(json_decode($json, true));
    }

    /**
     * @param array $statusesArray
     * @return HistoricalCompanyDailyStatusesDataModel
     */
    public static function fromArray(array $statusesArray): HistoricalCompanyDailyStatusesDataModel
    {
        $hcsdm = new HistoricalCompanyDailyStatusesDataModel();

        foreach($statusesArray as $date => $status) {
            $hcsdm->setDayStatus(Carbon::createFromFormat(self::DATE_FORMAT, $date), CompanyConsolidatedStatus::from($status));
        }

        return $hcsdm;
    }

    /**
     * @inheritDoc
     */
    public function getIterator(): ArrayIterator
    {
        return $this->data->getIterator();
    }
}
