<?php

namespace App\DataModels;

use App\Enums\CompanySalesStatus;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Exception;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class HistoricalCompanySalesStatusDataModel implements Jsonable
{
    const HAS_DECISION_MAKER = 'hdm';
    const SALES_STATUS = 'ss';

    const DATE_FORMAT = 'Y-m-d';

    private Collection $data;

    public function __construct()
    {
        $this->data = collect();
    }

    public function setDaySalesStatus(Carbon|CarbonInterface $date, CompanySalesStatus $companySalesStatus, bool $hasDecisionMaker): bool
    {
        $this->data->put($date->format(self::DATE_FORMAT), [self::SALES_STATUS => $companySalesStatus->value, self::HAS_DECISION_MAKER => (int) $hasDecisionMaker]);

        return true;
    }

    public function getDaySalesStatus(Carbon|CarbonInterface $date): array
    {
        return $this->data->get($date->format(self::DATE_FORMAT));
    }

    public function toArray(): array
    {
        return $this->data->toArray();
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    public static function fromJson(string $json): HistoricalCompanySalesStatusDataModel
    {
        $arrayData = json_decode($json, true);

        $hcssdm = new HistoricalCompanySalesStatusDataModel();

        foreach($arrayData as $date => $salesStatusInfo) {
            $hcssdm->setDaySalesStatus(Carbon::createFromFormat(self::DATE_FORMAT, $date), CompanySalesStatus::from($salesStatusInfo[self::SALES_STATUS]), $salesStatusInfo[self::HAS_DECISION_MAKER]);
        }

        return $hcssdm;
    }
}
