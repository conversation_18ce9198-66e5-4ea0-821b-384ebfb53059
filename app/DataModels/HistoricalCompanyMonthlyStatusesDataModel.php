<?php

namespace App\DataModels;

use App\Enums\CompanyConsolidatedStatus;
use ArrayIterator;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use IteratorAggregate;

class HistoricalCompanyMonthlyStatusesDataModel implements Jsonable, IteratorAggregate
{
    const DATE_FORMAT = 'Y-m';

    private Collection $data;

    public function __construct(

    ) {
        $this->data = collect();
    }

    public function setMonthStatus(Carbon $date, CompanyConsolidatedStatus $status): bool
    {
        $this->data->put($date->format(self::DATE_FORMAT), $status->value);

        return true;
    }

    public function getMonthStatus(Carbon $date): ?int
    {
        return $this->data->get($date->format(self::DATE_FORMAT));
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->data->toArray();
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * @param string $json
     * @return HistoricalCompanyMonthlyStatusesDataModel
     */
    public static function fromJson(string $json): HistoricalCompanyMonthlyStatusesDataModel
    {
        return self::fromArray(json_decode($json, true));
    }

    /**
     * @param array $statusesArray
     * @return HistoricalCompanyMonthlyStatusesDataModel
     */
    public static function fromArray(array $statusesArray): HistoricalCompanyMonthlyStatusesDataModel
    {
        $hcsdm = new HistoricalCompanyMonthlyStatusesDataModel();

        foreach($statusesArray as $date => $status) {
            $hcsdm->setMonthStatus(Carbon::createFromFormat(self::DATE_FORMAT, $date), CompanyConsolidatedStatus::from($status));
        }

        return $hcsdm;
    }

    /**
     * @inheritDoc
     */
    public function getIterator(): ArrayIterator
    {
        return $this->data->getIterator();
    }
}
