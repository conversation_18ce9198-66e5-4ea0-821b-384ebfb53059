<?php

namespace App\DataModels\Workflows;

class TaskNotifiersDataModel
{
    public function __construct(protected int $templateId, protected int $delay) {}

    /**
     * Returns the workflow template id to be run.
     *
     * @return int
     */
    public function getTemplateId(): int
    {
        return $this->templateId;
    }

    /**
     * Returns the delay before a user should be notified, in minutes.
     *
     * @return int
     */
    public function getDelay(): int
    {
        return $this->delay;
    }
}
