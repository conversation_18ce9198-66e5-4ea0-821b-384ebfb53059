<?php

namespace App\DataModels\Workflows;

use App\Enums\TaskResultType;
use Illuminate\Contracts\Support\Jsonable;

class TaskResultDataModel implements Jsonable
{
    const RESULT_ID = 'id';
    const TASK_NAME = 'name';
    const TASK_TYPE = 'type';
    const OPTIONS   = 'options';

    /**
     * @param int $id
     * @param string $name
     * @param TaskResultType $type
     * @param string[] $options
     */
    public function __construct(
        public int $id,
        public string $name,
        public TaskResultType $type,
        public array $options = []
    ) {
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::RESULT_ID => $this->id,
            self::TASK_NAME => $this->name,
            self::TASK_TYPE => $this->type,
            self::OPTIONS   => $this->options,
        ];
    }

    public function toJson($options = 0): ?string
    {
        return json_encode($this->toArray());
    }

    public static function fromJson(string $json): TaskResultDataModel
    {
        $object = json_decode($json, true);

        return new TaskResultDataModel(
            $object['id'],
            $object['name'],
            TaskResultType::from($object['type']),
            collect($object['options'])->toArray()
        );
    }
}
