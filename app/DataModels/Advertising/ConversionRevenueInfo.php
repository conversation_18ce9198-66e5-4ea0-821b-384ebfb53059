<?php

namespace App\DataModels\Advertising;

use ArrayIterator;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use IteratorAggregate;

class ConversionRevenueInfo implements IteratorAggregate, Arrayable
{
    const CONSUMER_PRODUCT_TRACKING_ID = 'consumer_product_tracking_id';
    const TRACK_TYPE = 'track_type';
    const TRACK_CODE = 'track_code';
    const TRACK_PAYLOAD = 'track_payload';
    const TRACK_CREATION_TIMESTAMP = 'track_creation_timestamp';
    const REVENUE = 'revenue';
    const DELIVERED_TIMESTAMP = 'delivered_timestamp';
    const REJECTED_TIMESTAMP = 'rejected_timestamp';
    const ORIGINAL_UPLOAD_TIMESTAMP = 'original_upload_timestamp';
    const PRODUCT_ASSIGNMENT_IDS = 'product_assignments_ids';
    const PRODUCT_REJECTION_IDS = 'product_rejection_ids';
    const CONVERSION_UPLOADED_BEFORE = 'conversion_uploaded_before';
    const CONVERSION_URL = 'conversion_url';

    private Collection $data;

    public function __construct()
    {
        $this->data = collect();
    }

    /**
     * @param int $consumerProductTrackingId
     * @param string $trackType
     * @param string $trackCode
     * @param array $trackPayload
     * @param int $trackCreationTimestamp
     * @param float $revenue
     * @param int $deliveredTimestamp
     * @param int $rejectedTimestamp
     * @param int $originalUploadTimestamp
     * @param array $productAssignmentIds
     * @param array $productRejectionIds
     * @param bool $conversionUploadedBefore
     * @param string $conversionUrl
     * @return bool
     */
    public function set(
        int $consumerProductTrackingId,
        string $trackType,
        string $trackCode,
        array $trackPayload = [],
        int $trackCreationTimestamp = 0,
        float $revenue = 0.00,
        int $deliveredTimestamp = 0,
        int $rejectedTimestamp = 0,
        int $originalUploadTimestamp = 0,
        array $productAssignmentIds = [],
        array $productRejectionIds = [],
        bool $conversionUploadedBefore = false,
        string $conversionUrl = ''
    ): bool
    {
        $this->data->put(
            $consumerProductTrackingId,
            collect([
                self::CONSUMER_PRODUCT_TRACKING_ID => $consumerProductTrackingId,
                self::TRACK_TYPE => $trackType,
                self::TRACK_CODE => $trackCode,
                self::TRACK_PAYLOAD => $trackPayload,
                self::TRACK_CREATION_TIMESTAMP => $trackCreationTimestamp,
                self::REVENUE => round($revenue, 2),
                self::DELIVERED_TIMESTAMP => $deliveredTimestamp,
                self::REJECTED_TIMESTAMP => $rejectedTimestamp,
                self::ORIGINAL_UPLOAD_TIMESTAMP => $originalUploadTimestamp,
                self::PRODUCT_ASSIGNMENT_IDS => array_map(fn($id) => (int) $id, $productAssignmentIds),
                self::PRODUCT_REJECTION_IDS => array_map(fn($id) => (int) $id, $productRejectionIds),
                self::CONVERSION_UPLOADED_BEFORE => $conversionUploadedBefore,
                self::CONVERSION_URL => $conversionUrl
            ])
        );

        return true;
    }

    /**
     * @param int $consumerProductTrackingId
     * @return Collection|null
     */
    public function get(int $consumerProductTrackingId): ?Collection
    {
        return $this->data->get($consumerProductTrackingId);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return $this->data->recursiveToArray();
    }

    /**
     * @inheritDoc
     */
    public function getIterator(): ArrayIterator
    {
        return $this->data->getIterator();
    }

    /**
     * @param ConversionRevenueInfo $data
     * @return self
     */
    public function concat(self $data): self
    {
        foreach($data as $trackingId => $trackingData) {
            $this->data->put($trackingId, $trackingData);
        }

        return $this;
    }

    /**
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->data->isEmpty();
    }
}
