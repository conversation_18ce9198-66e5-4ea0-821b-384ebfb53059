<?php

namespace App\DataModels\Advertising;

class AdCampaignsPaginatorDataModel
{
    /**
     * @param int $currentPage
     * @param int $perPage
     * @param int $lastPage
     * @param int $from
     * @param int $to
     * @param int $total
     * @param array $data
     * @param array $links
     * @param string $path
     * @param string $firstPageUrl
     * @param string $lastPageUrl
     * @param string $nextPageUrl
     * @param string $prevPageUrl
     */
    public function __construct(
        private int $currentPage = 1,
        private int $perPage = 1,
        private int $lastPage = 1,
        private int $from = 1,
        private int $to = 1,
        private int $total = 1,
        private array $data = [],
        private array $links = [],
        private string $path = '',
        private string $firstPageUrl = '',
        private string $lastPageUrl = '',
        private string $nextPageUrl = '',
        private string $prevPageUrl = ''
    ) {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            'current_page' => $this->currentPage,
            'data' => $this->data,
            'first_page_url' => $this->firstPageUrl,
            'from' => $this->from,
            'last_page' => $this->lastPage,
            'last_page_url' => $this->lastPageUrl,
            'links' => $this->links,
            'next_page_url' => $this->nextPageUrl,
            'path' => $this->path,
            'per_page' => $this->perPage,
            'prev_page_url' => $this->prevPageUrl,
            'to' => $this->to,
            'total' => $this->total,
        ];
    }

    /**
     * @param string $name
     * @return mixed
     */
    public function __get(string $name): mixed
    {
        return $this->{$name} ?? null;
    }

    /**
     * @param string $name
     * @param mixed $value
     */
    public function __set(string $name, mixed $value): void
    {
        $this->{$name} = $value;
    }
}
