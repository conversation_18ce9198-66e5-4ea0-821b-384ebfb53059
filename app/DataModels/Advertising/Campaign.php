<?php

namespace App\DataModels\Advertising;

use App\Models\AdvertisingCampaign;
use App\Models\AdvertisingCampaignAutomationParameter;
use Illuminate\Support\Collection;
use Exception;

class Campaign
{
    const AUTOMATION_PARAMETERS = 'automationParameters';
    const LOCATIONS = 'locations';
    const ADDITIONAL_PARAMETERS = 'additionalParameters';

    public function __construct(
        private AdvertisingCampaign $advertisingCampaign,
        private Collection $automationParameters,
        private Collection $locations,
        private Collection $additionalParameters
    ) {}

    /**
     * @param AdvertisingCampaignAutomationParameter $automationParameter
     * @return bool
     */
    public function addAutomationParameter(AdvertisingCampaignAutomationParameter $automationParameter): bool
    {
        $this->automationParameters->push($automationParameter);

        return true;
    }

    /**
     * @param string $platformLocationId
     * @param int $locationId
     * @return bool
     */
    public function addLocation(string $platformLocationId, int $locationId): bool
    {
        $this->locations->put($platformLocationId, $locationId);

        return true;
    }

    /**
     * @param array $locations
     * @return bool
     */
    public function setLocations(array $locations): bool
    {
        $this->locations = collect($locations);

        return true;
    }

    public function setAdditionalTargetingParameters(array $additionalParameters): bool
    {
        $this->additionalParameters = collect($additionalParameters);

        return true;
    }

    /**
     * @throws Exception
     */
    public function __get(string $name): mixed
    {
        $advertisingCampaignKeys = [
            AdvertisingCampaign::FIELD_ID,
            AdvertisingCampaign::FIELD_PLATFORM,
            AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID,
            AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID,
        ];

        $currentModelKeys = [
            self::AUTOMATION_PARAMETERS,
            self::LOCATIONS,
            self::ADDITIONAL_PARAMETERS
        ];

        $allowedKeys = array_merge($advertisingCampaignKeys, $currentModelKeys);

        if(!in_array($name, $allowedKeys)) {
            throw new Exception("'{$name}' property does not exist in Campaign data model");
        }

        if(in_array($name, $advertisingCampaignKeys)) {
            return $this->advertisingCampaign->{$name};
        }
        else {
            return $this->{$name};
        }
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            "advertisingCampaign" => $this->advertisingCampaign->toArray(),
            self::LOCATIONS => $this->locations->toArray(),
            self::AUTOMATION_PARAMETERS => $this->automationParameters->toArray()
        ];
    }
}
