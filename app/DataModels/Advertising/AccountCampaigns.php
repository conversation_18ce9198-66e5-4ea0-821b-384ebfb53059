<?php

namespace App\DataModels\Advertising;

use App\Models\AdvertisingCampaignAutomationParameter;
use ArrayIterator;
use Exception;
use Illuminate\Support\Collection;
use IteratorAggregate;

class AccountCampaigns implements IteratorAggregate
{
    private Collection $accountCampaigns;

    public function __construct() {
        $this->accountCampaigns = collect();
    }

    /**
     * @param string $accountId
     * @param string $platformCampaignId
     * @param Campaign $campaign
     * @return bool
     */
    public function setAccountCampaign(string $accountId, string $platformCampaignId, Campaign $campaign): bool
    {
        if(empty($this->accountCampaigns->get($accountId))) {
            $this->accountCampaigns->put($accountId, collect());
        }

        $this->accountCampaigns->get($accountId)->put($platformCampaignId, $campaign);

        return true;
    }

    /**
     * @param string $accountId
     * @param string $platformCampaignId
     * @param AdvertisingCampaignAutomationParameter $automationParameter
     * @return bool
     * @throws Exception
     */
    public function addAutomationParameter(string $accountId, string $platformCampaignId, AdvertisingCampaignAutomationParameter $automationParameter): bool
    {
        if(empty($this->accountCampaigns->get($accountId)?->get($platformCampaignId))) {
            throw new Exception("Campaign $platformCampaignId does not exist");
        }

        $this->accountCampaigns->get($accountId)->get($platformCampaignId)->addAutomationParameter($automationParameter);

        return true;
    }

    /**
     * @param string $accountId
     * @return Collection
     */
    public function get(string $accountId): Collection
    {
        return $this->accountCampaigns->get($accountId, collect());
    }

    /**
     * @return Collection
     */
    public function getAccountCampaigns(): Collection
    {
        return $this->accountCampaigns;
    }

    public function getCampaignsAdditionalParameters(): Collection
    {
        $additionalParameters = collect();

        foreach($this->accountCampaigns as $accountId => $campaigns) {
            $additionalParameters->put($accountId, collect());

            foreach($campaigns as $campaignId => $campaign) {
                $additionalParameters->get($accountId)->put($campaignId, $campaign->{Campaign::ADDITIONAL_PARAMETERS});
            }
        }

        return $additionalParameters;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        $outputArray = [];

        foreach($this->accountCampaigns as $accountId => $ac) {
            $outputArray[$accountId] = $ac->map(function($campaign) {
                return $campaign->toArray();
            });
        }

        return $outputArray;
    }

    /**
     * @return ArrayIterator
     */
    public function getIterator(): ArrayIterator
    {
        return $this->accountCampaigns->getIterator();
    }
}
