<?php

namespace App\DataModels\Advertising;

use ArrayIterator;
use Illuminate\Support\Collection;
use IteratorAggregate;

class CampaignLocations implements IteratorAggregate
{
    private Collection $campaignLocations;
    private Collection $additionalParameters;

    public function __construct() {
        $this->campaignLocations = collect();
        $this->additionalParameters = collect();
    }

    /**
     * @param string $accountId
     * @param string $platformCampaignId
     * @param string $platformLocationId
     * @param bool $exclude
     * @return bool
     */
    public function setCampaignLocation(string $accountId, string $platformCampaignId, string $platformLocationId, bool $exclude): bool
    {
        if(empty($this->campaignLocations->get($accountId))) {
            $this->campaignLocations->put($accountId, collect());
        }
        if(empty($this->campaignLocations->get($accountId)->get($platformCampaignId))) {
            $this->campaignLocations->get($accountId)->put($platformCampaignId, collect());
        }

        $this->campaignLocations->get($accountId)->get($platformCampaignId)->put($platformLocationId, $exclude);

        return true;
    }

    /**
     * @param string|null $accountId
     * @param string|null $platformCampaignId
     * @param string|null $platformLocationId
     * @return mixed
     */
    public function searchByIds(?string $accountId = null, ?string $platformCampaignId = null, ?string $platformLocationId = null): mixed
    {
        $returnValue = $this->campaignLocations;

        if($accountId) {
            $returnValue = $returnValue->get($accountId);

            if($platformCampaignId) {
                $returnValue = $returnValue->get($platformCampaignId);

                if($platformLocationId) {
                    $returnValue = $returnValue->get($platformLocationId);
                }
            }
        }

        return $returnValue;
    }

    public function setAdditionalParameters(array $additionalParameters): bool
    {
        $this->additionalParameters = collect($additionalParameters);

        return true;
    }

    public function getAdditionalParameters(): Collection
    {
        return $this->additionalParameters;
    }

    public function getIterator(): ArrayIterator
    {
        return $this->campaignLocations->getIterator();
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        $outputArray = [];

        foreach($this->campaignLocations as $accountId => $campaigns) {
            $outputArray[$accountId] = [];

            foreach($campaigns as $campaignId => $locations) {
                $outputArray[$accountId][$campaignId] = $locations->toArray();
            }
        }

        return $outputArray;
    }
}
