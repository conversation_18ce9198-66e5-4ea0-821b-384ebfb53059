<?php

namespace App\DataModels\Advertising;

use App\Models\Legacy\Location;
use App\Models\LockedAdvertisingCampaignLocation;
use Exception;
use JetBrains\PhpStorm\ArrayShape;

class DashboardTargetLocation
{
    const LOCATION_ID = 'locationId';
    const LOCATION_TYPE = 'locationType';
    const NAME = 'name';
    const LOCKED = 'locked';

    private int $locationId;
    private ?string $locationType;
    private string $name;
    private int $locked;

    /**
     * @param int $locationId
     * @param string $name
     * @param int $locked
     * @param string|null $locationType
     * @throws Exception
     */
    public function __construct(
        int $locationId,
        string $name,
        int $locked,
        ?string $locationType = null
    )
    {
        $validLocation = $this->validate(self::LOCATION_ID, $locationId);
        $validLocationType = $this->validate(self::LOCATION_TYPE, $locationType);
        $validName = $this->validate(self::NAME, $name);
        $validLocked = $this->validate(self::LOCKED, $locked);

        if(!$validLocation) {
            throw new Exception(__METHOD__.": Invalid value for location ID: $locationId");
        }
        if(!$validLocationType) {
            throw new Exception(__METHOD__.": Invalid value for location type: $locationType");
        }
        if(!$validName) {
            throw new Exception(__METHOD__.": Invalid value for name: $name");
        }
        if(!$validLocked) {
            throw new Exception(__METHOD__.": Invalid value for locked: $locked");
        }

        $this->{self::LOCATION_ID} = $locationId;
        $this->{self::LOCATION_TYPE} = $locationType;
        $this->{self::NAME} = $name;
        $this->{self::LOCKED} = $locked;
    }

    /**
     * @throws Exception
     */
    public function __get(string $name): mixed
    {
        if(!in_array($name, [self::LOCATION_ID, self::LOCATION_TYPE, self::LOCKED, self::NAME])) {
            throw new Exception("Invalid property: $name");
        }

        return $this->{$name};
    }

    /**
     * @throws Exception
     */
    public function __set(string $name, $value)
    {
        if(!$this->validate($name, $value)) {
            throw new Exception(__METHOD__.": Invalid value for $name: $value");
        }

        $this->{$name} = $value;
    }

    /**
     * @param string $name
     * @param $value
     * @return bool
     */
    private function validate(string $name, $value): bool
    {
        $valid = false;

        switch($name) {
            case self::LOCATION_ID:
                $valid = $value >= 0;
                break;
            case self::LOCATION_TYPE:
                $valid = in_array($value, Location::TYPES, true) || $value === null;
            case self::NAME:
                $valid = true; //it just needs to be a string, which it should already be
                break;
            case self::LOCKED:
                $valid = in_array(
                    $value,
                    [LockedAdvertisingCampaignLocation::TARGETED_NONE, LockedAdvertisingCampaignLocation::TARGETED_INCLUDE, LockedAdvertisingCampaignLocation::TARGETED_EXCLUDE],
                    true
                );
                break;
        }

        return $valid;
    }

    /**
     * @return array
     */
    #[ArrayShape([self::LOCATION_ID => "mixed", self::LOCATION_TYPE => "mixed", self::NAME => "mixed", self::LOCKED => "mixed"])]
    public function toArray(): array
    {
        return [
            self::LOCATION_ID => $this->{self::LOCATION_ID},
            self::LOCATION_TYPE => $this->{self::LOCATION_TYPE},
            self::NAME => $this->{self::NAME},
            self::LOCKED => $this->{self::LOCKED}
        ];
    }
}
