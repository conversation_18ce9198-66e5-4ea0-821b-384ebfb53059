<?php

namespace App\DataModels;

use App\Contracts\Workflows\HasTargetContract;
use App\Enums\Target as TargetType;
use App\Enums\TargetRelation;

class Target implements HasTargetContract
{
    public function __construct(protected int|string $targetId, protected TargetType $targetType, protected ?TargetRelation $relation = null) { }

    /**
     * @inheritDoc
     */
    public function getTarget(): int|string
    {
        return $this->targetId;
    }

    /**
     * @inheritDoc
     */
    public function getTargetType(): TargetType
    {
        return $this->targetType;
    }

    /**
     * @return TargetRelation|null
     */
    public function getRelationType(): ?TargetRelation
    {
        return $this->relation;
    }
}
