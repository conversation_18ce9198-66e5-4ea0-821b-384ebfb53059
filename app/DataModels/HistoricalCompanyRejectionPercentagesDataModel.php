<?php

namespace App\DataModels;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Exception;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use App\Enums\Odin\HistoricalCompanyRejectionPercentageType as RPT;

class HistoricalCompanyRejectionPercentagesDataModel implements Jsonable
{
    const DATE_FORMAT = 'Y-m-d';

    private Collection $data;

    public function __construct()
    {
        $this->data = collect();
    }

    /**
     * @param float $rejectionPercentage
     * @param Carbon|CarbonInterface $date
     * @param RPT $type
     * @return bool
     * @throws Exception
     */
    public function setRejectionPercentage(float $rejectionPercentage, Carbon|CarbonInterface $date, RPT $type): bool
    {
        if($rejectionPercentage < 0) {
            throw new Exception("Invalid rejectionPercentage");
        }

        if(empty($this->data->get($date->format(self::DATE_FORMAT)))) {
            $this->data->put(
                $date->format(self::DATE_FORMAT),
                collect([
                    RPT::OVERALL->value => 0,
                    RPT::MANUAL->value => 0,
                    RPT::CRM->value => 0
                ])
            );
        }

        $this->data->get($date->format(self::DATE_FORMAT))->put($type->value, $rejectionPercentage);

        return true;
    }

    /**
     * @param Carbon|CarbonInterface $date
     * @param RPT|null $type
     * @return float|Collection|null
     * @throws Exception
     */
    public function getRejectionPercentage(Carbon|CarbonInterface $date, ?RPT $type = null): float|Collection|null
    {
        if($type === null) {
            return $this->data->get($date->format(self::DATE_FORMAT));
        }
        else {
            return $this->data->get($date->format(self::DATE_FORMAT))?->get($type->value);
        }
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        $outputArray = [];
        foreach($this->data as $date => $rps) {
            $outputArray[$date] = $rps->toArray();
        }

        return $outputArray;
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    /**
     * @param string $json
     * @return self
     */
    public static function fromJson(string $json): self
    {
        $arrayData = json_decode($json, true);

        $hcrpdm = app(HistoricalCompanyRejectionPercentagesDataModel::class);

        $rpTypes = RPT::casesList();

        foreach($arrayData as $date => $rejectionPercentageTypes) {
            $carbonDate = Carbon::createFromFormat(self::DATE_FORMAT, $date);

            foreach($rejectionPercentageTypes as $type => $rp) {
                $hcrpdm->setRejectionPercentage($rp, $carbonDate, $rpTypes[$type]);
            }
        }

        return $hcrpdm;
    }
}
