<?php

namespace App\DataModels;

use App\Contracts\Workflows\HasEmailTargetContract;

class EmailTarget implements HasEmailTargetContract
{
    public function __construct(protected string $toAddress, protected string $fromAddress){}

    /**
     * @inheritDoc
     */
    public function getToAddress(): string

    {
        return $this->toAddress;
    }

    /**
     * @inheritDoc
     */
    public function getFromAddress(): string
    {
        return $this->fromAddress;
    }
}
