<?php

namespace App\DataModels;

use App\Enums\DateFormatOption;
use App\Enums\Logical;
use App\Enums\Operator;
use App\Exceptions\ValidatorException;
use Carbon\Carbon;
use Exception;

class AmountOfLeadsPurchasedObject
{
    const PROPERTY_ACTIVE = 'active';
    const PROPERTY_FIRST_VALUE = 'first_value';
    const PROPERTY_SECOND_VALUE = 'second_value';
    const PROPERTY_FIRST_OPERATOR = 'first_operator';
    const PROPERTY_SECOND_OPERATOR = 'second_operator';
    const PROPERTY_LOGICAL = 'logical';
    const PROPERTY_FIRST_DATE_TOGGLED = 'first_date_toggled';
    const PROPERTY_SECOND_DATE_TOGGLED = 'second_date_toggled';
    const PROPERTY_FIRST_FROM_DATE = 'first_from_date';
    const PROPERTY_FIRST_TO_DATE = 'first_to_date';
    const PROPERTY_SECOND_FROM_DATE = 'second_from_date';
    const PROPERTY_SECOND_TO_DATE = 'second_to_date';
    const PROPERTY_DATE_FORMAT = 'date_format';

    public bool $active = false;
    public bool $firstDateToggled = false;
    public bool $secondDateToggled = false;
    public int $firstValue;
    public ?int $secondValue = null;
    public ?Carbon $firstFromDate = null;
    public ?Carbon $firstToDate = null;
    public ?Carbon $secondFromDate = null;
    public ?Carbon $secondToDate = null;
    public Operator $firstOperator;
    public ?Operator $secondOperator = null;
    public ?Logical $logical = null;
    public ?DateFormatOption $dateFormatOption = null;

    /**
     * @param  array  $array
     * @return void
     */
    public function setProperties(array $array): void
    {
        $this->setDateFormatOption($array[self::PROPERTY_DATE_FORMAT] ?? null);
        $this->setActive($array[self::PROPERTY_ACTIVE] ?? null);
        $this->setFirstDateToggled($array[self::PROPERTY_FIRST_DATE_TOGGLED] ?? null);
        $this->setSecondDateToggled($array[self::PROPERTY_SECOND_DATE_TOGGLED] ?? null);
        $this->setFirstValue($array[self::PROPERTY_FIRST_VALUE] ?? null);
        $this->setSecondValue($array[self::PROPERTY_SECOND_VALUE] ?? null);
        $this->setFirstFromDate($array[self::PROPERTY_FIRST_FROM_DATE] ?? null);
        $this->setFirstToDate($array[self::PROPERTY_FIRST_TO_DATE] ?? null);
        $this->setSecondFromDate($array[self::PROPERTY_SECOND_FROM_DATE] ?? null);
        $this->setSecondToDate($array[self::PROPERTY_SECOND_TO_DATE] ?? null);
        $this->setFirstOperator($array[self::PROPERTY_FIRST_OPERATOR] ?? null);
        $this->setSecondOperator($array[self::PROPERTY_SECOND_OPERATOR] ?? null);
        $this->setLogical($array[self::PROPERTY_LOGICAL] ?? null);
    }

    /**
     * @param  mixed  $dateFormatOption
     */
    public function setDateFormatOption(mixed $dateFormatOption): void
    {
        $dateFormatOption = $dateFormatOption instanceof DateFormatOption ? $dateFormatOption : DateFormatOption::tryFrom($dateFormatOption);

        $this->dateFormatOption = $dateFormatOption;
    }

    /**
     * @param  mixed  $active
     */
    public function setActive(mixed $active): void
    {
        $this->active = $this->parseBoolean($active);
    }

    private function parseBoolean($arg)
    {
        if (gettype($arg) === 'boolean') {
            return $arg;
        } else {
            return filter_var($arg, FILTER_VALIDATE_BOOLEAN);
        }
    }

    /**
     * @param  mixed  $firstDateToggled
     */
    public function setFirstDateToggled(mixed $firstDateToggled): void
    {
        $this->firstDateToggled = $this->parseBoolean($firstDateToggled);
    }

    /**
     * @param  mixed  $secondDateToggled
     */
    public function setSecondDateToggled(mixed $secondDateToggled): void
    {
        $this->secondDateToggled = $this->parseBoolean($secondDateToggled);
    }

    /**
     * @param  mixed  $firstValue
     */
    public function setFirstValue(mixed $firstValue): void
    {
        $this->firstValue = intval($firstValue);
    }

    /**
     * @param  mixed  $secondValue
     */
    public function setSecondValue(mixed $secondValue): void
    {
        $this->secondValue = intval($secondValue);
    }

    /**
     * @param  mixed  $firstFromDate
     */
    public function setFirstFromDate(mixed $firstFromDate): void
    {
        $this->firstFromDate = $this->parseDateAsCarbon($firstFromDate, $this->dateFormatOption);
    }

    /**
     * @param $date
     * @param $dateFormatOption
     * @return ?Carbon
     */
    private function parseDateAsCarbon($date, $dateFormatOption): ?Carbon
    {
        $dateFormatOption = $dateFormatOption instanceof DateFormatOption ? $dateFormatOption : DateFormatOption::tryFrom($dateFormatOption);

        if (is_null($date)) {
            return null;
        }

        if (is_numeric($date)) {
            $date = intval($date);

            match ($dateFormatOption) {
                DateFormatOption::TIMESTAMP => $date = Carbon::createFromTimestamp($date),
                DateFormatOption::TIMESTAMP_MILLISECONDS => $date = Carbon::createFromTimestampMs($date),
                default => $date = null,
            };

            return $date;
        }

        if (!$date instanceof Carbon) {
            match ($dateFormatOption) {
                DateFormatOption::STRING => $date = Carbon::parse($date),
                DateFormatOption::TIMESTAMP => $date = Carbon::createFromTimestamp($date),
                DateFormatOption::TIMESTAMP_MILLISECONDS => $date = Carbon::createFromTimestampMs($date),
                default => $date = null,
            };
        }

        return $date;
    }

    /**
     * @param  mixed  $firstToDate
     */
    public function setFirstToDate(mixed $firstToDate): void
    {
        $this->firstToDate = $this->parseDateAsCarbon($firstToDate, $this->dateFormatOption);
    }

    /**
     * @param  mixed  $secondFromDate
     */
    public function setSecondFromDate(mixed $secondFromDate): void
    {
        $this->secondFromDate = $this->parseDateAsCarbon($secondFromDate, $this->dateFormatOption);
    }

    /**
     * @param  mixed  $secondToDate
     */
    public function setSecondToDate(mixed $secondToDate): void
    {
        $this->secondToDate = $this->parseDateAsCarbon($secondToDate, $this->dateFormatOption);
    }

    /**
     * @param  mixed  $firstOperator
     */
    public function setFirstOperator(mixed $firstOperator): void
    {
        $firstOperator = $firstOperator instanceof Operator ? $firstOperator : Operator::tryFrom($firstOperator);

        $this->firstOperator = $firstOperator;
    }

    /**
     * @param  mixed  $secondOperator
     */
    public function setSecondOperator(mixed $secondOperator): void
    {
        $secondOperator = $secondOperator instanceof Operator ? $secondOperator : Operator::tryFrom($secondOperator);

        $this->secondOperator = $secondOperator;
    }

    /**
     * @param  mixed  $logical
     */
    public function setLogical(mixed $logical): void
    {
        $logical = $logical instanceof Logical ? $logical : Logical::tryFrom($logical);

        $this->logical = $logical;
    }

    /**
     * @return void
     * @throws Exception
     */
    public function validateProperties(): void
    {
        if ($this->firstDateToggled) {
            if ($this->firstFromDate instanceof Carbon && $this->firstToDate instanceof Carbon) {
                if ($this->firstFromDate->greaterThan($this->firstToDate)) {
                    throw new ValidatorException('first_date_range',
                        'First from date cannot be greater than first to date.');
                }
            } else {
                throw new ValidatorException('first_date_range',
                    'First from date and first to date must be set as valid date values.');
            }
        }

        if ($this->secondDateToggled) {
            if ($this->secondFromDate instanceof Carbon && $this->secondToDate instanceof Carbon) {
                if ($this->secondFromDate->greaterThan($this->secondToDate)) {
                    throw new ValidatorException('second_date_range',
                        'Second from date cannot be greater than second to date.');
                }
            } else {
                throw new ValidatorException('second_date_range',
                    'Second from date and second to date must be set as valid date values.');
            }
        }
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::PROPERTY_ACTIVE => $this->active,
            self::PROPERTY_FIRST_DATE_TOGGLED => $this->firstDateToggled,
            self::PROPERTY_SECOND_DATE_TOGGLED => $this->secondDateToggled,
            self::PROPERTY_FIRST_VALUE => $this->firstValue,
            self::PROPERTY_SECOND_VALUE => $this->secondValue,
            self::PROPERTY_FIRST_FROM_DATE => $this->firstFromDate,
            self::PROPERTY_FIRST_TO_DATE => $this->firstToDate,
            self::PROPERTY_SECOND_FROM_DATE => $this->secondFromDate,
            self::PROPERTY_SECOND_TO_DATE => $this->secondToDate,
            self::PROPERTY_FIRST_OPERATOR => $this->firstOperator,
            self::PROPERTY_SECOND_OPERATOR => $this->secondOperator,
            self::PROPERTY_LOGICAL => $this->logical,
            self::PROPERTY_DATE_FORMAT => $this->dateFormatOption,
        ];
    }
}
