<?php

namespace App\DataModels\Odin;

use ArrayIterator;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;
use IteratorAggregate;
use Stringable;

class ConsumerProductTrackingPayloadDataModel implements Jsonable, Arrayable, IteratorAggregate, Stringable
{
    private Collection $data;

    const FBCLID = 'fbclid';
    const USER_AGENT = 'user_agent';
    const FB_PIXEL = 'fbp';

    const ALLOWED_KEYS = [
        self::FBCLID,
        self::USER_AGENT,
        self::FB_PIXEL
    ];

    public function __construct() {
        $this->data = collect();
    }

    public function set(string $key, mixed $value): bool
    {
        if(in_array($key, self::ALLOWED_KEYS, true)) {
            $this->data->put($key, $value);
        }

        return true;
    }

    public function get(string $key): mixed
    {
        return $this->data->get($key);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return $this->data->recursiveToArray();
    }

    /**
     * @inheritDoc
     */
    public function getIterator(): ArrayIterator
    {
        return $this->data->getIterator();
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0): string
    {
        return $this->data->toJson($options);
    }

    /**
     * @param array $array
     * @return self
     */
    public static function fromArray(array $array): self
    {
        $dataModel = new self();

        foreach($array as $key => $value) {
            $dataModel->set($key, $value);
        }

        return $dataModel;
    }

    /**
     * @param string|null $json
     * @return self
     */
    public static function fromJson(?string $json): self
    {
        return self::fromArray(json_decode($json, true));
    }

    /**
     * @inheritDoc
     */
    public function __toString()
    {
        return $this->toJson();
    }
}
