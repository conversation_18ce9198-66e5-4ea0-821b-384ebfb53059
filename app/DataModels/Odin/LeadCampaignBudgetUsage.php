<?php

namespace App\DataModels\Odin;

use Carbon\Carbon;

class LeadCampaignBudgetUsage
{
    const BUDGET_TYPE_UNLIMITED = 'unlimited';
    const BUDGET_TYPE_DAILY_LEAD = 'daily_lead';
    const BUDGET_TYPE_DAILY_SPEND = 'daily_spend';
    const MAX_BUDGET_USAGE = 115;

    public int $companyId;
    public int $campaignId;
    public int $saleTypeId;
    public int $calculationDays;
    public string $budgetType;
    public float|null $budget; //null if budget is unlimited
    public float $budgetSpend;
    public float $maxBudgetUsage;

    public bool $neverExceedBudgeStatus;

    /**
     * @param int $companyId
     *
     * @return $this
     */
    public function setCompanyId(int $companyId): static
    {
        $this->companyId = $companyId;

        return $this;
    }

    /**
     * @param int $campaignId
     *
     * @return $this
     */
    public function setCampaignId(int $campaignId): static
    {
        $this->campaignId = $campaignId;

        return $this;
    }


    /**
     * @param int $saleTypeId
     *
     * @return $this
     */
    public function setSalesTypeId(int $saleTypeId): static
    {
        $this->saleTypeId = $saleTypeId;

        return $this;
    }

    public function setBudgetType(string $budgetType): static
    {
        $this->budgetType = $budgetType;

        return $this;
    }

    public function setBudgetSpend(float $budgetSpend): static
    {
        $this->budgetSpend = $budgetSpend;

        return $this;
    }

    /**
     * @param float|null $budget
     *
     * @return $this
     */
    public function setBudget(float|null $budget): static
    {
        $this->budget = $budget;

        return $this;
    }

    /**
     * @param float $maxBudgetUsage
     *
     * @return $this
     */
    public function setMaxBudgetUsage(float $maxBudgetUsage): static
    {
        $this->maxBudgetUsage = $maxBudgetUsage;

        return $this;
    }

    /**
     * @param int $startTimestamp
     * @param int $endTimestamp
     *
     * @return $this
     */
    public function setCalculationDays(int $startTimestamp, int $endTimestamp): static
    {
        $this->calculationDays = (int) Carbon::createFromTimestamp($startTimestamp)->diffInDays(Carbon::createFromTimestamp($endTimestamp), true);

        return $this;
    }

    /**
     * @param bool $status
     *
     * @return $this
     */
    public function setNeverExceedBudgetStatus(bool $status): static
    {
        $this->neverExceedBudgeStatus = $status;

        return $this;
    }
}
