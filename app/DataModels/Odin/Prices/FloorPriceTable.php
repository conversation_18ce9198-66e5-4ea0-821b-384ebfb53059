<?php

namespace App\DataModels\Odin\Prices;

use App\Abstracts\ProductPricing\PriceTableDataModelAbstract;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\LeadPrice;
use App\Models\Legacy\Location;
use App\Models\Odin\FloorPriceFormula;
use App\Models\Odin\Industry;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Repositories\ProductPriceRepository;
use App\Services\Odin\FloorPriceFormulaService;
use Closure;
use Exception;
use Illuminate\Support\Collection;

class FloorPriceTable extends PriceTableDataModelAbstract
{
    // The default LEAD floor price for undefined state floors. This is just a safety measure, as state pricing definition should be mandatory.
    const DEFAULT_LEAD_STATE_FLOOR_PRICE = 0;

    //const DEFAULT_APPOINTMENT_FORMULA = todo;

    /** @var FloorPriceFormulaService|null $formulaService */
    private ?FloorPriceFormulaService $formulaService = null;

    protected ?float $defaultStateFloorPrice = null;

    /**
     * @param Product $product
     * @param string $industry
     * @param PropertyType $propertyType
     * @param bool $includeCounties
     * @param array|null $exclusiveStateList
     * @param array|null $exclusiveCountyKeys
     */
    public function __construct(
        Product $product = Product::LEAD,
        string $industry = Industry::INDUSTRY_SOLAR,
        PropertyType $propertyType = PropertyType::RESIDENTIAL,
        bool $includeCounties = true,
        ?array $exclusiveStateList = null,
        ?array $exclusiveCountyKeys = null
    )
    {
        $this->defaultStateFloorPrice = self::DEFAULT_LEAD_STATE_FLOOR_PRICE;

        parent::__construct(
            $product,
            $industry,
            $propertyType,
            $includeCounties,
            $exclusiveStateList,
            $exclusiveCountyKeys
        );
    }

    /**
     * Handles converting legacy pricing data to local format
     *
     * @param Collection $legacyPrices
     * @return bool
     */
    public function ingestLegacyData(Collection $legacyPrices): bool
    {
        foreach ($legacyPrices as $price) {
            /** @var LeadPrice $price */

            // skip unhandled legacy tier or sales type
            $qualityTier = $this->getQualityTierFromLegacyPrice($price);
            $salesType = $this->getSalesTypeFromLegacyPrice($price);
            if(!$qualityTier || !$salesType) {
                continue;
            }

            switch ($price->{Location::TYPE}) {
                case Location::TYPE_STATE:
                    if($this->exclusiveStateList
                    && !in_array($price->{Location::STATE_ABBREVIATION}, $this->exclusiveStateList)) {
                        break;
                    }

                    $this->addSalesTypeToState($qualityTier, $price->{Location::STATE_ABBREVIATION}, $salesType, $price->price);
                    break;
                case Location::TYPE_COUNTY:
                    if($this->includeCounties
                    && (empty($this->exclusiveCountyKeys) || in_array($price->{Location::COUNTY_KEY}, $this->exclusiveCountyKeys, true))) {
                        $this->addSalesTypeToCounty($qualityTier, $price->{Location::STATE_ABBREVIATION}, $price->{Location::COUNTY_KEY}, $salesType, $price->price);
                    }
                    break;
            }
        }

        // Apply Default state level price (self::DEFAULT_LEAD_STATE_FLOOR_PRICE). This is just a safety measure, as state pricing definition should be mandatory.
        $this->setStateInherentPrices();
        // $this->ApplyStateFormulas();
        $this->calculateStatePrices();
        // Add State inherited price to all counties, so we know what price is being overridden
        $this->setCountyInherentPrices();
        $this->calculateCountyPrices();

        return true;
    }

    /** @inheritDoc */
    public function ingestData(Collection $prices): bool
    {
        foreach ($prices as $price) {
            /** @var ProductCountyFloorPrice|ProductStateFloorPrice $price */

            $qualityTier = QualityTier::tryFrom($price->{ProductPriceRepository::COLUMN_QUALITY_TIER_NAME});
            $saleType = SaleTypes::tryFrom($price->{ProductPriceRepository::COLUMN_SALE_TYPE_NAME});

            if (!$qualityTier || !$saleType) continue;

            switch ($price->{Location::TYPE}) {
                case Location::TYPE_STATE:
                    if (!empty($this->exclusiveStateList) && in_array($price->{Location::STATE_ABBREVIATION}, $this->exclusiveStateList))
                        break;

                    $this->addSalesTypeToState($qualityTier, $price->{Location::STATE_ABBREVIATION}, $saleType, $price->price);
                    break;
                case Location::TYPE_COUNTY:
                    if (!empty($this->exclusiveCountyKeys) && in_array($price->{Location::COUNTY_KEY}, $this->exclusiveCountyKeys))
                        break;

                    $this->addSalesTypeToCounty($qualityTier, $price->{Location::STATE_ABBREVIATION}, $price->{Location::COUNTY_KEY}, $saleType, $price->price);
                    break;
            }
        }

        // Apply Default state level price. This is just a safety measure, as state pricing definition should be mandatory.
        $this->setDefaultStateFloorPrice(100);
        $this->setStateInherentPrices();
        $this->calculateStatePrices();

        // Add State inherited price to all counties, so we know what price is being overridden
        $this->setCountyInherentPrices();
        $this->calculateCountyPrices();

        return true;
    }

    /**
     * @inheritDoc
     */
    public function getSalesTypeStructure(): array
    {
        $salesTypes = match ($this->product) {
            Product::LEAD => self::LEAD_SALES_TYPES,
            Product::APPOINTMENT => self::APPOINTMENT_SALES_TYPES
        };

        $structure = [];
        foreach($salesTypes as $salesType) {
            $structure[$salesType->value] = [
                self::ARRAY_KEY_INHERENT_PRICE => null,
                self::ARRAY_KEY_FORMULA => null,
                self::ARRAY_KEY_EXPLICIT_PRICE => null,
                self::ARRAY_KEY_PRICE => null
            ];
        }

        return $structure;
    }

    /**
     * @param QualityTier $qualityTier
     * @param string $stateAbbr
     * @param SaleTypes $saleType
     * @param float $explicitPrice
     * @return void
     */
    private function addSalesTypeToState(QualityTier $qualityTier, string $stateAbbr, SaleTypes $saleType, float $explicitPrice): void
    {
        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_SALES_TYPES][$saleType->value][self::ARRAY_KEY_EXPLICIT_PRICE] = $explicitPrice;
    }

    /**
     * @param QualityTier $qualityTier
     * @param string $stateAbbr
     * @param string $countyKey
     * @param SaleTypes $saleType
     * @param float $explicitPrice
     * @return void
     */
    private function addSalesTypeToCounty(QualityTier $qualityTier, string $stateAbbr, string $countyKey, SaleTypes $saleType, float $explicitPrice): void
    {
        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_COUNTIES][$countyKey][self::ARRAY_KEY_SALES_TYPES][$saleType->value][self::ARRAY_KEY_EXPLICIT_PRICE] = $explicitPrice;
    }

    /**
     * Unlike inherited prices for counties, these are ONLY set if there is no explicit price, which should not ever be the case.
     *
     * @return void
     */
    private function setStateInherentPrices(): void
    {
        $this->forAllStateSalesTypes(function(&$salesTypeArr){
            $explicitPrice = $salesTypeArr[self::ARRAY_KEY_EXPLICIT_PRICE];
            $formula = $salesTypeArr[self::ARRAY_KEY_FORMULA];
            if($explicitPrice === null && $formula === null){
                $salesTypeArr[self::ARRAY_KEY_INHERENT_PRICE] = $this->defaultStateFloorPrice;
            }
        });
    }

    /**
     * Handles calculating the final price for all states, taking into account inherent price, formula, and explicit price
     *
     * @return void
     * @throws Exception
     */
    private function calculateStatePrices(): void
    {
        $this->forAllStateSalesTypes(function(&$salesTypeArr, $tier, $stateAbbr){
            $inherentPrice = $salesTypeArr[self::ARRAY_KEY_INHERENT_PRICE];
            $formula = $salesTypeArr[self::ARRAY_KEY_FORMULA];
            $explicitPrice = $salesTypeArr[self::ARRAY_KEY_EXPLICIT_PRICE];
            $salesTypeArr[self::ARRAY_KEY_PRICE] = $this->calculatePrice($inherentPrice, $formula, $explicitPrice, $stateAbbr);
        });
    }

    /**
     * executes callback function for each state sales type
     *
     * @param Closure $callback
     * @return void
     */
    private function forAllStateSalesTypes(Closure $callback): void
    {
        foreach (array_keys($this->priceData[self::ARRAY_KEY_QUALITY_TIERS]) as $qualityTier){
            foreach (array_keys($this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES]) as $stateAbbr){
                foreach (array_keys($this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_SALES_TYPES]) as $salesTypeKey){
                    $salesType = $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_SALES_TYPES][$salesTypeKey];
                    $callback($salesType, $qualityTier, $stateAbbr, $salesTypeKey);
                    // persist changes made by callback
                    $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_SALES_TYPES][$salesTypeKey] = $salesType;
                }
            }
        }
    }

    /**
     * Handles calculating the final price, taking into account inherent price, formula, and explicit price
     *
     * @param float|null $inherentPrice
     * @param FloorPriceFormula|null $formula
     * @param float|null $explicitPrice
     * @param $stateAbbr
     * @param null $countyKey
     * @return float
     * @throws Exception
     */
    private function calculatePrice(?float $inherentPrice, ?FloorPriceFormula $formula, ?float $explicitPrice, $stateAbbr, $countyKey = null): float
    {
        if($explicitPrice){return $explicitPrice;}
        if($formula && $this->formulaService){return round($this->formulaService->resolveFormula($formula, $stateAbbr, $countyKey));}
        if($inherentPrice){return $inherentPrice;}
        throw new \Exception('Unable to calculate price');
    }

    private function setCountyInherentPrices(): void
    {
        $this->forAllCountySalesTypes(function(&$countySalesTypeArr, $stateSalesTypeArr){
            $countySalesTypeArr[self::ARRAY_KEY_INHERENT_PRICE] = $stateSalesTypeArr[self::ARRAY_KEY_PRICE];
        });
    }

    /**
     * executes callback function for each county sales type
     *
     * @param Closure $callback
     * @return void
     */
    private function forAllCountySalesTypes(Closure $callback): void
    {
        foreach (array_keys($this->priceData[self::ARRAY_KEY_QUALITY_TIERS]) as $qualityTier){
            foreach (array_keys($this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES]) as $stateAbbr){

                $stateSalesTypes = $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_SALES_TYPES];

                foreach (array_keys($this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_COUNTIES]) as $countyKey){
                    foreach (array_keys($this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_COUNTIES][$countyKey][self::ARRAY_KEY_SALES_TYPES]) as $salesTypeKey) {
                        $stateSalesType = $stateSalesTypes[$salesTypeKey];
                        $countySalesType = $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_COUNTIES][$countyKey][self::ARRAY_KEY_SALES_TYPES][$salesTypeKey];
                        $callback($countySalesType, $stateSalesType, $qualityTier, $stateAbbr, $countyKey, $salesTypeKey);
                        // persist changes made by callback
                        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_COUNTIES][$countyKey][self::ARRAY_KEY_SALES_TYPES][$salesTypeKey] = $countySalesType;
                    }
                }
            }
        }
    }

    private function calculateCountyPrices(): void
    {
        $this->forAllCountySalesTypes(function(&$salesTypeArr, $stateSalesType, $tier, $stateAbbr, $countyKey){
            $inherentPrice = $salesTypeArr[self::ARRAY_KEY_INHERENT_PRICE];
            $formula = $salesTypeArr[self::ARRAY_KEY_FORMULA];
            $explicitPrice = $salesTypeArr[self::ARRAY_KEY_EXPLICIT_PRICE];
            $salesTypeArr[self::ARRAY_KEY_PRICE] = $this->calculatePrice($inherentPrice, $formula, $explicitPrice, $stateAbbr, $countyKey);
        });
    }

    /**
     * @param FloorPriceTable $referencePriceTable
     * @param Collection $formulae
     * @return void
     * @throws Exception
     */
    public function applyFormulaeAndCalculatePrices(FloorPriceTable $referencePriceTable, Collection $formulae): void
    {
        // Set state level formulae
        $stateFormulae = $formulae
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->where(FloorPriceFormula::FIELD_PRODUCT, $this->product->value)
            ->where(FloorPriceFormula::FIELD_INDUSTRY, $this->industry)
            ->where(FloorPriceFormula::FIELD_PROPERTY_TYPE, $this->propertyType->value);
        $this->forAllStateSalesTypes(function(&$salesTypeArr, $qualityTier, $stateAbbr, $salesTypeKey) use ($stateFormulae) {
            $formula = $stateFormulae->where(Location::STATE_ABBREVIATION, $stateAbbr)
                ->where(FloorPriceFormula::FIELD_QUALITY_TIER, $qualityTier)
                ->where(FloorPriceFormula::FIELD_SALES_TYPE, $salesTypeKey)
                ->first();
            // At the state level, since there is nowhere to inherit a price from,
            // when there is no defined formula we will apply a default.
            // This is not persisted, so any change to these default values will affect real-time pricing.
            $formula = $formula ?: FloorPriceFormula::defaultAppointmentFormula();
            $salesTypeArr[self::ARRAY_KEY_FORMULA] = $formula;
        });

        // Resolve state pricing based on formulae
        $this->initializeFormulaService($referencePriceTable);
        $this->calculateStatePrices();

        // Set county inherited pricing to parent states pricing
        $this->setCountyInherentPrices();

        // Set county level formulae
        $countyFormulae = $formulae
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->where(FloorPriceFormula::FIELD_PRODUCT, $this->product->value)
            ->where(FloorPriceFormula::FIELD_INDUSTRY, $this->industry)
            ->where(FloorPriceFormula::FIELD_PROPERTY_TYPE, $this->propertyType->value);
        $this->forAllCountySalesTypes(function(&$countySalesTypeArr, $stateSalesTypeArr, $qualityTier, $stateAbbr, $countyKey, $salesTypeKey) use ($countyFormulae) {
            $formula = $countyFormulae
                ->where(Location::STATE_ABBREVIATION, $stateAbbr)
                ->where(Location::COUNTY_KEY, $countyKey)
                ->where(FloorPriceFormula::FIELD_QUALITY_TIER, $qualityTier)
                ->where(FloorPriceFormula::FIELD_SALES_TYPE, $salesTypeKey)
                ->first();
            $countySalesTypeArr[self::ARRAY_KEY_FORMULA] = $formula;
        });

        // Resolve county pricing based on formulae or inherent price
        $this->calculateCountyPrices();
    }

    /**
     * @return void
     */
    private function removeAllExplicitPricing(): void
    {
        $this->forAllStateSalesTypes(function (&$salesTypeArr){$salesTypeArr[self::ARRAY_KEY_EXPLICIT_PRICE] = null;});
        $this->forAllCountySalesTypes(function(&$salesTypeArr){$salesTypeArr[self::ARRAY_KEY_EXPLICIT_PRICE] = null;});
    }

    /**
     * @param FloorPriceTable $referenceTable
     * @return void
     */
    private function initializeFormulaService(FloorPriceTable $referenceTable): void
    {
        $this->formulaService = new FloorPriceFormulaService($referenceTable);
    }

    /** @inheritDoc */
    public function getPrice(string $qualityTier, string $salesType, string $stateAbbr, ?string $countyKey = null): ?float
    {
        $array = $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier][self::ARRAY_KEY_STATES][$stateAbbr];

        if($countyKey) {
            $array = $array[self::ARRAY_KEY_COUNTIES][$countyKey];
        }

        return $array[self::ARRAY_KEY_SALES_TYPES][$salesType][self::ARRAY_KEY_PRICE];
    }

    /**
     * @return FloorPriceTable|null
     */
    public function getReferenceTable(): ?FloorPriceTable
    {
        return $this->formulaService?->basePriceTable;
    }

    /**
     * @param float $price
     *
     * @return void
     */
    protected function setDefaultStateFloorPrice(float $price): void
    {
        $this->defaultStateFloorPrice = $price;
    }
}
