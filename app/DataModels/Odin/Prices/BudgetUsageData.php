<?php

namespace App\DataModels\Odin\Prices;

use App\Enums\Odin\BudgetCategory;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Rules\InputTypes;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use IteratorAggregate;
use Traversable;

class BudgetUsageData implements Arrayable, IteratorAggregate
{
    const CAMPAIGN_ID = 'campaign_id';
    const COMPANY_ID = 'company_id';
    const CAMPAIGN_BUDGET_ID = 'campaign_budget_id';
    const CATEGORY = 'category';
    const BUDGET_SPENT = 'budget_spent';
    const DAILY_BUDGET = 'daily_budget';
    const BUDGET_TIMEFRAME_DAYS = 'budget_timeframe_days';
    const MAX_BUDGET_USAGE = 'max_budget_usage';
    const BUDGET_UNIT = 'budget_unit';
    const NEVER_EXCEED_BUDGET = 'never_exceed_budget';
    const COMPANY_INFO = 'company_info';
    const BUDGET_INFO = 'budget_info';

    private Collection $usageData;

    public function __construct()
    {
        $this->usageData = collect();
    }

    /**
     * @param int $campaignId
     * @param int $companyId
     * @param int $campaignBudgetId
     * @param string $category
     * @param float $budgetSpent
     * @param float $dailyBudget
     * @param int $budgetTimeframeDays
     * @param int $maxBudgetUsage
     * @param string $budgetUnit
     * @param bool $neverExceedBudget
     * @return bool
     * @throws Exception
     */
    public function setBudgetUsage(
        int $campaignId,
        int $companyId,
        int $campaignBudgetId,
        string $category,
        float $budgetSpent,
        float $dailyBudget,
        int $budgetTimeframeDays,
        int $maxBudgetUsage,
        string $budgetUnit,
        bool $neverExceedBudget
    ): bool
    {
        $rawBudgetUsageData = [
            self::CAMPAIGN_ID => $campaignId,
            self::COMPANY_ID => $companyId,
            self::CAMPAIGN_BUDGET_ID => $campaignBudgetId,
            self::CATEGORY => $category,
            self::BUDGET_SPENT => $budgetSpent,
            self::DAILY_BUDGET => $dailyBudget,
            self::BUDGET_TIMEFRAME_DAYS => $budgetTimeframeDays,
            self::MAX_BUDGET_USAGE => $maxBudgetUsage,
            self::BUDGET_UNIT => $budgetUnit,
            self::NEVER_EXCEED_BUDGET => $neverExceedBudget
        ];

        $validator = Validator::make(
            $rawBudgetUsageData,
            [
                self::CAMPAIGN_ID => ['required', 'integer', 'min:1'],
                self::COMPANY_ID => ['required', 'integer', 'min:1'],
                self::CAMPAIGN_BUDGET_ID => ['required', 'integer', 'min:0'],
                self::CATEGORY => ['required', new Enum(BudgetCategory::class)],
                self::BUDGET_SPENT => ['required', new InputTypes(['double']), 'min:0'],
                self::DAILY_BUDGET => ['required', new InputTypes(['double']), 'min:0'],
                self::BUDGET_TIMEFRAME_DAYS => ['required', 'integer', 'min:1'],
                self::MAX_BUDGET_USAGE => ['required', 'integer', 'min:1'],
                self::BUDGET_UNIT => ['required', 'string', Rule::in([
                    ProductCampaignBudget::VALUE_TYPE_NO_LIMIT,
                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS,
                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND,
                    ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS,

                    //TODO: remove legacy types once LeadCampaign is unnecessary
                    LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD,
                    LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED,
                    LeadCampaign::DISPLAY_BUDGET_UNIT_CURRENCY,
                ])],
                self::NEVER_EXCEED_BUDGET => ['required', 'boolean']
            ]
        );

        if($validator->fails()) {
            throw new Exception((string) $validator->getMessageBag());
        }

        if(empty($this->usageData->get($campaignId))) {
            $this->initCampaignBudgetDataBaseStructure($campaignId, $companyId, $neverExceedBudget);
        }

        $this->usageData->get($campaignId)->get(self::COMPANY_INFO)->put(self::COMPANY_ID, $companyId);
        $this->usageData->get($campaignId)->get(self::COMPANY_INFO)->put(self::NEVER_EXCEED_BUDGET, $neverExceedBudget);

        $this->usageData->get($campaignId)->get(self::BUDGET_INFO)->get($category)->put(self::CAMPAIGN_BUDGET_ID, $campaignBudgetId);
        $this->usageData->get($campaignId)->get(self::BUDGET_INFO)->get($category)->put(self::BUDGET_SPENT, $budgetSpent);
        $this->usageData->get($campaignId)->get(self::BUDGET_INFO)->get($category)->put(self::DAILY_BUDGET, $dailyBudget);
        $this->usageData->get($campaignId)->get(self::BUDGET_INFO)->get($category)->put(self::BUDGET_TIMEFRAME_DAYS, $budgetTimeframeDays);
        $this->usageData->get($campaignId)->get(self::BUDGET_INFO)->get($category)->put(self::MAX_BUDGET_USAGE, $maxBudgetUsage);
        $this->usageData->get($campaignId)->get(self::BUDGET_INFO)->get($category)->put(self::BUDGET_UNIT, $budgetUnit);

        return true;
    }

    /**
     * @param int $campaignId
     * @return Collection
     */
    public function getBudgetUsageData(int $campaignId): Collection
    {
        return $this->usageData->get($campaignId, collect());
    }

    /**
     * @param int $campaignId
     * @param int $companyId
     * @param bool $neverExceedBudget
     * @return bool
     * @throws Exception
     */
    public function initCampaignBudgetDataBaseStructure(int $campaignId, int $companyId, bool $neverExceedBudget): bool
    {
        $validator = Validator::make(
            [
                self::CAMPAIGN_ID => $campaignId,
                self::COMPANY_ID => $companyId,
                self::NEVER_EXCEED_BUDGET => $neverExceedBudget
            ],
            [
                self::CAMPAIGN_ID => ['required', 'integer', 'min:1'],
                self::COMPANY_ID => ['required', 'integer', 'min:1'],
                self::NEVER_EXCEED_BUDGET => ['required', 'boolean']
            ]
        );

        if($validator->fails()) {
            throw new Exception((string) $validator->getMessageBag());
        }

        $this->usageData->put($campaignId, collect([
            self::COMPANY_INFO => collect([
                self::COMPANY_ID => $companyId,
                self::NEVER_EXCEED_BUDGET => $neverExceedBudget
            ]),
            self::BUDGET_INFO => collect([
                BudgetCategory::VERIFIED->value => collect([
                    self::CAMPAIGN_BUDGET_ID => 0,
                    self::BUDGET_SPENT => 0,
                    self::DAILY_BUDGET => 0,
                    self::BUDGET_TIMEFRAME_DAYS => 0,
                    self::MAX_BUDGET_USAGE => 115,
                    self::BUDGET_UNIT => ''
                ]),
                BudgetCategory::UNVERIFIED->value => collect([
                    self::CAMPAIGN_BUDGET_ID => 0,
                    self::BUDGET_SPENT => 0,
                    self::DAILY_BUDGET => 0,
                    self::BUDGET_TIMEFRAME_DAYS => 0,
                    self::MAX_BUDGET_USAGE => 115,
                    self::BUDGET_UNIT => ''
                ]),
                BudgetCategory::EMAIL_ONLY->value => collect([
                    self::CAMPAIGN_BUDGET_ID => 0,
                    self::BUDGET_SPENT => 0,
                    self::DAILY_BUDGET => 0,
                    self::BUDGET_TIMEFRAME_DAYS => 0,
                    self::MAX_BUDGET_USAGE => 115,
                    self::BUDGET_UNIT => ''
                ])
            ])
        ]));

        return true;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->usageData->toArray();
    }

    /**
     * @return Traversable
     */
    public function getIterator(): Traversable
    {
        return $this->usageData->getIterator();
    }
}
