<?php

namespace App\DataModels\Odin\Prices;

use Exception;
use Illuminate\Contracts\Support\Arrayable;

class SaleTypeLimits implements Arrayable
{
    /** @var array $limits */
    private array $limits = [];

    /**
     * @param int $saleTypeId
     * @param int $saleLimit
     * @return bool
     * @throws Exception
     */
    public function setLimit(int $saleTypeId, int $saleLimit): bool
    {
        if($saleTypeId < 1 || $saleLimit < 1) {
            throw new Exception("Invalid sale type ID or sale limit");
        }

        $this->limits[$saleTypeId] = $saleLimit;

        return true;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->limits;
    }
}
