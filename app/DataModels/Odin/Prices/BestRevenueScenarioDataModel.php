<?php

namespace App\DataModels\Odin\Prices;

use App\Enums\Odin\QualityTier;
use App\Models\ComputedRejectionStatistic;
use App\Rules\InputTypes;
use ArrayIterator;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use IteratorAggregate;
use Validator;

/**
 * @property-read int $consumerProductId
 * @property-read int $saleTypeId
 * @property-read float $totalPrice
 * @property-read QualityTier $qualityTier
 * @property-read int $logRunId
 * @property-read Collection $campaigns
 */
class BestRevenueScenarioDataModel implements IteratorAggregate, Arrayable
{
    const ALLOWED_KEYS = [
        self::CONSUMER_PRODUCT_ID,
        self::SALE_TYPE_ID,
        self::TOTAL_PRICE,
        self::QUALITY_TIER,
        self::LOG_RUN_ID,
        self::CAMPAIGNS
    ];

    const CONSUMER_PRODUCT_ID = 'consumerProductId';
    const SALE_TYPE_ID = 'saleTypeId';
    const TOTAL_PRICE = 'totalPrice';
    const QUALITY_TIER = 'qualityTier';
    const LOG_RUN_ID = 'logRunId';
    const CAMPAIGNS = 'campaigns';

    //Campaign info attributes
    const PRICE = 'price';
    const UNRECTIFIED_PRICE = 'unrectified_price';
    const BUDGET_USAGE = 'budget_usage';
    const COMPANY_ID = 'company_id';
    const CAMPAIGN_ID = 'campaign_id';
    const BUDGET = 'budget';
    const CAMPAIGN_BUDGET_ID = 'campaign_budget_id';
    const SCHEDULE_ID = 'schedule_id';

    /**
     * @var Collection $scenario
     */
    private Collection $scenario;

    public function __construct(int $consumerProductId, QualityTier $qualityTier, int $logRunId) {
        $this->scenario = collect([
            self::CONSUMER_PRODUCT_ID => $consumerProductId,
            self::QUALITY_TIER => $qualityTier,
            self::LOG_RUN_ID => $logRunId,
            self::SALE_TYPE_ID => 0,
            self::TOTAL_PRICE => 0,
            self::CAMPAIGNS => collect()
        ]);
    }

    /**
     * @param int $saleTypeId
     * @param float $totalPrice
     * @param Collection $campaigns
     * @return bool
     * @throws Exception
     */
    public function setScenario(
        int $saleTypeId,
        float $totalPrice,
        Collection $campaigns
    ): bool
    {
        $validator = Validator::make(
            [
                self::SALE_TYPE_ID => $saleTypeId,
                self::TOTAL_PRICE => $totalPrice,
                self::CAMPAIGNS => $campaigns->toArray()
            ],
            [
                self::SALE_TYPE_ID => ['required', 'integer', 'min:1'],
                self::TOTAL_PRICE => ['required', new InputTypes(['double']), 'gt:0'],

                self::CAMPAIGNS => ['required', 'array'],
                self::CAMPAIGNS.'.*.'.self::PRICE => ['required', new InputTypes(['integer', 'double']), 'gt:0'],
                self::CAMPAIGNS.'.*.'.self::UNRECTIFIED_PRICE => ['required', new InputTypes(['integer', 'double']), 'gt:0'],
                self::CAMPAIGNS.'.*.'.ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => ['required', new InputTypes(['double']), 'min:0'],
                self::CAMPAIGNS.'.*.'.ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID => ['required', new InputTypes(['double']), 'min:0'],
                self::CAMPAIGNS.'.*.'.self::BUDGET_USAGE => ['required', new InputTypes(['double']), 'min:0'],
                self::CAMPAIGNS.'.*.'.self::COMPANY_ID => ['required', 'integer', 'min:1'],
                self::CAMPAIGNS.'.*.'.self::CAMPAIGN_ID => ['required', 'integer', 'min:1', 'distinct'],
                self::CAMPAIGNS.'.*.'.self::BUDGET => ['required', 'string'],
                self::CAMPAIGNS.'.*.'.self::CAMPAIGN_BUDGET_ID => ['required', 'integer', 'min:0'],
                self::CAMPAIGNS.'.*.'.self::SCHEDULE_ID => ['required', 'integer', 'min:0']
            ]
        );

        if($validator->fails()) {
            throw new Exception((string) $validator->getMessageBag());
        }

        $this->scenario->put(self::SALE_TYPE_ID, $saleTypeId);
        $this->scenario->put(self::TOTAL_PRICE, round($totalPrice, 2));
        $this->scenario->put(self::CAMPAIGNS, $campaigns);

        return true;
    }

    /**
     * @param string $name
     * @return mixed
     * @throws Exception
     */
    public function __get(string $name): mixed
    {
        if(!in_array($name, self::ALLOWED_KEYS, true)) {
            throw new Exception("Property $name does not exist");
        }

        return $this->scenario->get($name);
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::CONSUMER_PRODUCT_ID => $this->{self::CONSUMER_PRODUCT_ID},
            self::SALE_TYPE_ID => $this->{self::SALE_TYPE_ID},
            self::TOTAL_PRICE => $this->{self::TOTAL_PRICE},
            self::QUALITY_TIER => $this->{self::QUALITY_TIER},
            self::LOG_RUN_ID => $this->{self::LOG_RUN_ID},
            self::CAMPAIGNS => $this->{self::CAMPAIGNS}->toArray()
        ];
    }

    /**
     * @return ArrayIterator
     */
    public function getIterator(): ArrayIterator
    {
        return $this->scenario->get(self::CAMPAIGNS)->getIterator();
    }

    /**
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->scenario->get(self::CAMPAIGNS)->isEmpty();
    }

    /**
     * @return Collection
     */
    public function getCampaignIds(): Collection
    {
        return collect([
            $this->{self::SALE_TYPE_ID} => $this->scenario->get(self::CAMPAIGNS)->keys()
        ]);
    }

    /**
     * @return bool
     */
    public function unrectifyPrices(): bool
    {
        foreach($this->{self::CAMPAIGNS} as $campaignId => &$campaign) {
            $unrectifiedPrice = $campaign->get(self::UNRECTIFIED_PRICE);

            if(!empty($unrectifiedPrice)) {
                $campaign->put(self::PRICE, $unrectifiedPrice);
            }
        }

        return true;
    }
}
