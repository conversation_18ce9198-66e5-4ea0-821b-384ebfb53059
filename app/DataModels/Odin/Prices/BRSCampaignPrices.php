<?php

namespace App\DataModels\Odin\Prices;

use App\Enums\Odin\Product;
use App\Enums\Odin\QualityTier;
use App\Models\ComputedRejectionStatistic;
use ArrayIterator;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use IteratorAggregate;

/**
 * @property-read Product $product
 * @property-read QualityTier $qualityTier
 */
class BRSCampaignPrices implements IteratorAggregate, Arrayable
{
    const ALLOWED_KEYS = [
        'product',
        'qualityTier'
    ];

    const PRICE = 'price';
    const UNRECTIFIED_PRICE = 'unrectified_price';
    const COMPANY_ID = 'company_id';
    const SCHEDULE_ID = 'schedule_id';

    /**
     * @var Collection $campaigns
     */
    private Collection $prices;

    public function __construct(
        private readonly Product $product,
        private readonly QualityTier $qualityTier
    ) {
        $this->prices = collect();
    }

    /**
     * @param int $saleTypeId
     * @param int $campaignId
     * @param int $companyId
     * @param float $price
     * @param float $unrectifiedPrice
     * @param float $rejectionPercentageImpactingEligibility
     * @param float $rejectionPercentageImpactingBid
     * @param int|null $scheduleId
     * @return bool
     * @throws Exception
     */
    public function setPrice(
        int $saleTypeId,
        int $campaignId,
        int $companyId,
        float $price,
        float $unrectifiedPrice,
        float $rejectionPercentageImpactingEligibility,
        float $rejectionPercentageImpactingBid,
        ?int $scheduleId = 0
    ): bool
    {
        if($price < 0) {
            throw new Exception("Price must be greater than or equal to zero");
        }
        if($unrectifiedPrice < 0 || $unrectifiedPrice == 0) {
            throw new Exception("Unrectified price must be non-zero");
        }
        if($rejectionPercentageImpactingEligibility < 0 || $rejectionPercentageImpactingBid < 0) {
            throw new Exception("Rejection percentage can't be negative");
        }
        if($scheduleId < 0) {
            throw new Exception("Schedule ID can't be negative");
        }

        if(empty($this->prices->get($saleTypeId))) {
            $this->prices->put($saleTypeId, collect());
        }

        $this->prices->get($saleTypeId)->put(
            $campaignId,
            collect([
                self::PRICE                                                            => $price,
                self::UNRECTIFIED_PRICE                                                => $unrectifiedPrice,
                self::COMPANY_ID                                                       => $companyId,
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => $rejectionPercentageImpactingEligibility,
                ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID         => $rejectionPercentageImpactingBid,
                self::SCHEDULE_ID                                                      => $scheduleId ?? 0
            ])
        );

        return true;
    }

    /**
     * @param string $name
     * @return mixed
     * @throws Exception
     */
    public function __get(string $name): mixed
    {
        if(!in_array($name, self::ALLOWED_KEYS, true)) {
            throw new Exception("Invalid property $name");
        }

        return $this->{$name};
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->prices->toArray();
    }

    /**
     * @return ArrayIterator
     */
    public function getIterator(): ArrayIterator
    {
        return $this->prices->getIterator();
    }

    /**
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->prices->isEmpty();
    }

    /**
     * @return Collection
     */
    public function getCampaignIds(): Collection
    {
        $campaignIds = collect();
        foreach($this->prices as $saleTypeId => $campaignPrices) {
            $campaignIds->put($saleTypeId, $campaignPrices->keys());
        }

        return $campaignIds;
    }

    /**
     * @return Collection
     */
    public function getCampaignCompanyIds(): Collection
    {
        $companyIds = collect();
        foreach($this->prices as $saleTypeId => $campaignPrices) {
            $companyIds->put($saleTypeId, collect());

            foreach($campaignPrices as $campaignId => $campaignInfo) {
                $companyIds->get($saleTypeId)->put($campaignId, $campaignInfo[self::COMPANY_ID]);
            }
        }

        return $companyIds;
    }
}
