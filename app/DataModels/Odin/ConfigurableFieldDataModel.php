<?php

namespace App\DataModels\Odin;

use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class ConfigurableFieldDataModel implements Jsonable
{
    /**
     * @param Collection $data
     */
    public function __construct(public Collection $data) {}

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            "data" => $this->data->toArray()
        ];
    }

    /**
     * @param $options
     * @return string|null
     */
    public function toJson($options = 0): ?string
    {
        return json_encode($this->toArray());
    }

    /**
     * @param string $json
     * @return ConfigurableFieldDataModel
     */
    public static function fromJson(string $json): ConfigurableFieldDataModel
    {
        $object = json_decode($json, true);

        return new ConfigurableFieldDataModel(
            collect($object["data"])
        );
    }

    public static function fromArray(array $data): ConfigurableFieldDataModel
    {
        return new ConfigurableFieldDataModel(
            collect($data)
        );
    }
}
