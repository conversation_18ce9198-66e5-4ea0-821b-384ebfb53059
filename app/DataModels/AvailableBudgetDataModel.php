<?php


namespace App\DataModels;


use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use Exception;
use Illuminate\Support\Collection;

class AvailableBudgetDataModel
{
    const COMPANY_ID        = 'companyId';
    const LEGACY_COMPANY_ID = 'legacyCompanyId';
    const CAMPAIGN_ID       = 'campaignId';
    const TYPE              = 'type';
    const VALUE             = 'value';

    const TYPE_UNLIMITED = 'unlimited';
    const TYPE_DOLLAR    = 'dollar';
    const TYPE_VOLUME    = 'volume';

    public Collection $campaigns;

    /**
     * This array is used to track which companies have already contributed to this data set,
     * with the intention of limiting the available campaign count to 1 campaign per company.
     * In the case where a company has multiple campaigns covering the location,
     * we will prefer an 'Unlimited' campaign, then 'Dollar', and finally 'Volume'.
     * e.g. [ {companyId} => [ 'campaignId' => 1234, 'type' => 'dollar', 'value' => 120 ] ]
     */
    private array $companyCampaigns;

    public function __construct(
        public float $availableDollars,
        public int   $availableVolume,
        public int   $campaignCount,
        public int   $unlimitedCampaignCount,
        public int   $budgetTypeId
    ) {
        $this->campaigns = collect();
        $this->companyCampaigns = [];
    }

    /**
     * @param LeadCampaign $campaign
     * @param int $campaignId
     * @return $this
     */
    public function addUnlimitedCampaign(LeadCampaign $campaign, int $campaignId): AvailableBudgetDataModel
    {
        $legacyCompanyId = $campaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::ID};

        if ($this->checkForExistingCompanyCampaign($legacyCompanyId)) {
            $this->rectifyCompanyCampaign($legacyCompanyId, $campaignId, self::TYPE_UNLIMITED, 0);
        } else {
            $this->campaignCount++;
            $this->unlimitedCampaignCount++;

            $odinCompanyId = $campaign->{LeadCampaign::RELATION_COMPANY}?->{EloquentCompany::RELATION_MI_COMPANY}?->{Company::FIELD_ID} ?? null;

            $this->campaigns->push(new CampaignValueModel($campaignId, CampaignValueModel::TYPE_UNLIMITED, 0, $odinCompanyId, $legacyCompanyId));
            $this->logCompanyCampaign($legacyCompanyId, $campaignId, self::TYPE_UNLIMITED, 0, $odinCompanyId);
        }

        return $this;
    }

    /**
     * @param LeadCampaign $campaign
     * @param int $campaignId
     * @param float $value
     * @param bool $historicalBudget
     * @return $this
     */
    public function addDollarCampaign(LeadCampaign $campaign, int $campaignId, float $value, bool $historicalBudget = false): AvailableBudgetDataModel
    {
        $legacyCompanyId = $campaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::ID};

        if ($value > 0 && $this->checkForExistingCompanyCampaign($legacyCompanyId)) {
            $this->rectifyCompanyCampaign($legacyCompanyId, $campaignId, self::TYPE_DOLLAR, $value);
        } else {
            if($value > 0) {
                $this->campaignCount++;
                $this->availableDollars += $value;
            }
            if($value > 0 || $historicalBudget) {
                $odinCompanyId = $campaign->{LeadCampaign::RELATION_COMPANY}?->{EloquentCompany::RELATION_MI_COMPANY}?->{Company::FIELD_ID} ?? null;

                $this->campaigns->push(new CampaignValueModel($campaignId, CampaignValueModel::TYPE_DOLLAR, $value, $odinCompanyId, $legacyCompanyId));
            }
        }

        return $this;
    }

    /**
     * @param LeadCampaign $campaign
     * @param int $campaignId
     * @param int $value
     * @param bool $historicalBudget
     * @return $this
     */
    public function addVolumeCampaign(LeadCampaign $campaign, int $campaignId, int $value, bool $historicalBudget = false): AvailableBudgetDataModel
    {
        $legacyCompanyId = $campaign->{LeadCampaign::RELATION_COMPANY}->{EloquentCompany::ID};

        if ($value > 0 && $this->checkForExistingCompanyCampaign($legacyCompanyId)) {
            $this->rectifyCompanyCampaign($legacyCompanyId, $campaignId, self::TYPE_VOLUME, $value);
        } else {
            if($value > 0) {
                $this->campaignCount++;
                $this->availableVolume += $value;
            }
            if($value > 0 || $historicalBudget) {
                $odinCompanyId = $campaign->{LeadCampaign::RELATION_COMPANY}?->{EloquentCompany::RELATION_MI_COMPANY}?->{Company::FIELD_ID} ?? null;

                $this->campaigns->push(new CampaignValueModel($campaignId, CampaignValueModel::TYPE_VOLUME, $value, $odinCompanyId, $legacyCompanyId));
            }
        }

        return $this;
    }

    /**
     * @param int $companyId
     * @return bool
     */
    private function checkForExistingCompanyCampaign(int $companyId): bool
    {
        return array_key_exists($companyId, $this->companyCampaigns);
    }

    /**
     * @param int $companyId
     * @param int $newCampaignId
     * @param string $newType
     * @param float $newValue
     * @return void
     */
    private function rectifyCompanyCampaign(int $companyId, int $newCampaignId, string $newType, float $newValue): void
    {
        $oldCompanyCampaign = $this->companyCampaigns[$companyId];
        $oldCampaignId = $oldCompanyCampaign[self::CAMPAIGN_ID];
        $oldType = $oldCompanyCampaign[self::TYPE];
        $oldValue = $oldCompanyCampaign[self::VALUE];
        if ($oldType === self::TYPE_DOLLAR && $newType === self::TYPE_UNLIMITED) {
            $this->availableDollars -= $oldValue;
            $this->unlimitedCampaignCount++;
            $this->replaceCampaign($oldCampaignId, $newCampaignId, CampaignValueModel::TYPE_UNLIMITED, $newValue);
            $this->logCompanyCampaign($companyId, $newCampaignId, $newType, $newValue);
        } elseif ($oldType === self::TYPE_VOLUME && $newType !== self::TYPE_VOLUME) {
            $this->availableVolume -= $oldValue;
            if($newType === self::TYPE_UNLIMITED){
                $this->unlimitedCampaignCount++;
                $this->replaceCampaign($oldCampaignId, $newCampaignId, CampaignValueModel::TYPE_UNLIMITED, $newValue);
            } elseif ($newType === self::TYPE_DOLLAR) {
                $this->availableDollars += $newValue;
                $this->replaceCampaign($oldCampaignId, $newCampaignId, CampaignValueModel::TYPE_DOLLAR, $newValue);
            }
            $this->logCompanyCampaign($companyId, $newCampaignId, $newType, $newValue);
        }
    }

    /**
     * @param  int  $companyId
     * @param  int  $campaignId
     * @param  string  $type
     * @param  float  $value
     * @param  int|null  $odinCompanyId
     * @return void
     */
    private function logCompanyCampaign(int $companyId, int $campaignId, string $type, float $value, ?int $odinCompanyId = null): void
    {
        $this->companyCampaigns[$companyId] = [
            self::CAMPAIGN_ID => $campaignId,
            self::TYPE        => $type,
            self::VALUE       => $value,
            self::COMPANY_ID  => $odinCompanyId,
            self::LEGACY_COMPANY_ID => $companyId
        ];
    }

    /**
     * @param mixed $oldCampaignId
     * @param int $newCampaignId
     * @param int $newType
     * @param float $newValue
     * @return void
     */
    private function replaceCampaign(mixed $oldCampaignId, int $newCampaignId, int $newType, float $newValue): void
    {
        $this->campaigns = $this->campaigns->reject(function ($campaignValueModel) use ($oldCampaignId) {
            return $campaignValueModel->campaignId === $oldCampaignId;
        });
        $this->campaigns->push(new CampaignValueModel($newCampaignId, $newType, $newValue));
    }
}
