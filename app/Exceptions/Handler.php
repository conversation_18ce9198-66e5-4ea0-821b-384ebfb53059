<?php

namespace App\Exceptions;

use App\Contracts\NotifiableExceptionInterface;
use App\Exceptions\StackDriver\StackDriverExceptionHandler;
use App\Services\NotificationService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\App;
use Illuminate\Validation\UnauthorizedException;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<\Throwable>, \Psr\Log\LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<\Throwable>>
     */
    protected $dontReport = [
        UnauthorizedException::class
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register(): void
    {
        $this->reportable(function (NotifiableExceptionInterface $e) {
            if ($e->getRoleId() === null) {
                return;
            }

            /** @var NotificationService $notificationService */
            $notificationService = app()->make(NotificationService::class);

            $notificationService->createNotificationForRole(
                $e->getRoleId(),
                $e->getFromId(),
                $e->getSubject(),
                $e->getBody(),
                $e->getType()->value,
                $e->getLink(),
                $e->getLinkType(),
                $e->getPayload()
            );
        });
    }

    /**
     * Report errors to Sentry
     *
     * @param Throwable $exception
     * @return void
     * @throws Throwable
     * @throws BindingResolutionException
     */
    public function report(Throwable $exception): void
    {
        if (app()->bound('sentry')
            && App::isProduction()
            && $this->shouldReport($exception)) {
            app('sentry')->captureException($exception);
        }

        if(config('services.google.stackdriver.enabled')) {
            /** @var StackDriverExceptionHandler $service */
            $service = app()->make(StackDriverExceptionHandler::class);

            $service->report($exception);
        }

        parent::report($exception);
    }

}
