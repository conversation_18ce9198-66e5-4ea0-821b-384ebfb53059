<?php

namespace App\Exceptions\StackDriver;

use Google\Cloud\Core\Report\SimpleMetadataProvider;
use Google\Cloud\ErrorReporting\Bootstrap;
use Google\Cloud\Logging\LoggingClient;
use Google\Cloud\Logging\PsrLogger;
use Throwable;

class StackDriverExceptionHandler
{
    public function __construct(protected LoggingClient $client) {}

    /**
     * <PERSON><PERSON> reporting an exception using stackdriver.
     *
     * @param Throwable $exception
     * @return void
     */
    public function report(Throwable $exception): void
    {
        $logger = $this->getLogger();

        Bootstrap::init($logger);
        Bootstrap::exceptionHandler($exception);
    }

    /**
     * Returns the formatted psr logger.
     *
     * @return PsrLogger
     */
    protected function getLogger(): PsrLogger
    {
        return $this->client->psrLogger(
            "error-log",
            [
                "metadataProvider" => $this->getMetadata(),
                'batchEnabled' => true,
                'batchOptions' => [
                    'batchSize' => 5,
                    'callPeriod' => 2.0,
                    'workerNum' => 1,
                ],
            ]
        );
    }

    /**
     * Formats data into google's simple metadata provider.
     *
     * @return SimpleMetadataProvider
     */
    protected function getMetadata(): SimpleMetadataProvider
    {
        return new SimpleMetadataProvider(
            [],
            config('services.google.stackdriver.project_id'),
            config('app.name') . ' - ' . config('app.env'),
            "1",
            []
        );
    }
}
