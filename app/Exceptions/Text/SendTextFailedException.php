<?php

namespace App\Exceptions\Text;

use App\DTO\SMS;
use Exception;
use Throwable;

class SendTextFailedException extends Exception
{
    public function __construct(
        string $message,
        public SMS $sms,
        ?Throwable $previous = null,
    )
    {
        parent::__construct($message, 410, $previous);
    }

    public function getSMS(): SMS
    {
        return $this->sms;
    }
}
