<?php

namespace App\Exceptions\Billing;

use Exception;

class InvoicePaymentOverchargeException extends Exception
{
    public function __construct(
        public string $invoiceUuid,
        public string $invoicePaymentUuid,
    )
    {
        $message = 'Payment overcharge detected. invoiceUuid: ' . $this->invoiceUuid . ' paymentUuid: ' . $this->invoicePaymentUuid;
        parent::__construct($message);
    }
}
