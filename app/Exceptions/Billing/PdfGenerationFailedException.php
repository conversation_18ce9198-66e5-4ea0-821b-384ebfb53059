<?php

namespace App\Exceptions\Billing;

use Exception;

class PdfGenerationFailedException extends Exception
{
    public function __construct(
        protected ?array $failedRequests = [],
        protected ?array $errors = [],
        protected ?array $consoleMessages = [],
    )
    {
        $message = 'Pdf generation failed. failedRequests: '
            . json_encode($failedRequests) . ' errors: '
            . json_encode($errors) . ' console messages: '
            . json_encode($consoleMessages);
        parent::__construct($message);
    }

    /**
     * @return array|null
     */
    public function getFailedRequests(): ?array
    {
        return $this->failedRequests;
    }

    /**
     * @return array|null
     */
    public function getErrors(): ?array
    {
        return $this->errors;
    }
}
