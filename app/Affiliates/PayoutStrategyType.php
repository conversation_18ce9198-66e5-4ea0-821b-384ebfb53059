<?php

namespace App\Affiliates;

use App\DTO\Affiliate\PayoutStrategyValueBoundaries;
use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;

abstract class PayoutStrategyType
{
    /**
     * @return PayoutStrategyTypeEnum
     */
    public abstract function type(): PayoutStrategyTypeEnum;

    /**
     * @return string
     */
    public abstract function title(): string;

    /**
     * @return PayoutStrategyValueBoundaries
     */
    public abstract function valueBoundaries(): PayoutStrategyValueBoundaries;

    /**
     * @param ConsumerProduct $consumerProduct
     * @param PayoutStrategy $strategy
     * @return int
     */
    protected abstract function calculate(
        ConsumerProduct $consumerProduct,
        PayoutStrategy  $strategy,
    ): int;

    /**
     * @return int
     */
    public abstract function defaultValue(): int;

    /**
     * @param int $value
     * @return int
     */
    public function toPresentationValue(int $value): int
    {
        return $value;
    }

    /**
     * @param int $value
     * @return int
     */
    public function fromPresentationValue(int $value): int
    {
        return $value;
    }
}
