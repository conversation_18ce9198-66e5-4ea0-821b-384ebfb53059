<?php

namespace App\Affiliates;

use App\DTO\Affiliate\PayoutStrategyValueBoundaries;
use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;

class RawLeadFlatRatePayoutStrategyType extends PayoutStrategyType
{
    const int DEFAULT_FLAT_RATE_CENTS = 1000; //$10
    const int MAX_FLAT_RATE_CENTS = 10000; //$100
    const int MIN_FLAT_RATE_CENTS = 0;

    public function type(): PayoutStrategyTypeEnum
    {
        return PayoutStrategyTypeEnum::RAW_LEAD_FLAT_VALUE;
    }

    public function title(): string
    {
        return 'Raw Lead Flat Rate';
    }

    public function defaultValue(): int
    {
        return self::DEFAULT_FLAT_RATE_CENTS;
    }

    public function valueBoundaries(): PayoutStrategyValueBoundaries
    {
        return new PayoutStrategyValueBoundaries(
            max: self::MAX_FLAT_RATE_CENTS,
            min: self::MIN_FLAT_RATE_CENTS,
        );
    }

    public function calculate(
        ConsumerProduct $consumerProduct,
        PayoutStrategy $strategy
    ): int
    {
        return $strategy->value;
    }

    public function toPresentationValue(int $value): int
    {
        return round($value / 100); //convert to dollars
    }

    public function fromPresentationValue(int $value): int
    {
        return ($value * 100); //convert to cents
    }
}
