<?php

namespace App\Affiliates;

use App\DTO\Affiliate\PayoutStrategyValueBoundaries;
use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;

class SoldLeadFlatRatePayoutStrategyType extends PayoutStrategyType
{

    const int DEFAULT_FLAT_RATE_CENTS = 5000; //$50
    const int MAX_FLAT_RATE_CENTS = 10000; //$100
    const int MIN_FLAT_RATE_CENTS = 0;

    public function type(): PayoutStrategyTypeEnum
    {
        return PayoutStrategyTypeEnum::SOLD_LEAD_FLAT_VALUE;
    }

    public function title(): string
    {
        return 'Sold Lead Flat Rate';
    }


    public function defaultValue(): int
    {
        return self::DEFAULT_FLAT_RATE_CENTS;
    }

    public function valueBoundaries(): PayoutStrategyValueBoundaries
    {
        return new PayoutStrategyValueBoundaries(
            max: self::MAX_FLAT_RATE_CENTS,
            min: self::MIN_FLAT_RATE_CENTS,
        );
    }

    public function calculate(
        ConsumerProduct $consumerProduct,
        PayoutStrategy  $strategy,
    ): int
    {
        $productAssignments = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->get();

        if ($productAssignments->isEmpty()) {
            return 0;
        } else {
            return $strategy->value;
        }
    }

    public function toPresentationValue(int $value): int
    {
        return round($value / 100); //convert to dollars
    }

    public function fromPresentationValue(int $value): int
    {
        return ($value * 100); //convert to cents
    }
}
