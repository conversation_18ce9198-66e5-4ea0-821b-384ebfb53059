<?php

namespace App\Affiliates;

use App\DTO\Affiliate\PayoutStrategyValueBoundaries;
use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Illuminate\Contracts\Container\BindingResolutionException;

class RevenuePercentagePayoutStrategyType extends PayoutStrategyType
{

    const int DEFAULT_REVENUE_SHARE_PERCENTAGE = 50;
    const int MAX_REVENUE_SHARE_PERCENTAGE = 100;
    const int MIN_REVENUE_SHARE_PERCENTAGE = 0;

    public function type(): PayoutStrategyTypeEnum
    {
        return PayoutStrategyTypeEnum::REVENUE_PERCENTAGE;
    }

    public function title(): string
    {
        return 'Revenue Percentage';
    }


    public function defaultValue(): int
    {
        return self::DEFAULT_REVENUE_SHARE_PERCENTAGE;
    }

    public function valueBoundaries(): PayoutStrategyValueBoundaries
    {
        return new PayoutStrategyValueBoundaries(
            max: self::MAX_REVENUE_SHARE_PERCENTAGE,
            min: self::MIN_REVENUE_SHARE_PERCENTAGE,
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param PayoutStrategy $strategy
     * @return int
     * @throws BindingResolutionException
     */
    public function calculate(
        ConsumerProduct $consumerProduct,
        PayoutStrategy  $strategy,
    ): int
    {
        $atomicPercentage = $strategy->value;

        $productAssignments = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID, $consumerProduct->id)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_CANCELLATIONS)
            ->whereDoesntHave(ProductAssignment::RELATION_PRODUCT_REJECTIONS)
            ->get();

        if ($productAssignments->isEmpty()) {
            return 0;
        }

        $revenue = $productAssignments->sum(ProductAssignment::FIELD_COST);

        $payout = $revenue * $atomicPercentage;

        return round($payout);
    }
}
