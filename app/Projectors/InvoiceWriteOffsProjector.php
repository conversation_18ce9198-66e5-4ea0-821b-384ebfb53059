<?php

namespace App\Projectors;

use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\InvoiceStates;
use App\Events\Billing\StoredEvents\Invoice\WriteOff\InvoiceWrittenOff;
use App\Models\Billing\Invoice;
use App\Repositories\Billing\InvoiceWriteOffsRepository;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\InvoiceSnapshotService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceWriteOffsProjector extends Projector
{
    public function __construct(
        protected InvoiceWriteOffsRepository $InvoiceWriteOffsRepository,
        protected InvoiceSnapshotService $invoiceSnapshotService,
        protected InvoiceTransactionService $invoiceTransactionService,
        protected InvoiceService $invoiceService,
    )
    {
    }


    /**
     * @param InvoiceWrittenOff $event
     * @return void
     * @throws BindingResolutionException
     */
    public function onInvoiceWrittenOff(InvoiceWrittenOff $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $this->InvoiceWriteOffsRepository->create(
            uuid     : $event->uuid,
            invoiceId: $invoice->{Invoice::FIELD_ID},
            date     : $event->date,
            amount   : $event->amount,
            userId   : $event->authorId
        );

        $this->invoiceService->updateInvoiceStatus(
            invoice   : $invoice,
            newStatus : InvoiceStates::WRITTEN_OFF->value,
            authorType: InvoiceEventAuthorTypes::USER->value,
            authorId  : $event->authorId,
        );
    }
}
