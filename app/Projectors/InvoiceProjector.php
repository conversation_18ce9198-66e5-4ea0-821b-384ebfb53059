<?php

namespace App\Projectors;

use App\Enums\Billing\InvoiceStates;
use App\Events\Billing\StoredEvents\Invoice\InvoiceUpdated;
use App\Events\Billing\StoredEvents\Invoice\InvoiceInitialized;
use App\Events\Billing\StoredEvents\Invoice\InvoiceItemTaxApplied;
use App\Events\Billing\StoredEvents\Invoice\InvoiceStatusUpdated;
use App\Events\Billing\StoredEvents\Invoice\Pdf\InvoicePdfCreated;
use App\Events\Billing\StoredEvents\Invoice\Pdf\InvoicePdfFailed;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use App\Models\Odin\Company;
use App\Services\Billing\BillingProfile\BillingProfileService;
use App\States\Billing\Draft;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceProjector extends Projector
{
    public function __construct(protected BillingProfileService $billingProfileService)
    {

    }

    /**
     * @param InvoiceInitialized $event
     * @return void
     */
    public function onInvoiceInitialized(InvoiceInitialized $event): void
    {
        $company = Company::findByReference($event->companyReference);

        $invoice = new Invoice();

        $invoice->fill([
            Invoice::FIELD_UUID               => $event->invoiceUuid,
            Invoice::FIELD_COMPANY_ID         => $company->{Company::FIELD_ID},
            Invoice::FIELD_INVOICE_URL        => '',
            Invoice::FIELD_NOTES              => $event->notes,
            Invoice::FIELD_CREATED_BY_ID      => $event->authorId,
            Invoice::FIELD_ISSUE_AT           => $event->issueDate,
            Invoice::FIELD_DUE_AT             => $event->dueDate,
            Invoice::FIELD_BILLING_PROFILE_ID => $event->billingProfileId,
            Invoice::FIELD_STATUS             => (new Draft($invoice))->status()
        ]);

        $invoice->save();
    }

    /**
     * @param InvoiceStatusUpdated $event
     * @return void
     */
    public function onInvoiceStatusUpdated(InvoiceStatusUpdated $event): void
    {
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $state = InvoiceStates::tryFrom($event->newStatus);

        $invoice->status->transitionTo($state->getClass());

        if ($event->newStatus === InvoiceStates::ISSUED->value) {
            $this->billingProfileService->updateBillingProfileLastBilledAt(
                billingProfileId: $invoice->{Invoice::FIELD_BILLING_PROFILE_ID}
            );
        }
    }

    /**
     * @param InvoiceUpdated $event
     * @return void
     */
    public function onInvoiceUpdated(InvoiceUpdated $event)
    {
        $data = collect($event->changes)->mapWithKeys(fn($c) => ([
            $c['key'] => $c['new']
        ]));

        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $invoice->update($data->toArray());
    }


    /**
     * @param InvoiceItemTaxApplied $event
     * @return void
     */
    public function onInvoiceItemTaxApplied(InvoiceItemTaxApplied $event): void
    {
        $invoiceItem = InvoiceItem::query()->findOrFail($event->invoiceItemId);

        $invoiceItem->{InvoiceItem::FIELD_TAX} = $event->tax;

        $invoiceItem->save();
    }

    /**
     * @param InvoicePdfCreated $event
     * @return void
     */
    public function onInvoicePdfCreated(InvoicePdfCreated $event): void
    {
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $invoice->update([
            Invoice::FIELD_INVOICE_URL => $event->filepath,
            Invoice::FIELD_PDF_FAILED  => false,
        ]);
    }

    /**
     * @param InvoicePdfFailed $event
     * @return void
     */
    public function onInvoicePdfFailed(InvoicePdfFailed $event): void
    {
        Invoice::query()
            ->where(Invoice::FIELD_UUID, $event->invoiceUuid)
            ->update([
                Invoice::FIELD_PDF_FAILED => true
            ]);
    }
}
