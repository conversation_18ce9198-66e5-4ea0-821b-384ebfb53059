<?php

namespace App\Projectors;

use App\Enums\Billing\InvoiceItemTypes;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemAdded;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemDeleted;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemUpdated;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceItem;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceItemProjector extends Projector
{
    /**
     * @param InvoiceItemAdded $event
     * @return void
     */
    public function onInvoiceItemAdded(InvoiceItemAdded $event): void
    {
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $invoiceItem = new InvoiceItem();

        $invoiceItem->fill([
            InvoiceItem::FIELD_INVOICE_ID    => $invoice->{Invoice::FIELD_ID},
            InvoiceItem::FIELD_DESCRIPTION   => $event->description,
            InvoiceItem::FIELD_BILLABLE_ID   => $event->billableId,
            InvoiceItem::FIELD_BILLABLE_TYPE => InvoiceItemTypes::tryFrom($event->billableType)->getClass(),
            InvoiceItem::FIELD_UNIT_PRICE    => $event->unitPrice,
            InvoiceItem::FIELD_QUANTITY      => $event->quantity,
            InvoiceItem::FIELD_BILLABLE_ITEM => $event->invoiceUuid,
            InvoiceItem::FIELD_ADDED_BY      => $event->authorId,
        ]);

        $invoiceItem->save();
    }

    /**
     * @param InvoiceItemDeleted $event
     * @return void
     */
    public function onInvoiceItemDeleted(InvoiceItemDeleted $event): void
    {
        /** @var InvoiceItem $invoiceItem */
        $invoiceItem = InvoiceItem::query()->find($event->invoiceItemId);

        $invoiceItem->delete();
    }

    /**
     * @param InvoiceItemUpdated $event
     * @return void
     */
    public function onInvoiceItemUpdated(InvoiceItemUpdated $event): void
    {
        /** @var InvoiceItem $invoiceItem */
        $invoiceItem = InvoiceItem::query()->find($event->invoiceItemId);

        $data = collect($event->changes)->mapWithKeys(fn($change) => [$change['key'] => $change['new']]);

        $invoiceItem->update($data->toArray());
    }
}
