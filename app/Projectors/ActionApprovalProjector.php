<?php

namespace App\Projectors;

use App\Enums\Billing\ApprovalStatus;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalCancelled;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalRequested;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalReviewed;
use App\Models\Billing\ActionApproval;
use App\Repositories\Billing\ActionApprovalRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class ActionApprovalProjector extends Projector
{
    public function __construct(protected ActionApprovalRepository $actionApprovalRepository)
    {

    }

    /**
     * @param ActionApprovalRequested $event
     * @return void
     */
    public function onActionApprovalRequested(ActionApprovalRequested $event): void
    {
        if (!class_exists($event->modelClass)) {
            throw new ModelNotFoundException("The model for $event->modelClass could not be found.");
        }

        $this->actionApprovalRepository->create(
            uuid       : $event->actionApprovalUuid,
            modelId    : $event->modelId,
            modelClass : $event->modelClass,
            action     : $event->actionRequested,
            payload    : [
                'arguments' => $event->arguments
            ],
            status     : ApprovalStatus::PENDING->value,
            requestedBy: $event->authorId,
            note       : $event->note,
        );
    }

    /**
     * @param ActionApprovalReviewed $event
     * @return void
     */
    public function onActionApprovalReviewed(ActionApprovalReviewed $event): void
    {
        $actionApproval = ActionApproval::findByUuid($event->actionApprovalUuid);

        $actionApproval->update([
            ActionApproval::FIELD_REVIEWED_BY   => $event->authorId,
            ActionApproval::FIELD_STATUS        => $event->status,
            ActionApproval::FIELD_REASON        => $event->reason,
            ActionApproval::FIELD_REVIEWED_AT   => $event->createdAt(),
            ActionApproval::FIELD_IS_PROCESSING => true,
        ]);
    }

    public function onActionApprovalCancelled(ActionApprovalCancelled $event): void
    {
        $actionApproval = ActionApproval::findByUuid($event->actionApprovalUuid);

        $actionApproval->update([
            ActionApproval::FIELD_STATUS        => ApprovalStatus::CANCELLED,
        ]);
    }
}
