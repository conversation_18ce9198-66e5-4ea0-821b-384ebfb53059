<?php

namespace App\Projectors;

use App\Events\Billing\StoredEvents\Credit\CreditAdded;
use App\Events\Billing\StoredEvents\Credit\CreditAppliedToInvoice;
use App\Events\Billing\StoredEvents\Credit\CreditDeducted;
use App\Events\Billing\StoredEvents\Credit\CreditExpired;
use App\Events\Billing\StoredEvents\Credit\CreditExtended;
use App\Models\Billing\Credit;
use App\Models\Billing\CreditBillingProfile;
use App\Models\Billing\CreditType;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceCredit;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class CreditProjector extends Projector
{
    /**
     * @param CreditAdded $event
     * @return void
     */
    public function onCreditAdded(CreditAdded $event): void
    {
        $company = Company::findByReference($event->companyReference);

        $credit = new Credit();
        /* @var CreditType $creditType */
        $creditType = CreditType::query()->where(CreditType::FIELD_SLUG, $event->type)->first();

        $expiresAt = $creditType?->{CreditType::FIELD_EXPIRES_IN_DAYS}
            ? Carbon::now()->addDays($creditType?->{CreditType::FIELD_EXPIRES_IN_DAYS})
            : null;

        $credit->fill([
            Credit::FIELD_UUID            => $event->uuid,
            Credit::FIELD_COMPANY_ID      => $company->{Company::FIELD_ID},
            Credit::FIELD_CREDIT_TYPE     => $creditType->slug,
            Credit::FIELD_INITIAL_VALUE   => $event->amount,
            Credit::FIELD_REMAINING_VALUE => $event->amount,
            Credit::FIELD_EXPIRES_AT      => $expiresAt,
            Credit::FIELD_NOTES           => $event->notes
        ]);

        $credit->save();

        $data = collect($event->billingProfileIds)->map(fn($id) => [
            CreditBillingProfile::FIELD_BILLING_PROFILE_ID => $id,
            CreditBillingProfile::FIELD_CREDIT_ID          => $credit->id,
            CreditBillingProfile::FIELD_CREATED_AT         => now(),
            CreditBillingProfile::FIELD_UPDATED_AT         => now(),
        ]);

        CreditBillingProfile::query()->insert($data->toArray());
    }

    /**
     * @param CreditExpired $event
     * @return void
     */
    public function onCreditExpired(CreditExpired $event): void
    {
        /* @var Credit $credit */
        $credit = Credit::query()->findOrFail($event->creditId);
        $credit->expires_at = Carbon::now();
        $credit->save();
    }

    /**
     * @param CreditExtended $event
     * @return void
     */
    public function onCreditExtended(CreditExtended $event): void
    {
        /* @var Credit $credit */
        $credit = Credit::query()->findOrFail($event->creditId);
        $credit->expires_at = $event->newDate;
        $credit->save();
    }

    /**
     * @param CreditDeducted $event
     * @return void
     */
    public function onCreditDeducted(CreditDeducted $event): void
    {
        /* @var Credit $credit */
        $credit = Credit::query()->findOrFail($event->creditId);
        $credit->remaining_value = $credit->remaining_value - $event->amount;
        $credit->save();
    }

    /**
     * @param CreditAppliedToInvoice $event
     * @return void
     */
    public function onCreditAppliedToInvoice(CreditAppliedToInvoice $event): void
    {
        /** @var Credit $companyCredit */
        $companyCredit = Credit::findByUuid($event->creditUuid);
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        InvoiceCredit::query()->create([
            InvoiceCredit::FIELD_INVOICE_ID     => $invoice->id,
            InvoiceCredit::FIELD_CREDIT_ID      => $companyCredit->id,
            InvoiceCredit::FIELD_AMOUNT_APPLIED => $event->amount,
            InvoiceCredit::FIELD_APPLIED_AT     => $event->createdAt(),
            InvoiceCredit::FIELD_AUTHOR_ID      => $event->authorId,
            InvoiceCredit::FIELD_AUTHOR_TYPE    => $event->authorType
        ]);

        $companyCredit->update([
            Credit::FIELD_REMAINING_VALUE => $companyCredit->remaining_value - $event->amount
        ]);

        $companyCredit->save();
    }
}
