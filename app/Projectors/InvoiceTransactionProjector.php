<?php

namespace App\Projectors;

use App\Enums\Billing\InvoiceTransactionType;
use App\Enums\InvoiceRefundStatus;
use App\Events\Billing\StoredEvents\Invoice\InvoiceTransactionCreated;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceChargeRefundSuccess;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceRefund;
use App\Models\Billing\InvoiceRefundCharge;
use App\Models\Billing\InvoiceTransaction;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundChargeRepository;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundItemsRepository;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundRepository;
use App\Repositories\Billing\InvoiceTransactionRepository;
use App\Services\Billing\InvoiceSnapshotService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceTransactionProjector extends Projector
{
    public function __construct(
        protected InvoiceTransactionRepository $invoiceTransactionRepository,
        protected InvoiceTransactionService $invoiceTransactionService,
        protected InvoiceSnapshotService $invoiceSnapshotService,
        protected InvoiceRefundRepository $invoiceRefundRepository,
        protected InvoiceRefundChargeRepository $refundChargeRepository,
        protected InvoiceRefundItemsRepository $refundItemsRepository,
    )
    {
    }

    /**
     * @param InvoiceChargeRefundSuccess $event
     * @return void
     * @throws Exception
     */
    public function onInvoiceChargeRefundSuccess(InvoiceChargeRefundSuccess $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::query()->where(Invoice::FIELD_UUID, $event->invoiceUuid)->first();

        $payload = [
            'amount_captured'    => $event->amountCaptured,
            'transaction_amount' => $event->amount,
        ];

        $refundTransactionUuid = Str::uuid()->toString();

        $this->invoiceTransactionService->createInvoiceTransaction(
            uuid             : $refundTransactionUuid,
            invoiceUuid      : $event->invoiceUuid,
            externalReference: $event->externalTransactionId,
            amount           : $event->amount,
            currency         : $event->currency,
            type             : $event->type,
            origin           : $event->source,
            payload          : $payload,
            scenario         : $event->scenario,
            scope            : $event->scope,
        );

        //find a relevant refundCharge if it exists,
        $refundCharge = $this->refundChargeRepository->getInvoiceRefundChargeByAssociatedValues(
            refundedTransactionReference: $event->externalTransactionId,
            invoiceUuid                 : $event->invoiceUuid,
            refundAmount                : $event->amount,
            invoiceRefundChargeStatus   : [InvoiceRefundStatus::PENDING, InvoiceRefundStatus::REQUESTED],
        );

        if ($refundCharge) {
            $refundCharge->update([
                InvoiceRefundCharge::FIELD_REFUND_TRANSACTION_UUID => $refundTransactionUuid,
                InvoiceRefundCharge::FIELD_REQUEST_STATUS          => InvoiceRefundStatus::REFUNDED
            ]);

            /** @var InvoiceRefund $invoiceRefund */
            $invoiceRefund = $refundCharge->{InvoiceRefundCharge::RELATION_INVOICE_REFUND};

            $this->invoiceRefundRepository->updateInvoiceRefundStatus($invoiceRefund);


        } else {// if not create a new InvoiceRefund, InvoiceRefundItem, and InvoiceRefundCharge
            /** @var InvoiceTransaction $refundedPayment */
            $refundedPayment = $this->invoiceTransactionService->getInvoiceTransactions(
                invoiceUuid      : $event->invoiceUuid,
                type             : InvoiceTransactionType::PAYMENT,
                externalReference: $event->externalTransactionId,
            )->first();

            $invoiceRefund = $this->invoiceRefundRepository->createRefund(
                uuid     : Str::uuid(),
                invoiceId: $invoice->id,
                total    : $event->amount,
                status   : InvoiceRefundStatus::REFUNDED,
                reason   : $event->reason,
            );

            $this->refundItemsRepository->createRefundItem(
                invoiceRefundId: $invoiceRefund->id,
                value          : $event->amount,
                invoiceItemId  : null,
            );

            $this->refundChargeRepository->createRefundCharge(
                uuid                 : Str::uuid(),
                amount               : $event->amount,
                requestStatus        : InvoiceRefundStatus::REFUNDED,
                invoiceRefundId      : $invoiceRefund->id,
                refundedPaymentId    : $refundedPayment->id,
                refundTransactionUuid: $refundTransactionUuid
            );
        }
    }

    /**
     * @param InvoiceTransactionCreated $event
     * @return void
     */
    public function onInvoiceTransactionCreated(InvoiceTransactionCreated $event): void
    {
        $transaction = new InvoiceTransaction([
            InvoiceTransaction::FIELD_UUID               => $event->uuid,
            InvoiceTransaction::FIELD_INVOICE_UUID       => $event->invoiceUuid,
            InvoiceTransaction::FIELD_EXTERNAL_REFERENCE => $event->externalReference,
            InvoiceTransaction::FIELD_AMOUNT             => $event->amount,
            InvoiceTransaction::FIELD_CURRENCY           => $event->currency,
            InvoiceTransaction::FIELD_TYPE               => $event->type,
            InvoiceTransaction::FIELD_ORIGIN             => $event->origin,
            InvoiceTransaction::FIELD_PAYLOAD            => $event->payload,
            InvoiceTransaction::FIELD_SCENARIO           => $event->scenario,
            InvoiceTransaction::FIELD_SCOPE              => $event->scope,
            InvoiceTransaction::FIELD_DATE               => $event->date ?? now(),
        ]);

        $transaction->save();
    }
}
