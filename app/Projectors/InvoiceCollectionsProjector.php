<?php

namespace App\Projectors;

use App\Enums\Billing\InvoiceTransactionScenario;
use App\Enums\Billing\InvoiceTransactionType;
use App\Events\Billing\StoredEvents\Invoice\Collections\IssueInvoiceToCollections;
use App\Events\Billing\StoredEvents\Invoice\Collections\UpdateInvoiceCollections;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceTransaction;
use App\Repositories\Billing\InvoiceCollectionsRepository;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\InvoiceSnapshotService;
use App\Services\Billing\InvoiceTransactions\InvoiceTransactionService;
use Exception;
use Illuminate\Support\Str;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceCollectionsProjector extends Projector
{
    public function __construct(
        protected InvoiceCollectionsRepository $invoiceCollectionRepository,
        protected InvoiceSnapshotService $invoiceSnapshotService,
        protected InvoiceTransactionService $invoiceTransactionService,
        protected InvoiceService $invoiceService,
    )
    {
    }


    /**
     * @param IssueInvoiceToCollections $event
     * @return void
     * @throws Exception
     */
    public function onIssueInvoiceToCollections(IssueInvoiceToCollections $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $this->invoiceCollectionRepository->create(
            uuid           : $event->uuid,
            invoiceId      : $invoice->{Invoice::FIELD_ID},
            sentDate       : $event->sentDate,
            recoveryStatus : $event->recoveryStatus,
            amountCollected: $event->amountCollected,
            userId         : $event->authorId
        );
    }

    /**
     * @param UpdateInvoiceCollections $event
     * @return void
     * @throws Exception
     */
    public function onUpdateInvoiceCollections(UpdateInvoiceCollections $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        InvoiceTransaction::query()
            ->where(InvoiceTransaction::FIELD_INVOICE_UUID, $event->invoiceUuid)
            ->where(InvoiceTransaction::FIELD_TYPE, InvoiceTransactionType::COLLECTIONS->value)
            ->latest()
            ->delete();

        if (!empty($event->amountRecovered)) {
            $this->invoiceTransactionService->createInvoiceTransaction(
                uuid             : Str::uuid()->toString(),
                invoiceUuid      : $event->invoiceUuid,
                externalReference: Str::uuid(),
                amount           : $event->amountRecovered,
                currency         : 'usd',
                type             : InvoiceTransactionType::COLLECTIONS->value,
                origin           : 'A20',
                scenario         : InvoiceTransactionScenario::WON->value
            );
        }

        if (!empty($event->amountLost)) {
            $this->invoiceTransactionService->createInvoiceTransaction(
                uuid             : Str::uuid()->toString(),
                invoiceUuid      : $event->invoiceUuid,
                externalReference: Str::uuid(),
                amount           : $event->amountLost,
                currency         : 'usd',
                type             : InvoiceTransactionType::COLLECTIONS->value,
                origin           : 'A20',
                scenario         : InvoiceTransactionScenario::LOST->value
            );
        }

        $this->invoiceCollectionRepository->update(
            invoiceId      : $invoice->{Invoice::FIELD_ID},
            recoveryStatus : $event->recoveryStatus,
            amountRecovered: $event->amountRecovered,
            userId         : $event->authorId,
            recoveryDate   : $event->recoveryDate,
        );
    }
}
