<?php

namespace App\Projectors;

use App\Events\Billing\StoredEvents\Invoice\InvoiceSnapshotCreated;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceSnapshot;
use App\Services\Billing\InvoiceSnapshotService;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceSnapshotProjector extends Projector
{
    public function __construct(protected InvoiceSnapshotService $invoiceSnapshotService)
    {
    }

    /**
     * @param InvoiceSnapshotCreated $event
     * @return void
     */
    public function onInvoiceSnapshotCreated(InvoiceSnapshotCreated $event): void
    {
        // Check if we are not going to end up having duplicates
        InvoiceSnapshot::query()->firstOrCreate(
            [
                InvoiceSnapshot::FIELD_INVOICE_ID                      => Invoice::findByUuid($event->invoiceUuid)->id,
                InvoiceSnapshot::FIELD_COMPANY_ID                      => $event->companyId,
                InvoiceSnapshot::FIELD_ACCOUNT_MANAGER_ID              => $event->accountManagerId,
                InvoiceSnapshot::FIELD_SUCCESS_MANAGER_ID              => $event->successManagerId,
                InvoiceSnapshot::FIELD_BUSINESS_DEVELOPMENT_MANAGER_ID => $event->businessDevelopmentManagerId,
                InvoiceSnapshot::FIELD_STATUS                          => $event->status,
                InvoiceSnapshot::FIELD_TOTAL_VALUE                     => $event->totalValue,
                InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING               => $event->totalOutstanding,
                InvoiceSnapshot::FIELD_TOTAL_REFUNDED                  => $event->totalRefunded,
                InvoiceSnapshot::FIELD_TOTAL_PAID                      => $event->totalPaid,
                InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS               => $event->totalCollections,
                InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_RECOVERED     => $event->totalCollectionsRecovered,
                InvoiceSnapshot::FIELD_TOTAL_COLLECTIONS_LOST          => $event->totalCollectionsLost,
                InvoiceSnapshot::FIELD_TOTAL_CREDIT_APPLIED            => $event->totalCreditsApplied,
                InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_WON            => $event->totalChargebackWon,
                InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK_LOST           => $event->totalChargebackLost,
                InvoiceSnapshot::FIELD_TOTAL_CHARGEBACK                => $event->totalChargeback,
                InvoiceSnapshot::FIELD_TOTAL_WRITTEN_OFF               => $event->totalWrittenOff,
            ], [
                InvoiceSnapshot::FIELD_DATE => $event->date ?? now(),
                InvoiceSnapshot::FIELD_UUID => $event->uuid
            ]
        );
    }
}
