<?php

namespace App\Projectors;

use App\Enums\Billing\InvoicePaymentChargeStatus;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequest;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestAttempted;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestFailed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestMaxAttemptsExceeded;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestSuccess;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeScheduled;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentCanceled;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoicePaymentFailed;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoicePaymentCharge;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoicePaymentProjector extends Projector
{
    /**
     * @param InvoiceChargeRequest $event
     * @return void
     */
    public function onInvoiceChargeRequest(InvoiceChargeRequest $event): void
    {
        $payment = InvoicePayment::findByUuid($event->uuid);

        if (!$payment) {
            InvoicePayment::query()->create([
                InvoicePayment::FIELD_UUID                            => $event->uuid,
                InvoicePayment::FIELD_INVOICE_ID                      => Invoice::findByUuid($event->invoiceUuid)->id,
                InvoicePayment::FIELD_TOTAL                           => $event->total,
                InvoicePayment::FIELD_STATUS                          => 'pending',
                InvoicePayment::FIELD_AUTHOR_ID                       => $event->authorId,
                InvoicePayment::FIELD_AUTHOR_TYPE                     => $event->authorType,
                InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD => $event->maxAttempts,
                InvoicePayment::FIELD_ATTEMPT_NUMBER                  => 1,
                InvoicePayment::FIELD_NEXT_ATTEMPT_AT                 => $event->attemptChargeDate,
                InvoicePayment::FIELD_CREATED_AT                      => $event->invoicePaymentDate,
            ]);
        } else {
            $payment->update([
                InvoicePayment::FIELD_STATUS         => 'pending',
                InvoicePayment::FIELD_ATTEMPT_NUMBER => $payment->{InvoicePayment::FIELD_ATTEMPT_NUMBER} + 1,
            ]);
        }
    }

    /**
     * @param InvoiceChargeScheduled $event
     * @return void
     */
    public function onInvoiceChargeRequestScheduled(InvoiceChargeScheduled $event): void
    {
        $payment = InvoicePayment::findByUuid($event->uuid);

        if (!$payment) {
            $invoice = Invoice::findByUuid($event->invoiceUuid);

            InvoicePayment::query()->create([
                InvoicePayment::FIELD_UUID                            => $event->uuid,
                InvoicePayment::FIELD_INVOICE_ID                      => $invoice->id,
                InvoicePayment::FIELD_TOTAL                           => $event->total,
                InvoicePayment::FIELD_STATUS                          => 'rescheduled',
                InvoicePayment::FIELD_AUTHOR_ID                       => $event->authorId,
                InvoicePayment::FIELD_AUTHOR_TYPE                     => $event->authorType,
                InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD => $event->maxAttempts,
                InvoicePayment::FIELD_ATTEMPT_NUMBER                  => 0,
                InvoicePayment::FIELD_NEXT_ATTEMPT_AT                 => $event->attemptChargeDate,
            ]);
        } else {
            $payment->update([
                InvoicePayment::FIELD_STATUS          => 'rescheduled',
                InvoicePayment::FIELD_NEXT_ATTEMPT_AT => $event->attemptChargeDate,
            ]);
        }
    }

    /**
     * @param InvoiceChargeRequestAttempted $event
     * @return void
     */
    public function onInvoiceChargeRequestAttempted(InvoiceChargeRequestAttempted $event): void
    {
        $payment = InvoicePayment::findByUuid($event->invoicePaymentUuid);

        $paymentCharge = new InvoicePaymentCharge([
            InvoicePaymentCharge::FIELD_UUID               => $event->invoicePaymentChargeUuid,
            InvoicePaymentCharge::FIELD_INVOICE_PAYMENT_ID => $payment->id,
            InvoicePaymentCharge::FIELD_PAYMENT_METHOD_ID  => $event->paymentMethodId,
            InvoicePaymentCharge::FIELD_TOTAL              => $event->amount,
            InvoicePaymentCharge::FIELD_STATUS             => InvoicePaymentChargeStatus::PENDING->value,
            InvoicePaymentCharge::FIELD_CREATED_AT         => $event->attemptedAt
        ]);

        $paymentCharge->save();
    }

    /**
     * @param InvoiceChargeRequestSuccess $event
     * @return void
     */
    public function onInvoiceChargeRequestSuccess(InvoiceChargeRequestSuccess $event): void
    {
        InvoicePaymentCharge::query()
            ->where(InvoicePaymentCharge::FIELD_UUID, $event->invoicePaymentChargeUuid)
            ->update([
                InvoicePaymentCharge::FIELD_STATUS => InvoicePaymentChargeStatus::REQUESTED->value,
            ]);

        $invoicePayment = InvoicePayment::findByUuid($event->invoicePaymentUuid);

        $fullRequestSuccessful = $invoicePayment->{InvoicePayment::FIELD_TOTAL} - $event->amount === 0;

        if ($fullRequestSuccessful) {
            $invoicePayment->update([
                InvoicePayment::FIELD_STATUS       => InvoicePaymentStatus::REQUESTED->value,
                InvoicePayment::FIELD_REQUESTED_AT => $event->requestedAt,
            ]);
        }
    }

    /**
     * @param InvoiceChargeRequestMaxAttemptsExceeded $event
     * @return void
     */
    public function onInvoiceChargeRequestMaxAttemptsExceeded(InvoiceChargeRequestMaxAttemptsExceeded $event): void
    {
        $paymentCharge = InvoicePaymentCharge::findByUuid($event->invoicePaymentChargeUuid);

        $paymentCharge->update([
            InvoicePaymentCharge::FIELD_STATUS => InvoicePaymentChargeStatus::FAILED->value,
            InvoicePaymentCharge::FIELD_ERROR_MESSAGE => 'Max attempts exceeded',
        ]);
    }

    /**
     * @param InvoiceChargeRequestFailed $event
     * @return void
     */
    public function onInvoiceChargeRequestFailed(InvoiceChargeRequestFailed $event): void
    {
        $paymentCharge = InvoicePaymentCharge::findByUuid($event->invoicePaymentChargeUuid);

        $paymentCharge->update([
            InvoicePaymentCharge::FIELD_STATUS        => InvoicePaymentChargeStatus::FAILED->value,
            InvoicePaymentCharge::FIELD_ERROR_MESSAGE => $event->errorMessage,
        ]);
    }

    /**
     * @param InvoicePaymentFailed $event
     * @return void
     */
    public function onInvoicePaymentFailed(InvoicePaymentFailed $event): void
    {
        InvoicePayment::query()->where(InvoicePayment::FIELD_UUID, $event->invoicePaymentUuid)
            ->update([
                InvoicePayment::FIELD_ERROR_MESSAGE   => $event->errorMessage,
                InvoicePayment::FIELD_STATUS          => InvoicePaymentStatus::FAILED->value,
                InvoicePayment::FIELD_NEXT_ATTEMPT_AT => null
            ]);
    }

    /**
     * @param InvoicePaymentCanceled $event
     * @return void
     */
    public function onInvoicePaymentCanceled(InvoicePaymentCanceled $event): void
    {
        InvoicePayment::query()->where(InvoicePayment::FIELD_UUID, $event->invoicePaymentUuid)
            ->update([
                InvoicePayment::FIELD_STATUS          => InvoicePaymentStatus::CANCELED->value,
                InvoicePayment::FIELD_NEXT_ATTEMPT_AT => null
            ]);
    }
}
