<?php

namespace App\Visitors;

use App\Abstracts\Workflows\Action;
use App\Abstracts\Workflows\AssignsAccountManager;
use App\Contracts\VisitorContract;
use App\Events\AccountManagerAssignedEvent;
use App\Models\AccountManagerClient;
use App\Models\Odin\Company;
use App\Repositories\CompanyAccountManagerRepository;
use App\Services\RoundRobins\RoundRobinService;

class AssignAccountManagerVisitor implements VisitorContract
{
    public function __construct(
        protected RoundRobinService $roundRobinService,
        protected CompanyAccountManagerRepository $repository
    ) { }

    /**
     * Handles assigning an account manager.
     *
     * @param Action $action
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Action $action, \Closure $next): mixed
    {
        if ($action instanceof AssignsAccountManager) {
            $companyReference = $action->getPayload()->event->get('company_reference', null);

            /** @var Company|null $company */
            $company = $companyReference ? Company::query()->where(Company::FIELD_REFERENCE, $companyReference)->first() : null;


            if($company) {
                $accountManagerClient = $this->repository->assignAccountManager(
                    $company->id,
                    $this->roundRobinService->executeRoundRobin(
                        $action->roundRobin(),
                        $action->burnsTurn()
                    )
                );

                if ($accountManagerClient) $this->fireEvent($accountManagerClient, $companyReference);
            }

        }

        return $next($action);
    }

    /**
     * @param AccountManagerClient $accountManagerClient
     * @param string $companyReference
     * @return void
     */
    protected function fireEvent(AccountManagerClient $accountManagerClient, string $companyReference): void
    {
        AccountManagerAssignedEvent::dispatch([
            'account_manager_id' => $accountManagerClient->accountManager?->user?->legacy_user_id,
            'company_reference'  => $companyReference
        ]);
    }
}
