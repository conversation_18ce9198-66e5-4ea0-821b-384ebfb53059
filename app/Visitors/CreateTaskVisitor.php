<?php

namespace App\Visitors;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\CreateTaskVisitorContract;
use App\Jobs\HandleTaskNotifier;
use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Models\TaskNote;
use App\Repositories\TaskRepository;
use App\Services\Tasks\TaskTimezoneService;
use App\Workflows\Actions\CreateTaskAction;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use Closure;
use Exception;

class CreateTaskVisitor implements CreateTaskVisitorContract
{
    public function __construct(protected WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory) {}

    /**
     * @inheritDoc
     */
    public function handle(Action $action, Closure $next): mixed
    {
        if ($action instanceof CreateTaskAction) {
            $this->createTask($action);
        }

        return $next($action);
    }

    /**
     * @param CreateTaskAction $action
     * @throws Exception
     */
    protected function createTask(CreateTaskAction $action)
    {
        $assignee = $action->getAssignedUserId();

        $runningWorkflow = RunningWorkflow::query()->find($action->getRunningWorkflowId());

        $isMuted = false;

        if ($runningWorkflow) {
            $currentActionId = $runningWorkflow->current_action_id;

            $mutedTaskExists = Task::query()
                ->where(Task::FIELD_ASSIGNED_USER_ID, $assignee)
                ->whereHas(Task::RELATION_RUNNING_WORKFLOW, function ($query) use ($currentActionId) {
                    $query->where('current_action_id', $currentActionId);
                })
                ->where(Task::FIELD_MUTED, true)
                ->exists();

            if ($mutedTaskExists) {
                $isMuted = true;
            }
        }

        if (!empty($assignee)) {
            /** @var Task $task */
            $task = Task::query()->create([
                Task::FIELD_ASSIGNED_USER_ID      => $assignee,
                Task::FIELD_SUBJECT               => ($this->workflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER))->handle($action->getSubject(), $action->getPayload()),
                Task::FIELD_TASK_TYPE_ID          => $action->getTaskType(),
                Task::FIELD_AVAILABLE_AT          => $action->getAvailableAt(),
                Task::FIELD_RESULTS               => $action->getResults(),
                Task::FIELD_RUNNING_WORKFLOW_ID   => $action->getRunningWorkflowId(),
                Task::FIELD_PRIORITY              => $action->getPriority(),
                Task::FIELD_COMPLETED             => Task::TASK_NOT_COMPLETED,
                Task::FIELD_ALLOWS_RESCHEDULING   => $action->getReschedulingStatus(),
                Task::FIELD_TASK_CATEGORY_ID      => $action->getTaskCategory(),
                Task::FIELD_DYNAMIC_PRIORITY      => $action->getDynamicPriorityStatus(),
                Task::FIELD_DYNAMIC_PRIORITY_TYPE => $action->getDynamicPriorityType(),
                Task::FIELD_COUNTY_LOCATION_ID    => $action->getCountyLocationId(),
                Task::FIELD_MUTED                 => $isMuted ? 1 : 0,
            ]);

            $this->createNotifiers($action, $task->id);
            $this->inheritTaskNotes($action, $task->id);
            $this->calculateTaskTimezone($task);
        } else {
            $target = $action->getTaskTarget();

            logger()->error("Task can't be created since the system couldn't fetch an assignee for it. Technical insight - "
                .PHP_EOL."Task Event Category  : `{$action->getTaskEventCategory()}`"
                .PHP_EOL."Task Event Name      : `{$action->getTaskEventName()}`"
                .PHP_EOL."Task Target Type     : `{$target->getTargetType()?->value}`"
                .PHP_EOL."Task Target Relation : `{$target->getRelationType()?->value}`"
                .PHP_EOL."Task Category ID     : `{$action->getTaskCategory()}`"
                .PHP_EOL."Running Workflow ID  : `{$action->getRunningWorkflowId()}`"
            );
        }
    }

    /**
     * Handles creating task notifiers.
     *
     * @param CreateTaskAction $action
     * @param int $taskId
     * @return void
     */
    protected function createNotifiers(CreateTaskAction $action, int $taskId): void
    {
        foreach ($action->getNotifiers() as $notifier) {
            HandleTaskNotifier::dispatch($action->getRunningWorkflowId(), $taskId, $notifier->getTemplateId())->delay($notifier->getDelay() * 60);
        }
    }

    /**
     * Handles inheriting task notes.
     *
     * @param CreateTaskAction $action
     * @param int $taskId
     * @return void
     */
    protected function inheritTaskNotes(CreateTaskAction $action, int $taskId): void
    {
        if ($action->shouldInheritTaskNotes() && $action->getRunningWorkflowId() !== null) {
            /** @var RunningWorkflow|null $workflow */
            $workflow = RunningWorkflow::query()->find($action->getRunningWorkflowId());

            if ($workflow !== null && $workflow->payload->get('last_task', null) !== null) {
                /** @var Task $task */
                $task = Task::query()->find($workflow->payload->get('last_task'));

                if ($task !== null) {
                    foreach ($task->taskNotes as $note)
                        $this->createTaskNote($taskId, $note);
                }
            }
        }
    }

    /**
     * Creates a task note from an existing task note.
     *
     * @param int $taskId
     * @param TaskNote $note
     * @return void
     */
    protected function createTaskNote(int $taskId, TaskNote $note): void
    {
        TaskNote::query()->create(
            [
                TaskNote::FIELD_TASK_ID => $taskId,
                TaskNote::FIELD_USER_ID => $note->user_id,
                TaskNote::FIELD_NOTE    => $note->note
            ]
        );
    }

    /**
     * Handles calculating the timezone for a task, and then saving the timezone for the task.
     *
     * @param Task $task
     * @return void
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    protected function calculateTaskTimezone(Task $task): void
    {
        /** @var TaskRepository $repository */
        $repository = app()->make(TaskRepository::class);
        $repository->storeTimezoneForTask($task);
    }
}
