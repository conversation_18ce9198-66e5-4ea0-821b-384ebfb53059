<?php

namespace App\Visitors;

use App\Abstracts\Workflows\Action;
use App\Abstracts\Workflows\WorkflowShortcodeServiceAbstract;
use App\Contracts\Workflows\HasTargetContract;
use App\Events\Workflows\ActionCreatedEvent;
use App\Mail\Workflows\EmailNotification;
use App\Models\Odin\Company;
use App\Services\Delivery\Email;
use App\Workflows\Actions\AddNotificationAction;
use App\Contracts\VisitorContract;
use App\Enums\Channel;
use App\Enums\Target;
use App\Services\NotificationService;
use App\Repositories\TargetRepository;
use App\Workflows\Actions\EmailNotificationAction;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use Closure;
use Exception;

class HandlesNotificationVisitor implements VisitorContract
{
    private WorkflowShortcodeServiceAbstract $shortcodeService;

    /**
     * @param NotificationService $notificationService
     * @param TargetRepository $targetRepository
     * @param WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory
     * @throws Exception
     */
    public function __construct(
        protected NotificationService $notificationService,
        protected TargetRepository $targetRepository,
        protected WorkflowShortcodeServiceFactory $workflowShortcodeServiceFactory
    )
    {
        $this->shortcodeService = $this->workflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER);
    }

    /**
     * @param Action  $action
     * @param Closure $next
     * @return mixed
     */
    public function handle(Action $action, Closure $next)
    {
        if($action instanceof AddNotificationAction)
        {
            switch ($action->getChannel())
            {
                case Channel::DEFAULT:
                    $this->handleDefaultChannel($action);
                    break;
                case Channel::ACTIONS:
                    $this->handleActionsChannel($action);
                    break;
            }
        }

        if ($action instanceof EmailNotificationAction) {
            if ($action->getChannel() === Channel::EMAIL) {
                $this->handleEmailNotification($action);
            }
        }

        return $next($action);
    }

    /**
     * @param AddNotificationAction $action
     * @return void
     */
    protected function handleDefaultChannel(AddNotificationAction $action): void
    {
        foreach ($action->getTargets() as $target)
        {
            switch ($target->getTargetType())
            {
                case Target::USER:
                    $this->notificationService->createNotificationForUser(
                        $target->getTarget(),
                        $action->getNotifier(),
                        $this->getSubject($action),
                        $this->getMessage($action),
                        $action->getChannel()->toNotificationMapping()
                    );
                    break;

                case Target::STAFF:
                    $targets = $this->targetRepository->getTargets($target, $action->getPayload());
                    foreach($targets as $user) {
                        $this->notificationService->createNotificationForUser(
                            $user->id,
                            $action->getNotifier(),
                            $this->getSubject($action),
                            $this->getMessage($action),
                            $action->getChannel()->toNotificationMapping()
                        );
                    }
                    break;

                // TODO: Implement Company
                case Target::COMPANY:
            }
        }
    }

    /**
     * @param AddNotificationAction $notificationAction
     *
     * @return void
     */
    protected function handleActionsChannel(AddNotificationAction $notificationAction): void
    {
        /** @var HasTargetContract|null $target */
        $target = $notificationAction->getTargets()->first(fn(HasTargetContract $target) => $target->getTargetType() === Target::COMPANY);

        $action = new \App\Models\Action();

        $companyReference = $target?->getTarget() ?? '';

        $companyId = !empty($companyReference) ? Company::query()->where(Company::FIELD_REFERENCE, $companyReference)->first()?->{Company::FIELD_ID} : 0;

        $action->from_user_id = $notificationAction->getNotifier();
        $action->for_id = $companyId;
        $action->for_relation_type = \App\Models\Action::RELATION_TYPE_COMPANY;
        $action->subject = $notificationAction->getSubject();
        $action->message = $this->getMessage($notificationAction);

        $action->save();

        ActionCreatedEvent::dispatch($notificationAction);
    }

    protected function handleEmailNotification(EmailNotificationAction $emailNotification)
    {
        foreach ($emailNotification->getTargets() as $target) {
            Email::send($target->getToAddress(), new EmailNotification($emailNotification, $target));
        }
    }

    /**
     * Ensures that the notification goes through the shortcode service.
     *
     * @param AddNotificationAction $action
     * @return string
     */
    protected function getMessage(AddNotificationAction $action): string
    {
        return $this->shortcodeService->handle($action->getMessage(), $action->getPayload());
    }

    /**
     * Ensures that the notification goes through the shortcode service.
     *
     * @param AddNotificationAction $action
     * @return string
     */
    protected function getSubject(AddNotificationAction $action): string
    {
        return $this->shortcodeService->handle($action->getSubject(), $action->getPayload());
    }
}
