<?php

namespace App\Listeners;

use App\Enums\Billing\BillingLogLevel;
use App\Enums\LeadRefundItemChargeRefundStatus;
use App\Enums\LeadRefundStatus;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceChargeRefundUpdated;
use App\Models\Billing\InvoiceRefund;
use App\Models\Billing\InvoiceRefundCharge;
use App\Models\LeadRefund;
use App\Models\LeadRefundItemRefund;
use App\Services\Billing\BillingLogService;
use App\Services\BillingLegacyAdminEventService;

class InvoiceChargeRefundUpdatedListener
{
    public function __construct(protected BillingLegacyAdminEventService $billingLegacyAdminEventService)
    {

    }

    /**
     * Handle the event.
     */
    public function handle(InvoiceChargeRefundUpdated $event): void
    {
        if (!$event->invoiceRefundChargeUuid) {
            BillingLogService::log(
                message  : 'Skipping lead refund status update...',
                level    : BillingLogLevel::WARNING,
                namespace: 'invoice_charge_refund_updated_listener',
                context  : [
                    'event' => $event
                ],
            );
            return;
        }

        $invoiceRefund = InvoiceRefund::query()
            ->whereHas(InvoiceRefund::RELATION_REFUND_CHARGES, function ($query) use ($event) {
                $query->where(InvoiceRefundCharge::FIELD_UUID, $event->invoiceRefundChargeUuid);
            })->first();

        $leadRefundItemRefund = LeadRefundItemRefund::query()
            ->where(LeadRefundItemRefund::FIELD_INTERNAL_REFUND_UUID, $invoiceRefund->{InvoiceRefund::FIELD_UUID})
            ->first();

        if ($leadRefundItemRefund) {
            LeadRefundItemRefund::query()
                ->where(LeadRefundItemRefund::FIELD_INTERNAL_REFUND_UUID, $invoiceRefund->{InvoiceRefund::FIELD_UUID})
                ->update([
                    LeadRefundItemRefund::FIELD_STATUS => LeadRefundItemChargeRefundStatus::REFUNDED->value
                ]);

            LeadRefund::query()
                ->where(LeadRefund::FIELD_ID, $leadRefundItemRefund->{LeadRefundItemRefund::FIELD_LEAD_REFUND_ID})
                ->update([
                    LeadRefund::FIELD_STATUS => LeadRefundStatus::REFUNDED
                ]);
        }
    }
}




