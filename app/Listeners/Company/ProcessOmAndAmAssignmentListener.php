<?php

namespace App\Listeners\Company;

use App\Enums\RoleType;
use App\Events\CompanyCampaign\PostAllocationEvent;
use App\Models\Odin\ProductAssignment;
use App\Services\Companies\CompanyManagerAssignmentService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Throwable;

class ProcessOmAndAmAssignmentListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The name of the queue the job should be sent to.
     *
     * @var string
     */
    public string $queue = QueueHelperService::QUEUE_NAME_LONG_RUNNING;

    /**
     * Create the event listener.
     */
    public function __construct(protected CompanyManagerAssignmentService $service) {}

    /**
     * Handle the event.
     * @throws Throwable
     */
    public function handle(PostAllocationEvent $event): void
    {
        $event->productAssignments->each(function (ProductAssignment $productAssignment) {
            $firstLead = $this->service->getFirstLeadForCompany($productAssignment->company);

            if ($firstLead) {
                $this->service->processOmAssignments($productAssignment->company, $firstLead->delivered_at);
                $this->service->processAmAssignment($productAssignment->company, $firstLead->delivered_at);
            }

            $productAssignment->company->currentAssignment(RoleType::ONBOARDING_MANAGER->value)?->setCommissionableAt();
            $productAssignment->company->currentAssignment(RoleType::ACCOUNT_MANAGER->value)?->setCommissionableAt();
            $productAssignment->company->currentAssignment(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value)?->setCommissionableAt();
        });
    }
}
