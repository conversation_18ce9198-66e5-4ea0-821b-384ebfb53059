<?php

namespace App\Listeners\SalesBait;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\SalesBait\PunterRecordedInterest;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\Company;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRegisteredInterest;
use App\Repositories\Legacy\CompanyRepository;
use App\Services\Delivery\Email;
use App\Services\PubSub\PubSubService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

class SendPunterRecordedInterestPubSubEventListener implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(private PubSubService $pubSubService) {}

    /**
     * Handle the event.
     *
     * @param PunterRecordedInterest $event
     * @return void
     */
    public function handle(PunterRecordedInterest $event): void
    {
        $salesBait = $event->getSalesBait();
        $interest  = $event->getSalesBaitInterest();

        $this->pubSubService->handle(
            EventCategory::COMPANIES->value,
            EventName::SALES_BAIT_REGISTERED_INTEREST->value,
            [
                'company_id' => $salesBait->{SalesBaitLead::RELATION_COMPANY}->{EloquentCompany::RELATION_MI_COMPANY}->{Company::FIELD_ID},
                'company_reference' => $salesBait->{SalesBaitLead::RELATION_COMPANY}->reference,
                'lead_id' => $salesBait->{SalesBaitLead::FIELD_LEAD_ID},
                'contact_id' => $interest->{SalesBaitRegisteredInterest::RELATION_LEGACY_CONTACT}->{EloquentCompanyContact::FIELD_CONTACT_ID},
                'relation_id' => $interest->{SalesBaitRegisteredInterest::FIELD_RELATION_ID},
                'relation_type' => $interest->{SalesBaitRegisteredInterest::FIELD_RELATION_TYPE} === SalesBaitRegisteredInterest::TYPE_CONTACT ? "contact" : "user"
            ]
        );
    }
}
