<?php

namespace App\Listeners\SalesBait;

use App\Events\LeadProcessing\LeadUndersoldEvent;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\AvailableCampaignRepository;
use App\Repositories\SalesBait\SalesBaitRepository;
use App\Services\LeadQualificationService;
use App\Services\SalesBait\SalesBaitConfigurationService;
use App\Services\SalesBaitService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;

class LeadUndersoldListener implements ShouldQueue
{

    public function __construct(
        protected LeadQualificationService $leadQualificationService,
        protected AvailableCampaignRepository $availableCampaignRepository,
        protected SalesBaitService $salesBaitService,
        protected SalesBaitConfigurationService $salesBaitConfigurationService,
        protected SalesBaitRepository $baitRepository
    ){}

    /**
     * Handles tracking a lead that was undersold,
     * typically due to a lack of companies with budget
     *
     * @param LeadUndersoldEvent $event
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(LeadUndersoldEvent $event): void
    {
        /** @var EloquentQuote $lead */
        $lead = $event->getLead();
        // MI leads will not work with SalesBait yet
        if (!$lead->solar_lead && !$lead->roofing_lead) return;
            if(
            $this->salesBaitConfigurationService->isSalesBaitEnabledForZipCode($lead->address->zipcode, $lead->getLeadIndustry()->display_name) &&
            $this->leadQualificationService->qualifyUndersoldLeadForSalesBait($lead, $event->processingScenario)
        ){
            $campaigns = $this->availableCampaignRepository->getCampaignsByLead($lead);
            $this->salesBaitService->createSalesBaits($lead, $campaigns, $this->baitRepository->getNonPurchasingCompaniesForLead($lead));
        }
    }
}
