<?php

namespace App\Listeners\SalesBait;

use App\Events\LeadProcessing\LeadUnsoldEvent;
use App\Repositories\AvailableCampaignRepository;
use App\Repositories\SalesBait\SalesBaitRepository;
use App\Services\LeadQualificationService;
use App\Services\SalesBait\SalesBaitConfigurationService;
use App\Services\SalesBaitService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;

class LeadUnsoldListener implements ShouldQueue
{

    public function __construct(
        protected LeadQualificationService $leadQualificationService,
        protected AvailableCampaignRepository $availableCampaignRepository,
        protected SalesBaitService $salesBaitService,
        protected SalesBaitConfigurationService $salesBaitConfigurationService,
        protected SalesBaitRepository $baitRepository
    ){}

    /**
     * Handles tracking an approved lead that was not able to be allocated,
     * typically due to a lack of companies with budget
     *
     * @param LeadUnsoldEvent $event
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(LeadUnsoldEvent $event): void
    {
        $lead = $event->getLead();
        if(
            $this->salesBaitConfigurationService->isSalesBaitEnabledForZipCode($lead->address->zipcode, $lead->getLeadIndustry()->display_name) &&
            $this->leadQualificationService->qualifyUnsoldLeadForSalesBait($lead)
        ){
            $campaigns = $this->availableCampaignRepository->getCampaignsByLead($lead);
            $this->salesBaitService->createSalesBaits($lead, $campaigns, $this->baitRepository->getNonPurchasingCompaniesForLead($lead));
        }
    }
}
