<?php

namespace App\Listeners\SalesBait;

use App\Events\SalesBait\SalesBaitCreatedEvent;
use App\Repositories\SalesBait\AvailablePuntersRepository;
use App\Repositories\SalesBait\SalesBaitRepository;
use App\Services\SalesBaitService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;

class DeliverSalesBaitSMSListener implements ShouldQueue
{

    public function __construct(
        protected SalesBaitRepository        $salesBaitRepository,
        protected AvailablePuntersRepository $availablePuntersRepository,
        protected SalesBaitService           $salesBaitService
    ){}

    /**
     * @param SalesBaitCreatedEvent $event
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(SalesBaitCreatedEvent $event): void
    {
        $salesBait = $this->salesBaitRepository->find($event->salesBaitId);
        $punters = $this->availablePuntersRepository->getPuntersThatCanReceiveSMS($salesBait->campaign);
        $this->salesBaitService->deliverSMSToPunters($salesBait, $punters);
    }

}
