<?php

namespace App\Listeners;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\AccountManagerAssignedEvent;
use App\Jobs\DispatchPubSubEvent;

class AccountManagerAssignedListener
{
    /**
     * Handle the event.
     *
     * @param AccountManagerAssignedEvent $event
     * @return void
     */
    public function handle(AccountManagerAssignedEvent $event): void
    {
        DispatchPubSubEvent::dispatch(EventCategory::INTERNAL, EventName::ACCOUNT_MANAGER_ASSIGNED, $event->data);
    }
}
