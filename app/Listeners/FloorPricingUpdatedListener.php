<?php

namespace App\Listeners;

use App\Events\FloorPricingUpdatedEvent;
use App\Notifications\Slack\FloorPricingUpdatedSlackNotification;
use App\Services\Slack\SlackNotificationServiceContract;

class FloorPricingUpdatedListener
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected SlackNotificationServiceContract $slackNotificationService
    )
    {}

    /**
     * Handle the event.
     */
    public function handle(FloorPricingUpdatedEvent $event): void
    {
        $slackNotification = FloorPricingUpdatedSlackNotification::createFromEvent($event);

        if (!empty($slackNotification)) {
            $this->slackNotificationService->sendSlackNotification($slackNotification);
        }
    }
}




