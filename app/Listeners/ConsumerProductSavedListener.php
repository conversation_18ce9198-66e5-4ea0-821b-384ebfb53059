<?php

namespace App\Listeners;

use App\Events\ConsumerProductSaved;
use App\Services\Affiliate\PayoutStrategyService;
use App\Services\Affiliate\PayoutService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;

class ConsumerProductSavedListener implements ShouldQueue
{
    public string $queue = QueueHelperService::QUEUE_NAME_AFFILIATES;
    /**
     * Create the event listener.
     */
    public function __construct(
        protected PayoutService         $payoutService,
        protected PayoutStrategyService $payoutStrategyService,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ConsumerProductSaved $event): void
    {
        $consumerProduct = $event->consumerProduct;

        if (filled($consumerProduct->consumerProductAffiliateRecord?->affiliate_id) && empty($consumerProduct->affiliatePayout)) {
            $strategy = $this->payoutStrategyService
                ->getActiveStrategyByAffiliateId(
                    affiliateId: $consumerProduct->consumerProductAffiliateRecord->affiliate_id
                );

            //should we log if no strategy??
            if($strategy) {
                $this->payoutService->calculate(
                    strategy: $strategy,
                    consumerProduct: $consumerProduct,
                );
            }
        }
    }
}
