<?php

namespace App\Listeners;

use App\BillingWorkflows\Workflows\BillingEventHandlerWorkflow;
use App\Enums\Billing\BillingPolicyEventType;
use App\Events\Billing\BillingWorkflowEventContract;
use App\Services\Billing\BillingLogService;
use Exception;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Events\Dispatcher;
use Workflow\WorkflowStub;

class BillingWorkflowListener implements ShouldQueue
{
    public string $queue = 'billing';

    /**
     * @param BillingWorkflowEventContract $event
     * @return void
     */
    public function handleEvent(BillingWorkflowEventContract $event): void
    {
        $data = get_object_vars($event);

        try {
            $workflow = WorkflowStub::make(BillingEventHandlerWorkflow::class);

            BillingLogService::log(
                message  : 'Starting billing workflows for ' . $event::class,
                namespace: 'billing_workflow',
                context  : [
                    'event_properties' => $data,
                    'event_class'      => $event::class
                ]
            );

            $workflow->start($event::class, $data);
        } catch (Exception $exception) {
            BillingLogService::logException(
                exception: $exception,
                namespace: self::class,
                context  : [
                    'event_properties' => $data,
                    'event_class'      => $event::class
                ]
            );
        }
    }

    /**
     * @param Dispatcher $events
     * @return void
     */
    public function subscribe(Dispatcher $events): void
    {
        $events->listen(
            BillingPolicyEventType::allEventClasses()->toArray(),
            [self::class, 'handleEvent']
        );
    }
}
