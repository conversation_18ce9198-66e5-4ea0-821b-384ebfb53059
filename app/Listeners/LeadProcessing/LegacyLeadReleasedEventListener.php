<?php

namespace App\Listeners\LeadProcessing;

use App\Events\LeadProcessing\LeadReleasedEvent;
use App\Services\Legacy\APIConsumer;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Contracts\Container\BindingResolutionException;
use Exception;

class LegacyLeadReleasedEventListener implements ShouldQueue
{
    const API_BASE_ENDPOINT         = '/repositories/lead-processing';
    const API_RELEASE_LEAD_ENDPOINT = self::API_BASE_ENDPOINT . '/release-lead';

    /**
     * Handles releasing the lead in the legacy admin.
     *
     * @param LeadReleasedEvent $event
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function handle(LeadReleasedEvent $event): void
    {
        /** @var APIConsumer $consumer */
        $consumer        = app()->make(APIConsumer::class);
        $leadReference   = $event->leadReference;
        $leadProcessorId = $event->processorId;

        $consumer->patch(self::API_RELEASE_LEAD_ENDPOINT,
            compact("leadReference", "leadProcessorId")
        );
    }
}
