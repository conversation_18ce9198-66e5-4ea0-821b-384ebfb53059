<?php

namespace App\Listeners\LeadProcessing;

use App\Events\LeadProcessing\LeadCancelledEvent;
use App\Jobs\CalculateRevenueScenariosJob;
use App\Models\LeadRevenueScenario;
use Illuminate\Contracts\Queue\ShouldQueue;

class LeadCancelledRevenueScenarioListener implements ShouldQueue
{
    /**
     * <PERSON>les setting the best revenue scenario for a given lead.
     *
     * @param LeadCancelledEvent $event
     */
    public function handle(LeadCancelledEvent $event)
    {
        $lead = $event->getLead();

        if($lead)
            dispatch((new CalculateRevenueScenariosJob($lead->quoteid, LeadRevenueScenario::TYPE_CANCELLED)));
    }
}
