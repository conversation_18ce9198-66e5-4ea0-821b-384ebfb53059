<?php

namespace App\Listeners;

use App\Events\LeadProcessing\LeadUndersoldEvent;
use App\Events\LeadProcessing\LeadUnsoldEvent;
use App\Services\LeasingCompanyService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;

class LeasingCompanyListener implements ShouldQueue
{
    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public int $delay = 60;

    /**
     * Create the event listener.
     */
    public function __construct(protected LeasingCompanyService $leasingCompanyService) {}

    /**
     * Handle the event.
     * @throws BindingResolutionException
     */
    public function handle(LeadUnsoldEvent|LeadUndersoldEvent $event): void
    {
        if (!$event->getLead()->solar_lead) return;

        $this->leasingCompanyService->allocateLegacyLead($event->leadReference);
    }
}
