<?php

namespace App\Listeners;

use App\Enums\RoleType;
use App\Events\Calendar\DemoCreated;
use App\Models\Calendar\CalendarEventAttendee;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class DemoCreatedListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function handle(DemoCreated $event): void
    {
        $emails = User::query()
            ->select([User::FIELD_EMAIL, User::FIELD_EMAIL_ALIASES])
            ->get();

        $emails = $emails->flatMap(function ($user) {
            return [
                $user->email,
                ...($user->email_aliases ?? [])
            ];
        })->unique();

        $demo = $event->demo;

        $attendeeEmails = $demo->attendees->pluck(CalendarEventAttendee::FIELD_EMAIL);

        $otherEmails = $attendeeEmails->filter(fn ($email) => !$emails->contains($email));

        $companies = Company::query()
            ->select(Company::TABLE .'.*')
            ->leftJoin(
                CompanyUser::TABLE,
                CompanyUser::TABLE .'.'. CompanyUser::FIELD_COMPANY_ID,
                '=',
                Company::TABLE .'.'. Company::FIELD_ID
            )->leftJoin(
                NewBuyerProspect::TABLE,
                NewBuyerProspect::TABLE .'.'. NewBuyerProspect::FIELD_COMPANY_ID,
                '=',
                Company::TABLE .'.'. Company::FIELD_ID,
            )
            ->leftJoin(
                Contact::TABLE,
                Contact::TABLE .'.'. Contact::FIELD_PROSPECT_ID,
                '=',
                NewBuyerProspect::TABLE .'.'. NewBuyerProspect::FIELD_ID,
            )
            ->where(function ($query) use ($otherEmails) {
                $query->whereIn(NewBuyerProspect::TABLE .'.'. NewBuyerProspect::FIELD_DECISION_MAKER_EMAIL, $otherEmails->toArray())
                    ->orWhereIn(Contact::TABLE .'.'. Contact::FIELD_EMAIL, $otherEmails->toArray())
                    ->orWhereIn(CompanyUser::TABLE .'.'. CompanyUser::FIELD_EMAIL, $otherEmails->toArray());
            })
            ->distinct()
            ->get();

        $user = $demo->user;

        /** @var Company $company */
        foreach ($companies as $company) {
            if (!$company->hasAssignment(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value) && !$company->hasAssignment(RoleType::ACCOUNT_MANAGER->value)) {
                $company->assign(user: $user)->asBusinessDevelopmentManager(
                    notify: true,
                    reason: "$user->name booked demo for company"
                );
            }
        }
    }
}
