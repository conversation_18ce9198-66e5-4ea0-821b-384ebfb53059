<?php

namespace App\Listeners;

use App\Events\ProductAssignment\ProductAssignmentEvent;
use App\Models\Odin\ProductAssignment;
use App\Services\ProductRejectionCalculationService;
use Illuminate\Contracts\Container\BindingResolutionException;

class ManualRejectionPercentageChangeListener
{
    /**
     * @param ProductRejectionCalculationService $rejectionCalculationService
     */
    public function __construct(
        protected ProductRejectionCalculationService $rejectionCalculationService)
    {
    }

    /**
     * Handles updating the rejection percentage for a company
     *
     * @param ProductAssignmentEvent $event
     * @throws BindingResolutionException
     */
    public function handle(ProductAssignmentEvent $event): void
    {
        /** @var ProductAssignment $productAssignment */
        $productAssignment = $event->getProductAssignment();
        $productId         = $productAssignment->consumerProduct->serviceProduct->product_id;

        if($productId) {
            $this->rejectionCalculationService->calculateAndStoreCompanyProductRejectionPercentages($productAssignment->company, $productId);
        }
    }
}
