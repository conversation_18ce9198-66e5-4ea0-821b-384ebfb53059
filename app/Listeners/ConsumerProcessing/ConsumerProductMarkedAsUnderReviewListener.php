<?php

namespace App\Listeners\ConsumerProcessing;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsUnderReviewEvent;
use App\Jobs\SellUnderReviewQueueLeadsJob;
use Illuminate\Queue\InteractsWithQueue;

class ConsumerProductMarkedAsUnderReviewListener extends ConsumerProductQueueChangeBaseListener
{
    use InteractsWithQueue;

    /**
     * @param ConsumerProductMarkedAsUnderReviewEvent $event
     * @return void
     */
    public function handle(ConsumerProductMarkedAsUnderReviewEvent $event): void
    {
        $this->dispatchConsumerProcessingActivity(ConsumerProcessingActivityType::UNDER_REVIEW, $event);

        SellUnderReviewQueueLeadsJob::dispatch(
            $event->consumerProduct,
            $event->processor,
            $event->reason,
            $event->comment,
            $event->publicComment,
        );
    }
}
