<?php

namespace App\Listeners\ConsumerProcessing;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Events\ConsumerProcessing\ConsumerProductMarkedAsPendingReviewEvent;
use Illuminate\Queue\InteractsWithQueue;

class ConsumerProductMarkedAsPendingReviewListener extends ConsumerProductQueueChangeBaseListener
{
    use InteractsWithQueue;

    /**
     * @param ConsumerProductMarkedAsPendingReviewEvent $event
     * @return void
     */
    public function handle(ConsumerProductMarkedAsPendingReviewEvent $event): void
    {
        $this->dispatchConsumerProcessingActivity(ConsumerProcessingActivityType::PENDING_REVIEW, $event);
    }
}