<?php

namespace App\Listeners\ConsumerProcessing;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Events\ConsumerProcessing\ConsumerProductCancelledEvent;
use Illuminate\Queue\InteractsWithQueue;

class ConsumerProductCancelledListener extends ConsumerProductQueueChangeBaseListener
{
    use InteractsWithQueue;

    /**
     * @param ConsumerProductCancelledEvent $event
     * @return void
     */
    public function handle(ConsumerProductCancelledEvent $event): void
    {
        $this->dispatchConsumerProcessingActivity(ConsumerProcessingActivityType::CANCELLED, $event);
    }
}