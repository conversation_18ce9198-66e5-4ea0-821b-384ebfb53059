<?php

namespace App\Listeners\ConsumerProcessing;

use App\Enums\ConsumerProcessing\ConsumerProcessingActivityType;
use App\Enums\ConsumerProcessing\ConsumerProcessingActivityVisibilityScope;
use App\Events\ConsumerProcessing\ConsumerProcessingActivityEvent;
use App\Events\ConsumerProcessing\ConsumerProductQueueChangeBaseEvent;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

abstract class ConsumerProductQueueChangeBaseListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected function dispatchConsumerProcessingActivity(ConsumerProcessingActivityType $eventType, ConsumerProductQueueChangeBaseEvent $event): void
    {
        $user = $event->processor->user ?? User::systemUser();
        $scope = $event->publicComment
            ? ConsumerProcessingActivityVisibilityScope::SEND_TO_COMPANY
            : ConsumerProcessingActivityVisibilityScope::INTERNAL;

        event(new ConsumerProcessingActivityEvent(
            type: $eventType,
            consumerProduct: $event->consumerProduct,
            reason: $event->reason,
            comment: $event->comment,
            user: $user,
            scope: $scope,
        ));
    }
}