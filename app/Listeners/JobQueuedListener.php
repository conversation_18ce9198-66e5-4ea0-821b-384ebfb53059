<?php

namespace App\Listeners;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\Repositories\Odin\JobTrackingRepository;
use Illuminate\Queue\Events\JobQueued;

class JobQueuedListener
{
    /**
     * Create the event listener.
     */
    public function __construct(protected JobTrackingRepository $jobTrackingRepository){}

    /**
     * Handle the event.
     */
    public function handle(JobQueued $event): void
    {
        if ($event->job instanceof AttemptConsumerProjectAllocationJob) {
            $this->jobTrackingRepository->createConsumerProjectAllocationJobTracking($event->job, $event->payload);
        }
    }
}
