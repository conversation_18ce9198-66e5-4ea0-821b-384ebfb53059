<?php

namespace App\Listeners\BundleManagement;

use App\Events\BundleManagement\BundleInvoiceStatusTransitionEvent;
use App\Repositories\BundleManagement\BundleInvoiceRepository;

class BundleInvoiceStatusTransitionListener
{
    public function __construct(protected BundleInvoiceRepository $repository)
    {
    }


    /**
     * Handle the event.
     *
     * @param BundleInvoiceStatusTransitionEvent $event
     * @return void
     * @throws \Exception
     */
    public function handle(BundleInvoiceStatusTransitionEvent $event): void
    {
        $this->repository->transitionInvoiceStatus($event->bundleInvoice, $event->transition, $event->user);
    }
}
