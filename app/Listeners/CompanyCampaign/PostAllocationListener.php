<?php

namespace App\Listeners\CompanyCampaign;

use App\Events\CompanyCampaign\PostAllocationEvent;
use App\Models\Odin\ProductAssignment;
use App\Services\Alert\CampaignAlertService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class PostAllocationListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(protected CampaignAlertService $campaignAlertService) {}

    /**
     * Handle the event.
     */
    public function handle(PostAllocationEvent $event): void
    {
        $event->productAssignments->each(
            function (ProductAssignment $assignment) {
                if ($assignment->company->configuration?->campaign_alert_enabled) {
                    $this->campaignAlertService->notifyCampaignUsageThreshold($assignment->budget->budgetContainer->campaign);
                }
            }
        );
    }
}
