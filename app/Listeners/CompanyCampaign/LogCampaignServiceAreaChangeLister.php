<?php

namespace App\Listeners\CompanyCampaign;

use App\Events\CompanyCampaign\CompanyCampaignServiceAreaUpdatedEvent;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;

class LogCampaignServiceAreaChangeLister
{
    /**
     * Create the event listener.
     */
    public function __construct(protected CompanyCampaignRepository $companyCampaignRepository) {}

    /**
     * Handle the event.
     */
    public function handle(CompanyCampaignServiceAreaUpdatedEvent $event): void
    {
        $campaign = $this->companyCampaignRepository->find($event->companyCampaignId);

        if ($campaign) {
            activity('campaign_service_area')
                ->performedOn($campaign)
                ->event('updated')
                ->withProperties([
                    'old_locations' => $event->oldLocations,
                    'new_locations' => $campaign->locationModule->locations->pluck(CompanyCampaignLocationModuleLocation::FIELD_LOCATION_ID)->toArray()
                ])
                ->log('campaign-service-area-updated');
        }
    }
}
