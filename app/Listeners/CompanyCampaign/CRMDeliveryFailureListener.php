<?php

namespace App\Listeners\CompanyCampaign;

use App\Events\CompanyCampaign\CRMDeliveryFailureEvent;
use App\Models\LeadProcessingFailedLead;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Services\Odin\ProductProcessing\ProductProcessingService;
use App\Services\ProductRejectionCalculationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CRMDeliveryFailureListener implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        protected ProductRejectionCalculationService $rejectionCalculationService
    ) {}

    /**
     * @param CRMDeliveryFailureEvent $event
     * @return void
     */
    public function handle(CRMDeliveryFailureEvent $event): void
    {
        if (!$event->hadSuccessfulDeliveries)
            $this->createFailedLeadEntry($event);

        $this->updateCRMRejections($event);
    }

    /**
     * @param CRMDeliveryFailureEvent $event
     * @return void
     */
    private function updateCRMRejections(CRMDeliveryFailureEvent $event): void
    {
        $companies = Company::query()->findMany($event->failedCompanyIds);
        $productId = ConsumerProduct::query()->find($event->consumerProductId)->serviceProduct->product_id;

        $companies->each(fn(Company $company) =>
            $this->rejectionCalculationService->calculateAndStoreCompanyProductRejectionPercentages($company, $productId)
        );
    }

    /**
     * TODO: not convinced this table is adding any value with new allocation/delivery
     * @param CRMDeliveryFailureEvent $event
     * @return void
     */
    private function createFailedLeadEntry(CRMDeliveryFailureEvent $event): void
    {
        LeadProcessingFailedLead::query()->updateOrCreate(
            [
                LeadProcessingFailedLead::FIELD_CONSUMER_PRODUCT_ID => $event->consumerProductId
            ],
            [
                LeadProcessingFailedLead::FIELD_LEAD_PROCESSOR_ID => $event->processorId,
                LeadProcessingFailedLead::FIELD_REASON            => "All deliveries failed.",
                LeadProcessingFailedLead::FIELD_LEAD_ID           => ProductProcessingService::getLegacyId($event->consumerProductId),
            ]
        );
    }
}