<?php

namespace App\Listeners;

use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\MissedProducts\MissedProductReasonEventType;
use App\Events\CompanyChangedPurchasingStatusEvent;
use App\Models\Odin\Company;
use App\Repositories\MissedProducts\MissedProductReasonEventRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class CompanyChangedPurchasingStatusListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(protected MissedProductReasonEventRepository $missedProductReasonEventRepository)
    {}

    /**
     * Handle the event.
     */
    public function handle(CompanyChangedPurchasingStatusEvent $event): void
    {
        $company = Company::query()
            ->findOrFail($event->companyId);

        if ($this->canPurchase($company))
            $this->missedProductReasonEventRepository->handleNewCompanyEvent($company, MissedProductReasonEventType::ENTERED_PURCHASING_STATUS);
        else
            $this->missedProductReasonEventRepository->handleNewCompanyEvent($company, MissedProductReasonEventType::EXITED_PURCHASING_STATUS);
    }

    /**
     * @param Company $company
     *
     * @return bool
     */
    protected function canPurchase(Company $company): bool
    {
        return in_array($company->campaign_status, [
            CompanyCampaignStatus::CAMPAIGNS_ACTIVE,
            CompanyCampaignStatus::CAMPAIGNS_PAUSED,
            CompanyCampaignStatus::CAMPAIGNS_OFF
        ]) && $company->system_status === CompanySystemStatus::ELIGIBLE;
    }
}
