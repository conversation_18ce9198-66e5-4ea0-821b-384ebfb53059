<?php

namespace App\Listeners\Workflows;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\Workflows\TaskReassignedEvent;
use App\Services\PubSub\PubSubService;

class TaskReassignedListener
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected PubSubService $pubSubService){}

    /**
     * Handle the event.
     *
     * @param  TaskReassignedEvent  $event
     * @return void
     */
    public function handle(TaskReassignedEvent $event): void
    {
        $this->pubSubService->handle(
            EventCategory::INTERNAL->value,
            EventName::TASK_REASSIGNED->value,
            [
                'task_id' => $event->task->id,
                'task_reassigned_from_user_id' => $event->previousTaskAssignee,
                'task_assigned_user_id'    => $event->task->assigned_user_id
            ]
        );
    }
}
