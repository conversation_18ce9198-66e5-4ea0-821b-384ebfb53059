<?php

namespace App\Listeners\CompanyRegistration\V3;

use App\Events\CompanyRegistration\V3\ContractAccepted;
use App\Events\CompanyRegistration\V3\RegistrationCompleted;
use App\Events\CompanyRegistration\V3\RegistrationStarted;
use App\Models\Odin\Company;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\SlackDirectMessageService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Http\Client\ConnectionException;

class CompanyRegistrationSlackNotificationListener implements ShouldQueue
{
    use Queueable;

    protected ?string $channelId = null;
    protected ?string $slackAuthToken = null;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        $this->onQueue('long_running');
        $this->channelId = config('services.slack.registration_notification_channel_id');
        $this->slackAuthToken = config('services.slack.company_registration_bot_auth_token');
    }

    /**
     * Handle the event.
     *
     * @param RegistrationStarted|RegistrationCompleted|ContractAccepted $event
     *
     * @throws ConnectionException
     */
    public function handle(RegistrationStarted|RegistrationCompleted|ContractAccepted $event): void
    {
        if (!$this->channelId) {
            logger()->error('Slack notification failed: Missing registration notification channel ID');
            return;
        }

        switch ($event::class) {
            case RegistrationStarted::class;
                $this->handleRegistrationStartedEvent($event);
                break;
            case RegistrationCompleted::class:
                $this->handleRegistrationCompletedEvent($event);
                break;
            case ContractAccepted::class:
                $this->handleContractAcceptedEvent($event);
                break;
        }
    }

    /**
     * @param RegistrationStarted $event
     *
     * @return void
     * @throws ConnectionException
     */
    protected function handleRegistrationStartedEvent(RegistrationStarted $event): void
    {
        $this->notifySlackChannel(
            "{$this->getNameFromBuyerProspect($event->newBuyerProspect)} from {$event->newBuyerProspect->company_name} has begun registration.
            Phone: {$event->newBuyerProspect->company_phone}
            Email: {$event->newBuyerProspect->getSourceDataByKey(NewBuyerProspect::SOURCE_KEY_EMAIL)}"
        );
    }

    /**
     * @param RegistrationCompleted $event
     *
     * @return void
     * @throws ConnectionException
     */
    protected function handleRegistrationCompletedEvent(RegistrationCompleted $event): void
    {
        $this->notifySlackChannel("{$event->company->users()->first()?->completeName()} from {$event->company->name} has completed registration. {$this->getCompanyLink($event->company)}");
    }

    /**
     * @param ContractAccepted $event
     *
     * @return void
     * @throws ConnectionException
     */
    protected function handleContractAcceptedEvent(ContractAccepted $event): void
    {
        $this->notifySlackChannel("{$event->companyUser->completeName()} from {$event->company->name} has accepted contract. {$this->getCompanyLink($event->company)}");
    }

    /**
     * @param string $message
     *
     * @return void
     * @throws ConnectionException
     */
    protected function notifySlackChannel(string $message): void
    {
        SlackDirectMessageService::sendMessageToChannel(
            channelId: $this->channelId,
            message: $message,
            slackAuthToken: $this->slackAuthToken
        );
    }

    /**
     * @param NewBuyerProspect $newBuyerProspect
     *
     * @return string
     */
    protected function getNameFromBuyerProspect(NewBuyerProspect $newBuyerProspect): string
    {
        return rtrim("{$newBuyerProspect->getSourceDataByKey(NewBuyerProspect::SOURCE_KEY_FIRST_NAME)} {$newBuyerProspect->getSourceDataByKey(NewBuyerProspect::SOURCE_KEY_LAST_NAME)}");
    }

    /**
     * @param Company $company
     *
     * @return string
     */
    protected function getCompanyLink(Company $company): string
    {
        return config('app.url') . "/companies/$company->id";
    }
}
