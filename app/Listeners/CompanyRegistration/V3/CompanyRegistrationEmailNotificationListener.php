<?php

namespace App\Listeners\CompanyRegistration\V3;

use App\Events\CompanyRegistration\V3\ContractAccepted;
use App\Events\CompanyRegistration\V3\RegistrationCompleted;
use App\Events\CompanyRegistration\V3\RegistrationStarted;
use App\Mail\CompanyRegistration\V3\RegistrationNotificationEmail;
use App\Services\Companies\CompanyLinkService;
use App\Services\Roles\CompanyRoleNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class CompanyRegistrationEmailNotificationListener implements ShouldQueue
{
    use Queueable;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        $this->onQueue('long_running');
    }

    /**
     * Handle the event.
     */
    public function handle(RegistrationStarted|RegistrationCompleted|ContractAccepted $event): void
    {
        switch ($event::class) {
            case RegistrationStarted::class;
                $this->handleRegistrationStartedEvent($event);
                break;
            case RegistrationCompleted::class:
                $this->handleRegistrationCompletedEvent($event);
                break;
            case ContractAccepted::class:
                $this->handleContractAcceptedEvent($event);
                break;
        }
    }

    /**
     * @param RegistrationStarted $event
     *
     * @return void
     */
    protected function handleRegistrationStartedEvent(RegistrationStarted $event): void
    {
        Mail::to($this->getToEmail())
            ->sendNow(new RegistrationNotificationEmail(
                markdownTemplate: 'emails.company-registration.v3.company-started-registration',
                emailSubject: 'Company started registration',
                emailData: [
                    'newBuyerProspect' => $event->newBuyerProspect
                ]
            ));
    }

    /**
     * @param RegistrationCompleted $event
     *
     * @return void
     */
    protected function handleRegistrationCompletedEvent(RegistrationCompleted $event): void
    {
        $url = route('companies.index', $event->company->id);

        Mail::to($this->getToEmail())
            ->sendNow(new RegistrationNotificationEmail(
                markdownTemplate: 'emails.company-registration.v3.company-completed-registration',
                emailSubject: 'Company completed registration',
                emailData: [
                    'company' => $event->company,
                    'url' => $url,
                ]
            ));
    }

    /**
     * @param ContractAccepted $event
     *
     * @return void
     */
    protected function handleContractAcceptedEvent(ContractAccepted $event): void
    {
        $url = route('companies.index', $event->company->id);

        Mail::to($this->getToEmail())
            ->sendNow(new RegistrationNotificationEmail(
                markdownTemplate: 'emails.company-registration.v3.company-accepted-contract',
                emailSubject: 'Company accepted contract',
                emailData: [
                    'company' => $event->company,
                    'companyUser' => $event->companyUser,
                    'url' => $url,
                ]
            ));

        app(CompanyRoleNotificationService::class)->notifyAllCompanyManagers(
            company: $event->company,
            action: 'contract_accepted',
            reason: 'Contract Accepted',
            otherData: ['company_user' => $event->companyUser],
        );
    }

    /**
     * @return string
     */
    protected function getToEmail(): string
    {
        return '<EMAIL>';
    }
}
