<?php

namespace App\Listeners\CompanyRegistration\V3;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;
use Illuminate\Queue\InteractsWithQueue;
use App\Events\CompanyRegistration\V3\RegistrationCompleted;
use App\Mail\CompanyRegistration\V3\CompanyAssignedBusinessDevelopmentManagerNotification;

class BusinessDevelopmentManagerAssignmentListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public int $delay = 7200; // 2 hours

    public function handle(RegistrationCompleted $event): void
    {
        if ($event->company->refresh()->businessDevelopmentManager) {
            return;
        }

        $user = $event->company->assignViaRoundRobin('business-development-manager');

        if (filled($user)) {
            Mail::to($user->email)->send(new CompanyAssignedBusinessDevelopmentManagerNotification($user, $event->company));
        }
    }
}
