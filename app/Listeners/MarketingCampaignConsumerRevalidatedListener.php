<?php

namespace App\Listeners;

use App\Events\MarketingCampaignConsumerRevalidated;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\MarketingCampaign\MarketingCampaignService;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class MarketingCampaignConsumerRevalidatedListener
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected MarketingCampaignConsumerRepository $marketingCampaignConsumerRepository,
        protected MarketingCampaignService $marketingCampaignService,
        protected MarketingCampaignRepository $marketingCampaignRepository,
    )
    {}

    /**
     * Handle the event.
     */
    public function handle(MarketingCampaignConsumerRevalidated $event): void
    {
        $marketingConsumerId = $event->marketingConsumerId;

        try {
            $marketingCampaignConsumer = $this->marketingCampaignConsumerRepository->findOrFail($marketingConsumerId, [MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN]);
        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return;
        }

        $marketingCampaign = $marketingCampaignConsumer->{MarketingCampaignConsumer::RELATION_MARKETING_CAMPAIGN};

        $now = now();

        $this->marketingCampaignConsumerRepository->updateMarketingCampaignConsumer(
            campaignConsumer: $marketingCampaignConsumer,
            revalidatedAt   : $now,
        );

        $metrics = $this->marketingCampaignService->getMarketingCampaignMetrics($marketingCampaign);

        $metrics->incrementRevalidates();
        $metrics->setLastRevalidatedAt($now);

        $this->marketingCampaignRepository->updateMarketingCampaign(
            marketingCampaign: $marketingCampaign,
            metrics: $metrics
        );
    }
}
