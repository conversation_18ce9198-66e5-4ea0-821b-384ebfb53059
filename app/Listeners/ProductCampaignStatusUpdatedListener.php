<?php

namespace App\Listeners;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\ProductCampaignStatusUpdatedEvent;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Repositories\Odin\CompanyRepository;
use App\Services\PubSub\PubSubService;
use Carbon\Carbon;

class ProductCampaignStatusUpdatedListener
{
    public function __construct(
        private readonly CompanyRepository $companyRepository,
        private readonly PubSubService $pubSubService
    )
    {

    }

    /**
     * Handle the event.
     *
     * @param ProductCampaignStatusUpdatedEvent $event
     * @return void
     */
    public function handle(ProductCampaignStatusUpdatedEvent $event): void
    {
        $productCampaign = ProductCampaign::query()
            ->where(ProductCampaign::FIELD_ID, $event->productCampaignId)
            ->with(ProductCampaign::RELATION_COMPANY)
            ->firstOrFail();

        $company = $productCampaign->{ProductCampaign::RELATION_COMPANY};

        $oldCompanyStatus = $company->{Company::FIELD_STATUS};

        $this->companyRepository->updateConsolidatedStatus($company);

        if($productCampaign->{ProductCampaign::FIELD_STATUS}
        && $productCampaign->{ProductCampaign::FIELD_STATUS} != $event->oldStatus) {
            $now = Carbon::now('UTC');

            foreach ($productCampaign->{ProductCampaign::RELATION_BUDGETS} as $pcb) {
                $pcb->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP} = $now;

                $pcb->save();
            }
        }

        $this->pubSubService->handle(
            EventCategory::COMPANIES->value,
            EventName::STATUS_UPDATED->value,
            [
                'company_reference' => $company->{Company::FIELD_REFERENCE},
                'old_status' => $oldCompanyStatus,
                'new_status' => $company->{Company::FIELD_STATUS}
            ]
        );

        $this->pubSubService->handle(
            EventCategory::CAMPAIGNS->value,
            EventName::STATUS_UPDATED->value,
            [
                'product_campaign_id' => $productCampaign->{ProductCampaign::FIELD_ID},
                'campaign_reference' => $productCampaign->{ProductCampaign::RELATION_LEGACY_CAMPAIGN}->{LeadCampaign::UUID},
                'company_reference'  => $event->companyReference,
                'campaign_id'        => $productCampaign->{ProductCampaign::FIELD_ID},
                'initiator_id'       => $event->initiatorId,
                'old_status'         => (int) $event->oldStatus,
                'new_status'         => (int) $productCampaign->{ProductCampaign::FIELD_STATUS},
                'status_type'        => $event->statusType->toDisplayString(),
                'reactivation_date'  => $event->reactivationTimestamp,
                'status_reason'      => $event->statusReason
            ]
        );
    }
}
