<?php

namespace App\Listeners\ConsumerProductLifecycleTracking;

use App\Events\ConsumerProductLifecycleTracking\ApprovedToSell;
use App\Repositories\ConsumerProductLifecycleTrackingRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class LogApprovedToSell implements ShouldQueue
{
    public function __construct(
        protected ConsumerProductLifecycleTrackingRepository $repository
    ) {}

    public function handle(ApprovedToSell $event): void
    {
        $this->repository->addGoodToSellAt($event->consumerProductId, $event->updatedAt);
    }
}
