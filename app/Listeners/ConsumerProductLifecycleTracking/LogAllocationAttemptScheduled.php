<?php

namespace App\Listeners\ConsumerProductLifecycleTracking;

use App\Events\ConsumerProductLifecycleTracking\AllocationAttemptScheduled;
use App\Repositories\ConsumerProductLifecycleTrackingRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class LogAllocationAttemptScheduled implements ShouldQueue
{

    public function __construct( protected ConsumerProductLifecycleTrackingRepository $repository) {}

    public function handle(AllocationAttemptScheduled $event): void
    {
        $this->repository->appendAllocationAttemptSchedule($event->consumerProductId, $event->scheduleCalculatedAt, $event->delayInSeconds);
    }
}
