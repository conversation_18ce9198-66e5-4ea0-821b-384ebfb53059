<?php

namespace App\Listeners\ConsumerProductLifecycleTracking;

use App\Events\ConsumerProductLifecycleTracking\StatusUpdated;
use App\Repositories\ConsumerProductLifecycleTrackingRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class LogStatusUpdated implements ShouldQueue
{
    public function __construct(protected ConsumerProductLifecycleTrackingRepository $repository) {}

    public function handle(StatusUpdated $event): void
    {
        $this->repository->appendStatusUpdate($event->consumerProductId, $event->status, $event->updatedAt);
    }
}
