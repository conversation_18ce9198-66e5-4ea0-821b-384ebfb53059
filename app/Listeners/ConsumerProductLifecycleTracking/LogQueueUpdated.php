<?php

namespace App\Listeners\ConsumerProductLifecycleTracking;

use App\Events\ConsumerProductLifecycleTracking\QueueUpdated;
use App\Repositories\ConsumerProductLifecycleTrackingRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class LogQueueUpdated implements ShouldQueue
{
    public function __construct(protected ConsumerProductLifecycleTrackingRepository $repository) {}

    public function handle(QueueUpdated $event): void
    {
        $this->repository->appendQueueUpdate(
            $event->consumerProductId,
            $event->updatedAt,
            $event->processorName,
            $event->newQueue,
            $event->reason,
            $event->comment,
            $event->automatic
    );
    }
}
