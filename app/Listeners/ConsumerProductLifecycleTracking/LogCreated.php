<?php

namespace App\Listeners\ConsumerProductLifecycleTracking;

use App\Events\ConsumerProductLifecycleTracking\Created;
use App\Models\ConsumerProductLifecycleTracker;
use App\Repositories\ConsumerProductLifecycleTrackingRepository;
use Illuminate\Contracts\Queue\ShouldQueue;

class LogCreated implements ShouldQueue
{
    public function __construct(protected ConsumerProductLifecycleTrackingRepository $repository) {}

    public function handle(Created $event): void
    {
        $tracker = $this->repository->getTracker($event->consumerProductId);
        if (!$tracker) {
            $this->repository->createTracker([
                ConsumerProductLifecycleTracker::FIELD_CONSUMER_PRODUCT_ID         => $event->consumerProductId,
                ConsumerProductLifecycleTracker::FIELD_CONSUMER_PRODUCT_CREATED_AT => $event->createdAt
            ]);
        }
    }
}
