<?php

namespace App\Listeners\OutreachCadence;

use App\Events\Workflows\TaskRescheduledEvent;
use App\Models\Cadence\CompanyCadenceScheduledGroup;
use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;

class UpdateTaskActionExecutionTime
{
    use InteractsWithQueue, Queueable;

    /**
     * @return void
     */
    public function __construct(protected CompanyCadenceRoutineRepository $repository,)
    {
        $this->onQueue(config('queue.named_queues.outreach_cadence'));
    }

    /**
     * @param TaskRescheduledEvent $event
     * @return void
     */
    public function handle(TaskRescheduledEvent $event): void
    {
        $action = $this->repository->findPendingActionByTaskId($event->taskId);
        $action?->group->update([CompanyCadenceScheduledGroup::FIELD_TARGET_EXECUTION_TIMESTAMP => $event->newDateTime->format('Y-m-d H:i e')]);
    }
}
