<?php

namespace App\Listeners\OutreachCadence;

use App\Events\Workflows\TaskConcludedEvent;
use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use App\Services\OutreachCadence\ActionLogger;
use App\Services\OutreachCadence\CompanyCadenceRoutineService;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class ConcludeTaskActionListener implements ShouldQueue
{
    use InteractsWithQueue, Queueable;

    const DELETED_MESSAGE   = 'task was manually deleted';
    const COMPLETED_MESSAGE = 'task was completed';


    public function __construct(
        protected CompanyCadenceRoutineRepository $repository,
        protected CompanyCadenceRoutineService    $service
    )
    {
        $this->onQueue(config('queue.named_queues.outreach_cadence'));
    }

    /**
     * @param TaskConcludedEvent $event
     * @return void
     * @throws Exception
     */
    public function handle(TaskConcludedEvent $event): void
    {
        $action = $this->repository->findPendingActionByTaskId($event->taskId);
        if ($action) {
            ActionLogger::genericLog($action, match ($event->conclusion) {
                TaskConcludedEvent::CONCLUSION_DELETED => self::DELETED_MESSAGE,
                TaskConcludedEvent::CONCLUSION_COMPLETED => self::COMPLETED_MESSAGE
            });
            $this->service->concludeAction($action);

            // if this was a delivery issue alert task, delete it
            if($action->isDeliveryIssueAlertTask())
                $this->repository->deleteGroupAndAction($action->group->id);

        }
    }
}
