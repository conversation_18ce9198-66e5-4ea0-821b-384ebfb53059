<?php

namespace App\Listeners\OutreachCadence;

use App\Events\OutreachCadence\CompanyRoutineDeletedEvent;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\Cadence\CompanyCadenceScheduledGroup;
use App\Models\Cadence\CompanyCadenceScheduledGroupAction;
use App\Repositories\OutreachCadence\CompanyCadenceRoutineRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;

class DeleteRoutineTasksListener
{
    use InteractsWithQueue, Queueable;

    /**
     * @return void
     */
    public function __construct(private readonly CompanyCadenceRoutineRepository $repository)
    {
        $this->onQueue(config('queue.named_queues.outreach_cadence'));
    }

    /**
     * @param CompanyRoutineDeletedEvent $event
     * @return void
     */
    public function handle(CompanyRoutineDeletedEvent $event)
    {
        /** @var CompanyCadenceRoutine $routine */
        $routine = CompanyCadenceRoutine::query()->withTrashed()->find($event->companyCadenceRoutineId);
        if ($routine)
            /** @var CompanyCadenceScheduledGroup $group */
            foreach ($routine->scheduledGroups as $group) {
                /** @var CompanyCadenceScheduledGroupAction $action */
                foreach ($group->actions as $action) {
                    if ($action->task)
                        $action->task->delete();
                }
            }
    }
}
