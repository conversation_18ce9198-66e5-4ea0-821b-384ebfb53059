<?php

namespace App\Listeners;

use App\Events\PrivacyRequestConsumerRedacted;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class PrivacyRequestConsumerRedactedListener
{
    public function __construct() {}

    public function handle(PrivacyRequestConsumerRedacted $event): void
    {
        try {
            $consumer = Consumer::query()->findOrFail($event->consumerId);
        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return;
        }

        /** @var ProductAssignment $productAssignment */
        foreach($consumer->productAssignments() as $productAssignment) {
            //if not billed
            if(!$productAssignment->invoiceItem) {
                $productAssignment->chargeable = false;
                $productAssignment->save();
            }
        }
    }
}
