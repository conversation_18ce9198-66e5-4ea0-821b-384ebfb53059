<?php

namespace App\Listeners;

use App\Events\ProductRejectionDeleted;
use App\Models\Affiliates\Payout;
use App\Models\Odin\ConsumerProduct;
use App\Services\Affiliate\PayoutService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProductRejectionDeletedListener implements ShouldQueue
{
    public string $queue = QueueHelperService::QUEUE_NAME_AFFILIATES;
    /**
     * Create the event listener.
     */
    public function __construct(protected PayoutService $payoutService)
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(ProductRejectionDeleted $event): void
    {
        $consumerProduct = $event->productRejection->productAssignment->consumerProduct
            ->loadMissing([ConsumerProduct::RELATION_AFFILIATE_PAYOUT .'.'. Payout::RELATION_STRATEGY]);

        $this->payoutService->updateIfExists(consumerProduct: $consumerProduct);
    }
}
