<?php

namespace App\Listeners;

use App\Enums\Company\CompanySystemStatus;
use App\Events\RejectionStatisticsUpdatedEvent;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Company;
use App\Repositories\Odin\ProductRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class RejectionStatisticsUpdatedListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct(public ProductRepository $productRepository) {}

    /**
     * Handle the event.
     */
    public function handle(RejectionStatisticsUpdatedEvent $event): void
    {
        $this->handleCompaniesSystemStatusUpdate();
    }

    /**
     * @return void
     */
    protected function handleCompaniesSystemStatusUpdate(): void
    {
        Company::query()
            ->where(Company::FIELD_SYSTEM_STATUS, CompanySystemStatus::SUSPENDED_CRM_REJECTION_THRESHOLD)
            ->get()
            ->each(fn(Company $company) => $company->recalculateSystemStatus());

        ComputedRejectionStatistic::query()
            ->with(ComputedRejectionStatistic::RELATION_COMPANY)
            ->where(ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE, '>=', config('sales.leads.crm_rejection_percentage_threshold'))
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $this->productRepository->getLeadProductId())
            ->get()
            ->each(fn(ComputedRejectionStatistic $computedRejectionStatistic) => $computedRejectionStatistic->company->recalculateSystemStatus());
    }
}
