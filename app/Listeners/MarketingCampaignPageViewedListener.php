<?php

namespace App\Listeners;

use App\Events\MarketingCampaignPageViewed;
use App\Jobs\MarketingCampaign\UpdateEmailMarketingCampaignMetrics;
use App\Models\MarketingCampaignConsumer;
use App\Repositories\MarketingCampaign\MarketingCampaignConsumerRepository;
use App\Repositories\MarketingCampaign\MarketingCampaignRepository;
use App\Services\MarketingCampaign\MarketingCampaignService;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class MarketingCampaignPageViewedListener
{
    /**
     * Create the event listener.
     */
    public function __construct(
        protected MarketingCampaignConsumerRepository $marketingCampaignConsumerRepository,
        protected MarketingCampaignService $marketingCampaignService,
        protected MarketingCampaignRepository $marketingCampaignRepository,
    )
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(MarketingCampaignPageViewed $event): void
    {
        $marketingConsumerId = $event->marketingCampaignConsumerId;

        try {
            $marketingConsumer = $this->marketingCampaignConsumerRepository->findOrFail($marketingConsumerId);
        } catch (ModelNotFoundException $exception) {
            logger()->error($exception->getMessage());
            return;
        }

        if (empty($marketingConsumer->{MarketingCampaignConsumer::FIELD_REVALIDATED_AT})) {
            UpdateEmailMarketingCampaignMetrics::dispatch($marketingConsumer->{MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID});
        }
    }
}
