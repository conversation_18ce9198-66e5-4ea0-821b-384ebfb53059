<?php

namespace App\Factories;

use App\Enums\Odin\StateAbbreviation;
use App\Enums\RulesetType;
use App\Models\Ruleset;
use App\Services\Odin\Ruleset\Enums\OperationType;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyAppointmentRejectionRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyEmployeeRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyGoogleReviewRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyLeadRejectionRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyLeadRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyLocationRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyOfficeLocationsRule;
use App\Services\Odin\Ruleset\Rules\CompanyRules\CompanyRevenueEarnRule;
use App\Services\Odin\Ruleset\Rules\Condition;
use App\Services\Odin\Ruleset\Rules\Operation;
use App\Services\Odin\Ruleset\Rules\RuleData;

class RulesetFactory
{
    static function createDefaultCompanyLeadRule(): CompanyLeadRule
    {
        $ruleData = new RuleData([
            new Condition(10, [
                new Operation(OperationType::LESS_THAN, [
                    "start" => 10000,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
            new Condition(20, [
                new Operation(OperationType::GREATER_THAN, [
                    "start" => 10000,
                    "end" => null,
                    "rate" => null,
                ])
            ]),

        ],20);

        $rule = new CompanyLeadRule();
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function createDefaultCompanyLocationsRule(): CompanyLocationRule
    {
        $ruleData = new RuleData([
            new Condition(10, [
                new Operation(OperationType::RATE, [
                    "start" => null,
                    "end" => null,
                    "rate" => 2,
                ])
            ]),
        ],10);

        $rule = new CompanyLocationRule();
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function createDefaultCompanyGoogleReviewRule(): CompanyGoogleReviewRule
    {
        $ruleData = new RuleData([
            new Condition(10, [
                new Operation(OperationType::GREATER_THAN, [
                    "start" => 20,
                    "end" => null,
                    "rate" => null,
                ], CompanyGoogleReviewRule::FIELD_FOUR_STARS)
            ]),
        ],10);

        $rule = new CompanyGoogleReviewRule();
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function createDefaultCompanyEmployeeRule(): CompanyEmployeeRule
    {
        $ruleData = new RuleData([
            new Condition(0, [
                new Operation(OperationType::LESS_THAN, [
                    "start" => 10,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
            new Condition(5, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 10,
                    "end" => 20,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 20,
                    "end" => 50,
                    "rate" => null,
                ])
            ]),
            new Condition(20, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 50,
                    "end" => 200,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::GREATER_THAN, [
                    "start" => 100,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
        ],20);

        $rule = new CompanyEmployeeRule();
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function createDefaultCompanyAppointmentRejectionRule(): CompanyAppointmentRejectionRule
    {
        $ruleData = new RuleData([
            new Condition(0, [
                new Operation(OperationType::LESS_THAN, [
                    "start" => 10,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
            new Condition(5, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 10,
                    "end" => 20,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 20,
                    "end" => 50,
                    "rate" => null,
                ])
            ]),
            new Condition(20, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 50,
                    "end" => 200,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::GREATER_THAN, [
                    "start" => 100,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
        ],20);

        $rule = new CompanyAppointmentRejectionRule();
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function createDefaultCompanyCompanyLeadRejectionRule(): CompanyLeadRejectionRule
    {
        $ruleData = new RuleData([
            new Condition(0, [
                new Operation(OperationType::LESS_THAN, [
                    "start" => 10,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
            new Condition(5, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 10,
                    "end" => 20,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 20,
                    "end" => 50,
                    "rate" => null,
                ])
            ]),
            new Condition(20, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 50,
                    "end" => 200,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::GREATER_THAN, [
                    "start" => 100,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
        ],20);

        $rule = new CompanyLeadRejectionRule();
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function createDefaultCompanyRevenueEarnRule(): CompanyRevenueEarnRule
    {
        $ruleData = new RuleData([
            new Condition(0, [
                new Operation(OperationType::LESS_THAN, [
                    "start" => 10,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
            new Condition(5, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 10,
                    "end" => 20,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 20,
                    "end" => 50,
                    "rate" => null,
                ])
            ]),
            new Condition(20, [
                new Operation(OperationType::BETWEEN, [
                    "start" => 50,
                    "end" => 200,
                    "rate" => null,
                ])
            ]),
            new Condition(10, [
                new Operation(OperationType::GREATER_THAN, [
                    "start" => 100,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
        ],20);

        /** @var CompanyRevenueEarnRule $rule */
        $rule = app()->make(CompanyRevenueEarnRule::class);
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function createDefaultCompanyOfficeLocationsRule(): CompanyOfficeLocationsRule
    {
        $ruleData = new RuleData([
            new Condition(0, [
                new Operation(OperationType::IN, [
                    "start" => StateAbbreviation::CA->value,
                    "end" => null,
                    "rate" => null,
                ])
            ]),
        ],20);

        $rule = new CompanyOfficeLocationsRule();
        $rule->setIsActive(true);
        $rule->setData($ruleData);

        return $rule;
    }

    static function generateTemplate(): array
    {
        $defaultCompanyLeadRule = self::createDefaultCompanyLeadRule();
        $defaultCompanyLocationsRule = self::createDefaultCompanyLocationsRule();
        $defaultCompanyGoogleReviewRule = self::createDefaultCompanyGoogleReviewRule();
        $defaultCompanyEmployeeRule = self::createDefaultCompanyEmployeeRule();
        $defaultCompanyAppointmentRejectionRule = self::createDefaultCompanyAppointmentRejectionRule();
        $defaultCompanyCompanyLeadRejectionRule = self::createDefaultCompanyCompanyLeadRejectionRule();
        $defaultCompanyRevenueEarnRule = self::createDefaultCompanyRevenueEarnRule();
        $defaultCompanyOfficeLocationsRule = self::createDefaultCompanyOfficeLocationsRule();

        $ruleset = new Ruleset();

        $ruleset->{Ruleset::FIELD_NAME} = 'Rules template';
        $ruleset->{Ruleset::FIELD_TYPE} = RulesetType::RANKING->value;
        $ruleset->{Ruleset::FIELD_FILTER} = [];
        $ruleset->{Ruleset::FIELD_SOURCE} = 'companies'; // TODO - Create enum
        $ruleset->{Ruleset::FIELD_RULES} = [
            $defaultCompanyLeadRule->toArray(),
            $defaultCompanyLocationsRule->toArray(),
            $defaultCompanyGoogleReviewRule->toArray(),
            $defaultCompanyEmployeeRule->toArray(),
            $defaultCompanyAppointmentRejectionRule->toArray(),
            $defaultCompanyCompanyLeadRejectionRule->toArray(),
            $defaultCompanyRevenueEarnRule->toArray(),
            $defaultCompanyOfficeLocationsRule->toArray()
        ];

        return $ruleset->toArray();
    }
}

