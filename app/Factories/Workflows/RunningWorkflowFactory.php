<?php

namespace App\Factories\Workflows;

use App\Enums\RunningWorkflowStatus;
use App\Models\RunningWorkflow;
use App\Workflows\WorkflowPayload;

class RunningWorkflowFactory
{
    /**
     * @param int $workflowId
     * @param WorkflowPayload $payload
     * @param string $status
     * @param int $currentActionId
     *
     * @return RunningWorkflow
     */
    public static function create(int $workflowId, WorkflowPayload $payload, RunningWorkflowStatus $status, int $currentActionId): RunningWorkflow
    {
        $runningWorkflow = new RunningWorkflow();

        $runningWorkflow->workflow_id = $workflowId;
        $runningWorkflow->payload = $payload;
        $runningWorkflow->status = $status;
        $runningWorkflow->current_action_id = $currentActionId;

        $runningWorkflow->save();

        return $runningWorkflow;
    }
}
