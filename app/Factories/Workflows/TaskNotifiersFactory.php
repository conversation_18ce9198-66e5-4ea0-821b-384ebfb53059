<?php

namespace App\Factories\Workflows;

use App\DataModels\Workflows\TaskNotifiersDataModel;
use Illuminate\Support\Collection;

class TaskNotifiersFactory
{
    /**
     * Creates a collection of task notifiers from a payload.
     *
     * @param array $data
     * @return Collection
     */
    public static function makeFromPayload(array $data): Collection
    {
        return collect($data)->map(fn(array $datum) =>
            isset($datum["template"]) && isset($datum["delay"])
                ? new TaskNotifiersDataModel(intval($datum["template"]), intval($datum["delay"]))
                : null
        )->filter();
    }
}
