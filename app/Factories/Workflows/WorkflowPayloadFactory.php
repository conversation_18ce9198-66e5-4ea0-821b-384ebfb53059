<?php

namespace App\Factories\Workflows;

use App\Workflows\WorkflowEvent;
use App\Workflows\WorkflowPayload;

class WorkflowPayloadFactory
{
    /**
     * @param WorkflowEvent $event
     *
     * @return WorkflowPayload
     */
    public static function create(WorkflowEvent $event): WorkflowPayload
    {
        return new WorkflowPayload($event, collect());
    }

    /**
     * @param array $data
     *
     * @return WorkflowPayload
     */
    public static function createFromPubSubEventPayload(array $data): WorkflowPayload
    {
        return self::create(WorkflowEventFactory::createFromPubSubPayload($data));
    }
}
