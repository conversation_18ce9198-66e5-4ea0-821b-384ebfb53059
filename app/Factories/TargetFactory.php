<?php

namespace App\Factories;

use App\DataModels\EmailTarget;
use App\DataModels\Target;
use App\Enums\Target as TargetType;
use App\Enums\TargetRelation;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Collection;

class TargetFactory
{
    public static function fromPayload(array $targets, int|string $targetId = 0): Collection
    {
        return collect($targets)->map(
            fn($target) => key_exists("target_type", $target) && key_exists("target_relation", $target) ?
                new Target($targetId, TargetType::from($target["target_type"]), TargetRelation::get($target["target_relation"])) :
                null
        )->filter(fn($target) => $target !== null);
    }

    /**
     * @param array $targets
     *
     * @return Collection<EmailTarget>
     */
    public static function fromEmailPayload(array $targets, WorkflowPayload $payload): Collection
    {
        $shortcodeService = WorkflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER);

        return collect($targets)->map(
            fn($target) => filter_var($shortcodeService->handle($target['to'] ?? '', $payload), FILTER_VALIDATE_EMAIL) && key_exists('from', $target) ?
                new EmailTarget($shortcodeService->handle($target['to'], $payload), $target['from']) :
                null
        )->filter(fn($target) => $target !== null);
    }
}
