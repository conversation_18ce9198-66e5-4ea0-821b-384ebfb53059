<?php

namespace App\Factories;

use App\Contracts\APIResponseFactoryInterface;
use Illuminate\Contracts\Routing\ResponseFactory;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\JsonResponse;

class JsonAPIResponseFactory implements APIResponseFactoryInterface
{
    /** @var ResponseFactory $responseFactory */
    private ResponseFactory $responseFactory;

    /**
     * JsonApiResponseFactory constructor.
     *
     * @param ResponseFactory $responseFactory
     */
    public function __construct(ResponseFactory $responseFactory) {
        $this->responseFactory = $responseFactory;
    }

    /**
     * @param Validator $validator
     * @return JsonResponse
     */
    function makeValidationErrorResponse(Validator $validator): JsonResponse
    {
        return $this->responseFactory->json([
            'messages' => $validator->getMessageBag()->toArray(),
        ], JsonResponse::HTTP_UNPROCESSABLE_ENTITY);
    }
}
