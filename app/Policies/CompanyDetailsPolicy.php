<?php

namespace App\Policies;

use App\Enums\PermissionType;
use App\Http\Requests\StoreCompanyDetailsRequest;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCampaign;
use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Validation\ValidationException;

class CompanyDetailsPolicy
{
    use HandlesAuthorization;

    /**
     * @throws ValidationException
     */
    public function update(Company $company, User $user, ?array $serviceIds): true
    {
        if (!isset($serviceIds)) return true;

        $removedServiceIds = $company->services->pluck(IndustryService::FIELD_ID)->diff($serviceIds);

        if (!$user->can(PermissionType::COMPANY_UPDATE_INDUSTRIES->value) && $removedServiceIds->count() > 0)
        {
            throw ValidationException::withMessages([
                StoreCompanyDetailsRequest::FIELD_SERVICES => ["You don't have permission to update company industries."]
            ]);
        }

        if (count($removedServiceIds) > 0) {
            $companyCampaigns = $company
                ->productCampaigns()
                ->whereIn(ProductCampaign::FIELD_INDUSTRY_SERVICE_ID, $removedServiceIds)
                ->count();

            if ($companyCampaigns > 0) {
                $errorMessage = 'You cannot remove industries that are associated with campaigns set up by the company';

                throw ValidationException::withMessages([
                    'message' => $errorMessage,
                    StoreCompanyDetailsRequest::FIELD_SERVICES => [$errorMessage]
                ]);
            }
        }

        return true;
    }
}
