<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class Phone implements Rule
{

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        $phone = preg_replace('/\D/', '', $value);

        return preg_match('/^1(\d{10})$|^\d{10}$/', $phone);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'The :attribute must be a valid phone.';
    }

    /**
     * Strip a phone number back to digits before validation, so only the digits will be stored
     *
     * @param string|int|null $inputValue
     * @return ?string
     */
    static function preparePhoneNumberForValidation(string|int|null $inputValue): ?string
    {
        if (!$inputValue) return null;
        $phoneNumberAsString = gettype($inputValue) === 'integer' ? strval($inputValue) : $inputValue;
        $digits = preg_replace('/\D/', '', $phoneNumberAsString);
        return preg_replace('/^1/', '', $digits);
    }
}
