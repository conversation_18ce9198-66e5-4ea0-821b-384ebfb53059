<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Str;
use Illuminate\Translation\PotentiallyTranslatedString;

class StrongPassword implements ValidationRule
{
    protected $blacklist = [
        'solar',
        'roofing',
        'solarreviews',
        'fixr',
        'Solar1234',
        'solar12345',
        'Roofing123',
        'Roofing1234',
        'roofing123',
        'roofing1234',
        'roofing123!',
        'solar123!',
    ];

    /**
     * Run the validation rule.
     *
     * @param Closure(string): PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) < 8) {
            $fail('The :attribute must be at least 8 characters long.');
        }

        if (!preg_match('/[A-Z]/', $value)) {
            $fail('The :attribute must include at least one uppercase letter.');
        }

        if (!preg_match('/[a-z]/', $value)) {
            $fail('The :attribute must include at least one lowercase letter.');
        }

        if (!preg_match('/[\W_]/', $value)) {
            $fail('The :attribute must include at least one special character.');
        }

        foreach ($this->blacklist as $blacklistedWord) {
            if (Str::lower($value) === Str::lower($blacklistedWord)) {
                $fail("The :attribute must be more complex");
            }
        }
    }
}
