<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class InputTypes implements Rule
{
    /**
     * Create a new rule instance.
     *
     * Valid input types are those returned by php's gettype()
     * https://www.php.net/manual/en/function.gettype.php
     * @return void
     */
    public function __construct(private array $inputTypes)
    {

    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        return in_array(gettype($value), $this->inputTypes);
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return "Invalid type.";
    }
}
