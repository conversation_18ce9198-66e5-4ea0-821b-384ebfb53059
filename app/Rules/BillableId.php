<?php

namespace App\Rules;

use App\Enums\Billing\InvoiceItemTypes;
use App\Models\Billing\CreditType;
use App\Models\Odin\ProductAssignment;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class BillableId implements DataAwareRule, ValidationRule
{
    protected $data = [];
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $billableType = $this->data['billable_type'];
        $billableId = $this->data['billable_id'];

        if ($billableType === InvoiceItemTypes::CREDIT) {
            $creditTypeSlugs = CreditType::query()->pluck(CreditType::FIELD_SLUG)->toArray();
            if (!in_array($billableId, $creditTypeSlugs)) {
                $fail("Credit type: " . $billableId ." could not be found");
            }
        } elseif ($billableType === InvoiceItemTypes::PRODUCT) {
            $productAssignmentIds = ProductAssignment::query()->pluck(ProductAssignment::FIELD_ID)->toArray();
            if (!in_array($billableId, $productAssignmentIds)) {
                $fail("Product Assignment: ". $billableId ." could not be found");
            }
        } elseif ($billableType === InvoiceItemTypes::MANUAL) {
            return;
        } else {
            $fail("Invoice item type invalid");
        }
    }

    /**
     * @param array $data
     * @return $this
     */
    public function setData(array $data): static
    {
        $this->data = $data;
        return $this;
    }
}
