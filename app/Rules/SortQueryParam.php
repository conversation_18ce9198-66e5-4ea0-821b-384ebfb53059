<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\InvokableRule;

class SortQueryParam implements InvokableRule
{
    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail): void
    {
        $parts = explode(':', $value);

        if (count($parts) !== 2) {
            $fail("The $attribute format is invalid.");
        }

        if (!in_array($parts[1], ['asc', 'desc'])) {
            $fail("The $attribute must have either 'asc' or 'desc' after the colon.");
        }

        $acceptedFields = ['consolidated_status', 'lead_rejection_percentage', 'appointment_rejection_percentage', 'total_cost_spent', 'company_name', 'sales_status'];

        $acceptedFieldsString = implode(', ', $acceptedFields);

        if (!in_array($parts[0], $acceptedFields)) {
            $fail("The $attribute must have a field (before the colon) that is one of the following: $acceptedFieldsString. $value passed.");
        }
    }
}
