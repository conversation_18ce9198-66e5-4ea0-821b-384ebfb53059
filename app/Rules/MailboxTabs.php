<?php

namespace App\Rules;

use App\Enums\Mailbox\EmailCategory;
use App\Models\Mailbox\MailboxUserLabel;
use App\Services\Odin\Ruleset\Rules\Rule;
use Illuminate\Contracts\Validation\InvokableRule;
use Illuminate\Support\Facades\Auth;

class MailboxTabs implements InvokableRule
{
    /**
     * Run the validation rule.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     * @return void
     */
    public function __invoke($attribute, $value, $fail): void
    {
        $userId = Auth::user()->id;
        $mailboxUserLabels = MailboxUserLabel::query()
            ->where(MailboxUserLabel::FIELD_USER_ID, $userId)
            ->pluck(MailboxUserLabel::FIELD_ID)->toArray();

        if (!in_array($value, EmailCategory::getValues()->toArray()) && !in_array($value, $mailboxUserLabels)) {
            $fail("Not a valid tab");
        }
    }
}
