<?php

namespace App\Rules;

use App\Enums\EventName;
use App\Models\WorkflowEvent;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use Closure;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\InvokableRule;

class ValidateShortcodes implements InvokableRule, DataAwareRule
{
    const RX_SHORTCODE = "/\{([\w_-]+)}/";

    protected array $validShortcodes;
    protected ?EventName $eventName;

    /**
     * Run the validation rule.
     * @param string $attribute
     * @param mixed $value
     * @param Closure $fail
     * @throws BindingResolutionException
     * @return void
     */
    public function __invoke($attribute, $value, $fail): void
    {
        $shortcodeFactory = app()->make(WorkflowShortcodeServiceFactory::class);
        $shortcodes = $shortcodeFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER)->getShortcodes($this->eventName);
        $this->validShortcodes = array_map(fn($val) => $val['value'], $shortcodes);

        if (gettype($value) !== 'array' && gettype($value) !== 'string') return;

        $firstFailedCode = (gettype($value) === 'array')
            ? $this->validateArray($value)
            : $this->validateString($value);

        if ($firstFailedCode) $fail("The shortcode {{$firstFailedCode}} is invalid for this event.");
    }

    /**
     * Validates shortcodes in a single string
     * @param string $inputString
     * @return string|null
     */
    private function validateString(string $inputString): ?string
    {
        $shortcodeMatches = [];
        preg_match_all(self::RX_SHORTCODE, $inputString, $shortcodeMatches, PREG_SET_ORDER);
        if (!$shortcodeMatches) return null;
        foreach ($shortcodeMatches as $match) {
            if (!$this->testShortcode($match[1])) return $match[1];
        }
        return null;
    }

    /**
     * Validates all strings in an array, recursively
     * @param array $inputArray
     * @return string|null
     */
    private function validateArray(array $inputArray): ?string
    {
        return $this->processArrayLevel($inputArray);
    }

    /**
     * Recursion function for processing arrays
     * @param array $arr
     * @return string|null
     */
    private function processArrayLevel(array $arr): ?string
    {
        foreach ($arr as $val) {
            if (gettype($val) === 'string') {
                $result = $this->validateString($val);
                if ($result) {
                    return $result;
                }
            }
            else if (gettype($val) === 'array') {
                $innerResult = $this->processArrayLevel($val);
                if ($innerResult) {
                    return $innerResult;
                }
            }
        }
        return null;
    }

    /**
     * Test the validity of a single shortcode
     * @param string $code
     * @return bool
     */
    private function testShortcode(string $code): bool
    {
        return in_array($code, $this->validShortcodes);
    }

    /**
     * @param array $data
     * @return ValidateShortcodes
     */
    public function setData($data): ValidateShortcodes
    {
        $this->eventName = EventName::tryFrom($data[WorkflowEvent::FIELD_EVENT_NAME] ?? '');
        return $this;
    }
}
