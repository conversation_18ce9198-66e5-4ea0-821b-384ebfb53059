<?php

namespace App\Rules;

use App\Enums\SortOrder;
use Illuminate\Contracts\Validation\Rule;

class OrderFormat implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value): bool
    {
        return preg_match('/^[\w\s]+:(' . SortOrder::ASC->value . '|' . SortOrder::DESC->value . ')$/i', $value) === 1;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message(): string
    {
        return 'The :attribute format must be "field:ASC" or "field:DESC".';
    }
}
