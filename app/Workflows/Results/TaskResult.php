<?php

namespace App\Workflows\Results;

use App\DataModels\Workflows\TaskResultDataModel;
use App\Enums\TaskResultType;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class TaskResult implements Jsonable
{

    public function __construct(public TaskResultDataModel $result, public Collection $data) { }

    public function toJson($options = 0): ?string
    {
        return json_encode(
            [
                "id"          => $this->result->id,
                "name"        => $this->result->name,
                "result_type" => $this->result->type,
                "options"     => $this->result->options,
            ]
        );
    }

    public static function from<PERSON>son(string $json): TaskResultDataModel
    {
        $object = json_decode($json, true);

        return new TaskResultDataModel(
            $object['id'],
            $object['name'],
            TaskResultType::from($object['type']),
            collect($object['options'])->toArray()
        );
    }

}
