<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\ActionVisitor;
use App\Contracts\Workflows\CreateTaskContract;
use App\Contracts\Workflows\PausesWorkflow;
use App\DataModels\Target;
use App\DataModels\Workflows\TaskResultDataModel;
use App\Enums\ActionType;
use App\Enums\TaskResultType;
use App\Factories\TargetFactory;
use App\Factories\Workflows\TaskNotifiersFactory;
use App\Models\Legacy\EloquentQuote;
use App\Models\TaskCategory;
use App\Models\WorkflowAction;
use App\Repositories\LocationRepository;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;
use function PHPUnit\Framework\returnArgument;

class CreateTaskAction extends Action implements CreateTaskContract, PausesWorkflow
{
    /**
     * @return mixed
     */
    public function handle(): mixed
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    public function getSubject(): string
    {
        return $this->getAction()->payload["task_name"];
    }

    /**
     * @inheritDoc
     */
    public function getResults(): Collection
    {
        return $this->getAction()
                    ->children()
                    ->where(WorkflowAction::FIELD_ACTION_TYPE, ActionType::RESULT_NODE)
                    ->get()
                    ->map(fn(WorkflowAction $action) => new TaskResultDataModel($action->id, $action->payload["name"],
                        TaskResultType::from($action->payload["type"]), $action->payload["options"] ?? []));
    }

    /**
     * @inheritDoc
     */
    public function getAvailableAt(): Carbon
    {
        return Carbon::now();
    }

    /**
     * @inheritDoc
     */
    public function getTaskType(): int
    {
        return $this->getAction()->payload["task_type"] ?? "";
    }

    /**
     * @inheritDoc
     */
    public function getAssignedUserId(): ?int
    {
        return $this->getTargetRepository()
                    ->getTargets(
                        $this->getTaskTarget(),
                        $this->getPayload()
                    )->first()?->id;
    }

    /**
     * @inheritDoc
     */
    public function getTaskTarget(): Target
    {
        return TargetFactory::fromPayload($this->getAction()->payload['targets'], $this->getTargetIdFromEvent())->first();
    }

    /**
     * @inheritDoc
     */
    public function getTaskEventCategory(): string
    {
        return $this->getPayload()->event->eventCategory ?? "";
    }

    /**
     * @inheritDoc
     */
    public function getTaskEventName(): string
    {
        return $this->getPayload()->event->eventName ?? "";
    }

    /**
     * Handles overriding the get next action to allow for logic in choosing the next action.
     *
     * @return WorkflowAction|null
     */
    public function getNextAction(): ?WorkflowAction
    {
        $children = $this->getAction()
                         ->children()
                         ->where(WorkflowAction::FIELD_ACTION_TYPE, ActionType::RESULT_NODE)
                         ->get();

        return $children->where(WorkflowAction::FIELD_ID, $this->getPayload()->get('last_chosen_result'))->first();
    }

    /**
     * Handles calling the correct visitor method.
     *
     * @param ActionVisitor $visitor
     * @return mixed
     */
    public function accept(ActionVisitor $visitor): mixed
    {
        return $visitor->visitTaskAction($this);
    }

    /**
     * @inheritDoc
     */
    public function getPriority(): int
    {
        return $this->getAction()->payload["priority"];
    }

    /**
     * @inheritDoc
     */
    public function getReschedulingStatus(): bool
    {
        return $this->getAction()->payload["reschedule"] ?? false;
    }

    /**
     * @inheritDoc
     */
    public function getNotifiers(): Collection
    {
        return TaskNotifiersFactory::makeFromPayload($this->getAction()->payload["notifiers"]);
    }

    /**
     * @inheritDoc
     */
    public function shouldInheritTaskNotes(): bool
    {
        return isset($this->getAction()->payload["inherit_task_notes"]) && intval($this->getAction()->payload["inherit_task_notes"]) == 1;
    }

    /**
     * @inheritDoc
     */
    public function getTaskCategory(): int
    {
        return $this->getAction()->payload['task_category'] ?? TaskCategory::DEFAULT_CATEGORY;
    }

    /**
     * @inheritDoc
     */
    public function getDynamicPriorityStatus(): int
    {
        return $this->getAction()->payload['dynamic_priority_enabled'] ?? 0;
    }

    /**
     * @inheritDoc
     */
    public function getDynamicPriorityType(): ?int
    {
        return $this->getAction()->payload['dynamic_priority_type'] ?? null;
    }

    /**
     * @inheritDoc
     */
    public function getLead(): ?EloquentQuote
    {
        $leadId = $this->getPayload()->event->get('lead_id');

        if (!$leadId) return null;

        /** @var EloquentQuote */
        return EloquentQuote::query()->find($leadId);
    }

    /**
     * @return int|null
     * @throws BindingResolutionException
     */
    public function getCountyLocationId(): ?int
    {
        $lead = $this->getLead();

        if (!$lead) return null;

        /** @var LocationRepository $locationRepository */
        $locationRepository = app()->make(LocationRepository::class);

        return $locationRepository->getCountyFromZipcode($lead->address->zipcode)?->id;
    }
}
