<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\LogicNode;
use App\Enums\ComparisonOperator;
use App\Models\WorkflowAction;
use Illuminate\Support\Arr;

class ComparisonNode extends LogicNode
{
    /**
     * @inheritDoc
     */
    public function handle(): mixed
    {
        return null;
    }

    /**
     * Returns the value to be compared.
     *
     * @return float
     */
    protected function getValue(): mixed
    {
        $value = $this->processValue($this->getAction()->payload["value"]);

        return is_string($value) ? strtolower($value) : $value;
    }

    /**
     * Returns the value being compared against
     *
     * @return float
     */
    protected function getComparingValue(): mixed
    {
        $value = $this->processValue($this->getAction()->payload["comparison_value"]);

        return is_string($value) ? strtolower($value) : $value;
    }

    /**
     * Returns the operator for the comparison.
     *
     * @return ComparisonOperator
     */
    protected function getOperator(): ComparisonOperator
    {
        return ComparisonOperator::from($this->getAction()->payload["operator"]);
    }

    /**
     * @inheritDoc
     */
    public function compare(): bool
    {
        return match ($this->getOperator()) {
            ComparisonOperator::GREATER_THAN       => $this->getValue() > $this->getComparingValue(),
            ComparisonOperator::GREATER_THAN_EQUAL => $this->getValue() >= $this->getComparingValue(),
            ComparisonOperator::LESS_THAN          => $this->getValue() < $this->getComparingValue(),
            ComparisonOperator::LESS_THAN_EQUAL    => $this->getValue() <= $this->getComparingValue(),
            ComparisonOperator::NOT_EQUAL          => $this->getValue() != $this->getComparingValue(),
            ComparisonOperator::IN                 => in_array(strtolower($this->getValue()), collect(Arr::wrap($this->getComparingValue()))->map(fn($item) => is_string($item) ? strtolower($item) : $item)->toArray()),
            default                                => $this->getValue() == $this->getComparingValue(),
        };
    }
}
