<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\HasNotificationContract;
use App\Contracts\Workflows\HasTargetContract;
use App\Enums\Channel;
use App\Factories\TargetFactory;
use App\Models\Notification;
use Illuminate\Support\Collection;

class AddNotificationAction extends Action implements HasNotificationContract
{
    /**
     * @return int
     */
    public function getNotifier(): int
    {
        return $this->getAction()->payload['notifier'] ?? Notification::FROM_SYSTEM;
    }

    /**
     * @return Channel
     */
    public function getChannel(): Channel
    {
        return Channel::DEFAULT;
    }

    /**
     * @return string
     */
    public function getSubject(): string
    {
        return $this->getAction()->payload['subject'] ?? "";
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        // TODO: Investigate whether we want to add a "shortcode"-esque parsing here.
        return $this->getAction()->payload['message'] ?? "";
    }

    /**
     * @return Collection<HasTargetContract>
     */
    public function getTargets(): Collection
    {
        return TargetFactory::fromPayload(
            $this->getAction()->payload['targets'] ?? [],
            $this->getTargetIdFromEvent()
        );
    }

    /**
     * @return array|null
     */
    public function handle(): ?array
    {
        return null;
    }
}
