<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\PausesWorkflow;
use App\Jobs\Workflows\RunWorkflowPipeline;
use App\Models\RunningWorkflow;

class DelayNode extends Action implements PausesWorkflow
{
    /**
     * Returns the delay in seconds that the task should be paused for.
     *
     * @return float
     */
    public function getDelay(): float
    {
        return floatval($this->getAction()->payload["delay"]) ?? 60;
    }

    /**
     * Handles delaying the task.
     *
     * @return mixed
     */
    public function handle(): mixed
    {
        $workflow = RunningWorkflow::query()->find($this->getRunningWorkflowId());

        if($workflow)
            RunWorkflowPipeline::dispatch($workflow)->delay($this->getDelay());

        return null;
    }
}
