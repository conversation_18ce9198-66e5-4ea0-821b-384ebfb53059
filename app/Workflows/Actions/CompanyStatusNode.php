<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\HasTargetContract;
use App\DataModels\Target;
use App\Enums\Target as TargetType;
use App\Models\Legacy\EloquentCompany;
use App\Models\WorkflowAction;
use App\Repositories\Legacy\CompanyRepository;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Collection;

class CompanyStatusNode extends Action
{
    /**
     * @param WorkflowAction $workflowAction
     * @param WorkflowPayload $payload
     * @param int|null $runningWorkflowId
     * @param CompanyRepository $repository
     */
    public function __construct(
        protected WorkflowAction $workflowAction,
        protected WorkflowPayload $payload,
        protected ?int $runningWorkflowId,
        protected CompanyRepository $repository
    ){}

    /**
     * Returns the company status.
     *
     * @return string
     */
    public function getStatus(): string
    {
        return $this->getAction()->payload["company_status"] ?? "";
    }

    /**
     * @return Collection<HasTargetContract>
     */
    public function getTargets(): Collection
    {
        return collect([new Target($this->getPayload()->event->get('company_id'), TargetType::COMPANY)]);
    }

    /**
     * @return mixed
     */
    public function handle(): mixed
    {
        foreach($this->getTargets() as $target){
            /** @var EloquentCompany|null $company */
            $company = EloquentCompany::query()->find($target->getTarget())->first();
            if(!$company) {return null;}

            $this->repository->updateCompanyStatus($company->{EloquentCompany::REFERENCE}, $this->getStatus());
        }
        return true;
    }
}
