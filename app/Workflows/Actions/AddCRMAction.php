<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\HasNotificationContract;
use App\Contracts\Workflows\HasTargetContract;
use App\DataModels\Target;
use App\Enums\Channel;
use App\Enums\Target as TargetType;
use App\Factories\TargetFactory;
use App\Models\Notification;
use Illuminate\Support\Collection;

class AddCRMAction extends Action implements HasNotificationContract
{
    /**
     * @return int
     */
    public function getNotifier(): int
    {
        // TODO: Support 'User Actioning'
        return TargetFactory::fromPayload(
            $this->getAction()->payload['targets'] ?? [],
            $this->getTargetIdFromEvent()
        )->first()?->getTarget() ?? Notification::FROM_SYSTEM;
    }

    /**
     * @return Channel
     */
    public function getChannel(): Channel
    {
        return Channel::ACTIONS;
    }

    /**
     * @return string
     */
    public function getSubject(): string
    {
        return $this->getAction()->payload['subject'] ?? "";
    }

    /**
     * @return string
     */
    public function getMessage(): string
    {
        return match ($this->getAction()->payload['message_type']) {
            'result' => $this->getPayload()->get("last_result_value"),
            default  => $this->getAction()->payload['message'] ?? "",
        };
    }

    /**
     * @return Collection<HasTargetContract>
     */
    public function getTargets(): Collection
    {
        return collect([new Target($this->getPayload()->event->get('company_id'), TargetType::COMPANY)]);
    }

    /**
     * @return array|null
     */
    public function handle(): ?array
    {
        return null;
    }
}
