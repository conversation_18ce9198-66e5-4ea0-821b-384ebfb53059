<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Enums\RunningWorkflowStatus;
use App\Factories\Workflows\RunningWorkflowFactory;
use App\Jobs\Workflows\RunWorkflowPipeline;
use App\Models\Workflow;
use App\Services\Workflows\WorkflowProcessingService;
use Illuminate\Foundation\Bus\DispatchesJobs;

class RunTemplateNode extends Action
{
    use DispatchesJobs;

    public function handle(): mixed
    {
        $template = $this->getAction()->payload["template_id"];
        /** @var Workflow $workflow */
        $workflow = Workflow::find($template);

        if($template) {
            /** @var WorkflowProcessingService $service */
            $service = app()->make(WorkflowProcessingService::class);

            $service->dispatchNewRunningWorkflow($template, $this->getPayload());
        }

        return null;
    }
}
