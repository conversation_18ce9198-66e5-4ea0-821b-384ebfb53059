<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Contracts\Workflows\HasNotificationContract;
use App\DataModels\EmailTarget;
use App\Enums\Channel;
use App\Factories\TargetFactory;
use App\Models\Notification;
use App\Workflows\Shortcodes\WorkflowShortcodeServiceFactory;
use Illuminate\Support\Collection;

class EmailNotificationAction extends Action implements HasNotificationContract
{

    public function handle(): mixed
    {
        return null;
    }

    public function getNotifier(): int
    {
        return $this->getAction()->payload['notifier'] ?? Notification::FROM_SYSTEM;
    }

    public function getChannel(): Channel
    {
        return Channel::EMAIL;
    }

    public function getSubject(): string
    {
        $subject = $this->getAction()->payload['subject'] ?? '';

        return WorkflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER)->handle($subject, $this->getPayload());
    }

    public function getMessage(): string
    {
        $message = $this->getAction()->payload['message'] ?? '';

        return WorkflowShortcodeServiceFactory::makeService(WorkflowShortcodeServiceFactory::WORKFLOW_DRIVER)->handle($message, $this->getPayload());
    }

    /**
     * @return Collection<EmailTarget>
     */
    public function getTargets(): Collection
    {
        return TargetFactory::fromEmailPayload($this->getAction()->payload['targets'] ?? [], $this->getPayload());
    }
}
