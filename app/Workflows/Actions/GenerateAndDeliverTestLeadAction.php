<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\Action;
use App\Jobs\CreateAndDeliverTestProductJob;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class GenerateAndDeliverTestLeadAction extends Action
{
    /**
     * @return bool
     * @throws ModelNotFoundException
     */
    public function handle(): bool
    {
        /** @var Company $company */
        $company = Company::query()->findOrFail($this->getPayload()->event->get('company_id'));
        $service = $this->getPayload()->event->get('industry_service');

        if (!$service) {
            logger()->error('The event payload lacks service information for the test lead.');
            return false;
        }

        $industryService = $this->getIndustryService($service);

        $revealAt = now()->addDay();

        CreateAndDeliverTestProductJob::dispatch($company, $industryService, $revealAt);

        return true;
    }

    /**
     * @param string $service
     *
     * @return IndustryService
     */
    protected function getIndustryService(string $service): IndustryService
    {
        /** @var IndustryService */
        return IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $service)
            ->firstOrFail();
    }
}
