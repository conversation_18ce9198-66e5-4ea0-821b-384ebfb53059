<?php

namespace App\Workflows\Actions;

use App\Abstracts\Workflows\LogicNode;
use Illuminate\Contracts\Container\BindingResolutionException;
use Exception;

class ModuloNode extends LogicNode
{
    public function handle(): mixed
    {
        return null;
    }

    /**
     * Returns the value to be compared.
     *
     * @return mixed
     * @throws BindingResolutionException
     */
    protected function getValue(): mixed
    {
        $value = $this->processValue($this->getAction()->payload["value"]);

        return is_string($value) ? strtolower($value) : $value;
    }

    /**
     * Returns the value being compared against
     *
     * @return float
     * @throws BindingResolutionException
     */
    protected function getComparingValue(): mixed
    {
        $value = $this->processValue($this->getAction()->payload["comparison_value"]);

        return is_string($value) ? strtolower($value) : $value;
    }

    /**
     * Handle performing the modulo.
     *
     * @return bool
     * @throws Exception
     */
    public function compare(): bool
    {
        // string % float will cause an error if the string cannot be evaluated as a number
        if(is_numeric($this->getValue()) && is_numeric($this->getComparingValue()))
            return $this->getValue() % $this->getComparingValue() === 0 && $this->getValue() > 0;
        else
            throw new Exception('Modulo can only be evaluated with two numeric values');
    }
}
