<?php

namespace App\Workflows;

use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class WorkflowPayload implements Jsonable
{
    public function __construct(public WorkflowEvent $event, public Collection $data) { }

    /**
     * Handles converting this payload to json.
     * This is primarily used in the storing of this data in the database.
     *
     * @param $options
     * @return string
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray());
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            "event" => $this->event->toArray(),
            "data"  => $this->data->toArray(),
        ];
    }

    /**
     * Handles creating a new payload instance from json.
     * This is primarily used in the storing of this data in the database.
     *
     * @param string $json
     * @return WorkflowPayload
     */
    public static function fromJson(string $json): WorkflowPayload
    {
        $object = json_decode($json, true);

        return new WorkflowPayload(
            WorkflowEvent::fromArray($object["event"]),
            collect($object["data"])
        );
    }

    /**
     * Sets a value on the payload.
     *
     * @param string $key
     * @param mixed $value
     * @return $this
     */
    public function set(string $key, mixed $value): WorkflowPayload
    {
        $this->data->put($key, $value);

        return $this;
    }

    /**
     * Returns whether or not this payload has data.
     *
     * @param string $key
     * @return bool
     */
    public function has(string $key): bool
    {
        return $this->data->has($key);
    }

    /**
     * Gets a value from the payload.
     *
     * @param string $key
     * @param mixed|null $default
     * @return mixed
     */
    public function get(string $key, mixed $default = null): mixed
    {
        return $this->data->get($key, $default);
    }
}
