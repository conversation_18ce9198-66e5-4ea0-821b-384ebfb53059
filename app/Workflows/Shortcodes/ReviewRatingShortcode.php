<?php

namespace App\Workflows\Shortcodes;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Contracts\Workflows\ConditionalShortcodeContract;
use App\Enums\EventName;
use App\Workflows\WorkflowPayload;

class ReviewRatingShortcode extends WorkflowPipelineShortcode implements ConditionalShortcodeContract
{
    const string REVIEW_RATING_KEY   = "review_rating";
    const string REVIEW_RATING_LABEL = "Review Rating";

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return self::REVIEW_RATING_KEY;
    }

    /**
     * Handles when no value_name key exists
     * @param string $eventName
     * @return string
     */
    private function valueNameKeyNotFound(string $eventName): string
    {
        $logLine = "ShortcodeError - [$eventName] - tried to use {".self::REVIEW_RATING_KEY."}, key was not found on event";
        logger()->warning($logLine);
        return "'shortcode_not_found'";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $eventName = $payload->event->eventName;
        return $payload->event->get(self::REVIEW_RATING_KEY) ?? $this->valueNameKeyNotFound($eventName);
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return self::REVIEW_RATING_LABEL;
    }

    /**
     * @inheritDoc
     */
    public function conditionalEventValidation(EventName $event): bool
    {
        return in_array(
            $event,
            [
                EventName::CREATED,
                EventName::RESPONDED,
            ]);
    }
}
