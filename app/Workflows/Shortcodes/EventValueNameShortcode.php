<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Contracts\Workflows\ConditionalShortcodeContract;
use App\Enums\EventName;
use App\Workflows\WorkflowPayload;

class EventValueNameShortcode extends WorkflowPipelineShortcode implements ConditionalShortcodeContract
{
    const string VALUE_NAME_KEY   = "value_name";
    const string VALUE_NAME_LABEL = "Value Name";

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return self::VALUE_NAME_KEY;
    }

    /**
     * Handles when no value_name key exists
     * @param string $eventName
     * @return string
     */
    private function valueNameKeyNotFound(string $eventName): string
    {
        $logLine = "ShortcodeError - [$eventName] - tried to use {".self::VALUE_NAME_KEY."}, key was not found on event";
        logger()->warning($logLine);
        return "'shortcode_not_found'";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $eventName = $payload->event->eventName;
        return $payload->event->get(self::VALUE_NAME_KEY) ?? $this->valueNameKeyNotFound($eventName);
    }

    public function getLabel(): string
    {
        return self::VALUE_NAME_LABEL;
    }

    /**
     * @inheritDoc
     */
    public function conditionalEventValidation(EventName $event): bool
    {
        return in_array(
            $event,
            [
                EventName::PROFILE_ADDRESS_CREATED,
                EventName::PROFILE_ADDRESS_UPDATED,
                EventName::PROFILE_ADDRESS_DELETED,
                EventName::PROFILE_LICENSE_CREATED,
                EventName::PROFILE_LICENSE_UPDATED,
                EventName::PROFILE_LICENSE_DELETED
            ]);
    }
}
