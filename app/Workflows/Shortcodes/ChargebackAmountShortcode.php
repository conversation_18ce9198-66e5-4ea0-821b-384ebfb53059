<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class ChargebackAmountShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "chargeback-amount";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return !empty($payload->event->get('chargeback_amount'))
            ? '$' . $payload->event->get('chargeback_amount')
            : '';
    }

    public function getLabel(): string
    {
        return "Chargeback amount";
    }
}
