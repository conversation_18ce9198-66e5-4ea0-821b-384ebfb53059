<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class SMSFromShortcode extends WorkflowPipelineShortcode
{

    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'sms-from';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'SMS received from';
    }

    /**
     * @param WorkflowPayload $payload
     *
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $smsFrom = $payload->event->get('sms-from');

        return $smsFrom ? ($smsFrom['phone'] ?? 'Unknown') : 'Unknown';
    }
}
