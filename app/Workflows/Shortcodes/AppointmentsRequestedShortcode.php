<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentQuote;
use App\Workflows\WorkflowPayload;

class AppointmentsRequestedShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'appointments-requested';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Appointments Requested';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return EloquentQuote::query()
                ->where(EloquentQuote::QUOTE_ID, $payload->event->get('lead_id'))
                ->first()?->{EloquentQuote::BEST_TIME_TO_CALL_OTHER} ?? "No appointments specified";
    }
}
