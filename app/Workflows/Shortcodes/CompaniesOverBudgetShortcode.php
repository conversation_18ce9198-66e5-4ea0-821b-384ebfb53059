<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Repositories\Legacy\CompanyRepository;
use App\Workflows\WorkflowPayload;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class CompaniesOverBudgetShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
         return 'companies-over-budget-in-zipcode';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Companies Over Budget in Zipcode';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     * @throws BindingResolutionException
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $zipcode = $payload->event->get('zipcode');
        $zipcode = gettype($zipcode) === 'integer'
            // Zipcodes starting with 0 and sent as integers may need re-padding
            ? str_pad(strval($zipcode), 5, '00000', STR_PAD_LEFT)
            : $zipcode;
        if (gettype($zipcode) !== 'string' || strlen($zipcode) !== 5) {
            return "Zipcode must be a 5-digit number.";
        }

        $repository = app()->make(CompanyRepository::class);
        $overBudgetQuery = $repository->getOverBudgetSubQuery();
        $overBudgetCampaignIds = DB::query()->from($overBudgetQuery)->where('over_budget', 1)->pluck('id');

        $matchingCampaigns = LeadCampaign::query()
            ->whereHas(LeadCampaign::RELATION_LEAD_CAMPAIGN_LOCATIONS.".".LeadCampaignLocation::RELATION_LOCATION, fn(Builder $subQuery) =>
                $subQuery->where(Location::ZIP_CODE, $zipcode))
            ->whereIn(LeadCampaign::ID, $overBudgetCampaignIds)
            ->with(LeadCampaign::RELATION_COMPANY)
            ->get();

        return count($matchingCampaigns)
            ? $matchingCampaigns->map(function(LeadCampaign $campaign) {
                    $name = $campaign->{LeadCampaign::RELATION_COMPANY}?->{EloquentCompany::COMPANY_NAME} ?? null;
                    $id = $campaign->{LeadCampaign::RELATION_COMPANY}?->{EloquentCompany::ID} ?? null;
                    return $name && $id
                        ? "{$name} ({$id})"
                        : null;
                })
                ->filter()
                ->join(', ')
            : "No over budget Companies found for zip code {$zipcode}.";

    }

}
