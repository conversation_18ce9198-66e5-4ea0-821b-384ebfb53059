<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class AccountManagerNameShortcode extends WorkflowPipelineShortcode
{
    protected bool $isDeprecated = true;

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "account-manager";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $this->getCompany($payload)?->accountManager?->name ?? "Unknown Account Manager";
    }

    public function getLabel(): string
    {
        return "Account Manager Name";
    }
}
