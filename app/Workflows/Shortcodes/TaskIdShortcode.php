<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Workflows\WorkflowPayload;

class TaskIdShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "task-id";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->get('task_id') ?? "Unknown Task";
    }

    public function getLabel(): string
    {
        return "Task ID";
    }
}
