<?php

namespace App\Workflows\Shortcodes;

use App\Enums\Odin\RoofingConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;

class LeadRoofSizeShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'roof-size';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Roof Size';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        /** @var array|null $productData */
        $productData = $consumer
            ?->{Consumer::RELATION_CONSUMER_PRODUCT}()->latest()->first()
            ?->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}()->first()
            ?->{ConsumerProductData::FIELD_PAYLOAD};

        return is_array($productData) && key_exists(RoofingConfigurableFields::ROOF_ESTIMATE_MEDIAN->value, $productData)
            ? $productData[RoofingConfigurableFields::ROOF_ESTIMATE_MEDIAN->value]
            : 'Unknown ' . $this->getLabel();
    }
}
