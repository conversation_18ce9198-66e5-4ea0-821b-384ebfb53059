<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Repositories\Legacy\CompanyRepository;
use App\Workflows\WorkflowPayload;

class PaymentFailedCountShortcode extends WorkflowPipelineShortcode
{
    /**
     * @param CompanyRepository $companyRepository
     */
    public function __construct(protected CompanyRepository $companyRepository) {}

    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'payment-failed-count';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Payment Failed Count';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $companyId = Company::query()
            ->where(Company::FIELD_REFERENCE, $payload->get('company_reference') ?? '')
            ->firstOrFail()
            ->{Company::FIELD_ID};

        return $this->companyRepository->getCompanyFailedInvoiceCount($companyId);
    }
}
