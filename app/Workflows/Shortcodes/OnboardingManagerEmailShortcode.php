<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class OnboardingManagerEmailShortcode extends WorkflowPipelineShortcode
{

    #[\Override] public function getKey(): string
    {
        return 'onboarding-manager-email';
    }

    #[\Override] public function getLabel(): string
    {
        return 'Onboarding Manager Email';
    }

    #[\Override] protected function getValue(WorkflowPayload $payload): string
    {
        return $this->getCompany($payload)?->onboardingManager?->email ?? '';
    }
}
