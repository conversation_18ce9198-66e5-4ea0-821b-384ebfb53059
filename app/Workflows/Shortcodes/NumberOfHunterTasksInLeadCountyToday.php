<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentQuote;
use App\Models\TaskCategory;
use App\Repositories\LocationRepository;
use App\Repositories\TaskRepository;
use App\Workflows\WorkflowPayload;
use Illuminate\Contracts\Container\BindingResolutionException;

class NumberOfHunterTasksInLeadCountyToday extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'number-of-hunter-tasks-in-lead-county-today';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Number of Hunter Tasks in Lead County Today';
    }

    /**
     * @param WorkflowPayload $payload
     *
     * @return string
     * @throws BindingResolutionException
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        if (!$leadId) return 0;

        /** @var EloquentQuote $lead */
        $lead = EloquentQuote::query()->find($leadId);

        if (!$lead) return 0;

        /** @var LocationRepository $locationRepository */
        $locationRepository = app()->make(LocationRepository::class);

        $countyLocation = $locationRepository->getCountyFromZipcode($lead->address->zipcode);

        if (!$countyLocation) return 0;

        /** @var TaskCategory|null $taskCategory */
        $taskCategory = TaskCategory::query()->where(TaskCategory::NAME, \App\Enums\TaskCategory::CATEGORY_HUNTING)->first();

        if (!$taskCategory) return 0;

        /** @var TaskRepository $taskRepository */
        $taskRepository = app()->make(TaskRepository::class);

        return $taskRepository->getTasksForCountyLocation($countyLocation->id, now()->startOfDay(), $taskCategory->id);
    }
}
