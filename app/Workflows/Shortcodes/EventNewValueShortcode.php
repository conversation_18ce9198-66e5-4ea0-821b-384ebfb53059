<?php

namespace App\Workflows\Shortcodes;


use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Contracts\Workflows\ConditionalShortcodeContract;
use App\Enums\EventName;
use App\Workflows\WorkflowPayload;

class EventNewValueShortcode extends WorkflowPipelineShortcode implements ConditionalShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "new_value";
    }

    /**
     * <PERSON>le missing new_value keys, or events calling new_value when they haven't assigned a new_value key
     * @param string $eventName
     * @param string $missingNewKey
     * @return string
     */
    private function newValueKeyNotFound(string $eventName, string $missingNewKey = 'unknown_key'): string
    {
        $logLine = EventName::NEW_VALUE_KEY_NOT_ASSIGNED
            ? "ShortcodeError - [$eventName] - tried to use {new_value}, none assigned for event"
            : "ShortcodeError - [$eventName] - tried to use {new_value} to access key '$missingNewKey', key was not found on event";
        logger()->warning($logLine);
        return "'shortcode_not_found'";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $eventName = $payload->event->eventName;
        $newKeyName = EventName::from($eventName)->getNewValueKey();
        return $payload->event->get($newKeyName) ?? $this->newValueKeyNotFound($eventName, $newKeyName);
    }

    public function getLabel(): string
    {
        return "New Value";
    }

    /**
     * @inheritDoc
     */
    public function conditionalEventValidation(EventName $event): bool
    {
        return $event->getNewValueKey() !== EventName::NEW_VALUE_KEY_NOT_ASSIGNED;
    }
}
