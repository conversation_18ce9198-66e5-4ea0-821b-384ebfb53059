<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Models\User;
use App\Workflows\WorkflowPayload;

class TaskAssignedUserShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "task-assigned-user";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->has('task_assigned_user_id') ?
            (User::query()->find($payload->get('task_assigned_user_id'))?->name ?? "Unknown User") : "Unknown User";
    }

    public function getLabel(): string
    {
        return "Task Assigned User";
    }
}
