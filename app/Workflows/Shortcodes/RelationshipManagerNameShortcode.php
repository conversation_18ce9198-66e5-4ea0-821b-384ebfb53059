<?php

namespace App\Workflows\Shortcodes;

use App\Workflows\WorkflowPayload;

class RelationshipManagerNameShortcode extends BaseRelationshipManagerShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'relationship-manager-name';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return "Relationship Manager Name";
    }

    /**
     * @inheritDoc
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $this->getRelationshipManager($payload->event->get('company_reference', ''))
            ?->name
            ?? 'Unknown Relationship Manager';
    }
}