<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Odin\CompanyUser;
use App\Workflows\WorkflowPayload;

class InvoiceBillingContactNameShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "invoice-billing-contact-name";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $invoiceUuid = $payload->event->get('invoice_uuid', '');

        if (empty($invoiceUuid)) {
            return '';
        }

        /** @var BillingProfile $billingProfile */
        $billingProfile = Invoice::findByUuid($invoiceUuid)?->{Invoice::RELATION_BILLING_PROFILE};

        return $billingProfile?->{BillingProfile::RELATION_CONTACT}?->completeName() ?? '';
    }

    public function getLabel(): string
    {
        return "Invoice Billing Contact Name";
    }
}
