<?php

namespace App\Workflows\Shortcodes;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use Illuminate\Database\Eloquent\Builder;

class LeadSoldToCountShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'sold-to-count';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Sold To';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT, function(Builder $query) use($leadId){
                return $query->whereHas(ConsumerProduct::RELATION_CONSUMER, function(Builder $query) use($leadId){
                   return $query->where(Consumer::FIELD_LEGACY_ID, $leadId);
                });
            })
            ->count() ?? 0;
    }
}
