<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Str;

class RecipientNameShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'recipient-name';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Recipient Full Name';
    }

    /**
     * Handles returning name of a company contact that can ideally be used for any purpose/communication.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $companyContact = $payload->event->get('company_contact');

        return $companyContact
            ? Str::title($companyContact->{CompanyUser::FIELD_FIRST_NAME} .' '. $companyContact->{CompanyUser::FIELD_LAST_NAME})
            : 'Unknown ' . $this->getLabel();
    }
}
