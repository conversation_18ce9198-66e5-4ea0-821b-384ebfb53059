<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Workflows\WorkflowPayload;

class BestTimeToCallShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'best-time-to-call';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Best Time To Call';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        /** @var array|null $productData */
        $productData = $consumer
            ?->{Consumer::RELATION_CONSUMER_PRODUCT}->latest()->first()
            ?->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->first()
            ?->{ConsumerProductData::FIELD_PAYLOAD};

        return is_array($productData) && key_exists(GlobalConfigurableFields::BEST_TIME_TO_CALL->value, $productData)
            ? $productData[GlobalConfigurableFields::BEST_TIME_TO_CALL->value]
            : 'Unknown ' . $this->getLabel();
    }
}
