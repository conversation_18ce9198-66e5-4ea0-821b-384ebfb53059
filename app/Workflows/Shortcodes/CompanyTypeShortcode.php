<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Workflows\WorkflowPayload;

class CompanyTypeShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "company-type";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return ucwords(Company::query()
                              ->where(Company::FIELD_REFERENCE, $payload->event->get('company_reference'))
                              ->first()?->services()->first()?->{IndustryService::FIELD_NAME} ?? "Installer");
    }

    public function getLabel(): string
    {
        return "Company Type";
    }
}
