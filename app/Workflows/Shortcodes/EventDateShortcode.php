<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class EventDateShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'event-date';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Event Date';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return date("Y-m-d H:i:s", strtotime($payload->event->get('publish_time'))) ?? 'Unknown date';
    }
}
