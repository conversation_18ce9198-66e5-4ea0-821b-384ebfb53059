<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Legacy\LeadCampaign;
use App\Workflows\WorkflowPayload;

class CampaignNameShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "campaign-name";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        if ($payload->event->get('future_campaign') === true)
            return $this->cleanAutoGenCampaignName(
                $this->getFutureCampaignName($payload)
            );

        return $this->cleanAutoGenCampaignName(
            LeadCampaign::query()
              ->where(LeadCampaign::ID, $payload->event->get('campaign_id'))
              ->first()?->{LeadCampaign::NAME} ?? "Unknown Campaign"
        );
    }

    public function getLabel(): string
    {
        return "Campaign Name";
    }

    /**
     * @param WorkflowPayload $payload
     *
     * @return string
     */
    protected function getFutureCampaignName(WorkflowPayload $payload): string
    {
        return CompanyCampaign::query()->find($payload->event->get('campaign_id'))?->{CompanyCampaign::FIELD_NAME} ?? 'Unknown Campaign';

    }

    /**
     * Remove the auto-gen prefix from campaign names sent from Legacy pubsub
     *
     * @param string $name
     * @return string
     */
    private function cleanAutoGenCampaignName(string $name): string
    {
        $regex = "/^" . LegacyModule::LEAD_CAMPAIGN_PREFIX . "/";

        return preg_replace($regex, '', $name);
    }
}
