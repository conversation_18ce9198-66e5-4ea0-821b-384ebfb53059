<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Billing\Invoice;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Number;

class InvoiceTotal extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "invoice-total";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $invoiceUuid = $payload->event->get('invoice_uuid', '');

        if (empty($invoiceUuid)) {
            return '';
        }

        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($invoiceUuid);

        return $invoice ? Number::currency($invoice->getInvoiceItemsTotal() / 100) : '';
    }

    public function getLabel(): string
    {
        return "Invoice total";
    }
}
