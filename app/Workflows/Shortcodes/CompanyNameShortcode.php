<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Workflows\WorkflowPayload;

class CompanyNameShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "company-name";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return Company::query()
                              ->where(Company::FIELD_REFERENCE, $payload->event->get('company_reference'))
                              ->first()?->{Company::FIELD_NAME} ?? "Unknown Company";
    }

    public function getLabel(): string
    {
        return "Company Name";
    }
}
