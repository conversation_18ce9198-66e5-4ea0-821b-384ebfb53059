<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\LeadCampaign;
use App\Workflows\WorkflowPayload;

class CampaignIdShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "campaign-id";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        if ($payload->event->get('future_campaign') === true)
            return $payload->event->get('campaign_id') ?? "Unknown Id";

        return LeadCampaign::query()
                              ->where(LeadCampaign::ID, $payload->event->get('campaign_id'))
                              ->first()?->{LeadCampaign::ID} ?? "Unknown Id";
    }

    public function getLabel(): string
    {
        return "Campaign ID";
    }
}
