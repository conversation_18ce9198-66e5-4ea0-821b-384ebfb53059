<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowShortcodeServiceAbstract;
use Exception;

class WorkflowShortcodeServiceFactory
{
    const WORKFLOW_DRIVER = 'workflow';
    const DUMMY_DRIVER = 'dummy';

    /**
     * @param string $driver
     * @return WorkflowShortcodeServiceAbstract
     * @throws Exception
     */
    public static function makeService(string $driver): WorkflowShortcodeServiceAbstract
    {
        return match($driver) {
            self::WORKFLOW_DRIVER => new WorkflowShortcodeService(),
            self::DUMMY_DRIVER => new DummyWorkflowShortcodeService(),
            default => throw new Exception(__METHOD__.": Invalid driver")
        };
    }
}
