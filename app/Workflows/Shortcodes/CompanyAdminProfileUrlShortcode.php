<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class CompanyAdminProfileUrlShortcode extends WorkflowPipelineShortcode
{

    #[\Override] public function getKey(): string
    {
        return 'company-admin-profile-url';
    }

    #[\Override] public function getLabel(): string
    {
        return 'Company Admin Profile Url';
    }

    #[\Override] protected function getValue(WorkflowPayload $payload): string
    {
        if (!$this->getCompany($payload)) {
            return '';
        }

        $url = $this->getCompany($payload)->getAdminProfileUrl();

        return '<a href="' . $url . '">' . $url . '</a>';
    }
}
