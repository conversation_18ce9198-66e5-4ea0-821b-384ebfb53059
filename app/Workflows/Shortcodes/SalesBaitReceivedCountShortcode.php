<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Models\SalesBaitLead;
use App\Workflows\WorkflowPayload;

class SalesBaitReceivedCountShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "sales-baits-received-count";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $legacyCompanyId = Company::query()
            ->where(Company::FIELD_REFERENCE, $payload->event->get('company_reference') ?? '')
            ->firstOrFail()
            ->{Company::FIELD_LEGACY_ID};

        return SalesBaitLead::query()
                            ->where(SalesBaitLead::FIELD_COMPANY_ID, $legacyCompanyId)
                            ->groupBy(SalesBaitLead::FIELD_LEAD_ID)
                            ->count();
    }

    public function getLabel(): string
    {
        return "Number Of Sales Bait Received";
    }
}
