<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\AccountManager;
use App\Models\AccountManagerClient;
use App\Models\Odin\Company;
use App\Workflows\WorkflowPayload;

class AccountManagerTypeShortcode extends WorkflowPipelineShortcode
{
    protected bool $isDeprecated = true;

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "account-manager-type";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        /** @var AccountManager $accountManager */
        $accountManager = AccountManager::query()
                                        ->whereHas(AccountManager::RELATION_CLIENTS,
                                            fn($query) =>
                                                $query->where(AccountManagerClient::FIELD_COMPANY_REFERENCE, $payload->event->get('company_reference') ?? '')
                                                      ->where(AccountManagerClient::FIELD_STATUS, AccountManagerClient::STATUS_ACTIVE)
                                        )->first();

        return $accountManager ? AccountManager::TYPES[$accountManager->type] : "none";
    }

    public function getLabel(): string
    {
        return "Account Manager Type";
    }
}
