<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Repositories\CompanyContactRepository;
use App\Repositories\Legacy\LegacyUserRepository;
use App\Workflows\WorkflowPayload;

class InitiatorShortcode extends WorkflowPipelineShortcode
{
    const string PAYLOAD_KEY = 'initiator_id';

    public function __construct(protected LegacyUserRepository $legacyUserRepository, protected CompanyContactRepository $companyContactRepository) {}

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "initiator";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $user = $this->legacyUserRepository->getLegacyUser($payload->event->get(self::PAYLOAD_KEY) ?? 0);

        if(!empty($user)) {
            return "{$user['name']} ({$user['company']})";
        }
        else {
            $relationId = $payload->event->get('relation_id');
            $relationType = $payload->event->get('relation_type');

            if($relationType === 'user') {
                $user = $this->legacyUserRepository->getLegacyUser($relationId ?? 0);

                return "{$user['name']} ({$user['company']})";
            }
            else if($relationType === 'contact') {
                $contact = $this->companyContactRepository->getLegacyContact($relationId ?? 0);

                return "{$contact['name']} ({$contact['company']})";
            }
        }

        return "Unknown User";
    }

    public function getLabel(): string
    {
        return "Initiator";
    }
}
