<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class SMSForShortcode extends WorkflowPipelineShortcode
{

    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'sms-for';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'SMS sent to';
    }

    /**
     * @param WorkflowPayload $payload
     *
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->event->get('sms-for') ?? 'Unknown';
    }
}
