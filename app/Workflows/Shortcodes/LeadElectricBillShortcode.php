<?php

namespace App\Workflows\Shortcodes;

use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;

class LeadElectricBillShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'electric-bill';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Electric Bill';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        /** @var array|null $productData */
        $productData = $consumer
            ?->{Consumer::RELATION_CONSUMER_PRODUCT}()->latest()->first()
            ?->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}()->first()
            ?->{ConsumerProductData::FIELD_PAYLOAD};

        return is_array($productData) && key_exists(SolarConfigurableFields::ELECTRIC_COST->value, $productData)
            ? $productData[SolarConfigurableFields::ELECTRIC_COST->value]
            : 'Unknown ' . $this->getLabel();
    }
}
