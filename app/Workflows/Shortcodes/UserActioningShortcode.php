<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Models\User;
use App\Workflows\WorkflowPayload;

class UserActioningShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "user-actioning";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        /** @var User $user */
        $user = User::query()->find($payload->get('user_actioning', 0));

        return $user?->name ?? "Unknown Assignee";
    }

    public function getLabel(): string
    {
        return "User Actioning (use after task)";
    }
}
