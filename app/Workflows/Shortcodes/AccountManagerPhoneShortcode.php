<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Phone;
use App\Workflows\WorkflowPayload;

class AccountManagerPhoneShortcode extends WorkflowPipelineShortcode
{
    protected bool $isDeprecated = true;

    /**
     * @return string
     */
    public function getKey(): string
    {
        return "account-manager-phone";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        /** @var Phone|null $phone */
        $phone = $this->getCompany($payload)
            ?->accountManager
            ?->primaryPhone();

        return $phone && $phone->{Phone::FIELD_STATUS} === Phone::STATUS_ACTIVE
            ? $phone->{Phone::FIELD_PHONE}
            : 'Unknown ' . $this->getLabel();
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return "Account Manager Phone";
    }
}
