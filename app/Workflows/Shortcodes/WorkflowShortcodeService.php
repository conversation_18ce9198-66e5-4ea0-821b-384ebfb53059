<?php

namespace App\Workflows\Shortcodes;

use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowShortcodeServiceAbstract;
use Illuminate\Pipeline\Pipeline;

class WorkflowShortcodeService extends WorkflowShortcodeServiceAbstract
{
    /**
     * @param string $message
     * @param WorkflowPayload|null $payload
     * @return string
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function handle(string $message, ?WorkflowPayload $payload = null): string
    {
        /** @var Pipeline $pipeline */
        $pipeline = app()->make(Pipeline::class);

        $pipeline->send(compact('message', 'payload'))
                        ->through(self::WORKFLOW_SHORTCODES)
                        ->via("handle")
                        ->then(function($result) use (&$message) { $message = $result['message']; });

        return $message;
    }
}
