<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Workflows\WorkflowPayload;

class InvoiceIssueDateShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "invoice-issue-date";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $invoiceUuid = $payload->event->get('invoice_uuid');

        if (empty($invoiceUuid)) {
            return '';
        }

        $invoice = Invoice::findByUuid($invoiceUuid);

        return $invoice
            ? CarbonHelper::parse($invoice->{Invoice::FIELD_ISSUE_AT})->toFormat()
            : '';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return "Invoice Issue Date";
    }
}
