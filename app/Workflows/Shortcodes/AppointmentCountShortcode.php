<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAppointment;
use App\Workflows\WorkflowPayload;

class AppointmentCountShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'appointment-count';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Total Appointments';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        return $consumer?->consumerProducts()->latest()->first()?->appointments()->count() ?? '0';
    }
}
