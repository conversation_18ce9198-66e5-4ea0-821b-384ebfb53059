<?php

namespace App\Workflows\Shortcodes;

use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;

class ConsumerFirstNameShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'consumer-first-name';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Consumer First Name';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $consumerReference = $payload->event->get('consumer_reference');

        if ($consumerReference) {
            $consumer = Consumer::query()->where(Consumer::FIELD_REFERENCE, $consumerReference)->first();
        } else {
            $leadId = $payload->event->get('lead_id');

            /** @var Consumer|null $consumer */
            $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();
        }

        return $consumer ? $consumer->first_name : 'Unknown ' . $this->getLabel();
    }
}
