<?php

namespace App\Workflows\Shortcodes;

use App\Exceptions\ShortcodeModelNotFoundException;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Services\Consumer\ConsumerService;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use Illuminate\Contracts\Container\BindingResolutionException;

class ConsumerTopCompanyShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'consumer-top-company';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Consumer Top Company';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     * @throws BindingResolutionException
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $consumerReference = $payload->event->get('consumer_reference');
        if (empty($consumerReference)) {
            throw new ShortcodeModelNotFoundException("No Consumer Reference Given");
        }

        $consumer = Consumer::query()->where(Consumer::FIELD_REFERENCE, $consumerReference)->first();

        if (empty($consumer)) {
            throw new ShortcodeModelNotFoundException("No Consumer for reference: $consumerReference");
        }

        /** @var ConsumerService $consumerService */
        $consumerService = app()->make(ConsumerService::class);

        $topCompany = $consumerService->getTopCompaniesAndCampaignsForConsumer(
            $consumer,
            limit: 1
        )->first();

        if (empty($topCompany)) {
            throw new ShortcodeModelNotFoundException("Could not identify top company for consumer reference: $consumerReference");
        }

        return match (get_class($topCompany)) {
            CompanyCampaign::class => $topCompany->company->name,
            Company::class         => $topCompany->name,
            default                => throw new ShortcodeModelNotFoundException("Unknown Top Company Model Returned")
        };
    }
}
