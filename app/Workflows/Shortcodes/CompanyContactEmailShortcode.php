<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Workflows\WorkflowPayload;

class CompanyContactEmailShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'company-contact-email';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Company Contact Email';
    }

    /**
     * Handles returning email of a company contact that can ideally be used for any purpose/communication.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $companyReference = $payload->event->get('company_reference');

        /** @var Company|null $company */
        $company = Company::query()
           ->where(Company::FIELD_REFERENCE, $companyReference)
           ->first();

        /** @var CompanyUser|null $companyContact */
        $companyContact = CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->where(CompanyUser::FIELD_IS_CONTACT, CompanyUser::USER_IS_CONTACT)
            ->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE)
            ->whereNotNull(CompanyUser::FIELD_EMAIL)
            ->first();

        return $companyContact
            ? $companyContact->{CompanyUser::FIELD_EMAIL}
            : 'Unknown ' . $this->getLabel();
    }
}
