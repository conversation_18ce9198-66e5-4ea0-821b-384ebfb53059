<?php

namespace App\Workflows\Shortcodes;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductData;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;

class LeadAppointmentTimeShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'appointment-time';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Appointment Time';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        /** @var array|null $productData */
        $productData = $consumer
            ?->{Consumer::RELATION_CONSUMER_PRODUCT}->latest()->first()
            ?->{ConsumerProduct::RELATION_CONSUMER_PRODUCT_DATA}->first()
            ?->{ConsumerProductData::FIELD_PAYLOAD};

        return is_array($productData) && key_exists(GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value, $productData)
            ? $productData[GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value]
            : 'Unknown ' . $this->getLabel();
    }
}
