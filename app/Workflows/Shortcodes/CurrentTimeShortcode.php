<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Workflows\WorkflowPayload;
use Carbon\Carbon;

class CurrentTimeShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "current-time";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return Carbon::now()->timezone("America/Los_Angeles")->toDateTimeString('minute') . " PST";
    }

    public function getLabel(): string
    {
        return "Current Time";
    }
}
