<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowShortcodeServiceAbstract;
use App\Workflows\WorkflowPayload;
use Illuminate\Support\Str;

class DummyWorkflowShortcodeService extends WorkflowShortcodeServiceAbstract
{
    /**
     * @param string $message
     * @param WorkflowPayload|null $payload
     * @return string
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function handle(string $message, ?WorkflowPayload $payload = null): string
    {
        foreach(self::WORKFLOW_SHORTCODES as $shortcode) {
            $class = app()->make($shortcode);

            if(Str::contains($message, "{{$class->getKey()}}")) {
                $message = $class->replace($message, $class->getLabel());
            }
        }

        return $message;
    }
}
