<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class AccountManagerEmailShortcode extends WorkflowPipelineShortcode
{

    #[\Override] public function getKey(): string
    {
        return 'account-manager-email';
    }

    #[\Override] public function getLabel(): string
    {
        return 'Account Manager Email';
    }

    #[\Override] protected function getValue(WorkflowPayload $payload): string
    {
        return $this->getCompany($payload)?->accountManager?->email ?? '';
    }
}
