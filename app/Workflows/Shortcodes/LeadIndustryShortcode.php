<?php

namespace App\Workflows\Shortcodes;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;

class LeadIndustryShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'lead-industry';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Lead Industry';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        /** @var Industry|null $industry */
        $industry = $consumer
            ?->{Consumer::RELATION_CONSUMER_PRODUCT}()->latest()->first()
            ?->{ConsumerProduct::RELATION_SERVICE_PRODUCT}()->first()
            ?->{ServiceProduct::RELATION_SERVICE}()->first()
            ?->{IndustryService::RELATION_INDUSTRY}()->first();

        return $industry
            ? $industry->{Industry::FIELD_NAME}
            : 'Unknown ' . $this->getLabel();
    }
}
