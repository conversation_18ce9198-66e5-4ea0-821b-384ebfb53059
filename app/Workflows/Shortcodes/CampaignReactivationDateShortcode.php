<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;
use Carbon\Carbon;

class CampaignReactivationDateShortcode extends WorkflowPipelineShortcode
{

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'reactivation-date';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Campaign Reactivation Date';
    }

    /**
     * @inheritDoc
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $reactiveTimestamp = $payload->event->get('reactivation_date');

        if ($reactiveTimestamp) {
            return Carbon::createFromTimestamp($reactiveTimestamp)->toDateTimeString();
        }

        return 'Unknown';
    }
}
