<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class CampaignStatusTypeShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'status-type';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Campaign Status Type';
    }

    /**
     * @inheritDoc
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->event->has('status_type') ? $payload->event->get('status_type') : 'Unknown';
    }
}
