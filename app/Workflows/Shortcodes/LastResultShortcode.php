<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Workflows\WorkflowPayload;

class LastResultShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "last-result";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->get('last_result_value');
    }

    public function getLabel(): string
    {
        return "Last Result";
    }
}
