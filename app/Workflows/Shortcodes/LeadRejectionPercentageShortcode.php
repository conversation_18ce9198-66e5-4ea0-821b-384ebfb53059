<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Company;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Workflows\WorkflowPayload;

class LeadRejectionPercentageShortcode extends WorkflowPipelineShortcode
{
    public function __construct(protected ComputedRejectionStatisticRepository $rejectionRepository) {}

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "rejection-percentage";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        /** @var Company $company */
        $company = Company::query()->where(Company::FIELD_REFERENCE, $payload->event->get('company_reference'))->first();
        if(!$company)
            return "Unknown Rejection Percentage";

        $rejection = $this->rejectionRepository->getCompanyLeadRejectionPercentage($company);

        return number_format($rejection, 2) . '%';
    }

    public function getLabel(): string
    {
        return "Lead Rejection Percentage";
    }
}
