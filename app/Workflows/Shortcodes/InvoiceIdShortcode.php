<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Billing\Invoice;
use App\Workflows\WorkflowPayload;

class InvoiceIdShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "invoice-id";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $invoiceId = $payload->event->get('invoice_id');

        if (!empty($invoiceId)) {
            return $invoiceId;
        }

        $invoiceUuid = $payload->event->get('invoice_uuid', '');

        if (empty($invoiceUuid)) {
            return '';
        }

        return Invoice::findByUuid($invoiceUuid)?->{Invoice::FIELD_ID} ?? '';
    }

    public function getLabel(): string
    {
        return "Invoice id";
    }
}
