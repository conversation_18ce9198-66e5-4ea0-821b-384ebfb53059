<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Legacy\EloquentCompany;
use App\Models\RunningWorkflow;
use App\Models\Sales\Task;
use App\Models\User;
use App\Workflows\WorkflowPayload;

class TaskNameShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "task-name";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->has('task_id') ?
            (Task::query()->find($payload->get('task_id'))?->subject ?? "Unknown Task") : "Unknown Task";
    }

    public function getLabel(): string
    {
        return "Task Name";
    }
}
