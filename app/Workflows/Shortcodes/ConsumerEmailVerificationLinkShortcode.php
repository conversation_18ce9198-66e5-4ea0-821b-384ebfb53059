<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Consumer;
use App\Services\ConsumerService;
use App\Workflows\WorkflowPayload;

class ConsumerEmailVerificationLinkShortcode extends WorkflowPipelineShortcode
{

    #[\Override] public function getKey(): string
    {
        return 'consumer-email-verification-link';
    }

    #[\Override] public function getLabel(): string
    {
        return 'Consumer Email Verification Link';
    }

    #[\Override] protected function getValue(WorkflowPayload $payload): string
    {
        $reference = $payload->event->get('consumer_reference');

        if (!$reference) {
            return '';
        }

        $consumer = Consumer::query()->where(Consumer::FIELD_REFERENCE, $reference)->first();

        if (!$consumer) {
            return '';
        }

        /** @var ConsumerService $consumerService */
        $consumerService = app(ConsumerService::class);

        return $consumerService->getEmailVerificationLink($consumer);
    }
}
