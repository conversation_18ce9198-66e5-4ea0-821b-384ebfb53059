<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Contracts\Workflows\ConditionalShortcodeContract;
use App\Enums\EventName;
use App\Workflows\WorkflowPayload;

class EventOldValueShortcode extends WorkflowPipelineShortcode implements ConditionalShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "old_value";
    }

    /**
     * <PERSON>le missing old_value keys, or events calling old_value when they haven't assigned an old_value key
     * @param string $eventName
     * @param string $missingOldKey
     * @return string
     */
    private function oldValueKeyNotFound(string $eventName, string $missingOldKey = 'unknown_key'): string
    {
        $logLine = EventName::OLD_VALUE_KEY_NOT_ASSIGNED
            ? "ShortcodeError - [$eventName] - tried to use {old_value}, none assigned for event"
            : "ShortcodeError - [$eventName] - tried to use {old_value} to access key '$missingOldKey', key was not found on event";
        logger()->warning($logLine);
        return "'shortcode_not_found'";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $eventName = $payload->event->eventName;
        $oldKeyName = EventName::from($eventName)->getOldValueKey();
        return $payload->event->get($oldKeyName) ?? $this->oldValueKeyNotFound($eventName, $oldKeyName);
    }

    public function getLabel(): string
    {
        return "Old Value";
    }

    /**
     * @inheritDoc
     */
    public function conditionalEventValidation(EventName $event): bool
    {
        return $event->getOldValueKey() !== EventName::OLD_VALUE_KEY_NOT_ASSIGNED;
    }
}
