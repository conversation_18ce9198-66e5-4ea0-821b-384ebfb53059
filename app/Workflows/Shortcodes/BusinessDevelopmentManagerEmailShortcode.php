<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class BusinessDevelopmentManagerEmailShortcode extends WorkflowPipelineShortcode
{

    #[\Override] public function getKey(): string
    {
        return 'business-development-manager-email';
    }

    #[\Override] public function getLabel(): string
    {
        return 'Business Development Manager Email';
    }

    #[\Override] protected function getValue(WorkflowPayload $payload): string
    {
        return $this->getCompany($payload)?->businessDevelopmentManager?->email ?? '';
    }
}
