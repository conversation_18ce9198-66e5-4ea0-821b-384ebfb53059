<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class UnsubscribeShortcode extends WorkflowPipelineShortcode
{

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "unsubscribe-url";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->event->get('unsubscribe_url');
    }

    public function getLabel(): string
    {
        return "Unsubscribe URL";
    }

}
