<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Workflows\WorkflowPayload;

class LeadStateShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'lead-state';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Lead State';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        /** @var Address|null $address */
        $address = $consumer
            ?->{Consumer::RELATION_CONSUMER_PRODUCT}()->latest()->first()
            ?->{ConsumerProduct::RELATION_ADDRESS}()->first();

        return $address
            ? $address->{Address::FIELD_STATE}
            : 'Unknown ' . $this->getLabel();
    }
}
