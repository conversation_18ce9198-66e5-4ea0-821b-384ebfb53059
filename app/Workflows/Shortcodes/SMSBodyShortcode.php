<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class SMSBodyShortcode extends WorkflowPipelineShortcode
{

    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'sms-body';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'SMS body';
    }

    /**
     * @param WorkflowPayload $payload
     *
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->event->get('sms-body') ?? 'Empty message';
    }
}
