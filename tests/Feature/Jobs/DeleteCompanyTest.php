<?php

namespace Tests\Feature\Jobs;

use App\Jobs\DeleteCompany;
use App\Models\Odin\Company;
use App\Services\Companies\Delete\CompanyDeleteService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Illuminate\Support\Facades\Log;

class DeleteCompanyTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    /**
     * @link https://github.com/laravel/framework/issues/19450
     */
    #[Test]
    public function it_deletes_a_company(): void
    {
        $mock = Mockery::mock(CompanyDeleteService::class)->makePartial();
        $mock->shouldReceive('delete')->andReturn(true);
        $this->app->offsetSet(CompanyDeleteService::class, function () use ($mock) {
            return $mock;
        });

        Log::shouldReceive('error')->never();

        DeleteCompany::dispatch(Company::factory()->createQuietly()->id);
    }
}
