<?php

namespace Tests\Feature\Jobs\Billing;

use App\Jobs\Billing\UpdateInvoiceSnapshotManager;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\User;
use App\Services\Billing\BillingLogService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UpdateInvoiceSnapshotManagerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mock(BillingLogService::class);
    }

    #[Test]
    public function it_does_not_update_the_company_invoice_snapshots_if_all_ids_are_null(): void
    {
        $invoiceSnapshot = InvoiceSnapshot::factory()->createQuietly();

        UpdateInvoiceSnapshotManager::dispatch($invoiceSnapshot->company->id);

        $this->assertDatabaseHas('invoice_snapshots', [
            'account_manager_id' => null,
            'business_development_manager_id' => null,
            'success_manager_id' => null,
        ]);
    }

    #[Test]
    public function it_updates_the_company_invoice_snapshots_if_at_least_one_id_is_not_null(): void
    {
        $invoiceSnapshot = InvoiceSnapshot::factory()->createQuietly();

        $invoiceSnapshot->company->assign(User::factory()->create())->as(Role::findOrCreate('account-manager')->name);

        UpdateInvoiceSnapshotManager::dispatch($invoiceSnapshot->company->id);

        $invoiceSnapshot->refresh();

        $this->assertNotNull($invoiceSnapshot->account_manager_id);
        $this->assertNull($invoiceSnapshot->business_development_manager_id);
        $this->assertNull($invoiceSnapshot->success_manager_id);

        $this->assertDatabaseHas('invoice_snapshots', [
            'account_manager_id' => $invoiceSnapshot->refresh()->account_manager_id,
            'business_development_manager_id' => $invoiceSnapshot->business_development_manager_id,
            'success_manager_id' => $invoiceSnapshot->refresh()->success_manager_id,
        ]);
    }

    #[Test]
    public function it_updates_the_company_invoice_snapshots_for_all_manager_ids(): void
    {
        $invoiceSnapshot = InvoiceSnapshot::factory()->createQuietly();

        $invoiceSnapshot->company->assign(User::factory()->create())->as(Role::findOrCreate('account-manager')->name);

        $invoiceSnapshot->company->assign(User::factory()->create())->as(Role::findOrCreate('customer-success-manager')->name);

        $invoiceSnapshot->company->assign(User::factory()->create())->as(Role::findOrCreate('business-development-manager')->name);

        UpdateInvoiceSnapshotManager::dispatch($invoiceSnapshot->company->id);

        $invoiceSnapshot->refresh();

        $this->assertNotNull($invoiceSnapshot->account_manager_id);
        $this->assertNotNull($invoiceSnapshot->business_development_manager_id);
        $this->assertNotNull($invoiceSnapshot->success_manager_id);

        $this->assertDatabaseHas('invoice_snapshots', [
            'account_manager_id' => $invoiceSnapshot->refresh()->account_manager_id,
            'business_development_manager_id' => $invoiceSnapshot->business_development_manager_id,
            'success_manager_id' => $invoiceSnapshot->refresh()->success_manager_id,
        ]);
    }
}
