<?php

namespace Tests\Feature\Jobs\SalesIntel;

use App\Jobs\SalesIntel\ImportCompanies;
use App\Services\SalesIntel\SalesIntel;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ImportCompaniesTest extends TestCase
{
    #[Test, DataProvider('filterProvider')]
    public function it_calls_the_import_companies_function_on_the_sales_intel_api($filter, $value)
    {
        $this->mock(SalesIntel::class)
            ->shouldReceive('importCompanies')
            ->with($filter, $value)
            ->once();

        ImportCompanies::dispatch($filter, $value);
    }

    #[Test, DataProvider('filterProvider')]
    public function it_calls_the_import_companies_function_on_the_sales_intel_api_with_naics_codes($filter, $value)
    {
        $naics = [
            '111',
            '222',
            '333',
        ];

        $this->mock(SalesIntel::class)
            ->shouldReceive('importCompanies')
            ->with($filter, $value, $naics)
            ->once();

        ImportCompanies::dispatch($filter, $value, $naics);
    }

    public static function filterProvider()
    {
        return [
            'State' => [
                'state',
                'NJ',
            ],
            'Zipcode' => [
                'zipcode',
                '07001',
            ],
            'Domain' => [
                'domain',
                'solarreviews.com',
            ],
            'Specialty' => [
                'specialty',
                'leads',
            ],
        ];
    }
}
