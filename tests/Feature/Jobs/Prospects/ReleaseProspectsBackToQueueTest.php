<?php

namespace Tests\Feature\Jobs\Prospects;

use App\Enums\Prospects\ProspectStatus;
use App\Jobs\Prospects\ReleaseProspectsBackToQueue;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Queue;
use Tests\TestCase;

class ReleaseProspectsBackToQueueTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_dispatches_onto_the_queue()
    {
        Queue::fake();

        ReleaseProspectsBackToQueue::dispatch();

        Queue::assertPushed(ReleaseProspectsBackToQueue::class);
    }

    #[Test]
    public function a_prospect_is_released_back_to_the_queue_after_a_week()
    {
        $user = User::factory()->create();

        $prospect = NewBuyerProspect::factory()->create([
            'user_id' => $user->id,
            'user_assigned_at' => now()->subWeek(),
            'status' => ProspectStatus::ACTIVE,
        ]);

        ReleaseProspectsBackToQueue::dispatch();

        $prospect->refresh();

        $this->assertEquals(0, $prospect->user_id);
        $this->assertNull($prospect->user_assigned_at);
        $this->assertEquals(ProspectStatus::INITIAL->value, $prospect->status);
    }

    #[Test]
    public function a_prospect_is_not_released_back_to_the_queue_before_a_week()
    {
        $user = User::factory()->create();

        $prospect = NewBuyerProspect::factory()->create([
            'user_id' => $user->id,
            'user_assigned_at' => now(),
        ]);

        ReleaseProspectsBackToQueue::dispatch();

        $prospect->refresh();

        $this->assertEquals($user->id, $prospect->user_id);
        $this->assertNotNull($prospect->user_assigned_at);
    }

    #[Test]
    public function a_prospect_is_ignored_if_the_assigned_at_column_is_null()
    {
        $user = User::factory()->create();

        $prospect = NewBuyerProspect::factory()->create([
            'user_id' => $user->id,
            'user_assigned_at' => null,
        ]);

        ReleaseProspectsBackToQueue::dispatch();

        $prospect->refresh();

        $this->assertEquals($user->id, $prospect->user_id);
    }

    #[Test]
    public function a_prospect_is_ignored_if_their_status_is_not_active()
    {
        $user = User::factory()->create();

        $prospect = NewBuyerProspect::factory()->create([
            'user_id' => $user->id,
            'user_assigned_at' => now()->subMonth(),
            'status' => ProspectStatus::CLOSED,
        ]);

        ReleaseProspectsBackToQueue::dispatch();

        $prospect->refresh();

        $this->assertEquals($user->id, $prospect->user_id);
        $this->assertEquals(ProspectStatus::CLOSED->value, $prospect->status);
    }
}
