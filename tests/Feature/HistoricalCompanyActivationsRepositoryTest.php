<?php

namespace Tests\Feature;

use App\Enums\CompanyConsolidatedStatus;
use App\Jobs\RecordHistoricalCompanyActivations;
use App\Models\HistoricalCompanyActivation;
use App\Models\Odin\Company;
use App\Models\Odin\HistoricalCompanyStatuses;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductAssignment;
use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class HistoricalCompanyActivationsRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Model::withoutEvents(function () {
            IndustryService::factory()->create();

            CarbonImmutable::setTestNow(CarbonImmutable::createFromFormat('Y-m-d', '2024-01-30', 'UTC'));

            HistoricalCompanyStatuses::factory(5)->create();

            CarbonImmutable::setTestNow(CarbonImmutable::createFromFormat('Y-m-d', '2023-12-30', 'UTC'));

            HistoricalCompanyStatuses::factory(5)->create();

            CarbonImmutable::setTestNow(CarbonImmutable::createFromFormat('Y-m-d', '2024-01-30', 'UTC'));

            $companies = Company::all()->toArray();

            ProductAssignment::factory(30)
                ->sequence(fn (Sequence $s) => [ProductAssignment::FIELD_COMPANY_ID => $companies[$s->index % 10]['id']])
                ->create();
        });
    }

    #[Test]
    public function successfully_store_historical_company_activations(): void
    {
        $this->markTestSkipped('Test currently flaky - should be revisited.');

        $activeWithin90Days = [];
        $currentStatuses = [];

        foreach ([2023, 2024] as $year) {
            $historicalCompanyStatuses = HistoricalCompanyStatuses::query()
                ->with([
                    HistoricalCompanyStatuses::RELATION_COMPANY => function ($with) {
                        $with->select([
                            Company::TABLE.'.'.Company::FIELD_ID,
                            Company::TABLE.'.'.Company::FIELD_CONSOLIDATED_STATUS,
                        ]);
                    },
                    HistoricalCompanyStatuses::RELATION_PRODUCT_ASSIGNMENTS => function ($with) {
                        $with
                            ->selectRaw(implode(',', [
                                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID,
                                'COUNT('.ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID.') AS count',
                                'MIN('.ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT.') AS first_purchase_date',
                            ]))
                            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CHARGEABLE, true)
                            ->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_DELIVERED, true);
                    },
                ])
                ->where(HistoricalCompanyStatuses::FIELD_YEAR, $year)
                ->get();

            foreach ($historicalCompanyStatuses as $companyData) {
                $companyId = $companyData->{HistoricalCompanyStatuses::FIELD_COMPANY_ID};

                $companyStatus = $companyData
                    ->{HistoricalCompanyStatuses::RELATION_COMPANY}
                    ->{Company::FIELD_CONSOLIDATED_STATUS}
                    ->value;

                $purchases = $companyData
                    ->{Company::RELATION_PRODUCT_ASSIGNMENTS}
                    ->first()
                    ?->count ?? 0;

                $firstPurchaseDate = $companyData
                    ->{Company::RELATION_PRODUCT_ASSIGNMENTS}
                    ->first()
                    ?->first_purchase_date;

                $currentStatuses[$companyId] = [
                    'active' => $companyStatus === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value,
                    'purchases' => $purchases,
                ];

                if (! empty($activeWithin90Days[$companyId])) {
                    continue;
                }

                $activeWithin90Days[$companyId] = $companyData
                    ->{HistoricalCompanyStatuses::FIELD_DAILY_STATUSES}
                    ->toCollection()
                    ->filter(function (int $status, string $statusDate) use ($firstPurchaseDate) {
                        return $statusDate >= $firstPurchaseDate;
                    })
                    ->contains(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS->value);
            }
        }

        $dateTime = CarbonImmutable::now('UTC')->format('Y-m-d H:i:s');

        $expectedRows = [];
        foreach ($currentStatuses as $companyId => $status) {
            if ($status['active']
            && empty($activeWithin90Days[$companyId])
            && $status['purchases'] > 1) {
                $expectedRows[] = [
                    HistoricalCompanyActivation::FIELD_COMPANY_ID => $companyId,
                    HistoricalCompanyActivation::FIELD_STATUS_DATE => $dateTime,
                    HistoricalCompanyActivation::FIELD_TYPE => HistoricalCompanyActivation::REACTIVATION,
                ];
            } elseif ($status['active']
                && empty($activeWithin90Days[$companyId])
                && $status['purchases'] === 1) {
                $expectedRows[] = [
                    HistoricalCompanyActivation::FIELD_COMPANY_ID => $companyId,
                    HistoricalCompanyActivation::FIELD_STATUS_DATE => $dateTime,
                    HistoricalCompanyActivation::FIELD_TYPE => HistoricalCompanyActivation::ACTIVATION,
                ];
            } elseif (empty($status['active'])
                && $activeWithin90Days[$companyId]) {
                $expectedRows[] = [
                    HistoricalCompanyActivation::FIELD_COMPANY_ID => $companyId,
                    HistoricalCompanyActivation::FIELD_STATUS_DATE => $dateTime,
                    HistoricalCompanyActivation::FIELD_TYPE => HistoricalCompanyActivation::DEACTIVATION,
                ];
            }
        }

        RecordHistoricalCompanyActivations::dispatchSync();

        foreach ($expectedRows as $expectedRow) {
            $this->assertDatabaseHas(HistoricalCompanyActivation::TABLE, $expectedRow);
        }
    }
}
