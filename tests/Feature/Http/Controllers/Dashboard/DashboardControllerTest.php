<?php

namespace Tests\Feature\Http\Controllers\Dashboard;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class DashboardControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        Permission::findOrCreate('cms');

        Permission::findOrCreate('dashboard');

        $this->user = User::factory()->create()->givePermissionTo('dashboard');
    }

    #[Test]
    public function it_gets_the_index(): void
    {
        $this->actingAs($this->user)
            ->get(route('dashboard'))
            ->assertOk();
    }

    #[Test]
    public function the_index_has_a_title(): void
    {
        $appName = config('app.name');

        $this->actingAs($this->user)
            ->get(route('dashboard'))
            ->assertSeeHtmlInOrder([
                '<title>',
                $appName,
                '-',
                'Dashboard',
                '</title>',
            ]);
    }

    #[Test]
    public function the_index_requires_the_permission_dashboard(): void
    {
        $this->actingAs($this->user->revokePermissionTo('dashboard'))
            ->get(route('dashboard'))
            ->assertForbidden();
    }
}
