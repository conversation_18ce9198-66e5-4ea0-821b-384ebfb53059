<?php

namespace Tests\Feature\Http\Controllers\Prospects;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ProspectingUsersControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('update-prospecting-configurations'),
        );

        $this->actingAs($user);
    }

    #[Test]
    public function a_guest_cannot_view_prospecting_users(): void
    {
        Auth::logout();

        $this->assertGuest()
            ->getJson(route('internal-api.v2.prospecting.configuration.users.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function a_user_without_the_right_permissions_cannot_view_prospecting_users(): void
    {
        $this->actingAs(User::factory()->create()->givePermissionTo())
            ->getJson(route('internal-api.v2.prospecting.configuration.users.index'))
            ->assertForbidden();

        $this->actingAs(User::factory()->create()->givePermissionTo('prospecting'))
            ->getJson(route('internal-api.v2.prospecting.configuration.users.index'))
            ->assertForbidden();

        $this->actingAs(User::factory()->create()->givePermissionTo('update-prospecting-configurations'))
            ->getJson(route('internal-api.v2.prospecting.configuration.users.index'))
            ->assertForbidden();
    }

    #[Test]
    public function a_user_can_view_all_prospecting_users(): void
    {
        $bdmRole = Role::findOrCreate('business-development-manager');
        $sdrRole = Role::findOrCreate('sales-development-representative');

        $bdm = User::factory()->withRole('business-development-manager')->withRole('account-manager')->create();
        $sdr = User::factory()->withRole('sales-development-representative')->create();
        $otherUser = User::factory()->withRole('account-manager')->create();

        $this->getJson(route('internal-api.v2.prospecting.configuration.users.index'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $bdm->id,
                        'name' => $bdm->name,
                        'roles' => [
                            [
                                'id' => $bdmRole->id,
                                'name' => 'Business Development Manager',
                            ],
                        ],
                    ],
                    [
                        'id' => $sdr->id,
                        'name' => $sdr->name,
                        'roles' => [
                            [
                                'id' => $sdrRole->id,
                                'name' => 'Sales Development Representative',
                            ],
                        ],
                    ],
                ],
            ]);
    }
}
