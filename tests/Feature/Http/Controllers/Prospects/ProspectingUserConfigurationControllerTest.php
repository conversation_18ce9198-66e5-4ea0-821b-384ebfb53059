<?php

namespace Tests\Feature\Http\Controllers\Prospects;

use App\Models\Odin\Industry;
use App\Models\RoleConfiguration;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ProspectingUserConfigurationControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Industry::query()->delete();

        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('update-prospecting-configurations'),
        )->assignRole(Role::findOrCreate('account-manager'));

        $this->actingAs($user);
    }

    #[Test]
    public function a_guest_cannot_view_user_role_configurations(): void
    {
        Auth::logout();

        $this->assertGuest()
            ->getJson(route('internal-api.v2.prospecting.configuration.user.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function a_user_without_the_right_permissions_cannot_view_user_role_configurations(): void
    {
        $this->actingAs(User::factory()->create()->givePermissionTo())
            ->getJson(route('internal-api.v2.prospecting.configuration.user.index'))
            ->assertForbidden();

        $this->actingAs(User::factory()->create()->givePermissionTo('prospecting'))
            ->getJson(route('internal-api.v2.prospecting.configuration.user.index'))
            ->assertForbidden();

        $this->actingAs(User::factory()->create()->givePermissionTo('update-prospecting-configurations'))
            ->getJson(route('internal-api.v2.prospecting.configuration.user.index'))
            ->assertForbidden();
    }

    #[Test]
    public function a_user_can_view_all_user_role_configurations(): void
    {
        $industries = Industry::factory(5)->create();

        $roleConfiguration = RoleConfiguration::factory()->for(Auth::user())->create([
            'data' => [
                'processing_time_limit_days' => 100,
                'industries' => $industries->take(2)->pluck('id'),
            ],
        ]);

        $this->getJson(route('internal-api.v2.prospecting.configuration.user.index'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    [
                        'id' => $roleConfiguration->id,
                        'role' => [
                            'id' => $roleConfiguration->role->id,
                            'name' => str($roleConfiguration->role->name)->headline(),
                        ],
                        'user' => [
                            'id' => $roleConfiguration->user->id,
                            'name' => $roleConfiguration->user->name,
                        ],
                        'configurations' => [
                            [
                                'key' => 'industries',
                                'value' => $industries->take(2)->pluck('id'),
                                'display_name' => 'Industries',
                                'type' => 'multiselect',
                                'options' => $industries->map->only('id', 'name'),
                                'placeholder' => 'All Industries',
                            ],
                            [
                                'key' => 'processing_time_limit_days',
                                'value' => 100,
                                'display_name' => 'Processing Time Limit Days',
                                'type' => 'input',
                                'options' => null,
                                'placeholder' => null,
                            ],
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function a_user_can_create_a_user_specific_role_configuration()
    {
        $this->postJson(route('internal-api.v2.prospecting.configuration.user.store'), [
            'user_id' => Auth::user()->id,
            'role_id' => Auth::user()->roles->first()->id,
            'configs' => [
                [
                    'key' => 'industries',
                    'value' => [],
                ],
                [
                    'key' => 'processing_time_limit_days',
                    'value' => 42,
                ],
            ],
        ])->assertCreated();

        $this->assertDatabaseHas('role_configurations', [
            'user_id' => Auth::user()->id,
            'role_id' => Auth::user()->roles->first()->id,
            'data' => $this->castAsJson([
                'industries' => [],
                'processing_time_limit_days' => 42,
            ]),
        ]);
    }

    #[Test, DataProvider('invalidUserConfig')]
    public function a_user_cannot_create_a_user_specific_role_configuration_when($data, $error)
    {
        $this->postJson(route('internal-api.v2.prospecting.configuration.user.store'), $data())
            ->assertInvalid($error);
    }

    #[Test]
    public function a_user_can_update_user_specific_role_configurations(): void
    {
        $roleConfiguration = RoleConfiguration::factory()->for(Auth::user())->create();

        $this->patchJson(
            route('internal-api.v2.prospecting.configuration.user.update', $roleConfiguration),
            [
                'configs' => [
                    [
                        'key' => 'foo',
                        'value' => 'bar',
                    ],
                ],
            ]
        )->assertNoContent();

        $this->assertEquals([
            'foo' => 'bar',
        ], $roleConfiguration->refresh()->data);
    }

    public static function invalidUserConfig()
    {
        return [
            'user id is missing' => [
                fn () => [
                    'user_id' => '',
                    'role_id' => Auth::user()->roles->first()->id,
                    'configs' => [
                        'key' => 'industries',
                        'value' => [],
                    ],
                    [
                        'key' => 'processing_time_limit_days',
                        'value' => 42,
                    ],
                ],
                'user_id',
            ],
            'user id does not exist' => [
                fn () => [
                    'user_id' => 9999999999,
                    'role_id' => Auth::user()->roles->first()->id,
                    'configs' => [
                        'key' => 'industries',
                        'value' => [],
                    ],
                    [
                        'key' => 'processing_time_limit_days',
                        'value' => 42,
                    ],
                ],
                'user_id',
            ],
            'role id is missing' => [
                fn () => [
                    'user_id' => Auth::user()->id,
                    'role_id' => '',
                    'configs' => [
                        'key' => 'industries',
                        'value' => [],
                    ],
                    [
                        'key' => 'processing_time_limit_days',
                        'value' => 42,
                    ],
                ],
                'role_id',
            ],
            'role id does not exist' => [
                fn () => [
                    'user_id' => Auth::user()->id,
                    'role_id' => 9999999999,
                    'configs' => [
                        'key' => 'industries',
                        'value' => [],
                    ],
                    [
                        'key' => 'processing_time_limit_days',
                        'value' => 42,
                    ],
                ],
                'role_id',
            ],
            'user does not have role' => [
                fn () => [
                    'user_id' => Auth::user()->id,
                    'role_id' => Role::findOrCreate('test')->id,
                    'configs' => [
                        'key' => 'industries',
                        'value' => [],
                    ],
                    [
                        'key' => 'processing_time_limit_days',
                        'value' => 42,
                    ],
                ],
                'role_id',
            ],
            'configs is missing' => [
                fn () => [
                    'user_id' => Auth::user()->id,
                    'role_id' => Auth::user()->roles->first()->id,
                    'configs' => '',
                ],
                'configs',
            ],
            'configs is not an array' => [
                fn () => [
                    'user_id' => Auth::user()->id,
                    'role_id' => Auth::user()->roles->first()->id,
                    'configs' => 'foobar',
                ],
                'configs',
            ],
            'configs does not have a key for key' => [
                fn () => [
                    'user_id' => Auth::user()->id,
                    'role_id' => Auth::user()->roles->first()->id,
                    'configs' => [
                        [
                            'value' => 'foobar',
                        ],
                    ],
                ],
                'configs.0.key',
            ],
            'configs does not have a key for value' => [
                fn () => [
                    'user_id' => Auth::user()->id,
                    'role_id' => Auth::user()->roles->first()->id,
                    'configs' => [
                        [
                            'key' => 'foobar',
                        ],
                    ],
                ],
                'configs.0.value',
            ],
        ];
    }
}
