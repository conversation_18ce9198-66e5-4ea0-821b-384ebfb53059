<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\Companies;

use App\Enums\CampaignStatus;
use App\Enums\Odin\StateAbbreviation;
use App\Http\Resources\Roles\AccountManagerResource;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentLink;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Models\User;
use App\Services\Companies\Delete\CompanyDeleteService;
use Database\Seeders\DirectLeadsProductSeeder;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyControllerTest extends TestCase
{
    use DatabaseTruncation, RefreshDatabase;

    protected Company $company;

    protected User $user;

    protected array $connectionsToTruncate = [
        'mysql',
        'readonly',
    ];

    protected array $tablesToTruncate = [
        'mysql' => [
            'company_industry_types',
            'company_industry',
            'company_expert_reviews',
            'company_data',
            'company_change_logs',
            'company_admins',
            'company',
            'company_configuration',
        ],
        'readonly' => [
            EloquentLink::TABLE,
            EloquentCompany::TABLE,
        ],
    ];

    #[Test]
    public function it_gets_the_company_by_id(): void
    {
        $this->actingAs($this->user)->get(route('companies.index', ['id' => $this->company->id]))
            ->assertOk()
            ->assertViewIs('companies.index')
            ->assertViewHas('company')
            ->assertViewHas('company.admin_approved');
    }

    #[Test]
    public function it_gets_the_company_by_id_and_fails(): void
    {
        $company = Company::find(3000);

        if ($company) {
            $company->delete();
        }

        $this->actingAs($this->user)->get(route('companies.index', ['id' => 3000]))
            ->assertNotFound();
    }

    #[Test]
    public function it_gets_the_company_by_id_and_displays_social_media_links(): void
    {
        Role::findOrCreate('customer-success-manager');

        $this->company->legacyCompany->socialMediaLink()->create([
            'linktype' => 'facebook',
            'linkvalue' => 'https://www.facebook.com',
            'reltype' => 'companyid',
        ]);

        $this->actingAs($this->user)->get(route('companies.index', ['id' => $this->company->id]))
            ->assertOk()
            ->assertViewIs('companies.index')
            ->assertViewHas('company.socialMediaLinks', $this->company->legacyCompany->socialMediaLink);
    }

    #[Test]
    public function it_gets_the_company_by_name(): void
    {
        $this->actingAs($this->user)->get(route('companies.index', ['id' => $this->company->name, 'by_name' => true]))
            ->assertRedirect(route('companies.index', ['id' => $this->company->id]));
    }

    #[Test]
    public function it_gets_the_company_by_name_without_boolean_parameter(): void
    {
        $this->actingAs($this->user)->get(route('companies.index', ['id' => $this->company->name]))
            ->assertRedirect(route('companies.index', ['id' => $this->company->id]));
    }

    #[Test]
    public function it_gets_the_company_by_name_and_fails(): void
    {
        $this->actingAs($this->user)->get(route('companies.index', ['id' => 'test', 'by_name' => true]))
            ->assertNotFound();
    }

    #[Test]
    public function it_gets_the_company_by_name_and_fails_without_boolean_parameter(): void
    {
        $this->actingAs($this->user)->get(route('companies.index', ['id' => 'test']))
            ->assertNotFound();
    }

    #[Test]
    public function it_gets_the_company_search_page(): void
    {
        $successManager = SuccessManager::factory()->create();

        SuccessManagerClient::create([
            'status' => SuccessManagerClient::STATUS_ACTIVE,
            'success_manager_id' => $successManager->id,
            'company_reference' => $this->company->reference,
        ]);

        $this->actingAs($this->user)->get(route('companies.search'))
            ->assertOk()
            ->assertViewIs('companies.search')
            ->assertViewHas('user_id', $this->user->id)
            ->assertViewHas('industries', Industry::all())
            ->assertViewHas('account_managers', AccountManagerResource::collection(User::accountManagerRole()->get()))
            ->assertViewHas('success_managers', SuccessManager::get())
            ->assertViewHas('us_states', StateAbbreviation::asSelectArray())
            ->assertViewHas('campaign_statuses', CampaignStatus::asSelectArray())
            ->assertViewHas('user_permissions', $this->user->getAllPermissions()->toArray());
    }

    #[Test]
    public function the_company_page_is_not_accessible_without_the_companies_permission(): void
    {
        $this->actingAs($this->user->revokePermissionTo('companies'))
            ->get(route('companies.index', ['id' => $this->company->id]))
            ->assertForbidden();
    }

    #[Test]
    public function the_company_page_has_a_title(): void
    {
        $appName = config('app.name');

        $this->company = Company::factory()
            ->has(CompanyConfiguration::factory(), 'configuration')
            ->withLegacyCompany()
            ->createQuietly([
                'name' => "Hoeger-O'Conner",
            ]);

        $this->actingAs($this->user)
            ->get(route('companies.index', ['id' => $this->company->id]))
            ->assertSeeHtmlInOrder([
                '<title>',
                $appName,
                '-',
                'Company: Hoeger-O&amp;#039;Conner',
                '</title>',
            ]);
    }

    #[Test]
    public function it_cancels_deletion(): void
    {
        $companyDeletionService = new CompanyDeleteService($this->company);

        $companyDeletionService->markForDeletion();

        $this->assertTrue($companyDeletionService->markedForDeletion());

        $this->actingAs($this->user)
            ->get(route('companies.delete.cancel', $this->company))
            ->assertSessionHas('status', 'Company deletion Cancelled')
            ->assertRedirectToRoute('companies.index', ['id' => $this->company->id]);

        $this->assertFalse($companyDeletionService->markedForDeletion());
    }

    #[Test]
    public function it_does_not_cancel_deletion_if_already_cancelled(): void
    {
        $companyDeletionService = new CompanyDeleteService($this->company);

        $this->assertFalse($companyDeletionService->markedForDeletion());

        $this->company->delete();

        $this->actingAs($this->user)
            ->get(route('companies.delete.cancel', $this->company))
            ->assertSessionHas('status', 'Company has already been deleted')
            ->assertRedirectToRoute('companies.search');

        $this->assertFalse($companyDeletionService->markedForDeletion());
    }

    protected function setUp(): void
    {
        parent::setUp();

        IndustryService::factory()->create();

        $this->seed(ProductsSeeder::class);

        $this->seed(DirectLeadsProductSeeder::class);

        $this->company = Company::factory()
            ->has(CompanyConfiguration::factory(), 'configuration')
            ->withLegacyCompany()
            ->createQuietly();

        $this->user = User::factory()->withRole('account-manager')->create();

        Role::findOrCreate('business-development-manager');
        Role::findOrCreate('customer-success-manager');
        Role::findOrCreate('onboarding-manager');
        Role::findOrCreate('sales-development-representative');

        $this->user->givePermissionTo(Permission::findOrCreate('companies'));

        Permission::findOrCreate('can-reallocate-all');

        Permission::findOrCreate('can-reallocate-team');
    }
}
