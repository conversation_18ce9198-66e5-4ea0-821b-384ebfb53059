<?php

namespace Tests\Feature\Http\Controllers\API;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\Industry;
use App\Enums\TaskCategory as TaskCategoryEnum;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignCrmIntegration;
use App\Models\Legacy\Location;
use App\Models\Legacy\NonPurchasingCompanyLocation;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyLocation;
use App\Models\Sales\Task;
use App\Models\TaskCategory;
use App\Models\User;
use Illuminate\Database\ConnectionInterface;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class NonPurchasingServiceAreaControllerTest extends TestCase
{
    use RefreshDatabase, DatabaseTruncation;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        LeadCampaignCrmIntegration::query()->truncate();

        LeadCampaign::query()->truncate();

        EloquentCompany::query()->truncate();

        NonPurchasingCompanyLocation::query()->truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_no_companies_and_industry(): void
    {
        $industry = \App\Models\Odin\Industry::factory()->create([
            'slug' => 'solar',
        ]);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => $industry->slug,
            ]))
            ->assertBadRequest()
            ->assertJson([
                'data' => [
                    'companies' => [],
                    'message' => 'County not found',
                ],
            ]);
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_no_companies_and_no_industry(): void
    {
        $location = Location::factory()->create([
            'type' => Location::TYPE_ZIP_CODE,
            'zip_code' => '12345',
            'county_key' => 'MO',
            'state_key' => 'CA',
        ]);

        Location::factory()->create([
            'type' => Location::TYPE_COUNTY,
            'zip_code' => $location->zip_code,
            'county_key' => $location->county_key,
            'state_key' => $location->state_key,
        ]);

        $this->assertDatabaseCount(Location::TABLE, 2);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => 'non-existent-industry',
            ]))
            ->assertBadRequest()
            ->assertJson([
                'data' => [
                    'companies' => [],
                    'message' => 'Industry is invalid. "non-existent-industry" passed.',
                ],
            ]);
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_companies(): void
    {
        Role::findOrCreate('account-manager');

        Role::findOrCreate('customer-success-manager');

        $legacyCompany = EloquentCompany::factory()->create([
            'companyid' => random_int(1, 1000),
        ]);

        $company = Company::factory()->createQuietly([
            'legacy_id' => $legacyCompany->companyid,
        ]);

        CompanyIndustry::factory()->create([
            'company_id' => $company->id,
            'industry_id' => \App\Models\Odin\Industry::updateOrCreate([
                'slug' => Industry::SOLAR->value,
            ], [
                'name' => Industry::SOLAR->value,
            ])->id,
        ]);

        $location = Location::factory()->create([
            'type' => Location::TYPE_ZIP_CODE,
            'zip_code' => '12345',
            'county_key' => 'MO',
            'state_key' => 'CA',
        ]);

        $countyLocation = Location::factory()->create([
            'type' => Location::TYPE_COUNTY,
            'zip_code' => $location->zip_code,
            'county_key' => $location->county_key,
            'state_key' => $location->state_key,
        ]);

        NonPurchasingCompanyLocation::create([
            'company_id' => $legacyCompany->companyid,
            'location_id' => $countyLocation->id,
        ]);

        CompanyLocation::factory()->create([
            'company_id' => $company->id,
            'address_id' => Address::factory()->createQuietly([
                'legacy_id' => $location->id,
                'zip_code' => '12345',
            ])->id,
        ]);

        $companies = EloquentCompany::with('miCompany.locations', 'miCompany.users')->get()->toArray();

        $companies[0]['google_review_count'] = 0;
        $companies[0]['status'] = CompanyConsolidatedStatus::label($legacyCompany->refresh()->miCompany?->consolidated_status);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => Industry::SOLAR->value,
            ]))
            ->assertOk()
            ->assertJsonCount(1, 'data.companies')
            ->assertJson([
                'data' => [
                    'companies' => $companies,
                    'message' => 'Companies retrieved successfully.',
                ],
            ]);
    }

    #[Test]
    public function get_companies_in_county_by_zip_code_with_companies_and_filter_by_account_manager(): void
    {
        $legacyCompany = EloquentCompany::factory()->create();

        $this->assertDatabaseCount('tblcompany', 1, 'readonly');

        $company = Company::factory()->createQuietly([
            'legacy_id' => $legacyCompany->companyid,
        ]);

        CompanyIndustry::factory()->create([
            'company_id' => $company->id,
            'industry_id' => \App\Models\Odin\Industry::updateOrCreate([
                'slug' => Industry::SOLAR->value,
            ], [
                'name' => Industry::SOLAR->value,
            ])->id,
        ]);

        $location = Location::factory()->create([
            'type' => Location::TYPE_ZIP_CODE,
            'zip_code' => '12345',
            'county_key' => 'MO',
            'state_key' => 'CA',
        ]);

        $countyLocation = Location::factory()->create([
            'type' => Location::TYPE_COUNTY,
            'zip_code' => $location->zip_code,
            'county_key' => $location->county_key,
            'state_key' => $location->state_key,
        ]);

        NonPurchasingCompanyLocation::create([
            'company_id' => $legacyCompany->companyid,
            'location_id' => $countyLocation->id,
        ]);

        CompanyLocation::factory()->create([
            'company_id' => $company->id,
            'address_id' => Address::factory()->createQuietly([
                'legacy_id' => $location->id,
                'zip_code' => '12345',
            ])->id,
        ]);

        $user = User::factory()->withRole('account-manager')->create();

        $company->assign($user)->as('account-manager');

        $task = Task::factory()->create([
            'task_category_id' => TaskCategory::factory()->create([
                'name' => TaskCategoryEnum::CATEGORY_SYSTEM->value,
            ])->id,
        ]);

        $companies = EloquentCompany::with('miCompany.locations', 'miCompany.users')->get()->toArray();

        $companies[0]['google_review_count'] = 0;
        $companies[0]['status'] = CompanyConsolidatedStatus::label($legacyCompany->refresh()->miCompany?->consolidated_status);

        $this->actingAs($user)
            ->get(route('internal-api.v1.non-purchasing-service-areas.get-companies-in-county-by-zip-code', [
                'zip_code' => '12345',
                'industry' => Industry::SOLAR->value,
                'task_id' => $task->id,
            ]))
            ->assertOk()
            ->assertJsonCount(1, 'data.companies')
            ->assertJson([
                'data' => [
                    'companies' => [$companies[0]],
                    'message' => 'Companies retrieved successfully.',
                ],
            ]);
    }
}
