<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\API;

use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Enums\Campaigns\CampaignStatus;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModule;
use App\Models\Campaigns\Modules\LocationModule\CompanyCampaignLocationModuleLocation;
use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\Location;
use App\Models\Locations\USLocation;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\PropertyType;
use App\Models\User;
use App\Services\Communication\TwilioCommunicationService;
use App\Services\PubSub\PubSubService;
use Database\Seeders\ProductsSeeder;
use Database\Seeders\PropertyTypesSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Schema;
use Spatie\Permission\Models\Permission;
use Str;
use Tests\TestCase;

class CompanyCampaignWizardControllerTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected CompanyCampaign $companyCampaign;

    protected CompanyCampaignLocationModule $companyCampaignLocationModule;

    protected CompanyCampaignLocationModuleLocation $companyCampaignLocationModuleLocation;

    protected Location $location;

    protected BudgetContainer $budgetContainer;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        LeadCampaign::truncate();

        EloquentCompany::truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        $this->mock(PubSubService::class)->shouldReceive('handle');

        $this->seed(ProductsSeeder::class);

        $this->user = User::factory()->createQuietly();

        $this->user->givePermissionTo(Permission::findOrCreate('campaigns/view'));
    }

    #[Test]
    public function it_gets_the_campaign_summary_list()
    {
        $this->company = Company::factory()->createQuietly();

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly([
            'company_id' => $this->company->id,
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
            'product_id' => Product::first()->id,
            'service_id' => IndustryService::first()->id,
        ]);

        $this->companyCampaignLocationModule = CompanyCampaignLocationModule::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $locationTypes = [
            Location::TYPE_ZIP_CODE,
            Location::TYPE_COUNTY,
            Location::TYPE_STATE,
        ];

        foreach ($locationTypes as $locationType) {
            $this->location = Location::factory()->createQuietly([
                'type' => $locationType,
                'county_key' => 'Monmouth',
                'state_key' => 'NJ',
                'zip_code' => '07701',
            ]);

            $this->companyCampaignLocationModuleLocation = CompanyCampaignLocationModuleLocation::createQuietly([
                'module_id' => $this->companyCampaignLocationModule->id,
                'location_id' => $this->location->id,
            ]);
        }

        $this->budgetContainer = BudgetContainer::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.campaign-wizard.company.campaign.get-campaign-summary-list', $this->company->id))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'campaigns' => [
                        'current_page',
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                                'reference',
                                'status',
                                'zip_code_targeted',
                                'uses_custom_floor_prices',
                                'status_display',
                                'active',
                                'state_count',
                                'county_count',
                                'zip_code_count',
                                'average_daily_spend',
                                'average_daily_won',
                                'average_daily_available',
                                'product',
                                'service',
                                'industry',
                                'property_types',
                                'budgets',
                                'statistics',
                                'lead_last_sold_at',
                                'average_lead_cost',
                            ],
                        ],
                        'first_page_url',
                        'from',
                        'last_page',
                        'last_page_url',
                        'links' => [
                            '*' => [
                                'url',
                                'label',
                                'active',
                            ],
                        ],
                        'next_page_url',
                        'path',
                        'per_page',
                        'prev_page_url',
                        'to',
                        'total',
                    ],
                    'zip_code_exceptions',
                    'unrestricted_zip_code_targeting',
                    'all_campaigns_list',
                ],
            ])
            ->assertJsonFragment([
                'state_count' => 1,
                'county_count' => 1,
                'zip_code_count' => 3,
            ]);
    }

    #[Test]
    public function it_gets_the_campaign_summary_list_with_detailed_state_and_counties_information()
    {
        $this->company = Company::factory()->createQuietly();

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly([
            'company_id' => $this->company->id,
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
            'product_id' => Product::first()->id,
            'service_id' => IndustryService::first()->id,
        ]);

        $this->companyCampaignLocationModule = CompanyCampaignLocationModule::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $locationTypes = [
            Location::TYPE_ZIP_CODE,
            Location::TYPE_COUNTY,
            Location::TYPE_STATE,
        ];

        foreach ($locationTypes as $locationType) {
            $this->location = Location::factory()->createQuietly([
                'type' => $locationType,
                'county_key' => 'Monmouth',
                'state_key' => 'NJ',
                'zip_code' => '07701',
            ]);

            $this->companyCampaignLocationModuleLocation = CompanyCampaignLocationModuleLocation::createQuietly([
                'module_id' => $this->companyCampaignLocationModule->id,
                'location_id' => $this->location->id,
            ]);
        }

        $this->budgetContainer = BudgetContainer::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.campaign-wizard.company.campaign.get-campaign-summary-list', $this->company->id))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'campaigns' => [
                        'data' => [
                            '*' => [
                                'states' => [
                                    '*' => [
                                        'counties',
                                    ],
                                ],
                                'counties',
                            ],
                        ],
                    ],
                ],
            ])
            ->assertJsonFragment([
                'state_count' => 1,
                'county_count' => 1,
            ])
            ->assertJsonCount(1, 'data.campaigns.data.*.states.*')
            ->assertJsonCount(1, 'data.campaigns.data.*.counties.*');
    }

    #[Test]
    public function it_gets_the_campaign_summary_list_with_detailed_state_and_counties_information_that_matches_counts()
    {
        $this->company = Company::factory()->createQuietly();

        $company = $this->company;

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly([
            'company_id' => $this->company->id,
            'status' => CampaignStatus::PAUSED_PERMANENTLY,
            'product_id' => Product::first()->id,
            'service_id' => IndustryService::first()->id,
        ]);

        $this->companyCampaignLocationModule = CompanyCampaignLocationModule::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $locationTypes = [
            Location::TYPE_ZIP_CODE,
            Location::TYPE_COUNTY,
            Location::TYPE_STATE,
        ];

        foreach ($locationTypes as $locationType) {
            $this->location = Location::factory()->createQuietly([
                'type' => $locationType,
                'county_key' => 'Monmouth',
                'state_key' => 'NJ',
                'zip_code' => '07701',
            ]);

            $this->companyCampaignLocationModuleLocation = CompanyCampaignLocationModuleLocation::createQuietly([
                'module_id' => $this->companyCampaignLocationModule->id,
                'location_id' => $this->location->id,
            ]);
        }

        $this->budgetContainer = BudgetContainer::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        Location::factory()->createQuietly([
            'type' => USLocation::TYPE_STATE,
            'county_key' => 'Middlesex',
            'state_key' => 'NJ',
            'zip_code' => '07701',
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.campaign-wizard.company.campaign.get-campaign-summary-list', $company->id))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'campaigns' => [
                        'data' => [
                            '*' => [
                                'states' => [
                                    '*' => [
                                        'counties',
                                    ],
                                ],
                                'counties',
                            ],
                        ],
                    ],
                ],
            ])
            ->assertJsonFragment([
                'state_count' => 2,
                'county_count' => 1,
            ])
            ->assertJsonCount(2, 'data.campaigns.data.*.states.*')
            ->assertJsonCount(1, 'data.campaigns.data.*.counties.*')
            ->assertJsonCount(1, 'data.campaigns.data.*.states.0.counties.*')
            ->assertJsonCount(1, 'data.campaigns.data.*.states.1.counties.*');
    }

    #[Test]
    public function it_saves_the_crm_template()
    {
        $this->company = Company::factory()->createQuietly();

        $this->actingAs($this->user)->put(route('internal-api.v1.campaign-wizard.company.crm.save-template', $this->company->id), [
            'crm_type' => CRMType::STANDARD_WEB_FORM->value,
            'display_name' => 'test template',
            'payload' => [],
        ])->assertOk();

        $this->assertDatabaseHas('company_crm_templates', [
            'company_id' => $this->company->id,
            'crm_type' => CRMType::STANDARD_WEB_FORM,
            'display_name' => 'test template',
        ]);

        $this->assertEquals([], CompanyCRMTemplate::where('display_name', 'test template')->first()->payload);
    }

    #[Test]
    public function it_gets_crm_configurations()
    {
        $this->company = Company::factory()->createQuietly();

        $this->actingAs($this->user)->get(route('internal-api.v1.campaign-wizard.company.get-crm-configurations', $this->company->id))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'crm_configurations' => [
                        '*' => [
                            'id',
                            'key',
                            'name',
                            'system_fields' => [
                                '*' => [
                                    'key',
                                    'display_name',
                                    'value',
                                    'type',
                                    'required',
                                    'payload',
                                    'display_flag',
                                ],
                            ],
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_updates_campaign()
    {
        $this->mock(CampaignMediator::class)->shouldReceive('save')->andReturn(true);

        $this->seed(PropertyTypesSeeder::class);

        $this->user->givePermissionTo(Permission::findOrCreate('campaigns/update'));

        $this->company = Company::factory()->createQuietly();

        $reference = Str::uuid()->toString();

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly([
            'company_id' => $this->company->id,
            'status' => CampaignStatus::ACTIVE,
            'product_id' => Product::first()->id,
            'service_id' => IndustryService::first()->id,
            'reference' => $reference,
        ]);

        $this->actingAs($this->user)->patch(route('internal-api.v1.campaign-wizard.company.campaign.update', $this->company->id), [
            'status' => CampaignStatus::PAUSED_PERMANENTLY->value,
            'name' => 'test campaign',
            'reference' => $reference,
            'payload' => [
                'test',
            ],
            'property_types' => PropertyType::pluck('name')->toArray(),
        ])
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => true,
                ],
            ]);
    }

    #[Test]
    public function it_updates_campaign_with_realistic_payload()
    {
        $service = ClientTokenService::createQuietly([
            'service_key' => 'legacy_admin_api',
            'service_name' => 'Legacy Admin API',
        ]);

        ClientToken::createQuietly([
            'service_id' => $service->id,
            'client_token' => 'test',
        ]);

        $uuid = Str::uuid()->toString();

        Http::fake([
            '*/campaigns/create' => Http::response([
                'status' => true,
                'data' => compact('uuid'),
            ]),
        ]);

        $legacyLeadCampaign = LeadCampaign::factory()->createQuietly(compact('uuid'));

        $this->mock(TwilioCommunicationService::class);

        $this->seed(PropertyTypesSeeder::class);

        $this->user->givePermissionTo(Permission::findOrCreate('campaigns/update'));

        $this->company = Company::factory()->createQuietly();

        $reference = Str::uuid()->toString();

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly([
            'company_id' => $this->company->id,
            'status' => CampaignStatus::ACTIVE,
            'product_id' => Product::first()->id,
            'service_id' => IndustryService::first()->id,
            'reference' => $reference,
        ]);

        $this->budgetContainer = BudgetContainer::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        Budget::createQuietly([
            'budget_container_id' => $this->budgetContainer->id,
        ]);

        $companyCrmTemplate = CompanyCRMTemplate::createQuietly([
            'company_id' => $this->company->id,
            'crm_type' => CRMType::STANDARD_WEB_FORM,
            'display_name' => 'test template',
            'payload' => [],
        ]);

        $payload = json_decode(file_get_contents(base_path('tests/Fixtures/save-crm-template-request-payload.json')), true);

        $payload['company_id'] = $this->company->id;
        $payload['reference'] = $this->companyCampaign->reference;
        $payload['payload']['delivery']['crm_deliveries'][0]['template_id'] = $companyCrmTemplate->id;
        $payload['payload']['delivery']['crm_deliveries'][1]['template_id'] = $companyCrmTemplate->id;

        $this->assertDatabaseCount('company_crm_templates', 1);
        $this->assertDatabaseCount('company_campaign_delivery_module_crms', 0);
        $this->assertDatabaseCount('company_campaign_relations', 0);

        $this->actingAs($this->user)->patch(route('internal-api.v1.campaign-wizard.company.campaign.update', $this->company->id), $payload)
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => true,
                ],
            ]);

        $this->assertDatabaseCount('company_crm_templates', 1);
        $this->assertDatabaseCount('company_campaign_delivery_module_crms', 1);
        $this->assertDatabaseCount('company_campaign_relations', 1);

        $this->assertDatabaseHas('company_campaign_relations', [
            'company_campaign_id' => $this->companyCampaign->id,
            'relation_id' => $legacyLeadCampaign->id,
            'relation_type' => 'legacy_lead_campaign',
        ]);

        $this->assertDatabaseHas('company_campaign_delivery_modules', [
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $this->assertDatabaseHas('company_campaign_delivery_module_crms', [
            'module_id' => CompanyCampaignDeliveryModule::where('company_campaign_id', $this->companyCampaign->id)->first()->id,
            'crm_type' => 0,
            'active' => 1,
            'display_name' => '',
            'template_id' => $companyCrmTemplate->id,
        ]);
    }
}
