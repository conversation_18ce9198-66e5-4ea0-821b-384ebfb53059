<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\API\Workflows;

use App\Models\Odin\Company;
use App\Models\Sales\Task;
use App\Models\User;
use App\Services\Communication\TwilioCommunicationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class TaskControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Task $task;

    protected Company $company;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();

        $this->user->givePermissionTo(Permission::findOrCreate('tasks'));
    }

    #[Test]
    public function it_gets_all_tasks()
    {
        $this->mock(TwilioCommunicationService::class)->shouldReceive('__construct');

        $this->task = Task::factory()->create([
            'assigned_user_id' => $this->user->id,
            'payload' => [
                'company_id' => Company::factory()->createQuietly()->id,
            ],
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.tasks.get-all-tasks', [
                'sort_dir' => 'asc',
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'tasks' => [
                        'current_page',
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                                'completed',
                                'completed_at',
                                'relation',
                                'queue',
                                'priority',
                                'due',
                                'event',
                                'manual',
                                'payload' => [
                                    'company_id',
                                    'company_name',
                                ],
                                'category',
                                'assigned_user_id',
                                'assigned_user',
                                'timezone',
                                'auth_user_is_assigned',
                                'action_id',
                                'muted',
                            ],
                        ],
                        'first_page_url',
                        'from',
                        'last_page',
                        'last_page_url',
                        'links' => [
                            '*' => [
                                'url',
                                'label',
                                'active',
                            ],
                        ],
                        'next_page_url',
                        'path',
                        'per_page',
                        'prev_page_url',
                        'to',
                        'total',
                    ],
                ],
            ]);
    }
}
