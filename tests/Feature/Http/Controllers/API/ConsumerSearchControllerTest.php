<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\API;

use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use App\Models\User;
use App\Services\Filterables\Consumer\ConsumerFilterableService;
use Carbon\Carbon;
use Database\Seeders\DirectLeadsProductSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Testing\Fluent\AssertableJson;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class ConsumerSearchControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Consumer $consumer;

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(DirectLeadsProductSeeder::class);

        $this->user = User::factory()->create();

        $this->user->givePermissionTo(Permission::findOrCreate('consumer-search'));

        Permission::findOrCreate('view-lead-pii');
    }

    #[Test]
    public function it_searches_for_consumers()
    {
        $this->consumer = $this->createFilteredInConsumer();

        $this->actingAs($this->user)->post(route('internal-api.v1.consumer-search.search'))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'consumers' => [
                        'data' => [
                            '*' => [
                                'id',
                                'lead_id',
                                'created_at',
                                'origin',
                                'name',
                                'first_name',
                                'last_name',
                                'uuid',
                                'industry',
                                'service',
                                'good_to_sell',
                                'status',
                                'lead_status',
                                'appointment_status',
                                'direct_leads_status',
                                'contact_requests',
                                'verified',
                                'ad_track',
                                'consumer_url',
                                'map_url',
                                'consumer_products' => ['*' => ['id', 'product', 'created_at', 'companies_assigned' => ['*' => []], 'is_test_lead']],
                                'marketing_strategy',
                                'phone',
                                'email',
                                'address',
                                'available_campaigns',
                                'available_budget',
                                'locked',
                                'can_call',
                            ],
                        ],
                        'links' => ['first', 'last', 'prev', 'next'],
                        'meta' => ['current_page', 'from', 'last_page', 'links' => ['*' => ['url', 'label', 'active']], 'path', 'per_page', 'to', 'total'],
                    ],
                    'filter_updates',
                ],
            ]);
    }

    #[Test]
    public function it_gets_filter_options()
    {
        Permission::findOrCreate('consumer-product/view-leads-longer-than-90-days');

        $this->actingAs(
            User::factory()->create()
        )->getJson(route('internal-api.v1.consumer-search.get-filter-options'))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'presets',
                    'filter_options',
                ],
            ])
            ->assertJson(function (AssertableJson $json) {
                $json->has('data.filter_options', function (AssertableJson $json) {
                    foreach (app(ConsumerFilterableService::class)->getDisplayData() as $idx => $displayDatum) {
                        $json->hasAll(
                            collect(array_keys($displayDatum))->map(fn ($key) => "{$idx}.{$key}")->toArray()
                        );
                    }
                });
            });
    }

    #[Test]
    public function date_filter_exists_in_filter_options()
    {
        Carbon::setTestNow(Carbon::create(2023, 5, 21, 12));

        Permission::findOrCreate('consumer-product/view-leads-longer-than-90-days');

        $this->actingAs(
            User::factory()->create()
        )->getJson(route('internal-api.v1.consumer-search.get-filter-options'))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'filter_options' => [
                        1 => [
                            'type' => 'date-range',
                            'name' => 'Date Range',
                            'id' => 'consumer-date-range',
                            'clearable' => false,
                            'show' => true,
                        ],
                    ],
                ],
            ])
            ->assertJsonFragment([
                'min' => now()->subMonths(3)->subDays(2),
                'max' => now()->addHours(12),
                'default' => now()->subDay(),
            ])
            ->assertJsonFragment([
                'min' => now()->subMonths(3)->subDays(2),
                'max' => now()->addHours(12),
                'default' => now(),
            ])
            ->assertJsonStructure([
                'data' => [
                    'filter_options' => [
                        1 => [
                            'options' => [
                                'from' => [
                                    'min',
                                    'max',
                                    'default',
                                ],
                                'to' => [
                                    'min',
                                    'max',
                                    'default',
                                ],
                                'presets' => [
                                    'Last Hour',
                                    'Last 4 Hours',
                                    'Last 8 Hours',
                                    'Today Only',
                                    'Yesterday Only',
                                    'Last 24 Hours',
                                    'Last Week',
                                    'Last Month',
                                    'This Calendar Month',
                                ],
                            ],
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function min_date_in_date_filter_is_null_when_user_has_view_leads_longer_than_90_days_permission()
    {
        Carbon::setTestNow(Carbon::create(2023, 5, 21, 12));

        Permission::findOrCreate('consumer-product/view-leads-longer-than-90-days');

        $this->actingAs(
            User::factory()->create()->givePermissionTo('consumer-product/view-leads-longer-than-90-days')
        )->getJson(route('internal-api.v1.consumer-search.get-filter-options'))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'filter_options' => [
                        1 => [
                            'type' => 'date-range',
                            'name' => 'Date Range',
                            'id' => 'consumer-date-range',
                            'clearable' => false,
                            'show' => true,
                        ],
                    ],
                ],
            ])
            ->assertJsonFragment([
                'min' => null,
                'max' => now()->addHours(12),
                'default' => now()->subDay(),
            ])
            ->assertJsonFragment([
                'min' => null,
                'max' => now()->addHours(12),
                'default' => now(),
            ])
            ->assertJsonStructure([
                'data' => [
                    'filter_options' => [
                        1 => [
                            'options' => [
                                'from' => [
                                    'min',
                                    'max',
                                    'default',
                                ],
                                'to' => [
                                    'min',
                                    'max',
                                    'default',
                                ],
                                'presets' => [
                                    'Last Hour',
                                    'Last 4 Hours',
                                    'Last 8 Hours',
                                    'Today Only',
                                    'Yesterday Only',
                                    'Last 24 Hours',
                                    'Last Week',
                                    'Last Month',
                                    'This Calendar Month',
                                ],
                            ],
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_can_sort_by_created_at()
    {
        $consumerOne = $this->createFilteredInConsumer([
            'created_at' => now()->subDay(),
        ]);

        $consumerTwo = $this->createFilteredInConsumer([
            'created_at' => now()->subDays(2),
        ]);

        $filters = [
            'filters' => [
                'consumer-date-range' => [
                    'from' => now()->subDays(3)->toDateString(),
                    'to' => now()->toDateString(),
                    'preset' => null,
                ],
            ],
        ];

        $this->actingAs($this->user);

        $this->post(route('internal-api.v1.consumer-search.search'), [
            ...$filters,
            'sort' => [
                'date' => 'asc',
            ],
        ])
            ->assertOk()
            ->assertJsonPath('data.consumers.data.0.id', $consumerTwo->id)
            ->assertJsonPath('data.consumers.data.1.id', $consumerOne->id);

        $this->post(route('internal-api.v1.consumer-search.search'), [
            ...$filters,
            'sort' => [
                'date' => 'desc',
            ],
        ])
            ->assertOk()
            ->assertJsonPath('data.consumers.data.0.id', $consumerOne->id)
            ->assertJsonPath('data.consumers.data.1.id', $consumerTwo->id);
    }

    private function createFilteredInConsumer(array $consumerData = []): Consumer
    {
        $consumer = Consumer::factory()->create($consumerData);

        $industryService = IndustryService::factory()->create();

        $serviceProduct = ServiceProduct::factory()->create([
            'product_id' => Product::first()->id,
            'industry_service_id' => $industryService->id,
        ]);

        $address = Address::factory()->create();

        $website = Website::factory()->create();

        $consumerProductTracking = ConsumerProductTracking::factory()->create([
            'website_id' => $website->id,
        ]);

        $consumerProduct = ConsumerProduct::factory()->create([
            'consumer_id' => $consumer->id,
            'service_product_id' => $serviceProduct->id,
            'address_id' => $address->id,
            'consumer_product_tracking_id' => $consumerProductTracking->id,
            'created_at' => now()->subDays(1),
        ]);

        ProductAssignment::factory()->create([
            'consumer_product_id' => $consumerProduct->id,
        ]);

        return $consumer;
    }
}
