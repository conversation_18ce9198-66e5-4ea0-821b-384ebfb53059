<?php

namespace Tests\Feature\Http\Controllers\API\Users;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserBaseAPIControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_gets_user_roles_and_permissions_with_no_authenticated_user_and_redirects_to_login(): void
    {
        $this->get(route('internal-api.v1.users.get-user-roles-and-permissions'))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function it_gets_user_roles_and_permissions_with_authenticated_user_and_returns_ok_response(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('internal-api.v1.users.get-user-roles-and-permissions'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'roles' => [],
                    'permissions' => [],
                    'ids' => [
                        'user' => $user->id,
                        'accountManager' => null,
                        'successManager' => null,
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_gets_users_with_authenticated_user_and_returns_ok_response(): void
    {
        User::query()->delete();

        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('internal-api.v1.users.get-users'))
            ->assertOk()
            ->assertJsonCount(1, 'data.users')
            ->assertExactJson([
                'data' => [
                    'status' => true,
                    'users' => [
                        [
                            'created_at' => $user->created_at->toISOString(),
                            'created_by_name' => $user->createdBy?->name,
                            'email' => $user->email,
                            'force_two_factor_auth' => $user->force_two_factor_auth ?? 0,
                            'has_enabled_mailbox' => false,
                            'id' => $user->id,
                            'impersonating' => false,
                            'legacy_user_id' => null,
                            'meeting_url' => $user->meeting_url,
                            'name' => $user->name,
                            'phone' => null,
                            'roles' => [],
                            'permissions' => [],
                            'slack_username' => null,
                            'timezone' => $user->timezone ?? "-07:00",
                            'updated_at' => $user->updated_at->toISOString(),
                            'updated_by_name' => $user->updatedBy?->name,
                            'uses_mailbox' => 0,
                            'verified_2fa' => 0,
                        ],
                    ],
                ],
            ]);
    }

    #[Test]
    public function it_gets_the_authenticated_user_and_returns_ok_response(): void
    {
        $user = User::factory()->create();

        $this->actingAs($user)
            ->get(route('internal-api.v1.users.get-logged-user'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    'created_at' => $user->created_at->toISOString(),
                    'created_by_name' => $user->createdBy?->name,
                    'email' => $user->email,
                    'force_two_factor_auth' => $user->force_two_factor_auth,
                    'has_enabled_mailbox' => false,
                    'id' => $user->id,
                    'impersonating' => false,
                    'legacy_user_id' => null,
                    'meeting_url' => $user->meeting_url,
                    'name' => $user->name,
                    'phone' => null,
                    'roles' => [],
                    'permissions' => [],
                    'slack_username' => null,
                    'timezone' => $user->timezone,
                    'updated_at' => $user->updated_at->toISOString(),
                    'updated_by_name' => $user->updatedBy?->name,
                    'uses_mailbox' => null,
                    'verified_2fa' => null,
                ],
            ]);
    }
}
