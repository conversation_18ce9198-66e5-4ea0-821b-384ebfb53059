<?php

declare(strict_types=1);

namespace Tests\Feature\Http\Controllers\CompanyMetrics;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyMetricsControllerTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected CompanyMetric $companyMetric;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();

        IndustryService::factory()->create();

        $this->company = Company::factory()->createQuietly();

        $this->user = User::factory()->create();
    }

    #[Test]
    public function it_gets_company_ppc_spend_data()
    {
        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-company-ppc-spend-data', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => false,
                    'avg_spend' => null,
                    'breakdown' => [],
                    'last_calculated_at' => null,
                ],
            ]);
    }

    #[Test]
    public function it_gets_company_ppc_spend_data_with_data()
    {
        $this->companyMetric = CompanyMetric::factory()->create([
            'company_id' => $this->company->id,
            'request_type' => CompanyMetricRequestTypes::PPC_SPEND->value,
            'request_response' => [
                'monthlySpend' => 1000,
                'year' => 2021,
                'month' => 1,
            ],
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.companies.get-company-ppc-spend-data', ['company_id' => $this->company->id]))
            ->assertOk()
            ->assertJson([
                'data' => [
                    'status' => true,
                    'avg_spend' => '$1,000.00',
                    'breakdown' => [
                        [
                            'spent' => '$1,000.00',
                            'period' => 'Jan 2021',
                        ],
                    ],
                    'last_calculated_at' => now()->format('F j Y'),
                ],
            ]);
    }
}
