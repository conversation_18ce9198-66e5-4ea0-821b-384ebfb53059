<?php

namespace Tests\Feature\Http\Controllers\DashboardAPI\V4;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Dashboard\JWT\DashboardTokenModel;
use Database\Seeders\IndustriesSeeder;
use Database\Seeders\IndustryServicesSeeder;
use Database\Seeders\ProductsSeeder;
use Database\Seeders\ServiceProductsSeeder;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ProductAssignmentControllerTest extends TestCase
{
    use DatabaseTruncation, RefreshDatabase;

    protected Company $company;

    protected Product $product;

    protected Industry $industry;

    protected IndustryService $industryService;

    protected ConsumerProduct $consumerProduct;

    protected ServiceProduct $serviceProduct;

    protected EloquentQuoteCompany $legacyProductAssignment;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        EloquentQuoteCompany::query()->truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        $this->seed(IndustriesSeeder::class);

        $this->seed(IndustryServicesSeeder::class);

        $this->seed(ProductsSeeder::class);

        $this->seed(ServiceProductsSeeder::class);
    }

    #[Test]
    public function it_searches_for_product_assignments()
    {
        $this->company = Company::factory()->createQuietly();

        $mock = $this->partialMock(DashboardJWTService::class);

        $mock->shouldReceive('validate')->andReturn(true);

        $user = CompanyUser::factory()->create([
            'company_id' => $this->company->id,
        ]);

        $mock->shouldReceive('decode')->andReturn(new DashboardTokenModel($user->id));

        $this->industryService = IndustryService::query()->first();

        $this->serviceProduct = ServiceProduct::query()->where('industry_service_id', $this->industryService->id)->first();

        $this->consumerProduct = ConsumerProduct::factory()->createQuietly([
            'service_product_id' => $this->serviceProduct->id,
        ]);

        $this->legacyProductAssignment = app(EloquentQuoteCompany::class);

        $this->legacyProductAssignment->soldstatus = EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_PREMIUM;

        $this->legacyProductAssignment->save();

        $this->assertDatabaseCount('tblquotecompany', 1, 'readonly');

        ProductAssignment::factory()->createQuietly([
            'company_id' => $this->company->id,
            'delivered' => true,
            'legacy_id' => $this->legacyProductAssignment->quotecompanyid,
            'consumer_product_id' => $this->consumerProduct->id,
        ]);

        $this->withHeader('X-CLIENT-BEARER', (new DashboardJWTService('test', 5000))->generate($user->id))
            ->get(route('dashboard-api.v4.products.companies.product-assignments.search', [
                'productKey' => $this->serviceProduct->product->name,
                'companyId' => $this->company->id,
                'industry' => $this->industryService->industry->slug,
                'service' => $this->industryService->slug,
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'product_assignments' => [
                        'data' => [
                            '*' => [
                                'id',
                                'legacy_id',
                                'consumer_product_id',
                                'property_type_id',
                                'date_sent',
                                'status',
                                'source',
                                'name',
                                'address',
                                'street_address',
                                'city',
                                'state',
                                'zip_code',
                                'email',
                                'phone',
                                'cost',
                                'effective_cost',
                                'chargeable',
                                'exclude_budget',
                                'best_time_to_call',
                                'appointment_status',
                                'single_product_sale',
                                'rejection_expiry',
                                'rejection_timer',
                                'rejected',
                                'rejection_reason',
                                'rejected_time',
                            ],
                        ],
                        'current_page',
                        'first_page_url',
                        'from',
                        'last_page',
                        'last_page_url',
                        'links' => [
                            [
                                'url',
                                'label',
                                'active',
                            ],
                        ],
                        'next_page_url',
                        'path',
                        'per_page',
                        'prev_page_url',
                        'to',
                        'total',
                    ],
                    'total_spend',
                ],
            ])
            ->assertJsonCount(1, 'data.product_assignments.data');
    }
}
