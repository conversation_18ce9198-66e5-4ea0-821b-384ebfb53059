<?php

namespace Feature\Http\Controllers;

use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class CompanyCampaignContactDeliveryLogControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_lists_delivery_logs_and_returns_200_status(): void
    {
        CompanyCampaignContactDeliveryLog::factory()->createQuietly();

        $user = User::factory()->create()->givePermissionTo(Permission::findOrCreate('company-campaign-delivery-log/view'));

        $this->actingAs($user)
            ->get(route('internal-api.v1.contact-delivery-logs.list'))
            ->assertOk()
            ->assertExactJsonStructure([
                'data' => [
                    '*' => [
                        'campaign',
                        'consumer_product',
                        'company',
                        'date',
                        'id',
                        'method',
                        'payload',
                        'success',
                    ],
                ],
                'links',
                'meta',
            ]);
    }

    #[Test, DataProvider('delivery_status_filters')]
    public function it_lists_delivery_logs_and_filters_on_delivery_status_and_returns_200_status(bool $success, bool $filter, int $count): void
    {
        CompanyCampaignContactDeliveryLog::factory()->createQuietly(compact('success'));

        $user = User::factory()->create()->givePermissionTo(Permission::findOrCreate('company-campaign-delivery-log/view'));

        $this->actingAs($user)
            ->get(route('internal-api.v1.contact-delivery-logs.list', [
                'delivery_status' => $filter,
            ]))
            ->assertOk()
            ->assertJsonCount($count, 'data');
    }

    public static function delivery_status_filters(): array
    {
        return [
            'filtering successful log by true' => [true, true, 1],
            'filtering successful log by false' => [true, false, 0],
            'filtering failed log by true' => [false, true, 0],
            'filtering failed log by false' => [false, false, 1],
        ];
    }

    #[Test, DataProvider('date_filters')]
    public function it_lists_delivery_logs_and_filters_on_date_and_returns_200_status(Carbon $date, Carbon $filterFrom, Carbon $filterTo, int $count): void
    {
        CompanyCampaignContactDeliveryLog::factory()->createQuietly([
            'created_at' => $date,
        ]);

        $user = User::factory()->create()->givePermissionTo(Permission::findOrCreate('company-campaign-delivery-log/view'));

        $this->actingAs($user)
            ->get(route('internal-api.v1.contact-delivery-logs.list', [
                'date' => [
                    'from' => $filterFrom->format('Y-m-d'),
                    'to' => $filterTo->format('Y-m-d'),
                ],
            ]))
            ->assertOk()
            ->assertJsonCount($count, 'data');
    }

    public static function date_filters(): array
    {
        return [
            'filtering within range' => [Carbon::parse('2025-02-01'), Carbon::parse('2025-01-01'), Carbon::parse('2025-02-28'), 1],
            'filtering within range (inclusive)' => [Carbon::parse('2025-02-01'), Carbon::parse('2025-02-01'), Carbon::parse('2025-02-01'), 1],
            'filtering outside of range (start)' => [Carbon::parse('2025-02-01'), Carbon::parse('2025-02-02'), Carbon::parse('2025-02-28'), 0],
            'filtering outside of range (end)' => [Carbon::parse('2025-03-01'), Carbon::parse('2025-02-01'), Carbon::parse('2025-02-28'), 0],
        ];
    }
}
