<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserSettingsControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_edits_user_profile(): void
    {
        $user = User::factory()->createQuietly();

        $role = Role::findOrCreate('any-random-role');

        $user->assignRole($role);

        $this->actingAs($user)->get(route('edit-profile'))
            ->assertOk()
            ->assertViewIs('edit-user-profile')
            ->assertViewHas('user', $user)
            ->assertViewHas('userRoleAndPermissions', [
                'any-random-role' => [],
            ]);
    }
}
