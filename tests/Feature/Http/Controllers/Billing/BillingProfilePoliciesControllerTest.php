<?php

namespace Tests\Feature\Http\Controllers\Billing;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class BillingProfilePoliciesControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function create_profile_policy(): void
    {
        $user = User::factory()->create()->givePermissionTo(Permission::findOrCreate('billing-profile-policies/save'));

        $this->actingAs($user)
            ->post(route('v1.billing.prefix-policies.create-profile-policy', [
                'event' => BillingPolicyEventType::INVOICE_ISSUED->value,
                'action' => BillingPolicyActionType::SEND_EMAIL_NOTIFICATION->value,
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    'status' => [
                        'action_data',
                        'action_class',
                        'event_class',
                        'sort_order',
                        'billing_profile_id',
                        'updated_at',
                        'created_at',
                        'id',
                    ],
                ],
            ]);
    }
}
