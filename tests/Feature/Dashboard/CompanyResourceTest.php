<?php

namespace Tests\Feature\Dashboard;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyResourceTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected function setUp(): void
    {
        parent::setUp();

        $this->company = Company::factory()->createQuietly();

        $user = CompanyUser::factory()->for($this->company)->create();

        $this->withHeader('X-CLIENT-BEARER', app(DashboardJWTService::class)->generate($user->id));

        Role::findOrCreate('business-development-manager');
        Role::findOrCreate('account-manager');
    }

    #[Test]
    public function it_provides_a_business_contact_for_a_bdm()
    {
        $bdm = User::factory()->withRole('business-development-manager')->createQuietly();

        $this->company->assign($bdm)->as('business-development-manager');

        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => [
                    'type' => str('business-development-manager')->headline(),
                    'name' => $bdm->name,
                    'email' => $bdm->email,
                    'phone' => $bdm->phone,
                ],
            ]);
    }

    #[Test]
    public function it_provides_a_business_contact_for_an_account_manager()
    {
        $am = User::factory()->withRole('account-manager')->createQuietly();

        $this->company->assign($am)->as('account-manager');

        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => [
                    'type' => str('account-manager')->headline(),
                    'name' => $am->name,
                    'email' => $am->email,
                    'phone' => $am->phone,
                ],
            ]);
    }

    #[Test]
    public function it_provides_a_business_contact_for_a_bdm_prior_to_90_days()
    {
        $bdm = User::factory()->withRole('business-development-manager')->createQuietly();
        $am = User::factory()->withRole('account-manager')->createQuietly();

        $this->company->assign($bdm)->as('business-development-manager');
        $this->company->assign($am)->as('account-manager');

        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => [
                    'type' => str('business-development-manager')->headline(),
                    'name' => $bdm->name,
                    'email' => $bdm->email,
                    'phone' => $bdm->phone,
                ],
            ]);
    }

    #[Test]
    public function it_provides_a_business_contact_for_an_account_manager_after_90_days()
    {
        $bdm = User::factory()->withRole('business-development-manager')->createQuietly();
        $am = User::factory()->withRole('account-manager')->createQuietly();

        $this->company->assign($bdm)->as('business-development-manager');
        $this->company->assign($am)->as('account-manager');

        $this->company->currentAssignment('business-development-manager')->update([
            'created_at' => now()->subDay(90),
        ]);

        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => [
                    'type' => str('account-manager')->headline(),
                    'name' => $am->name,
                    'email' => $am->email,
                    'phone' => $am->phone,
                ],
            ]);
    }

    #[Test]
    public function it_provides_no_business_contact_if_none_exist()
    {
        $this->get(route('dashboard-api.v3.companies.show', ['companyId' => $this->company->id]))
            ->assertOk()
            ->assertJsonFragment([
                'business_contact' => null,
            ]);
    }
}
