<?php

namespace Tests\Feature\ShortcodeReplacer;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\Shortcode\ShortcodeReplacerService;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class ShortcodeReplacerTest extends TestCase
{
    use RefreshDatabase;

    const array SHORTCODES = [
        'id' => 123,
        'first_name' => 'John',
        'last_name' => 'Doe',
        'address' => [
            'street_number' => 123,
            'post_code' => 5000,
            'street' => 'Charles Road',
        ],
        'total_spent' => '$100',
        'total' => '$100',
        'discount' => '50%',
        'path' => "\\/var\\\/www",
        'pattern' => '$(abc|def|{{.]]*)^\\',
    ];

    /**
     * A basic unit test example.
     */
    #[DataProvider('dataProvider')]
    public function test_valid_shortcode_replacement(string $input, string $expected): void
    {
        $replacerService = new ShortcodeReplacerService;

        $this->assertEquals($expected, $replacerService->process($input, self::SHORTCODES));
    }

    public function test_eager_loaded_model_relations_are_shortcodified()
    {
        $rawText = '{consumer.first_name} with eager loaded relation allows for nested shortcodes of all eager loaded relation fields {consumer.consumer_products.consumer_id}';

        /** @var Consumer $consumer */
        $consumer = Consumer::factory()
            ->has(ConsumerProduct::factory()->count(1), Consumer::RELATION_CONSUMER_PRODUCT)
            ->create();

        $consumerWithConsumerProduct = Consumer::query()->with(Consumer::RELATION_CONSUMER_PRODUCT)->find($consumer->id);

        $shortcodes = [
            'consumer' => $consumerWithConsumerProduct,
        ];

        $replacerService = new ShortcodeReplacerService;

        $this->assertEquals("$consumer->first_name with eager loaded relation allows for nested shortcodes of all eager loaded relation fields $consumer->id", $replacerService->process($rawText, $shortcodes));
    }

    /**
     * A basic unit test example.
     */
    public function test_missing_shortcode(): void
    {
        $rawText = 'This {unknown} Shortcode should be caught and throw exception';

        $replacerService = new ShortcodeReplacerService;

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Not all shortcodes matched. Unmatched shortcodes: ["unknown"]');

        $replacerService->process($rawText, self::SHORTCODES);
    }

    public static function dataProvider(): array
    {
        return [
            [
                'Testing No Shortcodes',
                'Testing No Shortcodes',
            ],
            [
                'Hi {first_name} {last_name}',
                'Hi John Doe',
            ],
            [
                'Hi {first_name} {last_name} located at {address.street_number} {address.street}',
                'Hi John Doe located at 123 Charles Road',
            ],
            [
                'Hi {firstName} {last-name}', 'Hi John Doe',
            ],
            [
                'Hi {firstName} {last-name}, you have spent {total_spent} with {discount} discount',
                'Hi John Doe, you have spent $100 with 50% discount',
            ],
            ['path {path}', "path \\/var\\\/www"],
            ['regex test {pattern}', 'regex test $(abc|def|{{.]]*)^\\'],
            [
                file_get_contents(__DIR__.'/stubs/raw_template.html'),
                file_get_contents(__DIR__.'/stubs/expected_template.html'),
            ],
        ];
    }
}
