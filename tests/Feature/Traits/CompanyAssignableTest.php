<?php

namespace Tests\Feature\Traits;

use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyAssignableTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Role::findOrCreate('account-manager');
        Role::findOrCreate('business-development-manager');
        Role::findOrCreate('customer-success-manager');
        Role::findOrCreate('sales-development-representative');
        Role::findOrCreate('onboarding-manager');
    }

    #[Test]
    public function a_user_can_list_any_assigned_companies(): void
    {
        $user = User::factory()->createQuietly();

        $companyOne = Company::factory()->createQuietly();

        $companyOne->assign($user)->as('account-manager');

        $companyTwo = Company::factory()->createQuietly();

        $companyTwo->assign($user)->as('business-development-manager');

        $this->assertNotEmpty($user->assignedCompanies);
        $this->assertCount(2, $user->assignedCompanies);
        $this->assertTrue($user->assignedCompanies->contains($companyOne));
        $this->assertTrue($user->assignedCompanies->contains($companyTwo));
    }

    #[Test, DataProvider('relationshipProvider')]
    public function a_user_can_list_the_companies_where_they_are_assigned_as_an($role, $relationship): void
    {
        $user = User::factory()->createQuietly();
        $company = Company::factory()->createQuietly();

        $company->assign($user)->as($role);

        $this->assertNotEmpty($user->{$relationship});
        $this->assertTrue($user->{$relationship}->contains($company));
    }

    #[Test]
    public function a_user_does_not_see_any_inactive_assignments(): void
    {
        $user = User::factory()->createQuietly();

        $company = Company::factory()->createQuietly();

        $company->assign($user)->as('account-manager');

        $relationship = $user->companyRelationships->first();

        $this->assertNotSoftDeleted($relationship);
        $this->assertNotEmpty($user->assignedCompanies);
        $this->assertCount(1, $user->assignedCompanies);
        $this->assertTrue($user->assignedCompanies->contains($company));

        $company->assign(User::factory()->create())->as('account-manager');

        $this->assertSoftDeleted($relationship);
        $this->assertEmpty($user->refresh()->assignedCompanies);
    }

    #[Test, DataProvider('scopeProvider')]
    public function a_user_can_be_scoped_by($role, $scope)
    {
        $user = User::factory()->withRole($role)->create();
        $ignoredUser = User::factory()->create();

        $this->assertCount(1, User::{$scope}()->get());
        $this->assertTrue(User::{$scope}()->get()->contains($user));
    }

    public static function relationshipProvider()
    {
        return [
            'Account Manager' => [
                'account-manager',
                'accountManagerCompanies',
            ],
            'Business Development Manager' => [
                'business-development-manager',
                'businessDevelopmentManagerCompanies',
            ],
            'Customer Success Manager' => [
                'customer-success-manager',
                'customerSuccessManagerCompanies',
            ],
            'Sales Development Representative' => [
                'sales-development-representative',
                'salesDevelopmentRepresentativeCompanies',
            ],
            'Onboarding Manager' => [
                'onboarding-manager',
                'onboardingManagerCompanies',
            ],
        ];
    }

    public static function scopeProvider()
    {
        return [
            'Account Manager' => [
                'account-manager',
                'accountManagerRole',
            ],
            'Business Development Manager' => [
                'business-development-manager',
                'businessDevelopmentManagerRole',
            ],
            'Customer Success Manager' => [
                'customer-success-manager',
                'customerSuccessManagerRole',
            ],
            'Sales Development Representative' => [
                'sales-development-representative',
                'salesDevelopmentRepresentativeRole',
            ],
            'Onboarding Manager' => [
                'onboarding-manager',
                'onboardingManagerRole',
            ],
        ];
    }
}
