<?php

namespace Tests\Feature\Traits;

use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class HasUserAssignmentsTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Company $company;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->createQuietly();
        $this->company = Company::factory()->createQuietly();

        Role::findOrCreate('account-manager');
        Role::findOrCreate('business-development-manager');
        Role::findOrCreate('customer-success-manager');
        Role::findOrCreate('sales-development-representative');
    }

    #[Test, DataProvider('rolesProvider')]
    public function a_user_can_be_assigned_to_a_company_as_a(string $role): void
    {
        $this->company->assign($this->user)->as($role);

        $this->assertDatabaseHas('company_user_relationships', [
            'user_id' => $this->user->id,
            'company_id' => $this->company->id,
            'role_id' => Role::findByName($role)->id,
        ]);
    }

    #[Test, DataProvider('roleAssignmentConvenienceMethodsProvider')]
    public function a_user_can_be_assigned_to_a_company_as_a_role_using_a_convenience_method(string $role, string $roleConvenienceMethod): void
    {
        $this->company->assign($this->user)->{$roleConvenienceMethod}();

        $this->assertDatabaseHas('company_user_relationships', [
            'user_id' => $this->user->id,
            'company_id' => $this->company->id,
            'role_id' => Role::findByName($role)->id,
        ]);
    }

    #[Test]
    public function a_company_can_list_all_of_its_assignments(): void
    {
        $userTwo = User::factory()->createQuietly();
        $userThree = User::factory()->createQuietly();

        $this->company->assign($this->user)->as('account-manager');
        $this->company->assign($userTwo)->as('business-development-manager');
        $this->company->assign($userThree)->as('customer-success-manager');
        $this->company->assign($userTwo)->as('sales-development-representative');

        $this->assertNotEmpty($this->company->assignments);
        $this->assertCount(4, $this->company->assignments);
    }

    #[Test, DataProvider('rolesProvider')]
    public function a_company_can_reference_a_specific_assignment_by_role($role, $assignment): void
    {
        $this->company->assign($this->user)->as($role);

        $this->assertNotNull($this->company->{$assignment});
        $this->assertTrue($this->company->{$assignment}->is($this->user));
    }

    #[Test]
    public function a_company_can_delete_an_assignment()
    {
        $this->company->assign($this->user)->as('account-manager');

        $relationship = $this->company->userRelationships()->first();

        $this->assertNotNull($this->company->accountManager);
        $this->assertNotSoftDeleted($relationship);

        $this->company->unassign('account-manager');

        $this->assertSoftDeleted($relationship);
        $this->assertNull($this->company->refresh()->accountManager);
    }

    #[Test, DataProvider('roleUnassignmentConvenienceMethodsProvider')]
    public function a_company_can_delete_an_assignment_using_convenience_method(string $role, string $roleRelation, string $roleUnassignConvenienceMethod): void
    {
        $this->company->assign($this->user)->as($role);

        $relationship = $this->company->userRelationships()->first();

        $this->assertNotNull($this->company->{$roleRelation});
        $this->assertNotSoftDeleted($relationship);

        $this->company->{$roleUnassignConvenienceMethod}();

        $this->assertSoftDeleted($relationship);
        $this->assertNull($this->company->refresh()->{$roleRelation});
    }

    #[Test]
    public function a_company_can_determine_if_an_assignment_exists(): void
    {
        $this->company->assign($this->user)->as('account-manager');

        $this->assertTrue($this->company->hasAssignment('account-manager'));
        $this->assertFalse($this->company->hasAssignment('business-development-manager'));
    }

    #[Test]
    public function a_company_can_determine_which_user_is_currently__assigned(): void
    {
        $this->company->assign($this->user)->as('account-manager');

        $this->assertTrue($this->company->currentlyAssigned('account-manager')->is($this->user));

        $this->company->unassign('account-manager');

        $this->assertNull($this->company->currentlyAssigned('account-manager'));
    }

    #[Test]
    public function assignment_attempts_to_unassign_prior_to_completion(): void
    {
        $this->company->assign($this->user)->as('account-manager');
        $oldAssignment = $this->user->companyRelationships->first();

        $this->assertTrue($this->company->hasAssignment('account-manager'));
        $this->assertTrue($this->company->accountManager->is($this->user));
        $this->assertNotSoftDeleted($oldAssignment);

        $this->company->assign(User::factory()->create())->as('account-manager');

        $this->assertSoftDeleted($oldAssignment);
        $this->assertTrue($this->company->hasAssignment('account-manager'));
        $this->assertTrue($this->company->refresh()->accountManager->isNot($this->user));
    }

    #[Test]
    public function if_unassign_fails_a_new_user_is_not_assigned(): void
    {
        $company = $this->partialMock(Company::class, function (MockInterface $mock) {
            $mock->shouldReceive('unassign')
                ->once()
                ->andThrow(Exception::class);
        });

        try {
            $company->assign($this->user)->as('business-development-manager');
        } catch (Exception $e) {
            $this->assertEmpty($this->user->businessDevelopmentManagerCompanies);
            $this->assertEmpty($this->company->businessDevelopmentManager);
        }
    }

    #[Test]
    public function any_assignment_activity_is_logged()
    {
        $this->mock(ActivityLogRepository::class)
            ->shouldReceive('createActivityLog')
            ->once();

        $this->company->assign($this->user)->as('account-manager');
    }

    #[Test]
    public function any_unassignment_activity_is_logged()
    {
        $this->mock(ActivityLogRepository::class)
            ->shouldReceive('createActivityLog')
            ->once();

        CompanyUserRelationship::factory()
            ->for($this->user)
            ->for($this->company)
            ->for(Role::findByName('account-manager'))->create();

        $this->company->unassign('account-manager');
    }

    #[Test]
    // THIS IS NO LONGER ALLOWED! ONLY ONE ROLE CAN BE ACTIVE AT A TIME!
    public function the_user_assigned_is_the_latest_available(): void
    {
        $this->markTestSkipped();

        $company = Company::factory()->createQuietly();

        $oldAccountManager = User::factory()->createQuietly();
        $latestActiveAccountManager = User::factory()->createQuietly();
        $alsoActiveAccountManager = User::factory()->createQuietly();
        $latestInactiveAccountManager = User::factory()->createQuietly();

        // CompanyUserRelationship::factory(4)->accountManager()->for($company)->sequence(
        //     ['user_id' => $oldAccountManager, 'deleted_at' => now()],
        //     ['user_id' => $alsoActiveAccountManager, 'deleted_at' => null],
        //     ['user_id' => $latestActiveAccountManager, 'deleted_at' => null],
        //     ['user_id' => $latestInactiveAccountManager, 'deleted_at' => now()],
        // )->create();

        $this->assertNotNull($company->accountManager);
        $this->assertTrue($company->accountManager->is($latestActiveAccountManager));
    }

    public static function rolesProvider(): array
    {
        return [
            'Account Manager' => [
                'account-manager',
                'accountManager',
            ],
            'Business Development Manager' => [
                'business-development-manager',
                'businessDevelopmentManager',
            ],
            'Customer Success Manager' => [
                'customer-success-manager',
                'customerSuccessManager',
            ],
            'Sales Development Representative' => [
                'sales-development-representative',
                'salesDevelopmentRepresentative',
            ],
            'Onboarding Manager' => [
                'onboarding-manager',
                'onboardingManager',
            ],
        ];
    }

    public static function roleAssignmentConvenienceMethodsProvider(): array
    {
        return [
            'Account Manager' => [
                'account-manager',
                'asAccountManager',
            ],
            'Business Development Manager' => [
                'business-development-manager',
                'asBusinessDevelopmentManager',
            ],
            'Customer Success Manager' => [
                'customer-success-manager',
                'asCustomerSuccessManager',
            ],
            'Sales Development Representative' => [
                'sales-development-representative',
                'asSalesDevelopmentRepresentative',
            ],
            'Onboarding Manager' => [
                'onboarding-manager',
                'asOnboardingManager',
            ],
        ];
    }

    public static function roleUnassignmentConvenienceMethodsProvider(): array
    {
        return [
            'Account Manager' => [
                'account-manager',
                'accountManager',
                'unassignAccountManager',
            ],
            'Business Development Manager' => [
                'business-development-manager',
                'businessDevelopmentManager',
                'unassignBusinessDevelopmentManager',
            ],
            'Customer Success Manager' => [
                'customer-success-manager',
                'customerSuccessManager',
                'unassignCustomerSuccessManager',
            ],
            'Sales Development Representative' => [
                'sales-development-representative',
                'salesDevelopmentRepresentative',
                'unassignSalesDevelopmentRepresentative',
            ],
            'Onboarding Manager' => [
                'onboarding-manager',
                'onboardingManager',
                'unassignOnboardingManager',
            ],
        ];
    }
}
