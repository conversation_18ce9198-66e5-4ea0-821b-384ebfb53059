<?php

namespace Tests\Feature;

use App\Enums\LegacyQuoteOrigin;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\TestProducts\TestProductGeneratorService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Str;
use Tests\TestCase;

class TestProductTest extends TestCase
{
    use WithFaker;

    protected TestProductGeneratorService $testProductGeneratorService;

    protected Industry $solarIndustry;

    protected IndustryService $solarIndustryService;

    protected Industry $roofingIndustry;

    protected IndustryService $roofingIndustryService;

    protected Industry $kitchenIndustry;

    protected IndustryService $kitchenIndustryService;

    protected function setUp(): void
    {

        parent::setUp();

        $this->kitchenIndustry = Industry::firstOrCreate(
            $attributes = [
                Industry::FIELD_NAME => 'Kitchen',
                Industry::FIELD_SLUG => Str::slug('Kitchen'),
            ],
            Industry::factory($attributes)->raw()
        );

        $this->solarIndustry = Industry::firstOrCreate(
            $attributes = [
                Industry::FIELD_NAME => 'Solar',
                Industry::FIELD_SLUG => Str::slug('Solar'),
            ],
            Industry::factory($attributes)->raw()
        );

        $this->roofingIndustry = Industry::firstOrCreate(
            $attributes = [
                Industry::FIELD_NAME => 'Roofing',
                Industry::FIELD_SLUG => Str::slug('Roofing'),
            ],
            Industry::factory($attributes)->raw()
        );

        $this->solarIndustryService = IndustryService::factory([
            IndustryService::FIELD_INDUSTRY_ID => $this->solarIndustry->id,
        ])->make();

        $this->roofingIndustryService = IndustryService::factory([
            IndustryService::FIELD_INDUSTRY_ID => $this->roofingIndustry->id,
        ])->make();

        $this->kitchenIndustryService = IndustryService::factory([
            IndustryService::FIELD_INDUSTRY_ID => $this->kitchenIndustry->id,
        ])->make();
    }

    /**
     * Solar Test leads must have a utility set to 'Other', an electric spend and must own the property
     *
     * @throws BindingResolutionException
     */
    public function test_generate_consumer_payload_for_solar_services(): void
    {

        $this->testProductGeneratorService = app()->make(TestProductGeneratorService::class);
        $payload = $this->testProductGeneratorService->findValidProductDataPayload(service: $this->solarIndustryService);

        $this->assertEquals('Other', $payload[SolarConfigurableFields::UTILITY_NAME->value]);
        $this->assertIsNumeric($payload[SolarConfigurableFields::ELECTRIC_COST->value]);
        $this->assertTrue($payload[GlobalConfigurableFields::OWN_PROPERTY->value]);
        $this->assertEquals(LegacyQuoteOrigin::SE->value, $payload[GlobalConfigurableFields::ORIGIN->value]);
    }

    public function test_fetch_correct_lead_origin_for_roofing_service(): void
    {
        $this->testProductGeneratorService = app()->make(TestProductGeneratorService::class);
        $payload = $this->testProductGeneratorService->findValidProductDataPayload(service: $this->roofingIndustryService);

        $this->assertEquals(LegacyQuoteOrigin::RC->value, $payload[GlobalConfigurableFields::ORIGIN->value]);
    }

    public function test_fetch_correct_lead_origin_for_random_service(): void
    {
        $this->testProductGeneratorService = app()->make(TestProductGeneratorService::class);
        $payload = $this->testProductGeneratorService->findValidProductDataPayload(service: $this->kitchenIndustryService);

        $this->assertEquals(LegacyQuoteOrigin::FIXR->value, $payload[GlobalConfigurableFields::ORIGIN->value]);
    }
}
