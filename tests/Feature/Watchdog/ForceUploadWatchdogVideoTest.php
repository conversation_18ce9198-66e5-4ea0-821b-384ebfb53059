<?php

declare(strict_types=1);

namespace Tests\Feature\Watchdog;

use App\Actions\ForceUploadWatchdogVideo;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class ForceUploadWatchdogVideoTest extends TestCase
{
    use RefreshDatabase;

    protected string $url;

    protected ForceUploadWatchdogVideo $action;

    protected string $video = '123';

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('watchdog.url', 'http://127.0.0.1:8000');
        Config::set('watchdog.token', 'test');

        $this->url = rtrim(config('watchdog.url'), '/')."/api/videos/$this->video/upload";

        $this->action = app(ForceUploadWatchdogVideo::class);
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function json_is_expected_to_be_returned(): void
    {
        Http::fake([$this->url => Http::response()]);

        $this->action->handle($this->video);

        Http::assertSent(function (Request $request) {
            return $request->hasHeader('Accept', 'application/json');
        });
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function a_server_error_is_returned_given_no_url(): void
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('No url given');

        Config::set('watchdog.url');

        $fake = Http::fake([$this->url]);

        $this->action->handle($this->video);

        $fake->assertNothingSent();
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function a_server_error_is_returned_given_no_token(): void
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('No access token given');

        Config::set('watchdog.token');

        $fake = Http::fake([$this->url]);

        $this->action->handle($this->video);

        $fake->assertNothingSent();
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function unauthorized_is_returned_given_an_invalid_token(): void
    {
        Http::fake([
            $this->url => Http::response('Unauthenticated.', 401),
        ]);

        $response = $this->action->handle($this->video);

        $this->assertEquals(424, $response->status());
        $this->assertEquals([
            'watchdog_server_json' => null,
            'watchdog_server_body' => 'Unauthenticated.',
            'watchdog_server_status' => 401,
        ], $response->getData(true));
    }
}
