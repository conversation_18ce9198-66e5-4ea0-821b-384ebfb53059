<?php

namespace Tests\Feature\Watchdog;

use App\Actions\GetWatchdogPlaybackUrl;
use App\Models\Odin\Consumer;
use App\Models\WatchdogVideo;
use Exception;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TestCase;

class GetWatchdogPlaybackUrlTest extends TestCase
{
    use RefreshDatabase;

    protected string $playbackRequestUrl;

    protected GetWatchdogPlaybackUrl $action;

    protected string $source = '123';

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('watchdog.url', 'http://127.0.0.1:8000');
        Config::set('watchdog.token', 'test');

        $this->playbackRequestUrl = rtrim(config('watchdog.url'), '/')."/api/videos/$this->source/playback";

        $this->action = app(GetWatchdogPlaybackUrl::class);
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function json_is_expected_to_be_returned(): void
    {
        Http::fake([$this->playbackRequestUrl => Http::response()]);

        $this->action->handle($this->source);

        Http::assertSent(function (Request $request) {
            return $request->hasHeader('Accept', 'application/json');
        });
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function a_url_is_returned_given_a_valid_argument(): void
    {
        $url = rtrim(config('watchdog.url'), '/').'/videos/01J1QVSG0CE0K39XS96SVAP9GZ?expires=1720448154&signature=8b761526a67143ba630660e0d8744558e176b2cb0b70859c433c3ee58a1218b5';

        Http::fake([$this->playbackRequestUrl => Http::response(compact('url'))]);

        $response = $this->action->handle($this->source);

        $this->assertEquals(200, $response->status());
        $this->assertEquals(['data' => compact('url')], $response->getData(true));
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function a_server_error_is_returned_given_no_url(): void
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('No url given');

        Config::set('watchdog.url');

        $fake = Http::fake([$this->playbackRequestUrl]);

        $this->action->handle($this->source);

        $fake->assertNothingSent();
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function a_server_error_is_returned_given_no_token(): void
    {
        $this->expectException(HttpException::class);
        $this->expectExceptionMessage('No access token given');

        Config::set('watchdog.token');

        $fake = Http::fake([$this->playbackRequestUrl]);

        $this->action->handle($this->source);

        $fake->assertNothingSent();
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function not_acceptable_is_returned_given_a_source_to_an_invalid_video(): void
    {
        Http::fake([
            $this->playbackRequestUrl => Http::response('Cannot generate a link to play the video', 406),
        ]);

        $response = $this->action->handle($this->source);

        $this->assertEquals(424, $response->status());
        $this->assertEquals([
            'watchdog_server_json' => null,
            'watchdog_server_body' => 'Cannot generate a link to play the video',
            'watchdog_server_status' => 406,
        ], $response->getData(true));
    }

    /**
     * @throws Exception
     */
    #[Test]
    public function unauthorized_is_returned_given_an_invalid_token(): void
    {
        Http::fake([
            $this->playbackRequestUrl => Http::response('Unauthenticated.', 401),
        ]);

        $response = $this->action->handle($this->source);

        $this->assertEquals(424, $response->status());
        $this->assertEquals([
            'watchdog_server_json' => null,
            'watchdog_server_body' => 'Unauthenticated.',
            'watchdog_server_status' => 401,
        ], $response->getData(true));
    }

    #[Test]
    public function if_a_video_is_not_found_delete_all_associated_watchdog_videos(): void
    {
        WatchdogVideo::factory()->create(['consumer_id' => Consumer::factory()->create(['reference' => $this->source])->id]);

        Http::fake([
            $this->playbackRequestUrl => Http::response('Video not found', 404),
        ]);

        $this->action->handle($this->source);

        $this->assertDatabaseEmpty('watchdog_videos');
    }

    #[Test]
    public function if_a_video_is_anything_but_not_found_keep_all_associated_watchdog_videos(): void
    {
        WatchdogVideo::factory()->create(['consumer_id' => Consumer::factory()->create(['reference' => $this->source])->id]);

        Http::fake([
            $this->playbackRequestUrl => Http::response('Server error', 500),
        ]);

        $this->action->handle($this->source);

        $this->assertDatabaseCount('watchdog_videos', 1);
    }
}
