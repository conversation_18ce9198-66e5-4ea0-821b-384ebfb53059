<?php

namespace Tests\Feature\Watchdog;

use App\Actions\GetWatchdogPlaybackUrl;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Config;
use PHPUnit\Framework\Attributes\Test;
use Pusher\Pusher;
use Tests\TestCase;

class VideoUrlFromProductAssignmentControllerTest extends TestCase
{
    use RefreshDatabase;

    protected string $url;

    #[Test]
    public function it_calls_the_get_watchdog_playback_url_action(): void
    {
        Config::set('watchdog.url', 'http://127.0.0.1:8000');
        Config::set('watchdog.token', 'test');

        $response = [
            'data' => [
                'url' => rtrim(config('watchdog.url'), '/').'/videos/01J1QVSG0CE0K39XS96SVAP9GZ',
            ],
        ];

        $this->mock(Pusher::class);

        $this->mock(GetWatchdogPlaybackUrl::class)
            ->expects('handle')
            ->once()
            ->andReturn(new JsonResponse($response));

        $productAssignment = ProductAssignment::factory()->for(
            ConsumerProduct::factory()->for(Consumer::factory()->create())->lead()->create()
        )->create();

        $user = CompanyUser::factory()->create();

        $this->actingAs($user)
            ->withHeader('X-CLIENT-BEARER', app(DashboardJWTService::class)->generate($user->id))
            ->getJson(route('dashboard-api.v3.playback.watchdog', $productAssignment))
            ->assertOk()
            ->assertJson($response);
    }
}
