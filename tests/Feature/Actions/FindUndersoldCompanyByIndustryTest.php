<?php

namespace Tests\Feature\Actions;

use App\Actions\Company\FindUndersoldCompanyByIndustry;
use App\Enums\Odin\Industry as OdinIndustry;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\Industry;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use SolarInvestments\Testing\SkipTestWhenRunningCI;
use Tests\TestCase;

class FindUndersoldCompanyByIndustryTest extends TestCase
{
    use RefreshDatabase, SkipTestWhenRunningCI;

    protected Industry $industry;

    protected function setUp(): void
    {
        parent::setUp();

        $this->industry = Industry::factory()->createQuietly([
            'name' => OdinIndustry::ROOFING->value,
            'slug' => OdinIndustry::ROOFING->getSlug(),
        ]);
    }

    #[Test]
    public function it_finds_a_company_with_no_leads_for_at_least_30_days_for_a_specific_industry()
    {
        $company = Company::factory()->createQuietly();

        CompanyIndustry::truncate();

        CompanyIndustry::factory()->createQuietly([
            'company_id' => $company->id,
            'industry_id' => $this->industry->id,
        ]);

        ProductAssignment::factory()->for($company)->chargeableAndDelivered()->create([
            'delivered_at' => now()->subDay(40),
        ]);

        $this->actingAs(User::factory()->withRole('business-development-manager')->create());

        $nextAvailableCompany = app(FindUndersoldCompanyByIndustry::class)->run(OdinIndustry::ROOFING);

        $this->assertNotNull($nextAvailableCompany);

        $this->assertTrue($nextAvailableCompany->is($company));
    }
}
