<?php

namespace Tests\Feature\Artisan;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Category;
use App\Models\Legacy\EloquentCampaign;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentUser;
use Config;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class MigrateLegacyAffiliatesTest extends TestCase
{
    use RefreshDatabase;

    private int $latestCompanyId;

    protected function setUp(): void
    {
        parent::setUp();

        $this->markTestSkipped('Migration already ran');

        if (empty($this->latestCompanyId)) {
            $this->latestCompanyId = EloquentCompany::query()->max(EloquentCompany::COMPANY_ID);
        }

        // Prevent existing legacy companies from conflicting with test companies
        EloquentCompany::query()->delete();
        EloquentCampaign::query()->delete();

        Http::preventStrayRequests();
    }

    #[Test]
    public function migrate_legacy_affiliates_successfully(): void
    {
        $companies = EloquentCompany::factory(2)
            ->affiliate()
            ->sequence(fn (Sequence $sequence) => [
                EloquentCompany::COMPANY_ID => $this->latestCompanyId + ($sequence->index + 1),
            ])
            ->has(EloquentUser::factory()->count(2), EloquentCompany::RELATION_USERS)
            ->has(EloquentCampaign::factory()->count(3), EloquentCompany::RELATION_AFFILIATE_CAMPAIGNS)
            ->create();

        Config::set('services.affiliates_portal_api.url', 'https://test.com');

        $storeUserUrl = config('services.affiliates_portal_api.url').'/users';

        Http::fake([
            $storeUserUrl => Http::response(['id' => 1]),
        ]);

        $this->artisan('legacy-migrate:affiliates');

        Http::assertSent(fn (Request $request) => $request->url() === $storeUserUrl);

        $categoryIds = Category::query()->pluck(Category::FIELD_ID, Category::FIELD_NAME)->toArray();

        $companies->each(function (EloquentCompany $legacyCompany) use ($categoryIds) {
            $this->assertDatabaseHas(Affiliate::TABLE, [
                Affiliate::FIELD_ID => $legacyCompany->{EloquentCompany::COMPANY_ID},
                Affiliate::FIELD_NAME => $legacyCompany->{EloquentCompany::COMPANY_NAME},
            ]);

            $legacyCompany->{EloquentCompany::RELATION_AFFILIATE_CAMPAIGNS}->each(function (EloquentCampaign $legacyCampaign) use ($categoryIds) {
                $this->assertDatabaseHas(Category::TABLE, [
                    Category::FIELD_ID => $categoryIds[$legacyCampaign->{EloquentCampaign::CATEGORY}],
                    Category::FIELD_NAME => $legacyCampaign->{EloquentCampaign::CATEGORY},
                    Category::FIELD_AFFILIATE_ID => $legacyCampaign->refresh()->{EloquentCampaign::COMPANY_ID},
                ]);

                $this->assertDatabaseHas(Campaign::TABLE, [
                    Campaign::FIELD_ID => $legacyCampaign->{EloquentCampaign::CAMPAIGN_ID},
                    Campaign::FIELD_AFFILIATE_ID => $legacyCampaign->{EloquentCampaign::COMPANY_ID},
                    Campaign::FIELD_NAME => $legacyCampaign->{EloquentCampaign::NAME},
                    Campaign::FIELD_CATEGORY_ID => $categoryIds[$legacyCampaign->{EloquentCampaign::CATEGORY}] ?? 0,
                    Campaign::FIELD_STATUS => $legacyCampaign->{EloquentCampaign::STATUS},
                ]);
            });
        });
    }

    #[Test]
    public function migrate_legacy_affiliates_without_campaigns_successfully(): void
    {
        $companies = EloquentCompany::factory(2)
            ->affiliate()
            ->sequence(fn (Sequence $sequence) => [
                EloquentCompany::COMPANY_ID => $this->latestCompanyId + ($sequence->index + 1),
            ])
            ->has(EloquentUser::factory()->count(2), EloquentCompany::RELATION_USERS)
            ->create();

        Config::set('services.affiliates_portal_api.url', 'https://test.com');

        $storeUserUrl = config('services.affiliates_portal_api.url').'/users';

        Http::fake([
            $storeUserUrl => Http::response(['id' => 1]),
        ]);

        $this->artisan('legacy-migrate:affiliates');

        Http::assertSent(fn (Request $request) => $request->url() === $storeUserUrl);

        $this->assertDatabaseEmpty(Campaign::TABLE);
        $this->assertDatabaseEmpty(Category::TABLE);

        $companies->each(function (EloquentCompany $legacyCompany) {
            $this->assertDatabaseHas(Affiliate::TABLE, [
                Affiliate::FIELD_ID => $legacyCompany->{EloquentCompany::COMPANY_ID},
                Affiliate::FIELD_NAME => $legacyCompany->{EloquentCompany::COMPANY_NAME},
            ]);
        });
    }

    #[Test]
    public function migrate_legacy_affiliates_without_users_successfully(): void
    {
        $companies = EloquentCompany::factory(2)
            ->affiliate()
            ->sequence(fn (Sequence $sequence) => [
                EloquentCompany::COMPANY_ID => $this->latestCompanyId + ($sequence->index + 1),
            ])
            ->has(EloquentCampaign::factory()->count(3), EloquentCompany::RELATION_AFFILIATE_CAMPAIGNS)
            ->create();

        $this->artisan('legacy-migrate:affiliates');

        Http::assertNothingSent();

        $categoryIds = Category::query()->pluck(Category::FIELD_ID, Category::FIELD_NAME)->toArray();

        $companies->each(function (EloquentCompany $legacyCompany) use ($categoryIds) {
            $this->assertDatabaseHas(Affiliate::TABLE, [
                Affiliate::FIELD_ID => $legacyCompany->{EloquentCompany::COMPANY_ID},
                Affiliate::FIELD_NAME => $legacyCompany->{EloquentCompany::COMPANY_NAME},
            ]);

            $legacyCompany->{EloquentCompany::RELATION_AFFILIATE_CAMPAIGNS}->each(function (EloquentCampaign $legacyCampaign) use ($categoryIds) {
                $this->assertDatabaseHas(Category::TABLE, [
                    Category::FIELD_ID => $categoryIds[$legacyCampaign->{EloquentCampaign::CATEGORY}],
                    Category::FIELD_NAME => $legacyCampaign->{EloquentCampaign::CATEGORY},
                    Category::FIELD_AFFILIATE_ID => $legacyCampaign->{EloquentCampaign::COMPANY_ID},
                ]);

                $this->assertDatabaseHas(Campaign::TABLE, [
                    Campaign::FIELD_ID => $legacyCampaign->{EloquentCampaign::CAMPAIGN_ID},
                    Campaign::FIELD_AFFILIATE_ID => $legacyCampaign->{EloquentCampaign::COMPANY_ID},
                    Campaign::FIELD_NAME => $legacyCampaign->{EloquentCampaign::NAME},
                    Campaign::FIELD_CATEGORY_ID => $categoryIds[$legacyCampaign->{EloquentCampaign::CATEGORY}] ?? 0,
                    Campaign::FIELD_STATUS => $legacyCampaign->{EloquentCampaign::STATUS},
                ]);
            });
        });
    }

    #[Test]
    public function migrate_legacy_affiliates_without_campaigns_and_users_successfully(): void
    {
        $companies = EloquentCompany::factory(2)
            ->affiliate()
            ->sequence(fn (Sequence $sequence) => [
                EloquentCompany::COMPANY_ID => $this->latestCompanyId + ($sequence->index + 1),
            ])
            ->create();

        $this->artisan('legacy-migrate:affiliates');

        $this->assertDatabaseEmpty(Campaign::TABLE);
        $this->assertDatabaseEmpty(Category::TABLE);
        Http::assertNothingSent();

        $companies->each(function (EloquentCompany $legacyCompany) {
            $this->assertDatabaseHas(Affiliate::TABLE, [
                Affiliate::FIELD_ID => $legacyCompany->{EloquentCompany::COMPANY_ID},
                Affiliate::FIELD_NAME => $legacyCompany->{EloquentCompany::COMPANY_NAME},
            ]);
        });
    }
}
