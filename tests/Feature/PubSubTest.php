<?php

namespace Tests\Feature;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Events\SalesBait\SalesBaitCreatedEvent;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\SalesBaitRegisteredInterest;
use App\Services\PubSub\PubSubService;
use App\Services\SalesBait\SalesBaitEventService;
use App\Services\SalesBaitService;
use Event;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

/**
 * @note
 *
 * These events should fire to the dev pubsub channel.
 * This class is just to test that the functions and methods that call the pubsub handler can run without any exceptions,
 * to verify that the event actually made it to the pubsub channel we will have to check the Cloud PubSub dashboard manually.
 */
class PubSubTest extends TestCase
{
    use RefreshDatabase;

    private PubSubService $pubSubService;

    protected function setUp(): void
    {
        parent::setUp();

        if (blank(config('services.google.pubsub.service_account'))) {
            $this->markTestSkipped('Skipping due to missing PubSub credentials.');
        }

        $this->pubSubService = app(PubSubService::class);
    }

    public function test_handle()
    {
        $messageIds = $this->pubSubService->handle(
            EventCategory::COMPANIES->value,
            EventName::BUDGET_INCREASE->value,
            [
                'company_reference' => 'ABCDEFGH',
            ]
        );

        $this->assertIsArray($messageIds);
        $this->assertNotEmpty($messageIds);
    }

    public function test_sales_bait_unsubscribed()
    {
        $salesBaitEventService = app(SalesBaitEventService::class);

        $data = [
            SalesBaitEventService::DATA_USER_TYPE => SalesBaitRegisteredInterest::TYPE_CONTACT,
            SalesBaitEventService::DATA_ID => 1,
            SalesBaitEventService::DATA_TRACKING_ID => 1,
        ];

        $salesBaitEventService->handle('unsubscribe', $data);

        // To avoid risky test warning
        $this->assertTrue(true);
    }

    public function test_sales_bait_received()
    {
        $salesBaitService = app(SalesBaitService::class);

        $lead = EloquentQuote::factory()->make();
        $lead->setRelation(EloquentQuote::RELATION_ADDRESS, EloquentAddress::factory()->make());

        $campaign = LeadCampaign::factory()->make();
        $campaign->setRelation(LeadCampaign::RELATION_COMPANY, EloquentCompany::factory()->make());

        $campaigns = collect([$campaign]);

        Event::fake();

        $salesBaitService->createSalesBaits($lead, $campaigns, null);

        Event::assertDispatched(SalesBaitCreatedEvent::class);
    }
}
