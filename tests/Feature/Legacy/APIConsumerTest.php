<?php

namespace Tests\Feature\Legacy;

use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Services\Legacy\APIConsumer;
use Illuminate\Foundation\Testing\DatabaseMigrations;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class APIConsumerTest extends TestCase
{
    use DatabaseMigrations;

    /** @var APIConsumer */
    private $APIConsumerService;

    /**
     * @return void
     *
     * @throws \Exception
     */
    public function test_authenticate()
    {
        $this->markTestSkipped('Temporarily disabling asynchronous tests');

        $tokenFromFunction = $this->APIConsumerService->authenticate();

        $this->assertNotEmpty($tokenFromFunction);
        $this->assertIsString($tokenFromFunction);

        $serviceId = ClientTokenService::where(ClientTokenService::FIELD_SERVICE_KEY, '=',
            ClientTokenService::LEGACY_API_SERVICE_KEY)->first()->{ClientTokenService::FIELD_ID};
        $tokenFromDatabase = ClientToken::where(ClientToken::FIELD_SERVICE_ID, '=', $serviceId)->first();

        $this->assertNotEmpty($tokenFromDatabase);
        $this->assertInstanceOf(ClientToken::class, $tokenFromDatabase);
        $this->assertIsString($tokenFromDatabase->{ClientToken::FIELD_CLIENT_TOKEN});

        $this->assertSame($tokenFromDatabase->{ClientToken::FIELD_CLIENT_TOKEN}, $tokenFromFunction);
    }

    /**
     * @return void
     *
     * @throws \Exception
     */
    public function test_authenticate_mock()
    {
        $this->markTestSkipped('Disabling until this can be refactored');

        $url = config('services.admin_integration.base_url').'/api/v1/*';

        Http::fake([
            $url => Http::response([
                'client_token' => 'test_token',
            ]),
        ]);

        $tokenFromFunction = $this->APIConsumerService->authenticate();

        Http::recorded(function (Request $request, Response $response) {
            return $response->successful() && $response->json('client_token') === 'test_token';
        });

        $this->assertNotEmpty($tokenFromFunction);
        $this->assertIsString($tokenFromFunction);

        $serviceId = ClientTokenService::where(ClientTokenService::FIELD_SERVICE_KEY, '=',
            ClientTokenService::LEGACY_API_SERVICE_KEY)->first()->{ClientTokenService::FIELD_ID};
        $tokenFromDatabase = ClientToken::where(ClientToken::FIELD_SERVICE_ID, '=', $serviceId)->first();

        $this->assertNotEmpty($tokenFromDatabase);
        $this->assertInstanceOf(ClientToken::class, $tokenFromDatabase);
        $this->assertIsString($tokenFromDatabase->{ClientToken::FIELD_CLIENT_TOKEN});

        $this->assertSame($tokenFromDatabase->{ClientToken::FIELD_CLIENT_TOKEN}, $tokenFromFunction);
    }

    protected function setUp(): void
    {
        // parent::setUp();
        // $this->APIConsumerService = app(APIConsumer::class);
    }

    /*
    public function test_get(): void
    {
        $response = $this->APIConsumerService->get('test_endpoint');
        $this->assertTrue($response->successful());
    }

    public function test_post(): void
    {
        $response = $this->APIConsumerService->post('test_endpoint');
        $this->assertTrue($response->successful());
    }

    public function test_patch(): void
    {
        $response = $this->APIConsumerService->patch('test_endpoint');
        $this->assertTrue($response->successful());
    }
    */
}
