<?php

namespace Tests\Feature\Listeners\CompanyRegistration\V3;

use App\Events\CompanyRegistration\V3\RegistrationCompleted;
use App\Listeners\CompanyRegistration\V3\BusinessDevelopmentManagerAssignmentListener;
use App\Mail\CompanyRegistration\V3\CompanyAssignedBusinessDevelopmentManagerNotification;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class BusinessDevelopmentManagerAssignmentListenerTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected User $user;

    protected RegistrationCompleted $event;

    protected function setUp(): void
    {
        parent::setUp();

        Mail::fake();

        $this->company = Company::factory()->createQuietly();

        $this->user = User::factory()->withRole('business-development-manager')->create();

        $this->event = new RegistrationCompleted($this->company);
    }

    #[Test]
    public function it_listens_for_a_registration_completed_event()
    {
        Event::fake();

        Event::assertListening(RegistrationCompleted::class, BusinessDevelopmentManagerAssignmentListener::class);
    }

    #[Test]
    public function it_assign_a_user_to_a_company_with_only_one_available_user(): void
    {
        app(BusinessDevelopmentManagerAssignmentListener::class)->handle($this->event);

        $this->company->refresh();

        $this->assertTrue($this->user->is($this->company->businessDevelopmentManager));

        $this->assertEquals($this->user->id, Cache::get('last_assigned_user_id_business-development-manager'));

        Mail::assertSent(
            CompanyAssignedBusinessDevelopmentManagerNotification::class,
            fn (CompanyAssignedBusinessDevelopmentManagerNotification $notification) => $notification->user->is($this->user) && $notification->company->is($this->company)
        );
    }

    #[Test]
    public function it_assign_a_user_to_a_company_via_round_robin(): void
    {
        $previouslyAssignedUser = User::factory()->withRole('business-development-manager')->create();

        Cache::put('last_assigned_user_id_business-development-manager', $previouslyAssignedUser->id);

        app(BusinessDevelopmentManagerAssignmentListener::class)->handle($this->event);

        $this->company->refresh();

        $this->assertTrue($this->user->is($this->company->businessDevelopmentManager));

        $this->assertEquals($this->user->id, Cache::get('last_assigned_user_id_business-development-manager'));

        Mail::assertSent(
            CompanyAssignedBusinessDevelopmentManagerNotification::class,
            fn (CompanyAssignedBusinessDevelopmentManagerNotification $notification) => $notification->user->is($this->user) && $notification->company->is($this->company)
        );
    }

    #[Test]
    public function it_does_not_assign_a_user_to_a_company_if_there_are_no_business_development_managers(): void
    {
        $this->user->removeRole('business-development-manager');

        $this->assertEmpty(User::businessDevelopmentManagerRole()->get());

        Log::shouldReceive('warning')->once()->with('No users found with role: business-development-manager');

        app(BusinessDevelopmentManagerAssignmentListener::class)->handle($this->event);

        $this->assertDatabaseEmpty('company_user_relationships');

        $this->assertTrue(Cache::missing('last_assigned_user_id_business-development-manager'));

        Mail::assertNothingSent();
    }
}
