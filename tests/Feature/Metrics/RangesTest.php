<?php

namespace Tests\Feature\Metrics;

use App\Enums\Metrics\MetricRange;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class RangesTest extends TestCase
{
    protected $route;

    protected function setUp(): void
    {
        parent::setUp();

        $this->route = route('internal-api.v2.metrics.ranges');

        $this->actingAs(User::factory()->create());
    }

    #[Test]
    public function guests_cannot_view_metric_ranges()
    {
        Auth::logout();

        $this->assertGuest()
            ->getJson($this->route)
            ->assertUnauthorized();
    }

    #[Test]
    public function users_can_view_metric_ranges()
    {
        $this->getJson($this->route)->assertJson([
            'data' => collect(MetricRange::cases())->mapWithKeys(fn ($range) => [
                $range->value => str($range->name)->replace('_', ' ')->title()->value(),
            ])->all(),
        ]);
    }
}
