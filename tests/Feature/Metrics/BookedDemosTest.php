<?php

namespace Tests\Feature\Metrics;

use App\Enums\Metrics\MetricFormat;
use App\Enums\Metrics\MetricRange;
use App\Models\Prospects\CloserDemo;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class BookedDemosTest extends TestCase
{
    use RefreshDatabase;

    protected $route;

    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        Carbon::setTestNow('2025-02-18');

        $this->route = route('internal-api.v2.metrics.booked-demos');

        $this->user = User::factory()->create();

        $this->generateDemos();

        $this->actingAs($this->user);
    }

    #[Test]
    public function guests_cannot_view_booked_demos_metrics()
    {
        Auth::logout();

        $this->assertGuest()
            ->getJson($this->route)
            ->assertUnauthorized();
    }

    #[Test]
    public function users_with_demos_see_empty_booked_demos_metrics()
    {
        $user = User::factory()->create();

        $this->assertNotNull($user->closerDemos);
        $this->assertEmpty($user->closerDemos);

        $this->actingAs($user)
            ->getJson(url()->query($this->route, [
                'range' => MetricRange::TODAY->value,
            ]))->assertExactJson([
                'data' => [
                    'format' => MetricFormat::NUMBER->value,
                    'current' => 0,
                    'change' => 0,
                    'percentage' => 0,
                ],
            ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_booked_demos_for_today(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::TODAY->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 2,
                'change' => 1,
                'percentage' => 100,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_booked_demos_for_this_week(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_WEEK->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 3,
                'change' => -2,
                'percentage' => 40,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_booked_demos_for_this_month(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_MONTH->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 8,
                'change' => 8,
                'percentage' => 800,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_booked_demos_for_this_quarter(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_QUARTER->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 8,
                'change' => 8,
                'percentage' => 800,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_booked_demos_for_this_year(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_YEAR->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 8,
                'change' => 8,
                'percentage' => 800,
            ],
        ]);
    }

    private function generateDemos()
    {
        $nbp = NewBuyerProspect::factory()->create();

        $cancelled = CloserDemo::factory(10)->for($nbp, 'prospect')->for($this->user)->cancelled()->create();

        $today = CloserDemo::factory(2)->for($nbp, 'prospect')->for($this->user)->booked()->create([
            'demo_at' => now(),
        ]);

        $yesterday = CloserDemo::factory()->for($nbp, 'prospect')->for($this->user)->booked()->create([
            'demo_at' => now()->subDay(),
        ]);

        $lastWeek = CloserDemo::factory(5)->for($nbp, 'prospect')->for($this->user)->booked()->create([
            'demo_at' => now()->subWeek()->startOfWeek(),
        ]);
    }
}
