<?php

namespace Tests\Feature;

use App\Models\Odin\Company;
use App\Services\CompanyMetrics\SpyFuMetricsService;
use DateTime;
use Exception;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Client\Request as ClientRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class SpyFuMetricsServiceTest extends TestCase
{
    /**
     * Testing a successful HTTP response from spyfu api
     *
     * @throws GuzzleException
     */
    public function test_success(): void
    {
        $service = new SpyFuMetricsService;

        $company = new Company;
        $domain = 'test.com';
        $company[Company::FIELD_WEBSITE] = $domain;

        $startDate = new DateTime('yesterday');
        $startMonth = (int) $startDate->format('m');
        $startYear = $startDate->format('Y');
        $endDate = new DateTime('today');

        $result_count = 1;

        $monthlySpend = 10440.47;

        Http::fake(function (ClientRequest $request) use ($result_count, $startYear, $startMonth, $domain, $monthlySpend) {
            Log::info('URL: '.json_encode($request->url())."\n\n");
            Log::info('Data: '.json_encode($request->data())."\n\n");

            return Http::response(
                $this->get_test_response(
                    $result_count,
                    $startYear,
                    $startMonth,
                    $domain,
                    $monthlySpend),
                200);
        });

        $response = $service->getCompanyMetrics($company, $startDate, $endDate);

        $this->assertEquals($startYear, $response->getYear());
        $this->assertEquals($startMonth, (int) $response->getMonth());
        $this->assertEquals($monthlySpend, $response->getMonthlySpend());
    }

    /**
     * Testing a failed HTTP response from spyfu api from 0 result count
     *
     * @throws GuzzleException
     */
    public function test_result_count_failure(): void
    {
        $service = new SpyFuMetricsService;

        $company = new Company;
        $domain = 'test.com';
        $company[Company::FIELD_WEBSITE] = $domain;
        $company[Company::FIELD_NAME] = 'Test result count 0 failure';

        $startDate = new DateTime('yesterday');
        $startMonth = (int) $startDate->format('m');
        $startYear = $startDate->format('Y');
        $endDate = new DateTime('today');

        $result_count = 0;

        $monthlySpend = 10440.47;

        Http::fake(function (ClientRequest $request) use ($result_count, $startYear, $startMonth, $domain, $monthlySpend) {
            Log::info('URL: '.json_encode($request->url())."\n\n");
            Log::info('Data: '.json_encode($request->data())."\n\n");

            return Http::response(
                $this->get_test_response(
                    $result_count,
                    $startYear,
                    $startMonth,
                    $domain,
                    $monthlySpend),
                200);
        });

        // Expect exception for bad status in response
        $this->assertEquals(null, $service->getCompanyMetrics($company, $startDate, $endDate));
    }

    /**
     * Testing a failed HTTP response from spyfu api with 404
     *
     * @throws GuzzleException
     */
    public function test_http_failure(): void
    {
        $service = new SpyFuMetricsService;

        $company = new Company;
        $domain = 'test.com';
        $company[Company::FIELD_WEBSITE] = $domain;
        $company[Company::FIELD_NAME] = 'Test request 404 failure';

        $startDate = new DateTime('yesterday');
        $endDate = new DateTime('today');

        Http::fake(function (ClientRequest $request) {
            return Http::response('{}', 404);
        });

        // Expect exception for bad status in response
        $this->assertEquals(null, $service->getCompanyMetrics($company, $startDate, $endDate));
    }

    private function get_test_response(int $result_count, string $search_year, string $search_month, string $domain, float $monthlySpend): string
    {
        return '{
  "resultCount": '.$result_count.',
  "domain": "'.$domain.'",
  "results": [
    {
      "searchMonth": '.$search_month.',
      "searchYear": '.$search_year.',
      "averageOrganicRank": 41.080215,
      "monthlyPaidClicks": 61181.88,
      "averageAdRank": 1.0255591,
      "totalOrganicResults": 302731,
      "monthlyBudget": '.$monthlySpend.',
      "monthlyOrganicValue": 384233.16,
      "totalAdsPurchased": 599,
      "monthlyOrganicClicks": 290145,
      "strength": 0,
      "totalInverseRank": 4971564
    }
  ]
}';
    }
}
