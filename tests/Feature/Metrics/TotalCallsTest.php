<?php

namespace Tests\Feature\Metrics;

use App\Enums\Metrics\MetricFormat;
use App\Enums\Metrics\MetricRange;
use App\Models\Call;
use App\Models\Phone;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class TotalCallsTest extends TestCase
{
    use RefreshDatabase;

    protected $route;

    protected $phone;

    protected function setUp(): void
    {
        parent::setUp();

        Carbon::setTestNow('2025-02-18');

        $this->route = route('internal-api.v2.metrics.calls');

        $user = User::factory()->create();

        $this->phone = Phone::factory()->create();

        $user->phones()->attach($this->phone);

        $this->generateCalls();

        $this->actingAs($user);
    }

    #[Test]
    public function guests_cannot_view_total_calls_metrics()
    {
        Auth::logout();

        $this->assertGuest()
            ->getJson($this->route)
            ->assertUnauthorized();
    }

    #[Test]
    public function users_with_no_phones_see_empty_total_calls_metrics()
    {
        $user = User::factory()->create();

        $this->assertEmpty($user->phones);

        $this->actingAs($user)
            ->getJson(url()->query($this->route, [
                'range' => MetricRange::TODAY->value,
            ]))->assertExactJson([
                'data' => [
                    'format' => MetricFormat::NUMBER->value,
                    'current' => 0,
                    'change' => 0,
                    'percentage' => 0,
                ],
            ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_calls_for_today(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::TODAY->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 2,
                'change' => 1,
                'percentage' => 100,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_calls_for_this_week(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_WEEK->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 3,
                'change' => -4,
                'percentage' => 57.14,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_calls_for_this_month(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_MONTH->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 10,
                'change' => -21,
                'percentage' => 67.74,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_calls_for_this_quarter(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_QUARTER->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 41,
                'change' => 10,
                'percentage' => 32.26,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_number_of_calls_for_this_year(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_YEAR->value,
        ]))->assertExactJson([
            'data' => [
                'format' => MetricFormat::NUMBER->value,
                'current' => 41,
                'change' => -90,
                'percentage' => 68.7,
            ],
        ]);
    }

    private function generateCalls()
    {
        $today = Call::factory(2)->for($this->phone)->create([
            'call_start' => now(),
            'call_end' => now()->addMinute(),
        ]);

        $yesterday = Call::factory()->for($this->phone)->create([
            'call_start' => now()->subDay(),
            'call_end' => now()->subDay()->addMinute(),
        ]);

        $lastWeek = collect(range(0, 6))->each(fn ($day) => Call::factory()->for($this->phone)->create([
            'call_start' => now()->subWeek()->startOfWeek()->addDay($day),
            'call_end' => now()->subWeek()->startOfWeek()->addDay($day)->addMinute(),
        ]));

        $lastMonth = collect(range(0, 30))->each(fn ($day) => Call::factory()->for($this->phone)->create([
            'call_start' => now()->subMonth()->startOfMonth()->addDay($day),
            'call_end' => now()->subMonth()->startOfMonth()->addDay($day)->addMinute(),
        ]));

        $lastQuarter = collect(range(0, 30))->each(fn ($day) => Call::factory()->for($this->phone)->create([
            'call_start' => now()->subQuarter()->startOfQuarter()->addDay($day),
            'call_end' => now()->subQuarter()->startOfQuarter()->addDay($day)->addMinute(),
        ]));

        $lastYear = collect(range(0, 99))->each(fn ($day) => Call::factory()->for($this->phone)->create([
            'call_start' => now()->subYear()->startOfYear()->addDay($day),
            'call_end' => now()->subYear()->startOfYear()->addDay($day)->addMinute(),
        ]));
    }
}
