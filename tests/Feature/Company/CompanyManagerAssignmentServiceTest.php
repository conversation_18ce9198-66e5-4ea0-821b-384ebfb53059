<?php

namespace Tests\Feature\Company;

use App\Enums\CompanyUserRelationships\CompanyManagerAssignmentRule;
use App\Enums\RoleType;
use App\Enums\RoundRobinType;
use App\Jobs\Company\SendAmPreAssignmentNotificationJob;
use App\Jobs\Emails\SendGenericEmailJob;
use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\Role;
use App\Models\User;
use App\Repositories\RoundRobins\RoundRobinRepository;
use App\Services\Companies\CompanyManagerAssignmentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyManagerAssignmentServiceTest extends TestCase
{
    use RefreshDatabase;

    private CompanyManagerAssignmentService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new CompanyManagerAssignmentService;

        Role::findOrCreate(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value);
        Role::findOrCreate(RoleType::ACCOUNT_MANAGER->value);
        Role::findOrCreate(RoleType::ONBOARDING_MANAGER->value);
    }

    #[Test]
    public function it_unassigns_bdm_after_default_x_days()
    {
        Queue::fake();

        $bdmRole = Role::findOrCreate(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value);
        $user = User::factory()->createQuietly();
        $company = Company::factory()->createQuietly();

        ProductAssignment::factory()->createQuietly([
            ProductAssignment::FIELD_COMPANY_ID => $company->id,
            ProductAssignment::FIELD_CHARGEABLE => true,
            ProductAssignment::FIELD_DELIVERED => true,
            ProductAssignment::CREATED_AT => now()->subDays(CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS->days() + 1),
            ProductAssignment::FIELD_DELIVERED_AT => now()->subDays(CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS->days() + 1),
        ]);
        $user->assignRole($bdmRole->name);

        $relationship = CompanyUserRelationship::factory()->createQuietly([
            'company_id' => $company->id,
            'user_id' => $user->id,
            'role_id' => $bdmRole->id,
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS->days() + 10),
        ]);

        $this->service->handleBdm();

        $this->assertSoftDeleted($relationship);

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    #[Test]
    public function it_unassigns_bdm_after_x_days_without_purchase()
    {
        Queue::fake();

        $bdmRole = Role::findOrCreate(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value);
        $user = User::factory()->createQuietly();
        $company = Company::factory()->createQuietly();

        $user->assignRole($bdmRole->name);

        $relationship = CompanyUserRelationship::factory()->createQuietly([
            'company_id' => $company->id,
            'user_id' => $user->id,
            'role_id' => $bdmRole->id,
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::BDM_UN_ASSIGNMENT_DAYS_NOT_PURCHASED->days() + 1),
        ]);

        $this->service->handleBdm();

        $this->assertSoftDeleted($relationship);

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    #[Test]
    public function it_assigns_om_after_first_purchase()
    {
        Queue::fake();

        $omRole = Role::findOrCreate(RoleType::ONBOARDING_MANAGER->value);
        $user = User::factory()->createQuietly();

        /** @var Company $company */
        $company = Company::factory()->createQuietly();

        $user->assignRole($omRole->name);

        ProductAssignment::factory()->create([
            'company_id' => $company->id,
            'delivered_at' => now()->subDays(),
            'created_at' => now()->subDays(),
            'delivered' => true,
            'chargeable' => true,
        ]);

        $this->service->processOmAssignments($company, $this->service->getFirstLeadForCompany($company)->delivered_at);

        $this->assertTrue($company->hasAssignment(RoleType::ONBOARDING_MANAGER->value));

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    #[Test]
    public function it_unassigns_om_after_x_days()
    {
        Queue::fake();

        $omRole = Role::findOrCreate(RoleType::ONBOARDING_MANAGER->value);
        $user = User::factory()->createQuietly();
        $company = Company::factory()->createQuietly();

        $user->assignRole($omRole->name);

        ProductAssignment::factory()->create([
            'company_id' => $company->id,
            'delivered_at' => now()->subDays(CompanyManagerAssignmentRule::OM_UN_ASSIGNMENT_DAYS->days()),
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::OM_UN_ASSIGNMENT_DAYS->days()),
            'delivered' => true,
            'chargeable' => true,
        ]);

        $relationship = CompanyUserRelationship::factory()->create([
            'company_id' => $company->id,
            'user_id' => $user->id,
            'role_id' => $omRole->id,
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::OM_UN_ASSIGNMENT_DAYS->days()),
        ]);

        $this->service->processOmAssignments($company, $this->service->getFirstLeadForCompany($company)->delivered_at);

        $this->assertSoftDeleted($relationship);

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    #[Test]
    public function it_selects_am_after_x_days_but_does_not_assign()
    {
        Queue::fake();

        $omRole = Role::findOrCreate(RoleType::ACCOUNT_MANAGER->value);
        $user = User::factory()->createQuietly();

        /** @var Company $company */
        $company = Company::factory()->createQuietly();

        $user->assignRole($omRole->name);
        $this->addUserToRoundRobin(RoundRobinType::ACCOUNT_ASSIGNMENT, $user);

        ProductAssignment::factory()->create([
            'company_id' => $company->id,
            'delivered_at' => now()->subDays(CompanyManagerAssignmentRule::AM_SELECTION_DAYS->days()),
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::AM_SELECTION_DAYS->days()),
            'delivered' => true,
            'chargeable' => true,
        ]);

        $this->service->processAmAssignment($company, $this->service->getFirstLeadForCompany($company)->delivered_at);
        $this->assertNotNull($company->refresh()->preassignedAccountManager);

        Queue::assertPushed(SendAmPreAssignmentNotificationJob::class);
    }

    #[Test]
    public function it_assigns_am_after_x_days()
    {
        Queue::fake();

        $amRole = Role::findOrCreate(RoleType::ACCOUNT_MANAGER->value);
        $user = User::factory()->createQuietly();

        /** @var Company $company */
        $company = Company::factory()->createQuietly();
        $company->preassigned_account_manager_user_id = $user->id;
        $company->save();

        $user->assignRole($amRole->name);

        ProductAssignment::factory()->createQuietly([
            'company_id' => $company->id,
            'delivered_at' => now()->subDays(CompanyManagerAssignmentRule::AM_ASSIGNMENT_DAYS->days()),
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::AM_ASSIGNMENT_DAYS->days()),
            'delivered' => true,
            'chargeable' => true,
        ]);

        $this->service->processAmAssignment($company, $this->service->getFirstLeadForCompany($company)->delivered_at);

        $this->assertTrue($company->hasAssignment(RoleType::ACCOUNT_MANAGER->value));
        $this->assertFalse($company->refresh()->preassignedAccountManager()->exists());

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    #[Test]
    public function it_sends_notifications_to_bdm_for_first_specified_days_without_purchase()
    {
        Queue::fake();

        $bdmRole = Role::findOrCreate(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value);
        $user = User::factory()->createQuietly();
        $company = Company::factory()->createQuietly();

        $user->assignRole($bdmRole->name);

        CompanyUserRelationship::factory()->createQuietly([
            'company_id' => $company->id,
            'user_id' => $user->id,
            'role_id' => $bdmRole->id,
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::BDM_NOTIFICATION_FIRST_NOT_PURCHASED_DAYS->days()),
        ]);

        $this->service->handleBdm();

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    #[Test]
    public function it_sends_notifications_to_bdm_for_second_specified_days_without_purchase()
    {
        Queue::fake();

        $bdmRole = Role::findOrCreate(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value);
        $user = User::factory()->createQuietly();
        $company = Company::factory()->createQuietly();

        $user->assignRole($bdmRole->name);

        CompanyUserRelationship::factory()->createQuietly([
            'company_id' => $company->id,
            'user_id' => $user->id,
            'role_id' => $bdmRole->id,
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::BDM_NOTIFICATION_SECOND_NOT_PURCHASED_DAYS->days()),
        ]);

        $this->service->handleBdm();

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    #[Test]
    public function it_sends_notifications_to_bdm_for_third_specified_days_without_purchase()
    {
        Queue::fake();

        $bdmRole = Role::findOrCreate(RoleType::BUSINESS_DEVELOPMENT_MANAGER->value);
        $user = User::factory()->createQuietly();
        $company = Company::factory()->createQuietly();

        $user->assignRole($bdmRole->name);

        CompanyUserRelationship::factory()->createQuietly([
            'company_id' => $company->id,
            'user_id' => $user->id,
            'role_id' => $bdmRole->id,
            'created_at' => now()->subDays(CompanyManagerAssignmentRule::BDM_NOTIFICATION_THIRD_NOT_PURCHASED_DAYS->days()),
        ]);

        $this->service->handleBdm();

        Queue::assertPushed(SendGenericEmailJob::class);
    }

    private function addUserToRoundRobin(RoundRobinType $type, User $user): void
    {
        $repository = app(RoundRobinRepository::class);
        $repository->addParticipants($type, [$user->id]);
    }
}
