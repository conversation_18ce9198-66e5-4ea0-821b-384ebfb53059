<?php

namespace Tests\Feature\Company;

use App\Http\Controllers\Companies\CompanyController;
use App\Http\Controllers\Companies\CompanyDeleteController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CompanyDeleteControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_cancel_delete_queued_for_company(): void
    {
        /** @see CompanyDeleteController::cancel() */
        /** @see CompanyController::cancelDelete() */
        $this->markTestIncomplete();
    }

    public function test_dispatch_delete_job_for_company(): void
    {
        /** @see CompanyDeleteController::dispatchDelete() */
        $this->markTestIncomplete();
    }

    public function test_validate_company_delete(): void
    {
        /** @see CompanyDeleteController::validateDelete() */
        $this->markTestIncomplete();
    }
}
