<?php

namespace Tests\Feature\Company;

use App\Enums\Billing\InvoiceStates;
use App\Enums\Billing\PaymentMethodServices;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Company\CompanyAdminStatus;
use App\Enums\Company\CompanyCampaignStatus;
use App\Enums\Company\CompanySystemStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Billing\Invoice;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\States\Billing\Chargeback;
use App\States\Billing\Collection;
use App\States\Billing\Failed;
use App\States\Billing\Paid;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyStatusTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_updates_company_consolidated_status_on_company_admin_status_change()
    {
        Bus::fake();

        $company = $this->getCompanyForTest();

        $company->update([Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_LOCKED]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::COLLECTIONS]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ARCHIVED]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_APPROVED]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS, $company->consolidated_status);
    }

    #[Test]
    public function it_updates_company_consolidated_status_on_company_system_status_change()
    {
        Bus::fake();

        $company = $this->getCompanyForTest();

        $company->update([Company::FIELD_SYSTEM_STATUS => CompanySystemStatus::SUSPENDED_PAYMENT]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_SYSTEM_STATUS => CompanySystemStatus::SUSPENDED_PAYMENT_METHOD]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_SYSTEM_STATUS => CompanySystemStatus::ELIGIBLE]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS, $company->consolidated_status);
    }

    #[Test]
    public function it_updates_company_consolidated_status_on_company_campaign_status_change()
    {
        Bus::fake();

        $company = $this->getCompanyForTest();

        $company->update([Company::FIELD_CAMPAIGN_STATUS => CompanyCampaignStatus::NO_CAMPAIGNS]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_CAMPAIGN_STATUS => CompanyCampaignStatus::CAMPAIGNS_PAUSED]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_CAMPAIGN_STATUS => CompanyCampaignStatus::CAMPAIGNS_OFF]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_CAMPAIGN_STATUS => CompanyCampaignStatus::CAMPAIGNS_OFF_NEVER_PURCHASED]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CANNOT_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_CAMPAIGN_STATUS => CompanyCampaignStatus::CAMPAIGNS_ACTIVE]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS, $company->consolidated_status);
    }

    #[Test]
    public function it_forces_consolidated_status_to_can_receive_leads_if_admin_status_is_force_eligible()
    {
        Bus::fake();

        $company = $this->getCompanyForTest();

        $company->update([Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_OVERRIDE]);

        $company->update([Company::FIELD_SYSTEM_STATUS => CompanySystemStatus::SUSPENDED_PAYMENT]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS, $company->consolidated_status);

        $company->update([Company::FIELD_CAMPAIGN_STATUS => CompanyCampaignStatus::NO_CAMPAIGNS]);
        $this->assertEquals(CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS, $company->consolidated_status);
    }

    #[Test]
    public function it_updates_company_campaign_status_on_campaign_status_change()
    {
        Bus::fake();

        $company = $this->getCompanyForTest();
        $campaign = CompanyCampaign::factory()->create([
            CompanyCampaign::FIELD_COMPANY_ID => $company->id,
            CompanyCampaign::FIELD_STATUS => CampaignStatus::ACTIVE,
        ]);

        $this->assertEquals(CompanyCampaignStatus::CAMPAIGNS_ACTIVE, $company->refresh()->campaign_status);

        $campaign->update([CompanyCampaign::FIELD_STATUS => CampaignStatus::PAUSED_PERMANENTLY]);
        $this->assertEquals(CompanyCampaignStatus::CAMPAIGNS_OFF_NEVER_PURCHASED, $company->refresh()->campaign_status);

        $campaign->update([CompanyCampaign::FIELD_STATUS => CampaignStatus::PAUSED_TEMPORARILY]);
        $this->assertEquals(CompanyCampaignStatus::CAMPAIGNS_PAUSED, $company->refresh()->campaign_status);

        $campaign->delete();
        $this->assertEquals(CompanyCampaignStatus::NO_CAMPAIGNS, $company->refresh()->campaign_status);
    }

    #[Test]
    public function it_updates_company_system_status_on_invoice_status_change()
    {
        Bus::fake();

        $company = $this->getCompanyForTest();

        CompanyPaymentMethod::query()->createQuietly([
            CompanyPaymentMethod::FIELD_COMPANY_ID => $company->id,
            CompanyPaymentMethod::FIELD_TYPE => PaymentMethodServices::MANUAL->value,
        ]);

        /** @var Invoice $invoice */
        $invoice = Invoice::factory()->createQuietly([
            Invoice::FIELD_COMPANY_ID => $company->id,
            Invoice::FIELD_STATUS => InvoiceStates::ISSUED->value,
        ]);

        $invoice->status->transitionTo(Collection::class);
        $this->assertEquals(CompanySystemStatus::SUSPENDED_PAYMENT, $company->refresh()->system_status);

        $invoice->delete();
        $this->assertEquals(CompanySystemStatus::ELIGIBLE, $company->refresh()->system_status);

        /** @var Invoice $invoice */
        $invoice = Invoice::factory()->createQuietly([
            Invoice::FIELD_COMPANY_ID => $company->id,
            Invoice::FIELD_STATUS => InvoiceStates::ISSUED->value,
        ]);

        $invoice->status->transitionTo(Failed::class);
        $this->assertEquals(CompanySystemStatus::SUSPENDED_PAYMENT, $company->refresh()->system_status);

        $invoice->status->transitionTo(Paid::class);
        $this->assertEquals(CompanySystemStatus::ELIGIBLE, $company->refresh()->system_status);

        $invoice->status->transitionTo(Chargeback::class);
        $this->assertEquals(CompanySystemStatus::SUSPENDED_PAYMENT, $company->refresh()->system_status);
    }

    #[Test]
    public function it_updates_company_system_status_on_payment_method_change()
    {
        Bus::fake();

        $company = $this->getCompanyForTest();

        $company->update([Company::FIELD_SYSTEM_STATUS => CompanySystemStatus::SUSPENDED_PAYMENT_METHOD]);

        $paymentMethod = CompanyPaymentMethod::query()->create([
            CompanyPaymentMethod::FIELD_COMPANY_ID => $company->id,
            CompanyPaymentMethod::FIELD_TYPE => PaymentMethodServices::MANUAL->value,
        ]);

        $this->assertEquals(CompanySystemStatus::ELIGIBLE, $company->refresh()->system_status);

        $paymentMethod->delete();

        $this->assertEquals(CompanySystemStatus::SUSPENDED_PAYMENT_METHOD, $company->refresh()->system_status);

        $this->assertTrue(true);
    }

    protected function getCompanyForTest(): Company
    {
        return Company::factory()->createQuietly([
            Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_APPROVED,
            Company::FIELD_SYSTEM_STATUS => CompanySystemStatus::ELIGIBLE,
            Company::FIELD_CAMPAIGN_STATUS => CompanyCampaignStatus::CAMPAIGNS_ACTIVE,
            Company::FIELD_CONSOLIDATED_STATUS => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
        ]);
    }
}
