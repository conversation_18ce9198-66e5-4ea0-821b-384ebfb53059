<?php

namespace Tests\Feature\AffiliatesPortal;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Models\Affiliates\Affiliate;
use App\Models\Permission;
use App\Models\User;
use Config;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Testing\Fluent\AssertableJson;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class AffiliateUserControllerTest extends TestCase
{
    use RefreshDatabase;

    private Affiliate $affiliate;

    protected function setUp(): void
    {
        parent::setUp();

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES->value]);

        $role = Role::findOrCreate(RoleType::AFFILIATES_ADMIN->value);
        $role->givePermissionTo(PermissionType::AFFILIATES->value);

        $user = User::factory()->create()->assignRole($role);

        $this->actingAs($user);

        $this->affiliate = Affiliate::factory()->create();

        Http::preventStrayRequests();

        Config::set('services.affiliates_portal_api.url', 'https://test.com');
    }

    #[Test]
    public function index(): void
    {
        $users = [
            [
                'id' => 1,
                'name' => 'User 1',
                'email' => '<EMAIL>',
                'email_verified_at' => '2024-01-01 00:00:00',
                'view_lead_details' => true,
                'affiliate_id' => 'test',
                'created_at' => '2024-01-01 00:00:00',
                'updated_at' => '2024-01-01 00:00:00',
            ],
            [
                'id' => 2,
                'name' => 'User 2',
                'email' => '<EMAIL>',
                'email_verified_at' => '2025-01-01 00:00:00',
                'view_lead_details' => false,
                'affiliate_id' => 'test',
                'created_at' => '2025-01-01 00:00:00',
                'updated_at' => '2025-01-01 00:00:00',
            ],
        ];

        Http::fake([
            config('services.affiliates_portal_api.url').'/users?affiliate_id='.$this->affiliate->uuid => Http::response([
                'data' => $users,
            ]),
        ]);

        $this
            ->getJson(route('internal-api.v1.affiliates-portal.affiliates.users.index', ['affiliate' => $this->affiliate->uuid]))
            ->assertSuccessful()
            ->assertJson(function (AssertableJson $json) use ($users) {
                $json
                    ->has('data', 2)
                    ->has('data.0', function (AssertableJson $json) use ($users) {
                        $json
                            ->whereAll([
                                'id' => $users[0]['id'],
                                'name' => $users[0]['name'],
                                'email' => $users[0]['email'],
                                'email_verified_at' => $users[0]['email_verified_at'],
                                'view_lead_details' => $users[0]['view_lead_details'],
                                'affiliate_id' => $users[0]['affiliate_id'],
                                'created_at' => $users[0]['created_at'],
                                'updated_at' => $users[0]['updated_at'],
                            ]);
                    })
                    ->has('data.1', function (AssertableJson $json) use ($users) {
                        $json
                            ->whereAll([
                                'id' => $users[1]['id'],
                                'name' => $users[1]['name'],
                                'email' => $users[1]['email'],
                                'email_verified_at' => $users[1]['email_verified_at'],
                                'view_lead_details' => $users[1]['view_lead_details'],
                                'affiliate_id' => $users[1]['affiliate_id'],
                                'created_at' => $users[1]['created_at'],
                                'updated_at' => $users[1]['updated_at'],
                            ]);
                    });
            });
    }

    #[Test]
    public function store(): void
    {
        Http::fake([
            config('services.affiliates_portal_api.url').'/users' => Http::response(['id' => 1]),
        ]);

        $this
            ->postJson(
                route('internal-api.v1.affiliates-portal.affiliates.users.store', ['affiliate' => $this->affiliate->uuid]),
                [
                    'name' => 'New User',
                    'email' => '<EMAIL>',
                    'view_lead_details' => true,
                ]
            )
            ->assertSuccessful()
            ->assertJson([
                'data' => [
                    'id' => 1,
                ],
            ]);
    }

    #[Test]
    public function update(): void
    {
        Http::fake([
            config('services.affiliates_portal_api.url').'/users/1' => Http::response([]),
        ]);

        $this
            ->patchJson(
                route('internal-api.v1.affiliates-portal.affiliates.users.update', ['affiliate' => $this->affiliate->uuid, 'user' => 1]),
                [
                    'name' => 'Updated User',
                    'email' => '<EMAIL>',
                    'view_lead_details' => true,
                ]
            )
            ->assertSuccessful();
    }

    #[Test]
    public function destroy(): void
    {
        Http::fake([
            config('services.affiliates_portal_api.url').'/users/1' => Http::response([]),
        ]);

        $this
            ->deleteJson(route('internal-api.v1.affiliates-portal.affiliates.users.destroy', ['affiliate' => $this->affiliate->uuid, 'user' => 1]))
            ->assertSuccessful();
    }
}
