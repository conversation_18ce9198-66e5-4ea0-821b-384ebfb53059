<?php

namespace Tests\Feature\AffiliatesPortal;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Helpers\NumberHelper;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ProductAssignment;
use App\Models\Permission;
use App\Models\User;
use Carbon\CarbonImmutable;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class StatisticsControllerTest extends TestCase
{
    use RefreshDatabase;

    private Affiliate $affiliate;

    private PayoutStrategy $payoutStrategy;

    protected function setUp(): void
    {
        parent::setUp();

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES->value]);

        $role = Role::findOrCreate(RoleType::AFFILIATES_ADMIN->value);
        $role->givePermissionTo(PermissionType::AFFILIATES->value);

        $user = User::factory()->create()->assignRole($role);

        $this->actingAs($user);

        $this->affiliate = Affiliate::factory()->create();
        $this->payoutStrategy = PayoutStrategy::factory()
            ->for($this->affiliate)
            ->create();

        $this->seed(ProductsSeeder::class);
    }

    #[Test]
    public function get_affiliate_statistics_successfully(): void
    {
        $now = CarbonImmutable::now('UTC');

        $campaigns = Campaign::factory(2)
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $consumerProducts = collect();
        $campaigns->each(function (Campaign $campaign) use (&$consumerProducts, $now) {
            $affiliateRecord = ConsumerProductAffiliateRecord::factory()
                ->for($this->affiliate, ConsumerProductAffiliateRecord::RELATION_AFFILIATE)
                ->for($campaign, ConsumerProductAffiliateRecord::RELATION_AFFILIATE_CAMPAIGN)
                ->create();

            $consumerProducts = $consumerProducts->merge(
                ConsumerProduct::factory(2)
                    ->goodToSell()
                    ->has(ProductAssignment::factory(2)->chargeableAndDelivered()->notRejected(), ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT)
                    ->for($affiliateRecord, ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD)
                    ->create([ConsumerProduct::FIELD_CREATED_AT => $now->format('Y-m-d H:i:s')])
            );
        });

        $start = $now->subDay()->timestamp;
        $end = $now->timestamp;

        $statistics = $this
            ->getJson(route(
                'internal-api.v1.affiliates-portal.statistics',
                ['type' => 'affiliates', 'ids' => [$this->affiliate->{Affiliate::FIELD_UUID}], 'start' => $start, 'end' => $end]
            ))
            ->assertSuccessful()
            ->json("data.{$this->affiliate->uuid}");

        $totalRevenue = 0;
        $legsSold = 0;
        $leadsCreated = 0;
        $leadsGoodToSell = 0;

        $consumerProducts->each(function (ConsumerProduct $product) use (&$totalRevenue, &$legsSold, &$leadsCreated, &$leadsGoodToSell) {
            $leadsCreated++;
            $leadsGoodToSell++;
            $legsSold += $product->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->count();
            $totalRevenue += $product->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->sum(ProductAssignment::FIELD_COST);
        });

        $this->assertEquals(NumberHelper::currency($totalRevenue), $statistics['total_revenue']);
        $this->assertEquals($legsSold, $statistics['legs_sold']);
        $this->assertEquals($leadsCreated, $statistics['leads_created']);
        $this->assertEquals($leadsGoodToSell, $statistics['leads_good_to_sell']);
    }

    #[Test]
    public function get_campaign_statistics_successfully(): void
    {
        $now = CarbonImmutable::now('UTC');

        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $records = ConsumerProductAffiliateRecord::factory(2)
            ->for($campaign, ConsumerProductAffiliateRecord::RELATION_AFFILIATE_CAMPAIGN)
            ->for($this->affiliate, ConsumerProductAffiliateRecord::RELATION_AFFILIATE)
            ->create();

        $consumerProducts = collect();
        $records->each(function (ConsumerProductAffiliateRecord $record) use (&$consumerProducts, $now) {
            $consumerProducts = $consumerProducts->merge(
                ConsumerProduct::factory(2)
                    ->goodToSell()
                    ->has(ProductAssignment::factory(2)->chargeableAndDelivered()->notRejected(), ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT)
                    ->for($record, ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD)
                    ->create([ConsumerProduct::FIELD_CREATED_AT => $now->format('Y-m-d H:i:s')])
            );
        });

        $start = $now->subDay()->timestamp;
        $end = $now->timestamp;

        $statistics = $this
            ->getJson(route(
                'internal-api.v1.affiliates-portal.statistics',
                ['type' => 'campaigns', 'ids' => [$campaign->{Campaign::FIELD_ID}], 'start' => $start, 'end' => $end]
            ))
            ->assertSuccessful()
            ->json("data.{$campaign->{Campaign::FIELD_ID}}");

        $totalRevenue = 0;
        $legsSold = 0;
        $leadsCreated = 0;
        $leadsGoodToSell = 0;

        $consumerProducts->each(function (ConsumerProduct $product) use (&$totalRevenue, &$legsSold, &$leadsCreated, &$leadsGoodToSell) {
            $leadsCreated++;
            $leadsGoodToSell++;
            $legsSold += $product->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->count();
            $totalRevenue += $product->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->sum(ProductAssignment::FIELD_COST);
        });

        $this->assertEquals(NumberHelper::currency($totalRevenue), $statistics['total_revenue']);
        $this->assertEquals($legsSold, $statistics['legs_sold']);
        $this->assertEquals($leadsCreated, $statistics['leads_created']);
        $this->assertEquals($leadsGoodToSell, $statistics['leads_good_to_sell']);
    }

    #[Test]
    public function return_default_if_there_are_no_consumer_product_affiliate_records(): void
    {
        $this
            ->getJson(route(
                'internal-api.v1.affiliates-portal.statistics',
                ['type' => 'campaigns', 'ids' => [1], 'start' => 1, 'end' => 2]
            ))
            ->assertSuccessful()
            ->assertExactJson([
                'data' => [
                    1 => [
                        'legs_sold' => 0,
                        'leads_created' => 0,
                        'leads_good_to_sell' => 0,
                        'total_payout' => '$0.00',
                        'total_revenue' => '$0.00',
                    ],
                ],
            ]);
    }
}
