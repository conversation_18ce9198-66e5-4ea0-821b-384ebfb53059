<?php

namespace Tests\Feature\AffiliatesPortal;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Payout;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use App\Models\Odin\ProductAssignment;
use App\Models\Permission;
use App\Models\User;
use Carbon\CarbonImmutable;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

use function PHPUnit\Framework\assertEquals;

class PayoutStrategyTest extends TestCase
{
    use RefreshDatabase;

    private Affiliate $affiliate;

    protected function setUp(): void
    {
        parent::setUp();

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES->value]);

        $role = Role::findOrCreate(RoleType::AFFILIATES_ADMIN->value);
        $role->givePermissionTo(PermissionType::AFFILIATES->value);

        $user = User::factory()->create()->assignRole($role);

        $this->seed(ProductsSeeder::class);

        $this->actingAs($user);

        $this->affiliate = Affiliate::factory()->create();
    }

    /**
     * @return void
     */
    #[DataProvider('strategyProvider')]
    public function test_strategy_types(PayoutStrategyTypeEnum $payoutStrategyTypeEnum)
    {
        $now = CarbonImmutable::now('UTC');

        $strategyClass = $payoutStrategyTypeEnum->getClass();

        PayoutStrategy::factory()
            ->for($this->affiliate, PayoutStrategy::RELATION_AFFILIATE)
            ->create([
                PayoutStrategy::FIELD_VALUE => $strategyClass->defaultValue(),
                PayoutStrategy::FIELD_TYPE => $strategyClass->type(),
            ]);

        $campaigns = Campaign::factory(2)
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $consumerProducts = collect();

        $campaigns->each(function (Campaign $campaign) use (&$consumerProducts, $now) {
            $affiliateRecord = ConsumerProductAffiliateRecord::factory()
                ->for($this->affiliate, ConsumerProductAffiliateRecord::RELATION_AFFILIATE)
                ->for($campaign, ConsumerProductAffiliateRecord::RELATION_AFFILIATE_CAMPAIGN)
                ->create();

            $consumerProducts = $consumerProducts->merge(
                ConsumerProduct::factory(2)
                    ->goodToSell()
                    ->has(ProductAssignment::factory(2)->chargeableAndDelivered()->notRejected(), ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT)
                    ->for($affiliateRecord, ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD)
                    ->create([ConsumerProduct::FIELD_CREATED_AT => $now->format('Y-m-d H:i:s')])
            );
        });

        $expectedPayout = 0;

        foreach ($consumerProducts as $consumerProduct) {
            if ($payoutStrategyTypeEnum == PayoutStrategyTypeEnum::REVENUE_PERCENTAGE) {
                $expectedPayout += round($consumerProduct->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->sum(ProductAssignment::FIELD_COST) * $strategyClass->defaultValue());
            } else {
                $expectedPayout += $strategyClass->defaultValue();
            }
        }

        $payout = Payout::query()
            ->where(Payout::FIELD_AFFILIATE_ID, $this->affiliate->id)
            ->sum(Payout::FIELD_CENT_VALUE);

        assertEquals($expectedPayout, $payout);
    }

    public static function strategyProvider()
    {
        return array_map(fn ($case) => [$case], PayoutStrategyTypeEnum::cases());
    }
}
