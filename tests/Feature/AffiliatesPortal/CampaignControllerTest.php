<?php

namespace Tests\Feature\AffiliatesPortal;

use App\Http\Controllers\API\Affiliates\Portal\CampaignController;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\Affiliates\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Testing\Fluent\AssertableJson;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CampaignControllerTest extends TestCase
{
    use RefreshDatabase;

    private Affiliate $affiliate;

    protected function setUp(): void
    {
        parent::setUp();

        $this->affiliate = Affiliate::factory()->create();

        $this->withToken(config('services.affiliates_portal_api.token'));
    }

    #[Test]
    public function list_campaigns_successfully(): void
    {
        Campaign::factory(2)
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $page = 1;
        $perPage = 10;

        $start = 1;
        $end = 2;

        $searchName = '';

        $this
            ->getJson(route(
                'affiliates-portal.v1.affiliates.campaigns.index',
                ['affiliate' => $this->affiliate->{Affiliate::FIELD_UUID}, ...compact('page', 'perPage', 'start', 'end', 'searchName')]
            ))
            ->assertSuccessful()
            ->assertJsonStructure([
                'data' => [
                    'campaigns' => [
                        'current_page',
                        'from',
                        'to',
                        'per_page',
                        'total',
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                            ],
                        ],
                    ],
                    'statistics' => [
                        '*' => [
                            'legs_sold',
                            'leads_created',
                            'total_payout',
                            'leads_good_to_sell',
                        ],
                    ],
                ],
            ])
            ->assertJsonCount(2, 'data.campaigns.data')
            ->assertJsonCount(2, 'data.statistics');
    }

    #[Test]
    public function filter_campaigns_by_name_successfully(): void
    {
        $campaigns = Campaign::factory(2)
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $page = 1;
        $perPage = 10;

        $start = 1;
        $end = 2;

        $searchName = $campaigns->last()->name;

        $this
            ->getJson(route(
                'affiliates-portal.v1.affiliates.campaigns.index',
                ['affiliate' => $this->affiliate->{Affiliate::FIELD_UUID}, ...compact('page', 'perPage', 'start', 'end', 'searchName')]
            ))
            ->assertSuccessful()
            ->assertJsonStructure([
                'data' => [
                    'campaigns' => [
                        'current_page',
                        'from',
                        'to',
                        'per_page',
                        'total',
                        'data' => [
                            '*' => [
                                'id',
                                'name',
                            ],
                        ],
                    ],
                    'statistics' => [
                        '*' => [
                            'legs_sold',
                            'leads_created',
                            'total_payout',
                            'leads_good_to_sell',
                        ],
                    ],
                ],
            ])
            ->assertJsonCount(1, 'data.statistics')
            ->assertJson(function (AssertableJson $json) use ($campaigns) {
                $json->has('data.campaigns.data', 1, function (AssertableJson $json) use ($campaigns) {
                    $campaign = $campaigns->last();

                    $json->whereAll([
                        'id' => $campaign->id,
                        'name' => $campaign->name,
                        'category_id' => $campaign->category_id,
                        'status' => (int) $campaign->status,
                    ]);
                });
            });
    }

    #[Test]
    public function create_campaign_successfully(): void
    {
        $affiliateCampaignCategory = Category::factory()->create();

        $this
            ->postJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.store',
                    ['affiliate' => $this->affiliate->{Affiliate::FIELD_UUID}]
                ),
                [
                    CampaignController::REQUEST_NAME => 'New Campaign',
                    CampaignController::REQUEST_STATUS => true,
                    CampaignController::REQUEST_CATEGORY_ID => $affiliateCampaignCategory->{Category::FIELD_ID},
                ]
            )
            ->assertSuccessful();

        $this->assertDatabaseHas(Campaign::TABLE, [
            Campaign::FIELD_NAME => 'New Campaign',
            Campaign::FIELD_AFFILIATE_ID => $this->affiliate->{Affiliate::FIELD_ID},
            Campaign::FIELD_CATEGORY_ID => $affiliateCampaignCategory->{Category::FIELD_ID},
            Campaign::FIELD_STATUS => true,
        ]);
    }

    #[Test]
    public function fail_to_create_campaign_without_name(): void
    {
        $affiliateCampaignCategory = Category::factory()->create();

        Campaign::query()->forceDelete();

        $this
            ->postJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.store',
                    ['affiliate' => $this->affiliate->{Affiliate::FIELD_UUID}]
                ),
                [
                    CampaignController::REQUEST_NAME => '',
                    CampaignController::REQUEST_STATUS => true,
                    CampaignController::REQUEST_CATEGORY_ID => $affiliateCampaignCategory->{Category::FIELD_ID},
                ]
            )
            ->assertJsonValidationErrors([CampaignController::REQUEST_NAME]);

        $this->assertDatabaseEmpty(Campaign::TABLE);
    }

    #[Test]
    public function fail_to_create_campaign_without_category(): void
    {
        Campaign::query()->forceDelete();

        $this
            ->postJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.store',
                    ['affiliate' => $this->affiliate->{Affiliate::FIELD_UUID}]
                ),
                [
                    CampaignController::REQUEST_NAME => 'New Campaign',
                    CampaignController::REQUEST_STATUS => true,
                    CampaignController::REQUEST_CATEGORY_ID => '',
                ]
            )
            ->assertJsonValidationErrors([CampaignController::REQUEST_CATEGORY_ID]);

        $this->assertDatabaseEmpty(Campaign::TABLE);
    }

    #[Test]
    public function fail_to_create_campaign_without_status(): void
    {
        $affiliateCampaignCategory = Category::factory()->create();

        Campaign::query()->forceDelete();

        $this
            ->postJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.store',
                    ['affiliate' => $this->affiliate->{Affiliate::FIELD_UUID}]
                ),
                [
                    CampaignController::REQUEST_NAME => 'New Campaign',
                    CampaignController::REQUEST_CATEGORY_ID => $affiliateCampaignCategory->{Category::FIELD_ID},
                ]
            )
            ->assertJsonValidationErrors([CampaignController::REQUEST_STATUS]);

        $this->assertDatabaseEmpty(Campaign::TABLE);
    }

    #[Test]
    public function read_campaign_successfully(): void
    {
        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $res = $this
            ->getJson(route(
                'affiliates-portal.v1.affiliates.campaigns.show',
                [
                    'affiliate' => $this->affiliate->{Affiliate::FIELD_UUID},
                    'campaign' => $campaign->{Campaign::FIELD_ID},
                ]
            ))
            ->assertSuccessful()
            ->assertJsonStructure([
                'data' => [
                    'id',
                    'name',
                ],
            ]);

        $this->assertEquals($campaign->{Campaign::FIELD_ID}, $res['data']['id']);
        $this->assertEquals($campaign->{Campaign::FIELD_NAME}, $res['data']['name']);
    }

    #[Test]
    public function update_campaign_successfully(): void
    {
        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create([Campaign::FIELD_NAME => 'New Campaign']);

        $this
            ->patchJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.update',
                    [
                        'affiliate' => $this->affiliate->{Affiliate::FIELD_UUID},
                        'campaign' => $campaign->{Campaign::FIELD_ID},
                    ]
                ),
                [
                    CampaignController::REQUEST_CATEGORY_ID => $campaign->{Campaign::RELATION_CATEGORY}->{Category::FIELD_ID},
                    CampaignController::REQUEST_NAME => 'Updated Campaign',
                    CampaignController::REQUEST_STATUS => $campaign->{Campaign::FIELD_STATUS},
                ]
            )
            ->assertSuccessful();

        $this->assertDatabaseHas(Campaign::TABLE, [
            Campaign::FIELD_ID => $campaign->{Campaign::FIELD_ID},
            Campaign::FIELD_NAME => 'Updated Campaign',
        ]);
    }

    #[Test]
    public function fail_to_update_campaign_without_name(): void
    {
        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create([Campaign::FIELD_NAME => 'New Campaign']);

        $this
            ->patchJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.update',
                    [
                        'affiliate' => $this->affiliate->{Affiliate::FIELD_UUID},
                        'campaign' => $campaign->{Campaign::FIELD_ID},
                    ]
                ),
                [
                    CampaignController::REQUEST_NAME => '',
                    CampaignController::REQUEST_STATUS => $campaign->{Campaign::FIELD_STATUS},
                    CampaignController::REQUEST_CATEGORY_ID => $campaign->{Campaign::RELATION_CATEGORY}->{Category::FIELD_ID},
                ]
            )
            ->assertJsonValidationErrors([CampaignController::REQUEST_NAME]);

        $this->assertDatabaseHas(Campaign::TABLE, [
            Campaign::FIELD_ID => $campaign->{Campaign::FIELD_ID},
            Campaign::FIELD_NAME => $campaign->{Campaign::FIELD_NAME},
            Campaign::FIELD_STATUS => $campaign->{Campaign::FIELD_STATUS},
            Campaign::FIELD_CATEGORY_ID => $campaign->{Campaign::FIELD_CATEGORY_ID},
        ]);
    }

    #[Test]
    public function fail_to_update_campaign_without_category(): void
    {
        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create([Campaign::FIELD_NAME => 'New Campaign']);

        $this
            ->patchJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.update',
                    [
                        'affiliate' => $this->affiliate->{Affiliate::FIELD_UUID},
                        'campaign' => $campaign->{Campaign::FIELD_ID},
                    ]
                ),
                [
                    CampaignController::REQUEST_NAME => $campaign->{Campaign::FIELD_NAME},
                    CampaignController::REQUEST_STATUS => $campaign->{Campaign::FIELD_STATUS},
                    CampaignController::REQUEST_CATEGORY_ID => '',
                ]
            )
            ->assertJsonValidationErrors([CampaignController::REQUEST_CATEGORY_ID]);

        $this->assertDatabaseHas(Campaign::TABLE, [
            Campaign::FIELD_ID => $campaign->{Campaign::FIELD_ID},
            Campaign::FIELD_NAME => $campaign->{Campaign::FIELD_NAME},
            Campaign::FIELD_STATUS => $campaign->{Campaign::FIELD_STATUS},
            Campaign::FIELD_CATEGORY_ID => $campaign->{Campaign::FIELD_CATEGORY_ID},
        ]);
    }

    #[Test]
    public function fail_to_update_campaign_without_status(): void
    {
        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create([Campaign::FIELD_NAME => 'New Campaign']);

        $this
            ->patchJson(
                route(
                    'affiliates-portal.v1.affiliates.campaigns.update',
                    [
                        'affiliate' => $this->affiliate->{Affiliate::FIELD_UUID},
                        'campaign' => $campaign->{Campaign::FIELD_ID},
                    ]
                ),
                [
                    CampaignController::REQUEST_NAME => $campaign->{Campaign::FIELD_NAME},
                    CampaignController::REQUEST_CATEGORY_ID => $campaign->{Campaign::RELATION_CATEGORY}->{Category::FIELD_ID},
                ]
            )
            ->assertJsonValidationErrors([CampaignController::REQUEST_STATUS]);

        $this->assertDatabaseHas(Campaign::TABLE, [
            Campaign::FIELD_ID => $campaign->{Campaign::FIELD_ID},
            Campaign::FIELD_NAME => $campaign->{Campaign::FIELD_NAME},
            Campaign::FIELD_STATUS => $campaign->{Campaign::FIELD_STATUS},
            Campaign::FIELD_CATEGORY_ID => $campaign->{Campaign::FIELD_CATEGORY_ID},
        ]);
    }

    #[Test]
    public function delete_campaign_successfully(): void
    {
        $campaign = Campaign::factory()
            ->for($this->affiliate, Campaign::RELATION_AFFILIATE)
            ->create();

        $this
            ->deleteJson(route(
                'affiliates-portal.v1.affiliates.campaigns.destroy',
                [
                    'affiliate' => $this->affiliate->{Affiliate::FIELD_UUID},
                    'campaign' => $campaign->{Campaign::FIELD_ID},
                ]
            ))
            ->assertSuccessful();

        $this->assertSoftDeleted(Campaign::TABLE, [
            Campaign::FIELD_ID => $campaign->{Campaign::FIELD_ID},
        ]);
    }
}
