<?php

namespace Feature\AffiliatesPortal;

use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Models\Affiliates\Affiliate;
use App\Models\Permission;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ShadowAffiliateControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        $this->affiliate = Affiliate::factory()->create();

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES->value]);
        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::AFFILIATES_SHADOW->value]);

        $role = Role::findOrCreate(RoleType::AFFILIATES_ADMIN->value);
        $role->givePermissionTo(PermissionType::AFFILIATES->value);
        $role->givePermissionTo(PermissionType::AFFILIATES_SHADOW->value);

        $user = User::factory()->create()->assignRole($role);

        $this->actingAs($user);

        $this->withToken(config('services.affiliates_portal_api.token'));

        Carbon::setTestNow(now());
    }

    #[Test]
    public function successfully_login_with_token(): void
    {
        /** @var Affiliate $affiliate */
        $affiliate = Affiliate::factory()->create();

        $url = $this
            ->getJson(route('internal-api.v1.affiliates-portal.affiliates.shadow-url', ['affiliate' => $affiliate->uuid]))
            ->assertStatus(200)
            ->json('data.url');

        $this->assertTrue(Str::of($url)->contains('token'));

        $this
            ->postJson(route('affiliates-portal.v1.shadow',
                ['token' => Str::of($url)->after('token=')->toString()]
            ))->assertJson([
                'data' => [
                    'affiliate_numeric_id' => $affiliate->id,
                    'affiliate_uuid' => $affiliate->uuid,
                    'shadow_id' => Auth()->user()->id,
                    'expiry' => now()->addMinutes(10)->toJSON(),
                ],
            ]);
    }
}
