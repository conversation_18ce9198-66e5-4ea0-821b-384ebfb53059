<?php

namespace Tests\Feature\Campaigns\Module;

use App\Actions\ForceUploadWatchdogVideo;
use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Campaigns\Delivery\DeliveryType;
use App\Campaigns\Modules\DataModels\PostAllocationData;
use App\Campaigns\Modules\DeliveryModule;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleContact;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\CompanyCampaignData;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Str;
use Tests\TestCase;

class DeliveryModuleTest extends TestCase
{
    use RefreshDatabase;

    protected CompanyCampaign $companyCampaign;

    protected ProductAssignment $productAssignment;

    protected PostAllocationData $postAllocationData;

    protected CompanyCampaignDeliveryModule $companyCampaignDeliveryModule;

    protected CompanyCampaignDeliveryModuleCRM $companyCampaignDeliveryModuleCrm;

    protected CompanyCampaignDeliveryModuleContact $companyCampaignDeliveryModuleContact;

    protected Company $company;

    protected function setUp(): void
    {
        parent::setUp();

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly();
        $this->productAssignment = ProductAssignment::factory()->createQuietly();
        $this->postAllocationData = app(PostAllocationData::class);

        $this->productAssignment->consumerProduct->consumer->watchdogVideos()->createQuietly([
            'watchdog_video_id' => Str::ulid(),
        ]);
    }

    #[Test]
    public function it_logs_failed_assignment_if_missing_delivery_methods()
    {
        $this->partialMock(PostAllocationData::class)->shouldReceive('logFailedAssignment')->once()->with($this->productAssignment, DeliveryType::NONE, 'Campaign has no valid delivery methods');

        $this->partialMock(ForceUploadWatchdogVideo::class)->shouldReceive(methodNames: 'handle')->times(0);

        $this->postAllocationData = app(PostAllocationData::class);

        $result = app(DeliveryModule::class)->postAllocation($this->companyCampaign, $this->productAssignment, $this->postAllocationData);

        $this->assertDatabaseCount('company_campaign_data', 0);

        $this->assertInstanceOf(PostAllocationData::class, $result);
    }

    #[Test]
    public function it_logs_failed_assignment_if_has_crm_delivery_methods_but_missing_crm_result()
    {
        $this->partialMock(PostAllocationData::class)->shouldReceive('logFailedAssignment')->once()->with($this->productAssignment, DeliveryType::CRM, 'All CRM Deliveries Failed');

        $this->partialMock(ForceUploadWatchdogVideo::class)->shouldReceive('handle')->once();

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly();

        $this->companyCampaignDeliveryModule = CompanyCampaignDeliveryModule::createQuietly([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $this->companyCampaignDeliveryModuleCrm = CompanyCampaignDeliveryModuleCRM::createQuietly([
            'module_id' => $this->companyCampaignDeliveryModule->id,
            'crm_type' => CRMType::PARSEABLE_EMAIL->value,
            'active' => true,
            'payload' => [
                'system_fields' => [
                    [
                        'key' => 'email_address',
                        'value' => '<EMAIL>',
                    ],
                ],
            ],
        ]);

        $this->postAllocationData = app(PostAllocationData::class);

        $result = app(DeliveryModule::class)->postAllocation($this->companyCampaign, $this->productAssignment, $this->postAllocationData);

        $this->assertDatabaseCount('company_campaign_data', 0);

        $this->assertInstanceOf(PostAllocationData::class, $result);
    }

    #[Test]
    public function it_logs_failed_and_successful_assignment_if_has_contact_delivery_methods_but_missing_contact_result()
    {
        $this->company = Company::factory()->createQuietly();

        $this->companyCampaign = CompanyCampaign::factory()->createQuietly([
            'company_id' => $this->company->id,
        ]);

        $this->productAssignment = ProductAssignment::factory()->createQuietly([
            'company_id' => $this->company->id,
        ]);

        $this->productAssignment->consumerProduct->consumer->watchdogVideos()->createQuietly([
            'watchdog_video_id' => Str::ulid(),
        ]);

        $this->partialMock(ForceUploadWatchdogVideo::class)->shouldReceive('handle')->once();

        $mock = $this->partialMock(PostAllocationData::class);

        $mock->shouldReceive('logFailedAssignment')->once()->with($this->productAssignment, DeliveryType::CONTACT, 'All Contact Deliveries Failed');

        $mock->shouldReceive('logSuccessfulAssignment')->once()->with($this->productAssignment);

        $this->companyCampaignDeliveryModule = CompanyCampaignDeliveryModule::create([
            'company_campaign_id' => $this->companyCampaign->id,
        ]);

        $this->companyCampaignDeliveryModuleCrm = CompanyCampaignDeliveryModuleCRM::createQuietly([
            'module_id' => $this->companyCampaignDeliveryModule->id,
            'crm_type' => CRMType::PARSEABLE_EMAIL->value,
            'active' => true,
            'payload' => [
                'system_fields' => [
                    [
                        'key' => 'email_address',
                        'value' => '<EMAIL>',
                    ],
                ],
            ],
        ]);

        $this->companyCampaignDeliveryModuleContact = CompanyCampaignDeliveryModuleContact::createQuietly([
            'module_id' => $this->companyCampaignDeliveryModule->id,
            'contact_id' => 1,
            'active' => true,
        ]);

        $this->postAllocationData = app(PostAllocationData::class);

        $result = app(DeliveryModule::class)->postAllocation($this->companyCampaign, $this->productAssignment, $this->postAllocationData);

        $this->assertInstanceOf(PostAllocationData::class, $result);

        $this->assertDatabaseHas('company_campaign_data', [
            'campaign_id' => $this->companyCampaign->id,
        ]);

        $payload = CompanyCampaignData::first()->payload;

        $this->assertNull($payload['averageLeadCost']);
        $this->assertNull($payload['averageLeadCostLastCalculatedAt']);
        $this->assertNull($payload['averageLeadCostLeadCount']);
        $this->assertNotNull($payload['leadLastSoldAt']);
    }
}
