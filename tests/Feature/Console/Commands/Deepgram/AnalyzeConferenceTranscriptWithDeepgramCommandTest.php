<?php

namespace Tests\Feature\Console\Commands\Deepgram;

use App\Console\Commands\Deepgram\AnalyzeConferenceTranscriptWithDeepgramCommand;
use App\Jobs\Deepgram\AnalyzeConferenceTranscriptWithDeepgram;
use App\Models\Conference\ConferenceTranscript;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AnalyzeConferenceTranscriptWithDeepgramCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Config::set('services.deepgram.api_key', 'testing');
    }

    #[Test]
    public function it_can_run_the_job(): void
    {
        Queue::fake();

        $this->artisan(AnalyzeConferenceTranscriptWithDeepgramCommand::class, [
            'conferenceTranscriptId' => ConferenceTranscript::factory()->create()->id,
        ])
            ->assertOk()
            ->expectsOutputToContain('Job dispatched...');

        Queue::assertCount(1);

        Queue::assertPushed(AnalyzeConferenceTranscriptWithDeepgram::class);
    }

    #[Test]
    public function it_fails_if_the_conference_transcript_does_not_exist(): void
    {
        Queue::fake();

        $this->artisan(AnalyzeConferenceTranscriptWithDeepgramCommand::class, [
            'conferenceTranscriptId' => 10000,
        ])
            ->assertFailed()
            ->expectsOutputToContain('No query results for model [App\Models\Conference\ConferenceTranscript] 10000');

        Queue::assertNothingPushed();
    }

    #[Test]
    public function it_fails_if_api_key_is_not_set(): void
    {
        Config::set('services.deepgram.api_key');

        Queue::fake();

        $this->artisan(AnalyzeConferenceTranscriptWithDeepgramCommand::class, [
            'conferenceTranscriptId' => ConferenceTranscript::factory()->create()->id,
        ])
            ->assertFailed()
            ->expectsOutputToContain('You must set the DEEPGRAM_API_KEY environment variable to run this command.');

        Queue::assertNothingPushed();
    }
}
