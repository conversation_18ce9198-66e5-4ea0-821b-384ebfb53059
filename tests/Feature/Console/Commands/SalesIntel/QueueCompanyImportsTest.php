<?php

namespace Tests\Feature\Console\Commands\SalesIntel;

use App\Console\Commands\SalesIntel\QueueCompanyImports;
use App\Jobs\SalesIntel\ImportCompanies;
use App\Models\SalesIntel\FailedCompanyImportRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class QueueCompanyImportsTest extends TestCase
{
    use RefreshDatabase;

    #[Test, DataProvider('optionProvider')]
    public function it_pushes_the_jobs_onto_the_imports_queue($option, $values)
    {
        Queue::fake();

        $this->artisan(QueueCompanyImports::class, [$option => $values]);

        Queue::assertPushedOn('imports', ImportCompanies::class);
    }

    #[Test, DataProvider('optionProvider')]
    public function it_accepts_a_list_of_comma_separated_values_as_a_parameter($option, $values, $filter)
    {
        Queue::fake();

        $this->artisan(QueueCompanyImports::class, [$option => $values])
            ->expectsOutputToContain("Queuing Company Imports for the {$filter}");

        Queue::assertPushed(ImportCompanies::class, 2);
    }

    #[Test, DataProvider('optionProvider')]
    public function it_skips_a_provided_option_if_it_has_already_been_exhausted($option, $values, $filter)
    {
        Queue::fake();

        str($values)->explode(',')->each(
            fn ($value) => FailedCompanyImportRecord::factory()->create([
                'filter' => $filter,
                'value' => str($value)->trim(),
            ])
        );

        $this->artisan(QueueCompanyImports::class, [$option => $values])
            ->expectsOutputToContain("No more Company records exist for the {$filter}");

        Queue::assertNothingPushed();
    }

    #[Test, DataProvider('optionProvider')]
    public function it_accepts_a_list_of_comma_separated_naics_codes($option, $values, $filter)
    {
        Queue::fake();

        $naics = '111,222, 333';

        $this->artisan(QueueCompanyImports::class, [$option => $values, '--naics' => $naics])
            ->expectsOutputToContain('with NAICS 111, 222, 333');

        Queue::assertPushed(ImportCompanies::class, 2);
    }

    #[Test, DataProvider('optionProvider')]
    public function it_skips_a_provided_option_if_it_has_already_been_exhausted_accounting_for_naics($option, $values, $filter)
    {
        Queue::fake();

        $naics = '111';

        str($values)->remove(' ')->explode(',')->each(
            fn ($value) => FailedCompanyImportRecord::factory()->create([
                'filter' => $filter,
                'value' => $value,
                'naics' => $naics,
            ])
        );

        $this->artisan(QueueCompanyImports::class, [$option => $values, '--naics' => $naics])
            ->expectsOutputToContain("No more Company records exist for the {$filter}");

        Queue::assertNothingPushed();
    }

    #[Test, DataProvider('optionProvider')]
    public function it_does_not_skip_a_provided_option_if_it_has_already_been_exhausted_with_a_null_naics($option, $values, $filter)
    {
        Queue::fake();

        $naics = '111';

        str($values)->remove(' ')->explode(',')->each(
            fn ($value) => FailedCompanyImportRecord::factory()->create([
                'filter' => $filter,
                'value' => $value,
            ])
        );

        $this->artisan(QueueCompanyImports::class, [$option => $values, '--naics' => $naics]);

        Queue::assertPushed(ImportCompanies::class, 2);
    }

    #[Test, DataProvider('optionProvider')]
    public function it_does_not_skip_a_provided_option_if_it_has_already_been_exhausted_when_not_passing_an_naics($option, $values, $filter)
    {
        Queue::fake();

        $naics = '111';

        str($values)->remove(' ')->explode(',')->each(
            fn ($value) => FailedCompanyImportRecord::factory()->create([
                'filter' => $filter,
                'value' => $value,
                'naics' => $naics,
            ])
        );

        $this->artisan(QueueCompanyImports::class, [$option => $values]);

        Queue::assertPushed(ImportCompanies::class, 2);
    }

    #[Test]
    public function it_does_not_push_a_job_when_no_options_are_provided()
    {
        Queue::fake();

        $this->artisan(QueueCompanyImports::class);

        Queue::assertNothingPushed();
    }

    #[Test, DataProvider('invalidOptionProvider')]
    public function it_does_not_dispatch_a_job_if_an_invalid_list_of_values_is_provided($option, $values)
    {
        Queue::fake();

        $this->artisan(QueueCompanyImports::class, [$option => $values]);

        Queue::assertNothingPushed();
    }

    public static function optionProvider()
    {
        return [
            'State' => [
                '--states',
                'NJ, CA',
                'state',
            ],
            'Zipcode' => [
                '--zipcodes',
                '07001, 90603',
                'zipcode',
            ],
            'Domain' => [
                '--domains',
                'solarreviews.com, fixr.com',
                'domain',
            ],
            'Specialty' => [
                '--specialties',
                'leads, b2b',
                'specialty',
            ],
        ];
    }

    public static function invalidOptionProvider()
    {
        return [
            'State' => [
                '--states',
                'foo, 10, A1',
            ],
            'Zipcode' => [
                '--zipcodes',
                '---,abcde,1234,00000',
            ],
            'Domain' => [
                '--domains',
                'not-a-website, <EMAIL>',
            ],
            'Specialty' => [
                '--specialties',
                ' ',
            ],
        ];
    }
}
