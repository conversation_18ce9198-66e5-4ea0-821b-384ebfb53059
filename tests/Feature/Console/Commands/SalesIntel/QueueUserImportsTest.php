<?php

namespace Tests\Feature\Console\Commands\SalesIntel;

use App\Console\Commands\SalesIntel\QueueUserImports;
use App\Jobs\SalesIntel\ImportUsers;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\SalesIntel\FailedUserImportRecord;
use App\Models\SalesIntel\UserImportRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class QueueUserImportsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Queue::fake();

        Company::all()->each->delete();
    }

    #[Test]
    public function it_pushes_the_jobs_onto_the_imports_queue()
    {
        Company::factory()->createQuietly();

        $this->artisan(QueueUserImports::class);

        Queue::assertPushed(ImportUsers::class);

        Queue::assertPushedOn('imports', ImportUsers::class);
    }

    #[Test]
    public function it_dispatches_a_job_to_fetch_user_to_import()
    {
        $company = Company::factory()->createQuietly();

        $this->artisan(QueueUserImports::class)
            ->expectsOutput("Queuing User Imports for {$company->name} for domain {$company->website}");

        Queue::assertPushed(ImportUsers::class, fn ($job) => $job->company->is($company));
    }

    #[Test]
    public function it_accepts_a_list_of_comma_separated_ids_as_a_parameter()
    {
        $allCompanies = Company::factory(5)->createQuietly();

        $importingCompanies = $allCompanies->take(2);

        $ids = $importingCompanies->pluck('id')->implode(',');

        $this->artisan(QueueUserImports::class, compact('ids'))
            ->expectsOutput("Queuing User Imports for {$importingCompanies->first()->name} for domain {$importingCompanies->first()->website}")
            ->expectsOutput("Queuing User Imports for {$importingCompanies->last()->name} for domain {$importingCompanies->last()->website}");

        Queue::assertPushed(ImportUsers::class, 2);
    }

    #[Test]
    public function it_dispatches_one_job_per_company()
    {
        Company::factory(2)->createQuietly();

        $this->artisan(QueueUserImports::class);

        Queue::assertPushed(ImportUsers::class, 2);
    }

    #[Test]
    public function it_does_not_dispatch_a_job_for_a_company_that_already_has_users()
    {
        Company::factory()->createQuietly();

        CompanyUser::factory()
            ->for(Company::first())
            ->create();

        $this->artisan(QueueUserImports::class);

        Queue::assertNothingPushed();
    }

    #[Test]
    public function it_does_not_dispatch_a_job_for_a_company_that_does_not_have_a_website()
    {
        Company::factory()->createQuietly([
            'website' => '',
        ]);

        $this->artisan(QueueUserImports::class);

        Queue::assertNothingPushed();
    }

    #[Test]
    public function it_does_not_dispatch_a_job_for_a_company_that_has_already_been_queued()
    {
        $company = Company::factory()->createQuietly();

        UserImportRecord::factory()->for($company)->create();

        $this->artisan(QueueUserImports::class);

        Queue::assertNothingPushed();

        $company = Company::factory()->createQuietly();

        FailedUserImportRecord::factory()->for($company)->create();

        $this->artisan(QueueUserImports::class);

        Queue::assertNothingPushed();
    }

    #[Test]
    public function it_only_dispatches_the_oldest_100_records()
    {
        Company::factory(101)->createQuietly();

        $this->artisan(QueueUserImports::class);

        Queue::assertPushed(ImportUsers::class, 100);
    }

    #[Test]
    public function it_dispatches_on_the_imports_queue()
    {
        $company = Company::factory()->createQuietly();

        $this->artisan(QueueUserImports::class);

        Queue::assertPushedOn('imports', ImportUsers::class);
    }
}
