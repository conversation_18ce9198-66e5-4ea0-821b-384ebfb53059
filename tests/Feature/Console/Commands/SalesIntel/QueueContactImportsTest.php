<?php

namespace Tests\Feature\Console\Commands\SalesIntel;

use App\Console\Commands\SalesIntel\QueueContactImports;
use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectStatus;
use App\Jobs\SalesIntel\ImportContacts;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class QueueContactImportsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        Queue::fake();

        NewBuyerProspect::all()->each->delete();
    }

    #[Test]
    public function it_dispatches_a_job_to_fetch_contacts_to_import()
    {
        $prospect = NewBuyerProspect::factory()->sourcedFromSalesIntel()->create();

        $this->artisan(QueueContactImports::class)
            ->expectsOutput("Queuing Contact Imports for {$prospect->company_name}");

        Queue::assertPushed(ImportContacts::class, fn ($job) => $job->prospect->is($prospect));
    }

    #[Test]
    public function it_dispatches_one_job_per_prospect()
    {
        NewBuyerProspect::factory(2)->sourcedFromSalesIntel()->create();

        $this->artisan(QueueContactImports::class);

        Queue::assertPushed(ImportContacts::class, 2);
    }

    #[Test]
    public function it_does_not_dispatch_a_job_for_a_prospect_that_already_has_contacts()
    {
        NewBuyerProspect::factory()->sourcedFromSalesIntel()->create();

        Contact::factory()
            ->for(NewBuyerProspect::first(), 'prospect')
            ->create();

        $this->artisan(QueueContactImports::class);

        Queue::assertNothingPushed();
    }

    #[Test]
    public function it_does_not_dispatch_a_job_for_a_prospect_that_is_not_from_sales_intel()
    {
        NewBuyerProspect::factory()->sourcedFromAList()->create();
        NewBuyerProspect::factory()->sourcedFromRegistration()->create();

        $this->artisan(QueueContactImports::class);

        Queue::assertNothingPushed();
    }

    #[Test]
    public function it_does_not_dispatch_a_job_for_a_prospect_that_is_closed_or_resolved_due_to_no_contacts()
    {
        NewBuyerProspect::factory()->sourcedFromSalesIntel()->create([
            'status' => ProspectStatus::CLOSED,
        ]);

        NewBuyerProspect::factory()->sourcedFromSalesIntel()->create([
            'resolution' => ProspectResolution::NO_AVAILABLE_CONTACTS,
        ]);

        $this->artisan(QueueContactImports::class);

        Queue::assertNothingPushed();
    }

    #[Test]
    public function it_only_dispatches_the_oldest_100_records()
    {
        NewBuyerProspect::factory(101)->sourcedFromSalesIntel()->create();

        $this->artisan(QueueContactImports::class);

        Queue::assertPushed(ImportContacts::class, 100);
    }

    #[Test]
    public function it_dispatches_on_the_imports_queue()
    {
        NewBuyerProspect::factory()->sourcedFromSalesIntel()->create();

        $this->artisan(QueueContactImports::class);

        Queue::assertPushedOn('imports', ImportContacts::class);
    }
}
