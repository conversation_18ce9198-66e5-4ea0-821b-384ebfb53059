<?php

namespace Tests\Feature;

use App\Services\ContactCacheService;
use App\Services\EmailAddress\EmailAddressService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

use function PHPUnit\Framework\assertEquals;
use function PHPUnit\Framework\assertFalse;
use function PHPUnit\Framework\assertTrue;

class EmailServiceTest extends TestCase
{
    use RefreshDatabase;

    private EmailAddressService $emailService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->emailService = app(EmailAddressService::class);
    }

    #[DataProvider('emailFormatProvider')]
    public function test_format(string $input, string $expected): void
    {
        $formatted = $this->emailService->formatEmailAddress(email: $input);

        assertEquals($expected, $formatted);
    }

    #[DataProvider('emailFormatProvider')]
    public function test_add_email_to_cache(string $input, string $expected): void
    {
        $this->emailService->markEmailContacted(email: $input);

        assertTrue(Cache::has(ContactCacheService::CONTACTED_BASE_CACHE_KEY.$expected));

        $this->travel(1)->days();

        assertFalse(Cache::has(ContactCacheService::CONTACTED_BASE_CACHE_KEY.$expected));
    }

    public static function emailFormatProvider(): array
    {
        return [
            ['<EMAIL>', '<EMAIL>'],
            ['<EMAIL> ', '<EMAIL>'],
            ['  test   @ examp le. com', '<EMAIL>'],
        ];
    }
}
