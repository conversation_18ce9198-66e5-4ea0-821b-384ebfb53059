<?php

namespace Tests\Feature;

use App\Enums\BundleInvoiceStatus;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Events\Company\CompanySuspendedEvent;
use App\Jobs\CreateLegacyCompanyJob;
use App\Models\Bundle;
use App\Models\BundleInvoice;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Models\Permission;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class BundlesTest extends TestCase
{
    use RefreshDatabase;

    protected string $baseBundleUrl = '/internal-api/v1/bundles';

    protected string $baseInvoiceUrl = '/internal-api/v1/bundle-invoices';

    protected int $invalidId = 0;

    /** @todo Remove this since it's brittle! */
    protected int $testCompanyId = 1517;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        Bus::fake([
            CreateLegacyCompanyJob::class,
        ]);

        Event::fake([
            CompanySuspendedEvent::class,
        ]);

        IndustryService::factory()->create();
        Company::factory()->createQuietly([Company::FIELD_ID => $this->testCompanyId]);

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::MANAGE_BUNDLE->value]);
        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::MANAGE_BUNDLE_INVOICE->value]);

        $role = Role::findOrCreate(RoleType::BUNDLE_ADMIN->value);
        $role->givePermissionTo(PermissionType::MANAGE_BUNDLE->value);
        $role->givePermissionTo(PermissionType::MANAGE_BUNDLE_INVOICE->value);

        $this->user = User::factory()->create()->assignRole($role);

        $this->actingAs($this->user);
    }

    #[Test]
    public function a_bundle_can_be_created()
    {
        $bundle = Bundle::factory()->inactive()->raw([
            'created_by' => $this->user->id,
        ]);

        $this->post($this->baseBundleUrl, $bundle)
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'bundle',
                ],
            ]);

        $this->assertDatabaseHas('bundles', $bundle);
    }

    #[Test]
    public function a_bundle_can_be_updated()
    {
        $bundle = Bundle::factory()->create();

        $newBundle = [
            'name' => fake()->name,
            'note' => fake()->realText,
            'title' => fake()->name,
            'description' => fake()->realText,
            'cost' => fake()->randomFloat(2, 100, 500),
            'credit' => fake()->randomFloat(2, 600, 1000),
        ];

        $this->patch($this->baseBundleUrl.'/'.$bundle->id, $newBundle)
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'bundle',
                ],
            ]);

        $this->assertDatabaseMissing('bundles', $bundle->toArray());
        $this->assertDatabaseHas('bundles', $newBundle);
    }

    #[Test]
    public function an_invoice_can_be_created_for_a_bundle()
    {
        $invoice = [
            'bundle_id' => Bundle::factory()->create()->id,
            'company_id' => $this->testCompanyId,
            'billing_version' => 'v1',
        ];

        $this->post($this->baseInvoiceUrl, $invoice)
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'invoice',
                ],
            ]);

        $this->assertDatabaseHas('bundle_invoices', $invoice);
    }

    #[Test]
    public function an_invoices_status_can_be_updated()
    {
        $this->skipIfMissingPubSubService();

        $invoice = BundleInvoice::factory()->create();

        $updatedInvoice = [
            'transition' => 'fail',
            'fail_reason' => 'Marked as failed through the BundlesTest PHPUnit test script.',
        ];

        $this->patch($this->baseInvoiceUrl.'/transition/'.$invoice->id, $updatedInvoice)
            ->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'status',
                ],
            ]);

        $this->assertEquals(BundleInvoiceStatus::CANCELLED->value, $invoice->refresh()->status->value);
    }

    #[Test]
    public function a_bundle_can_be_deleted()
    {
        $bundle = Bundle::factory()->create();

        $this->delete($this->baseBundleUrl.'/'.$bundle->id);

        $this->assertSoftDeleted($bundle);
    }

    #[Test]
    public function a_bundle_invoice_can_be_deleted()
    {
        $this->skipIfMissingPubSubService();

        $invoice = BundleInvoice::factory()->create();

        $this->delete($this->baseInvoiceUrl.'/'.$invoice->id);

        $this->assertSoftDeleted($invoice);
    }

    #[Test]
    public function a_bundle_cannot_be_created_with_invalid_data(): void
    {
        $this->post($this->baseBundleUrl, [
            'cost' => 'badvalue',
        ])->assertInvalid();
    }

    #[Test]
    public function a_bundle_cannot_be_updated_if_it_does_not_exist(): void
    {
        $this->patch($this->baseBundleUrl.'/'.$this->invalidId, [
            'name' => fake()->name,
            'note' => fake()->realText,
        ])->assertNotFound();
    }

    #[Test]
    public function an_invoice_cannot_be_created_with_invalid_data(): void
    {
        $this->post($this->baseInvoiceUrl, [
            'company_id' => 'badvalue',
        ])->assertInvalid();
    }

    #[Test]
    public function an_invoice_cannot_be_updated_if_it_does_not_exist(): void
    {
        $this->patch($this->baseInvoiceUrl.'/transition/'.$this->invalidId, [
            'transition' => 'paid',
        ])->assertNotFound(404);
    }

    private function skipIfMissingPubSubService()
    {
        if (
            config('services.google.pubsub.service_account')
            && config('services.google.pubsub.project_id')
            && config('services.google.pubsub.topic')
        ) {
            return;
        }

        $this->markTestSkipped('Skipping due to PubSub service not being configured.');
    }
}
