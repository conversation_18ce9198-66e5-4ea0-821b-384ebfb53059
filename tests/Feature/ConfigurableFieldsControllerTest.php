<?php

namespace Tests\Feature;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\Odin\CompanyConfigurableFieldCategory;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Http\Controllers\API\IndustryManagement\ConfigurableFieldsController;
use App\Models\Odin\ConsumerConfigurableFieldCategory;
use App\Models\Odin\GlobalType;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Permission;
use App\Models\User;
use App\Services\Odin\ConfigurableFieldsService;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Illuminate\Testing\Fluent\AssertableJson;
use PHPUnit\Framework\Attributes\DataProvider;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ConfigurableFieldsControllerTest extends TestCase
{
    use RefreshDatabase;

    private string $baseApiPath = 'internal-api/v1/configurable-fields';

    private ConfigurableFieldsService $configurableFieldsService;

    protected function setUp(): void
    {
        parent::setUp();

        Permission::factory()->create([Permission::FIELD_NAME => PermissionType::ADMIN->value]);

        $role = Role::findOrCreate(RoleType::ADMIN->value);
        $role->givePermissionTo(PermissionType::ADMIN->value);

        $user = User::factory()->create()->assignRole($role);

        $this->actingAs($user);

        $this->configurableFieldsService = app(ConfigurableFieldsService::class);
    }

    /**
     * @return string[][]
     */
    public static function typeCategoryDataProvider(): array
    {
        $typeCategories = [];
        foreach (ConfigurableFieldsService::MODELS as $type => $categories) {
            foreach ($categories as $category => $class) {
                $typeCategories[] = [
                    'type' => $type,
                    'category' => $category,
                ];
            }
        }

        return $typeCategories;
    }

    private function getCategoryId(string $category): ?int
    {
        $categoryId = null;

        if ($category !== ConfigurableFieldsService::CATEGORY_GLOBAL) {
            if ($category === ConfigurableFieldsService::CATEGORY_INDUSTRY) {
                $categoryId = Industry::factory()->create()->{Industry::FIELD_ID};
            } elseif ($category === ConfigurableFieldsService::CATEGORY_SERVICE) {
                $categoryId = IndustryService::factory()->create()->{IndustryService::FIELD_ID};
            } elseif ($category === ConfigurableFieldsService::CATEGORY_GLOBAL_TYPE) {
                $categoryId = GlobalType::factory()->create()->{GlobalType::FIELD_ID};
            }
        }

        return $categoryId;
    }

    /**
     * @throws Exception
     */
    private function getExtraColumns(string $type, string $category): array
    {
        $columns = $this->configurableFieldsService->getModelColumns($type, $category)->toArray();

        return array_diff($columns, ['id', 'name', 'key', 'type', 'category', 'payload', 'updated_at', 'created_at', 'category_id']);
    }

    private function assertDatabaseHasFields(array $fields, Model $model, bool $checkId = false): void
    {
        foreach ($fields as $field) {
            unset($field[$model::CREATED_AT]);
            unset($field[$model::UPDATED_AT]);

            if (isset($field['payload'])) {
                if ($field['payload'] instanceof ConfigurableFieldDataModel) {
                    $payload = $field['payload']->toArray();
                } else {
                    $payload = (new ConfigurableFieldDataModel(collect($field['payload'])))->toArray();
                }

                $field['payload'] = $this->castAsJson($payload);
            }

            if (! $checkId) {
                unset($field[$model::FIELD_ID]);
            }

            $this->assertDatabaseHas($model::TABLE, $field);
        }
    }

    /**
     * @return array[]
     *
     * @throws Exception
     */
    private function getTestFields(string $type, string $category, int $consumerCategoryId, ?int $categoryId = null): array
    {
        $model = $this->configurableFieldsService->determineModel($type, $category);

        $fields = [
            [
                $model::FIELD_ID => 0,
                $model::FIELD_KEY => 'first',
                $model::FIELD_NAME => 'First',
                $model::FIELD_TYPE => 1,
                $model::CREATED_AT => null,
                $model::UPDATED_AT => null,
            ],
            [
                $model::FIELD_ID => 0,
                $model::FIELD_KEY => 'second',
                $model::FIELD_NAME => 'Second',
                $model::FIELD_TYPE => 2,
                $model::CREATED_AT => null,
                $model::UPDATED_AT => null,
            ],
        ];

        if (defined($model::class.'::FIELD_PAYLOAD')) {
            foreach ($fields as &$field) {
                $field[$model::FIELD_PAYLOAD] = [];
            }
        }
        if (defined($model::class.'::FIELD_CATEGORY')) {
            foreach ($fields as &$field) {
                $field[$model::FIELD_CATEGORY] = CompanyConfigurableFieldCategory::BASIC_INFO->value;
            }
        }
        if (defined($model::class.'::FIELD_CATEGORY_ID')) {
            foreach ($fields as &$field) {
                $field[$model::FIELD_CATEGORY_ID] = $consumerCategoryId;
            }
        }

        $optionalColumns = $this->getExtraColumns($type, $category);

        foreach ($optionalColumns as $optionalColumn) {
            $colType = Schema::getColumnType($model::TABLE, $optionalColumn);

            foreach ($fields as &$field) {
                if (stripos($colType, 'int') !== false
                || $colType === 'boolean') {
                    $field[$optionalColumn] = 0;
                } else {
                    $field[$optionalColumn] = '';
                }
            }
        }

        if ($category !== ConfigurableFieldsService::CATEGORY_GLOBAL) {
            if ($category === ConfigurableFieldsService::CATEGORY_INDUSTRY) {
                foreach ($fields as &$field) {
                    $field[$model::FIELD_INDUSTRY_ID] = $categoryId;
                }
            } elseif ($category === ConfigurableFieldsService::CATEGORY_SERVICE) {
                foreach ($fields as &$field) {
                    $field[$model::FIELD_INDUSTRY_SERVICE_ID] = $categoryId;
                }
            } elseif ($category === ConfigurableFieldsService::CATEGORY_GLOBAL_TYPE) {
                foreach ($fields as &$field) {
                    $field[$model::FIELD_GLOBAL_TYPE_ID] = $categoryId;
                }
            }
        }

        return $fields;
    }

    /**
     * @throws Exception
     *
     * @dataProvider typeCategoryDataProvider
     */
    #[DataProvider('typeCategoryDataProvider')]
    public function test_list($type, $category)
    {
        $params = "type={$type}&category={$category}";

        $categoryId = $this->getCategoryId($category);
        if ($categoryId) {
            $params = "{$params}&category_id={$categoryId}";
        }

        $model = $this->configurableFieldsService->determineModel($type, $category);
        $modelCols = $this->configurableFieldsService->getModelColumns($type, $category)->toArray();

        $res = $this->get($this->baseApiPath."/list?{$params}");

        $res->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'fields' => [
                        '*' => $modelCols,
                    ],
                    'field_columns',
                ],
            ])
            ->assertJson(function (AssertableJson $json) use ($modelCols) {
                $json
                    ->where('data.status', true)
                    ->where('data.field_columns', $modelCols);
            });

        $referenceFieldsListQuery = $model->newQuery();

        if ($category !== ConfigurableFieldsService::CATEGORY_GLOBAL) {
            if ($category === ConfigurableFieldsService::CATEGORY_INDUSTRY) {
                $referenceFieldsListQuery->where($model::FIELD_INDUSTRY_ID, $categoryId);
            } elseif ($category === ConfigurableFieldsService::CATEGORY_SERVICE) {
                $referenceFieldsListQuery->where($model::FIELD_INDUSTRY_SERVICE_ID, $categoryId);
            } elseif ($category === ConfigurableFieldsService::CATEGORY_GLOBAL_TYPE) {
                $referenceFieldsListQuery->where($model::FIELD_GLOBAL_TYPE_ID, $categoryId);
            }
        }

        $referenceFieldsList = $referenceFieldsListQuery->orderBy($model::FIELD_ID, 'asc')->get()->toArray();

        if (defined($model::class.'::FIELD_PAYLOAD')) {
            foreach ($referenceFieldsList as &$refField) {
                $refField[$model::FIELD_PAYLOAD] = $refField[$model::FIELD_PAYLOAD]->toArray();
            }
        }

        $fields = $res->json()['data']['fields'];

        usort($fields, function ($a, $b) use ($model) {
            return $a[$model::FIELD_ID] - $b[$model::FIELD_ID];
        });

        $referenceFieldsListCount = count($referenceFieldsList);

        for ($i = 0; $i < $referenceFieldsListCount; $i++) {
            foreach ($referenceFieldsList[$i] as $field => $value) {
                $this->assertEquals($value, $res['data']['fields'][$i][$field]);
            }
        }
    }

    /**
     * @throws Exception
     *
     * @dataProvider typeCategoryDataProvider
     */
    #[DataProvider('typeCategoryDataProvider')]
    public function test_create($type, $category)
    {
        $fakeConsumerCategory = ConsumerConfigurableFieldCategory::factory()->create();

        $categoryId = $this->getCategoryId($category);

        $model = $this->configurableFieldsService->determineModel($type, $category);

        $fields = $this->getTestFields($type, $category, $fakeConsumerCategory->id, $categoryId);

        $res = $this->post($this->baseApiPath.'/save', [
            ConfigurableFieldsController::REQUEST_TYPE => $type,
            ConfigurableFieldsController::REQUEST_CATEGORY => $category,
            ConfigurableFieldsController::REQUEST_CATEGORY_ID => $categoryId,
            ConfigurableFieldsController::REQUEST_FIELDS => $fields,
        ]);

        $res->assertStatus(200);

        $this->assertDatabaseHasFields($fields, $model);
    }

    /**
     * @throws Exception
     *
     * @dataProvider typeCategoryDataProvider
     */
    #[DataProvider('typeCategoryDataProvider')]
    public function test_update($type, $category)
    {
        $fakeConsumerCategory = ConsumerConfigurableFieldCategory::factory()->create();

        $categoryId = $this->getCategoryId($category);

        $model = $this->configurableFieldsService->determineModel($type, $category);

        $fields = $this->getTestFields($type, $category, $fakeConsumerCategory->id, $categoryId);

        $createRes = $this->configurableFieldsService->saveFields(collect($fields), $type, $category, $categoryId);

        $this->assertTrue($createRes);

        $fields = $this->configurableFieldsService->getFields($type, $category, $categoryId)->toArray();

        foreach ($fields as &$field) {
            if (isset($field['payload'])) {
                $field['payload'] = $field['payload']->toArray();
            }
        }

        $fields[0][$model::FIELD_NAME] = 'Updated First';

        $res = $this->post($this->baseApiPath.'/save', [
            ConfigurableFieldsController::REQUEST_TYPE => $type,
            ConfigurableFieldsController::REQUEST_CATEGORY => $category,
            ConfigurableFieldsController::REQUEST_CATEGORY_ID => $categoryId,
            ConfigurableFieldsController::REQUEST_FIELDS => $fields,
        ]);

        $res->assertStatus(200);

        $this->assertDatabaseHasFields($fields, $model, true);
    }

    /**
     * Ensure that the field key value in database is kept on field update
     *
     * @throws Exception
     *
     * @dataProvider typeCategoryDataProvider
     */
    #[DataProvider('typeCategoryDataProvider')]
    public function test_keep_database_field_key_on_update($type, $category)
    {
        $fakeConsumerCategory = ConsumerConfigurableFieldCategory::factory()->create();

        $categoryId = $this->getCategoryId($category);

        $model = $this->configurableFieldsService->determineModel($type, $category);

        $createdFields = $this->getTestFields($type, $category, $fakeConsumerCategory->id, $categoryId);

        $createRes = $this->configurableFieldsService->saveFields(collect($createdFields), $type, $category, $categoryId);

        $this->assertTrue($createRes);

        $fields = $this->configurableFieldsService->getFields($type, $category, $categoryId)->toArray();

        foreach ($fields as &$field) {
            if (isset($field['payload'])) {
                $field['payload'] = $field['payload']->toArray();
            }
        }

        $fields[1][$model::FIELD_KEY] = 'should_not_be_updated';

        $res = $this->post($this->baseApiPath.'/save', [
            ConfigurableFieldsController::REQUEST_TYPE => $type,
            ConfigurableFieldsController::REQUEST_CATEGORY => $category,
            ConfigurableFieldsController::REQUEST_CATEGORY_ID => $categoryId,
            ConfigurableFieldsController::REQUEST_FIELDS => $fields,
        ]);

        $updatedFields = $this->configurableFieldsService->getFields($type, $category, $categoryId)->toArray();

        $res->assertStatus(200);
        $this->assertEquals($updatedFields[1][$model::FIELD_KEY], $createdFields[1][$model::FIELD_KEY]);
    }

    /**
     * @throws Exception
     *
     * @dataProvider typeCategoryDataProvider
     */
    #[DataProvider('typeCategoryDataProvider')]
    public function test_delete($type, $category)
    {
        $fakeConsumerCategory = ConsumerConfigurableFieldCategory::factory()->create();

        $categoryId = $this->getCategoryId($category);

        $model = $this->configurableFieldsService->determineModel($type, $category);

        $fields = $this->getTestFields($type, $category, $fakeConsumerCategory->id, $categoryId);

        $createRes = $this->configurableFieldsService->saveFields(collect($fields), $type, $category, $categoryId);

        $this->assertTrue($createRes);

        $fields = $this->configurableFieldsService->getFields($type, $category, $categoryId)->toArray();

        foreach ($fields as &$field) {
            if (isset($field['payload'])) {
                $field['payload'] = $field['payload']->toArray();
            }
        }

        $deletedField = $fields[0];
        unset($fields[0]);

        $res = $this->post($this->baseApiPath.'/save', [
            'type' => $type,
            'category' => $category,
            'category_id' => $categoryId,
            'fields' => $fields,
        ]);

        $res->assertStatus(200);

        $this->assertDatabaseMissing($model::TABLE, $deletedField);
        $this->assertDatabaseHasFields($fields, $model, true);
    }

    public function test_get_type_categories()
    {
        $typeCategories = [];
        foreach (ConfigurableFieldsService::MODELS as $type => $categories) {
            $typeCategories[$type] = array_keys($categories);
        }

        $res = $this->get($this->baseApiPath.'/type-categories');

        $res->assertStatus(200)
            ->assertJson(function (AssertableJson $json) {
                $json->where('data.status', true)
                    ->has('data.type_categories', count(ConfigurableFieldsService::MODELS));
            })
            ->assertJson([
                'data' => [
                    'status' => true,
                    'type_categories' => $typeCategories,
                ],
            ]);
    }

    public function test_get_field_types()
    {
        $res = $this->get($this->baseApiPath.'/field-types');

        $fieldTypes = $this->configurableFieldsService->getFieldTypes()->toArray();

        $res->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'status',
                    'field_types',
                ],
            ])
            ->assertJson([
                'data' => [
                    'status' => true,
                    'field_types' => $fieldTypes,
                ],
            ]);
    }
}
