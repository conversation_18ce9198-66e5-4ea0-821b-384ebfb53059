<?php

namespace Tests\Feature;

use App\Enums\Advertising\AdvertisingTrackType;
use App\Enums\Odin\OriginDomain;
use App\Enums\Odin\Product as ProductEnum;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadTrackingUrl;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\Odin\Website;
use Database\Seeders\WebsitesSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class DeduplicateConsumerProductTrackingIdsTest extends TestCase
{
    private bool $migrated = false;

    protected function setUp(): void
    {
        parent::setUp();

        if (empty($this->migrated)) {
            $this->artisan('migrate:fresh');

            $this->migrated = true;
        }

        EloquentQuote::query()->delete();
        LeadTrackingUrl::query()->delete();
    }

    protected function tearDown(): void
    {
        EloquentQuote::query()->delete();
        LeadTrackingUrl::query()->delete();
        Consumer::query()->delete();
        ConsumerProduct::query()->delete();
        ConsumerProductTracking::query()->delete();
        Product::query()->delete();
        ServiceProduct::query()->delete();
        IndustryService::query()->delete();
        Industry::query()->delete();
        Website::query()->delete();

        parent::tearDown();
    }

    #[Test]
    public function successfully_deduplicate_consumer_product_tracking_ids(): void
    {
        DB::beginTransaction();

        $consumerProducts = null;
        Model::withoutEvents(function () use (&$consumerProducts) {
            $consumerProducts = ConsumerProduct::factory(10)
                ->sequence(fn (Sequence $s) => [
                    ConsumerProduct::FIELD_CONSUMER_ID => $s->index + 1,
                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => ($s->index % 4) + 1,
                ])
                ->create();
        });

        $consumerIds = $consumerProducts->pluck(ConsumerProduct::FIELD_CONSUMER_ID)->toArray();

        $largerConsumersByTrackingId = [];
        $consumerProducts
            ->groupBy(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
            ->each(function (Collection $consumers, int $trackingId) use (&$largerConsumersByTrackingId) {
                foreach ($consumers as $consumer) {
                    if (($largerConsumersByTrackingId[$trackingId] ?? 0) < $consumer->{ConsumerProduct::FIELD_CONSUMER_ID}) {
                        $largerConsumersByTrackingId[$trackingId] = $consumer->{ConsumerProduct::FIELD_CONSUMER_ID};
                    }
                }
            });

        $this->artisan('db:deduplicate-consumer-product-tracking-ids');

        foreach ($largerConsumersByTrackingId as $trackingId => $consumerId) {
            $this->assertDatabaseHas(ConsumerProduct::TABLE, [
                ConsumerProduct::FIELD_CONSUMER_ID => $consumerId,
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $trackingId,
            ]);
        }

        $nullifiedConsumers = array_diff($consumerIds, $largerConsumersByTrackingId);

        foreach ($nullifiedConsumers as $consumerId) {
            $this->assertDatabaseHas(ConsumerProduct::TABLE, [
                ConsumerProduct::FIELD_CONSUMER_ID => $consumerId,
                ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => null,
            ]);
        }

        DB::rollback();
    }

    #[Test]
    public function successfully_populate_missing_consumer_product_tracking_ids(): void
    {
        $this->seed(WebsitesSeeder::class);

        $websites = Website::query()->pluck(Website::FIELD_ID, Website::FIELD_ABBREVIATION)->toArray();

        $consumerProducts = null;
        $quotes = null;
        $consumers = null;
        Model::withoutEvents(function () use (&$consumerProducts, &$quotes, &$consumers) {
            $product = Product::factory()->create([Product::FIELD_NAME => ProductEnum::LEAD->value]);
            $serviceProduct = ServiceProduct::factory()->state([ServiceProduct::FIELD_PRODUCT_ID => $product->id])->create();

            $consumerProductTrackings = ConsumerProductTracking::factory(5)->create();

            $adTrackDataToCreate = [];
            for ($i = 0; $i < 5; $i++) {
                $adTrackDataToCreate[] = [
                    ConsumerProductTracking::AD_TRACK_TYPE => fake()->randomElement(AdvertisingTrackType::cases()),
                    ConsumerProductTracking::AD_TRACK_CODE => fake()->unique()->randomNumber(6),
                ];
            }

            $quotes = EloquentQuote::factory(10)
                ->sequence(function (Sequence $s) use ($consumerProductTrackings, $adTrackDataToCreate) {
                    if ($s->index % 2 === 0) {
                        return [
                            EloquentQuote::ID => $s->index + 1,
                            EloquentQuote::TRACK_NAME => $consumerProductTrackings->get($s->index % 5)->{ConsumerProductTracking::AD_TRACK_TYPE},
                            EloquentQuote::TRACK_CODE => $consumerProductTrackings->get($s->index % 5)->{ConsumerProductTracking::AD_TRACK_CODE},
                            EloquentQuote::ORIGIN => strtoupper(fake()->randomElement(OriginDomain::getAbbreviations())),
                        ];
                    } else {
                        return [
                            EloquentQuote::ID => $s->index + 1,
                            EloquentQuote::TRACK_NAME => $adTrackDataToCreate[$s->index % 5][ConsumerProductTracking::AD_TRACK_TYPE],
                            EloquentQuote::TRACK_CODE => $adTrackDataToCreate[$s->index % 5][ConsumerProductTracking::AD_TRACK_CODE],
                            EloquentQuote::ORIGIN => fake()->randomElement(OriginDomain::getAbbreviations()),
                        ];
                    }
                })
                ->has(LeadTrackingUrl::factory(), EloquentQuote::RELATION_TRACKING_URL)
                ->create()
                ->keyBy(EloquentQuote::ID);

            $consumers = Consumer::factory(10)
                ->sequence(fn (Sequence $s) => [
                    Consumer::FIELD_ID => $s->index + 1,
                    Consumer::FIELD_LEGACY_ID => $s->index + 1,
                ])
                ->create()
                ->keyBy(Consumer::FIELD_ID);

            $consumerProducts = ConsumerProduct::factory(10)
                ->sequence(fn (Sequence $s) => [
                    ConsumerProduct::FIELD_SERVICE_PRODUCT_ID => $serviceProduct->id,
                    ConsumerProduct::FIELD_CONSUMER_ID => $s->index + 1,
                    ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => $s->index % 2 === 0
                        ? fake()->unique()->randomElement($consumerProductTrackings->pluck(ConsumerProductTracking::ID)->toArray())
                        : null,
                ])
                ->create();
        });

        $consumerIdsWithoutTracking = $consumerProducts
            ->whereNull(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID)
            ->pluck(ConsumerProduct::FIELD_CONSUMER_ID)
            ->toArray();

        $quoteIdsByConsumer = $consumers
            ->only($consumerIdsWithoutTracking)
            ->pluck(Consumer::FIELD_ID, Consumer::FIELD_LEGACY_ID)
            ->toArray();

        $createdConsumerProductTrackingRecords = [];

        $quotes
            ->only(array_keys($quoteIdsByConsumer))
            ->each(function (EloquentQuote $quote) use ($websites, &$createdConsumerProductTrackingRecords) {
                $createdConsumerProductTrackingRecords[] = [
                    ConsumerProductTracking::AD_TRACK_TYPE => $quote->{EloquentQuote::TRACK_NAME},
                    ConsumerProductTracking::AD_TRACK_CODE => $quote->{EloquentQuote::TRACK_CODE},
                    ConsumerProductTracking::URL_START => $quote->{EloquentQuote::RELATION_TRACKING_URL}->{LeadTrackingUrl::URL_START},
                    ConsumerProductTracking::URL_CONVERT => $quote->{EloquentQuote::RELATION_TRACKING_URL}->{LeadTrackingUrl::URL_CONVERT},
                    ConsumerProductTracking::CALCULATOR_SOURCE => $quote->{EloquentQuote::RELATION_TRACKING_URL}->{LeadTrackingUrl::SOURCE},
                    ConsumerProductTracking::WEBSITE_ID => $websites[$quote->{EloquentQuote::ORIGIN}],
                ];
            });

        $this->artisan('db:populate-missing-consumer-product-tracking-ids');

        foreach ($createdConsumerProductTrackingRecords as $record) {
            $this->assertDatabaseHas(ConsumerProductTracking::TABLE, $record);
        }
    }
}
