<?php

namespace Tests\Feature\CompanyProfile;

use App\Http\Middleware\Odin\VerifyExternalWebsiteToken;
use App\Http\Requests\Discovery\StoreCompanyProfileRequest;
use App\Models\ClientTokenService;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\CompanyProfile\CompanyProfileReview;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use Database\Seeders\CompanyDiscoveryClientTokenSeeder;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyProfileControllerTest extends TestCase
{
    use RefreshDatabase;
    const string BASE_URL                 = 'api/company-discovery';
    const string INTEGRATION_TOKEN_HEADER = 'x-integration-token';

    protected function setUp(): void
    {
        parent::setUp();

        $this->seed(CompanyDiscoveryClientTokenSeeder::class);
        $this->withoutMiddleware([
            VerifyExternalWebsiteToken::class
        ]);
    }

    #[Test]
    public function should_throw_validation_error_if_industry_service_not_provided()
    {
        $response = $this->getClient()->getJson('/api/v2/top-specialists');

        $response->assertStatus(422);
        $response->assertJsonValidationErrors([
            "industry_service_slug" => [
                "The industry service slug field is required."
            ]
        ]);
    }

    #[Test]
    public function should_list_top_companies()
    {
        $industryService = IndustryService::factory()->create()->refresh();

        $companyProfile = CompanyProfile::factory()
            ->has(CompanyProfileIndustryService::factory()->state([
                CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id
            ]), CompanyProfile::RELATION_COMPANY_PROFILE_INDUSTRY_SERVICES)
            ->create();

        $response = $this->getClient()->get("/api/v2/top-specialists?industry_service_slug=" . $industryService->{IndustryService::FIELD_SLUG});
        $response->assertStatus(200);
        $response->assertJson([
            'companies' => [
                [
                    'name'            => $companyProfile->{CompanyProfile::FIELD_NAME},
                    'profile_slug'    => $companyProfile->{CompanyProfile::FIELD_PROFILE_SLUG},
                    'expert_rating'   => 'N/A',
                    'consumer_rating' => $companyProfile->{CompanyProfile::FIELD_RATING},
                    'total_reviews'   => 0
                ]
            ]
        ]);
    }

    #[Test]
    public function should_filter_top_companies()
    {
        $this->markTestSkipped("Could not get this working with database refresh or transactions");
        $industryService = IndustryService::factory()->create()->refresh();

        CompanyProfile::factory()
            ->has(CompanyProfileIndustryService::factory()->state([
                CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id
            ]), CompanyProfile::RELATION_COMPANY_PROFILE_INDUSTRY_SERVICES)
            ->has(CompanyProfileLocation::factory()->count(2), CompanyProfile::RELATION_COMPANY_PROFILE_LOCATIONS)
            ->count(3)
            ->create();

        $state = CompanyProfileLocation::query()->latest()->first()->location->{Location::STATE_ABBREVIATION};

        $url = "/api/v2/top-specialists?industry_service_slug=" . $industryService->{IndustryService::FIELD_SLUG};

        $response = $this->getClient()->get($url . "&state_abbreviation=" . $state);
        $response->assertJsonCount(1, 'companies');
    }

    #[Test]
    public function should_not_break_listing_with_filters()
    {
        $industryService = IndustryService::factory()->create()->refresh();

        CompanyProfile::factory()
            ->has(CompanyProfileIndustryService::factory()->state([
                CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id
            ]), CompanyProfile::RELATION_COMPANY_PROFILE_INDUSTRY_SERVICES)
            ->has(CompanyProfileLocation::factory()->count(2), CompanyProfile::RELATION_COMPANY_PROFILE_LOCATIONS)
            ->count(3)
            ->create();

        $url = "/api/v2/top-specialists?industry_service_slug=random&country_abbreviation=random&state_abbreviation=random&county_key=random";

        $response = $this->getClient()->get($url);
        $response->assertJsonCount(0, 'companies');
    }

    public function getClient(): CompanyProfileControllerTest
    {
        $clientTokenService = ClientTokenService::query()
            ->where(ClientTokenService::FIELD_SERVICE_KEY, ClientTokenService::COMPANY_DISCOVERY_API_SERVICE_KEY)
            ->first();

        $serviceKey = $clientTokenService->clientToken->client_token;

        return $this->withHeaders([
            self::INTEGRATION_TOKEN_HEADER => $serviceKey,
            'Authorization'                => $serviceKey
        ]);
    }

    #[Test]
    public function create_company_profile_successfully(): void
    {
        $this->markTestSkipped('Skip broken test temporarily');
        $data = CompanyProfile::factory()->toApi();

        /** @var Location $location */
        $location = Location::factory()
            ->zipcode()
            ->create([
                Location::ZIP_CODE => 0
            ]);
        Location::factory()
            ->create([
                Location::STATE              => $location->state,
                Location::STATE_ABBREVIATION => $location->state_abbr,
                Location::STATE_KEY          => $location->state_key,
            ]);

        $data[StoreCompanyProfileRequest::ADDRESSES] = [
            [
                'state'     => $location->state_abbr,
                'post_code' => 0,
                'raw'       => $location->state,
            ]
        ];

        $response = $this->getClient()->postJson(
            self::BASE_URL,
            $data
        );

        $response->assertStatus(200);

        $this->assertDatabaseHas(CompanyProfile::TABLE, [
            CompanyProfile::FIELD_NAME => $data[StoreCompanyProfileRequest::NAME],
        ]);

        $reviews = $data[StoreCompanyProfileRequest::REVIEWS];

        foreach ($reviews as $review) {
            $this->assertDatabaseHas(CompanyProfileReview::TABLE, [
                CompanyProfileReview::FIELD_TEXT   => $review[StoreCompanyProfileRequest::REVIEW_TEXT],
                CompanyProfileReview::FIELD_AUTHOR => $review[StoreCompanyProfileRequest::REVIEW_AUTHOR],
            ]);
        }

        $industryService = IndustryService::query()->where(IndustryService::FIELD_SLUG, $data[StoreCompanyProfileRequest::INDUSTRY_SERVICE_SLUG])->first();

        $this->assertDatabaseHas(CompanyProfileIndustryService::TABLE, [
            CompanyProfileIndustryService::FIELD_INDUSTRY_SERVICE_ID => $industryService->id,
        ]);

        $this->assertDatabaseHas(CompanyProfileLocation::TABLE, [
            CompanyProfileLocation::FIELD_LOCATION_ID => Location::query()
                ->where(Location::TYPE, Location::TYPE_STATE)
                ->where(Location::STATE_ABBREVIATION, $location->state_abbr)->first()->id,
        ]);
    }

    #[Test]
    public function update_company_profile_successfully(): void
    {
        $sharedWebsite = 'www.google.com';

        CompanyProfile::factory()
            ->has(CompanyProfileIndustryService::factory()->count(2), CompanyProfile::RELATION_COMPANY_PROFILE_INDUSTRY_SERVICES)
            ->has(CompanyProfileLocation::factory()->count(3), CompanyProfile::RELATION_COMPANY_PROFILE_LOCATIONS)
            ->has(CompanyProfileReview::factory()->count(5), CompanyProfile::RELATION_REVIEWS)
            ->create([CompanyProfile::FIELD_WEBSITES => [$sharedWebsite]]);

        $clientTokenService = ClientTokenService::query()
            ->where(ClientTokenService::FIELD_SERVICE_KEY, ClientTokenService::COMPANY_DISCOVERY_API_SERVICE_KEY)
            ->first();

        $serviceKey = $clientTokenService->clientToken->client_token;

        $data = CompanyProfile::factory()->toApi();

        $data[StoreCompanyProfileRequest::WEBSITES] = [$sharedWebsite];
        $data[StoreCompanyProfileRequest::DOMAINS] = [$sharedWebsite];

        $response = $this->withHeaders([
            self::INTEGRATION_TOKEN_HEADER => $serviceKey
        ])->postJson(
            self::BASE_URL,
            $data
        );

        $response->assertStatus(200);

        $this->assertDatabaseCount(CompanyProfile::TABLE, 1);
    }

    #[Test]
    public function it_rejects_requests_with_missing_or_invalid_header()
    {
        $response = $this->postJson(self::BASE_URL);

        $response->assertStatus(401)->assertJson(['message' => 'Unauthorized integration request.']);

        $response = $this->withHeaders([
            self::INTEGRATION_TOKEN_HEADER => 'wrong-value'
        ])->postJson(self::BASE_URL);

        $response->assertStatus(401)->assertJson(['message' => 'Unauthorized integration request.']);
    }
}
