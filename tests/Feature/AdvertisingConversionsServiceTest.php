<?php

namespace Tests\Feature;

use App\Abstracts\Advertising\AdvertisingConversionsServiceAbstract;
use App\DataModels\Advertising\ConversionRevenueInfo;
use App\DataModels\Odin\ConsumerProductTrackingPayloadDataModel;
use App\Enums\Advertising\Advertiser as AdvertiserEnum;
use App\Enums\Advertising\AdvertisingPlatform as AdvertisingPlatformEnum;
use App\Enums\Advertising\AdvertisingTrackType;
use App\Jobs\Advertising\UploadConversionDataJob;
use App\Models\Advertiser;
use App\Models\AdvertisingAccount;
use App\Models\AdvertisingAccountWebsite;
use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductTracking;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Services\Advertising\Conversions\AdvertisingConversionsServiceFactory;
use Carbon\CarbonImmutable;
use Database\Seeders\ClientTokenServiceSeeder;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Group;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;
use Throwable;

class AdvertisingConversionsServiceTest extends TestCase
{
    use RefreshDatabase;

    private AdvertisingConversionsServiceAbstract $service;

    private Collection $trackingRecords;

    private AdvertisingAccount $account;

    private CarbonImmutable $now;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = AdvertisingConversionsServiceFactory::make(AdvertisingConversionsServiceFactory::TEST_DUMMY);

        $this->now = CarbonImmutable::now('UTC');

        CarbonImmutable::setTestNow($this->now);
    }

    private function dummyServiceSetup(): void
    {
        $this->account = AdvertisingAccount::factory()->create();

        $accountWebsite = AdvertisingAccountWebsite::factory()
            ->create([
                AdvertisingAccountWebsite::FIELD_PLATFORM => $this->account->platform,
                AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID => $this->account->platform_account_id,
            ]);

        $this->trackingRecords = ConsumerProductTracking::factory(2)
            ->state(new Sequence(
                [
                    ConsumerProductTracking::ID => 1,
                    ConsumerProductTracking::AD_TRACK_CODE => 'trackcode1',
                    ConsumerProductTracking::AD_TRACK_TYPE => AdvertisingTrackType::ADWORDS->value,
                    ConsumerProductTracking::ESTIMATED_REVENUE => 50,
                    ConsumerProductTracking::URL_CONVERT => 'www.example.com',
                ],
                [
                    ConsumerProductTracking::ID => 2,
                    ConsumerProductTracking::AD_TRACK_CODE => 'trackcode2',
                    ConsumerProductTracking::AD_TRACK_TYPE => AdvertisingTrackType::FACEBOOK->value,
                    ConsumerProductTracking::ESTIMATED_REVENUE => 10,
                    ConsumerProductTracking::URL_CONVERT => 'www.example.com',
                ]
            ))
            ->create([
                ConsumerProductTracking::CONVERSION_UPLOADED => false,
                ConsumerProductTracking::WEBSITE_ID => $accountWebsite->website_id,
                ConsumerProductTracking::CREATED_AT => $this->now,
            ]);

        ConsumerProductTracking::query()
            ->where(ConsumerProductTracking::ID, 1)
            ->update([
                ConsumerProductTracking::PAYLOAD => null,
            ]);
    }

    #[Test]
    public function successfully_get_leads_with_tracking_id_and_estimated_revenue(): void
    {
        $this->dummyServiceSetup();

        ConsumerProduct::factory(10)
            ->state(new Sequence(
                [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 1],
                [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 2]
            ))
            ->create([
                ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                ConsumerProduct::FIELD_CREATED_AT => $this->now,
            ]);

        $conversionRevenueInfo = $this
            ->service
            ->getLeadsWithTrackingIdAndEstimatedRevenue(
                $this->account,
                AdvertisingPlatformEnum::tryFrom($this->account->platform)
            )
            ->toArray();

        $this->assertArrayHasKey(1, $conversionRevenueInfo);
        $this->assertArrayHasKey(2, $conversionRevenueInfo);

        $trackingRecords = $this->trackingRecords->keyBy(ConsumerProductTracking::ID);

        foreach ($trackingRecords as $trackingId => $trackingRecord) {
            $conversionRevenue = $conversionRevenueInfo[$trackingId];

            $this->assertEquals($trackingId, $conversionRevenue[ConversionRevenueInfo::CONSUMER_PRODUCT_TRACKING_ID]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::AD_TRACK_TYPE}, $conversionRevenue[ConversionRevenueInfo::TRACK_TYPE]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::AD_TRACK_CODE}, $conversionRevenue[ConversionRevenueInfo::TRACK_CODE]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::PAYLOAD}->toArray(), $conversionRevenue[ConversionRevenueInfo::TRACK_PAYLOAD]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::TRACK_CREATION_TIMESTAMP]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::DELIVERED_TIMESTAMP]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::REJECTED_TIMESTAMP]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::ORIGINAL_UPLOAD_TIMESTAMP]);

            $this->assertEquals(round($trackingRecord->{ConsumerProductTracking::ESTIMATED_REVENUE}, 2), round($conversionRevenue[ConversionRevenueInfo::REVENUE], 2));

            $this->assertEquals([], $conversionRevenue[ConversionRevenueInfo::PRODUCT_ASSIGNMENT_IDS]);
            $this->assertEquals([], $conversionRevenue[ConversionRevenueInfo::PRODUCT_REJECTION_IDS]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CONVERSION_UPLOADED}, $conversionRevenue[ConversionRevenueInfo::CONVERSION_UPLOADED_BEFORE]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::URL_CONVERT}, $conversionRevenue[ConversionRevenueInfo::CONVERSION_URL]);
        }
    }

    #[Test]
    public function successfully_get_sold_leads_with_tracking_id_and_revenue(): void
    {
        $this->dummyServiceSetup();

        $consumerProducts = ConsumerProduct::factory(4)
            ->state(new Sequence(
                [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 1],
                [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 2]
            ))
            ->create([
                ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                ConsumerProduct::FIELD_CREATED_AT => $this->now,
            ]);

        $consumerProducts = $consumerProducts->groupBy(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID);

        $productAssignments = collect();
        foreach ($consumerProducts as $trackingId => $trackedConsumerProducts) {
            $productAssignments->put(
                $trackingId,
                ProductAssignment::factory(4)
                    ->chargeableAndDelivered()
                    ->sequence(fn (Sequence $s) => [ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $trackedConsumerProducts->get($s->index % $trackedConsumerProducts->count())->id])
                    ->state([
                        ProductAssignment::FIELD_CONVERSION_UPLOADED => false,
                        ProductAssignment::FIELD_DELIVERED_AT => $this->now,
                        ProductAssignment::FIELD_REJECTION_EXPIRY => '0000-00-00 00:00:00',
                    ])
                    ->createQuietly()
            );
        }

        $productRejections = collect();
        foreach ($productAssignments as $trackingId => $trackedProductAssignments) {
            $productRejections->put(
                $trackingId,
                ProductRejection::factory()->create([
                    ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID => $trackedProductAssignments->first()->id,
                    ProductRejection::FIELD_DELETED_AT => null,
                    ProductRejection::FIELD_CONVERSION_UPLOADED => false,
                    ProductRejection::CREATED_AT => $this->now,
                ])
            );
        }

        $conversionRevenueInfo = $this
            ->service
            ->getSoldLeadsWithTrackingIdAndRevenue(
                $this->account,
                AdvertisingPlatformEnum::tryFrom($this->account->platform)
            )
            ->toArray();

        $this->assertArrayHasKey(1, $conversionRevenueInfo);
        $this->assertArrayHasKey(2, $conversionRevenueInfo);

        $trackingRecords = $this->trackingRecords->keyBy(ConsumerProductTracking::ID);

        foreach ($trackingRecords as $trackingId => $trackingRecord) {
            $conversionRevenue = $conversionRevenueInfo[$trackingId];

            $this->assertEquals($trackingId, $conversionRevenue[ConversionRevenueInfo::CONSUMER_PRODUCT_TRACKING_ID]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::AD_TRACK_TYPE}, $conversionRevenue[ConversionRevenueInfo::TRACK_TYPE]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::AD_TRACK_CODE}, $conversionRevenue[ConversionRevenueInfo::TRACK_CODE]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::PAYLOAD}->toArray(), $conversionRevenue[ConversionRevenueInfo::TRACK_PAYLOAD]);

            $deliveredAt = $consumerProducts->get($trackingId)->first()->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT}->first()->{ProductAssignment::FIELD_DELIVERED_AT}->timestamp;

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::TRACK_CREATION_TIMESTAMP]);
            $this->assertEquals($deliveredAt, $conversionRevenue[ConversionRevenueInfo::DELIVERED_TIMESTAMP]);
            $this->assertEquals($deliveredAt, $conversionRevenue[ConversionRevenueInfo::ORIGINAL_UPLOAD_TIMESTAMP]);
            $this->assertEquals($productRejections->get($trackingId)->{ProductRejection::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::REJECTED_TIMESTAMP]);

            $totalRevenue = 0;
            foreach ($productAssignments->get($trackingId) as $productAssignment) {
                $totalRevenue += $productAssignment->{ProductAssignment::RELATION_PRODUCT_REJECTIONS}->isEmpty() ? $productAssignment->{ProductAssignment::FIELD_COST} : 0;
            }

            $this->assertEquals(round($totalRevenue, 2), round($conversionRevenue[ConversionRevenueInfo::REVENUE], 2));

            $productAssignmentIds = [];
            foreach ($consumerProducts->get($trackingId) as $consumerProduct) {
                foreach ($consumerProduct->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT} as $productAssignment) {
                    $productAssignmentIds[] = $productAssignment->{ProductAssignment::FIELD_ID};
                }
            }

            $this->assertEqualsCanonicalizing($productAssignmentIds, $conversionRevenue[ConversionRevenueInfo::PRODUCT_ASSIGNMENT_IDS]);

            $productRejectionIds = [];
            foreach ($consumerProducts->get($trackingId) as $consumerProduct) {
                foreach ($consumerProduct->{ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT} as $productAssignment) {
                    foreach ($productAssignment->{ProductAssignment::RELATION_PRODUCT_REJECTIONS} as $productRejection) {
                        $productRejectionIds[] = $productRejection->{ProductRejection::FIELD_ID};
                    }
                }
            }

            $this->assertEqualsCanonicalizing($productRejectionIds, $conversionRevenue[ConversionRevenueInfo::PRODUCT_REJECTION_IDS]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CONVERSION_UPLOADED}, $conversionRevenue[ConversionRevenueInfo::CONVERSION_UPLOADED_BEFORE]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::URL_CONVERT}, $conversionRevenue[ConversionRevenueInfo::CONVERSION_URL]);
        }
    }

    #[Test]
    public function successfully_get_unsold_good_to_sell_leads_with_tracking_id(): void
    {
        $this->dummyServiceSetup();

        $consumerProducts = ConsumerProduct::factory(10)
            ->state(new Sequence(
                [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 1],
                [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 2]
            ))
            ->create([
                ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                ConsumerProduct::FIELD_CREATED_AT => $this->now,
            ])
            ->groupBy(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID);

        $conversionRevenueInfo = $this
            ->service
            ->getUnsoldGoodToSellLeadsWithTrackingId(
                $this->account,
                AdvertisingPlatformEnum::tryFrom($this->account->platform)
            )
            ->toArray();

        $this->assertArrayHasKey(1, $conversionRevenueInfo);
        $this->assertArrayHasKey(2, $conversionRevenueInfo);

        $trackingRecords = $this->trackingRecords->keyBy(ConsumerProductTracking::ID);

        foreach ($trackingRecords as $trackingId => $trackingRecord) {
            $conversionRevenue = $conversionRevenueInfo[$trackingId];

            $this->assertEquals($trackingId, $conversionRevenue[ConversionRevenueInfo::CONSUMER_PRODUCT_TRACKING_ID]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::AD_TRACK_TYPE}, $conversionRevenue[ConversionRevenueInfo::TRACK_TYPE]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::AD_TRACK_CODE}, $conversionRevenue[ConversionRevenueInfo::TRACK_CODE]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::PAYLOAD}->toArray(), $conversionRevenue[ConversionRevenueInfo::TRACK_PAYLOAD]);

            $this->assertEquals($consumerProducts->get($trackingId)->first()->{ConsumerProduct::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::DELIVERED_TIMESTAMP]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CREATED_AT}->timestamp, $conversionRevenue[ConversionRevenueInfo::TRACK_CREATION_TIMESTAMP]);
            $this->assertEquals(0, $conversionRevenue[ConversionRevenueInfo::REJECTED_TIMESTAMP]);
            $this->assertEquals(0, $conversionRevenue[ConversionRevenueInfo::ORIGINAL_UPLOAD_TIMESTAMP]);

            $this->assertEquals(0, $conversionRevenue[ConversionRevenueInfo::REVENUE]);

            $this->assertEquals([], $conversionRevenue[ConversionRevenueInfo::PRODUCT_ASSIGNMENT_IDS]);
            $this->assertEquals([], $conversionRevenue[ConversionRevenueInfo::PRODUCT_REJECTION_IDS]);

            $this->assertEquals($trackingRecord->{ConsumerProductTracking::CONVERSION_UPLOADED}, $conversionRevenue[ConversionRevenueInfo::CONVERSION_UPLOADED_BEFORE]);
            $this->assertEquals($trackingRecord->{ConsumerProductTracking::URL_CONVERT}, $conversionRevenue[ConversionRevenueInfo::CONVERSION_URL]);
        }
    }

    #[Test, Group('successful_failure')]
    public function successfully_upload_meta_conversions(): void
    {
        /**
         * It's not possible to generate fake click ID's for our respective ad platforms, so the upload test always fails.
         * What we are looking for here is a "successful" failure, that is, the entire process succeeds up until click ID verification.
         * Since this will always throw an exception, it will break production/development pipelines, so this only runs locally.
         *
         * The reason why it isn't recommended to simply assert an exception is thrown, is because there are different exceptions with different
         * messages/contents that the API's can throw, and their contents must be validated manually to make sure they're the specific exception we want to throw
         */
        if (app()->runningCI()) {
            $this->markTestSkipped('Upload conversions test only runs locally');
        }

        try {
            $advertiser = Advertiser::factory()->create([
                Advertiser::FIELD_KEY => AdvertiserEnum::getKey(AdvertiserEnum::WADE),
                Advertiser::FIELD_NAME => AdvertiserEnum::WADE->getDisplayName(),
            ]);

            $this->account = AdvertisingAccount::factory()->create([
                AdvertisingAccount::FIELD_PLATFORM => AdvertisingPlatformEnum::META->value,
                AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID => random_int(100000000000000000, 1000000000000000000),
                AdvertisingAccount::FIELD_ADVERTISER_ID => $advertiser->{Advertiser::FIELD_ID},
                AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS => 1,
                AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_LAST_RUN_TIMESTAMP => 0,
            ]);

            $accountWebsite = AdvertisingAccountWebsite::factory()
                ->create([
                    AdvertisingAccountWebsite::FIELD_PLATFORM => $this->account->platform,
                    AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID => $this->account->platform_account_id,
                ]);

            $this->trackingRecords = ConsumerProductTracking::factory(2)
                ->state(new Sequence(
                    [
                        ConsumerProductTracking::ID => 1,
                        ConsumerProductTracking::AD_TRACK_CODE => $this->account->{AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID},
                        ConsumerProductTracking::AD_TRACK_TYPE => AdvertisingTrackType::FACEBOOK->value,
                        ConsumerProductTracking::ESTIMATED_REVENUE => 50,
                        ConsumerProductTracking::URL_CONVERT => 'www.example.com',
                        ConsumerProductTracking::PAYLOAD => ConsumerProductTrackingPayloadDataModel::fromArray([
                            ConsumerProductTrackingPayloadDataModel::FBCLID => 'test321',
                            ConsumerProductTrackingPayloadDataModel::USER_AGENT => 'PHP/654321;',
                            ConsumerProductTrackingPayloadDataModel::FB_PIXEL => 'fb.0.1234567.7654321',
                        ]),
                    ],
                    [
                        ConsumerProductTracking::ID => 2,
                        ConsumerProductTracking::AD_TRACK_CODE => $this->account->{AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID},
                        ConsumerProductTracking::AD_TRACK_TYPE => AdvertisingTrackType::FACEBOOK->value,
                        ConsumerProductTracking::ESTIMATED_REVENUE => 10,
                        ConsumerProductTracking::URL_CONVERT => 'www.example.com',
                        ConsumerProductTracking::PAYLOAD => ConsumerProductTrackingPayloadDataModel::fromArray([
                            ConsumerProductTrackingPayloadDataModel::FBCLID => 'test123',
                            ConsumerProductTrackingPayloadDataModel::USER_AGENT => 'PHP/123456;',
                            ConsumerProductTrackingPayloadDataModel::FB_PIXEL => 'fb.0.7654321.1234567',
                        ]),
                    ]
                ))
                ->create([
                    ConsumerProductTracking::CONVERSION_UPLOADED => false,
                    ConsumerProductTracking::WEBSITE_ID => $accountWebsite->website_id,
                    ConsumerProductTracking::CREATED_AT => $this->now,
                ]);

            $consumerProducts = ConsumerProduct::factory(4)
                ->state(new Sequence(
                    [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 1],
                    [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 2]
                ))
                ->create([
                    ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                    ConsumerProduct::FIELD_CREATED_AT => $this->now,
                ]);

            $consumerProducts = $consumerProducts->groupBy(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID);

            $productAssignments = collect();
            foreach ($consumerProducts as $trackingId => $trackedConsumerProducts) {
                $productAssignments->put(
                    $trackingId,
                    ProductAssignment::factory(4)
                        ->chargeableAndDelivered()
                        ->sequence(fn (Sequence $s) => [ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $trackedConsumerProducts->get($s->index % $trackedConsumerProducts->count())->id])
                        ->state([
                            ProductAssignment::FIELD_CONVERSION_UPLOADED => false,
                            ProductAssignment::FIELD_DELIVERED_AT => $this->now,
                            ProductAssignment::FIELD_REJECTION_EXPIRY => '0000-00-00 00:00:00',
                        ])
                        ->createQuietly()
                );
            }

            $this->seed(ClientTokenServiceSeeder::class);
            ClientToken::create([
                ClientToken::FIELD_SERVICE_ID => ClientTokenService::query()->where(ClientTokenService::FIELD_SERVICE_KEY, ClientTokenService::META_ADS_API_SERVICE_KEY)->first()->id,
                ClientToken::FIELD_CLIENT_TOKEN => config('services.meta.wade_ads.admin_system_user_token'),
                ClientToken::FIELD_ADDITIONAL_DATA => [
                    'advertiser' => AdvertiserEnum::getKey(AdvertiserEnum::WADE),
                ],
            ]);

            UploadConversionDataJob::dispatchSync(AdvertisingPlatformEnum::META, AdvertiserEnum::WADE, false);
        } catch (Throwable $e) {
            logger()->error($e);
        }
    }

    #[Test, Group('successful_failure')]
    public function successfully_upload_google_conversion(): void
    {
        /**
         * It's not possible to generate fake click ID's for our respective ad platforms, so the upload test always fails.
         * What we are looking for here is a "successful" failure, that is, the entire process succeeds up until click ID verification.
         * Since this will always throw an exception, it will break production/development pipelines, so this only runs locally.
         *
         * The reason why it isn't recommended to simply assert an exception is thrown, is because there are different exceptions with different
         * messages/contents that the API's can throw, and their contents must be validated manually to make sure they're the specific exception we want to throw
         */
        if (app()->runningCI()) {
            $this->markTestSkipped('Upload conversions test only runs locally');
        }

        try {
            $advertiser = Advertiser::factory()->create([
                Advertiser::FIELD_KEY => AdvertiserEnum::getKey(AdvertiserEnum::GABE),
                Advertiser::FIELD_NAME => AdvertiserEnum::GABE->getDisplayName(),
            ]);

            $this->account = AdvertisingAccount::factory()->create([
                AdvertisingAccount::FIELD_PLATFORM => AdvertisingPlatformEnum::GOOGLE->value,
                AdvertisingAccount::FIELD_ADVERTISER_ID => $advertiser->{Advertiser::FIELD_ID},
                AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_INTERVAL_HOURS => 1,
                AdvertisingAccount::FIELD_UPLOAD_CONVERSIONS_LAST_RUN_TIMESTAMP => 0,
                AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID => config('services.google.ads.test_account_id'),
            ]);

            $accountWebsite = AdvertisingAccountWebsite::factory()
                ->create([
                    AdvertisingAccountWebsite::FIELD_PLATFORM => $this->account->platform,
                    AdvertisingAccountWebsite::FIELD_PLATFORM_ACCOUNT_ID => $this->account->platform_account_id,
                ]);

            $this->trackingRecords = ConsumerProductTracking::factory(2)
                ->state(new Sequence(
                    [
                        ConsumerProductTracking::ID => 1,
                        ConsumerProductTracking::AD_TRACK_CODE => 'abc123',
                        ConsumerProductTracking::AD_TRACK_TYPE => AdvertisingTrackType::ADWORDS->value,
                        ConsumerProductTracking::ESTIMATED_REVENUE => 50,
                        ConsumerProductTracking::URL_CONVERT => 'www.example.com',
                    ],
                    [
                        ConsumerProductTracking::ID => 2,
                        ConsumerProductTracking::AD_TRACK_CODE => '123abc',
                        ConsumerProductTracking::AD_TRACK_TYPE => AdvertisingTrackType::GBRAID->value,
                        ConsumerProductTracking::ESTIMATED_REVENUE => 10,
                        ConsumerProductTracking::URL_CONVERT => 'www.example.com',
                    ]
                ))
                ->create([
                    ConsumerProductTracking::CONVERSION_UPLOADED => false,
                    ConsumerProductTracking::WEBSITE_ID => $accountWebsite->website_id,
                    ConsumerProductTracking::CREATED_AT => $this->now,
                ]);

            $consumerProducts = ConsumerProduct::factory(4)
                ->state(new Sequence(
                    [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 1],
                    [ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID => 2]
                ))
                ->create([
                    ConsumerProduct::FIELD_GOOD_TO_SELL => true,
                    ConsumerProduct::FIELD_CREATED_AT => $this->now,
                ]);

            $consumerProducts = $consumerProducts->groupBy(ConsumerProduct::FIELD_CONSUMER_PRODUCT_TRACKING_ID);

            $productAssignments = collect();
            foreach ($consumerProducts as $trackingId => $trackedConsumerProducts) {
                $productAssignments->put(
                    $trackingId,
                    ProductAssignment::factory(4)
                        ->chargeableAndDelivered()
                        ->sequence(fn (Sequence $s) => [ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => $trackedConsumerProducts->get($s->index % $trackedConsumerProducts->count())->id])
                        ->state([
                            ProductAssignment::FIELD_CONVERSION_UPLOADED => false,
                            ProductAssignment::FIELD_DELIVERED_AT => $this->now,
                            ProductAssignment::FIELD_REJECTION_EXPIRY => '0000-00-00 00:00:00',
                        ])
                        ->createQuietly()
                );
            }

            UploadConversionDataJob::dispatchSync(AdvertisingPlatformEnum::GOOGLE, AdvertiserEnum::GABE, false);
        } catch (Throwable $e) {
            logger()->error($e);
        }
    }
}
