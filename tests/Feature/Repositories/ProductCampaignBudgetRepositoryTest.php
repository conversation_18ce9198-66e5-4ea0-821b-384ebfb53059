<?php

namespace Tests\Feature\Repositories;

use App\DataModels\Odin\Prices\BudgetUsageData;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\RejectionReasons;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product as ProductModel;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ProductRejection;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ProductCampaignBudgetRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class ProductCampaignBudgetRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private bool $initialized = false;

    private ?ProductCampaign $productCampaign;

    private ?ProductCampaignBudgetRepository $productCampaignBudgetRepository;

    private ?int $appointmentProductId;

    protected function setUp(): void
    {
        $this->markTestSkipped('Appointments deactivated');

        parent::setUp();

        if (blank(config('services.google.pubsub.service_account'))) {
            $this->markTestSkipped('Skipping due to missing PubSub credentials.');
        }

        if (! $this->initialized) {
            $this->productCampaignBudgetRepository = app(ProductCampaignBudgetRepository::class);

            IndustryService::factory()->create();

            $company = Company::factory()
                ->has(CompanyConfiguration::factory(), Company::RELATION_CONFIGURATION)
                ->create([Company::FIELD_ID => 1517]);

            $this->productCampaign = ProductCampaign::factory()
                ->for($company, ProductCampaign::RELATION_COMPANY)
                ->create();

            ProductCampaignBudget::factory(2)
                ->for($this->productCampaign, ProductCampaignBudget::RELATION_PRODUCT_CAMPAIGN)
                ->state(new Sequence(
                    [ProductCampaignBudget::FIELD_QUALITY_TIER => QualityTier::IN_HOME->value],
                    [ProductCampaignBudget::FIELD_QUALITY_TIER => QualityTier::ONLINE->value]
                ))
                ->create([ProductCampaignBudget::FIELD_STATUS => true]);

            $this->appointmentProductId = ProductModel::factory()->create([ProductModel::FIELD_NAME => ProductEnum::APPOINTMENT->value])->id;

            $this->initialized = true;
        }
    }

    /**
     * @return string[][]
     */
    public static function productCampaignBudgetDataProvider(): array
    {
        $baseProductAssignment = [
            ProductAssignment::FIELD_COMPANY_ID => 1517,
            ProductAssignment::FIELD_COST => 100,
            ProductAssignment::FIELD_CHARGEABLE => true,
            ProductAssignment::FIELD_DELIVERED => true,
            ProductAssignment::FIELD_EXCLUDE_BUDGET => 0,
            ProductAssignment::FIELD_SALE_TYPE_ID => 1,
            ProductAssignment::FIELD_DELIVERED_AT => Carbon::now('UTC'),
            ProductAssignment::FIELD_REJECTION_EXPIRY => Carbon::now('UTC'),
            ProductAssignment::FIELD_OFF_HOUR_SALE => false,
        ];

        $baseProductRejection = [
            ProductRejection::FIELD_COMPANY_USER_ID => 1,
            ProductRejection::FIELD_REASON => RejectionReasons::OTHER->value,
        ];

        return [
            'No product assignments' => [
                'qualityTier' => QualityTierEnum::IN_HOME,
                'budgetType' => BudgetCategory::VERIFIED,
                'valueType' => ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND,
                'productAssignmentData' => null,
                'productAssignmentCount' => 1,
                'productRejectionData' => null,
            ],
            'One product assignment daily appointments' => [
                'qualityTier' => QualityTierEnum::ONLINE,
                'budgetType' => BudgetCategory::VERIFIED,
                'valueType' => ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS,
                'productAssignmentData' => $baseProductAssignment,
                'productAssignmentCount' => 1,
                'productRejectionData' => null,
            ],
            'One product assignment daily spend' => [
                'qualityTier' => QualityTierEnum::IN_HOME,
                'budgetType' => BudgetCategory::VERIFIED,
                'valueType' => ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND,
                'productAssignmentData' => $baseProductAssignment,
                'productAssignmentCount' => 1,
                'productRejectionData' => null,
            ],
            'Two product assignments daily appointments' => [
                'qualityTier' => QualityTierEnum::ONLINE,
                'budgetType' => BudgetCategory::VERIFIED,
                'valueType' => ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS,
                'productAssignmentData' => $baseProductAssignment,
                'productAssignmentCount' => 2,
                'productRejectionData' => null,
            ],
            'Two product assignments daily spend' => [
                'qualityTier' => QualityTierEnum::IN_HOME,
                'budgetType' => BudgetCategory::VERIFIED,
                'valueType' => ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND,
                'productAssignmentData' => $baseProductAssignment,
                'productAssignmentCount' => 2,
                'productRejectionData' => null,
            ],
            'Two product assignments one rejected' => [
                'qualityTier' => QualityTierEnum::ONLINE,
                'budgetType' => BudgetCategory::VERIFIED,
                'valueType' => ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND,
                'productAssignmentData' => $baseProductAssignment,
                'productAssignmentCount' => 2,
                'productRejectionData' => $baseProductRejection,
            ],
            'Unlimited budget' => [
                'qualityTier' => QualityTierEnum::IN_HOME,
                'budgetType' => BudgetCategory::VERIFIED,
                'valueType' => ProductCampaignBudget::VALUE_TYPE_NO_LIMIT,
                'productAssignmentData' => null,
                'productAssignmentCount' => 0,
                'productRejectionData' => null,
            ],
        ];
    }

    protected function instantiateProductAssignmentAndRejection(
        ?array $productAssignmentData,
        ?array $productRejectionData,
        QualityTierEnum $qualityTier,
        int $productAssignmentCount
    ): array {
        $productAssignments = null;
        $productRejection = null;

        ProductAssignment::query()->delete();
        ProductRejection::query()->delete();

        if ($productAssignmentData
        && $productAssignmentCount > 0) {
            $productAssignments = [];

            $consumerProductId = ConsumerProduct::factory()
                ->has(ServiceProduct::factory()->state([ServiceProduct::FIELD_PRODUCT_ID => $this->appointmentProductId]), ConsumerProduct::RELATION_SERVICE_PRODUCT)
                ->create()
                ->{ConsumerProduct::FIELD_ID};

            for ($i = 0; $i < $productAssignmentCount; $i++) {
                $productAssignment = new ProductAssignment($productAssignmentData);

                $productAssignment->{ProductAssignment::FIELD_CONSUMER_PRODUCT_ID} = $consumerProductId;

                $productAssignment->{ProductAssignment::FIELD_CAMPAIGN_ID} = $this->productCampaign->{ProductCampaign::FIELD_ID};

                $productAssignment->{ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID} = $this->productCampaign
                    ->{ProductCampaign::RELATION_BUDGETS}
                    ->where(ProductCampaignBudget::FIELD_QUALITY_TIER, $qualityTier)
                    ->first()
                    ->{ProductCampaignBudget::FIELD_ID};

                $productAssignment->save();

                $productAssignments[] = $productAssignment;
            }

            if ($productRejectionData) {
                $productRejection = new ProductRejection($productRejectionData);

                $productAssignments[0]->{ProductAssignment::FIELD_PARENT_PRODUCT_ID} = $this->appointmentProductId;

                $productAssignments[0]->save();

                $productRejection->{ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID} = $productAssignments[0]->{ProductAssignment::FIELD_ID};

                $productRejection->save();
            }
        }

        return [
            'productAssignments' => $productAssignments,
            'productRejection' => $productRejection,
        ];
    }

    /**
     * @throws Exception
     *
     * @dataProvider productCampaignBudgetDataProvider
     */
    #[DataProvider('productCampaignBudgetDataProvider')]
    public function test_get_product_campaign_budget_usage(
        QualityTierEnum $qualityTier,
        BudgetCategory $budgetType,
        string $valueType,
        ?array $productAssignmentData,
        int $productAssignmentCount,
        ?array $productRejectionData
    ): void {
        $productCampaignBudget = $this->productCampaign
            ->{ProductCampaign::RELATION_BUDGETS}
            ->where(ProductCampaignBudget::FIELD_QUALITY_TIER, $qualityTier)
            ->first();

        $productCampaignBudget->{ProductCampaignBudget::FIELD_VALUE_TYPE} = $valueType;
        $productCampaignBudget->{ProductCampaignBudget::FIELD_BUDGET_START_TIMESTAMP} = Carbon::now('UTC')->subWeek();

        $productCampaignBudget->save();

        extract($this->instantiateProductAssignmentAndRejection($productAssignmentData, $productRejectionData, $qualityTier, $productAssignmentCount));

        $budgetUsageData = $this->productCampaignBudgetRepository->getProductCampaignBudgetUsage([$this->productCampaign->{ProductCampaign::FIELD_ID}], $qualityTier);

        $campaignBudgetUsageData = $budgetUsageData->getBudgetUsageData($this->productCampaign->{ProductCampaign::FIELD_ID});

        $this->assertEquals(false, $campaignBudgetUsageData->isEmpty());

        $companyData = $campaignBudgetUsageData->get(BudgetUsageData::COMPANY_INFO);

        $this->assertEquals(1517, $companyData->get(BudgetUsageData::COMPANY_ID));

        $budgetData = $campaignBudgetUsageData->get(BudgetUsageData::BUDGET_INFO);

        $budgetTypeData = $budgetData->get($budgetType->value);

        $this->assertEquals($valueType, $budgetTypeData->get(BudgetUsageData::BUDGET_UNIT));

        if ($valueType !== ProductCampaignBudget::VALUE_TYPE_NO_LIMIT) {
            if (! empty($productAssignments)) {
                $spent = $valueType === ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_SPEND
                    ? collect($productAssignments)->sum(ProductAssignment::FIELD_COST)
                    : count($productAssignments);

                if (! empty($productRejection)) {
                    $spent -= $productRejection->{ProductRejection::RELATION_PRODUCT_ASSIGNMENT}->{ProductAssignment::FIELD_COST};
                }

                $this->assertEquals($spent, $budgetTypeData->get(BudgetUsageData::BUDGET_SPENT));
            } else {
                $this->assertEquals(0, $budgetTypeData->get(BudgetUsageData::BUDGET_SPENT));
            }
        }
    }
}
