<?php

namespace Tests\Feature;

use App\Models\Affiliates\Affiliate;
use App\Services\SlugService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class SlugServiceTest extends TestCase
{
    use RefreshDatabase;

    private SlugSErvice $slugService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->slugService = app(SlugService::class);

        Affiliate::factory()->create([
            Affiliate::FIELD_NAME => 'already-exists',
        ]);

        Affiliate::factory()->create([
            Affiliate::FIELD_NAME => 'already-exists-twice',
        ]);

        Affiliate::factory()->create([
            Affiliate::FIELD_NAME => 'already-exists-twice-1',
        ]);
    }

    #[DataProvider('slugProvider')]
    public function test_create_unique_slug(string $input, string $expectedOutput): void
    {
        $output = $this->slugService->createUniqueSlug(
            modelToCheck: Affiliate::class,
            slugField: Affiliate::FIELD_NAME,
            slugValue: $input,
        );

        $this->assertEquals($expectedOutput, $output);
    }

    public static function slugProvider(): array
    {
        return [
            [' Test', 'test'],
            ['With SpaCE', 'with-space'],
            ['Already Exists', 'already-exists-1'],
            ['Already Exists Twice', 'already-exists-twice-2'],
        ];
    }

}
