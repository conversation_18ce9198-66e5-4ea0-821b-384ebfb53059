<?php

namespace Tests\Feature\Services;

use App\Models\CompanyProfileCallNumber;
use App\Models\Odin\Company;
use App\Models\Phone;
use App\Services\Companies\CompanyProfileCallService;
use App\Services\Twilio\TwilioPhoneNumberService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyProfileCallServiceTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_returns_a_free_proxy_phone_from_the_pool_if_available()
    {
        /** @var Company $company */
        $company = Company::factory()->createQuietly();
        /** @var Phone $phone */
        $phone = Phone::factory()->createQuietly();

        CompanyProfileCallNumber::query()->createQuietly([
            CompanyProfileCallNumber::FIELD_PHONE_COMPANY_ID => $company->id,
            CompanyProfileCallNumber::FIELD_PHONE_ID => $phone->id,
            CompanyProfileCallNumber::FIELD_IN_USE => false,
            CompanyProfileCallNumber::FIELD_IN_USE_SINCE => null
        ]);

        $mockService = Mockery::mock(TwilioPhoneNumberService::class);

        $mockService->shouldNotReceive('acquireNumber');
        $this->app->instance(TwilioPhoneNumberService::class, $mockService);

        $proxyPhone = app(CompanyProfileCallService::class)->getProxyPhone($company);

        $this->assertEquals($phone->phone, $proxyPhone);
    }

    #[Test]
    public function it_purchases_a_new_proxy_phone_if_none_are_available_in_the_pool()
    {
        /** @var Company $company */
        $company = Company::factory()->createQuietly();
        /** @var Phone $phone */
        $phone = Phone::factory()->createQuietly();

        CompanyProfileCallNumber::query()->createQuietly([
            CompanyProfileCallNumber::FIELD_PHONE_COMPANY_ID => $company->id,
            CompanyProfileCallNumber::FIELD_PHONE_ID => Phone::factory()->createQuietly()->id,
            CompanyProfileCallNumber::FIELD_IN_USE => true,
            CompanyProfileCallNumber::FIELD_IN_USE_SINCE => now()
        ]);

        $mockService = Mockery::mock(TwilioPhoneNumberService::class);
        $mockService->shouldReceive('acquireNumber')
            ->once()
            ->andReturn($phone);
        $this->app->instance(TwilioPhoneNumberService::class, $mockService);

        $proxyPhone = app(CompanyProfileCallService::class)->getProxyPhone($company);

        $this->assertEquals($phone->phone, $proxyPhone);
    }
}
