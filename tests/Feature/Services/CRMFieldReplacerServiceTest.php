<?php

declare(strict_types=1);

namespace Tests\Feature\Services;

use App\Campaigns\Delivery\CRM\CRMFieldReplacerService;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\WatchdogVideo;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use SolarInvestments\Testing\SkipTestWhenRunningCI;
use Tests\TestCase;

class CRMFieldReplacerServiceTest extends TestCase
{
    use RefreshDatabase, SkipTestWhenRunningCI;

    #[Test]
    public function it_replaces_watchdog_video_id_field()
    {
        $watchdogVideo = WatchdogVideo::factory()->create([
            'link' => 'https://test.com',
        ]);

        $productAssignment = ProductAssignment::factory()
            ->has(Company::factory())
            ->create([
                'consumer_product_id' => ConsumerProduct::factory()->lead()->create([
                    'consumer_id' => $watchdogVideo->consumer->id,
                ])->id,
            ]);

        $companyCampaign = CompanyCampaign::create([
            'company_id' => $productAssignment->company_id,
            'product_id' => $productAssignment->consumerProduct->serviceProduct->product_id,
            'service_id' => $productAssignment->consumerProduct->serviceProduct->industry_service_id,
        ]);

        $this->assertEquals(
            "get the {$watchdogVideo->link} from the watchdog_videos table",
            app(CRMFieldReplacerService::class)->replaceField(
                $productAssignment->consumerProduct,
                $companyCampaign,
                'get the [watchdog_video_link] from the watchdog_videos table'
            )
        );
    }
}
