<?php

namespace Feature\Services;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\Consumer;
use App\Models\Odin\ProductAssignment;
use App\Models\Phone;
use App\Services\Odin\ConsumerProxyPhoneService;
use App\Services\Twilio\TwilioPhoneNumberService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ConsumerProxyPhoneServiceTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_gets_proxy_phone_if_enabled_for_company()
    {
        /** @var Company $company */
        $company = Company::factory()->createQuietly();
        /** @var Phone $phone */
        $phone = Phone::factory()->createQuietly();
        $nthLead = (int) (100 / Consumer::PROXY_PHONE_PERCENT);

        $company->configuration()->createQuietly([
            CompanyConfiguration::FIELD_CONSUMER_PROXY_PHONE_ENABLED => true
        ]);

        ProductAssignment::factory()->count($nthLead)->createQuietly([
            ProductAssignment::FIELD_COMPANY_ID => $company->id,
            ProductAssignment::FIELD_DELIVERED_AT => now(),
            ProductAssignment::FIELD_CHARGEABLE => true,
            ProductAssignment::FIELD_DELIVERED => true
        ]);

        $mockService = Mockery::mock(TwilioPhoneNumberService::class);
        $mockService->shouldReceive('acquireNumber')
            ->once()
            ->andReturn($phone);

        $this->app->instance(TwilioPhoneNumberService::class, $mockService);

        $phoneNumber = app(ConsumerProxyPhoneService::class)->getProxyPhone($company->id);

        $this->assertEquals($phone->phone, $phoneNumber);
    }

    #[Test]
    public function it_does_not_get_proxy_phone_if_disabled_for_company()
    {
        /** @var Company $company */
        $company = Company::factory()->createQuietly();

        $company->configuration()->createQuietly([
            CompanyConfiguration::FIELD_CONSUMER_PROXY_PHONE_ENABLED => false
        ]);

        $mockService = Mockery::mock(TwilioPhoneNumberService::class);
        $mockService->shouldNotReceive('acquireNumber');

        $this->app->instance(TwilioPhoneNumberService::class, $mockService);

        $phone = app(ConsumerProxyPhoneService::class)
            ->getProxyPhone($company->id);

        $this->assertEquals(null, $phone);
    }
}
