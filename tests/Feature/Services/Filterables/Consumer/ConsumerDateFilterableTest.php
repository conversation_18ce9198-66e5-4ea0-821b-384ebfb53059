<?php

declare(strict_types=1);

namespace Tests\Feature\Services\Filterables\Consumer;

use App\Models\Odin\Consumer;
use App\Services\Filterables\Consumer\ConsumerDateFilterable;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ConsumerDateFilterableTest extends TestCase
{
    #[Test]
    public function it_filters_on_fixed_date_range()
    {
        Carbon::setTestNow('2021-09-01 00:00:00');

        $query = Consumer::query()->join('consumer_products', 'consumers.id', '=', 'consumer_products.consumer_id');

        $to = Carbon::now()->subDays(30)->toDateTimeString();
        $from = Carbon::now()->subDays(35)->toDateTimeString();
        $preset = null;

        $result = app(ConsumerDateFilterable::class)->runQuery($query, compact('from', 'to', 'preset'));

        $this->assertEquals("select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-07-28 00:00:00' and `consumer_products`.`created_at` <= '2021-08-02 00:00:00'", $result->toRawSql());
    }

    #[Test]
    public function it_filters_on_fixed_date_range_that_is_in_future()
    {
        Carbon::setTestNow('2021-09-01 00:00:00');

        $query = Consumer::query()->join('consumer_products', 'consumers.id', '=', 'consumer_products.consumer_id');

        $to = Carbon::now()->addDays(35)->toDateTimeString();
        $from = Carbon::now()->addDays(30)->toDateTimeString();
        $preset = null;

        $result = app(ConsumerDateFilterable::class)->runQuery($query, compact('from', 'to', 'preset'));

        $this->assertEquals("select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` <= '2021-09-01 12:00:00'", $result->toRawSql());
    }

    #[Test]
    public function it_filters_on_fixed_date_range_from_exceeds_to()
    {
        Carbon::setTestNow('2021-09-01 00:00:00');

        $query = Consumer::query()->join('consumer_products', 'consumers.id', '=', 'consumer_products.consumer_id');

        $to = Carbon::now()->subDays(35)->toDateTimeString();
        $from = Carbon::now()->subDays(value: 30)->toDateTimeString();
        $preset = null;

        $result = app(ConsumerDateFilterable::class)->runQuery($query, compact('from', 'to', 'preset'));

        $this->assertEquals("select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-08-31 00:00:00' and `consumer_products`.`created_at` <= '2021-09-01 00:00:00'", $result->toRawSql());
    }

    #[Test]
    public function it_is_missing_date_range_and_preset()
    {
        Carbon::setTestNow('2021-09-01 00:00:00');

        $query = Consumer::query()->join('consumer_products', 'consumers.id', '=', 'consumer_products.consumer_id');

        $to = null;
        $from = null;
        $preset = null;

        $result = app(ConsumerDateFilterable::class)->runQuery($query, compact('from', 'to', 'preset'));

        $this->assertEquals("select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-08-31 00:00:00' and `consumer_products`.`created_at` <= '2021-09-01 00:00:00'", $result->toRawSql());
    }

    #[Test, DataProvider('dateRangePreset')]
    public function it_filters_on_relative_date_range($preset, $rawQueryString)
    {
        Carbon::setTestNow('2021-10-01 00:00:00');

        $query = Consumer::query()->join('consumer_products', 'consumers.id', '=', 'consumer_products.consumer_id');

        $to = null;
        $from = null;

        $result = app(ConsumerDateFilterable::class)->runQuery($query, compact('from', 'to', 'preset'));

        $this->assertEquals($rawQueryString, $result->toRawSql());
    }

    public static function dateRangePreset(): array
    {
        return [
            'Last Hour' => [
                'Last Hour',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-30 23:00:00'",
            ],
            'Last 4 Hours' => [
                'Last 4 Hours',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-30 20:00:00'",
            ],
            'Last 8 Hours' => [
                'Last 8 Hours',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-30 16:00:00'",
            ],
            'Today Only' => [
                'Today Only',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-10-01 00:00:00'",
            ],
            'Yesterday Only' => [
                'Yesterday Only',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-30 00:00:00' and `consumer_products`.`created_at` <= '2021-10-01 00:00:00'",
            ],
            'Last 24 Hours' => [
                'Last 24 Hours',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-30 00:00:00'",
            ],
            'Last Week' => [
                'Last Week',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-24 00:00:00'",
            ],
            'Last Month' => [
                'Last Month',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-01 00:00:00'",
            ],
            'This Calendar Month' => [
                'This Calendar Month',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-10-01 00:00:00'",
            ],
            'Last 3 Months' => [
                'Last 3 Months',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-07-01 00:00:00'",
            ],
            'Last 12 Months' => [
                'Last 12 Months',
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2020-10-01 00:00:00'",
            ],
            'Non Option' => [
                'Non Option',
                'select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id`',
            ],
            'Null' => [
                null,
                "select * from `consumers` inner join `consumer_products` on `consumers`.`id` = `consumer_products`.`consumer_id` where `consumer_products`.`created_at` >= '2021-09-30 00:00:00' and `consumer_products`.`created_at` <= '2021-10-01 00:00:00'",
            ],
        ];
    }
}
