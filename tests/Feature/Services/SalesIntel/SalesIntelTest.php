<?php

namespace Tests\Feature\Services\SalesIntel;

use App\Enums\Prospects\ProspectResolution;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\IndustryService;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\SalesIntel\CompanyImportRecord;
use App\Models\SalesIntel\FailedCompanyImportRecord;
use App\Services\SalesIntel\SalesIntel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Client\Request;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SalesIntelTest extends TestCase
{
    use RefreshDatabase;

    protected string $peopleUrl = 'https://api.salesintel.io/service/people';

    protected string $companyUrl = 'https://api.salesintel.io/service/company';

    protected array $personResponse;

    protected array $companyResponse;

    protected function setUp(): void
    {
        parent::setUp();

        Http::preventStrayRequests();

        Config::set('services.salesintel.api.key', 'foobar');

        $this->personResponse = [
            'search_results' => [
                [
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'job_department' => 'IT',
                    'job_title' => 'Software Developer',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
        ];

        $this->companyResponse = [
            'search_results' => [
                [
                    'company_id' => 12345,
                    'phone_numbers' => [
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                    'locations' => [
                        [
                            'city' => 'Fake City',
                            'postal_code' => '12345-0987',
                            'state' => 'NJ',
                            'street1' => '1234 N Fake St',
                        ],
                    ],
                    'primary_domain' => 'examplewebsite.com',
                    'primary_name' => 'Example Company Inc',
                ],
            ],
        ];
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_users_for_companies()
    {
        $company = Company::factory()->createQuietly();

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]),
        ]);

        app(SalesIntel::class)->importUsers($company);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_creates_company_user_records_from_the_people_api_response()
    {
        $company = Company::factory()->createQuietly([
            'website' => 'www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]) => Http::response(json_encode($this->personResponse)),
        ]);

        app(SalesIntel::class)->importUsers($company);

        $this->assertDatabaseCount('company_users', 1);

        $this->assertDatabaseHas('company_users', [
            'company_id' => $company->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'title' => 'Software Developer',
            'department' => 'IT',
            'cell_phone' => '(*************',
            'formatted_cell_phone' => 5551234567,
            'office_phone' => '(*************',
            'formatted_office_phone' => 5558901112,
            'status' => 1,
        ]);
    }

    #[Test]
    public function it_creates_a_company_user_import_record_when_storing_company_users()
    {
        $company = Company::factory()->createQuietly([
            'website' => 'www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]) => Http::response(json_encode($this->personResponse)),
        ]);

        app(SalesIntel::class)->importUsers($company);

        $companyUser = CompanyUser::first();

        $this->assertEquals(CompanyUser::IMPORT_SOURCE_SALES_INTEL, $companyUser->import_source);

        $this->assertDatabaseCount('sales_intel_user_import_records', 1);

        $this->assertDatabaseHas('sales_intel_user_import_records', [
            'company_id' => $company->id,
            'company_user_id' => $companyUser->id,
        ]);
    }

    #[Test, DataProvider('invalidUserProvider')]
    public function it_does_not_create_company_user_records_when($user)
    {
        $company = Company::factory()->createQuietly([
            'website' => 'www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]) => Http::response(json_encode(['search_results' => [$user]])),
        ]);

        app(SalesIntel::class)->importUsers($company);

        $this->assertDatabaseCount('company_users', 0);
    }

    #[Test, DataProvider('validUserProvider')]
    public function it_still_creates_a_company_user_record_when($user)
    {
        $company = Company::factory()->createQuietly([
            'website' => 'www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]) => Http::response(json_encode(['search_results' => [$user]])),
        ]);

        app(SalesIntel::class)->importUsers($company);

        $this->assertDatabaseCount('company_users', 1);
    }

    #[Test]
    public function it_creates_a_failed_import_record_if_no_users_are_found_for_a_company()
    {
        $company = Company::factory()->createQuietly([
            'website' => 'www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]) => Http::response(json_encode(['search_results' => []])),
        ]);

        app(SalesIntel::class)->importUsers($company);

        $this->assertDatabaseCount('company_users', 0);
        $this->assertDatabaseCount('sales_intel_user_import_records', 0);
        $this->assertDatabaseCount('sales_intel_failed_user_import_records', 1);

        $this->assertDatabaseHas('sales_intel_failed_user_import_records', [
            'company_id' => $company->id,
        ]);
    }

    #[Test, DataProvider('expectedExceptionCodes')]
    public function it_accounts_for_errors_from_the_api_and_does_not_create_a_failed_record(int $status)
    {
        $this->expectException(RequestException::class);
        $this->expectExceptionCode($status);

        $company = Company::factory()->createQuietly([
            'website' => 'www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]) => Http::response(status: $status),
        ]);

        app(SalesIntel::class)->importUsers($company);

        $this->assertDatabaseCount('company_users', 0);
        $this->assertDatabaseCount('sales_intel_user_import_records', 0);
        $this->assertDatabaseCount('sales_intel_failed_user_import_records', 0);
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_users_for_prospects()
    {
        $prospect = NewBuyerProspect::factory()->create([
            'company_website' => 'https://www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => 'foobar.com',
                'is_international' => false,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importContacts($prospect);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_creates_prospect_contact_records_from_the_people_api_response()
    {
        $prospect = NewBuyerProspect::factory()->create([
            'company_website' => 'https://www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => 'foobar.com',
                'is_international' => false,
            ]) => Http::response($this->personResponse),
        ]);

        app(SalesIntel::class)->importContacts($prospect);

        $this->assertDatabaseCount('prospect_contacts', 1);

        $this->assertDatabaseHas('prospect_contacts', [
            'prospect_id' => $prospect->id,
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'title' => 'Software Developer',
            'department' => 'IT',
            'cell_phone' => '(*************',
            'office_phone' => '(*************',
        ]);
    }

    #[Test, DataProvider('invalidUserProvider')]
    public function it_does_not_create_prospect_contact_records_when($user)
    {
        $prospect = NewBuyerProspect::factory()->create([
            'company_website' => 'https://www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => 'foobar.com',
                'is_international' => false,
            ]) => Http::response(json_encode(['search_results' => [$user]])),
        ]);

        app(SalesIntel::class)->importContacts($prospect);

        $this->assertDatabaseCount('prospect_contacts', 0);
    }

    #[Test, DataProvider('validUserProvider')]
    public function it_still_creates_a_prospect_contact_record_when($user)
    {
        $prospect = NewBuyerProspect::factory()->create([
            'company_website' => 'https://www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => 'foobar.com',
                'is_international' => false,
            ]) => Http::response(json_encode(['search_results' => [$user]])),
        ]);

        app(SalesIntel::class)->importContacts($prospect);

        $this->assertDatabaseCount('prospect_contacts', 1);
    }

    #[Test]
    public function it_sets_a_resolution_for_the_prospect_if_no_contacts_are_available_to_import()
    {
        $prospect = NewBuyerProspect::factory()->create([
            'company_website' => 'https://www.foobar.com',
        ]);

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => 'foobar.com',
                'is_international' => false,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importContacts($prospect);

        $prospect->refresh();

        $this->assertTrue($prospect->status === ProspectStatus::INITIAL->value);
        $this->assertTrue($prospect->resolution === ProspectResolution::NO_AVAILABLE_CONTACTS->value);
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies()
    {
        $zipcode = '12345';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies_using_the_appropriate_page()
    {
        $zipcode = '12345';

        CompanyImportRecord::factory()->zipcodeFilter($zipcode)->create();

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 2,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_creates_prospect_records_from_the_company_api_response()
    {
        $zipcode = '12345';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        $this->assertDatabaseCount('new_buyer_prospects', 1);

        $this->assertDatabaseHas('new_buyer_prospects', [
            'external_reference' => 12345,
            'user_id' => 0,
            'company_id' => 0,
            'source' => ProspectSource::SALESINTEL,
            'company_name' => 'Example Company Inc',
            'company_website' => 'examplewebsite.com',
            'company_phone' => '(*************',
            'address_street' => '1234 N Fake St',
            'address_city_key' => str('Fake City')->kebab(),
            'address_state_abbr' => 'NJ',
        ]);

        $this->assertEquals('12345', NewBuyerProspect::first()->source_data['zip_codes'][0]);
    }

    #[Test]
    public function it_creates_a_prospect_import_record_when_storing_the_company()
    {
        $zipcode = '12345';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        $this->assertDatabaseCount('sales_intel_company_import_records', 1);

        $this->assertDatabaseHas('sales_intel_company_import_records', [
            'importable_type' => NewBuyerProspect::class,
            'importable_id' => NewBuyerProspect::latest('id')->first()->id,
            'filter' => 'zipcode',
            'value' => $zipcode,
            'page' => 1,
        ]);
    }

    #[Test]
    public function it_creates_a_prospect_import_record_accounting_for_zipcode_and_page()
    {
        $zipcode = '12345';

        CompanyImportRecord::factory()->forProspect()->zipcodeFilter($zipcode)->create();

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 2,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        $this->assertDatabaseCount('sales_intel_company_import_records', 2);

        $this->assertDatabaseHas('sales_intel_company_import_records', [
            'importable_type' => NewBuyerProspect::class,
            'importable_id' => NewBuyerProspect::latest('id')->first()->id,
            'filter' => 'zipcode',
            'value' => $zipcode,
            'page' => 2,
        ]);
    }

    #[Test]
    public function it_does_not_create_prospects_when_missing_a_company_name()
    {
        $zipcode = '12345';

        $this->companyResponse['search_results'][0]['primary_name'] = null;

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        $this->assertDatabaseCount('new_buyer_prospects', 0);
    }

    #[Test]
    public function it_does_not_create_prospects_when_missing_a_company_website()
    {
        $zipcode = '12345';

        $this->companyResponse['search_results'][0]['primary_domain'] = null;

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        $this->assertDatabaseCount('new_buyer_prospects', 0);
    }

    #[Test]
    public function it_does_not_create_prospects_when_a_prospect_exists_with_the_same_website()
    {
        NewBuyerProspect::factory()->create([
            'company_website' => 'examplewebsite.com',
        ]);

        $zipcode = '12345';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        $this->assertDatabaseCount('new_buyer_prospects', 1);
    }

    #[Test]
    public function it_creates_a_failed_import_record_if_no_companies_are_imported()
    {
        $zipcode = '12345';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);

        $this->assertDatabaseCount('sales_intel_failed_company_import_records', 1);

        $this->assertDatabaseHas('sales_intel_failed_company_import_records', [
            'filter' => 'zipcode',
            'value' => $zipcode,
        ]);
    }

    #[Test]
    public function it_throws_an_exception_if_there_are_no_more_records_available_for_the_given_zipcode()
    {
        $zipcode = '12345';

        FailedCompanyImportRecord::factory()->zipcodeFilter($zipcode)->create();

        $this->expectExceptionMessage("No more Company records exist for zipcode: {$zipcode}.");

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode);
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies_with_state()
    {
        $state = 'CA';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_states' => $state,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies_using_the_appropriate_page_based_on_state()
    {
        $state = 'CA';

        CompanyImportRecord::factory()->stateFilter($state)->create();

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_states' => $state,
                'page' => 2,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_throws_an_exception_if_there_are_no_more_records_available_for_the_given_state()
    {
        $state = 'CA';

        FailedCompanyImportRecord::factory()->stateFilter($state)->create();

        $this->expectExceptionMessage("No more Company records exist for state: {$state}.");

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_location_states' => $state,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state);
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies_with_domain()
    {
        $domain = 'google.com';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_domain' => $domain,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('domain', $domain);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies_using_the_appropriate_page_based_on_domain()
    {
        $domain = 'google.com';

        CompanyImportRecord::factory()->domainFilter($domain)->create();

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_domain' => $domain,
                'page' => 2,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('domain', $domain);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_flags_prospect_records_from_the_company_api_response_when_imported_via_domain()
    {
        $domain = 'examplewebsite.com';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_domain' => $domain,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('domain', $domain);

        $this->assertDatabaseCount('new_buyer_prospects', 1);

        $this->assertDatabaseHas('new_buyer_prospects', [
            'external_reference' => 12345,
            'user_id' => 0,
            'company_id' => 0,
            'source' => ProspectSource::SALESINTEL,
            'company_name' => 'Example Company Inc',
            'company_website' => 'examplewebsite.com',
            'company_phone' => '(*************',
            'address_street' => '1234 N Fake St',
            'address_city_key' => str('Fake City')->kebab(),
            'address_state_abbr' => 'NJ',
        ]);

        $this->assertEquals('true', NewBuyerProspect::first()->source_data['targeted']);
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies_with_optional_naics_filter()
    {
        $naics = ['111', '222', '333'];
        $state = 'CA';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_naics_codes' => implode(',', $naics),
                'company_location_states' => $state,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state, $naics);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_sends_a_request_to_sales_intel_to_import_companies_using_the_appropriate_page_based_on_naics()
    {
        $naics = ['111', '222', '333'];
        $state = 'CA';

        CompanyImportRecord::factory()->stateFilter($state)->create([
            'naics' => implode(',', $naics),
        ]);

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_naics_codes' => implode(',', $naics),
                'company_location_states' => $state,
                'page' => 2,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state, $naics);

        Http::assertSent(fn (Request $request) => $request->hasHeader('X-CB-ApiKey', 'foobar'));
    }

    #[Test]
    public function it_accounts_for_naics_when_creating_a_company_import_record()
    {
        $naics = ['111', '222', '333'];
        $state = 'CA';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_naics_codes' => implode(',', $naics),
                'company_location_states' => $state,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state, $naics);

        $this->assertDatabaseCount('sales_intel_company_import_records', 1);

        $this->assertDatabaseHas('sales_intel_company_import_records', [
            'importable_type' => NewBuyerProspect::class,
            'importable_id' => NewBuyerProspect::latest('id')->first()->id,
            'filter' => 'state',
            'value' => $state,
            'page' => 1,
            'naics' => implode(',', $naics),
        ]);
    }

    #[Test]
    public function it_accounts_for_naics_when_creating_a_failed_import_record()
    {
        $naics = ['111', '222', '333'];
        $state = 'CA';

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_naics_codes' => implode(',', $naics),
                'company_location_states' => $state,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state, $naics);

        $this->assertDatabaseCount('sales_intel_failed_company_import_records', 1);

        $this->assertDatabaseHas('sales_intel_failed_company_import_records', [
            'filter' => 'state',
            'value' => $state,
            'naics' => implode(',', $naics),
        ]);
    }

    #[Test]
    public function it_associates_the_proper_industry_service_ids_to_a_prospect_on_import()
    {
        IndustryService::all()->each->delete();

        $roofingIndustryService = IndustryService::factory()->create([
            'slug' => 'roof-replacement',
        ]);

        $foundationIndustryService = IndustryService::factory()->create([
            'slug' => 'foundation-repair',
        ]);

        $naics = [
            '238160', // Roofing
            '238110', // Foundation
        ];

        $zipcode = '12345';

        $this->companyResponse['search_results'][0]['company'] = [
            'naics_codes' => [
                '238160',
                '23816',
                '238110',
                '23811',
                '2381',
            ],
        ];

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_naics_codes' => implode(',', $naics),
                'company_location_zipcodes' => $zipcode,
                'company_location_zipcodes_distance' => 50,
                'page' => 1,
            ]) => Http::response(json_encode($this->companyResponse)),
        ]);

        app(SalesIntel::class)->importCompanies('zipcode', $zipcode, $naics);

        $this->assertDatabaseCount('new_buyer_prospects', 1);

        $this->assertDatabaseHas('new_buyer_prospects', [
            'external_reference' => 12345,
            'user_id' => 0,
            'company_id' => 0,
            'source' => ProspectSource::SALESINTEL,
            'company_name' => 'Example Company Inc',
            'company_website' => 'examplewebsite.com',
            'company_phone' => '(*************',
            'address_street' => '1234 N Fake St',
            'address_city_key' => str('Fake City')->kebab(),
            'address_state_abbr' => 'NJ',
        ]);

        $this->assertEquals(
            [$roofingIndustryService->id, $foundationIndustryService->id],
            NewBuyerProspect::first()->industry_service_ids
        );
    }

    #[Test]
    public function it_sorts_company_user_imports_based_on_verified()
    {
        $company = Company::factory()->createQuietly([
            'website' => 'www.foobar.com',
        ]);

        $response = [
            'search_results' => collect(range('a', 'j'))
                ->map(fn ($letter, $index) => [
                    'first_name' => $letter,
                    'last_name' => $letter,
                    'work_emails' => ['<EMAIL>'],
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                    ],
                    'verified' => $index % 2,
                ]),
        ];

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => $company->website,
                'is_international' => false,
            ]) => Http::response(json_encode($response)),
        ]);

        app(SalesIntel::class)->importUsers($company);

        $this->assertDatabaseCount('company_users', 10);

        $orderedUserNames = CompanyUser::whereCompanyId($company->id)->get()->pluck('first_name')->all();

        $this->assertEquals(['b', 'd', 'f', 'h', 'j', 'a', 'c', 'e', 'g', 'i'], $orderedUserNames);
    }

    #[Test]
    public function it_sorts_prospect_contact_imports_based_on_verified()
    {
        $prospect = NewBuyerProspect::factory()->create([
            'company_website' => 'https://www.foobar.com',
        ]);

        $response = [
            'search_results' => collect(range('a', 'j'))
                ->map(fn ($letter, $index) => [
                    'first_name' => $letter,
                    'last_name' => $letter,
                    'work_emails' => ['<EMAIL>'],
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                    ],
                    'verified' => $index % 2,
                ]),
        ];

        Http::fake([
            url()->query($this->peopleUrl, [
                'company_domains' => 'foobar.com',
                'is_international' => false,
            ]) => Http::response(json_encode($response)),
        ]);

        app(SalesIntel::class)->importContacts($prospect);

        $this->assertDatabaseCount('prospect_contacts', 5);

        $orderedContactNames = Contact::whereProspectId($prospect->id)->get()->pluck('first_name')->all();

        $this->assertEquals(['b', 'd', 'f', 'h', 'j'], $orderedContactNames);
    }

    #[Test]
    public function it_takes_naics_into_account_when_determining_failed_record_exceptions()
    {
        $naics = ['111'];
        $state = 'CA';

        FailedCompanyImportRecord::factory()->stateFilter($state)->create([
            'naics' => implode(',', $naics),
        ]);

        Http::fake([
            url()->query($this->companyUrl, [
                'is_international' => false,
                'company_naics_codes' => '222',
                'company_location_states' => $state,
                'page' => 1,
            ]) => Http::response(),
        ]);

        app(SalesIntel::class)->importCompanies('state', $state, ['222']);

        $this->expectNotToPerformAssertions();
    }

    public static function invalidUserProvider()
    {
        return [
            'missing users' => [
                [],
            ],
            'missing first name' => [
                [
                    'first_name' => '',
                    'last_name' => 'Doe',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
            'missing last name' => [
                [
                    'first_name' => 'John',
                    'last_name' => '',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
            'missing email' => [
                [
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'work_emails' => [],
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
            'missing phone numbers' => [
                [
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'phone_numbers' => [],
                ],
            ],
        ];
    }

    public static function validUserProvider()
    {
        return [
            'missing job department' => [
                [
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'job_department' => '',
                    'job_title' => 'Software Developer',
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
            'missing job title' => [
                [
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'job_department' => 'IT',
                    'job_title' => '',
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
            'missing mobile phone' => [
                [
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'job_department' => 'IT',
                    'job_title' => 'Software Developer',
                    'phone_numbers' => [
                        [
                            'type' => 'work_hq',
                            'country_code' => '+1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
            'missing work phone' => [
                [
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'work_emails' => [
                        '<EMAIL>',
                    ],
                    'job_department' => 'IT',
                    'job_title' => 'Software Developer',
                    'phone_numbers' => [
                        [
                            'type' => 'mobile',
                            'country_code' => '1',
                            'value' => '(*************',
                        ],
                    ],
                ],
            ],
        ];
    }

    public static function expectedExceptionCodes()
    {
        return [[401], [402], [403], [500]];
    }
}
