<?php

namespace Tests\Feature\Reviews;

use App\Jobs\CalculateCompanyRatingsJob;
use App\Models\Odin\Company;
use App\Repositories\CompanyConsumerReviewRepository\CompanyConsumerReviewRepository;
use App\Repositories\CompanyRatingRepository;
use App\Services\CompanyConsumerReviews\CompanyConsumerReviewService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Mockery;
use Mockery\MockInterface;
use Tests\TestCase;

class CalculateCompanyRatingsJobTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * Testing the job with a single company
     *
     * @throws BindingResolutionException
     */
    public function test_single_company(): void
    {
        // Company to be passed to rating calculation job
        $companyCollect = $this->makeFakeCompanies(1);
        $company = $companyCollect->first();

        // Collection of reviews, doesn't need to be actual review objects
        $reviewCollection = new Collection([1]);

        // Create database query to be returned by review repository
        $builderMock = Mockery::mock(Builder::class);
        $builderMock->shouldReceive('get')->andReturn($reviewCollection);

        // Rating to be returned by avg calculation
        $rating = 4.5;

        // Create mock for CompanyConsumerReviewRepository
        $this->instance(
            CompanyConsumerReviewRepository::class,
            Mockery::mock(CompanyConsumerReviewRepository::class, function (MockInterface $mock) use ($builderMock) {
                $mock->shouldReceive('getApprovedConsumerReviews')->once()->andReturn($builderMock);
            })
        );

        // Create mock for CompanyConsumerReviewService
        $this->instance(
            CompanyConsumerReviewService::class,
            Mockery::mock(CompanyConsumerReviewService::class, function (MockInterface $mock) use ($rating) {
                $mock->shouldReceive('calculateBayesianAvgCompanyRating')->once()->andReturn($rating);
            })
        );

        // Create mock for CompanyConsumerRatingRepository
        $this->instance(
            CompanyRatingRepository::class,
            Mockery::mock(CompanyRatingRepository::class, function (MockInterface $mock) use ($rating, $company) {
                $mock->shouldReceive('createCompanyRating')->withArgs(function ($companyIdParam, $ratingParam) use ($rating, $company) {
                    return
                        $companyIdParam === $company[Company::FIELD_ID] &&
                        $ratingParam === $rating;
                })->once();
            })
        );

        // Create job instance with company parameter for constructor
        $job = app()->make(CalculateCompanyRatingsJob::class, ['companyIds' => $companyCollect->pluck(Company::FIELD_ID)->toArray()]);

        // Run handle with injected dependencies, tested by expected function calls in mocks
        $this->assertEquals(null, $job->handle(
            app()->make(CompanyConsumerReviewRepository::class),
            app()->make(CompanyConsumerReviewService::class),
            app()->make(CompanyRatingRepository::class),
        ));
    }

    /**
     * Testing the job with multiple companies
     *
     * @throws BindingResolutionException
     */
    public function test_multiple_companies(): void
    {
        // Set the number of companies
        $number_of_companies = 10;

        // Company to be passed to rating calculation job
        $companyCollect = $this->makeFakeCompanies($number_of_companies);

        // Collection of reviews, doesn't need to be actual review objects
        $reviewCollection = new Collection([1]);

        // Create database query to be returned by review repository
        $builderMock = Mockery::mock(Builder::class);
        $builderMock->shouldReceive('get')->andReturn($reviewCollection);

        // Rating to be returned by avg calculation
        $rating = 4.5;

        // Create mock for CompanyConsumerReviewRepository
        $this->instance(
            CompanyConsumerReviewRepository::class,
            Mockery::mock(CompanyConsumerReviewRepository::class, function (MockInterface $mock) use ($companyCollect, $builderMock) {
                $mock->shouldReceive('getApprovedConsumerReviews')->times($companyCollect->count())->andReturn($builderMock);
            })
        );

        // Create mock for CompanyConsumerReviewService
        $this->instance(
            CompanyConsumerReviewService::class,
            Mockery::mock(CompanyConsumerReviewService::class, function (MockInterface $mock) use ($companyCollect, $rating) {
                $mock->shouldReceive('calculateBayesianAvgCompanyRating')->times($companyCollect->count())->andReturn($rating);
            })
        );

        // Create mock for CompanyConsumerRatingRepository
        $this->instance(
            CompanyRatingRepository::class,
            Mockery::mock(CompanyRatingRepository::class, function (MockInterface $mock) use ($rating, $companyCollect) {
                $mock->shouldReceive('createCompanyRating')->withArgs(function ($companyIdParam, $ratingParam) use ($rating, $companyCollect) {
                    return
                        $companyCollect->contains([Company::FIELD_ID], $companyIdParam) &&
                        $ratingParam === $rating;
                })->times($companyCollect->count());
            })
        );

        // Create job instance with company parameter for constructor
        $job = app()->make(CalculateCompanyRatingsJob::class, ['companyIds' => $companyCollect->pluck(Company::FIELD_ID)->toArray()]);

        // Run handle with injected dependencies, tested by expected function calls in mocks
        $this->assertEquals(null, $job->handle(
            app()->make(CompanyConsumerReviewRepository::class),
            app()->make(CompanyConsumerReviewService::class),
            app()->make(CompanyRatingRepository::class),
        ));
    }

    protected function makeFakeCompanies(int $count): Collection
    {
        $companies = new Collection([]);
        for ($i = 1; $i <= $count; $i++) {
            $company = new Company;
            $company[Company::FIELD_ID] = $i;
            $companies->push($company);
        }

        return $companies;
    }

    protected function tearDown(): void
    {
        parent::tearDown();

        Mockery::close();
    }
}
