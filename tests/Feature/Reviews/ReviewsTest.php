<?php

namespace Tests\Feature\Reviews;

use App\Enums\CompanyConsumerReviewStatus;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\PermissionType;
use App\Enums\RoleType;
use App\Jobs\AssociateLeadWithReviewJob;
use App\Jobs\CalculateCompanyRatingsJob;
use App\Jobs\Workflows\RunWorkflowPipeline;
use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\Reviewer;
use App\Models\Odin\Company;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Website;
use App\Models\User;
use App\Models\Workflow;
use App\Models\WorkflowEvent;
use App\Services\Twilio\TwilioVerificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Queue;
use Illuminate\Testing\Fluent\AssertableJson;
use Mockery;
use Mockery\MockInterface;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ReviewsTest extends TestCase
{
    use RefreshDatabase;

    const string BASE_PUBLIC_ROUTE = '/api/v2/reviews';

    const string BASE_INTERNAL_ROUTE = 'internal-api/v1/reviews';

    const string FIXR_ORIGIN = 'fixr';

    const string TEST_PHONE = '******-765-4321';

    protected function setUp(): void
    {
        parent::setUp();

        if (collect(
            [
                config('services.google.pubsub.service_account'),
                config('services.google.pubsub.project_id'),
                config('services.google.pubsub.topic'),
            ]
        )->some(fn ($item) => empty($item))) {
            $this->markTestSkipped('Skipping due to missing necessary Google pubsub credentials in config');
        }

        Permission::findOrCreate(PermissionType::ADMIN->value);
        Permission::findOrCreate(PermissionType::CONSUMER_REVIEWS_MANAGE->value);
        $role = Role::findOrCreate(RoleType::ADMIN->value);
        $role->givePermissionTo(PermissionType::ADMIN->value);
        $role->givePermissionTo(PermissionType::CONSUMER_REVIEWS_MANAGE->value);

        $user = User::factory()->create()->assignRole($role);

        $this->actingAs($user);

        Website::factory()->create([Website::FIELD_ABBREVIATION => self::FIXR_ORIGIN]);

        $solar = Industry::query()
            ->firstOrCreate([
                Industry::FIELD_NAME => 'Solar',
                Industry::FIELD_SLUG => 'solar',
            ]);
        $solar
            ->services()
            ->firstOrCreate([
                IndustryService::FIELD_NAME => 'Solar Installation',
                IndustryService::FIELD_SLUG => 'solar-installation',
            ]);

        WorkflowEvent::factory()
            ->has(Workflow::factory()->state([Workflow::FIELD_ENTRY_ACTION_ID => 1]), WorkflowEvent::RELATION_WORKFLOWS)
            ->create([
                WorkflowEvent::FIELD_EVENT_NAME => EventName::CREATED->value,
                WorkflowEvent::FIELD_EVENT_CATEGORY => EventCategory::REVIEWS->value,
            ]);
    }

    /**
     * Test creating a review with the route: post /public-api/reviews
     */
    public function test_create_review(): void
    {
        // Setup variables for review
        $name = 'Layne Sauer';
        $contactMethod = 'email';
        $email = '<EMAIL>';
        $rawPhone = '************';
        $phone = preg_replace("/\D/", '', $rawPhone);
        $comments = 'test review comments 123456';
        $overallScore = '4.5';

        $company = $this->createCompany();

        // Create review route
        $route = self::BASE_PUBLIC_ROUTE.'/create-review';

        // Review Data
        $data = [
            'reviewer_name' => $name,
            'reviewer_contact_method' => $contactMethod,
            'reviewer_email' => $email,
            'reviewer_phone' => $rawPhone,
            'review_comments' => $comments,
            'review_overall_score' => $overallScore,
            'company_reference' => $company->{Company::FIELD_REFERENCE},
            'registration_origin' => self::FIXR_ORIGIN,
            'reviewer_ip' => '127.0.0.1',
            'zip_code' => '20001',
        ];

        // Create review
        $response = $this->post($route, $data);

        // Confirm response successful and contains expected data
        $response->assertStatus(200);
        $response->assertJsonPath('data.status', true);
        $response->assertJson(fn (AssertableJson $json) => $json
            ->has('data.review')
            ->has('data.review.uuid')
            ->has('data.review.reviewer_id')
            ->has('data.review.company_id')
            ->has('data.review.review_data_id')
            ->has('data.review.updated_at')
            ->has('data.review.created_at')
            ->has('data.review.id')
        );

        // Contain database contains new review created
        $this->assertDatabaseCount('reviews', 1);
        $this->assertDatabaseCount('reviewers', 1);
        $this->assertDatabaseCount('review_data', 1);

        // Check values
        $this->assertDatabaseHas('reviews', [
            'company_id' => $company->{Company::FIELD_ID},
            'uuid' => $response->getData()->{'data'}->{'review'}->{'uuid'},
            'status' => CompanyConsumerReviewStatus::PENDING_APPROVAL,
        ]);
        $this->assertDatabaseHas('reviewers', [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'is_email_verified' => false,
            'is_phone_verified' => false,
        ]);
        $this->assertDatabaseHas('review_data', [
            'overall_score' => $overallScore,
            'comments' => $comments,
        ]);

        // Confirm that lead association with review job is dispatched on review creation (on reviews queue)
        Queue::assertPushedOn('reviews', AssociateLeadWithReviewJob::class);
    }

    /**
     * Test the route: get consumer-reviews/{review uuid}
     */
    public function test_get_review(): void
    {
        // Setup variables for review
        $name = 'Test Person';
        $contactMethod = 'email';
        $email = '<EMAIL>';
        $phone = self::TEST_PHONE;
        $comments = 'test review comments!';
        $overallScore = '3';

        $company = $this->createCompany();

        // Create review route
        $route = self::BASE_PUBLIC_ROUTE.'/create-review';

        // Review Data
        $data = [
            'reviewer_name' => $name,
            'reviewer_contact_method' => $contactMethod,
            'reviewer_email' => $email,
            'reviewer_phone' => $phone,
            'review_comments' => $comments,
            'review_overall_score' => $overallScore,
            'company_reference' => $company->{Company::FIELD_REFERENCE},
            'registration_origin' => self::FIXR_ORIGIN,
            'zip_code' => '20001',
        ];

        // Create review
        $createResponse = $this->post($route, $data);

        // Confirm successful creation
        $createResponse->assertStatus(200);

        $reviewUuid = $createResponse->getData()->{'data'}->{'review'}->{'uuid'};

        // Build route to retrieve review data
        $getRoute = self::BASE_PUBLIC_ROUTE."/$reviewUuid";
        // Retrieve review
        $getResponse = $this->get($getRoute);

        // Check exact response data
        $getResponse->assertStatus(200);
        $getResponse->assertJsonPath('data.status', true);
        $getResponse->assertJsonPath('data.review.uuid', $reviewUuid);
        $getResponse->assertJsonPath('data.review.company_id', $company[Company::FIELD_ID]);
        $getResponse->assertJsonPath('data.review.status', (int) CompanyConsumerReviewStatus::PENDING_APPROVAL->value);
        $getResponse->assertJsonPath('data.review.is_verified', 0);

        // Check response keys exist
        $getResponse->assertJson(fn (AssertableJson $json) => $json
            ->has('data.review')
            ->has('data.review.uuid')
            ->has('data.review.reviewer_id')
            ->has('data.review.company_id')
            ->has('data.review.review_data_id')
            ->has('data.review.updated_at')
            ->has('data.review.created_at')
            ->has('data.review.id')
        );
    }

    /**
     * Test the route: post consumer-reviews/send-phone-verification
     */
    public function test_send_phone_verification(): void
    {
        // Setup variables for review
        $name = 'Test Person Verify';
        $contactMethod = 'phone';
        $email = '<EMAIL>';
        $phone = $this->cleanTestPhone();
        $comments = 'test review comments!';
        $overallScore = '2.5';

        // Create company for review
        $company = $this->createCompany();
        $review = $this->createReview($company, $name, $contactMethod, $email, self::TEST_PHONE, $comments, $overallScore);

        // Confirm reviewer creation and not verified
        $this->assertDatabaseHas('reviewers', [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'is_email_verified' => false,
            'is_phone_verified' => false,
        ]);

        // Mock TwilioVerificationService for phone verify
        $this->instance(
            TwilioVerificationService::class,
            Mockery::mock(TwilioVerificationService::class, function (MockInterface $mock) use ($phone) {
                $mock->shouldReceive('setPhoneNumber')->with($phone)->once()->andReturn(
                    Mockery::mock(TwilioVerificationService::class, function (MockInterface $mock) {
                        $mock->shouldReceive('sendCode')->once()->andReturn(true);
                    }));
            })
        );

        // Data for phone verification
        $data = [
            'company_reference' => $company[Company::FIELD_REFERENCE],
            'phone' => $phone,
        ];

        // Create route to post
        $verifyRoute = self::BASE_PUBLIC_ROUTE.'/'.$review->uuid.'/send-phone-verification';

        // Send phone verification
        $verifyResponse = $this->post($verifyRoute, $data);

        // Verify post was successful and response keys exist
        $verifyResponse->assertStatus(200);
        $verifyResponse->assertJsonPath('data.status', true);
        $verifyResponse->assertJson(fn (AssertableJson $json) => $json->has('data.user_reference')
        );
    }

    /**
     * Test the route: post consumer-reviews/verify-phone
     */
    public function test_verify_phone(): void
    {
        // Setup variables for review
        $name = 'Test Person Verify';
        $contactMethod = 'phone';
        $email = '<EMAIL>';
        $phone = $this->cleanTestPhone();
        $comments = 'test review comments!';
        $overallScore = '2.5';

        // This will be sent in post request, mocked twilio service will confirm it is received as the comparison argument
        $verificationCode = '1234';

        // Create company for review
        $company = $this->createCompany();
        $review = $this->createReview($company, $name, $contactMethod, $email, self::TEST_PHONE, $comments, $overallScore);

        // Confirm reviewer creation and not verified
        $this->assertDatabaseHas('reviewers', [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'is_email_verified' => false,
            'is_phone_verified' => false,
        ]);

        // Mock TwilioVerificationService for phone verify
        $this->instance(
            TwilioVerificationService::class,
            Mockery::mock(TwilioVerificationService::class, function (MockInterface $mock) use ($phone) {
                $mock->shouldReceive('setPhoneNumber')->with($phone)->once()->andReturn(
                    Mockery::mock(TwilioVerificationService::class, function (MockInterface $mock) {
                        $mock->shouldReceive('sendCode')->once()->andReturn(true);
                    }));
                $mock->shouldReceive('setPhoneNumber')->with($phone)->once()->andReturn(
                    Mockery::mock(TwilioVerificationService::class, function (MockInterface $mock) {
                        $mock->shouldReceive('verifyCode')->once()->andReturn(true);
                    }));
            })
        );

        // Data for phone verification
        $data = [
            'company_reference' => $company[Company::FIELD_REFERENCE],
            'phone' => $phone,
        ];

        // Route for sending the verification text
        $verifyRoute = self::BASE_PUBLIC_ROUTE.'/'.$review->uuid.'/send-phone-verification';

        // Send verification text
        $verifyResponse = $this->post($verifyRoute, $data);

        // Verify post was successful and response keys exist
        $verifyResponse->assertStatus(200);
        $verifyResponse->assertJsonPath('data.status', true);
        $verifyResponse->assertJson(fn (AssertableJson $json) => $json->has('data.user_reference')
        );

        // Data for sending verification code
        $data = [
            'verification_code' => $verificationCode,
            'user_reference' => $verifyResponse->getData()->{'data'}->{'user_reference'},
            'company_reference' => $company[Company::FIELD_REFERENCE],
        ];

        // Route for posting verification code back to server
        $verifyRoute = self::BASE_PUBLIC_ROUTE.'/'.$review->uuid.'/verify-phone';

        // Send verification code to admin2
        $verifyCodeResponse = $this->post($verifyRoute, $data);

        // Confirm successful verification
        $verifyCodeResponse->assertStatus(200);

        // Confirm database is updated for review verified
        $this->assertDatabaseHas('reviews', [
            'company_id' => $company->{Company::FIELD_ID},
            'uuid' => $review->{Review::FIELD_UUID},
            'status' => CompanyConsumerReviewStatus::PENDING_APPROVAL,
            'is_verified' => true,
        ]);
        $this->assertDatabaseHas('reviewers', [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'is_email_verified' => false,
            'is_phone_verified' => true,
        ]);

        // Confirm workflow event is fired for verified review creation
        Queue::assertPushed(RunWorkflowPipeline::class);
    }

    /**
     * Test the route: post consumer-reviews/send-email-verification
     */
    public function test_send_email_verification(): void
    {
        // Setup variables for review
        $name = 'Test Person Verify';
        $contactMethod = 'email';
        $email = '<EMAIL>';
        $phone = $this->cleanTestPhone();
        $comments = 'test review comments!';
        $overallScore = '5';

        // Fake mail notification to prevent verify email from being sent
        Notification::fake();

        // Create company for review
        $company = $this->createCompany();
        $review = $this->createReview($company, $name, $contactMethod, $email, self::TEST_PHONE, $comments, $overallScore);

        // Confirm reviewer creation and not verified
        $this->assertDatabaseHas('reviewers', [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'is_email_verified' => false,
            'is_phone_verified' => false,
        ]);

        // Data for phone verification
        $data = [
            'email' => $email,
        ];

        // Create route to post
        $verifyRoute = self::BASE_PUBLIC_ROUTE.'/'.$review->uuid.'/send-email-verification';

        // Send phone verification
        $verifyResponse = $this->post($verifyRoute, $data);

        // Verify post was successful and response keys exist
        $verifyResponse->assertStatus(200);
        $verifyResponse->assertJsonPath('data.status', true);

        // Confirm email notification was sent
        Notification::assertCount(1);
    }

    /**
     * Test the route: post consumer-reviews/verify-email
     */
    public function test_verify_email(): void
    {
        // Setup variables for review
        $name = 'Test Person Verify';
        $contactMethod = 'email';
        $email = '<EMAIL>';
        $phone = $this->cleanTestPhone();
        $comments = 'test review comments!';
        $overallScore = '5';

        // Fake mail notification to prevent verify email from being sent
        Notification::fake();

        // Create company for review
        $company = $this->createCompany();
        $review = $this->createReview($company, $name, $contactMethod, $email, self::TEST_PHONE, $comments, $overallScore);

        // Confirm reviewer creation and not verified
        $this->assertDatabaseHas('reviewers', [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'is_email_verified' => false,
            'is_phone_verified' => false,
        ]);

        // Data for phone verification
        $data = [
            'email' => $email,
        ];

        // Create route to post
        $verifyRoute = self::BASE_PUBLIC_ROUTE.'/'.$review->uuid.'/send-email-verification';

        // Send phone verification
        $verifyResponse = $this->post($verifyRoute, $data);

        // Verify post was successful and response keys exist
        $verifyResponse->assertStatus(200);
        $verifyResponse->assertJsonPath('data.status', true);

        // Confirm email notification was sent
        Notification::assertCount(1);

        // Get reviewer instance for reference and email code
        $reviewer = DB::table('reviewers')->get()->first();

        // Data for sending verification code
        $data = [
            'user_reference' => $reviewer->{Reviewer::FIELD_REFERENCE},
            'token' => $reviewer->{Reviewer::FIELD_EMAIL_VERIFICATION_TOKEN},
        ];

        // Route for posting verification code back to server
        $verifyRoute = self::BASE_PUBLIC_ROUTE.'/'.$review->uuid.'/verify-email';

        // Send verification code to admin2
        $verifyCodeResponse = $this->post($verifyRoute, $data);

        // Confirm successful verification
        $verifyCodeResponse->assertStatus(200);

        // Confirm database is updated for review verified
        $this->assertDatabaseHas('reviews', [
            'company_id' => $company->{Company::FIELD_ID},
            'uuid' => $review->{Review::FIELD_UUID},
            'status' => CompanyConsumerReviewStatus::PENDING_APPROVAL,
            'is_verified' => true,
        ]);
        $this->assertDatabaseHas('reviewers', [
            'name' => $name,
            'email' => $email,
            'phone' => $phone,
            'is_email_verified' => true,
            'is_phone_verified' => false,
        ]);

        // Confirm workflow event is fired for verified review creation
        Queue::assertPushed(RunWorkflowPipeline::class);
    }

    /**
     * Test the route: get internal-api/v1/companies/{companyId}/consumer-reviews
     */
    public function test_get_reviews(): void
    {
        // Create company for review
        $company = $this->createCompany();
        $review = $this->createReview($company);
        $reviewer = $review->reviewer()->get()->first();
        $reviewData = $review->reviewData()->get()->first();

        // Get created review
        $route = self::BASE_INTERNAL_ROUTE;
        $getResponse = $this->get($route);

        // Confirm successful request
        $getResponse->assertStatus(200);

        // Verify json contains review information
        $getResponse
            ->assertJson(fn (AssertableJson $json) => $json
                ->has('data.company_consumer_reviews.data', 1, fn (AssertableJson $json) => $json
                    ->where('reference', $review->uuid)
                    ->where('reviewer_name', $reviewer->{Reviewer::FIELD_NAME})
                    ->where('status', CompanyConsumerReviewStatus::PENDING_APPROVAL->value)
                    ->where('display_status', 'pending')
                    ->where('review_is_verified', 0)
                    ->etc()
                )
            );
    }

    /**
     * Test the route: post internal-api/v1/companies/{companyId}/consumer-reviews/approve
     */
    public function test_approve_review(): void
    {
        // Create company and review
        $company = $this->createCompany();
        $review = $this->createReview($company);

        // Create route for approve
        $route = self::BASE_INTERNAL_ROUTE."/$review->uuid/approve";

        // Approve review
        $response = $this->post($route);

        // Confirm successful approval
        $response->assertStatus(200);
        $response->assertJsonPath('data.status', true);

        // Confirm database reflects approval
        $this->assertDatabaseHas('reviews', [
            'company_id' => $company->{Company::FIELD_ID},
            'uuid' => $review->{Review::FIELD_UUID},
            'status' => CompanyConsumerReviewStatus::APPROVED,
        ]);

        // Assert calculation review job for company overall rating was dispatched
        Queue::assertPushed(CalculateCompanyRatingsJob::class);
    }

    /**
     * Test the route: post internal-api/v1/companies/{companyId}/consumer-reviews/decline
     */
    public function test_decline_review(): void
    {
        // Create company and review
        $company = $this->createCompany();
        $review = $this->createReview($company);

        // Create route for decline
        $route = self::BASE_INTERNAL_ROUTE."/$review->uuid/decline";

        // Decline review
        $response = $this->post($route);

        // Confirm successful decline
        $response->assertStatus(200);
        $response->assertJsonPath('data.status', true);

        // Confirm database reflects decline
        $this->assertDatabaseHas('reviews', [
            'company_id' => $company->{Company::FIELD_ID},
            'uuid' => $review->{Review::FIELD_UUID},
            'status' => CompanyConsumerReviewStatus::DECLINED,
        ]);

        // Confirm that overall company calculation job was not dispatched
        Queue::assertNotPushed(CalculateCompanyRatingsJob::class);
    }

    /**
     * Test the route: post internal-api/v1/companies/{companyId}/consumer-reviews/{reviewId}/reply
     */
    public function test_reply_review(): void
    {
        // Create company and review
        $company = $this->createCompany();
        $review = $this->createReview($company);

        $reviewReply = 'This is a test review reply';

        // Create route for decline
        $route = self::BASE_INTERNAL_ROUTE."/$review->uuid/reply";

        // Payload for reply request
        $data = [
            'reply' => $reviewReply,
        ];

        // Approve review
        $response = $this->post($route, $data);

        // Confirm successful approval
        $response->assertStatus(200);
        $response->assertJsonPath('data.status', true);

        $this->assertDatabaseHas('review_replies', [
            'comments' => $reviewReply,
        ]);
    }

    public function createReview(
        Company $company,
        string $name = 'test name',
        string $contactMethod = 'email',
        string $email = '<EMAIL>',
        string $phone = '************',
        string $comments = 'test review comments',
        string $overallScore = '4.5',
    ): Review {
        // Fake queue to prevent job dispatch on review creation
        Queue::fake();

        // Review Data
        $data = [
            'reviewer_name' => $name,
            'reviewer_contact_method' => $contactMethod,
            'reviewer_email' => $email,
            'reviewer_phone' => $phone,
            'review_comments' => $comments,
            'review_overall_score' => $overallScore,
            'company_reference' => $company->{Company::FIELD_REFERENCE},
            'registration_origin' => self::FIXR_ORIGIN,
            'reviewer_ip' => '127.0.0.1',
            'zip_code' => '20001',
        ];

        // Create review
        $route = self::BASE_PUBLIC_ROUTE.'/create-review';
        $createResponse = $this->post($route, $data);
        $createResponse->assertStatus(200);

        /** @var Review */
        return Review::query()
            ->where(Review::FIELD_UUID, $createResponse->getData()->{'data'}->{'review'}->{'uuid'})
            ->first();
    }

    public function createCompany(string $reference = 'test-company-reference'): Company
    {
        // Fake queue to prevent job dispatch on company creation
        Queue::fake();

        IndustryService::factory()->create();

        $company = Company::factory()->create();
        $company[Company::FIELD_REFERENCE] = 'test-company-reference';
        $company->save();

        return $company;
    }

    private function cleanTestPhone(): string
    {
        return preg_replace("/\D/", '', self::TEST_PHONE);
    }
}
