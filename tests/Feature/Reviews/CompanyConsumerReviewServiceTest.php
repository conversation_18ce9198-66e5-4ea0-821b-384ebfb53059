<?php

namespace Tests\Feature\Reviews;

use App\Models\ConsumerReviews\Review;
use App\Models\ConsumerReviews\ReviewData;
use App\Services\CompanyConsumerReviews\CompanyConsumerReviewService;
use Carbon\Carbon;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Support\Collection;
use Tests\TestCase;

class CompanyConsumerReviewServiceTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * One 5 star review
     *
     * @throws BindingResolutionException
     */
    public function test_bayesian_average_one_review(): void
    {
        $service = app()->make(CompanyConsumerReviewService::class);

        $reviewValues = [5.0];
        $reviews = $this->makeReviews($reviewValues, now());

        $this->assertEquals(3.75, $service->calculateBayesianAvgCompanyRating($reviews));
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_bayesian_average_multiple_reviews(): void
    {
        $service = app()->make(CompanyConsumerReviewService::class);

        $reviewValues = [5.0, 4.5, 3.76, 5, 4.9];
        $reviews = $this->makeReviews($reviewValues, now());

        $this->assertEquals(4.07, $service->calculateBayesianAvgCompanyRating($reviews));
    }

    /**
     * No reviews
     *
     * @throws BindingResolutionException
     */
    public function test_bayesian_average_no_reviews(): void
    {
        $service = app()->make(CompanyConsumerReviewService::class);

        $reviewValues = [];
        $reviews = $this->makeReviews($reviewValues, now());

        $this->assertEquals(3.5, $service->calculateBayesianAvgCompanyRating($reviews));
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_bayesian_average_old_review(): void
    {
        $service = app()->make(CompanyConsumerReviewService::class);

        $reviewValues = [5.0];
        $reviews = $this->makeReviews($reviewValues, now()->subYears(1)->subDays(1));

        $this->assertEquals(3.67, $service->calculateBayesianAvgCompanyRating($reviews));
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_bayesian_average_really_old_review(): void
    {
        $service = app()->make(CompanyConsumerReviewService::class);

        $reviewValues = [5.0];
        $reviews = $this->makeReviews($reviewValues, now()->subYears(2)->subDays(1));

        $this->assertEquals(3.59, $service->calculateBayesianAvgCompanyRating($reviews));
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_bayesian_average_combo_reviews(): void
    {
        $service = app()->make(CompanyConsumerReviewService::class);

        $reviewValues = [5.0];
        $reviews = $this->makeReviews($reviewValues, now()->subYears(2)->subDays(1));
        $reviews = $reviews->merge($this->makeReviews($reviewValues, now()));

        $this->assertEquals(3.82, $service->calculateBayesianAvgCompanyRating($reviews));
    }

    /**
     * @throws BindingResolutionException
     */
    public function test_bayesian_average_combo_many_reviews(): void
    {
        $service = app()->make(CompanyConsumerReviewService::class);

        $newReviewValues = [5.0, 3.0, 1.0, 3.4, 4.4, 3.9, 1.5];
        $mediumValues = [3.3, 4.1, 2.3, 4.7, 5.0];
        $oldValues = [1.0, 2.0, 3.1, 2.3, 5];
        $reviews = $this->makeReviews($newReviewValues, now());
        $reviews = $reviews->merge($this->makeReviews($mediumValues, now()->subYears(1)->subDays(1)));
        $reviews = $reviews->merge($this->makeReviews($oldValues, now()->subYears(2)->subDays(1)));

        $this->assertEquals(3.36, $service->calculateBayesianAvgCompanyRating($reviews));
    }

    protected function makeReviews(array $values, Carbon $date): Collection
    {
        $reviews = collect([]);
        foreach ($values as $value) {
            $review = new Review;
            $review[Review::FIELD_CREATED_AT] = $date;

            $reviewData = new ReviewData;
            $reviewData[ReviewData::FIELD_OVERALL_SCORE] = $value;
            $reviewData->save();

            $review[Review::FIELD_REVIEW_DATA_ID] = $reviewData[ReviewData::FIELD_ID];
            $reviews->push($review);
        }

        return $reviews;
    }
}
