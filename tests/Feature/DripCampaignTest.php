<?php

namespace Tests\Feature;

use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Jobs\MarketingCampaign\DispatchDripCampaigns;
use App\Jobs\MarketingCampaign\ProcessDripCampaign;
use App\Models\MarketingCampaign;
use App\Services\MarketingCampaign\Sending\MarketingCampaignSendingService;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\DataProvider;
use Tests\TestCase;

class DripCampaignTest extends TestCase
{
    use RefreshDatabase;

    private MarketingCampaignSendingService $service;

    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub
        $this->service = app(MarketingCampaignSendingService::class);
    }

    /**
     * A basic feature test example.
     */
    public function test_dispatch_drip_campaigns_work(): void
    {
        MarketingCampaign::factory()->create([
            MarketingCampaign::FIELD_TYPE => MarketingCampaignType::DRIP_EMAIL,
            MarketingCampaign::FIELD_STATUS => MarketingCampaignStatus::ACTIVE,
            MarketingCampaign::FIELD_SENT_AT => now(),
            MarketingCampaign::FIELD_CONFIGURATION => [
                'filters' => [
                    'consumer-location' => [
                        'consumer-county' => [],
                        'consumer-location' => [],
                        'consumer-zip-code' => [],
                    ],
                ],
                'send_time' => [
                    'hours' => 9,
                    'minutes' => 20,
                    'seconds' => 0,
                ],
                'span_type' => 'year',
                'span_value' => 7,
                'sender_local' => 'darcy.secondary',
                'sender_domain' => 'gmail.com',
                'email_template_id' => '11',
            ],
        ]);

        Queue::fake([ProcessDripCampaign::class]);

        DispatchDripCampaigns::dispatchSync();

        Queue::assertPushed(ProcessDripCampaign::class);
    }

    #[DataProvider('missedCampaigns')]
    public function test_doesnt_dispatch_drip_campaigns(
        MarketingCampaignStatus $campaignStatus, MarketingCampaignType $campaignType, Carbon $sentAt,
    ) {
        MarketingCampaign::factory()->create([
            MarketingCampaign::FIELD_TYPE => $campaignType,
            MarketingCampaign::FIELD_STATUS => $campaignStatus,
            MarketingCampaign::FIELD_SENT_AT => $sentAt,
            MarketingCampaign::FIELD_CONFIGURATION => [
                'filters' => [
                    'consumer-location' => [
                        'consumer-county' => [],
                        'consumer-location' => [],
                        'consumer-zip-code' => [],
                    ],
                ],
                'send_time' => [
                    'start' => [
                        'hours' => 9,
                        'minutes' => 20,
                        'seconds' => 0,
                    ],
                    'end' => [
                        'hours' => 9,
                        'minutes' => 20,
                        'seconds' => 0,
                    ],
                ],
                'span_type' => 'year',
                'span_value' => 7,
                'sender_local' => 'darcy.secondary',
                'sender_domain' => 'gmail.com',
                'email_template_id' => '11',
            ],
        ]);

        Queue::fake([ProcessDripCampaign::class]);

        DispatchDripCampaigns::dispatchSync();

        Queue::assertNothingPushed();
    }

    #[DataProvider('sendDelayProvider')]
    public function test_calculate_send_delay($sendTime, CarbonTimeZone $timezone)
    {
        Carbon::setTestNow(Carbon::create(2025, 3, 5, 0, 0, 0, $timezone));

        $campaign = MarketingCampaign::factory()->create([
            MarketingCampaign::FIELD_TYPE => MarketingCampaignType::DRIP_EMAIL,
            MarketingCampaign::FIELD_STATUS => MarketingCampaignStatus::DRAFT,
            MarketingCampaign::FIELD_SENT_AT => now(),
            MarketingCampaign::FIELD_CONFIGURATION => [
                'filters' => [
                    'consumer-location' => [
                        'consumer-county' => [],
                        'consumer-location' => [],
                        'consumer-zip-code' => [],
                    ],
                ],
                'send_time' => $sendTime,
                'span_type' => 'year',
                'span_value' => 7,
                'sender_local' => 'test.test',
                'sender_domain' => 'gmail.com',
                'email_template_id' => '11',
            ],
        ]);

        $delay = $this->service->calculateSendDelay($campaign, $timezone);

        $expectedStart = now()->setTimezone($timezone)
            ->startOfDay()
            ->addHours($sendTime['start']['hours'])
            ->addMinutes($sendTime['start']['minutes'])
            ->addSeconds($sendTime['start']['seconds']);

        $expectedEnd = now()->setTimezone($timezone)
            ->startOfDay()
            ->addHours($sendTime['end']['hours'])
            ->addMinutes($sendTime['end']['minutes'])
            ->addSeconds($sendTime['end']['seconds']);

        $this->assertTrue(
            $delay->between($expectedStart, $expectedEnd),
            "Returned time {$delay->toTimeString()} is not between {$expectedStart->toTimeString()} and {$expectedEnd->toTimeString()} in timezone {$timezone->getName()}"
        );
    }

    public static function missedCampaigns(): array
    {
        return [
            [MarketingCampaignStatus::PAUSED, MarketingCampaignType::DRIP_EMAIL, now()->subDay()], // paused
            [MarketingCampaignStatus::DRAFT, MarketingCampaignType::INTERNAL_EMAIL, now()->subDay()], // internal
            [MarketingCampaignStatus::DRAFT, MarketingCampaignType::MAILCHIMP_EMAIL, now()->subDay()], // mailchimp
            [MarketingCampaignStatus::DRAFT, MarketingCampaignType::DRIP_EMAIL, now()->addDay()], // starts tomorrow
        ];
    }

    public static function sendDelayProvider(): array
    {
        return [
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('America/New_York')],
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('America/Chicago')],
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('America/Denver')],
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('America/Phoenix')],
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('America/Los_Angeles')],
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('America/Anchorage')],
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('Pacific/Honolulu')],
            [['start' => ['hours' => 6, 'minutes' => 30, 'seconds' => 0], 'end' => ['hours' => 12, 'minutes' => 30, 'seconds' => 0]], new CarbonTimeZone('America/Puerto_Rico')],
        ];
    }
}
