<?php

namespace Tests\Feature\SalesIntel;

use App\Jobs\SalesIntel\ImportUsers;
use App\Models\Odin\Company;
use App\Services\SalesIntel\SalesIntel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ImportUsersTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_calls_the_import_user_function_on_the_sales_intel_api_with_the_supplied_company()
    {
        $company = Company::factory()->createQuietly();

        $this->mock(SalesIntel::class)
            ->shouldReceive('importUsers')
            ->with(Mockery::on(fn (Company $input) => $input->is($company)))
            ->once();

        ImportUsers::dispatch($company);
    }

    #[Test]
    public function it_does_not_call_the_api_if_the_company_already_has_user_import_records()
    {
        $company = Company::factory()->hasUserImportRecords()->createQuietly();

        $this->mock(SalesIntel::class)->shouldNotReceive('importUsers');

        ImportUsers::dispatch($company);
    }

    #[Test]
    public function it_does_not_call_the_api_if_the_company_already_has_failed_import_records()
    {
        $company = Company::factory()->hasFailedImportRecords()->createQuietly();

        $this->mock(SalesIntel::class)->shouldNotReceive('importUsers');

        ImportUsers::dispatch($company);
    }
}
