<?php

namespace Tests\Feature\SalesIntel;

use App\Jobs\SalesIntel\ImportContacts;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\SalesIntel\SalesIntel;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ImportContactsTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_calls_the_import_contact_function_on_the_sales_intel_api_with_the_supplied_prospect()
    {
        $prospect = NewBuyerProspect::factory()->createQuietly();

        $this->mock(SalesIntel::class)
            ->shouldReceive('importContacts')
            ->with(Mockery::on(fn (NewBuyerProspect $input) => $input->is($prospect)))
            ->once();

        ImportContacts::dispatch($prospect);
    }
}
