<?php

namespace Tests\Feature;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\DataModels\Campaigns\ConsumerProject;
use Carbon\Carbon;
use Tests\TestCase;

use function PHPUnit\Framework\assertFalse;
use function PHPUnit\Framework\assertTrue;

class AttemptConsumerProjectAllocationJobTest extends TestCase
{
    public function test_job_set_to_final_for_two_previous_days(): void
    {
        // 2 Attempts, yesterday and day before
        $jobAttempts = [
            Carbon::now()->subDays(2)->timestamp,
            Carbon::now()->subDays(1)->timestamp,
        ];

        $consumerMock = $this->getMockBuilder(ConsumerProject::class)->disableOriginalConstructor()->getMock();
        $attemptConsumerProjectAllocationJob = new AttemptConsumerProjectAllocationJob($consumerMock, true, 1, $jobAttempts);
        $shouldByFinal = $attemptConsumerProjectAllocationJob->shouldBeFinalAttempt();
        assertTrue($shouldByFinal);
    }

    public function test_job_set_to_final_for_one_attempt_yesterday(): void
    {
        // 2 Attempts, today and yesterday
        $jobAttempts = [
            Carbon::now()->subDays(1)->timestamp,
            Carbon::now()->timestamp,
        ];

        $consumerMock = $this->getMockBuilder(ConsumerProject::class)->disableOriginalConstructor()->getMock();
        $attemptConsumerProjectAllocationJob = new AttemptConsumerProjectAllocationJob($consumerMock, true, 1, $jobAttempts);
        $shouldByFinal = $attemptConsumerProjectAllocationJob->shouldBeFinalAttempt();

        assertTrue($shouldByFinal);
    }

    public function test_job_not_set_to_final_for_one_attempt_today(): void
    {
        // 1 attempt today
        $jobAttempts = [
            Carbon::now()->timestamp,
        ];

        $consumerMock = $this->getMockBuilder(ConsumerProject::class)->disableOriginalConstructor()->getMock();
        $attemptConsumerProjectAllocationJob = new AttemptConsumerProjectAllocationJob($consumerMock, true, 1, $jobAttempts);
        $shouldByFinal = $attemptConsumerProjectAllocationJob->shouldBeFinalAttempt();
        assertFalse($shouldByFinal);
    }
}
