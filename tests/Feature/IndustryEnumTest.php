<?php

namespace Tests\Feature;

use App\Enums\Odin\Industry as IndustryEnum;
use Illuminate\Support\Str;
use Tests\TestCase;

class IndustryEnumTest extends TestCase
{
    /**
     * A basic feature test example.
     */
    public function test_industry_enum_slug_list_is_valid(): void
    {
        foreach (IndustryEnum::cases() as $industry) {
            $this->assertEquals(Str::slug($industry->value), IndustryEnum::from($industry->value)->getSlug());
        }
    }
}
