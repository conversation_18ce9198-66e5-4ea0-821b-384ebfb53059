<?php

namespace Tests\Database\Seeders;

use App\Models\Conference\ConferenceTranscript;
use Database\Seeders\ConferenceTranscriptForDeepgramSeeder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ConferenceTranscriptForDeepgramSeederTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_runs(): void
    {
        $this->seed(ConferenceTranscriptForDeepgramSeeder::class);

        $this->assertDatabaseCount('users', 1);

        $this->assertDatabaseCount('conference_transcripts', 1);

        $this->assertDatabaseCount('conferences', 1);

        $this->assertDatabaseCount('calendar_events', 1);

        $this->assertDatabaseCount('calendars', 1);

        $this->assertDatabaseCount('conference_participants', 2);

        $this->assertDatabaseCount('conference_transcript_entries', 4);

        $this->assertSame('<p><b><PERSON></b><br />09:00:00 - 09:05:00<br />Hey man, how are you?</p><p><b><PERSON></b><br />09:05:05 - 09:15:00<br />Good, good.</p><p><b><PERSON> <PERSON>e</b><br />09:15:05 - 09:30:00<br />Glad to hear.</p>', ConferenceTranscript::first()->text);
    }
}
