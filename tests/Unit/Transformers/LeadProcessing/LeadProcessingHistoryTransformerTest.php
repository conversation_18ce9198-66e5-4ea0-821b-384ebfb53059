<?php

namespace Tests\Unit\Transformers\LeadProcessing;

use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\Legacy\EloquentQuote;
use App\Transformers\LeadProcessing\LeadProcessingHistoryTransformer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LeadProcessingHistoryTransformerTest extends TestCase
{
    use RefreshDatabase;

    protected LeadProcessingHistory $leadProcessingHistoryEntry;

    protected function setUp(): void
    {
        parent::setUp();

    }

    #[Test]
    public function it_transforms_lead_processing_history_entry()
    {
        $this->markTestSkipped('Removed - lead processing history entries are no longer created with legacy IDs');
        /*
         * Removed - lead processing history entries are no longer created with legacy IDs
         * @todo - rewrite tests post total legacy shutdown
         */

    }

    #[Test]
    public function it_transforms_lead_processing_history_entry_with_no_related_queue_configuration()
    {
        $this->markTestSkipped('Removed - lead processing history entries are no longer created with legacy IDs');
        /*
         * Removed - lead processing history entries are no longer created with legacy IDs
         * @todo - rewrite tests post total legacy shutdown
         */
    }
}
