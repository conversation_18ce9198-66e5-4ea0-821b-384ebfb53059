<?php

namespace Tests\Unit\Models\Conference;

use App\Jobs\Deepgram\AnalyzeConferenceTranscriptWithDeepgram;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Conference\ConferenceTranscript;
use App\Models\Conference\ConferenceTranscriptEntry;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Queue;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ConferenceTranscriptTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_gets_conference(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->create();

        $this->assertInstanceOf(Conference::class, $conferenceTranscript->conference);

        $this->assertTrue(Conference::first()->is($conferenceTranscript->conference));
    }

    #[Test]
    public function it_gets_entries(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->has(ConferenceTranscriptEntry::factory()->count(3), 'entries')->create();

        $this->assertCount(3, $conferenceTranscript->entries);

        $this->assertTrue($conferenceTranscript->entries->contains(ConferenceTranscriptEntry::first()));
    }

    #[Test]
    public function it_can_analyze_with_deepgram(): void
    {
        Queue::fake();

        $conferenceTranscript = ConferenceTranscript::factory()->create();

        $conferenceTranscript->analyzeWithDeepgram();

        Queue::assertCount(1);

        Queue::assertPushed(AnalyzeConferenceTranscriptWithDeepgram::class);
    }

    #[Test]
    public function it_can_combine_entry_text_into_one_full_text(): void
    {
        $conferenceTranscript = ConferenceTranscript::factory()->has(
            ConferenceTranscriptEntry::factory()->count(2)->sequence(
                [
                    'conference_participant_id' => ConferenceParticipant::factory()->create(['name' => 'John Doe']),
                    'text' => 'This is a valid entry.',
                    'start_time' => Carbon::create(2025, 5, 1, 9, 5, 5),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 15),
                ],
                [
                    'conference_participant_id' => ConferenceParticipant::factory()->create(['name' => 'Jane Doe']),
                    'text' => 'This is another valid entry.',
                    'start_time' => Carbon::create(2025, 5, 1, 9, 15),
                    'end_time' => Carbon::create(2025, 5, 1, 9, 25),
                ],
            ),
            'entries'
        )->create();

        $this->assertSame("John Doe\n09:05:05 - 09:15:00\n\nThis is a valid entry.\n\nJane Doe\n09:15:00 - 09:25:00\n\nThis is another valid entry.", $conferenceTranscript->text);
    }
}
