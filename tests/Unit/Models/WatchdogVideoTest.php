<?php

declare(strict_types=1);

namespace Tests\Unit\Models;

use App\Actions\GetWatchdogPlaybackUrl;
use App\Models\WatchdogVideo;
use Illuminate\Http\JsonResponse;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class WatchdogVideoTest extends TestCase
{
    #[Test]
    public function it_gets_the_link_thats_saved()
    {
        $watchdogVideo = WatchdogVideo::factory()->create([
            'link' => 'https://example.com/video.mp4',
        ]);

        $this->assertEquals('https://example.com/video.mp4', $watchdogVideo->getLink());
    }

    #[Test]
    public function it_gets_the_link_from_watchdog()
    {
        $watchdogVideo = WatchdogVideo::factory()->create([
            'link' => null,
        ]);

        $watchdogPlaybackUrl = 'https://example.com/video.mp4';

        $this->mock(GetWatchdogPlaybackUrl::class, function ($mock) use ($watchdogPlaybackUrl) {
            $mock->shouldReceive('handle')->andReturn(new JsonResponse(['data' => ['url' => $watchdogPlaybackUrl]]));
        });

        $this->assertEquals($watchdogPlaybackUrl, $watchdogVideo->getLink());
    }

    #[Test]
    public function it_fails_to_get_the_link_from_watchdog()
    {
        $watchdogVideo = WatchdogVideo::factory()->create([
            'link' => null,
        ]);

        $this->mock(GetWatchdogPlaybackUrl::class, function ($mock) {
            $mock->shouldReceive('handle')->andReturn(new JsonResponse([
                [
                    'watchdog_server_json' => [],
                    'watchdog_server_body' => 'Server error',
                    'watchdog_server_status' => 500,
                ],
            ], 424));
        });

        $this->assertNull($watchdogVideo->getLink());
    }
}
