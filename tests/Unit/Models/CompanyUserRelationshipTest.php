<?php

namespace Tests\Unit\Models;

use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyUserRelationshipTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function user(): void
    {
        $user = CompanyUserRelationship::factory()->has(User::factory())->quietCompanyCreation()->create()->user;

        $this->assertInstanceOf(User::class, $user);
    }

    #[Test]
    public function company(): void
    {
        $company = CompanyUserRelationship::factory()->has(User::factory())->quietCompanyCreation()->create()->company;

        $this->assertInstanceOf(Company::class, $company);
    }

    #[Test]
    public function role(): void
    {
        $role = CompanyUserRelationship::factory()->has(User::factory())->quietCompanyCreation()->create()->role;

        $this->assertInstanceOf(Role::class, $role);
    }
}
