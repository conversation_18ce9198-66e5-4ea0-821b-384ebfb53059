<?php

namespace Tests\Unit\Models\Calendar;

use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\Demo;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class DemoTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_returns_false_when_demo_has_no_conferences(): void
    {
        $demo = Demo::factory()->create();

        $this->assertFalse($demo->has_external_participants);
    }

    #[Test]
    public function it_returns_false_when_demo_has_conferences_but_no_participants(): void
    {
        $demo = Demo::factory()->create();
        
        Conference::factory()->create([
            'calendar_event_id' => $demo->calendar_event_id,
        ]);

        $this->assertFalse($demo->has_external_participants);
    }

    #[Test]
    public function it_returns_false_when_all_participants_are_users(): void
    {
        $user1 = User::factory()->create(['name' => '<PERSON>e']);
        $user2 = User::factory()->create(['name' => '<PERSON>']);
        
        $demo = Demo::factory()->create();
        
        $conference = Conference::factory()->create([
            'calendar_event_id' => $demo->calendar_event_id,
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference->id,
            'name' => 'John Doe', // Matches user1
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference->id,
            'name' => 'Jane Smith', // Matches user2
        ]);

        $this->assertFalse($demo->has_external_participants);
    }

    #[Test]
    public function it_returns_true_when_some_participants_are_external(): void
    {
        $user = User::factory()->create(['name' => 'John Doe']);
        
        $demo = Demo::factory()->create();
        
        $conference = Conference::factory()->create([
            'calendar_event_id' => $demo->calendar_event_id,
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference->id,
            'name' => 'John Doe', // Matches user
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference->id,
            'name' => 'External Client', // Does not match any user
        ]);

        $this->assertTrue($demo->has_external_participants);
    }

    #[Test]
    public function it_returns_true_when_all_participants_are_external(): void
    {
        $demo = Demo::factory()->create();
        
        $conference = Conference::factory()->create([
            'calendar_event_id' => $demo->calendar_event_id,
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference->id,
            'name' => 'External Client 1',
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference->id,
            'name' => 'External Client 2',
        ]);

        $this->assertTrue($demo->has_external_participants);
    }

    #[Test]
    public function it_handles_multiple_conferences_correctly(): void
    {
        $user = User::factory()->create(['name' => 'John Doe']);
        
        $demo = Demo::factory()->create();
        
        // First conference with only internal participants
        $conference1 = Conference::factory()->create([
            'calendar_event_id' => $demo->calendar_event_id,
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference1->id,
            'name' => 'John Doe', // Matches user
        ]);

        // Second conference with external participant
        $conference2 = Conference::factory()->create([
            'calendar_event_id' => $demo->calendar_event_id,
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference2->id,
            'name' => 'External Client', // Does not match any user
        ]);

        $this->assertTrue($demo->has_external_participants);
    }

    #[Test]
    public function it_ignores_null_participant_names(): void
    {
        $demo = Demo::factory()->create();
        
        $conference = Conference::factory()->create([
            'calendar_event_id' => $demo->calendar_event_id,
        ]);

        ConferenceParticipant::factory()->create([
            'conference_id' => $conference->id,
            'name' => null, // Null name should be ignored
        ]);

        $this->assertFalse($demo->has_external_participants);
    }
}
