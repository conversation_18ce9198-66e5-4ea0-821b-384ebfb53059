<?php

namespace Tests\Unit\Models\Deepgram;

use App\Models\Conference\ConferenceTranscript;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecord;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecordSentiment;
use App\Models\Deepgram\ConferenceTranscriptDeepgramRecordTopic;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ConferenceTranscriptDeepgramRecordTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function conference_record_id_is_unique(): void
    {
        $conferenceTranscriptDeepgramRecord = ConferenceTranscriptDeepgramRecord::factory()->createQuietly();

        $this->expectException(QueryException::class);

        ConferenceTranscriptDeepgramRecord::factory()->create([
            'conference_transcript_id' => $conferenceTranscriptDeepgramRecord->conference_transcript_id,
        ]);
    }

    #[Test]
    public function it_gets_conference_transcript(): void
    {
        $conferenceTranscriptDeepgramRecord = ConferenceTranscriptDeepgramRecord::factory()->createQuietly();

        $this->assertInstanceOf(ConferenceTranscript::class, $conferenceTranscriptDeepgramRecord->conference_transcript);

        $this->assertTrue(ConferenceTranscript::first()->is($conferenceTranscriptDeepgramRecord->conference_transcript));
    }

    #[Test]
    public function it_gets_topics(): void
    {
        $conferenceTranscriptDeepgramRecordTopic = ConferenceTranscriptDeepgramRecordTopic::factory()->createQuietly();

        $this->assertTrue(ConferenceTranscriptDeepgramRecord::first()->topics->contains($conferenceTranscriptDeepgramRecordTopic));
    }

    #[Test]
    public function it_gets_sentiments(): void
    {
        $conferenceTranscriptDeepgramRecordTopic = ConferenceTranscriptDeepgramRecordSentiment::factory()->createQuietly();

        $this->assertTrue(ConferenceTranscriptDeepgramRecord::first()->sentiments->contains($conferenceTranscriptDeepgramRecordTopic));
    }
}
