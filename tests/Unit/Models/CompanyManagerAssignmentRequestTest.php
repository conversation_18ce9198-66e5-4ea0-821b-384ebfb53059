<?php

namespace Tests\Unit\Models;

use App\Models\CompanyManagerAssignmentRequest;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyManagerAssignmentRequestTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_gets_company(): void
    {
        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly();

        $this->assertTrue($companyManagerAssignmentRequest->company->is(Company::first()));
    }

    #[Test]
    public function it_gets_user(): void
    {
        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly();

        $this->assertTrue($companyManagerAssignmentRequest->user->is(User::first()));
    }

    #[Test]
    public function it_gets_role(): void
    {
        $role = Role::findOrCreate('account-manager');

        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->createQuietly([
            'role_id' => $role->id,
        ]);

        $this->assertTrue($companyManagerAssignmentRequest->role->is($role));
    }

    #[Test]
    public function it_gets_deciding_user(): void
    {
        $companyManagerAssignmentRequest = CompanyManagerAssignmentRequest::factory()->decided()->createQuietly();

        $this->assertInstanceOf(User::class, $companyManagerAssignmentRequest->refresh()->deciding_user);
    }
}
