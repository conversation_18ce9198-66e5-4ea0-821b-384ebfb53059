<?php

namespace Tests\Unit\Services;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\User;
use App\Services\CompanyFilteringService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyFilteringServiceTest extends TestCase
{
    use RefreshDatabase;

    protected CompanyFilteringService $companyFilteringService;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        EloquentCompany::query()->truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();

        $this->companyFilteringService = app(CompanyFilteringService::class);
    }

    #[Test]
    public function append_attributes_with_no_results(): void
    {
        $result = $this->companyFilteringService->appendAttributes(
            EloquentCompany::query(),
            [],
        );

        $this->assertInstanceOf(Collection::class, $result);

        $this->assertEmpty($result);
    }

    #[Test, DataProvider('can_update_sales_data')]
    public function it_determines_if_can_update_sales_data_for_legacy_company(callable $setup, bool $expectedResult): void
    {
        $setup($this);

        $collection = $this->companyFilteringService->appendAttributes(
            EloquentCompany::query(),
            [],
        );

        $this->assertInstanceOf(Collection::class, $collection);

        $this->assertNotEmpty($collection);

        $this->assertEquals($expectedResult, (bool) $collection->first()->toArray()['can_update_sales_data']);
    }

    public static function can_update_sales_data(): array
    {
        return [
            'false with no company user relationships' => [
                function () {
                    EloquentCompany::factory()->createQuietly();
                },
                false,
            ],
            'false with account manager and customer success manager' => [
                function (self $self) {
                    $legacyCompany = EloquentCompany::factory()->createQuietly();

                    $company = Company::factory()->createQuietly([
                        'legacy_id' => $legacyCompany->companyid,
                    ]);

                    $user = User::factory()->createQuietly();

                    $self->actingAs($user);

                    $role = Role::findOrCreate('not-account-manager');

                    $company->assign($user)->as($role->name);

                    $role = Role::findOrCreate('not-customer-success-manager');

                    $company->assign($user)->as($role->name);
                },
                false,
            ],
            'true with account manager' => [
                function (self $self) {
                    $legacyCompany = EloquentCompany::factory()->createQuietly();

                    $company = Company::factory()->createQuietly([
                        'legacy_id' => $legacyCompany->companyid,
                    ]);

                    $user = User::factory()->createQuietly();

                    $self->actingAs($user);

                    $role = Role::findOrCreate('account-manager');

                    $company->assign($user)->as($role->name);
                },
                true,
            ],
            'true with customer success manager' => [
                function (self $self) {
                    $legacyCompany = EloquentCompany::factory()->createQuietly();

                    $company = Company::factory()->createQuietly([
                        'legacy_id' => $legacyCompany->companyid,
                    ]);

                    $user = User::factory()->createQuietly();

                    $self->actingAs($user);

                    $role = Role::findOrCreate('customer-success-manager');

                    $company->assign($user)->as($role->name);
                },
                true,
            ],
            'true with account manager and customer success manager' => [
                function (self $self) {
                    $legacyCompany = EloquentCompany::factory()->createQuietly();

                    $company = Company::factory()->createQuietly([
                        'legacy_id' => $legacyCompany->companyid,
                    ]);

                    $user = User::factory()->createQuietly();

                    $self->actingAs($user);

                    $role = Role::findOrCreate('account-manager');

                    $company->assign($user)->as($role->name);

                    $role = Role::findOrCreate('customer-success-manager');

                    $company->assign($user)->as($role->name);
                },
                true,
            ],
        ];
    }
}
