<?php

namespace Tests\Unit\Services;

use App\Models\CompanyUserRelationship;
use App\Models\Odin\Company;
use App\Models\Teams\Team;
use App\Models\Teams\TeamMember;
use App\Models\User;
use App\Services\UserAuthorizationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class UserAuthorizationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected UserAuthorizationService $authService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->company = Company::factory()->createQuietly();

        $this->authService = app(UserAuthorizationService::class);
    }

    #[Test]
    public function a_current_bdm_can_edit_the_bdm_assignment_for_a_company(): void
    {
        $currentBDM = User::factory()->withRole('business-development-manager')->create();

        $this->company->assign($currentBDM)->as('business-development-manager');

        $this->actingAs($currentBDM);

        $this->assertTrue($this->authService->canEditBusinessDevelopmentManager($this->company));
    }

    #[Test]
    public function a_sales_manager_can_edit_the_bdm_assignment_for_a_company(): void
    {
        $salesManager = User::factory()->withRole('sales-manager')->create();

        $this->actingAs($salesManager);

        $this->assertTrue($this->authService->canEditBusinessDevelopmentManager($this->company));
    }

    #[Test]
    public function an_admin_can_edit_the_bdm_assignment_for_a_company(): void
    {
        $admin = User::factory()->withRole('admin')->create();

        $this->actingAs($admin);

        $this->assertTrue($this->authService->canEditBusinessDevelopmentManager($this->company));
    }

    #[Test]
    public function a_previous_bdm_cannot_edit_the_bdm_assignment_for_a_company(): void
    {
        $previousBDM = User::factory()->withRole('business-development-manager')->create();
        $currentBDM = User::factory()->withRole('business-development-manager')->create();

        $this->company->assign($previousBDM)->as('business-development-manager');
        $this->company->assign($currentBDM)->as('business-development-manager');

        $this->actingAs($previousBDM);

        $this->assertFalse($this->authService->canEditBusinessDevelopmentManager($this->company));
    }

    #[Test]
    public function an_unrelated_user_cannot_edit_the_bdm_assignment_for_a_company(): void
    {
        Role::findOrCreate('business-development-manager');

        $this->actingAs(User::factory()->create());

        $this->assertFalse($this->authService->canEditBusinessDevelopmentManager($this->company));
    }

    #[Test]
    public function it_cannot_edit_account_manager_if_unauthenticated(): void
    {
        $result = $this->authService->canEditAccountManager(Company::factory()->createQuietly());

        $this->assertFalse($result);
    }

    #[Test]
    public function it_can_edit_account_manager_if_user_has_can_reallocate_permission(): void
    {
        $permission = Permission::findOrCreate('can-reallocate-all');

        $this->actingAs(User::factory()->create()->givePermissionTo($permission));

        $result = $this->authService->canEditAccountManager(Company::factory()->createQuietly());

        $this->assertTrue($result);
    }

    #[Test]
    public function it_can_edit_account_manager_if_user_has_can_team_permission_and_user_is_company_account_manager_in_a_team(
    ): void {
        Permission::findOrCreate('can-reallocate-all');

        $permission = Permission::findOrCreate('can-reallocate-team');

        $user = User::factory()->create()->givePermissionTo($permission);

        $team = Team::factory()->create();

        TeamMember::factory()->create([
            'team_id' => $team->id,
            'user_id' => $user->id,
        ]);

        $this->actingAs($user);

        $relationship = CompanyUserRelationship::factory()->createQuietly([
            'user_id' => $user->id,
            'role_id' => Role::findOrCreate('account-manager')->id,
        ]);

        $result = $this->authService->canEditAccountManager($relationship->company);

        $this->assertTrue($result);
    }

    #[Test]
    public function it_can_edit_account_manager_if_authenticated_user_matches_company_account_manager(): void
    {
        Permission::findOrCreate('can-reallocate-all');

        Permission::findOrCreate('can-reallocate-team');

        $user = User::factory()->create();

        $this->actingAs($user);

        $relationship = CompanyUserRelationship::factory()->createQuietly([
            'role_id' => Role::findOrCreate('account-manager')->id,
            'user_id' => $user,
        ]);

        $result = $this->authService->canEditAccountManager($relationship->company);

        $this->assertTrue($result);
    }

    #[Test]
    public function it_cannot_edit_account_manager_if_authenticated_user_doesnt_match_company_account_manager(): void
    {
        Permission::findOrCreate('can-reallocate-all');

        Permission::findOrCreate('can-reallocate-team');

        $this->actingAs(User::factory()->create());

        $relationship = CompanyUserRelationship::factory()->createQuietly([
            'role_id' => Role::findOrCreate('account-manager')->id,
        ]);

        $result = $this->authService->canEditAccountManager($relationship->company);

        $this->assertFalse($result);
    }
}
