<?php

namespace Tests\Unit\Services\CompanyProfile;

use App\DTO\Geocoding\GeocodingResponseDTO;
use App\DTO\SalesIntel\PersonDTO;
use App\Enums\Prospects\ProspectSource;
use App\Models\CompanyProfile\CompanyProfile;
use App\Models\CompanyProfile\CompanyProfileIndustryService;
use App\Models\CompanyProfile\CompanyProfileLocation;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Prospects\NewBuyerProspect;
use App\Repositories\Legacy\ZipCodeSetRepository;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Services\CompanyProfile\CompanyProfilePromotionService;
use App\Services\Google\GeocodingService;
use App\Services\SalesIntel\SalesIntelService;
use App\Services\WebsiteSanitizerService;
use Exception;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Event;
use Mockery;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyProfilePromotionServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    private CompanyProfilePromotionService $companyProfilePromotionService;

    protected function setUp(): void
    {
        parent::setUp();
        Event::fake();
    }

    protected function getMockedCompanyProfilePromotionService(int $salesIntelCount = 0)
    {
        return Mockery::mock(CompanyProfilePromotionService::class, [
                $this->getMockedSalesIntel($salesIntelCount),
                app(WebsiteSanitizerService::class),
                app(CompanyRepository::class),
                $this->getMockedAddressRepository(),
                app(CompanyLocationRepository::class),
                $this->getMockedGeocodingService(),
                $this->getMockedLocationRepository()
            ]
        )
            ->makePartial()
            ->shouldAllowMockingProtectedMethods();
    }

    protected function createCompanyProfile()
    {
        return CompanyProfile::factory()
            ->has(CompanyProfileIndustryService::factory()->count(2), CompanyProfile::RELATION_COMPANY_PROFILE_INDUSTRY_SERVICES)
            ->has(CompanyProfileLocation::factory()->count(3), CompanyProfile::RELATION_COMPANY_PROFILE_LOCATIONS)
            ->create();
    }

    #[Test]
    public function should_throw_exception_if_already_promoted(): void
    {
        $companyProfilePromotionService = $this->getMockedCompanyProfilePromotionService();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Company already promoted');

        $companyProfile = new CompanyProfile();
        $companyProfile->setRelation(CompanyProfile::RELATION_COMPANY, new Company());

        $companyProfilePromotionService->promote($companyProfile);

        $companyProfile = new CompanyProfile();
        $companyProfile->setRelation(CompanyProfile::RELATION_NEW_BUYER_PROSPECT, new NewBuyerProspect());

        $companyProfilePromotionService->promote($companyProfile);
    }

    #[Test]
    public function should_throw_exception_if_company_profile_has_no_websites(): void
    {
        $companyProfilePromotionService = $this->getMockedCompanyProfilePromotionService();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Company profile websites is required');

        $companyProfile = new CompanyProfile();

        $companyProfilePromotionService->promote($companyProfile);
    }

    #[Test]
    public function should_throw_exception_if_company_profile_has_no_valid_websites(): void
    {
        $companyProfilePromotionService = $this->getMockedCompanyProfilePromotionService();

        $this->expectException(Exception::class);
        $this->expectExceptionMessage('Company profile does not have a valid website');

        $companyProfile = new CompanyProfile();
        $companyProfile->websites = [
            'www.google.com',
            'https://youtube.com/test'
        ];

        $companyProfilePromotionService->promote($companyProfile);
    }

    public function getMockedSalesIntel(int $count = 0): SalesIntelService
    {
        $salesIntelMock = Mockery::mock(SalesIntelService::class);
        $salesIntelMock->shouldReceive('findEmployees')->andReturn(
            collect()->times($count, fn() => (new PersonDTO([
                'first_name'     => $this->faker->name(),
                'last_name'      => $this->faker->lastName(),
                'job_department' => $this->faker->word,
                'job_title'      => $this->faker->jobTitle(),
                'work_emails'    => [$this->faker->email()],
                'phone_numbers'  => [$this->faker->phoneNumber()],
            ]))->toArray())
        );

        return $salesIntelMock;
    }

    public function getMockedLocationRepository()
    {
        $mockedLocationRepository = $this->partialMock(LocationRepository::class);
        $mockedLocationRepository->shouldReceive('getLocationIdsByZipcode')
            ->andReturn([
                'zip_code_location_id' => $this->faker->numberBetween(1, 10),
                'county_location_id'   => $this->faker->numberBetween(1, 10),
                'state_location_id'    => $this->faker->numberBetween(1, 10),
            ]);

        return $mockedLocationRepository;
    }

    public function getMockedGeocodingService()
    {
        $mockedGeocodingService = $this->mock(GeocodingService::class);

        $mockedGeocodingService->shouldReceive('getGeocodeData')
            ->andReturn(GeocodingResponseDTO::fromArray(
                [
                    "results" => [
                        [
                            "address_components" => [
                                [
                                    "long_name"  => "1600",
                                    "short_name" => "1600",
                                    "types"      => ["street_number"]
                                ],
                                [
                                    "long_name"  => "Amphitheatre Parkway",
                                    "short_name" => "Amphitheatre Pkwy",
                                    "types"      => ["route"]
                                ],
                                [
                                    "long_name"  => "Mountain View",
                                    "short_name" => "Mountain View",
                                    "types"      => ["locality", "political"]
                                ],
                                [
                                    "long_name"  => "Santa Clara County",
                                    "short_name" => "Santa Clara County",
                                    "types"      => ["administrative_area_level_2", "political"]
                                ],
                                [
                                    "long_name"  => "California",
                                    "short_name" => "CA",
                                    "types"      => ["administrative_area_level_1", "political"]
                                ],
                                [
                                    "long_name"  => "United States",
                                    "short_name" => "US",
                                    "types"      => ["country", "political"]
                                ],
                                [
                                    "long_name"  => "94043",
                                    "short_name" => "94043",
                                    "types"      => ["postal_code"]
                                ]
                            ],
                            "formatted_address"  => "1600 Amphitheatre Parkway, Mountain View, CA 94043, USA",
                            "geometry"           => [
                                "location"      => [
                                    "lat" => 37.4224764,
                                    "lng" => -122.0842499
                                ],
                                "location_type" => "ROOFTOP",
                                "viewport"      => [
                                    "northeast" => [
                                        "lat" => 37.4238253802915,
                                        "lng" => -122.0829009197085
                                    ],
                                    "southwest" => [
                                        "lat" => 37.4211274197085,
                                        "lng" => -122.0855988802915
                                    ]
                                ]
                            ],
                            "place_id"           => "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
                            "types"              => ["street_address"]
                        ]
                    ],
                    "status"  => "OK"
                ]
            ));

        return $mockedGeocodingService;
    }

    public function getMockedAddressRepository()
    {
        $mockedZipcodeRepository = $this->mock(ZipCodeSetRepository::class);
        $mockedZipcodeRepository->shouldReceive('getUtc')->andReturn(1);

        $mockedAddressRepository = Mockery::mock(AddressRepository::class, [
                $mockedZipcodeRepository
            ]
        )->makePartial();

        return $mockedAddressRepository;
    }

    #[Test]
    public function should_create_new_buyer_prospect(): void
    {
        $companyProfilePromotionService = $this->getMockedCompanyProfilePromotionService();

        $companyProfilePromotionService->shouldNotReceive('createCompany', 'createCompanyUsers');

        $companyProfile = $this->createCompanyProfile();

        $companyProfilePromotionService->promote($companyProfile);

        $companyProfilePromotionService->shouldHaveReceived('createNewBuyerProspect');

        $refreshCompanyProfile = $companyProfile->refresh();
        $this->assertNotNull($refreshCompanyProfile->{CompanyProfile::FIELD_NEW_BUYER_PROSPECT_ID});

        $newBuyerProspect = $refreshCompanyProfile->{CompanyProfile::RELATION_NEW_BUYER_PROSPECT};

        /** @var CompanyProfileLocation $location */
        $location = $refreshCompanyProfile->{CompanyProfile::RELATION_COMPANY_PROFILE_LOCATIONS}()->first();

        $this->assertModelFields([
            NewBuyerProspect::FIELD_SOURCE               => ProspectSource::FIXR_DISCOVERY->value,
            NewBuyerProspect::FIELD_INDUSTRY_SERVICE_IDS => $refreshCompanyProfile->{CompanyProfile::RELATION_INDUSTRY_SERVICES}->pluck('id')->toArray(),
            NewBuyerProspect::FIELD_COMPANY_NAME         => $refreshCompanyProfile->{CompanyProfile::FIELD_NAME},
            NewBuyerProspect::FIELD_COMPANY_WEBSITE      => $refreshCompanyProfile->{CompanyProfile::FIELD_WEBSITES}[0],
            NewBuyerProspect::FIELD_COMPANY_DESCRIPTION  => $refreshCompanyProfile->{CompanyProfile::FIELD_DESCRIPTION},
            NewBuyerProspect::FIELD_COMPANY_PHONE        => $refreshCompanyProfile->{CompanyProfile::FIELD_PHONES}[0],
            NewBuyerProspect::FIELD_ADDRESS_STREET       => $location->raw,
            NewBuyerProspect::FIELD_ORDINAL_VALUE        => 9999,
        ], $newBuyerProspect);

    }

    #[Test]
    public function should_create_company(): void
    {
        $companyProfilePromotionService = $this->getMockedCompanyProfilePromotionService(salesIntelCount: 1);

        $companyProfile = $this->createCompanyProfile();

        $companyProfilePromotionService->promote($companyProfile);

        $updatedProfile = $companyProfile->refresh();

        $this->assertNotNull($updatedProfile->{CompanyProfile::FIELD_COMPANY_ID});
        $this->assertNull($updatedProfile->{CompanyProfile::FIELD_NEW_BUYER_PROSPECT_ID});

        $this->assertEquals($companyProfile->company->{Company::RELATION_USERS}->count(), 1);
        $this->assertEquals($companyProfile->company->{Company::RELATION_LOCATIONS}->count(), 3);

        $this->assertModelFields([
            Address::FIELD_LEGACY_ID            => null,
            Address::FIELD_ADDRESS_1            => "Amphitheatre Parkway",
            Address::FIELD_ADDRESS_2            => "",
            Address::FIELD_CITY                 => "Mountain View",
            Address::FIELD_COUNTY               => "Santa Clara County",
            Address::FIELD_STATE                => "California",
            Address::FIELD_ZIP_CODE             => "94043",
            Address::FIELD_COUNTRY              => "US",
            Address::FIELD_LATITUDE             => 37.42,
            Address::FIELD_LONGITUDE            => -122.08,
            Address::FIELD_PLACE_ID             => "ChIJ2eUgeAK6j4ARbn5u_wAGqWA",
            Address::FIELD_UTC                  => 1,
            Address::FIELD_IMPORTED             => 1,
        ], $companyProfile->company->{Company::RELATION_LOCATIONS}()->first()->address);

        $this->assertEquals(
            $companyProfile->{CompanyProfile::RELATION_INDUSTRY_SERVICES}->pluck('id'),
            $companyProfile->company->{Company::RELATION_SERVICES}->pluck('id')
        );
    }

    #[Test]
    public function should_not_dupe_company(): void
    {
        $companyProfilePromotionService = $this->getMockedCompanyProfilePromotionService(salesIntelCount: 1);

        $companyProfile = $this->createCompanyProfile();

        Company::factory()
            ->create([
                Company::FIELD_WEBSITE => $companyProfile->getPrimaryWebsite()
            ]);

        $companyProfilePromotionService->shouldNotReceive('createCompany');
        $companyProfilePromotionService->shouldNotReceive('createNewBuyerProspect');

        $companyProfilePromotionService->promote($companyProfile);

        $updatedProfile = $companyProfile->refresh();

        $this->assertNotNull($updatedProfile->{CompanyProfile::FIELD_COMPANY_ID});
        $this->assertNull($updatedProfile->{CompanyProfile::FIELD_NEW_BUYER_PROSPECT_ID});

        $this->assertEquals($companyProfile->company->{Company::RELATION_USERS}->count(), 1);
        $this->assertEquals($companyProfile->company->{Company::RELATION_LOCATIONS}->count(), 0);
    }

    #[Test]
    public function should_not_dupe_prospect(): void
    {
        $companyProfilePromotionService = $this->getMockedCompanyProfilePromotionService();

        /** @var CompanyProfile $companyProfile */
        $companyProfile = $this->createCompanyProfile();

        $newBuyerProspect = NewBuyerProspect::factory()
            ->create([
                NewBuyerProspect::FIELD_COMPANY_WEBSITE => $companyProfile->getPrimaryWebsite()
            ]);

        $companyProfilePromotionService->shouldNotHaveReceived('createCompany');
        $companyProfilePromotionService->shouldNotHaveReceived('createNewBuyerProspect');

        $companyProfilePromotionService->promote($companyProfile);

        $this->assertNull($companyProfile->refresh()->{CompanyProfile::FIELD_COMPANY_ID});
        $this->assertNotNull($companyProfile->refresh()->{CompanyProfile::FIELD_NEW_BUYER_PROSPECT_ID});
    }
}
