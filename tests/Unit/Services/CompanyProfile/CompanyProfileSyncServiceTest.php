<?php

namespace Tests\Unit\Services\CompanyProfile;

use App\Models\CompanyProfile\CompanyProfile;
use App\Models\Legacy\Location;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\CompanyProfile\CompanyProfileRepository;
use App\Repositories\CompanyProfile\CompanyProfileReviewRepository;
use App\Repositories\CompanyProfile\CompanyProfileServiceAreaRepository;
use App\Repositories\LocationRepository;
use App\Services\CompanyProfile\CompanyProfileIndustryServiceService;
use App\Services\CompanyProfile\CompanyProfileLocationService;
use App\Services\CompanyProfile\CompanyProfileSyncService;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\WithFaker;
use Mockery;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyProfileSyncServiceTest extends TestCase
{
    use DatabaseTransactions, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        $this->app->instance(CompanyProfileRepository::class, $this->mock(CompanyProfileRepository::class));
        $this->app->instance(CompanyProfileReviewRepository::class, $this->mock(CompanyProfileReviewRepository::class));
        $this->app->instance(CompanyProfileLocationService::class, $this->mock(CompanyProfileLocationService::class));
        $this->app->instance(CompanyProfileIndustryServiceService::class, $this->mock(CompanyProfileIndustryServiceService::class));
        $this->app->instance(CompanyProfileServiceAreaRepository::class, $this->mock(CompanyProfileServiceAreaRepository::class));
        $this->app->instance(ActivityLogRepository::class, $this->mock(ActivityLogRepository::class));
    }

    /**
     * @param string $stringClass
     * @param array $data
     * @return Mockery\LegacyMockInterface
     */
    public function getMocked(string $stringClass, array $data): Mockery\LegacyMockInterface
    {
        $mock = $this->mock($stringClass)->makePartial()->shouldAllowMockingProtectedMethods();

        foreach ($data as $method => $return) {
            $mock->shouldReceive($method)
                ->andReturn($return);
        }

        return $mock;
    }

    #[Test]
    public function should_throw_exception_if_zip_not_found(): void
    {
        $mockedLocationRepository = $this->getMocked(LocationRepository::class, [
            'getZipCode' => null
        ]);

        $this->app->instance(LocationRepository::class, $mockedLocationRepository);

        $this->expectExceptionMessage('Location for zipcode not found');

        $companyProfileSyncService = app(CompanyProfileSyncService::class);

        $companyProfileSyncService->createUniqueCompanySlug('random', 1234567);
    }

    #[Test]
    public function should_fail_if_part_is_missing(): void
    {
        $zipLocation = new Location();
        $zipLocation->{Location::STATE_ABBREVIATION} = 'CA';

        $mockedLocationRepository = $this->getMocked(LocationRepository::class, [
            'getZipCode' => $zipLocation
        ]);

        $this->app->instance(LocationRepository::class, $mockedLocationRepository);

        $this->expectExceptionMessage('Some part is missing to compose the slug');

        /** @var CompanyProfileSyncService $companyProfileSyncService */
        $companyProfileSyncService = app(CompanyProfileSyncService::class);
        $companyProfileSyncService->createUniqueCompanySlug($this->faker->name, 1234567);

        $this->assertEquals(true, false);
    }

    #[Test]
    public function should_successfully_create_unique_slug(): void
    {
        $zipLocation = new Location();
        $zipLocation->{Location::STATE_ABBREVIATION} = 'ca';
        $zipLocation->{Location::COUNTY_KEY} = 'san-francisco';

        $mockedLocationRepository = $this->getMocked(LocationRepository::class, [
            'getZipCode' => $zipLocation
        ]);

        $this->app->instance(LocationRepository::class, $mockedLocationRepository);

        CompanyProfile::factory()
            ->create([
                CompanyProfile::FIELD_PROFILE_SLUG => 'solarreviews-ca-san-francisco'
            ]);

        /** @var CompanyProfileSyncService $companyProfileSyncService */
        $companyProfileSyncService = app(CompanyProfileSyncService::class);
        $slug = $companyProfileSyncService->createUniqueCompanySlug('solarreviews', 1234567);

        $this->assertEquals('solarreviews-ca-san-francisco-1', $slug);
    }

    #[Test]
    #[DataProvider('namePartsProvider')]
    public function should_successfully_create_slug(string $companyName, string $county, string $state, string $expected): void
    {
        $zipLocation = new Location();
        $zipLocation->{Location::STATE_ABBREVIATION} = $state;
        $zipLocation->{Location::COUNTY_KEY} = $county;

        $mockedLocationRepository = $this->getMocked(LocationRepository::class, [
            'getZipCode' => $zipLocation
        ]);

        $this->app->instance(LocationRepository::class, $mockedLocationRepository);

        /** @var CompanyProfileSyncService $companyProfileSyncService */
        $companyProfileSyncService = app(CompanyProfileSyncService::class);
        $slug = $companyProfileSyncService->createUniqueCompanySlug($companyName, 1234567);

        $this->assertEquals($expected, $slug);
    }

    public static function namePartsProvider(): array
    {
        return [
            'basic_company_with_space'       => [
                'ABC Corp',
                'Los Angeles',
                'CA',
                'abc-corp-ca-los-angeles'
            ],
            'company_with_ampersand'         => [
                'Smith & Jones LLC',
                'Cook',
                'IL',
                'smith-jones-llc-il-cook'
            ],
            'company_with_dash_and_period'   => [
                'Tech-Solutions Inc.',
                'King',
                'WA',
                'tech-solutions-inc-wa-king'
            ],
            'company_with_quotes'            => [
                'Restaurant "Le Petit"',
                'Orleans',
                'LA',
                'restaurant-le-petit-la-orleans'
            ],
            'company_with_apostrophe'        => [
                'Mom\'s Kitchen',
                'Harris',
                'TX',
                'mom-s-kitchen-tx-harris'
            ],
            'company_with_numbers'           => [
                '123 Main St. Store',
                'Riverside',
                'CA',
                '123-main-st-store-ca-riverside'
            ],
            'company_with_apostrophe_name'   => [
                'O\'Reilly Auto Parts',
                'Maricopa',
                'AZ',
                'o-reilly-auto-parts-az-maricopa'
            ],
            'company_with_parentheses'       => [
                'Company (Holdings)',
                'New York',
                'NY',
                'company-holdings-ny-new-york'
            ],
            'company_with_accent'            => [
                'Café & Bistro',
                'Saint Louis',
                'MO',
                'caf-bistro-mo-saint-louis'
            ],
            'company_with_exclamation'       => [
                'Best Buy!',
                'Hennepin',
                'MN',
                'best-buy-mn-hennepin'
            ],
            'company_hr_block'               => [
                'H&R Block',
                'Jackson',
                'MS',
                'h-r-block-ms-jackson'
            ],
            'company_att'                    => [
                'AT&T Services',
                'Dallas',
                'TX',
                'at-t-services-tx-dallas'
            ],
            'company_ben_jerrys'             => [
                'Ben & Jerry\'s',
                'Chittenden',
                'VT',
                'ben-jerry-s-vt-chittenden'
            ],
            'company_with_dash_county_space' => [
                'Johnson-Smith Co.',
                'Salt Lake',
                'UT',
                'johnson-smith-co-ut-salt-lake'
            ],
            'company_with_cent_symbol'       => [
                '99¢ Store',
                'Orange',
                'CA',
                '99-store-ca-orange'
            ],
            'company_toys_r_us'              => [
                'Toys"R"Us',
                'Bergen',
                'NJ',
                'toys-r-us-nj-bergen'
            ],
            'company_mcdonalds_with_number'  => [
                'McDonald\'s #1234',
                'Wayne',
                'MI',
                'mcdonald-s-1234-mi-wayne'
            ],
            'company_tmobile'                => [
                'T-Mobile USA',
                'King',
                'WA',
                't-mobile-usa-wa-king'
            ],
            'company_barnes_noble'           => [
                'Barnes & Noble',
                'New York',
                'NY',
                'barnes-noble-ny-new-york'
            ],
            'company_seven_eleven'           => [
                '7-Eleven',
                'Travis',
                'TX',
                '7-eleven-tx-travis'
            ],
        ];
    }
}
