<?php

namespace Tests\Unit\Services\Prospects;

use App\Models\Odin\Company;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectDuplicateMatchingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class ProspectDuplicateMatchingServiceTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_get_all_potential_matches_with_a_simplified_domain()
    {
        $company = Company::factory()->createQuietly([
            'website_verified_url' => 'https://www.foobar.com',
        ]);

        $prospect = NewBuyerProspect::factory()->sourcedFromSalesIntel()->create([
            'company_website' => 'foobar.com',
        ]);

        $matches = ProspectDuplicateMatchingService::getAllPotentialMatches($prospect);

        $this->assertCount(1, $matches);
        $this->assertTrue($matches->contains($company));
    }

    #[Test]
    public function it_can_get_all_potential_matches_with_a_full_domain()
    {
        $company = Company::factory()->createQuietly([
            'website_verified_url' => 'https://www.foobar.com',
        ]);

        $prospect = NewBuyerProspect::factory()->sourcedFromSalesIntel()->create([
            'company_website' => 'http://www.foobar.com/some-other-path',
        ]);

        $matches = ProspectDuplicateMatchingService::getAllPotentialMatches($prospect);

        $this->assertCount(1, $matches);
        $this->assertTrue($matches->contains($company));
    }

    #[Test]
    public function it_returns_nothing_on_an_empty_domain_match()
    {
        $prospect = NewBuyerProspect::factory()->sourcedFromSalesIntel()->create([
            'company_website' => '',
        ]);

        $matches = ProspectDuplicateMatchingService::findCompaniesWithExactWebsite($prospect);

        $this->assertEmpty($matches);
    }
}
