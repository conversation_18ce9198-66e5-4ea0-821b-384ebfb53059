<?php

namespace Tests\Unit\Services\Prospects;

use App\Enums\Company\CompanyAdminStatus;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanySalesStatus;
use App\Enums\Prospects\ProspectStatus;
use App\Models\ActivityFeed;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\ProductAssignment;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use App\Services\Prospects\ProspectHuntingService;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class ProspectHuntingServiceTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    protected Industry $industry;

    protected User $user;

    protected Role $role;

    public static function sales_statuses_that_disqualify_company(): array
    {
        return [
            'Out of Business' => [CompanySalesStatus::OUT_OF_BUSINESS],
            'Do not engage' => [CompanySalesStatus::DO_NOT_ENGAGE],
            'DQ\d' => [CompanySalesStatus::DQ_D],
            'Not interested' => [CompanySalesStatus::NOT_INTERESTED],
            'Commercial only' => [CompanySalesStatus::COMMERCIAL_ONLY],
        ];
    }

    #[Test]
    public function get_next_available_existing_company_with_it_assigns_a_business_development_manager_to_a_company_with_null_consolidated_status(): void
    {
        $this->markTestSkipped('Consolidated status null has been removed with company status refactor. Revisit and fix with appropriate status');

        $this->company = Company::factory()->createQuietly([
            'consolidated_status' => null,
        ]);

        CompanyIndustry::factory()->createQuietly([
            'company_id' => $this->company->id,
            'industry_id' => $this->industry->id,
        ]);

        $this->role = Role::findOrCreate('business-development-manager');

        $this->user = User::factory()->withRole($this->role->name)->create();

        ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $this->assertDatabaseHas('company_user_relationships', [
            'company_id' => $this->company->id,
            'user_id' => $this->user->id,
            'role_id' => $this->role->id,
        ]);

        $this->assertDatabaseCount('company_user_relationships', 1);

        $this->assertTrue($this->user->is($this->company->refresh()->businessDevelopmentManager));
    }

    #[Test]
    public function get_next_available_existing_company_with_it_does_not_assign_business_development_manager_if_reserve_is_false(): void
    {
        Company::factory()->createQuietly();

        ProspectHuntingService::getNextAvailableExistingCompany(User::factory()->create(), reserve: false);

        $this->assertDatabaseCount('company_user_relationships', 0);
    }

    #[Test]
    public function get_next_available_existing_company_with_it_does_not_assign_business_development_manager_if_there_is_no_company(): void
    {
        ProspectHuntingService::getNextAvailableExistingCompany(User::factory()->create());

        $this->assertDatabaseCount('company_user_relationships', 0);
    }

    #[Test]
    public function get_next_available_existing_company_with_it_does_not_assign_business_development_manager_if_company_is_not_good_to_assign_due_to_present_account_manager(): void
    {
        Role::findOrCreate('account-manager');

        $this->company = Company::factory()->createQuietly();

        $this->user = User::factory()->create();

        $this->company->assign($this->user)->as(Role::findOrCreate('account-manager')->name);

        $result = ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $this->assertNull($this->company->refresh()->businessDevelopmentManager);

        $this->assertNull($result);
    }

    #[Test]
    public function get_next_available_existing_company_with_it_does_not_assign_business_development_manager_if_company_is_not_good_to_assign_due_to_present_business_development_manager(): void
    {
        $this->role = Role::findOrCreate('business-development-manager');

        $this->company = Company::factory()->createQuietly();

        $this->user = User::factory()->create();

        $this->company->assign($this->user)->as($this->role->name);

        $result = ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $this->assertNotNull($this->company->refresh()->businessDevelopmentManager);

        $this->assertNull($result);
    }

    #[Test]
    public function get_next_available_existing_company_with_it_does_not_assign_business_development_manager_if_company_is_not_good_to_assign_due_to_being_an_active_buyer(): void
    {
        Role::findOrCreate('business-development-manager');

        $this->company = Company::factory()->createQuietly();

        $this->user = User::factory()->create();

        ProductAssignment::factory()->create([
            'company_id' => $this->company->id,
            'delivered' => true,
            'chargeable' => true,
            'delivered_at' => Carbon::now(),
        ]);

        ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $this->assertNull($this->company->refresh()->businessDevelopmentManager);
    }

    #[Test]
    public function get_next_available_existing_company_with_it_does_not_assign_business_development_manager_if_company_is_not_good_to_assign_due_to_having_recent_business_development_manager_activity(): void
    {
        $this->role = Role::findOrCreate('business-development-manager');

        $this->company = Company::factory()->createQuietly();

        $this->user = User::factory()->create();

        $this->company->assign(User::factory()->create())->as($this->role->name);

        ActivityFeed::factory()->create([
            'company_id' => $this->company->id,
        ]);

        ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $this->assertTrue($this->user->isNot($this->company->refresh()->businessDevelopmentManager));

        $this->assertNotNull($this->company->refresh()->businessDevelopmentManager);
    }

    #[Test, DataProvider('sales_statuses_that_disqualify_company')]
    public function get_next_available_existing_company_with_it_does_not_assign_business_development_manager_if_company_is_one_of_the_disqualified_sales_statuses(CompanySalesStatus $companySalesStatus): void
    {
        $this->company = Company::factory()->createQuietly([
            'consolidated_status' => CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
            'sales_status' => $companySalesStatus,
        ]);

        $this->user = User::factory()->create();

        ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $result = ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $this->assertNull($this->company->refresh()->businessDevelopmentManager);

        $this->assertNull($result);
    }

    #[Test]
    public function its_sort_order_for_companies_accounts_for_sales_intel_user_imports(): void
    {
        $otherCompany = Company::factory()->createQuietly([
            Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_LOCKED,
        ]);

        CompanyIndustry::factory()->createQuietly([
            'company_id' => $otherCompany->id,
            'industry_id' => $this->industry->id,
        ]);

        $companyWithSalesIntelUser = Company::factory()->createQuietly([
            Company::FIELD_ADMIN_STATUS => CompanyAdminStatus::ADMIN_LOCKED,
        ]);

        CompanyIndustry::factory()->createQuietly([
            'company_id' => $companyWithSalesIntelUser->id,
            'industry_id' => $this->industry->id,
        ]);

        CompanyUser::factory()->for($companyWithSalesIntelUser)->create([
            'import_source' => 'salesintel',
        ]);

        $this->user = User::factory()->create();

        $sortedCompany = ProspectHuntingService::getNextAvailableExistingCompany($this->user);

        $this->assertTrue($sortedCompany->isNot($otherCompany));
        $this->assertTrue($sortedCompany->is($companyWithSalesIntelUser));
    }

    #[Test]
    public function its_sort_order_for_prospects_accounts_for_sales_intel_verified_results(): void
    {
        $this->markTestSkipped('skipped');
        $verifiedProspect = NewBuyerProspect::factory()->has(Contact::factory())->sourcedFromSalesIntel()->createQuietly([
            'user_id' => 0,
            'source_data' => ['verified' => true],
            'status' => ProspectStatus::ACTIVE,
        ]);

        $unverifiedProspect = NewBuyerProspect::factory()->has(Contact::factory())->sourcedFromSalesIntel()->createQuietly([
            'user_id' => 0,
            'released_at' => now(),
            'source_data' => ['verified' => false],
        ]);

        $sortedCompanyProspect = ProspectHuntingService::getNextAvailableProspect(User::factory()->create());

        $this->assertTrue($sortedCompanyProspect->isNot($verifiedProspect),
            $sortedCompanyProspect->id.' should not match '.$verifiedProspect->id);

        $this->assertTrue($sortedCompanyProspect->is($unverifiedProspect),
            $sortedCompanyProspect->id.' should match '.$unverifiedProspect->id);

        $sortedCompanyProspect = ProspectHuntingService::getNextAvailableProspect(User::factory()->create());

        $this->assertNull($sortedCompanyProspect);
    }

    #[Test]
    public function its_sort_order_for_prospects_accounts_for_sales_intel_targeted_results(): void
    {
        $untargetedProspect = NewBuyerProspect::factory()->has(Contact::factory())->sourcedFromSalesIntel()->createQuietly([
            'user_id' => 0,
            'source_data' => ['targeted' => false],
        ]);

        $targetedProspect = NewBuyerProspect::factory()->has(Contact::factory())->sourcedFromSalesIntel()->createQuietly([
            'user_id' => 0,
            'source_data' => ['targeted' => true],
            'status' => ProspectStatus::ACTIVE,
        ]);

        $sortedCompanyProspect = ProspectHuntingService::getNextAvailableProspect(User::factory()->create());

        $this->assertTrue($sortedCompanyProspect->isNot($targetedProspect));
        $this->assertTrue($sortedCompanyProspect->is($untargetedProspect));

        $sortedCompanyProspect = ProspectHuntingService::getNextAvailableProspect(User::factory()->create());

        $this->assertNull($sortedCompanyProspect);
    }

    #[Test]
    public function prospect_assigned_at_is_set_when_reserved()
    {
        $prospect = NewBuyerProspect::factory()->create([
            'user_id' => 0
        ]);

        Contact::factory()->for($prospect, 'prospect')->create();

        $user = User::factory()->create();

        $nextProspect = ProspectHuntingService::getNextAvailableProspect($user);

        $this->assertTrue($prospect->is($nextProspect));
        $this->assertNotNull($prospect->refresh()->user_assigned_at);
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->industry = Industry::factory()->createQuietly([
            'id' => 2,
        ]);
    }
}
