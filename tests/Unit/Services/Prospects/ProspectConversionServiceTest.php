<?php

namespace Tests\Unit\Services\Prospects;

use App\Models\Odin\Company;
use App\Models\Prospects\Contact;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\Prospects\ProspectConversionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase;
use PHPUnit\Framework\Attributes\Test;

class ProspectConversionServiceTest extends TestCase
{
    use RefreshDatabase;

    protected Company $company;

    #[Test]
    public function it_can_create_the_decision_maker_as_a_company_user()
    {
        $prospect = NewBuyerProspect::factory()->create();
        $company = Company::factory()->createQuietly();

        $this->assertDatabaseEmpty('company_users');

        ProspectConversionService::createCompanyUsers($company, $prospect);

        $this->assertDatabaseHas('company_users', [
            'first_name' => $prospect->decision_maker_first_name,
            'last_name' => $prospect->decision_maker_last_name,
            'email' => $prospect->decision_maker_email,
            'cell_phone' => $prospect->decision_maker_phone,
            'is_decision_maker' => true,
        ]);
    }

    #[Test]
    public function it_can_create_other_contacts_as_company_users()
    {
        $prospect = NewBuyerProspect::factory()->create();
        $company = Company::factory()->createQuietly();

        $contact = Contact::factory()->for($prospect, 'prospect')->create();

        $this->assertDatabaseEmpty('company_users');

        ProspectConversionService::createCompanyUsers($company, $prospect);

        $this->assertDatabaseCount('company_users', 2);
        $this->assertDatabaseHas('company_users', [
            'first_name' => $contact->first_name,
            'last_name' => $contact->last_name,
            'email' => $contact->email,
            'cell_phone' => $contact->cell_phone,
            'office_phone' => $contact->office_phone,
            'title' => $contact->title,
            'department' => $contact->department,
            'is_decision_maker' => 0,
        ]);
    }

    #[Test]
    public function it_does_not_create_a_duplicate_company_user_for_the_decision_maker()
    {
        $prospect = NewBuyerProspect::factory()->create();
        $company = Company::factory()->createQuietly();

        $contact = Contact::factory()->for($prospect, 'prospect')->create([
            'first_name' => $prospect->decision_maker_first_name,
            'last_name' => $prospect->decision_maker_last_name,
            'email' => $prospect->decision_maker_email,
            'cell_phone' => $prospect->decision_maker_phone,
        ]);

        $this->assertDatabaseEmpty('company_users');

        ProspectConversionService::createCompanyUsers($company, $prospect);

        $this->assertDatabaseCount('company_users', 1);
    }
}
