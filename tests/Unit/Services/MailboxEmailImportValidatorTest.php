<?php

namespace Tests\Unit\Services;

use App\DTO\Mail\Email;
use App\DTO\Mail\EmailMeta;
use App\Services\Mailbox\MailboxEmailImportValidator;
use Faker\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

class MailboxEmailImportValidatorTest extends TestCase
{
    private MailboxEmailImportValidator $validator;

    protected function setUp(): void
    {
        parent::setUp();
        $this->validator = new MailboxEmailImportValidator;
    }

    #[DataProvider('emailValidationProvider')]
    public function test_email_validation(array $emailData, array $blacklist, bool $expected)
    {
        $email = new Email(
            meta: new EmailMeta(
                from: Arr::get($emailData, 'from'),
                to  : Arr::get($emailData, 'to'),
            )
        );

        $result = $this->validator->isValidEmail($email, collect($blacklist));
        $this->assertEquals($expected, $result);
    }

    public static function emailValidationProvider(): array
    {
        function generateFakeEmail(?string $domain = null, ?string $name = null): string
        {
            $faker = Factory::create();
            $name = $name ?? $faker->firstName();
            $domain = $domain ?? $faker->domainName();

            return Str::lower("$name@$domain.com");
        }

        function generateMirroredRules(array $rules): array
        {
            $mirroredRules = [];
            foreach ($rules as $rule) {
                [$from, $to] = explode(':', $rule);
                $mirroredRules[] = "$to:$from";
            }

            return $mirroredRules;
        }

        function generateScenarios(array $baseScenarios): array
        {
            $scenarios = [];
            foreach ($baseScenarios as $key => [$emailData, $rules, $expected]) {
                // Add the base scenario
                $scenarios[$key] = [$emailData, $rules, $expected];

                // Flip from and to
                $mirroredEmailData = [
                    'from' => $emailData['to'][0],
                    'to' => [$emailData['from']],
                ];

                // Add the mirrored scenario
                $scenarios[$key.' (mirrored)'] = [$mirroredEmailData, generateMirroredRules($rules), $expected];
            }

            return $scenarios;
        }

        $baseScenarios = [
            'Should return false 1' => [
                [
                    'from' => '<EMAIL>',
                    'to' => [generateFakeEmail('company')],
                ],
                ['<EMAIL>:*@Company.com '],
                false,
            ],
            'Should return false 2' => [
                [
                    'from' => '<EMAIL>',
                    'to' => [generateFakeEmail('company')],
                ],
                ['<EMAIL>:*'],
                false,
            ],
            'Should return false 3' => [
                [
                    'from' => generateFakeEmail('example'),
                    'to' => [generateFakeEmail('email')],
                ],
                ['*@example.com:*@email.com'],
                false,
            ],
            'Should return false 4' => [
                [
                    'from' => generateFakeEmail(),
                    'to' => [generateFakeEmail()],
                ],
                ['*:*'],
                false,
            ],
            'Should return false 5' => [
                [
                    'from' => '<EMAIL>',
                    'to' => [generateFakeEmail()],
                ],
                ['<EMAIL>:*'],
                false,
            ],
            'Should return false 6' => [
                [
                    'from' => '<EMAIL>',
                    'to' => ['<EMAIL>'],
                ],
                ['<EMAIL>:<EMAIL>'],
                false,
            ],
            'Should return false 7' => [
                [
                    'from' => generateFakeEmail('company', 'cto'),
                    'to' => [generateFakeEmail('company', 'dev1'), generateFakeEmail('company', 'dev2')],
                ],
                ['<EMAIL>:*@company.com', '<EMAIL>:*@company.com'],
                false,
            ],
            'Should return false 8' => [
                [
                    'from' => generateFakeEmail('company', 'cto'),
                    'to' => [generateFakeEmail('company', 'dev1'), generateFakeEmail('company', 'dev2')],
                ],
                ['*:*'],
                false,
            ],
            'Should return true 1' => [
                [
                    'from' => '<EMAIL>',
                    'to' => ['<EMAIL>'],
                ],
                [],
                true,
            ],
            'Should return true 2' => [
                [
                    'from' => '<EMAIL>',
                    'to' => ['<EMAIL>'],
                ],
                ['<EMAIL>:<EMAIL>'],
                true,
            ],
            'Should return true 3' => [
                [
                    'from' => generateFakeEmail('company', 'dev'),
                    'to' => [generateFakeEmail('company', 'dev1')],
                ],
                ['<EMAIL>:<EMAIL>'],
                true,
            ],
        ];

        return generateScenarios($baseScenarios);
    }
}
