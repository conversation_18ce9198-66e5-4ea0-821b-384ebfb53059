<?php

namespace Tests\Unit\Jobs;

use App\Jobs\PrivacyRequestSearchJob;
use App\Models\PrivacyRequest;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Str;
use Propaganistas\LaravelPhone\PhoneNumber;
use Tests\TestCase;

class PrivacyRequestSearchJobTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub
        $this->markTestSkipped('Temporarily disabled for launch');
    }

    public function test_can_dispatch_job(): void
    {
        Queue::fake();
        $this->markTestSkipped('Temporarily disabled for launch');
        $privacyRequest = PrivacyRequest::factory()
            ->create();

        PrivacyRequestSearchJob::dispatch($privacyRequest);

        Queue::assertPushed(
            PrivacyRequestSearchJob::class,
            fn ($job) => $job->privacyRequest->id === $privacyRequest->id
        );
    }

    public function test_can_get_search_params(): void
    {
        $privacyRequest = PrivacyRequest::factory()
            ->create();

        $job = new PrivacyRequestSearchJob($privacyRequest);

        $results = $job->__searchParams;

        $this->assertNotEmpty($results);
        $this->assertIsArray($results);
        $this->assertCount(7, $results);
        $this->assertEquals($privacyRequest->payload['email'], $results[0]);
        $this->assertEquals($privacyRequest->payload['phone'], $results[1]);
        $this->assertEquals(
            Str::replaceMatches('/\D/', '', $privacyRequest->payload['phone']),
            $results[2]
        );

        $phone = new PhoneNumber($privacyRequest->payload['phone'], 'US');

        $this->assertEquals($phone->formatE164(), $results[3]);
        $this->assertEquals($phone->formatInternational(), $results[4]);
        $this->assertEquals($phone->formatNational(), $results[5]);
        $this->assertEquals($phone->formatForCountry('US'), $results[6]);

        $job->__searchParams = [$privacyRequest->payload['email']];

        $results = $job->__searchParams;

        $this->assertNotEmpty($results);
        $this->assertIsArray($results);
        $this->assertCount(1, $results);
        $this->assertEquals($privacyRequest->payload['email'], $results[0]);

        $job->__searchParams = [$privacyRequest->payload['phone']];

        $results = $job->__searchParams;

        $this->assertNotEmpty($results);
        $this->assertIsArray($results);
        $this->assertCount(1, $results);
        $this->assertEquals($privacyRequest->payload['phone'], $results[0]);
    }
}
