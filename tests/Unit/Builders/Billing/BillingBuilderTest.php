<?php

namespace Tests\Unit\Builders\Billing;

use App\Builders\Billing\BillingBuilder;
use App\Models\Billing\Invoice;
use App\Models\CompanyUserRelationship;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class BillingBuilderTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function for_company_user_relationship(): void
    {
        $invoice = Invoice::factory()->createQuietly();

        $onboardingManagerRelationship = $this->createCompanyUserRelationshipForInvoice($invoice, 'onboarding-manager');
        $accountManagerRelationship = $this->createCompanyUserRelationshipForInvoice($invoice, 'account-manager');
        $businessDevelopmentManagerRelationship = $this->createCompanyUserRelationshipForInvoice($invoice, 'business-development-manager');

        $result = TestBillingBuilder::query()->forCompanyUserRelationship($onboardingManagerRelationship->user->id, $accountManagerRelationship->user->id, $businessDevelopmentManagerRelationship->user->id);

        $this->assertCount(1, $result->getQuery()->get());

        $onboardingManagerRelationship->delete();

        $result = TestBillingBuilder::query()->forCompanyUserRelationship($onboardingManagerRelationship->user->id, $accountManagerRelationship->user->id, $businessDevelopmentManagerRelationship->user->id);

        $this->assertCount(0, $result->getQuery()->get());

        $onboardingManagerRelationship->restore();

        $this->assertCount(1, $result->getQuery()->get());
    }

    #[Test]
    public function filter_by_role_with_no_user(): void
    {
        Invoice::factory()->createQuietly();

        $result = TestBillingBuilder::query()->filterByRole();

        $this->assertCount(1, $result->getQuery()->get());
    }

    #[Test]
    public function filter_by_role_with_user_is_finance_owner(): void
    {
        Invoice::factory()->createQuietly();

        $result = TestBillingBuilder::query()->filterByRole(User::factory()->create()->assignRole(Role::findOrCreate('finance-owner')));

        $this->assertCount(1, $result->getQuery()->get());
    }

    #[Test]
    public function filter_by_role_with_user_is_not_finance_owner_count_is_zero(): void
    {
        Invoice::factory()->createQuietly();

        $result = TestBillingBuilder::query()->filterByRole(User::factory()->create());

        $this->assertCount(1, $result->getQuery()->get());
    }

    #[Test]
    public function filter_by_role_with_user_is_not_finance_owner_count_is_one(): void
    {
        $invoice = Invoice::factory()->createQuietly();

        $accountManagerRelationship = CompanyUserRelationship::factory()->create([
            'company_id' => $invoice->company_id,
            'role_id' => Role::findOrCreate('account-manager')->id,
        ]);

        $result = TestBillingBuilder::query()->filterByRole($accountManagerRelationship->user);

        $this->assertCount(1, $result->getQuery()->get());
    }

    private function createCompanyUserRelationshipForInvoice(Invoice $invoice, string $role, ?User $user = null): CompanyUserRelationship
    {
        return CompanyUserRelationship::factory()->create([
            'company_id' => $invoice->company_id,
            'user_id' => $user?->id ?? User::factory()->create()->id,
            'role_id' => Role::findOrCreate($role)->id,
        ]);
    }
}

class TestBillingBuilder extends BillingBuilder
{
    public static function query(): BillingBuilder
    {
        return new self(Invoice::query());
    }
}
