<?php

namespace Tests;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Support\Facades\DB;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication;

    protected function setUp(): void
    {
        parent::setUp();

        // Sometimes the test database isn't always in UTC as it should be
        DB::statement("SET @@session.time_zone = '+00:00';");

        $this->withoutVite();
    }

    public function assertModelFields(array $assertions, Model $model): void
    {
        foreach ($assertions as $field => $value) {
            $this->assertEquals($model->toArray()[$field], $value);
        }
    }
}
