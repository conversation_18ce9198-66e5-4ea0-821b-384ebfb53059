<?php

namespace Tests;

use Illuminate\Support\Str;
use Mo<PERSON>y\Exception;

use function app;

trait Testable
{
    public function __call(string $name, array $arguments = []): mixed
    {
        if (app()->environment('testing')) {
            $name = Str::replaceStart('__', '', $name);

            return $this->{$name}(...$arguments);
        }

        throw new Exception('Call to undefined method '.__CLASS__.'::'.$name.'()');
    }

    public function __get(string $name): mixed
    {
        if (app()->environment('testing')) {
            $name = Str::replaceStart('__', '', $name);

            return $this->$name;
        }

        throw new Exception('Call to undefined property '.__CLASS__.'::'.$name);
    }

    public function __set(string $name, mixed $value): void
    {
        if (app()->environment('testing')) {
            $name = Str::replaceStart('__', '', $name);
            $this->$name = $value;

            return;
        }

        throw new Exception('Call to undefined property '.__CLASS__.'::'.$name);
    }
}
