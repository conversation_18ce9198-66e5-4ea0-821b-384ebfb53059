# For more information: https://laravel.com/docs/sail
services:
    laravel.development:
        build:
            context: .
            dockerfile: Dockerfile.development
            args:
                WWWGROUP: '${WWWGROUP}'
        extra_hosts:
            - 'host.docker.internal:host-gateway'
        ports:
            - '127.0.0.1:${APP_PORT:-80}:80'
            - '127.0.0.1:${HMR_PORT:-8080}:8080'
        environment:
            WWWUSER: '${WWWUSER}'
            LARAVEL_SAIL: 1
            XDEBUG_MODE: '${SAIL_XDEBUG_MODE:-off}'
            XDEBUG_CONFIG: '${SAIL_XDEBUG_CONFIG:-client_host=host.docker.internal}'
        volumes:
            - '.:/app'
            - './server/default:/etc/nginx/sites-available/default'
            - './server/fpm/www.conf:/usr/local/etc/php-fpm.d/www.conf'
        networks:
            - sail
        depends_on:
            - redis
            - mysql
    mysql:
        image: mysql:8.0
        restart: always
        ports:
            - '127.0.0.1:${FORWARD_DB_PORT:-3306}:3306'
        environment:
            MYSQL_ROOT_PASSWORD: '${DB_PASSWORD}'
            MYSQL_ROOT_HOST: "%"
            MYSQL_USER: '${DB_USERNAME}'
            MYSQL_PASSWORD: '${DB_PASSWORD}'
        volumes:
            - 'sail-mysql:/var/lib/mysql'
        networks:
            - sail
        healthcheck:
            test: ["CMD", "mysqladmin", "ping", "-p${DB_PASSWORD}"]
            retries: 3
            timeout: 5s
    redis:
        image: redis:6.2
        command: redis-server --appendonly yes
        expose:
            - 6379
        volumes:
            - sail-redis:/data
        networks:
            - sail
networks:
    sail:
        driver: bridge
volumes:
    sail-redis:
    sail-mysql:
        external: true
        name: admin-2-mysql-volume
