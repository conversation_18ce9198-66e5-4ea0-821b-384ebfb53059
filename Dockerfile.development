FROM node:20.12.0-alpine3.19 as nodejs

RUN npm install -g n

FROM gcr.io/admin-solarreviews/base:latest

ARG WWWGROUP
ENV DEBIAN_FRONTEND noninteractive
ENV TZ=UTC

WORKDIR /app

COPY --from=composer/composer:2.7.2-bin /composer /usr/bin/composer
COPY --from=nodejs /usr/local/bin/n /usr/local/bin/n

#Node LTS, update accordingly
RUN n 20

RUN apt update
RUN apt install \
    libxml2-dev \
    nano \
    ne \
    vim \
    sudo \
    --no-install-recommends -y

RUN groupadd --force -g $WWWGROUP sail
RUN useradd -ms /bin/bash --no-user-group -g $WWWGROUP -u 1337 sail

#Grant root access to sail user without password, now we can use sudo when logged in as sail
#Obviously, only allow this in development
RUN usermod -a -G sudo sail
RUN echo "sail ALL=(ALL:ALL) NOPASSWD: ALL" | sudo tee /etc/sudoers.d/sail

RUN pecl install xdebug
RUN docker-php-ext-enable xdebug
RUN docker-php-ext-install soap
RUN docker-php-ext-configure pcntl --enable-pcntl
RUN docker-php-ext-install pcntl

EXPOSE 80
EXPOSE 8000
ENTRYPOINT php-fpm -D -R && nginx -g "daemon off;"
