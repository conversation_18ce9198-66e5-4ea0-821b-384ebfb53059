{"private": true, "scripts": {"dev": "vite", "build": "vite build", "build-dev": "NODE_ENV=development vite build --mode development", "watch": "NODE_ENV=development vite build --mode development -- --with-watch", "mjml-build": "node mjml-build.js", "format": "npx prettier --write resources/"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@vitejs/plugin-vue": "^5.0.4", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.5", "axios": "^0.25", "dotenv": "^16.0.3", "laravel-mix": "^6.0.6", "laravel-vite-plugin": "^1.0.4", "lodash": "^4.17.19", "postcss": "^8.4.6", "postcss-import": "^14.0.2", "prettier": "^3.5.3", "prettier-plugin-blade": "^2.1.21", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.0.18", "vite": "^5.2.11", "vite-plugin-vue-devtools": "^7.2.1", "vue-loader": "^16.8.3"}, "dependencies": {"@headlessui/vue": "^1.6.4", "@heroicons/vue": "^1.0.6", "@sentry/browser": "^8.17.0", "@toast-ui/editor": "^3.1.8", "@toast-ui/editor-plugin-color-syntax": "^3.0.3", "@twilio/voice-sdk": "^2.1.1", "@vuepic/vue-datepicker": "^8.3.1", "@vueuse/core": "^9.1.0", "chart.js": "^3.9.1", "currency.js": "^2.0.4", "dayjs": "^1.11.3", "dompurify": "^3.2.4", "echarts": "^5.5.0", "he": "^1.2.0", "luxon": "^3.4.4", "mailslurp-client": "^15.17.5", "pinia": "^2.0.17", "pinia-plugin-persistedstate": "^3.1.0", "puppeteer": "^22.11.2", "pusher-js": "^7.1.0-beta", "twilio-client": "^1.14.0", "vue": "^3.2.33", "vue-chartjs": "^4.1.2", "vue-draggable-next": "^2.2.1", "vue-echarts": "^6.6.9", "ws": "^8.6.0"}, "type": "module"}