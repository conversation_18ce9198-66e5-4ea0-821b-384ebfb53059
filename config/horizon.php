<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Horizon Domain
    |--------------------------------------------------------------------------
    |
    | This is the subdomain where Horizon will be accessible from. If this
    | setting is null, Horizon will reside under the same domain as the
    | application. Otherwise, this value will serve as the subdomain.
    |
    */

    'domain' => env('HORIZON_DOMAIN'),

    /*
    |--------------------------------------------------------------------------
    | Horizon Path
    |--------------------------------------------------------------------------
    |
    | This is the URI path where Horizon will be accessible from. Feel free
    | to change this path to anything you like. Note that the URI will not
    | affect the paths of its internal API that aren't exposed to users.
    |
    */

    'path' => env('HORIZON_PATH', 'horizon'),

    /*
    |--------------------------------------------------------------------------
    | Horizon Redis Connection
    |--------------------------------------------------------------------------
    |
    | This is the name of the Redis connection where Horizon will store the
    | meta information required for it to function. It includes the list
    | of supervisors, failed jobs, job metrics, and other information.
    |
    */

    'use' => 'queue',

    /*
    |--------------------------------------------------------------------------
    | Horizon Redis Prefix
    |--------------------------------------------------------------------------
    |
    | This prefix will be used when storing all Horizon data in Redis. You
    | may modify the prefix when you are running multiple installations
    | of Horizon on the same server so that they don't have problems.
    |
    */

    'prefix' => env(
        'HORIZON_PREFIX',
        Str::slug(env('APP_NAME', 'laravel'), '_').'_horizon:'
    ),

    /*
    |--------------------------------------------------------------------------
    | Horizon Route Middleware
    |--------------------------------------------------------------------------
    |
    | These middleware will get attached onto each Horizon route, giving you
    | the chance to add your own middleware to this list or change any of
    | the existing middleware. Or, you can simply stick with this list.
    |
    */

    'middleware' => ['web', 'permission:horizon'],

    /*
    |--------------------------------------------------------------------------
    | Queue Wait Time Thresholds
    |--------------------------------------------------------------------------
    |
    | This option allows you to configure when the LongWaitDetected event
    | will be fired. Every connection / queue combination may have its
    | own, unique threshold (in seconds) before this event is fired.
    |
    */

    'waits' => [
        'redis:default' => 60,
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Trimming Times
    |--------------------------------------------------------------------------
    |
    | Here you can configure for how long (in minutes) you desire Horizon to
    | persist the recent and failed jobs. Typically, recent jobs are kept
    | for one hour while all failed jobs are stored for an entire week.
    |
    */

    'trim' => [
        'recent' => 60,
        'pending' => 60,
        'completed' => 60,
        'recent_failed' => 10080,
        'failed' => 10080,
        'monitored' => 10080,
    ],

    /*
    |--------------------------------------------------------------------------
    | Silenced Jobs
    |--------------------------------------------------------------------------
    |
    | Silencing a job will instruct Horizon to not place the job in the list
    | of completed jobs within the Horizon dashboard. This setting may be
    | used to fully remove any noisy jobs from the completed jobs list.
    |
    */

    'silenced' => [
        // App\Jobs\ExampleJob::class,
        \Laravel\Telescope\Jobs\ProcessPendingUpdates::class,
        \App\Jobs\RecordMonitoringLog::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Metrics
    |--------------------------------------------------------------------------
    |
    | Here you can configure how many snapshots should be kept to display in
    | the metrics graph. This will get used in combination with Horizon's
    | `horizon:snapshot` schedule to define how long to retain metrics.
    |
    */

    'metrics' => [
        'trim_snapshots' => [
            'job'   => 288, // 24 hours @ 5 min increments
            'queue' => 288, // 24 hours @ 5 min increments
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Fast Termination
    |--------------------------------------------------------------------------
    |
    | When this option is enabled, Horizon's "terminate" command will not
    | wait on all of the workers to terminate unless the --wait option
    | is provided. Fast termination can shorten deployment delay by
    | allowing a new instance of Horizon to start while the last
    | instance will continue to terminate each of its workers.
    |
    */

    'fast_termination' => false,

    /*
    |--------------------------------------------------------------------------
    | Memory Limit (MB)
    |--------------------------------------------------------------------------
    |
    | This value describes the maximum amount of memory the Horizon master
    | supervisor may consume before it is terminated and restarted. For
    | configuring these limits on your workers, see the next section.
    |
    */

    'memory_limit' => 4096,

    /*
    |--------------------------------------------------------------------------
    | Queue Worker Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may define the queue worker settings used by your application
    | in all environments. These supervisors and settings handle all your
    | queued jobs and will be provisioned by Horizon during deployment.
    |
    */

    'defaults' => [

        'critical' => [
            'connection' => 'redis',
            'queue' => ['critical'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 3,
            'balanceCooldown' => 1,
            'maxProcesses' => 10,
            'maxTime' => 1800,
            'maxJobs' => 500,
            'memory' => 2048,
            'tries' => 3,
            'timeout' => 30,
            'rest' => 0,
        ],
        'lead-allocation' => [
            'connection' => 'redis',
            'queue' => ['lead_allocation_queue'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 3,
            'balanceCooldown' => 1,
            'maxProcesses' => 15,
            'maxTime' => 3600,
            'maxJobs' => 2000,
            'memory' => 4096,
            'tries' => 3,
            'rest' => 1,
        ],
        'appointment-allocation' => [
            'connection' => 'redis',
            'queue' => ['appointment_allocation_queue', 'appointment_delivery_queue'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 2,
            'balanceCooldown' => 1,
            'maxProcesses' => 12,
            'maxTime' => 3600,
            'maxJobs' => 1500,
            'memory' => 4096,
            'tries' => 3,
            'timeout' => 60,
            'rest' => 1,
        ],
        'default' => [
            'connection' => 'redis',
            'queue' => ['default', 'batch', 'email_notification', 'contact_identification_identify', 'contact_identification_sync_data', 'workflows', 'heartbeat', 'sms'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 2,
            'balanceCooldown' => 1,
            'maxProcesses' => 20,
            'maxTime' => 3600,
            'maxJobs' => 1500,
            'memory' => 4096,
            'tries' => 3,
            'timeout' => 60,
            'rest' => 1,
        ],
        'mailbox' => [
            'connection' => 'redis',
            'queue' => ['mailbox_handle_mail_provider_event', 'mailbox_modify_email_in_mail_provider' , 'mailbox_renew_user_email_listener', 'mailbox_send_email', 'mailbox_setup_emails_listener', 'mailbox_sync_user_emails'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 7,
            'maxTime' => 3600,
            'maxJobs' => 1000,
            'tries' => 3,
            'timeout' => 60,
            'rest' => 1,
        ],
        'long-running' => [
            'connection' => 'redis',
            'queue' => ['long_running', 'legacy-migration'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'size',
            'balanceMaxShift' => 2,
            'balanceCooldown' => 1,
            'maxProcesses' => 8,
            'maxTime' => 7200,
            'timeout' => 1200,
            'maxJobs' => 500,
            'memory' => 6144,
            'tries' => 3,
            'rest' => 2
        ],
        'priority-long-running' => [
            'connection' => 'redis',
            'queue' => ['priority_long_running'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'size',
            'balanceMaxShift' => 2,
            'balanceCooldown' => 1,
            'maxProcesses' => 8,
            'maxTime' => 7200,
            'timeout' => 1200,
            'maxJobs' => 500,
            'memory' => 6144,
            'tries' => 3,
            'rest' => 2
        ],
        'long-running-single-attempt' => [
            'connection' => 'redis',
            'queue' => ['long_running_single_attempt'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'size',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 2,
            'maxTime' => 3600,
            'timeout' => 600,
            'maxJobs' => 1000,
            'memory' => 4096,
            'tries' => 1,
            'rest' => 1
        ],
        'logging' => [
            'connection' => 'redis',
            'queue' => ['logging'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'size',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 2,
            'maxTime' => 3600,
            'timeout' => 10,
            'maxJobs' => 1000,
            'memory' => 1024,
            'tries' => 1,
            'nice' => 8
        ],
        'billing' => [
            'connection' => 'redis',
            'queue' => ['billing'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 2,
            'maxTime' => 3600,
            'maxJobs' => 1000,
            'tries' => 1,
            'timeout' => 600,
            'memory' => 1024,
            'rest' => 1,
        ],
        'privacy' => [
            'connection' => 'redis',
            'queue' => ['privacy'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 1,
            'maxTime' => 3600,
            'maxJobs' => 1000,
            'tries' => 1,
            'timeout' => 60,
            'rest' => 1,
        ],
        'marketing' => [
            'connection' => 'redis',
            'queue' => ['marketing'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'size',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 2,
            'maxTime' => 3600,
            'timeout' => 600,
            'maxJobs' => 1000,
            'memory' => 4096,
            'tries' => 3,
            'rest' => 1
        ],
        'imports' => [
            'connection' => 'redis',
            'queue' => ['imports'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'size',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 2,
            'maxTime' => 3600,
            'timeout' => 600,
            'maxJobs' => 1000,
            'memory' => 4096,
            'tries' => 3,
            'rest' => 1
        ],
        'affiliates' => [
            'connection' => 'redis',
            'queue' => ['affiliates'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 10,
            'maxTime' => 3600,
            'maxJobs' => 1000,
            'memory' => 4096,
            'tries' => 3,
            'timeout' => 60,
            'rest' => 1,
        ],
        'test-leads' => [
            'connection' => 'redis',
            'queue' => ['test_leads'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 2,
            'balanceCooldown' => 1,
            'maxProcesses' => 8,
            'maxTime' => 3600,
            'maxJobs' => 1000,
            'memory' => 2048,
            'tries' => 3,
            'timeout' => 120,
            'rest' => 1,
        ],
        'qa-automation' => [
            'connection' => 'redis',
            'queue' => ['qa_automation'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 2,
            'balanceCooldown' => 1,
            'maxProcesses' => 10,
            'maxTime' => 3600,
            'maxJobs' => 2000,
            'memory' => 4096,
            'tries' => 3,
            'rest' => 1,
        ],
        'data-enrichment' => [
            'connection' => 'redis',
            'queue' => ['data_enrichment'],
            'balance' => 'auto',
            'autoScalingStrategy' => 'time',
            'balanceMaxShift' => 1,
            'balanceCooldown' => 3,
            'maxProcesses' => 4,
            'maxTime' => 3600,
            'maxJobs' => 1000,
            'memory' => 2048,
            'tries' => 3,
            'timeout' => 120,
            'rest' => 1,
        ],
    ],

    // each environment must be defined here, even if we're using defaults
    'environments' => [
        'production' => [],
        'local' => [],
        'dev' => [], // staging
    ],
];
