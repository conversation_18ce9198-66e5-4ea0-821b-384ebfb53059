<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Queue Connection Name
    |--------------------------------------------------------------------------
    |
    | Laravel's queue API supports an assortment of back-ends via a single
    | API, giving you convenient access to each back-end using the same
    | syntax for every one. Here you may define a default connection.
    |
    */

    'default' => env('QUEUE_CONNECTION', 'sync'),

    'log_job_events' => env('LOG_JOB_EVENTS', false),

    /*
    |--------------------------------------------------------------------------
    | Queue Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each server that
    | is used by your application. A default configuration has been added
    | for each back-end shipped with <PERSON><PERSON>. You are free to add more.
    |
    | Drivers: "sync", "database", "beanstalkd", "sqs", "redis", "null"
    |
    */

    'connections' => [

        'sync' => [
            'driver' => 'sync',
        ],

        'database' => [
            'driver'       => 'database',
            'table'        => 'jobs',
            'queue'        => 'default',
            'retry_after'  => env('DATABASE_CONNECTION_RETRY_AFTER', 600),
            'after_commit' => env('DATABASE_QUEUE_AFTER_COMMIT', false),
        ],

        'beanstalkd' => [
            'driver'       => 'beanstalkd',
            'host'         => 'localhost',
            'queue'        => 'default',
            'retry_after'  => 90,
            'block_for'    => 0,
            'after_commit' => false,
        ],

        'sqs' => [
            'driver'       => 'sqs',
            'key'          => env('AWS_ACCESS_KEY_ID'),
            'secret'       => env('AWS_SECRET_ACCESS_KEY'),
            'prefix'       => env('SQS_PREFIX', 'https://sqs.us-east-1.amazonaws.com/your-account-id'),
            'queue'        => env('SQS_QUEUE', 'default'),
            'suffix'       => env('SQS_SUFFIX'),
            'region'       => env('AWS_DEFAULT_REGION', 'us-east-1'),
            'after_commit' => false,
        ],

        'redis' => [
            'driver'       => 'redis',
            'connection'   => 'queue',
            'queue'        => env('REDIS_QUEUE', 'default'),
            'retry_after'  => 5400,  // this matches what is in prod right now - will revisit
            'block_for'    => null,
            'after_commit' => false,
        ],

    ],

    /*
    |--------------------------------------------------------------------------
    | Failed Queue Jobs
    |--------------------------------------------------------------------------
    |
    | These options configure the behavior of failed queue job logging so you
    | can control which database and table are used to store the jobs that
    | have failed. You may change them to any database / table you wish.
    |
    */

    'failed'       => [
        'driver'   => env('QUEUE_FAILED_DRIVER', 'database-uuids'),
        'database' => env('DB_CONNECTION', 'mysql'),
        'table'    => 'failed_jobs',
    ],

    /**
     * Add all named queues here for easy reference in code.
     */
    'named_queues' => [
        'critical'                              => 'critical',
        'default'                               => 'default',
        'long_running'                          => 'long_running',
        'billing'                               => 'billing',
        'lead_allocation_queue'                 => 'lead_allocation_queue',
        'dynamic_priority'                      => 'long_running',
        'heartbeat'                             => 'heartbeat',
        'sms'                                   => 'sms',
        'appointment_allocation_queue'          => 'appointment_allocation_queue',
        'appointment_delivery_queue'            => 'appointment_delivery_queue',
        'workflows'                             => 'workflows',
        'reviews'                               => 'reviews',
        'migrations'                            => 'legacy-migration',
        'email_notification'                    => 'email_notification',
        'outreach-cadence'                      => 'default', // todo: make new queue?
        'logging'                               => 'logging',
        'privacy'                               => 'privacy',
        'marketing'                             => 'marketing',
        'imports'                               => 'imports',
        'affiliates'                            => 'affiliates',
        'test_leads'                            => 'test_leads',

        'mailbox_handle_mail_provider_event'    => 'mailbox_handle_mail_provider_event',
        'mailbox_modify_email_in_mail_provider' => 'mailbox_modify_email_in_mail_provider',
        'mailbox_renew_user_email_listener'     => 'mailbox_renew_user_email_listener',
        'mailbox_send_email'                    => 'mailbox_send_email',
        'mailbox_setup_emails_listener'         => 'mailbox_setup_emails_listener',
        'mailbox_sync_user_emails'              => 'mailbox_sync_user_emails',
        'contact_identification_identify'       => 'contact_identification_identify',
        'contact_identification_sync_data'      => 'contact_identification_sync_data',
    ],
];
