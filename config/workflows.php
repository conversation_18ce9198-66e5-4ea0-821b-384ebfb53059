<?php

declare(strict_types=1);

use App\BillingWorkflows\Models\StoredBillingWorkflow;
use App\BillingWorkflows\Models\StoredBillingWorkflowLog;
use App\BillingWorkflows\Models\StoredBillingWorkflowTimer;
use App\BillingWorkflows\Models\StoredBillingWorkflowException;
use App\BillingWorkflows\Models\StoredBillingWorkflowSignal;

return [
    'workflows_table' => 'billing_workflows',

    'workflows_folder' => 'BillingWorkflows',

    'base_model' => Illuminate\Database\Eloquent\Model::class,

    'stored_workflow_model' => StoredBillingWorkflow::class,

    'stored_workflow_exception_model' => StoredBillingWorkflowException::class,

    'stored_workflow_log_model' => StoredBillingWorkflowLog::class,

    'stored_workflow_signal_model' => StoredBillingWorkflowSignal::class,

    'stored_workflow_timer_model' => StoredBillingWorkflowTimer::class,

    'workflow_relationships_table' => 'billing_workflow_relationships',

    'monitor' => env('WORKFLOW_MONITOR', false),

    'monitor_url' => env('WORKFLOW_MONITOR_URL'),

    'monitor_api_key' => env('WORKFLOW_MONITOR_API_KEY'),

    'monitor_connection' => env('WORKFLOW_MONITOR_CONNECTION', config('queue.default')),

    'monitor_queue' => env(
        'WORKFLOW_MONITOR_QUEUE',
        config('queue.connections.' . config('queue.default') . '.queue', 'default')
    ),
];
