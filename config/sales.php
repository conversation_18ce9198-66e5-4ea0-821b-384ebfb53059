<?php

return [
    'brs_driver' => env('BRS_DRIVER', 'default'),
    'product_pricing_driver' => env('PRODUCT_PRICING_DRIVER', 'driver'),

    'leads' => [
        'minimum_electric_spend' => env('LEAD_MINIMUM_ELECTRIC_SPEND', 0),
        'premium_electric_spend' => 300,
        'overall_rejection_percentage_threshold' => env('LEAD_REJECTION_PERCENTAGE_THRESHOLD', 10),
        'crm_rejection_percentage_threshold' => env('LEAD_CRM_REJECTION_PERCENTAGE_THRESHOLD', 10),
        'rejection_window_duration_hours' => env('LEAD_REJECTION_WINDOW_DURATION_HOURS', 48),
    ],

    'appointments' => [
        'minimum_electric_spend' => env('APPT_MINIMUM_ELECTRIC_SPEND', 0),
        'overall_rejection_percentage_threshold' => env('APPT_REJECTION_PERCENTAGE_THRESHOLD', 10),
        'crm_rejection_percentage_threshold' => env('APPT_CRM_REJECTION_PERCENTAGE_THRESHOLD', 10),
        'offering_available_duration_min' => env('APPT_OFFERING_AVAILABLE_DURATION_MIN', 15),
        'rejection_window_duration_hours' => env('APPT_REJECTION_WINDOW_DURATION_HOURS', 72),
        'max_offering_attempts' => env('APPT_MAX_OFFERING_ATTEMPTS', 4),
        'consumer_advance_notice_hours' => env('APPT_CONSUMER_ADVANCE_NOTICE_HOURS', 24),
        'log_brs_queries' => env('APPT_LOG_BRS_QUERIES', false),
        'next_delivery_attempt_buffer_minutes' => env('APPT_NEXT_DELIVERY_ATTEMPT_BUFFER_MINUTES', 30),
        'max_delivery_attempts' => env('APPT_MAX_DELIVERY_ATTEMPTS', 3),
        'sell_appt_as_lead_buffer_after_min' => env('APPT_SELL_APPT_AS_LEAD_BUFFER_AFTER_MIN', 120)
    ],

    'direct_leads' => [
        'allocate_without_timezone_open_delay' => env('ALLOCATE_DIRECT_LEADS_WITHOUT_TIMEZONE_OPEN_DELAY', false)
    ],

    'defaults' => [
        'overall_rejection_percentage_threshold' => 10,
        'crm_rejection_percentage_threshold'     => 10,
        'manual_rejection_percentage_threshold'  => 10,
        'zip_code_targeting_price_multiplier'    => 1,
    ],

    'are_utility_filters_active' => env('ARE_UTILITY_FILTERS_ACTIVE', true),

    'max_charge_attempts' => env('MAX_CHARGE_ATTEMPTS', 3),

    'large_account_revenue_threshold' => env('LARGE_ACCOUNT_REVENUE_THRESHOLD', 20000),

    'available_budgets' => [
        'omit_low_never_exceed_budget' => env('OMIT_LOW_NEVER_EXCEED_BUDGET', true)
    ],

    'ignore_timezone_delay' => env('IGNORE_ALLOCATION_TIMEZONE_DELAY', false),

    // This is a temporary measure for the sake of enabling individual industries
    // to exclusively sell direct leads (1-to-1 consent)
    // Can be removed after all industries are transitioned
    'direct_lead_enabled_industry_ids' => explode(",", env('DIRECT_LEAD_ENABLED_INDUSTRY_IDS', "")),

    'missed_products_minimum_hours' => 72,
    'missed_products_expiry_days' => 60,
    'missed_product_reason_events_expiry_days' => 90,
];
