<?php

use App\Models\Odin\Consumer;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\CompanyReview;
use App\Models\ConsumerReviews\Review;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentCompanyReviewEmail;
use App\Models\Legacy\EloquentSubscribe;

return [
    'models' => [
        Consumer::class,
        CompanyUser::class,
        CompanyReview::class,
        Review::class,
        EloquentCompanyContact::class,
        EloquentQuote::class,
        EloquentCompanyReviewEmail::class,
        EloquentSubscribe::class
    ],
    'redact_model_relations' => [
        \App\Models\Legacy\EloquentQuote::class => [
            \App\Models\Legacy\EloquentQuote::RELATION_ADDRESS => \App\Models\Legacy\EloquentAddress::class,
        ]
    ],
    'include_endpoints' => env('PRIVACY_REQUEST_API_ENDPOINTS', true),
    'uri-prefix' => env('PRIVACY_API_PREFIX', '/api/privacy-request'),
    'access-token' => env('PRIVACY_ACCESS_TOKEN', null),
    'fields' => [
        'name' => ['name', 'firstname', 'first_name', 'first', 'lastname', 'last_name'],
        'phone' => ['phone', 'office_phone', 'formatted_office_phone', 'officephone', 'mobile', 'mobile_phone', 'cell', 'cellphone', 'cell_phone', 'formatted_cell_phone', 'formatted_phone'],
        'email' => ['email', 'useremail']
    ]
];
