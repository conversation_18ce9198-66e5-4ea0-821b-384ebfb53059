apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: sr-admin-example-ingress
  annotations:
    kubernetes.io/ingress.global-static-ip-name: "sr-admin-example-static-ip"
    networking.gke.io/v1beta1.FrontendConfig: "sr-admin-example-ingress-frontend-config"
    networking.gke.io/managed-certificates: "sr-admin-example-ssl-certificate"
spec:
  backend:
    serviceName: sr-admin-example-node-port
    servicePort: 80
  rules:
    - host: example-domain.admin-sr.com
      http:
        paths:
          - path: /api/webhooks/subscriptions/*
            backend:
              serviceName: sr-admin-example-webhook-node-port
              servicePort: 80
          - path: /*
            backend:
              serviceName: sr-admin-example-node-port
              servicePort: 80
