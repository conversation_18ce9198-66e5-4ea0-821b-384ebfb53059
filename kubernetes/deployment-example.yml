apiVersion: apps/v1
kind: Deployment
metadata:
  name: sr-admin-example-deployment
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sr-admin-example-deployment
  template:
    metadata:
      labels:
        app: sr-admin-example-deployment
    spec:
      containers:
        - name: server
          image: gcr.io/example-project/example-container:example-version
          ports:
            - containerPort: 80
          resources:
            requests:
              memory: "2048Mi"
              cpu: "500m"
            limits:
              memory: "8192Mi"
              cpu: "2000m"
