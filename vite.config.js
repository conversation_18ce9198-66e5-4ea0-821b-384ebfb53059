import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';
import vueDevTools from 'vite-plugin-vue-devtools';

/** @type {import('vite').UserConfig} */
export default defineConfig(() => {
    const watch = (process?.argv ?? []).find(arg => arg === '--with-watch')
        ? { buildDelay: 3000 }
        : null;

    return {
        envPrefix: 'MIX_', // We'll continue to use the MIX_ prefix instead of VITE_ for ease of transition
        resolve: {
            alias: {
                'vue': 'vue/dist/vue.esm-bundler.js',
            }
        },
        define: {
            'process.env': {},
        },
        build: {
            watch: watch,
        },
        plugins: [
            laravel({
                input: [ 'resources/css/app.css', 'resources/js/app.js', 'resources/css/mail.css' ],
                refresh: true,
            }),
            vue({
                template: {
                    transformAssetUrls: {
                        base: null,
                        includeAbsolute: false,
                    },
                },
            }),
            vueDevTools(),
        ],
    }
});
