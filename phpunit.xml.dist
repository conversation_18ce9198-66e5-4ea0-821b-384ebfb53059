<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="./vendor/phpunit/phpunit/phpunit.xsd"
         bootstrap="vendor/autoload.php"
         colors="true"
         failOnRisky="true"
         failOnWarning="true"
         stopOnError="true"
         stopOnFailure="true"
>
    <groups>
        <exclude>
            <group>successful_failure</group>
            <group>slow</group>
        </exclude>
    </groups>
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <source>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
    </source>
    <php>
        <ini name="date.timezone" value="UTC" />
        <ini name="intl.default_locale" value="C.UTF-8" />
        <ini name="memory_limit" value="4G"/>
        <env name="APP_ENV" value="testing"/>
        <env name="APP_KEY" value="base64:/NE/O9s7/GDnFuoHgxgh++qfChJ593PGyXwcdkVXNRE="/>
        <env name="APP_URL" value="https://localhost"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="BROADCAST_DRIVER" value="log"/>
        <env name="CACHE_DRIVER" value="array"/>
        <env name="DB_DATABASE" value="test_admin_20"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="READONLY_DB_DATABASE" value="test_readonly"/>
        <env name="REDIS_CLIENT" value="null"/>
        <env name="SESSION_DRIVER" value="array"/>
        <env name="TELESCOPE_ENABLED" value="false"/>
    </php>
</phpunit>
